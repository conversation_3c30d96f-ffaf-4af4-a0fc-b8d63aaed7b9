<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-49" aopId="786686" id="thSvg" product="E8000V2" version="1.0" viewBox="3117 -1208 2198 1209">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.890909" x1="29" x2="29" y1="6" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.583333" x1="26" x2="26" y1="4" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="4" x2="22" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="22" x2="22" y1="0" y2="18"/>
   </symbol>
   <symbol id="earth:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="7" x2="11" y1="30" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="9" x2="9" y1="5" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="0" x2="18" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="6" x2="13" y1="26" y2="26"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.742424" x1="2" x2="2" y1="11" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="26" x2="9" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="9" x2="9" y1="18" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="5" x2="5" y1="13" y2="5"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.742424" x1="7" x2="11" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="9" x2="9" y1="27" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="0" x2="18" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="6" x2="13" y1="6" y2="6"/>
   </symbol>
   <symbol id="generator:shape6">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,46 5,1 " stroke-width="3"/>
   </symbol>
   <symbol id="generator:shape7">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="6,47 53,47 " stroke-width="3"/>
   </symbol>
   <symbol id="hydroGenerator:shape2">
    <polyline points="12,27 11,28 12,28 12,29 12,30 13,31 13,32 14,33 15,33 15,34 16,34 17,35 18,35 19,35 20,35 21,35 22,34 23,34 24,33 25,33 25,32 26,31 26,30 27,29 27,28 27,28 27,27 " stroke-width="0.06"/>
    <circle cx="27" cy="27" fillStyle="0" r="26.5" stroke-width="0.55102"/>
    <polyline arcFlag="1" points="28,27 28,26 28,25 28,24 29,23 29,22 30,21 30,21 31,20 32,20 33,19 34,19 35,19 36,18 37,19 38,19 39,19 40,20 40,20 41,21 42,21 42,22 43,23 43,24 43,25 44,26 43,27 " stroke-width="0.06"/>
   </symbol>
   <symbol id="lightningRod:shape106">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="26" y1="40" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="54" x2="43" y1="40" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="42" x2="42" y1="48" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="35" x2="35" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.676705" x1="34" x2="16" y1="40" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="16" x2="16" y1="48" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="9" x2="9" y1="48" y2="31"/>
    <circle cx="30" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="26" cy="15" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="20" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="0" x2="9" y1="40" y2="40"/>
   </symbol>
   <symbol id="lightningRod:shape55">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="50" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="9" y1="41" y2="41"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="27,39 5,17 5,5 " stroke-width="1"/>
    <rect height="4" stroke-width="1" width="19" x="7" y="26"/>
   </symbol>
   <symbol id="lightningRod:shape38">
    <rect height="8" stroke-width="0.75" width="18" x="21" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.278409" x1="1" x2="1" y1="8" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.351519" x1="4" x2="4" y1="10" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.351519" x1="7" x2="7" y1="14" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.351519" x1="21" x2="7" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="46" x2="26" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="28" x2="26" y1="9" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="26" x2="28" y1="7" y2="5"/>
   </symbol>
   <symbol id="lightningRod:shape50">
    <polyline DF8003:Layer="PUBLIC" points="5,39 0,50 11,50 5,39 5,40 5,39 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.222222" x1="5" x2="5" y1="29" y2="33"/>
    <polyline DF8003:Layer="PUBLIC" points="5,25 0,14 11,14 5,25 5,24 5,25 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="59" y2="51"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="6" y2="41"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="63" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="55" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="56" y2="56"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="59" x2="24" y1="7" y2="7"/>
    <rect height="12" stroke-width="1" width="26" x="18" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="1" x2="1" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="5" x2="5" y1="3" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="17" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="8" x2="8" y1="12" y2="1"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="55" x2="55" y1="12" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="54" x2="46" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="59" x2="59" y1="3" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="62" x2="62" y1="5" y2="8"/>
    <rect height="12" stroke-width="1" width="26" x="19" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="39" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape164">
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="63" x2="63" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="60" x2="60" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="57" x2="57" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="57" x2="48" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="7" x2="15" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="15" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="7" x2="15" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="15" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="transformer2:shape11_0">
    <ellipse cx="13" cy="34" fillStyle="0" rx="13" ry="12.5" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="40" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="36" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="32" y2="36"/>
   </symbol>
   <symbol id="transformer2:shape11_1">
    <circle cx="13" cy="16" fillStyle="0" r="13" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="20" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="16" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="12" y2="16"/>
   </symbol>
   <symbol id="transformer2:shape24_0">
    <circle cx="16" cy="42" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,41 40,41 40,70 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="56" y2="98"/>
    <polyline DF8003:Layer="PUBLIC" points="14,84 20,71 7,71 14,84 14,83 14,84 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="73" y2="73"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="75" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="43" x2="40" y1="78" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="39" x2="14" y1="47" y2="72"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="41" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="42" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="43" y2="47"/>
   </symbol>
   <symbol id="transformer2:shape24_1">
    <circle cx="16" cy="20" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="13" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="19" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="15" y1="19" y2="13"/>
   </symbol>
   <symbol id="transformer2:shape100_0">
    <circle cx="26" cy="61" fillStyle="0" r="25" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="72" x2="57" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="72" x2="57" y1="45" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="57" x2="57" y1="54" y2="37"/>
    <circle cx="56" cy="45" fillStyle="0" r="25" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="25" x2="18" y1="66" y2="73"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="33" x2="25" y1="73" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="25" x2="25" y1="57" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape100_1">
    <circle cx="26" cy="29" fillStyle="0" r="24.5" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="25" x2="18" y1="24" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="33" x2="25" y1="31" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="25" x2="25" y1="15" y2="24"/>
   </symbol>
   <symbol id="voltageTransformer:shape6">
    <circle cx="18" cy="15" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="11" cy="11" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="18" cy="7" fillStyle="0" r="6" stroke-width="0.431185"/>
   </symbol>
   <symbol id="voltageTransformer:shape11">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="18" x2="15" y1="11" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="13" x2="15" y1="11" y2="9"/>
    <circle cx="15" cy="19" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="24" cy="16" fillStyle="0" r="6" stroke-width="0.431185"/>
    <ellipse cx="15" cy="11" fillStyle="0" rx="6.5" ry="6" stroke-width="0.431185"/>
    <circle cx="7" cy="16" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="22" x2="24" y1="18" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="25" x2="25" y1="16" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="27" x2="24" y1="18" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="13" x2="15" y1="22" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="16" x2="16" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="18" x2="15" y1="22" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="6" x2="9" y1="19" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="6" x2="3" y1="19" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="3" x2="9" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="16" x2="16" y1="9" y2="7"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_201ef30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1b9e4e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1b9eef0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_20211b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_20223e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2023080" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2023c20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2024620" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_201fc80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_201fc80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2027800" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2027800" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2029630" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2029630" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_202a640" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_202c2d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_202cf20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_202de20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_202e760" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_202fe20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2030b20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_20313e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2031ba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2032c80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2033600" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_20340f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2034ab0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2035f30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2036ad0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2037b00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2038740" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2046f10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2039dc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_203af60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_203c540" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1219" width="2208" x="3112" y="-1213"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-599"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-1079"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-1199"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4416.582328 -941.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43209">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4145.416667 -498.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7245" ObjectName="SW-CX_FJ.CX_FJ_3011SW"/>
     <cge:Meas_Ref ObjectId="43209"/>
    <cge:TPSR_Ref TObjectID="7245"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43210">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4179.416667 -546.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7246" ObjectName="SW-CX_FJ.CX_FJ_30117SW"/>
     <cge:Meas_Ref ObjectId="43210"/>
    <cge:TPSR_Ref TObjectID="7246"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43397">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4144.000000 -419.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7250" ObjectName="SW-CX_FJ.CX_FJ_3911SW"/>
     <cge:Meas_Ref ObjectId="43397"/>
    <cge:TPSR_Ref TObjectID="7250"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43400">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4186.000000 -398.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7253" ObjectName="SW-CX_FJ.CX_FJ_39117SW"/>
     <cge:Meas_Ref ObjectId="43400"/>
    <cge:TPSR_Ref TObjectID="7253"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43398">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4144.000000 -306.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7251" ObjectName="SW-CX_FJ.CX_FJ_3916SW"/>
     <cge:Meas_Ref ObjectId="43398"/>
    <cge:TPSR_Ref TObjectID="7251"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43399">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4186.000000 -286.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7252" ObjectName="SW-CX_FJ.CX_FJ_39167SW"/>
     <cge:Meas_Ref ObjectId="43399"/>
    <cge:TPSR_Ref TObjectID="7252"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43401">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4186.000000 -348.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7254" ObjectName="SW-CX_FJ.CX_FJ_39160SW"/>
     <cge:Meas_Ref ObjectId="43401"/>
    <cge:TPSR_Ref TObjectID="7254"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3604.000000 -49.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3605.000000 -166.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3605.000000 -269.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3971.000000 -75.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4034.000000 -76.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4104.000000 -75.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43420">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4337.000000 -418.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7256" ObjectName="SW-CX_FJ.CX_FJ_3921SW"/>
     <cge:Meas_Ref ObjectId="43420"/>
    <cge:TPSR_Ref TObjectID="7256"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43423">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4380.000000 -398.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7259" ObjectName="SW-CX_FJ.CX_FJ_39217SW"/>
     <cge:Meas_Ref ObjectId="43423"/>
    <cge:TPSR_Ref TObjectID="7259"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43424">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4379.000000 -350.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7260" ObjectName="SW-CX_FJ.CX_FJ_39260SW"/>
     <cge:Meas_Ref ObjectId="43424"/>
    <cge:TPSR_Ref TObjectID="7260"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43421">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4337.000000 -305.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7257" ObjectName="SW-CX_FJ.CX_FJ_3926SW"/>
     <cge:Meas_Ref ObjectId="43421"/>
    <cge:TPSR_Ref TObjectID="7257"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43422">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4379.000000 -288.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7258" ObjectName="SW-CX_FJ.CX_FJ_39267SW"/>
     <cge:Meas_Ref ObjectId="43422"/>
    <cge:TPSR_Ref TObjectID="7258"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43200">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4146.416667 -719.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7238" ObjectName="SW-CX_FJ.CX_FJ_1011SW"/>
     <cge:Meas_Ref ObjectId="43200"/>
    <cge:TPSR_Ref TObjectID="7238"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43201">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4145.000000 -898.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7239" ObjectName="SW-CX_FJ.CX_FJ_1016SW"/>
     <cge:Meas_Ref ObjectId="43201"/>
    <cge:TPSR_Ref TObjectID="7239"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4509.582328 -941.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4605.582328 -943.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4653.000000 -61.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4612.000000 -670.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4836.000000 -845.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4832.000000 -430.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43256">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4337.000000 -497.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7247" ObjectName="SW-CX_FJ.CX_FJ_3901SW"/>
     <cge:Meas_Ref ObjectId="43256"/>
    <cge:TPSR_Ref TObjectID="7247"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3605.000000 -464.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3606.000000 -581.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3606.000000 -684.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3605.000000 -783.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3605.000000 -933.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3606.000000 -1050.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3606.000000 -1153.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43202">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4019.000000 -620.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7240" ObjectName="SW-CX_FJ.CX_FJ_1010SW"/>
     <cge:Meas_Ref ObjectId="43202"/>
    <cge:TPSR_Ref TObjectID="7240"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43203">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4189.000000 -789.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7241" ObjectName="SW-CX_FJ.CX_FJ_10117SW"/>
     <cge:Meas_Ref ObjectId="43203"/>
    <cge:TPSR_Ref TObjectID="7241"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43204">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4190.000000 -873.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7242" ObjectName="SW-CX_FJ.CX_FJ_10160SW"/>
     <cge:Meas_Ref ObjectId="43204"/>
    <cge:TPSR_Ref TObjectID="7242"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43205">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4189.000000 -948.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7243" ObjectName="SW-CX_FJ.CX_FJ_10167SW"/>
     <cge:Meas_Ref ObjectId="43205"/>
    <cge:TPSR_Ref TObjectID="7243"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43257">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4307.000000 -546.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7248" ObjectName="SW-CX_FJ.CX_FJ_39017SW"/>
     <cge:Meas_Ref ObjectId="43257"/>
    <cge:TPSR_Ref TObjectID="7248"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3870.000000 -166.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3869.000000 -63.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4001.000000 -63.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3875.000000 -286.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3872.000000 -449.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3871.000000 -793.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3874.000000 -957.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4552.000000 -56.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4511.000000 -150.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4485.000000 -197.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4481.000000 -133.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4484.000000 -70.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4783.000000 -56.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4742.000000 -149.703704)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4716.000000 -197.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4712.000000 -133.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4715.000000 -70.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 5141.000000 -71.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 5141.000000 -196.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 5140.000000 -322.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 5139.000000 -454.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 5140.000000 -617.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 5140.000000 -742.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 5139.000000 -868.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="Generator_Layer">
   <g DF8003:Layer="PUBLIC" id="SM-CX_FJ.CX_FJ_GN1">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4154.000000 -47.000000)" xlink:href="#generator:shape6"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43313" ObjectName="SM-CX_FJ.CX_FJ_GN1"/>
    <cge:TPSR_Ref TObjectID="43313"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_FJ.CX_FJ_GN2">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4473.000000 -209.000000)" xlink:href="#generator:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43314" ObjectName="SM-CX_FJ.CX_FJ_GN2"/>
    <cge:TPSR_Ref TObjectID="43314"/></metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="PAS_T2" endPointId="0" endStationName="CX_FJ" flowDrawDirect="1" flowShape="0" id="AC-110kV.xihudujTfj_line" runFlow="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4160,-1127 4160,-1183 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="14383" ObjectName="AC-110kV.xihudujTfj_line"/>
    <cge:TPSR_Ref TObjectID="14383_SS-49"/></metadata>
   <polyline fill="none" opacity="0" points="4160,-1127 4160,-1183 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_12f07b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4218.416667 -563.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_164a640" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4218.000000 -303.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_16178b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4218.000000 -365.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15fc2b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3757.000000 -49.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1674fb0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3757.000000 -166.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_163d6d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3756.000000 -268.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1681a10" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4412.000000 -415.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_169d830" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4411.000000 -367.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1672260" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4411.000000 -305.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1605ca0" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4448.000000 -871.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15ca050" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4539.000000 -872.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_164c7e0" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4636.000000 -873.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1623600" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 5020.000000 -910.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1660540" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 5021.000000 -784.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15b1300" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 5021.000000 -659.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1664480" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 5021.000000 -364.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_16883e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 5022.000000 -238.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14d6e50" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 5022.000000 -113.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1646cb0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 5020.000000 -496.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_166c1a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3758.000000 -464.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15d3260" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3758.000000 -581.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1512740" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3757.000000 -683.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1589a20" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3756.000000 -782.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15c4970" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3758.000000 -933.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_159c000" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3758.000000 -1050.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15ddd90" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3757.000000 -1152.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15a9e60" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4222.000000 -806.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1525fa0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4223.000000 -890.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1526c90" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4222.000000 -965.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_18031b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4218.000000 -415.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_18039b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4263.000000 -563.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1804380" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4436.000000 -214.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1804db0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4437.000000 -150.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_18057c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4440.000000 -87.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1806250" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4671.000000 -213.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1806ce0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4668.000000 -150.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1807770" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4671.000000 -87.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1808200" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3936.000000 -107.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1808c90" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3999.000000 -108.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1809720" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4069.000000 -107.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_180a1b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4025.000000 -600.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1618db0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4068.000000 -327.000000)" xlink:href="#lightningRod:shape106"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15fb660">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3804.000000 -40.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_16743a0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3805.000000 -156.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_163ca80">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3805.000000 -259.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1605030">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4437.000000 -823.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_16065d0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4394.000000 -1006.000000)" xlink:href="#lightningRod:shape38"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15c95d0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4530.000000 -823.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15ca980">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4487.000000 -1006.000000)" xlink:href="#lightningRod:shape38"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_169d3c0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4626.000000 -825.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_164d110">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4583.000000 -1008.000000)" xlink:href="#lightningRod:shape38"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1630640">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4632.000000 -106.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14f6590">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4632.000000 -676.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_16227f0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4988.000000 -889.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1550230">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4842.000000 -825.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_165f9c0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4989.000000 -763.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15b0740">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4989.000000 -638.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1663760">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4989.000000 -343.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1687900">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4990.000000 -217.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14d62f0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4990.000000 -92.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1646060">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4988.000000 -475.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1609d30">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4839.000000 -410.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1616580">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4515.000000 -565.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_166b180">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3805.000000 -454.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15d24a0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3806.000000 -571.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1511980">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3806.000000 -674.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15889d0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3805.000000 -773.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1517320">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3942.000000 -439.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15c1930">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3940.000000 -277.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15c3900">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3805.000000 -923.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_159b240">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3806.000000 -1040.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15dcfd0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3806.000000 -1143.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1540370">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3944.000000 -947.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15418a0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3942.000000 -784.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_152a1d0">
    <use class="BV-110KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4257.000000 -999.000000)" xlink:href="#lightningRod:shape106"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_155e9b0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3614.000000 -1133.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_155fa70">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3750.000000 -1133.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1560c80">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3616.000000 -1030.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15619d0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3752.000000 -1030.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14d8da0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3616.000000 -913.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14d99e0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3752.000000 -913.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14db0b0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3674.000000 -1144.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14dbe60">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3674.000000 -1044.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14dcc10">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3673.000000 -927.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14dd9c0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3673.000000 -777.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14de770">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3674.000000 -684.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14df520">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3674.000000 -581.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14e02d0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3673.000000 -464.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14e1080">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3613.000000 -763.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14e1dd0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3749.000000 -763.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14e34a0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3615.000000 -664.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14e41f0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3751.000000 -664.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14e58c0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3616.000000 -561.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14e6610">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3752.000000 -561.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14e7ce0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3750.000000 -444.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14e8a30">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3614.000000 -444.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14ea100">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3673.000000 -269.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14fa5a0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3673.000000 -166.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14fb350">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3672.000000 -49.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14fc100">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3612.000000 -249.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14fce50">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3748.000000 -249.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14fe520">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3749.000000 -146.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14ff270">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3613.000000 -146.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1500940">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3611.000000 -29.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1501690">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3747.000000 -29.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1502d60">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3936.000000 -162.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1503b10">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4077.000000 -259.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15048c0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3931.000000 -231.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1506160">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 -1.000000 -0.000000 4001.500000 -160.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1506ea0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3924.000000 -394.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1507c50">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3927.000000 -738.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1508a00">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3926.000000 -901.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15097b0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4273.000000 -604.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_150a560">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4582.000000 -602.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_150b310">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4343.000000 -623.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_150c050">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4280.000000 -259.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_16ed1f0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4261.000000 -328.000000)" xlink:href="#lightningRod:shape106"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_16ee700">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4427.000000 -878.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_16ef910">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4520.000000 -880.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_16f0b20">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4616.000000 -880.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_16f1d30">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4616.000000 -1014.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_16f2f40">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4520.000000 -1012.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_16f4150">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4427.000000 -1012.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_16f5360">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4539.000000 -250.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_16f6110">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4634.000000 -250.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_16f6ec0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4770.000000 -251.296296)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_16f7c70">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4765.000000 -431.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_16f8a20">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4768.000000 -846.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_16f97d0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5108.000000 -889.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_16fa520">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5236.000000 -889.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_16fbbf0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5106.000000 -909.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_16fc9a0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5107.000000 -783.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_16fd750">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5109.000000 -763.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_16fe4a0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5237.000000 -763.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_16ffb70">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5110.000000 -638.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_17008c0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5238.000000 -638.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1701f90">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5107.000000 -658.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1702d40">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5107.000000 -475.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_149b700">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5235.000000 -475.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_149cdd0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5106.000000 -495.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_149db80">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5108.000000 -343.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_149e8d0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5236.000000 -343.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_149ffa0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5107.000000 -363.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14a0d50">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5109.000000 -217.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14a1aa0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5237.000000 -217.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14a3170">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5108.000000 -237.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14a3f20">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5111.000000 -92.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14a4c70">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5239.000000 -92.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14a6340">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5108.000000 -112.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_180b1d0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4199.000000 -1079.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3234.000000 -1118.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="0" id="ME-43828" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4265.000000 -704.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43828" ObjectName="CX_FJ:CX_FJ_1T_Tp"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-43167" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4265.000000 -675.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43167" ObjectName="CX_FJ:CX_FJ_1T_Tmp1"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-43141" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4113.000000 -881.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43141" ObjectName="CX_FJ:CX_FJ_101BK_Ua"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-43142" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4113.000000 -862.333333) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43142" ObjectName="CX_FJ:CX_FJ_101BK_Ub"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-43143" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4113.000000 -843.666667) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43143" ObjectName="CX_FJ:CX_FJ_101BK_Uc"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-43153" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4113.000000 -825.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43153" ObjectName="CX_FJ:CX_FJ_101BK_Uab"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-43147" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4039.000000 -529.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43147" ObjectName="CX_FJ:CX_FJ_301BK_Ua"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-43148" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4039.000000 -510.333333) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43148" ObjectName="CX_FJ:CX_FJ_301BK_Ub"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-43149" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4039.000000 -491.666667) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43149" ObjectName="CX_FJ:CX_FJ_301BK_Uc"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-43156" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4039.000000 -473.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43156" ObjectName="CX_FJ:CX_FJ_301BK_Uab"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-78590" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3254.538462 -1016.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78590" ObjectName="CX_FJ:CX_FJ_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-79721" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3252.538462 -973.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79721" ObjectName="CX_FJ:CX_FJ_sumQ"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-43179" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4235.000000 -264.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43179" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7249"/>
     <cge:Term_Ref ObjectID="10607"/>
    <cge:TPSR_Ref TObjectID="7249"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-43180" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4235.000000 -264.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43180" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7249"/>
     <cge:Term_Ref ObjectID="10607"/>
    <cge:TPSR_Ref TObjectID="7249"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-43169" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4235.000000 -264.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43169" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7249"/>
     <cge:Term_Ref ObjectID="10607"/>
    <cge:TPSR_Ref TObjectID="7249"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-43193" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4357.000000 -215.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43193" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7255"/>
     <cge:Term_Ref ObjectID="10619"/>
    <cge:TPSR_Ref TObjectID="7255"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-43194" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4357.000000 -215.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43194" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7255"/>
     <cge:Term_Ref ObjectID="10619"/>
    <cge:TPSR_Ref TObjectID="7255"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-43183" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4357.000000 -215.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43183" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7255"/>
     <cge:Term_Ref ObjectID="10619"/>
    <cge:TPSR_Ref TObjectID="7255"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-43163" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4036.416667 -589.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43163" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7244"/>
     <cge:Term_Ref ObjectID="10595"/>
    <cge:TPSR_Ref TObjectID="7244"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-43164" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4036.416667 -589.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43164" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7244"/>
     <cge:Term_Ref ObjectID="10595"/>
    <cge:TPSR_Ref TObjectID="7244"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-43150" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4036.416667 -589.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43150" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7244"/>
     <cge:Term_Ref ObjectID="10595"/>
    <cge:TPSR_Ref TObjectID="7244"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-43159" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4311.000000 -949.500000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43159" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7237"/>
     <cge:Term_Ref ObjectID="10511"/>
    <cge:TPSR_Ref TObjectID="7237"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-43160" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4311.000000 -949.500000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43160" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7237"/>
     <cge:Term_Ref ObjectID="10511"/>
    <cge:TPSR_Ref TObjectID="7237"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-43144" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4311.000000 -949.500000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43144" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7237"/>
     <cge:Term_Ref ObjectID="10511"/>
    <cge:TPSR_Ref TObjectID="7237"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="3246" y="-1177"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="3246" y="-1177"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3197" y="-1194"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3197" y="-1194"/></g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_地调直调.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="3246" y="-1177"/></g>
   <g href="cx_索引_接线图_地调直调.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3197" y="-1194"/></g>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-43207">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4151.416667 -584.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7244" ObjectName="SW-CX_FJ.CX_FJ_301BK"/>
     <cge:Meas_Ref ObjectId="43207"/>
    <cge:TPSR_Ref TObjectID="7244"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43395">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4150.000000 -378.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7249" ObjectName="SW-CX_FJ.CX_FJ_391BK"/>
     <cge:Meas_Ref ObjectId="43395"/>
    <cge:TPSR_Ref TObjectID="7249"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3999.000000 -39.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43418">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4343.000000 -376.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7255" ObjectName="SW-CX_FJ.CX_FJ_392BK"/>
     <cge:Meas_Ref ObjectId="43418"/>
    <cge:TPSR_Ref TObjectID="7255"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43198">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4151.000000 -839.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7237" ObjectName="SW-CX_FJ.CX_FJ_101BK"/>
     <cge:Meas_Ref ObjectId="43198"/>
    <cge:TPSR_Ref TObjectID="7237"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4517.000000 -107.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4748.000000 -104.296296)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4349,-754 4676,-754 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4349,-754 4676,-754 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_FJ.CX_FJ_3M">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4080,-493 4559,-493 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="10773" ObjectName="BS-CX_FJ.CX_FJ_3M"/>
    <cge:TPSR_Ref TObjectID="10773"/></metadata>
   <polyline fill="none" opacity="0" points="4080,-493 4559,-493 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3871,-288 3871,-17 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="3871,-288 3871,-17 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4406,-34 4863,-34 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4406,-34 4863,-34 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4922,-969 4922,-608 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4922,-969 4922,-608 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4923,-506 4923,-33 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4923,-506 4923,-33 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3872,-799 3872,-409 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="3872,-799 3872,-409 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3872,-1172 3872,-901 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="3872,-1172 3872,-901 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_FJ.XM">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4155,-797 4166,-797 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="47737" ObjectName="BS-CX_FJ.XM"/>
    <cge:TPSR_Ref TObjectID="47737"/></metadata>
   <polyline fill="none" opacity="0" points="4155,-797 4166,-797 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="HydroGenerator_Layer">
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3487.500000 -62.500000)" xlink:href="#hydroGenerator:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3491.500000 -179.500000)" xlink:href="#hydroGenerator:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3488.500000 -282.500000)" xlink:href="#hydroGenerator:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4460.500000 -1134.500000)" xlink:href="#hydroGenerator:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4553.500000 -1134.500000)" xlink:href="#hydroGenerator:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4649.500000 -1136.500000)" xlink:href="#hydroGenerator:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5298.500000 -865.500000)" xlink:href="#hydroGenerator:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5299.500000 -739.500000)" xlink:href="#hydroGenerator:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5300.500000 -614.500000)" xlink:href="#hydroGenerator:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5303.500000 -319.500000)" xlink:href="#hydroGenerator:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5304.500000 -193.500000)" xlink:href="#hydroGenerator:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5304.500000 -68.500000)" xlink:href="#hydroGenerator:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5302.500000 -451.500000)" xlink:href="#hydroGenerator:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3488.500000 -477.500000)" xlink:href="#hydroGenerator:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3492.500000 -594.500000)" xlink:href="#hydroGenerator:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3492.500000 -697.500000)" xlink:href="#hydroGenerator:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3488.500000 -796.500000)" xlink:href="#hydroGenerator:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3488.500000 -946.500000)" xlink:href="#hydroGenerator:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3491.500000 -1063.500000)" xlink:href="#hydroGenerator:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3490.500000 -1166.500000)" xlink:href="#hydroGenerator:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="3871" cy="-34" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="3871" cy="-151" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="3871" cy="-253" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="3871" cy="-48" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="3871" cy="-151" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="3872" cy="-449" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="3872" cy="-566" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="3872" cy="-668" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="3872" cy="-767" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="3872" cy="-918" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="3872" cy="-1034" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="3872" cy="-1137" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="3872" cy="-778" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4627" cy="-754" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4431" cy="-754" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4524" cy="-754" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4620" cy="-754" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4922" cy="-895" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4922" cy="-769" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4922" cy="-644" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4923" cy="-349" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4923" cy="-223" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4923" cy="-98" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4923" cy="-481" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10773" cx="4159" cy="-493" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10773" cx="4352" cy="-493" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10773" cx="4510" cy="-493" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="3871" cy="-271" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="3872" cy="-434" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4922" cy="-830" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4923" cy="-415" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4628" cy="-35" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4527" cy="-33" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4757" cy="-33" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="3872" cy="-942" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10773" cx="4352" cy="-493" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10773" cx="4160" cy="-493" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47737" cx="4161" cy="-796" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47737" cx="4161" cy="-796" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1679690" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4410.000000 -1168.000000) translate(0,12)">1号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1679690" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4410.000000 -1168.000000) translate(0,27)">630kw</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15e9cc0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4280.000000 -764.000000) translate(0,12)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_130b8d0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3521.000000 -94.000000) translate(0,12)">3号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_130b8d0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3521.000000 -94.000000) translate(0,27)">800kw</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15e9530" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3522.000000 -211.000000) translate(0,12)">2号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15e9530" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3522.000000 -211.000000) translate(0,27)">800kw</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13eaf20" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3522.000000 -314.000000) translate(0,12)">1号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13eaf20" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3522.000000 -314.000000) translate(0,27)">800kw</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1658920" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3479.000000 -196.000000) translate(0,12)">麻</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1658920" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3479.000000 -196.000000) translate(0,27)">旺</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1658920" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3479.000000 -196.000000) translate(0,42)">河</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1658920" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3479.000000 -196.000000) translate(0,57)">三</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1658920" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3479.000000 -196.000000) translate(0,72)">级</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1658920" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3479.000000 -196.000000) translate(0,87)">电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1658920" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3479.000000 -196.000000) translate(0,102)">站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1652d00" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4503.000000 -1168.000000) translate(0,12)">2号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1652d00" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4503.000000 -1168.000000) translate(0,27)">630kw</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_169cff0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4599.000000 -1170.000000) translate(0,12)">3号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_169cff0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4599.000000 -1170.000000) translate(0,27)">630kw</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_165ef10" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5224.000000 -956.000000) translate(0,12)">1号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_165ef10" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5224.000000 -956.000000) translate(0,27)">800kw</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_165f400" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4969.000000 -928.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14f8e50" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5225.000000 -830.000000) translate(0,12)">2号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14f8e50" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5225.000000 -830.000000) translate(0,27)">800kw</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14f9480" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4970.000000 -802.000000) translate(0,12)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1662760" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5225.000000 -705.000000) translate(0,12)">3号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1662760" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5225.000000 -705.000000) translate(0,27)">500kw</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1662c50" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4970.000000 -677.000000) translate(0,12)">3号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1495700" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5225.000000 -410.000000) translate(0,12)">2号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1495700" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5225.000000 -410.000000) translate(0,27)">800kw</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16876c0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4970.000000 -382.000000) translate(0,12)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14d5bc0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5226.000000 -284.000000) translate(0,12)">3号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14d5bc0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5226.000000 -284.000000) translate(0,27)">800kw</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14d60b0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4971.000000 -256.000000) translate(0,12)">3号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15344c0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5226.000000 -159.000000) translate(0,12)">4号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15344c0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5226.000000 -159.000000) translate(0,27)">800kw</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1534c80" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4971.000000 -131.000000) translate(0,12)">4号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16084b0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5224.000000 -542.000000) translate(0,12)">1号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16084b0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5224.000000 -542.000000) translate(0,27)">800kw</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16089a0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4969.000000 -514.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15d1fb0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3522.000000 -509.000000) translate(0,12)">4号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15d1fb0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3522.000000 -509.000000) translate(0,27)">800kw</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1511490" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3523.000000 -626.000000) translate(0,12)">3号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1511490" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3523.000000 -626.000000) translate(0,27)">800kw</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_157afc0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3523.000000 -729.000000) translate(0,12)">2号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_157afc0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3523.000000 -729.000000) translate(0,27)">800kw</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_157b4b0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3479.000000 -679.000000) translate(0,12)">麻</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_157b4b0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3479.000000 -679.000000) translate(0,27)">旺</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_157b4b0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3479.000000 -679.000000) translate(0,42)">河</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_157b4b0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3479.000000 -679.000000) translate(0,57)">二</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_157b4b0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3479.000000 -679.000000) translate(0,72)">级</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_157b4b0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3479.000000 -679.000000) translate(0,87)">电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_157b4b0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3479.000000 -679.000000) translate(0,102)">站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1513a90" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3522.000000 -828.000000) translate(0,12)">1号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1513a90" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3522.000000 -828.000000) translate(0,27)">800kw</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_159ad50" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3522.000000 -978.000000) translate(0,12)">3号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_159ad50" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3522.000000 -978.000000) translate(0,27)">800kw</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15dcae0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3523.000000 -1095.000000) translate(0,12)">2号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15dcae0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3523.000000 -1095.000000) translate(0,27)">800kw</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1539220" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3523.000000 -1198.000000) translate(0,12)">1号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1539220" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3523.000000 -1198.000000) translate(0,27)">800kw</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15396e0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3478.000000 -1093.000000) translate(0,12)">麻</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15396e0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3478.000000 -1093.000000) translate(0,27)">旺</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15396e0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3478.000000 -1093.000000) translate(0,42)">河</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15396e0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3478.000000 -1093.000000) translate(0,57)">一</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15396e0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3478.000000 -1093.000000) translate(0,72)">级</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15396e0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3478.000000 -1093.000000) translate(0,87)">电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15396e0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3478.000000 -1093.000000) translate(0,102)">站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15b4de0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3776.000000 -64.000000) translate(0,12)">3号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15b52d0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3776.000000 -181.000000) translate(0,12)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15b5510" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3776.000000 -283.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15b5750" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3840.000000 -312.000000) translate(0,12)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15b5990" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3774.000000 -479.000000) translate(0,12)">4号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15b5bd0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3775.000000 -595.000000) translate(0,12)">3号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15b5e10" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3776.000000 -701.000000) translate(0,12)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15b6050" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3774.000000 -798.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15b6290" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3773.000000 -948.000000) translate(0,12)">3号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15b64d0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3775.000000 -1066.000000) translate(0,12)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15b6710" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3774.000000 -1168.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15b6950" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4024.000000 -962.000000) translate(0,12)">35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15b6950" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4024.000000 -962.000000) translate(0,27)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15b6950" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4024.000000 -962.000000) translate(0,42)">麻</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15b6950" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4024.000000 -962.000000) translate(0,57)">一</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15b6950" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4024.000000 -962.000000) translate(0,72)">二</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15b6950" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4024.000000 -962.000000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15b6b70" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4033.000000 -402.000000) translate(0,12)">35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15b6b70" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4033.000000 -402.000000) translate(0,27)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15b6b70" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4033.000000 -402.000000) translate(0,42)">麻</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15b6b70" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4033.000000 -402.000000) translate(0,57)">二</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15b6b70" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4033.000000 -402.000000) translate(0,72)">三</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15b6b70" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4033.000000 -402.000000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15b6db0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3843.000000 -818.000000) translate(0,12)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15b7010" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3843.000000 -894.000000) translate(0,12)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15b9300" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4490.000000 -1198.000000) translate(0,12)">班果河一级站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15b9db0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4893.000000 -603.000000) translate(0,12)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15ba030" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4364.000000 -849.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15ba270" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4459.000000 -849.000000) translate(0,12)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15ba4b0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4556.000000 -849.000000) translate(0,12)">3号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15ba6f0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5299.000000 -811.000000) translate(0,12)">纳</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15ba6f0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5299.000000 -811.000000) translate(0,27)">嫩</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15ba6f0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5299.000000 -811.000000) translate(0,42)">河</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15ba6f0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5299.000000 -811.000000) translate(0,57)">一</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15ba6f0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5299.000000 -811.000000) translate(0,72)">级</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15ba6f0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5299.000000 -811.000000) translate(0,87)">电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15ba6f0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5299.000000 -811.000000) translate(0,102)">站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15baf50" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5300.000000 -344.000000) translate(0,12)">纳</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15baf50" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5300.000000 -344.000000) translate(0,27)">嫩</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15baf50" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5300.000000 -344.000000) translate(0,42)">河</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15baf50" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5300.000000 -344.000000) translate(0,57)">三</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15baf50" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5300.000000 -344.000000) translate(0,72)">级</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15baf50" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5300.000000 -344.000000) translate(0,87)">电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15baf50" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5300.000000 -344.000000) translate(0,102)">站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15bb190" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4892.000000 -525.000000) translate(0,12)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15bda30" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4790.000000 -401.000000) translate(0,12)">35kV纳三支线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15be1c0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4687.000000 -464.000000) translate(0,12)">35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15be1c0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4687.000000 -464.000000) translate(0,27)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15be1c0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4687.000000 -464.000000) translate(0,42)">班</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15be1c0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4687.000000 -464.000000) translate(0,57)">纳</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15be1c0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4687.000000 -464.000000) translate(0,72)">一</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15be1c0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4687.000000 -464.000000) translate(0,87)">级</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15be1c0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4687.000000 -464.000000) translate(0,102)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15be420" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4593.000000 -467.000000) translate(0,12)">35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15be420" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4593.000000 -467.000000) translate(0,27)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15be420" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4593.000000 -467.000000) translate(0,42)">班</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15be420" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4593.000000 -467.000000) translate(0,57)">二</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15be420" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4593.000000 -467.000000) translate(0,72)">三</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15be420" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4593.000000 -467.000000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15be660" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4436.000000 -274.000000) translate(0,12)">35kV福班线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15a1600" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4598.000000 -21.000000) translate(0,12)">班果河三级站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15a1e50" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4128.000000 -180.000000) translate(0,12)">35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15a1e50" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4128.000000 -180.000000) translate(0,27)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15a1e50" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4128.000000 -180.000000) translate(0,42)">福</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15a1e50" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4128.000000 -180.000000) translate(0,57)">麻</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15a1e50" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4128.000000 -180.000000) translate(0,72)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15a3580" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4133.000000 -1193.000000) translate(0,12)">110</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15a3580" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4133.000000 -1193.000000) translate(0,27)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15a3580" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4133.000000 -1193.000000) translate(0,42)">西</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15a3580" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4133.000000 -1193.000000) translate(0,57)">虎</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15a3580" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4133.000000 -1193.000000) translate(0,72)">杜</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15a3580" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4133.000000 -1193.000000) translate(0,87)">嘉</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15a3580" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4133.000000 -1193.000000) translate(0,102)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_152c4c0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3928.000000 -1183.000000) translate(0,12)">老虎</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_152c4c0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3928.000000 -1183.000000) translate(0,27)">山二</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_152c4c0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3928.000000 -1183.000000) translate(0,42)">级站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14a70f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4121.416667 -612.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14a7720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4113.416667 -545.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14a7960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4179.416667 -595.000000) translate(0,12)">30117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14a7da0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4170.000000 -869.000000) translate(0,12)">101</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14a81d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4168.416667 -767.000000) translate(0,12)">1011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14a8410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4179.000000 -841.000000) translate(0,12)">10117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14a8650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4179.000000 -925.000000) translate(0,12)">10160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14a8890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4168.000000 -946.000000) translate(0,12)">1016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14a8ad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4178.000000 -1000.000000) translate(0,12)">10167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14a8d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4359.000000 -544.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14a9230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4296.000000 -598.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14a94b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4121.000000 -407.000000) translate(0,12)">391</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14a96f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4113.000000 -466.000000) translate(0,12)">3911</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14a9930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4112.000000 -353.000000) translate(0,12)">3916</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14a9b70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4175.000000 -450.000000) translate(0,12)">39117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14a9db0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4175.000000 -400.000000) translate(0,12)">39160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14a9ff0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4175.000000 -338.000000) translate(0,12)">39167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14aa230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4312.000000 -405.000000) translate(0,12)">392</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14aa470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4304.000000 -465.000000) translate(0,12)">3921</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14aa6b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4303.000000 -352.000000) translate(0,12)">3926</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14aa8f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4369.000000 -450.000000) translate(0,12)">39217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14aab30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4367.000000 -402.000000) translate(0,12)">39260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14aad70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4367.000000 -340.000000) translate(0,12)">39267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_14aafb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4090.000000 -741.000000) translate(0,15)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14abdb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4041.000000 -667.000000) translate(0,12)">1010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14b20d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,17)">危险点说明:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14b20d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14b20d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14b20d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14b20d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14b20d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14b20d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14b20d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14b20d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14b20d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14b20d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14b20d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14b20d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14b20d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14b20d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14b20d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14b20d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14b20d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,374)">联系方式:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_17e2330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,17)">频率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_17e2330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_17e2330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,59)">全站有功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_17e2330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_17e2330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,101)">全站无功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_17e2330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_17e2330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,143)">并网联络点的电压和交换功率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_17ebcc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3272.000000 -1166.500000) translate(0,16)">福嘉升压站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17edf40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4080.000000 -513.000000) translate(0,12)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17efe60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4477.000000 -676.000000) translate(0,12)">1</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17efe60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4477.000000 -676.000000) translate(0,27)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17efe60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4477.000000 -676.000000) translate(0,42)">站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17efe60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4477.000000 -676.000000) translate(0,57)">用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17efe60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4477.000000 -676.000000) translate(0,72)">变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17f0730" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3887.000000 -39.000000) translate(0,12)">3311</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17f09a0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3961.000000 -39.000000) translate(0,12)">331</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17f0be0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4025.000000 -39.000000) translate(0,12)">3316</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17f0e20" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3893.000000 -91.000000) translate(0,12)">33117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17f1060" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3959.000000 -91.000000) translate(0,12)">33160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17f12a0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4025.000000 -90.000000) translate(0,12)">33167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17f14e0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3884.000000 -142.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17f1720" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3895.000000 -264.000000) translate(0,12)">3301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17f1960" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3897.000000 -430.000000) translate(0,12)">3202</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17f1ba0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3899.000000 -773.000000) translate(0,12)">3201</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17f1de0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3900.000000 -937.000000) translate(0,12)">3101</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17f2020" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3629.000000 -1133.000000) translate(0,12)">4001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17f2260" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3627.000000 -1030.000000) translate(0,12)">4002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17f24a0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3623.000000 -912.000000) translate(0,12)">4003</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17f26e0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3623.000000 -764.000000) translate(0,12)">4001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17f2920" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3623.000000 -661.000000) translate(0,12)">4002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17f2b60" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3623.000000 -560.000000) translate(0,12)">4003</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17f2da0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3623.000000 -246.000000) translate(0,12)">4001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17f2fe0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3623.000000 -143.000000) translate(0,12)">4002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17f3220" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3623.000000 -25.000000) translate(0,12)">4003</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17f3460" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3623.000000 -444.000000) translate(0,12)">4004</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17f36a0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4440.000000 -989.000000) translate(0,12)">4001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17f38e0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4534.000000 -989.000000) translate(0,12)">4002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17f3b20" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4630.000000 -989.000000) translate(0,12)">4003</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17f3d60" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4637.000000 -717.000000) translate(0,12)">3701</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17f3fa0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4863.000000 -856.000000) translate(0,12)">3401</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17f41e0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4860.000000 -442.000000) translate(0,12)">3601</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17f4420" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5139.000000 -886.000000) translate(0,12)">4001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17f4660" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5139.000000 -762.000000) translate(0,12)">4002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17f48a0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5139.000000 -636.000000) translate(0,12)">4003</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17f4ae0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5139.000000 -471.000000) translate(0,12)">4001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17f4d20" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5139.000000 -341.000000) translate(0,12)">4002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17f4f60" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5139.000000 -211.000000) translate(0,12)">4003</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17f51a0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5139.000000 -88.000000) translate(0,12)">4004</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17f53e0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4535.000000 -196.000000) translate(0,12)">3816</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17f5620" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4536.000000 -138.000000) translate(0,12)">381</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17f5860" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4532.000000 -74.000000) translate(0,12)">3811</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17f5aa0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4471.000000 -217.000000) translate(0,12)">38167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17f5ce0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4470.000000 -152.000000) translate(0,12)">38160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17f5f20" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4469.000000 -90.000000) translate(0,12)">38117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17f6160" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4768.000000 -193.000000) translate(0,12)">3826</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17f63a0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4769.000000 -135.000000) translate(0,12)">382</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17f65e0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4765.000000 -71.000000) translate(0,12)">3821</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17f6820" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4702.000000 -87.000000) translate(0,12)">38217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17f6a60" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4703.000000 -149.000000) translate(0,12)">38260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17f6ca0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4704.000000 -214.000000) translate(0,12)">38267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17f6ee0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4635.000000 -77.000000) translate(0,12)">3801</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17fd530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4220.000000 -704.000000) translate(0,12)">档位:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17fe160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4220.000000 -677.000000) translate(0,12)">温度:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_180e500" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3250.000000 -216.000000) translate(0,12)">15125745338</text>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14acac0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4175.000000 264.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14adf30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4164.000000 249.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14aec80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4189.000000 234.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14afc10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4302.000000 215.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14afe90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4291.000000 200.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14b00d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4316.000000 185.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14b0ac0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3977.000000 589.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14b0d50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3966.000000 574.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14b0f90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3991.000000 559.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 262.000000 -364.500000)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14b1980" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3994.000000 588.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14b1c50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3983.000000 573.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14b1e90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4008.000000 558.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18014f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4056.000000 844.333333) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1801db0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4048.000000 827.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1802340" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4056.000000 861.666667) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1802550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4056.000000 879.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1802880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3977.000000 492.333333) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1802af0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3969.000000 475.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1802d30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3977.000000 509.666667) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1802f70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3977.000000 527.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3802.000000 -21.000000)" xlink:href="#transformer2:shape11_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3802.000000 -21.000000)" xlink:href="#transformer2:shape11_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3802.000000 -138.000000)" xlink:href="#transformer2:shape11_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3802.000000 -138.000000)" xlink:href="#transformer2:shape11_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3801.000000 -241.000000)" xlink:href="#transformer2:shape11_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3801.000000 -241.000000)" xlink:href="#transformer2:shape11_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4445.000000 -878.000000)" xlink:href="#transformer2:shape11_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4445.000000 -878.000000)" xlink:href="#transformer2:shape11_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4538.000000 -879.000000)" xlink:href="#transformer2:shape11_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4538.000000 -879.000000)" xlink:href="#transformer2:shape11_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4634.000000 -880.000000)" xlink:href="#transformer2:shape11_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4634.000000 -880.000000)" xlink:href="#transformer2:shape11_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4993.000000 -907.000000)" xlink:href="#transformer2:shape11_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4993.000000 -907.000000)" xlink:href="#transformer2:shape11_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4994.000000 -781.000000)" xlink:href="#transformer2:shape11_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4994.000000 -781.000000)" xlink:href="#transformer2:shape11_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4994.000000 -656.000000)" xlink:href="#transformer2:shape11_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4994.000000 -656.000000)" xlink:href="#transformer2:shape11_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4994.000000 -361.000000)" xlink:href="#transformer2:shape11_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4994.000000 -361.000000)" xlink:href="#transformer2:shape11_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4995.000000 -235.000000)" xlink:href="#transformer2:shape11_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4995.000000 -235.000000)" xlink:href="#transformer2:shape11_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4995.000000 -110.000000)" xlink:href="#transformer2:shape11_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4995.000000 -110.000000)" xlink:href="#transformer2:shape11_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4993.000000 -493.000000)" xlink:href="#transformer2:shape11_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4993.000000 -493.000000)" xlink:href="#transformer2:shape11_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3803.000000 -436.000000)" xlink:href="#transformer2:shape11_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3803.000000 -436.000000)" xlink:href="#transformer2:shape11_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3803.000000 -553.000000)" xlink:href="#transformer2:shape11_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3803.000000 -553.000000)" xlink:href="#transformer2:shape11_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3802.000000 -656.000000)" xlink:href="#transformer2:shape11_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3802.000000 -656.000000)" xlink:href="#transformer2:shape11_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3801.000000 -755.000000)" xlink:href="#transformer2:shape11_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3801.000000 -755.000000)" xlink:href="#transformer2:shape11_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3803.000000 -905.000000)" xlink:href="#transformer2:shape11_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3803.000000 -905.000000)" xlink:href="#transformer2:shape11_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3803.000000 -1022.000000)" xlink:href="#transformer2:shape11_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3803.000000 -1022.000000)" xlink:href="#transformer2:shape11_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3802.000000 -1125.000000)" xlink:href="#transformer2:shape11_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3802.000000 -1125.000000)" xlink:href="#transformer2:shape11_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_FJ.CX_FJ_Zyb">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="10633"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4495.000000 -591.000000)" xlink:href="#transformer2:shape24_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4495.000000 -591.000000)" xlink:href="#transformer2:shape24_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="7317" ObjectName="TF-CX_FJ.CX_FJ_Zyb"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_FJ.CX_FJ_T1">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="19550"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4135.416667 -638.000000)" xlink:href="#transformer2:shape100_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4135.416667 -638.000000)" xlink:href="#transformer2:shape100_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="43447" ObjectName="TF-CX_FJ.CX_FJ_T1"/>
    <cge:TPSR_Ref TObjectID="43447"/></metadata>
   </g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-37318" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3416.000000 -1086.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5900" ObjectName="DYN-CX_FJ"/>
     <cge:Meas_Ref ObjectId="37318"/>
    </metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_15efd70">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4013.000000 -141.000000)" xlink:href="#voltageTransformer:shape6"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_16147f0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4336.000000 -669.000000)" xlink:href="#voltageTransformer:shape11"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_1598b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4160,-643 4160,-619 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="43447@1" ObjectIDZND0="7244@1" Pin0InfoVect0LinkObjId="SW-43207_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4160,-643 4160,-619 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1596d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4160,-493 4160,-520 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="10773@0" ObjectIDZND0="7245@0" Pin0InfoVect0LinkObjId="SW-43209_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4160,-493 4160,-520 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_15af500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4159,-493 4159,-477 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="10773@0" ObjectIDZND0="7250@1" Pin0InfoVect0LinkObjId="SW-43397_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4159,-493 4159,-477 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_15e8ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4159,-425 4177,-425 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="7249@x" ObjectIDND1="7250@x" ObjectIDZND0="7253@0" Pin0InfoVect0LinkObjId="SW-43400_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-43395_0" Pin1InfoVect1LinkObjId="SW-43397_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4159,-425 4177,-425 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1604ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4213,-424 4223,-424 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="7253@1" ObjectIDZND0="g_18031b0@0" Pin0InfoVect0LinkObjId="g_18031b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43400_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4213,-424 4223,-424 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_15d9790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4159,-425 4159,-413 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="7253@x" ObjectIDND1="7250@x" ObjectIDZND0="7249@1" Pin0InfoVect0LinkObjId="SW-43395_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-43400_0" Pin1InfoVect1LinkObjId="SW-43397_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4159,-425 4159,-413 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_15d9980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4159,-441 4159,-425 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="7250@0" ObjectIDZND0="7253@x" ObjectIDZND1="7249@x" Pin0InfoVect0LinkObjId="SW-43400_0" Pin0InfoVect1LinkObjId="SW-43395_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43397_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4159,-441 4159,-425 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_164adf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4159,-313 4177,-313 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="7251@x" ObjectIDND1="g_1618db0@0" ObjectIDND2="g_1503b10@0" ObjectIDZND0="7252@0" Pin0InfoVect0LinkObjId="SW-43399_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-43398_0" Pin1InfoVect1LinkObjId="g_1618db0_0" Pin1InfoVect2LinkObjId="g_1503b10_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4159,-313 4177,-313 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_164afe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4213,-312 4223,-312 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="7252@1" ObjectIDZND0="g_164a640@0" Pin0InfoVect0LinkObjId="g_164a640_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43399_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4213,-312 4223,-312 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_164b7a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4159,-328 4159,-313 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="7251@0" ObjectIDZND0="7252@x" ObjectIDZND1="g_1618db0@0" ObjectIDZND2="g_1503b10@0" Pin0InfoVect0LinkObjId="SW-43399_0" Pin0InfoVect1LinkObjId="g_1618db0_0" Pin0InfoVect2LinkObjId="g_1503b10_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43398_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4159,-328 4159,-313 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1618020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4159,-375 4177,-375 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="7249@x" ObjectIDND1="7251@x" ObjectIDZND0="7254@0" Pin0InfoVect0LinkObjId="SW-43401_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-43395_0" Pin1InfoVect1LinkObjId="SW-43398_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4159,-375 4177,-375 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1618210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4213,-374 4223,-374 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="7254@1" ObjectIDZND0="g_16178b0@0" Pin0InfoVect0LinkObjId="g_16178b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43401_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4213,-374 4223,-374 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16189d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4159,-386 4159,-375 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="7249@0" ObjectIDZND0="7254@x" ObjectIDZND1="7251@x" Pin0InfoVect0LinkObjId="SW-43401_0" Pin0InfoVect1LinkObjId="SW-43398_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43395_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4159,-386 4159,-375 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1618bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4159,-375 4159,-364 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="7254@x" ObjectIDND1="7249@x" ObjectIDZND0="7251@1" Pin0InfoVect0LinkObjId="SW-43398_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-43401_0" Pin1InfoVect1LinkObjId="SW-43395_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4159,-375 4159,-364 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_15fb2e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4159,-313 4159,-287 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="generator" ObjectIDND0="7252@x" ObjectIDND1="7251@x" ObjectIDZND0="g_1618db0@0" ObjectIDZND1="g_1503b10@0" ObjectIDZND2="43313@x" Pin0InfoVect0LinkObjId="g_1618db0_0" Pin0InfoVect1LinkObjId="g_1503b10_0" Pin0InfoVect2LinkObjId="SM-CX_FJ.CX_FJ_GN1_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-43399_0" Pin1InfoVect1LinkObjId="SW-43398_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4159,-313 4159,-287 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15fbed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3871,-34 3854,-34 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_15fb660@0" Pin0InfoVect0LinkObjId="g_15fb660_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3871,-34 3854,-34 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15fc0c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3766,-34 3766,-54 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="earth" ObjectIDND0="0@x" ObjectIDZND0="g_15fc2b0@0" Pin0InfoVect0LinkObjId="g_15fc2b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3766,-34 3766,-54 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15fca60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3679,-34 3679,-54 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_1501690@0" ObjectIDZND0="g_14fb350@0" Pin0InfoVect0LinkObjId="g_14fb350_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_1501690_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3679,-34 3679,-54 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_162c820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3679,-34 3662,-34 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_14fb350@0" ObjectIDND1="g_1501690@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_14fb350_0" Pin1InfoVect1LinkObjId="g_1501690_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3679,-34 3662,-34 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1674bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3871,-151 3855,-151 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_16743a0@0" Pin0InfoVect0LinkObjId="g_16743a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3871,-151 3855,-151 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1674dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3766,-151 3766,-171 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="earth" ObjectIDND0="0@x" ObjectIDZND0="g_1674fb0@0" Pin0InfoVect0LinkObjId="g_1674fb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3766,-151 3766,-171 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1675760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3680,-151 3680,-171 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_14fe520@0" ObjectIDZND0="g_14fa5a0@0" Pin0InfoVect0LinkObjId="g_14fa5a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_14fe520_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3680,-151 3680,-171 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_163c020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3680,-151 3663,-151 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_14fa5a0@0" ObjectIDND1="g_14fe520@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_14fa5a0_0" Pin1InfoVect1LinkObjId="g_14fe520_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3680,-151 3663,-151 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_163d2f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3871,-253 3855,-253 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_163ca80@0" Pin0InfoVect0LinkObjId="g_163ca80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3871,-253 3855,-253 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_163d4e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3765,-254 3765,-273 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="earth" ObjectIDND0="0@x" ObjectIDZND0="g_163d6d0@0" Pin0InfoVect0LinkObjId="g_163d6d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3765,-254 3765,-273 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_163de80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3680,-254 3680,-274 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_14fce50@0" ObjectIDZND0="g_14ea100@0" Pin0InfoVect0LinkObjId="g_14ea100_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_14fce50_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3680,-254 3680,-274 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15d6550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3680,-254 3663,-254 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_14ea100@0" ObjectIDND1="g_14fce50@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_14ea100_0" Pin1InfoVect1LinkObjId="g_14fce50_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3680,-254 3663,-254 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1658ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3871,-48 3891,-48 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3871,-48 3891,-48 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_161aa30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3944,-48 3944,-66 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3944,-48 3944,-66 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_161ac20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3945,-102 3945,-112 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_1808200@0" Pin0InfoVect0LinkObjId="g_1808200_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3945,-102 3945,-112 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_161b470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3927,-48 3944,-48 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3927,-48 3944,-48 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1574780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3963,-48 3944,-48 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3963,-48 3944,-48 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1610920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4007,-49 4007,-67 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4007,-49 4007,-67 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1610b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4008,-103 4008,-113 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_1808c90@0" Pin0InfoVect0LinkObjId="g_1808c90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4008,-103 4008,-113 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_16114b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3990,-48 4007,-48 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3990,-48 4007,-48 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_16116d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4007,-48 4023,-48 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4007,-48 4023,-48 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15eeea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4078,-102 4078,-112 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_1809720@0" Pin0InfoVect0LinkObjId="g_1809720_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4078,-102 4078,-112 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15ef0c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3943,-151 3943,-167 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_1506160@0" ObjectIDZND0="g_1502d60@0" Pin0InfoVect0LinkObjId="g_1502d60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_1506160_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3943,-151 3943,-167 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15efb50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3928,-151 3943,-151 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_1502d60@0" ObjectIDZND1="g_1506160@0" Pin0InfoVect0LinkObjId="g_1502d60_0" Pin0InfoVect1LinkObjId="g_1506160_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3928,-151 3943,-151 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14d2be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3892,-151 3871,-151 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3892,-151 3871,-151 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1536800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4352,-424 4371,-424 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="7256@x" ObjectIDND1="7255@x" ObjectIDZND0="7259@0" Pin0InfoVect0LinkObjId="SW-43423_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-43420_0" Pin1InfoVect1LinkObjId="SW-43418_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4352,-424 4371,-424 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1536a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4407,-424 4417,-424 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="7259@1" ObjectIDZND0="g_1681a10@0" Pin0InfoVect0LinkObjId="g_1681a10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43423_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4407,-424 4417,-424 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_15374b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4352,-440 4352,-424 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="7256@0" ObjectIDZND0="7259@x" ObjectIDZND1="7255@x" Pin0InfoVect0LinkObjId="SW-43423_0" Pin0InfoVect1LinkObjId="SW-43418_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43420_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4352,-440 4352,-424 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16412e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4352,-424 4352,-411 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="7259@x" ObjectIDND1="7256@x" ObjectIDZND0="7255@1" Pin0InfoVect0LinkObjId="SW-43418_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-43423_0" Pin1InfoVect1LinkObjId="SW-43420_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4352,-424 4352,-411 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_169e160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4352,-376 4370,-376 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="7255@x" ObjectIDND1="7257@x" ObjectIDZND0="7260@0" Pin0InfoVect0LinkObjId="SW-43424_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-43418_0" Pin1InfoVect1LinkObjId="SW-43421_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4352,-376 4370,-376 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_169e380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4406,-376 4416,-376 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="7260@1" ObjectIDZND0="g_169d830@0" Pin0InfoVect0LinkObjId="g_169d830_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43424_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4406,-376 4416,-376 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_169ee10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4352,-384 4352,-376 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="7255@0" ObjectIDZND0="7260@x" ObjectIDZND1="7257@x" Pin0InfoVect0LinkObjId="SW-43424_0" Pin0InfoVect1LinkObjId="SW-43421_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43418_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4352,-384 4352,-376 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_166fb30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4352,-376 4352,-363 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="7260@x" ObjectIDND1="7255@x" ObjectIDZND0="7257@1" Pin0InfoVect0LinkObjId="SW-43421_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-43424_0" Pin1InfoVect1LinkObjId="SW-43418_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4352,-376 4352,-363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_15feb80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4352,-314 4370,-314 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="7257@x" ObjectIDND1="g_16ed1f0@0" ObjectIDND2="g_150c050@0" ObjectIDZND0="7258@0" Pin0InfoVect0LinkObjId="SW-43422_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-43421_0" Pin1InfoVect1LinkObjId="g_16ed1f0_0" Pin1InfoVect2LinkObjId="g_150c050_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4352,-314 4370,-314 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_15feda0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4406,-314 4416,-314 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="7258@1" ObjectIDZND0="g_1672260@0" Pin0InfoVect0LinkObjId="g_1672260_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43422_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4406,-314 4416,-314 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_15ff830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4352,-327 4352,-314 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="7257@0" ObjectIDZND0="7258@x" ObjectIDZND1="g_16ed1f0@0" ObjectIDZND2="g_150c050@0" Pin0InfoVect0LinkObjId="SW-43422_0" Pin0InfoVect1LinkObjId="g_16ed1f0_0" Pin0InfoVect2LinkObjId="g_150c050_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43421_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4352,-327 4352,-314 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_15ffa50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4352,-288 4315,-288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="7258@x" ObjectIDND1="7257@x" ObjectIDND2="g_150c050@0" ObjectIDZND0="g_16ed1f0@0" Pin0InfoVect0LinkObjId="g_16ed1f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-43422_0" Pin1InfoVect1LinkObjId="SW-43421_0" Pin1InfoVect2LinkObjId="g_150c050_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4352,-288 4315,-288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16004e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4352,-314 4352,-288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="generator" ObjectIDND0="7258@x" ObjectIDND1="7257@x" ObjectIDZND0="g_16ed1f0@0" ObjectIDZND1="g_150c050@0" ObjectIDZND2="43314@x" Pin0InfoVect0LinkObjId="g_16ed1f0_0" Pin0InfoVect1LinkObjId="g_150c050_0" Pin0InfoVect2LinkObjId="SM-CX_FJ.CX_FJ_GN2_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-43422_0" Pin1InfoVect1LinkObjId="SW-43421_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4352,-314 4352,-288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_15cd4e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4161,-723 4161,-741 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="43447@0" ObjectIDZND0="7238@0" Pin0InfoVect0LinkObjId="SW-43200_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4161,-723 4161,-741 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1605a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4433,-862 4453,-862 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="earth" ObjectIDND0="0@x" ObjectIDZND0="g_1605ca0@0" Pin0InfoVect0LinkObjId="g_1605ca0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4433,-862 4453,-862 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_16505f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4401,-961 4401,-948 4432,-948 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_16065d0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_16ee700@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_16ee700_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_16065d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4401,-961 4401,-948 4432,-948 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1650810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4432,-948 4432,-963 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_16065d0@0" ObjectIDND1="g_16ee700@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_16065d0_0" Pin1InfoVect1LinkObjId="g_16ee700_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4432,-948 4432,-963 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15c9e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4525,-863 4544,-863 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="earth" ObjectIDND0="0@x" ObjectIDZND0="g_15ca050@0" Pin0InfoVect0LinkObjId="g_15ca050_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4525,-863 4544,-863 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15cbc10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4494,-961 4494,-948 4525,-948 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_15ca980@0" ObjectIDZND0="0@x" ObjectIDZND1="g_16ef910@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_16ef910_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15ca980_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4494,-961 4494,-948 4525,-948 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15cbe00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4525,-948 4525,-963 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_15ca980@0" ObjectIDND1="g_16ef910@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_15ca980_0" Pin1InfoVect1LinkObjId="g_16ef910_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4525,-948 4525,-963 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_164c5c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4621,-864 4641,-864 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="earth" ObjectIDND0="0@x" ObjectIDZND0="g_164c7e0@0" Pin0InfoVect0LinkObjId="g_164c7e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4621,-864 4641,-864 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_164e3a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4590,-963 4590,-950 4621,-950 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_164d110@0" ObjectIDZND0="0@x" ObjectIDZND1="g_16f0b20@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_16f0b20_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_164d110_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4590,-963 4590,-950 4621,-950 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_164e5c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4621,-950 4621,-965 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_164d110@0" ObjectIDND1="g_16f0b20@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_164d110_0" Pin1InfoVect1LinkObjId="g_16f0b20_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4621,-950 4621,-965 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_162dfd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3809,-34 3797,-34 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_15fb660@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15fb660_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3809,-34 3797,-34 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_162e5f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3810,-151 3797,-151 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_16743a0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_16743a0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3810,-151 3797,-151 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_162ec10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3810,-254 3796,-254 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_163ca80@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_163ca80_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3810,-254 3796,-254 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_162f200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4432,-818 4432,-831 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_1605030@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1605030_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4432,-818 4432,-831 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_162f820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4525,-818 4525,-832 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_15c95d0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15c95d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4525,-818 4525,-832 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_162fe10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4621,-820 4621,-833 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_169d3c0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_169d3c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4621,-820 4621,-833 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14f3ae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4653,-256 4638,-256 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_16f6110@0" Pin0InfoVect0LinkObjId="g_16f6110_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4653,-256 4638,-256 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_16233a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5029,-915 5029,-895 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="transformer2" ObjectIDND0="g_1623600@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1623600_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5029,-915 5029,-895 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1624090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5113,-914 5113,-894 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_16fbbf0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_16f97d0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_16f97d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_16fbbf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5113,-914 5113,-894 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_16242f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5113,-894 5130,-894 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_16f97d0@0" ObjectIDND1="g_16fbbf0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_16f97d0_0" Pin1InfoVect1LinkObjId="g_16fbbf0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5113,-894 5130,-894 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_154ffd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4775,-851 4775,-831 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_16f8a20@0" ObjectIDZND0="g_1550230@0" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_1550230_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_16f8a20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4775,-851 4775,-831 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1551670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4775,-830 4792,-830 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_16f8a20@0" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_1550230@0" Pin0InfoVect0LinkObjId="g_1550230_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_16f8a20_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4775,-830 4792,-830 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1587560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4837,-830 4858,-830 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1550230@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1550230_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4837,-830 4858,-830 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15877c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4894,-830 4922,-830 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4894,-830 4922,-830 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1587a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4938,-895 4922,-895 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="busSection" ObjectIDND0="g_16227f0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_16227f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4938,-895 4922,-895 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_165ecb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4983,-894 4998,-894 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_16227f0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_16227f0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4983,-894 4998,-894 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1660300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5030,-789 5030,-769 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="transformer2" ObjectIDND0="g_1660540@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1660540_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5030,-789 5030,-769 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1660fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5114,-788 5114,-768 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_16fc9a0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_16fd750@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_16fd750_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_16fc9a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5114,-788 5114,-768 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1661230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5114,-768 5131,-768 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_16fc9a0@0" ObjectIDND1="g_16fd750@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_16fc9a0_0" Pin1InfoVect1LinkObjId="g_16fd750_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5114,-768 5131,-768 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14f77d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4939,-769 4922,-769 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="busSection" ObjectIDND0="g_165f9c0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_165f9c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4939,-769 4922,-769 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14f8bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4984,-768 4999,-768 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_165f9c0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_165f9c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4984,-768 4999,-768 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15b10d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5030,-664 5030,-644 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="transformer2" ObjectIDND0="g_15b1300@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15b1300_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5030,-664 5030,-644 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15b1d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5114,-663 5114,-643 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_1701f90@0" ObjectIDZND0="0@x" ObjectIDZND1="g_16ffb70@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_16ffb70_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1701f90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5114,-663 5114,-643 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15b1ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5114,-643 5131,-643 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_1701f90@0" ObjectIDND1="g_16ffb70@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1701f90_0" Pin1InfoVect1LinkObjId="g_16ffb70_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5114,-643 5131,-643 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15b3480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4939,-644 4922,-644 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="busSection" ObjectIDND0="g_15b0740@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15b0740_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4939,-644 4922,-644 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1662500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4984,-643 4999,-643 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_15b0740@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15b0740_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4984,-643 4999,-643 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1664220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5030,-369 5030,-349 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="transformer2" ObjectIDND0="g_1664480@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1664480_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5030,-369 5030,-349 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1492a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5114,-368 5114,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_149ffa0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_149db80@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_149db80_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_149ffa0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5114,-368 5114,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1492c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5114,-348 5131,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_149ffa0@0" ObjectIDND1="g_149db80@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_149ffa0_0" Pin1InfoVect1LinkObjId="g_149db80_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5114,-348 5131,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1494080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4939,-349 4923,-349 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="busSection" ObjectIDND0="g_1663760@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1663760_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4939,-349 4923,-349 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14954a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4984,-348 4999,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_1663760@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1663760_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4984,-348 4999,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_16881b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5031,-243 5031,-223 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="transformer2" ObjectIDND0="g_16883e0@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_16883e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5031,-243 5031,-223 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1688e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5115,-242 5115,-222 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_14a3170@0" ObjectIDZND0="0@x" ObjectIDZND1="g_14a0d50@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_14a0d50_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_14a3170_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5115,-242 5115,-222 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1689070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5115,-222 5132,-222 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_14a0d50@0" ObjectIDND1="g_14a3170@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_14a0d50_0" Pin1InfoVect1LinkObjId="g_14a3170_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5115,-222 5132,-222 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_168a4c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4940,-223 4923,-223 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="busSection" ObjectIDND0="g_1687900@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1687900_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4940,-223 4923,-223 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14d5960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4985,-222 5000,-222 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_1687900@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1687900_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4985,-222 5000,-222 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14d6c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5031,-118 5031,-98 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="transformer2" ObjectIDND0="g_14d6e50@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_14d6e50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5031,-118 5031,-98 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14d78e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5115,-117 5115,-97 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_14a6340@0" ObjectIDZND0="0@x" ObjectIDZND1="g_14a3f20@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_14a3f20_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_14a6340_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5115,-117 5115,-97 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14d7b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5115,-97 5132,-97 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_14a6340@0" ObjectIDND1="g_14a3f20@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_14a6340_0" Pin1InfoVect1LinkObjId="g_14a3f20_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5115,-97 5132,-97 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1532e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4940,-98 4923,-98 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="busSection" ObjectIDND0="g_14d62f0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_14d62f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4940,-98 4923,-98 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1534260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4985,-97 5000,-97 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_14d62f0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_14d62f0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4985,-97 5000,-97 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1646a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5029,-501 5029,-481 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="transformer2" ObjectIDND0="g_1646cb0@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1646cb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5029,-501 5029,-481 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1647740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5113,-500 5113,-480 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_149cdd0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_1702d40@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_1702d40_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_149cdd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5113,-500 5113,-480 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_16479a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5113,-480 5130,-480 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_149cdd0@0" ObjectIDND1="g_1702d40@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_149cdd0_0" Pin1InfoVect1LinkObjId="g_1702d40_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5113,-480 5130,-480 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1648e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4938,-481 4923,-481 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="busSection" ObjectIDND0="g_1646060@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1646060_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4938,-481 4923,-481 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1608250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4983,-480 4998,-480 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_1646060@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1646060_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4983,-480 4998,-480 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1609360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4772,-436 4772,-416 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_16f7c70@0" ObjectIDZND0="g_1609d30@0" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_1609d30_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_16f7c70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4772,-436 4772,-416 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_160a860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4772,-415 4789,-415 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_16f7c70@0" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_1609d30@0" Pin0InfoVect0LinkObjId="g_1609d30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_16f7c70_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4772,-415 4789,-415 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_16339a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4834,-415 4854,-415 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1609d30@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1609d30_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4834,-415 4854,-415 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1633c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4890,-415 4923,-415 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4890,-415 4923,-415 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1613380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4352,-493 4352,-519 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="10773@0" ObjectIDZND0="7247@0" Pin0InfoVect0LinkObjId="SW-43256_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4352,-493 4352,-519 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16135e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4352,-572 4334,-572 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="7247@x" ObjectIDND1="g_15097b0@0" ObjectIDND2="g_150b310@0" ObjectIDZND0="7248@1" Pin0InfoVect0LinkObjId="SW-43257_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-43256_0" Pin1InfoVect1LinkObjId="g_15097b0_0" Pin1InfoVect2LinkObjId="g_150b310_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4352,-572 4334,-572 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1613840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4298,-572 4288,-572 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="7248@0" ObjectIDZND0="g_18039b0@0" Pin0InfoVect0LinkObjId="g_18039b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43257_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4298,-572 4288,-572 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1614330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4352,-555 4352,-572 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="7247@1" ObjectIDZND0="7248@x" ObjectIDZND1="g_15097b0@0" ObjectIDZND2="g_150b310@0" Pin0InfoVect0LinkObjId="SW-43257_0" Pin0InfoVect1LinkObjId="g_15097b0_0" Pin0InfoVect2LinkObjId="g_150b310_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43256_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4352,-555 4352,-572 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1614590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4352,-610 4331,-610 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="7248@x" ObjectIDND1="7247@x" ObjectIDND2="g_150b310@0" ObjectIDZND0="g_15097b0@0" Pin0InfoVect0LinkObjId="g_15097b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-43257_0" Pin1InfoVect1LinkObjId="SW-43256_0" Pin1InfoVect2LinkObjId="g_150b310_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4352,-610 4331,-610 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_166aac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4510,-493 4510,-515 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="lightningRod" ObjectIDND0="10773@0" ObjectIDZND0="g_1616580@0" Pin0InfoVect0LinkObjId="g_1616580_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4510,-493 4510,-515 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_166ad20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4510,-560 4510,-596 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_1616580@1" ObjectIDZND0="7317@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1616580_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4510,-560 4510,-596 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_166bce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3872,-449 3855,-449 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_166b180@0" Pin0InfoVect0LinkObjId="g_166b180_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3872,-449 3855,-449 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_166bf40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3767,-449 3767,-469 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="earth" ObjectIDND0="0@x" ObjectIDZND0="g_166c1a0@0" Pin0InfoVect0LinkObjId="g_166c1a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3767,-449 3767,-469 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_166cc30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3680,-449 3680,-469 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_14e7ce0@0" ObjectIDZND0="g_14e02d0@0" Pin0InfoVect0LinkObjId="g_14e02d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_14e7ce0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3680,-449 3680,-469 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15d0b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3680,-449 3663,-449 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_14e02d0@0" ObjectIDND1="g_14e7ce0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_14e02d0_0" Pin1InfoVect1LinkObjId="g_14e7ce0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3680,-449 3663,-449 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15d2dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3872,-566 3856,-566 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_15d24a0@0" Pin0InfoVect0LinkObjId="g_15d24a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3872,-566 3856,-566 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15d3000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3767,-566 3767,-586 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="earth" ObjectIDND0="0@x" ObjectIDZND0="g_15d3260@0" Pin0InfoVect0LinkObjId="g_15d3260_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3767,-566 3767,-586 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15d3cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3681,-566 3681,-586 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_14e6610@0" ObjectIDZND0="g_14df520@0" Pin0InfoVect0LinkObjId="g_14df520_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_14e6610_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3681,-566 3681,-586 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1510000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3681,-566 3664,-566 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_14df520@0" ObjectIDND1="g_14e6610@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_14df520_0" Pin1InfoVect1LinkObjId="g_14e6610_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3681,-566 3664,-566 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15122b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3872,-668 3856,-668 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_1511980@0" Pin0InfoVect0LinkObjId="g_1511980_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3872,-668 3856,-668 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15124e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3766,-669 3766,-688 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="earth" ObjectIDND0="0@x" ObjectIDZND0="g_1512740@0" Pin0InfoVect0LinkObjId="g_1512740_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3766,-669 3766,-688 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1577080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3681,-669 3681,-689 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_14e41f0@0" ObjectIDZND0="g_14de770@0" Pin0InfoVect0LinkObjId="g_14de770_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_14e41f0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3681,-669 3681,-689 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1579b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3681,-669 3664,-669 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_14de770@0" ObjectIDND1="g_14e41f0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_14de770_0" Pin1InfoVect1LinkObjId="g_14e41f0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3681,-669 3664,-669 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15eae30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3810,-449 3798,-449 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_166b180@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_166b180_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3810,-449 3798,-449 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15ec250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3811,-566 3798,-566 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_15d24a0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15d24a0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3811,-566 3798,-566 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15ed670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3811,-669 3797,-669 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_1511980@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1511980_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3811,-669 3797,-669 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1589560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3872,-767 3855,-767 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_15889d0@0" Pin0InfoVect0LinkObjId="g_15889d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3872,-767 3855,-767 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15897c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3765,-768 3765,-787 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="earth" ObjectIDND0="0@x" ObjectIDZND0="g_1589a20@0" Pin0InfoVect0LinkObjId="g_1589a20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3765,-768 3765,-787 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_158a4b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3680,-768 3680,-782 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_14e1dd0@0" ObjectIDZND0="g_14dd9c0@0" Pin0InfoVect0LinkObjId="g_14dd9c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_14e1dd0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3680,-768 3680,-782 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_158cf60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3680,-768 3663,-768 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_14dd9c0@0" ObjectIDND1="g_14e1dd0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_14dd9c0_0" Pin1InfoVect1LinkObjId="g_14e1dd0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3680,-768 3663,-768 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1514e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3810,-768 3796,-768 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_15889d0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15889d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3810,-768 3796,-768 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15170c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3872,-434 3892,-434 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3872,-434 3892,-434 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1517ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3930,-434 3947,-434 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_1517320@1" Pin0InfoVect0LinkObjId="g_1517320_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3930,-434 3947,-434 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15c0980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4000,-434 4000,-400 3983,-400 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1517320@0" ObjectIDND1="g_15c1930@0" ObjectIDND2="g_15048c0@0" ObjectIDZND0="g_1506ea0@0" Pin0InfoVect0LinkObjId="g_1506ea0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1517320_0" Pin1InfoVect1LinkObjId="g_15c1930_0" Pin1InfoVect2LinkObjId="g_15048c0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4000,-434 4000,-400 3983,-400 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15c1470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3992,-434 4000,-434 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="g_1517320@0" ObjectIDZND0="g_1506ea0@0" ObjectIDZND1="g_15c1930@0" ObjectIDZND2="g_15048c0@0" Pin0InfoVect0LinkObjId="g_1506ea0_0" Pin0InfoVect1LinkObjId="g_15c1930_0" Pin0InfoVect2LinkObjId="g_15048c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1517320_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3992,-434 4000,-434 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15c16d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3871,-271 3897,-271 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3871,-271 3897,-271 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15c24e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3933,-271 3945,-271 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_15c1930@1" Pin0InfoVect0LinkObjId="g_15c1930_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3933,-271 3945,-271 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15c2740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4001,-271 4001,-237 3990,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_15c1930@0" ObjectIDND1="g_1517320@0" ObjectIDND2="g_1506ea0@0" ObjectIDZND0="g_15048c0@0" Pin0InfoVect0LinkObjId="g_15048c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_15c1930_0" Pin1InfoVect1LinkObjId="g_1517320_0" Pin1InfoVect2LinkObjId="g_1506ea0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4001,-271 4001,-237 3990,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15c3230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3990,-271 4001,-271 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="g_15c1930@0" ObjectIDZND0="g_1517320@0" ObjectIDZND1="g_1506ea0@0" ObjectIDZND2="g_15048c0@0" Pin0InfoVect0LinkObjId="g_1517320_0" Pin0InfoVect1LinkObjId="g_1506ea0_0" Pin0InfoVect2LinkObjId="g_15048c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15c1930_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3990,-271 4001,-271 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15c3490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4001,-271 4016,-271 4016,-434 4000,-434 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="g_15c1930@0" ObjectIDND1="g_15048c0@0" ObjectIDZND0="g_1517320@0" ObjectIDZND1="g_1506ea0@0" Pin0InfoVect0LinkObjId="g_1517320_0" Pin0InfoVect1LinkObjId="g_1506ea0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_15c1930_0" Pin1InfoVect1LinkObjId="g_15048c0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4001,-271 4016,-271 4016,-434 4000,-434 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15c44b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3872,-918 3855,-918 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_15c3900@0" Pin0InfoVect0LinkObjId="g_15c3900_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3872,-918 3855,-918 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15c4710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3767,-918 3767,-938 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="earth" ObjectIDND0="0@x" ObjectIDZND0="g_15c4970@0" Pin0InfoVect0LinkObjId="g_15c4970_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3767,-918 3767,-938 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15c5400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3680,-918 3680,-932 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_14d99e0@0" ObjectIDZND0="g_14dcc10@0" Pin0InfoVect0LinkObjId="g_14dcc10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_14d99e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3680,-918 3680,-932 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15998c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3680,-918 3663,-918 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_14d99e0@0" ObjectIDND1="g_14dcc10@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_14d99e0_0" Pin1InfoVect1LinkObjId="g_14dcc10_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3680,-918 3663,-918 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_159bb70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3872,-1034 3856,-1034 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_159b240@0" Pin0InfoVect0LinkObjId="g_159b240_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3872,-1034 3856,-1034 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_159bda0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3767,-1035 3767,-1055 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="earth" ObjectIDND0="0@x" ObjectIDZND0="g_159c000@0" Pin0InfoVect0LinkObjId="g_159c000_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3767,-1035 3767,-1055 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_159ca90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3681,-1035 3681,-1049 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_15619d0@0" ObjectIDZND0="g_14dbe60@0" Pin0InfoVect0LinkObjId="g_14dbe60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_15619d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3681,-1035 3681,-1049 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_159f540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3681,-1035 3664,-1035 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_15619d0@0" ObjectIDND1="g_14dbe60@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_15619d0_0" Pin1InfoVect1LinkObjId="g_14dbe60_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3681,-1035 3664,-1035 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15dd900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3872,-1137 3856,-1137 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_15dcfd0@0" Pin0InfoVect0LinkObjId="g_15dcfd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3872,-1137 3856,-1137 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15ddb30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3766,-1138 3766,-1157 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="earth" ObjectIDND0="0@x" ObjectIDZND0="g_15ddd90@0" Pin0InfoVect0LinkObjId="g_15ddd90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3766,-1138 3766,-1157 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15de820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3681,-1138 3681,-1149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_155fa70@0" ObjectIDZND0="g_14db0b0@0" Pin0InfoVect0LinkObjId="g_14db0b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_155fa70_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3681,-1138 3681,-1149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15e12d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3681,-1138 3664,-1138 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_155fa70@0" ObjectIDND1="g_14db0b0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_155fa70_0" Pin1InfoVect1LinkObjId="g_14db0b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3681,-1138 3664,-1138 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_153ab50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3810,-918 3798,-918 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_15c3900@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15c3900_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3810,-918 3798,-918 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_153bf70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3811,-1035 3798,-1035 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_159b240@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_159b240_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3811,-1035 3798,-1035 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_153d390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3811,-1138 3797,-1138 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_15dcfd0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15dcfd0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3811,-1138 3797,-1138 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1540110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3872,-942 3897,-942 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3872,-942 3897,-942 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1540f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3932,-942 3949,-942 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_1540370@1" Pin0InfoVect0LinkObjId="g_1540370_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3932,-942 3949,-942 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1541180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4002,-941 4002,-907 3985,-907 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1540370@0" ObjectIDND1="g_15418a0@0" ObjectIDND2="g_1507c50@0" ObjectIDZND0="g_1508a00@0" Pin0InfoVect0LinkObjId="g_1508a00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1540370_0" Pin1InfoVect1LinkObjId="g_15418a0_0" Pin1InfoVect2LinkObjId="g_1507c50_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4002,-941 4002,-907 3985,-907 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15413e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3994,-941 4002,-941 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="g_1540370@0" ObjectIDZND0="g_1508a00@0" ObjectIDZND1="g_15418a0@0" ObjectIDZND2="g_1507c50@0" Pin0InfoVect0LinkObjId="g_1508a00_0" Pin0InfoVect1LinkObjId="g_15418a0_0" Pin0InfoVect2LinkObjId="g_1507c50_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1540370_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3994,-941 4002,-941 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1541640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3872,-778 3893,-778 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3872,-778 3893,-778 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1542450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3932,-778 3947,-778 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_15418a0@1" Pin0InfoVect0LinkObjId="g_15418a0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3932,-778 3947,-778 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15426b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4003,-778 4003,-744 3986,-744 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_15418a0@0" ObjectIDND1="g_1508a00@0" ObjectIDND2="g_1540370@0" ObjectIDZND0="g_1507c50@0" Pin0InfoVect0LinkObjId="g_1507c50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_15418a0_0" Pin1InfoVect1LinkObjId="g_1508a00_0" Pin1InfoVect2LinkObjId="g_1540370_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4003,-778 4003,-744 3986,-744 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1542910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3992,-778 4003,-778 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="g_15418a0@0" ObjectIDZND0="g_1508a00@0" ObjectIDZND1="g_1540370@0" ObjectIDZND2="g_1507c50@0" Pin0InfoVect0LinkObjId="g_1508a00_0" Pin0InfoVect1LinkObjId="g_1540370_0" Pin0InfoVect2LinkObjId="g_1507c50_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15418a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3992,-778 4003,-778 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1542b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4003,-778 4018,-778 4018,-941 4002,-941 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="g_15418a0@0" ObjectIDND1="g_1507c50@0" ObjectIDZND0="g_1508a00@0" ObjectIDZND1="g_1540370@0" Pin0InfoVect0LinkObjId="g_1508a00_0" Pin0InfoVect1LinkObjId="g_1540370_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_15418a0_0" Pin1InfoVect1LinkObjId="g_1507c50_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4003,-778 4018,-778 4018,-941 4002,-941 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15b7250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4627,-671 4627,-692 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_14f6590@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_14f6590_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4627,-671 4627,-692 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15b7440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4627,-728 4627,-754 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4627,-728 4627,-754 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15b7b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4431,-773 4431,-754 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="busSection" ObjectIDND0="g_1605030@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1605030_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4431,-773 4431,-754 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15b82a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4524,-773 4524,-754 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="busSection" ObjectIDND0="g_15c95d0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15c95d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4524,-773 4524,-754 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15b8ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4620,-775 4620,-754 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="busSection" ObjectIDND0="g_169d3c0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_169d3c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4620,-775 4620,-754 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15a1880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4526,-32 4526,-47 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4526,-32 4526,-47 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15a1a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4627,-52 4628,-35 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4627,-52 4628,-35 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15a1c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4627,-111 4627,-88 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1630640@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1630640_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4627,-111 4627,-88 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_15a6c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4160,-702 4034,-702 4034,-678 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="7240@1" Pin0InfoVect0LinkObjId="SW-43202_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4160,-702 4034,-702 4034,-678 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_15a6ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4034,-642 4034,-626 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="7240@0" ObjectIDZND0="g_180a1b0@0" Pin0InfoVect0LinkObjId="g_180a1b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43202_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4034,-642 4034,-626 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_15a9c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4216,-815 4227,-815 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="7241@1" ObjectIDZND0="g_15a9e60@0" Pin0InfoVect0LinkObjId="g_15a9e60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43203_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4216,-815 4227,-815 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_15ad3a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4161,-899 4181,-899 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="7237@x" ObjectIDND1="7239@x" ObjectIDZND0="7242@0" Pin0InfoVect0LinkObjId="SW-43204_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-43198_0" Pin1InfoVect1LinkObjId="SW-43201_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4161,-899 4181,-899 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1525d40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4217,-899 4228,-899 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="7242@1" ObjectIDZND0="g_1525fa0@0" Pin0InfoVect0LinkObjId="g_1525fa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43204_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4217,-899 4228,-899 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1526a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4216,-974 4227,-974 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="7243@1" ObjectIDZND0="g_1526c90@0" Pin0InfoVect0LinkObjId="g_1526c90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43205_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4216,-974 4227,-974 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_152c000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4160,-876 4160,-899 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="7237@1" ObjectIDZND0="7242@x" ObjectIDZND1="7239@x" Pin0InfoVect0LinkObjId="SW-43204_0" Pin0InfoVect1LinkObjId="SW-43201_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43198_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4160,-876 4160,-899 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_152c260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4160,-920 4160,-899 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="7239@0" ObjectIDZND0="7242@x" ObjectIDZND1="7237@x" Pin0InfoVect0LinkObjId="SW-43204_0" Pin0InfoVect1LinkObjId="SW-43198_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43201_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4160,-920 4160,-899 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_15328d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4352,-572 4352,-610 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="7248@x" ObjectIDND1="7247@x" ObjectIDZND0="g_15097b0@0" ObjectIDZND1="g_150b310@0" Pin0InfoVect0LinkObjId="g_15097b0_0" Pin0InfoVect1LinkObjId="g_150b310_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-43257_0" Pin1InfoVect1LinkObjId="SW-43256_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4352,-572 4352,-610 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1520050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4475,-96 4465,-96 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_18057c0@0" Pin0InfoVect0LinkObjId="g_18057c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4475,-96 4465,-96 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15222a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4526,-159 4508,-159 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4526,-159 4508,-159 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1522500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4472,-159 4462,-159 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_1804db0@0" Pin0InfoVect0LinkObjId="g_1804db0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4472,-159 4462,-159 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1522760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4526,-159 4526,-172 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4526,-159 4526,-172 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15229c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4526,-142 4526,-159 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4526,-142 4526,-159 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1522c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4511,-96 4526,-96 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4511,-96 4526,-96 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1522e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4526,-96 4526,-115 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4526,-96 4526,-115 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15230e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4526,-83 4526,-96 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4526,-83 4526,-96 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1564910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4476,-223 4461,-223 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_1804380@0" Pin0InfoVect0LinkObjId="g_1804380_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4476,-223 4461,-223 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1570340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4706,-96 4696,-96 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_1807770@0" Pin0InfoVect0LinkObjId="g_1807770_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4706,-96 4696,-96 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1572590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4757,-159 4739,-159 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4757,-159 4739,-159 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15727f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4703,-159 4693,-159 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_1806ce0@0" Pin0InfoVect0LinkObjId="g_1806ce0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4703,-159 4693,-159 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14b6fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4757,-159 4757,-172 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4757,-159 4757,-172 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14b7210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4757,-142 4757,-159 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4757,-142 4757,-159 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14b7470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4742,-96 4757,-96 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4742,-96 4757,-96 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14b76d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4757,-96 4757,-112 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4757,-96 4757,-112 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14b7930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4757,-83 4757,-96 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4757,-83 4757,-96 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14ba560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4707,-223 4696,-222 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_1806250@0" Pin0InfoVect0LinkObjId="g_1806250_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4707,-223 4696,-222 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14bfc60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4757,-33 4757,-47 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4757,-33 4757,-47 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14bfec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4757,-83 4757,-96 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" EndDevType1="breaker" EndDevType2="switch" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4757,-83 4757,-96 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14c0120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4743,-223 4757,-223 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="g_1550230@0" ObjectIDZND2="g_16f8a20@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_1550230_0" Pin0InfoVect2LinkObjId="g_16f8a20_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4743,-223 4757,-223 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14c0380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4757,-223 4757,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_1550230@0" ObjectIDND2="g_16f8a20@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_1550230_0" Pin1InfoVect2LinkObjId="g_16f8a20_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4757,-223 4757,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_155f5b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3628,-1138 3609,-1138 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_155e9b0@1" Pin0InfoVect0LinkObjId="g_155e9b0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3628,-1138 3609,-1138 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_155f810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3556,-1138 3543,-1138 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="hydroGenerator" ObjectIDND0="g_155e9b0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_155e9b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3556,-1138 3543,-1138 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15607c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3755,-1138 3745,-1138 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_155fa70@1" Pin0InfoVect0LinkObjId="g_155fa70_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3755,-1138 3745,-1138 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1560a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3692,-1138 3681,-1138 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_155fa70@0" ObjectIDZND0="0@x" ObjectIDZND1="g_14db0b0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_14db0b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_155fa70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3692,-1138 3681,-1138 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1562720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3756,-1035 3747,-1035 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_15619d0@1" Pin0InfoVect0LinkObjId="g_15619d0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3756,-1035 3747,-1035 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1562980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3694,-1035 3681,-1035 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_15619d0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_14dbe60@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_14dbe60_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15619d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3694,-1035 3681,-1035 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1562be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3628,-1035 3611,-1035 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_1560c80@1" Pin0InfoVect0LinkObjId="g_1560c80_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3628,-1035 3611,-1035 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1562e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3558,-1035 3544,-1035 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="hydroGenerator" ObjectIDND0="g_1560c80@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1560c80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3558,-1035 3544,-1035 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14da730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3756,-918 3747,-918 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_14d99e0@1" Pin0InfoVect0LinkObjId="g_14d99e0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3756,-918 3747,-918 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14da990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3694,-918 3680,-918 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_14d99e0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_14dcc10@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_14dcc10_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_14d99e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3694,-918 3680,-918 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14dabf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3627,-918 3611,-918 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_14d8da0@1" Pin0InfoVect0LinkObjId="g_14d8da0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3627,-918 3611,-918 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14dae50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3558,-918 3541,-918 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="hydroGenerator" ObjectIDND0="g_14d8da0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_14d8da0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3558,-918 3541,-918 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14e2b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3754,-768 3744,-768 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_14e1dd0@1" Pin0InfoVect0LinkObjId="g_14e1dd0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3754,-768 3744,-768 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14e2d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3691,-768 3680,-768 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_14e1dd0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_14dd9c0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_14dd9c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_14e1dd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3691,-768 3680,-768 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14e2fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3627,-768 3608,-768 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_14e1080@1" Pin0InfoVect0LinkObjId="g_14e1080_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3627,-768 3608,-768 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14e3240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3555,-768 3541,-768 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="hydroGenerator" ObjectIDND0="g_14e1080@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_14e1080_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3555,-768 3541,-768 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14e4f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3755,-669 3746,-669 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_14e41f0@1" Pin0InfoVect0LinkObjId="g_14e41f0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3755,-669 3746,-669 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14e51a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3693,-669 3681,-669 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_14e41f0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_14de770@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_14de770_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_14e41f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3693,-669 3681,-669 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14e5400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3628,-669 3610,-669 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_14e34a0@1" Pin0InfoVect0LinkObjId="g_14e34a0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3628,-669 3610,-669 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14e5660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3557,-669 3545,-669 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="hydroGenerator" ObjectIDND0="g_14e34a0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_14e34a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3557,-669 3545,-669 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14e7360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3756,-566 3747,-566 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_14e6610@1" Pin0InfoVect0LinkObjId="g_14e6610_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3756,-566 3747,-566 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14e75c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3694,-566 3681,-566 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_14e6610@0" ObjectIDZND0="0@x" ObjectIDZND1="g_14df520@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_14df520_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_14e6610_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3694,-566 3681,-566 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14e7820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3628,-566 3611,-566 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_14e58c0@1" Pin0InfoVect0LinkObjId="g_14e58c0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3628,-566 3611,-566 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14e7a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3558,-566 3545,-566 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="hydroGenerator" ObjectIDND0="g_14e58c0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_14e58c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3558,-566 3545,-566 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14e9780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3627,-449 3609,-449 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_14e8a30@1" Pin0InfoVect0LinkObjId="g_14e8a30_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3627,-449 3609,-449 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14e99e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3556,-449 3541,-449 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="hydroGenerator" ObjectIDND0="g_14e8a30@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_14e8a30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3556,-449 3541,-449 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14e9c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3756,-449 3745,-449 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_14e7ce0@1" Pin0InfoVect0LinkObjId="g_14e7ce0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3756,-449 3745,-449 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14e9ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3692,-449 3680,-449 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_14e7ce0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_14e02d0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_14e02d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_14e7ce0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3692,-449 3680,-449 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14fdba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3754,-254 3743,-254 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_14fce50@1" Pin0InfoVect0LinkObjId="g_14fce50_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3754,-254 3743,-254 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14fde00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3690,-254 3680,-254 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_14fce50@0" ObjectIDZND0="g_14ea100@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_14ea100_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_14fce50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3690,-254 3680,-254 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14fe060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3627,-254 3607,-254 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_14fc100@1" Pin0InfoVect0LinkObjId="g_14fc100_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3627,-254 3607,-254 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14fe2c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3554,-254 3541,-254 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="hydroGenerator" ObjectIDND0="g_14fc100@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_14fc100_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3554,-254 3541,-254 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14fffc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3627,-151 3608,-151 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_14ff270@1" Pin0InfoVect0LinkObjId="g_14ff270_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3627,-151 3608,-151 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1500220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3555,-151 3544,-151 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="hydroGenerator" ObjectIDND0="g_14ff270@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_14ff270_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3555,-151 3544,-151 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1500480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3755,-151 3744,-151 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_14fe520@1" Pin0InfoVect0LinkObjId="g_14fe520_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3755,-151 3744,-151 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15006e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3691,-151 3680,-151 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_14fe520@0" ObjectIDZND0="g_14fa5a0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_14fa5a0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_14fe520_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3691,-151 3680,-151 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15023e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3755,-34 3742,-34 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_1501690@1" Pin0InfoVect0LinkObjId="g_1501690_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3755,-34 3742,-34 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1502640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3689,-34 3679,-34 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_1501690@0" ObjectIDZND0="0@x" ObjectIDZND1="g_14fb350@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_14fb350_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1501690_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3689,-34 3679,-34 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15028a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3626,-34 3606,-34 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_1500940@1" Pin0InfoVect0LinkObjId="g_1500940_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3626,-34 3606,-34 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1502b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3553,-34 3540,-34 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="hydroGenerator" ObjectIDND0="g_1500940@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1500940_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3553,-34 3540,-34 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1505670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4159,-287 4122,-287 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="7252@x" ObjectIDND1="7251@x" ObjectIDND2="g_1503b10@0" ObjectIDZND0="g_1618db0@0" Pin0InfoVect0LinkObjId="g_1618db0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-43399_0" Pin1InfoVect1LinkObjId="SW-43398_0" Pin1InfoVect2LinkObjId="g_1503b10_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4159,-287 4122,-287 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15069e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3943,-151 3966,-151 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_1502d60@0" ObjectIDZND0="g_1506160@1" Pin0InfoVect0LinkObjId="g_1506160_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_1502d60_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3943,-151 3966,-151 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1506c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3997,-151 4018,-151 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_1506160@0" ObjectIDZND0="g_15efd70@0" Pin0InfoVect0LinkObjId="g_15efd70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1506160_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3997,-151 4018,-151 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_150bb90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4352,-673 4352,-659 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_16147f0@0" ObjectIDZND0="g_150b310@1" Pin0InfoVect0LinkObjId="g_150b310_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_16147f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4352,-673 4352,-659 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_150bdf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4352,-628 4352,-610 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_150b310@0" ObjectIDZND0="7248@x" ObjectIDZND1="7247@x" ObjectIDZND2="g_15097b0@0" Pin0InfoVect0LinkObjId="SW-43257_0" Pin0InfoVect1LinkObjId="SW-43256_0" Pin0InfoVect2LinkObjId="g_15097b0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_150b310_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4352,-628 4352,-610 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_16ef450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4432,-873 4432,-883 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_16ee700@1" Pin0InfoVect0LinkObjId="g_16ee700_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4432,-873 4432,-883 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_16ef6b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4432,-936 4432,-948 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_16ee700@0" ObjectIDZND0="g_16065d0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_16065d0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_16ee700_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4432,-936 4432,-948 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_16f0660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4525,-874 4525,-885 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_16ef910@1" Pin0InfoVect0LinkObjId="g_16ef910_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4525,-874 4525,-885 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_16f08c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4525,-938 4525,-948 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_16ef910@0" ObjectIDZND0="g_15ca980@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_15ca980_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_16ef910_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4525,-938 4525,-948 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_16f1870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4621,-875 4621,-885 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_16f0b20@1" Pin0InfoVect0LinkObjId="g_16f0b20_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4621,-875 4621,-885 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_16f1ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4621,-938 4621,-950 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_16f0b20@0" ObjectIDZND0="g_164d110@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_164d110_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_16f0b20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4621,-938 4621,-950 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_16f2a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4621,-1001 4621,-1019 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_16f1d30@1" Pin0InfoVect0LinkObjId="g_16f1d30_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4621,-1001 4621,-1019 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_16f2ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4621,-1072 4621,-1083 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="hydroGenerator" ObjectIDND0="g_16f1d30@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_16f1d30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4621,-1072 4621,-1083 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_16f3c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4525,-999 4525,-1017 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_16f2f40@1" Pin0InfoVect0LinkObjId="g_16f2f40_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4525,-999 4525,-1017 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_16f3ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4525,-1070 4525,-1081 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="hydroGenerator" ObjectIDND0="g_16f2f40@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_16f2f40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4525,-1070 4525,-1081 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_16f4ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4432,-999 4432,-1017 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_16f4150@1" Pin0InfoVect0LinkObjId="g_16f4150_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4432,-999 4432,-1017 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_16f5100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4432,-1070 4432,-1081 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="hydroGenerator" ObjectIDND0="g_16f4150@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_16f4150_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4432,-1070 4432,-1081 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_16fb270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5040,-894 5050,-894 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_16f97d0@0" Pin0InfoVect0LinkObjId="g_16f97d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5040,-894 5050,-894 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_16fb4d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5103,-894 5113,-894 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_16f97d0@1" ObjectIDZND0="0@x" ObjectIDZND1="g_16fbbf0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_16fbbf0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_16f97d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5103,-894 5113,-894 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_16fb730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5166,-894 5178,-894 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_16fa520@0" Pin0InfoVect0LinkObjId="g_16fa520_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5166,-894 5178,-894 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_16fb990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5231,-894 5245,-894 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="hydroGenerator" ObjectIDND0="g_16fa520@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_16fa520_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5231,-894 5245,-894 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_16ff1f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5167,-768 5179,-768 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_16fe4a0@0" Pin0InfoVect0LinkObjId="g_16fe4a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5167,-768 5179,-768 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_16ff450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5232,-768 5246,-768 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="hydroGenerator" ObjectIDND0="g_16fe4a0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_16fe4a0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5232,-768 5246,-768 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_16ff6b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5041,-768 5051,-768 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_16fd750@0" Pin0InfoVect0LinkObjId="g_16fd750_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5041,-768 5051,-768 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_16ff910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5104,-768 5114,-768 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_16fd750@1" ObjectIDZND0="0@x" ObjectIDZND1="g_16fc9a0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_16fc9a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_16fd750_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5104,-768 5114,-768 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1701610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5167,-643 5180,-643 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_17008c0@0" Pin0InfoVect0LinkObjId="g_17008c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5167,-643 5180,-643 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1701870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5233,-643 5247,-643 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="hydroGenerator" ObjectIDND0="g_17008c0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_17008c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5233,-643 5247,-643 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1701ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5041,-643 5052,-643 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_16ffb70@0" Pin0InfoVect0LinkObjId="g_16ffb70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5041,-643 5052,-643 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1701d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5105,-643 5114,-643 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_16ffb70@1" ObjectIDZND0="g_1701f90@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_1701f90_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_16ffb70_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5105,-643 5114,-643 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_149c450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5166,-480 5177,-480 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_149b700@0" Pin0InfoVect0LinkObjId="g_149b700_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5166,-480 5177,-480 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_149c6b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5230,-480 5249,-480 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="hydroGenerator" ObjectIDND0="g_149b700@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_149b700_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5230,-480 5249,-480 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_149c910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5040,-480 5049,-480 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_1702d40@0" Pin0InfoVect0LinkObjId="g_1702d40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5040,-480 5049,-480 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_149cb70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5102,-480 5113,-480 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_1702d40@1" ObjectIDZND0="g_149cdd0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_149cdd0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1702d40_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5102,-480 5113,-480 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_149f620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5167,-348 5178,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_149e8d0@0" Pin0InfoVect0LinkObjId="g_149e8d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5167,-348 5178,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_149f880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5231,-348 5250,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="hydroGenerator" ObjectIDND0="g_149e8d0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_149e8d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5231,-348 5250,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_149fae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5041,-348 5050,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_149db80@0" Pin0InfoVect0LinkObjId="g_149db80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5041,-348 5050,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_149fd40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5103,-348 5114,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_149db80@1" ObjectIDZND0="g_149ffa0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_149ffa0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_149db80_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5103,-348 5114,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14a27f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5168,-222 5179,-222 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_14a1aa0@0" Pin0InfoVect0LinkObjId="g_14a1aa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5168,-222 5179,-222 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14a2a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5232,-222 5251,-222 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="hydroGenerator" ObjectIDND0="g_14a1aa0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_14a1aa0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5232,-222 5251,-222 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14a2cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5042,-222 5051,-222 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_14a0d50@0" Pin0InfoVect0LinkObjId="g_14a0d50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5042,-222 5051,-222 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14a2f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5104,-222 5115,-222 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_14a0d50@1" ObjectIDZND0="0@x" ObjectIDZND1="g_14a3170@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_14a3170_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_14a0d50_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5104,-222 5115,-222 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14a59c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5168,-97 5181,-97 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_14a4c70@0" Pin0InfoVect0LinkObjId="g_14a4c70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5168,-97 5181,-97 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14a5c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5234,-97 5251,-97 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="hydroGenerator" ObjectIDND0="g_14a4c70@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_14a4c70_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5234,-97 5251,-97 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14a5e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5042,-97 5053,-97 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_14a3f20@0" Pin0InfoVect0LinkObjId="g_14a3f20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5042,-97 5053,-97 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14a60e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5106,-97 5115,-97 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_14a3f20@1" ObjectIDZND0="g_14a6340@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_14a6340_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_14a3f20_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5106,-97 5115,-97 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_17ed600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4160,-592 4160,-572 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="7244@0" ObjectIDZND0="7245@x" ObjectIDZND1="7246@x" Pin0InfoVect0LinkObjId="SW-43209_0" Pin0InfoVect1LinkObjId="SW-43210_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43207_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4160,-592 4160,-572 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_17ed820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4160,-572 4160,-556 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="7244@x" ObjectIDND1="7246@x" ObjectIDZND0="7245@1" Pin0InfoVect0LinkObjId="SW-43209_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-43207_0" Pin1InfoVect1LinkObjId="SW-43210_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4160,-572 4160,-556 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_17eda80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4160,-572 4170,-572 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="7245@x" ObjectIDND1="7244@x" ObjectIDZND0="7246@0" Pin0InfoVect0LinkObjId="SW-43210_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-43209_0" Pin1InfoVect1LinkObjId="SW-43207_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4160,-572 4170,-572 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_17edce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4206,-572 4223,-572 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="7246@1" ObjectIDZND0="g_12f07b0@0" Pin0InfoVect0LinkObjId="g_12f07b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43210_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4206,-572 4223,-572 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_17fa1a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4626,-626 4626,-592 4589,-592 4589,-607 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_14f6590@0" ObjectIDZND0="g_150a560@0" Pin0InfoVect0LinkObjId="g_150a560_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_14f6590_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4626,-626 4626,-592 4589,-592 4589,-607 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_17fa3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4639,-257 4626,-257 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_1630640@0" Pin0InfoVect0LinkObjId="g_1630640_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4639,-257 4626,-257 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_17fae90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4626,-625 4626,-257 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_1630640@0" Pin0InfoVect0LinkObjId="g_1630640_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4626,-625 4626,-257 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_17fb0f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4626,-257 4626,-156 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_1630640@0" Pin0InfoVect0LinkObjId="g_1630640_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4626,-257 4626,-156 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_17fb8d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4756,-415 4757,-257 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_1550230@0" ObjectIDND1="g_16f8a20@0" ObjectIDND2="g_16f7c70@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="g_16f6ec0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_16f6ec0_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1550230_0" Pin1InfoVect1LinkObjId="g_16f8a20_0" Pin1InfoVect2LinkObjId="g_16f7c70_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4756,-415 4757,-257 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_17fc370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4773,-830 4756,-830 4756,-415 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_1550230@0" ObjectIDND1="g_16f8a20@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="g_16f6ec0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_16f6ec0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1550230_0" Pin1InfoVect1LinkObjId="g_16f8a20_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4773,-830 4756,-830 4756,-415 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_17fc5d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4756,-415 4772,-415 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="g_16f6ec0@0" ObjectIDZND0="g_16f7c70@0" ObjectIDZND1="g_1609d30@0" Pin0InfoVect0LinkObjId="g_16f7c70_0" Pin0InfoVect1LinkObjId="g_1609d30_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_16f6ec0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4756,-415 4772,-415 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_17fd070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4757,-223 4757,-257 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="g_1550230@0" ObjectIDZND1="g_16f8a20@0" ObjectIDZND2="g_16f7c70@0" Pin0InfoVect0LinkObjId="g_1550230_0" Pin0InfoVect1LinkObjId="g_16f8a20_0" Pin0InfoVect2LinkObjId="g_16f7c70_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4757,-223 4757,-257 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_17fd2d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4757,-257 4759,-259 4774,-258 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1550230@0" ObjectIDND1="g_16f8a20@0" ObjectIDND2="g_16f7c70@0" ObjectIDZND0="g_16f6ec0@0" Pin0InfoVect0LinkObjId="g_16f6ec0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1550230_0" Pin1InfoVect1LinkObjId="g_16f8a20_0" Pin1InfoVect2LinkObjId="g_16f7c70_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4757,-257 4759,-259 4774,-258 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_180ed00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4159,-265 4159,-93 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="generator" ObjectIDND0="g_1503b10@0" ObjectIDND1="7252@x" ObjectIDND2="7251@x" ObjectIDZND0="43313@0" Pin0InfoVect0LinkObjId="SM-CX_FJ.CX_FJ_GN1_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1503b10_0" Pin1InfoVect1LinkObjId="SW-43399_0" Pin1InfoVect2LinkObjId="SW-43398_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4159,-265 4159,-93 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_180f6c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4135,-265 4159,-265 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_1503b10@0" ObjectIDZND0="7252@x" ObjectIDZND1="7251@x" ObjectIDZND2="g_1618db0@0" Pin0InfoVect0LinkObjId="SW-43399_0" Pin0InfoVect1LinkObjId="SW-43398_0" Pin0InfoVect2LinkObjId="g_1618db0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1503b10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4135,-265 4159,-265 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_180f910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4159,-265 4159,-287 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="generator" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_1503b10@0" ObjectIDND1="43313@x" ObjectIDZND0="7252@x" ObjectIDZND1="7251@x" ObjectIDZND2="g_1618db0@0" Pin0InfoVect0LinkObjId="SW-43399_0" Pin0InfoVect1LinkObjId="SW-43398_0" Pin0InfoVect2LinkObjId="g_1618db0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1503b10_0" Pin1InfoVect1LinkObjId="SM-CX_FJ.CX_FJ_GN1_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4159,-265 4159,-287 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_180fb40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4077,-48 4159,-48 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4077,-48 4159,-48 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1810610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4077,-66 4077,-48 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4077,-66 4077,-48 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1810870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4077,-48 4059,-48 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4077,-48 4059,-48 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1812340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4352,-265 4352,-255 4478,-255 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="generator" ObjectIDND0="7258@x" ObjectIDND1="7257@x" ObjectIDND2="g_16ed1f0@0" ObjectIDZND0="43314@0" Pin0InfoVect0LinkObjId="SM-CX_FJ.CX_FJ_GN2_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-43422_0" Pin1InfoVect1LinkObjId="SW-43421_0" Pin1InfoVect2LinkObjId="g_16ed1f0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4352,-265 4352,-255 4478,-255 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1812dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4352,-288 4352,-265 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" EndDevType1="generator" ObjectIDND0="7258@x" ObjectIDND1="7257@x" ObjectIDND2="g_16ed1f0@0" ObjectIDZND0="g_150c050@0" ObjectIDZND1="43314@x" Pin0InfoVect0LinkObjId="g_150c050_0" Pin0InfoVect1LinkObjId="SM-CX_FJ.CX_FJ_GN2_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-43422_0" Pin1InfoVect1LinkObjId="SW-43421_0" Pin1InfoVect2LinkObjId="g_16ed1f0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4352,-288 4352,-265 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1813030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4352,-265 4338,-265 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="7258@x" ObjectIDND1="7257@x" ObjectIDND2="g_16ed1f0@0" ObjectIDZND0="g_150c050@0" Pin0InfoVect0LinkObjId="g_150c050_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-43422_0" Pin1InfoVect1LinkObjId="SW-43421_0" Pin1InfoVect2LinkObjId="g_16ed1f0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4352,-265 4338,-265 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1813b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4526,-208 4526,-223 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="g_16f5360@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_16f5360_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4526,-208 4526,-223 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1813d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4526,-223 4512,-223 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_16f5360@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_16f5360_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4526,-223 4512,-223 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1813fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4526,-223 4526,-256 4543,-256 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="g_16f5360@0" Pin0InfoVect0LinkObjId="g_16f5360_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4526,-223 4526,-256 4543,-256 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1815d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4160,-974 4180,-974 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" ObjectIDND0="7239@x" ObjectIDND1="g_152a1d0@0" ObjectIDND2="14383@1" ObjectIDZND0="7243@0" Pin0InfoVect0LinkObjId="SW-43205_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-43201_0" Pin1InfoVect1LinkObjId="g_152a1d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4160,-974 4180,-974 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1816800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4160,-974 4160,-956 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" ObjectIDND0="7243@x" ObjectIDND1="g_152a1d0@0" ObjectIDND2="14383@1" ObjectIDZND0="7239@1" Pin0InfoVect0LinkObjId="SW-43201_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-43205_0" Pin1InfoVect1LinkObjId="g_152a1d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4160,-974 4160,-956 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1816a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4160,-1039 4203,-1039 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="powerLine" EndDevType0="lightningRod" ObjectIDND0="7243@x" ObjectIDND1="7239@x" ObjectIDND2="14383@1" ObjectIDZND0="g_152a1d0@0" Pin0InfoVect0LinkObjId="g_152a1d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-43205_0" Pin1InfoVect1LinkObjId="SW-43201_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4160,-1039 4203,-1039 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1817550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4160,-1039 4160,-974 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="powerLine" BeginDevType2="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_152a1d0@0" ObjectIDND1="14383@1" ObjectIDND2="g_180b1d0@0" ObjectIDZND0="7243@x" ObjectIDZND1="7239@x" Pin0InfoVect0LinkObjId="SW-43205_0" Pin0InfoVect1LinkObjId="SW-43201_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_152a1d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="g_180b1d0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4160,-1039 4160,-974 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_18177b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4160,-1087 4203,-1087 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="14383@1" ObjectIDND1="g_152a1d0@0" ObjectIDND2="7243@x" ObjectIDZND0="g_180b1d0@0" Pin0InfoVect0LinkObjId="g_180b1d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="g_152a1d0_0" Pin1InfoVect2LinkObjId="SW-43205_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4160,-1087 4203,-1087 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_18182a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4160,-1129 4160,-1087 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="14383@1" ObjectIDZND0="g_180b1d0@0" ObjectIDZND1="g_152a1d0@0" ObjectIDZND2="7243@x" Pin0InfoVect0LinkObjId="g_180b1d0_0" Pin0InfoVect1LinkObjId="g_152a1d0_0" Pin0InfoVect2LinkObjId="SW-43205_0" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4160,-1129 4160,-1087 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1818500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4160,-1087 4160,-1039 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="14383@1" ObjectIDND1="g_180b1d0@0" ObjectIDZND0="g_152a1d0@0" ObjectIDZND1="7243@x" ObjectIDZND2="7239@x" Pin0InfoVect0LinkObjId="g_152a1d0_0" Pin0InfoVect1LinkObjId="SW-43205_0" Pin0InfoVect2LinkObjId="SW-43201_0" Pin0Num="2" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="g_180b1d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4160,-1087 4160,-1039 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1818760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4352,-493 4352,-476 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="10773@0" ObjectIDZND0="7256@1" Pin0InfoVect0LinkObjId="SW-43420_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4352,-493 4352,-476 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1819e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4161,-796 4161,-777 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="47737@0" ObjectIDZND0="7238@1" Pin0InfoVect0LinkObjId="SW-43200_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bf2f90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4161,-796 4161,-777 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1bf89d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4161,-796 4160,-815 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="47737@0" ObjectIDZND0="7241@x" ObjectIDZND1="7237@x" Pin0InfoVect0LinkObjId="SW-43203_0" Pin0InfoVect1LinkObjId="SW-43198_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bf2f90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4161,-796 4160,-815 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1bf2f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4180,-815 4160,-815 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" EndDevType1="breaker" ObjectIDND0="7241@0" ObjectIDZND0="47737@0" ObjectIDZND1="7237@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="SW-43198_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43203_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4180,-815 4160,-815 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_186b640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4160,-815 4160,-847 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="47737@0" ObjectIDND1="7241@x" ObjectIDZND0="7237@0" Pin0InfoVect0LinkObjId="SW-43198_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1bf2f90_0" Pin1InfoVect1LinkObjId="SW-43203_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4160,-815 4160,-847 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_FJ"/>
</svg>