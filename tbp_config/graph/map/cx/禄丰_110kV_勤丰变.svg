<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-18" aopId="270" id="thSvg" viewBox="3117 -1252 2099 1253">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="36" y1="15" y2="4"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="36" y1="15" y2="4"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="capacitor:shape14">
    <polyline arcFlag="1" fill="none" points="27,100 25,100 23,99 22,99 20,98 19,97 17,96 16,94 15,92 15,91 14,89 14,87 14,85 15,83 15,82 16,80 17,79 19,77 20,76 22,75 23,75 25,74 27,74 29,74 31,75 32,75 34,76 35,77 37,79 38,80 39,82 39,83 40,85 40,87 "/>
    <rect height="23" stroke-width="0.945274" width="11" x="41" y="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.945274" x1="47" x2="45" y1="31" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.945274" x1="49" x2="47" y1="34" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.07143" x1="47" x2="47" y1="54" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.428414" x1="47" x2="47" y1="24" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.428414" x1="54" x2="40" y1="16" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.428414" x1="50" x2="44" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.309343" x1="49" x2="45" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="40" x2="28" y1="87" y2="87"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="27" x2="27" y1="99" y2="107"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="12" y1="15" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="11" y1="54" y2="47"/>
    <polyline arcFlag="1" fill="none" points="11,15 10,15 9,15 9,15 8,15 8,16 7,16 7,17 6,17 6,18 6,18 6,19 5,20 5,20 5,21 6,22 6,22 6,23 6,23 7,24 7,24 8,25 8,25 9,25 9,26 10,26 11,26 "/>
    <polyline arcFlag="1" fill="none" points="11,25 10,25 9,25 9,25 8,26 8,26 7,26 7,27 6,27 6,28 6,29 6,29 5,30 5,31 5,31 6,32 6,33 6,33 6,34 7,34 7,35 8,35 8,35 9,36 9,36 10,36 11,36 "/>
    <polyline arcFlag="1" fill="none" points="11,36 10,36 9,36 9,37 8,37 8,37 7,38 7,38 6,39 6,39 6,40 6,40 5,41 5,42 5,42 6,43 6,44 6,44 6,45 7,45 7,46 8,46 8,47 9,47 9,47 10,47 11,47 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.42985" x1="28" x2="28" y1="88" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.368819" x1="45" x2="12" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.395152" x1="28" x2="13" y1="8" y2="8"/>
    <rect height="23" stroke-width="0.398039" width="12" x="22" y="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.338133" x1="46" x2="12" y1="55" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="46" x2="46" y1="2" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="11" x2="11" y1="2" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72286" x1="28" x2="28" y1="14" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="38" x2="18" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="38" x2="18" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.370253" x1="2" x2="2" y1="45" y2="16"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="13" x2="4" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="5" x2="7" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="3" x2="9" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="0" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="9" y2="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="7" y2="5"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="28" stroke-width="1" width="14" x="0" y="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="54" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="7" x2="7" y1="51" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="52" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="4" y2="39"/>
   </symbol>
   <symbol id="lightningRod:shape45">
    <circle cx="18" cy="15" r="6" stroke-width="0.431185"/>
    <circle cx="18" cy="7" r="6" stroke-width="0.431185"/>
    <circle cx="11" cy="11" r="6" stroke-width="0.431185"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape28">
    <ellipse cx="11" cy="12" rx="10.5" ry="11.5" stroke-width="0.64567"/>
    <ellipse cx="11" cy="25" rx="10.5" ry="11.5" stroke-width="0.64567"/>
   </symbol>
   <symbol id="lightningRod:shape29">
    <ellipse cx="11" cy="15" rx="10.5" ry="11.5" stroke-width="0.64567"/>
    <ellipse cx="11" cy="28" rx="10.5" ry="11.5" stroke-width="0.64567"/>
   </symbol>
   <symbol id="lightningRod:shape54">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="6" x2="6" y1="58" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="6" x2="6" y1="8" y2="37"/>
   </symbol>
   <symbol id="lightningRod:shape50">
    <polyline DF8003:Layer="PUBLIC" points="5,39 0,50 11,50 5,39 5,40 5,39 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.222222" x1="5" x2="5" y1="29" y2="33"/>
    <polyline DF8003:Layer="PUBLIC" points="5,25 0,14 11,14 5,25 5,24 5,25 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="59" y2="51"/>
   </symbol>
   <symbol id="lightningRod:shape106">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="26" y1="40" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="54" x2="43" y1="40" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="42" x2="42" y1="48" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="35" x2="35" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.676705" x1="34" x2="16" y1="40" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="16" x2="16" y1="48" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="9" x2="9" y1="48" y2="31"/>
    <circle cx="30" cy="8" r="8.5" stroke-width="1"/>
    <circle cx="26" cy="15" r="8.5" stroke-width="1"/>
    <circle cx="20" cy="8" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="0" x2="9" y1="40" y2="40"/>
   </symbol>
   <symbol id="lightningRod:shape4">
    <rect height="31" stroke-width="2" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="9" x2="9" y1="35" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape132">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="7" x2="36" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="9" y2="9"/>
    <rect height="16" stroke-width="2" width="31" x="6" y="1"/>
   </symbol>
   <symbol id="lightningRod:shape41">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.282524" x1="6" x2="6" y1="35" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.337605" x1="7" x2="7" y1="5" y2="16"/>
    <polyline arcFlag="1" fill="none" points="7,28 7,28 7,28 7,28 8,28 8,28 8,27 8,27 9,27 9,27 9,26 9,26 9,26 9,25 9,25 9,24 9,24 9,24 9,23 8,23 8,23 8,23 8,22 7,22 7,22 7,22 "/>
    <polyline arcFlag="1" fill="none" points="6,34 7,34 7,34 7,34 7,34 8,34 8,34 8,33 8,33 8,33 8,32 9,32 9,32 9,31 9,31 9,31 8,30 8,30 8,30 8,29 8,29 8,29 7,29 7,28 7,28 7,28 "/>
    <polyline arcFlag="1" fill="none" points="7,22 7,22 7,22 7,22 8,22 8,21 8,21 8,21 9,21 9,20 9,20 9,20 9,19 9,19 9,19 9,18 9,18 9,17 9,17 8,17 8,17 8,16 8,16 7,16 7,16 7,16 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.266312" x1="5" x2="8" y1="50" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.282524" x1="4" x2="9" y1="48" y2="48"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.282524" x1="0" x2="13" y1="46" y2="46"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline fill="none" points="1,13 9,1 17,13 "/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="7" x2="15" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="15" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="7" x2="15" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="15" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="transformer:shape16_0">
    <ellipse cx="70" cy="46" rx="26.5" ry="26" stroke-width="0.540424"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="0" x2="71" y1="29" y2="100"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="80" x2="73" y1="47" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="87" x2="80" y1="54" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="80" x2="80" y1="38" y2="47"/>
   </symbol>
   <symbol id="transformer:shape16_1">
    <ellipse cx="41" cy="61" rx="26" ry="26.5" stroke-width="0.540424"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="41" x2="34" y1="71" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="49" x2="41" y1="79" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="41" x2="41" y1="62" y2="71"/>
   </symbol>
   <symbol id="transformer:shape16-2">
    <circle cx="41" cy="30" r="26.5" stroke-width="0.55102"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="40" x2="31" y1="32" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="40" x2="49" y1="32" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="31" x2="49" y1="16" y2="16"/>
   </symbol>
   <symbol id="transformer2:shape3_0">
    <ellipse cx="13" cy="34" rx="13" ry="12.5" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="40" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="36" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="32" y2="36"/>
   </symbol>
   <symbol id="transformer2:shape3_1">
    <circle cx="13" cy="18" r="13" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="20" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="16" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="12" y2="16"/>
   </symbol>
   <symbol id="transformer2:shape15_0">
    <ellipse cx="25" cy="29" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="17" x2="25" y1="32" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="33" y1="24" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="25" y1="16" y2="24"/>
   </symbol>
   <symbol id="transformer2:shape15_1">
    <circle cx="25" cy="61" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="16" x2="24" y1="75" y2="67"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="32" y1="67" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="24" y1="59" y2="67"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">开关检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注1</text>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注2</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">禁止操作</text>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="78" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.246377 -0.000000 0.000000 -1.035714 2.739130 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="32" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(0.512795 -0.000000 0.000000 -1.035714 2.846957 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape28">
    
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(0,0,0)" height="1263" width="2109" x="3112" y="-1257"/>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3979.000000 1035.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3968.000000 1020.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3993.000000 1005.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4295.000000 1035.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4284.000000 1020.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4309.000000 1005.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5124.000000 1172.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5113.000000 1157.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5138.000000 1142.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5124.000000 1018.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5113.000000 1003.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5138.000000 988.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5124.000000 924.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5113.000000 909.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5138.000000 894.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5124.000000 650.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5113.000000 635.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5138.000000 620.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5124.000000 565.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5113.000000 550.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5138.000000 535.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5124.000000 334.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5113.000000 319.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5138.000000 304.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4321.000000 581.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4310.000000 566.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4335.000000 551.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3922.000000 602.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3911.000000 587.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3936.000000 572.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -81.000000 300.500000)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3905.000000 371.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3894.000000 356.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3919.000000 341.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -54.000000 291.500000)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4007.000000 362.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3996.000000 347.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4021.000000 332.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -98.000000 294.500000)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4379.000000 365.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4368.000000 350.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4393.000000 335.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -86.000000 298.500000)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4499.000000 368.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4488.000000 353.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4513.000000 338.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4669.000000 738.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4658.000000 723.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4683.000000 708.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4668.000000 852.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4657.000000 837.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4682.000000 822.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3540.000000 62.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3565.000000 47.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3681.000000 62.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3706.000000 47.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4525.000000 62.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4550.000000 47.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4642.000000 62.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4667.000000 47.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3684.000000 815.000000) translate(0,12)">档位（档）：</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3684.000000 800.000000) translate(0,12)">油温（℃）：</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3654.000000 785.000000) translate(0,12)">绕组温度（℃）：</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4240.000000 798.000000) translate(0,12)">档位（档）：</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4240.000000 783.000000) translate(0,12)">油温（℃）：</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4210.000000 768.000000) translate(0,12)">绕组温度（℃）：</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4238.000000 572.000000) translate(0,12)">Ia:</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4117.000000 939.000000) translate(0,12)">Ia:</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4955.000000 744.000000) translate(0,12)">Ia:</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3456.000000 536.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3456.000000 522.000000) translate(0,12)">Uc（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3455.000000 507.000000) translate(0,12)">3U0（V）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3456.000000 551.000000) translate(0,12)">Ua（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3448.000000 492.000000) translate(0,12)">Uab（kV）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4759.000000 236.000000) translate(0,12)">Uc（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4759.000000 221.000000) translate(0,12)">3U0（V）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4759.000000 206.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4759.000000 265.000000) translate(0,12)">Ua（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4759.000000 250.000000) translate(0,12)">Ub（kV）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4698.000000 531.000000) translate(0,12)">Uc（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4697.000000 516.000000) translate(0,12)">3U0（V）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4698.000000 560.000000) translate(0,12)">Ua（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4690.000000 501.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4698.000000 545.000000) translate(0,12)">Ub（kV）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4733.000000 1235.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4733.000000 1221.000000) translate(0,12)">Uc（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4732.000000 1206.000000) translate(0,12)">3U0（V）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4725.000000 1191.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4733.000000 1250.000000) translate(0,12)">Ua（kV）：</text>
   <metadata/></g>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(0,0,0)" stroke-width="1" width="360" x="3118" y="-1199"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(0,0,0)" stroke-width="1" width="360" x="3118" y="-1079"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(0,0,0)" stroke-width="1" width="360" x="3118" y="-599"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-36447">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3855.000000 -493.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5801" ObjectName="SW-CX_QF.CX_QF_4011SW"/>
     <cge:Meas_Ref ObjectId="36447"/>
    <cge:TPSR_Ref TObjectID="5801"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36448">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3855.000000 -591.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5802" ObjectName="SW-CX_QF.CX_QF_4016SW"/>
     <cge:Meas_Ref ObjectId="36448"/>
    <cge:TPSR_Ref TObjectID="5802"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36985">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4403.000000 -818.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5842" ObjectName="SW-CX_QF.CX_QF_1026SW"/>
     <cge:Meas_Ref ObjectId="36985"/>
    <cge:TPSR_Ref TObjectID="5842"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36986">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4445.000000 -782.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5843" ObjectName="SW-CX_QF.CX_QF_10267SW"/>
     <cge:Meas_Ref ObjectId="36986"/>
    <cge:TPSR_Ref TObjectID="5843"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37004">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4401.585714 -897.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5854" ObjectName="SW-CX_QF.CX_QF_1971SW"/>
     <cge:Meas_Ref ObjectId="37004"/>
    <cge:TPSR_Ref TObjectID="5854"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37005">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4443.585714 -952.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5855" ObjectName="SW-CX_QF.CX_QF_19717SW"/>
     <cge:Meas_Ref ObjectId="37005"/>
    <cge:TPSR_Ref TObjectID="5855"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37007">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4444.585714 -1023.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5857" ObjectName="SW-CX_QF.CX_QF_19760SW"/>
     <cge:Meas_Ref ObjectId="37007"/>
    <cge:TPSR_Ref TObjectID="5857"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37008">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4443.585714 -1096.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5858" ObjectName="SW-CX_QF.CX_QF_19767SW"/>
     <cge:Meas_Ref ObjectId="37008"/>
    <cge:TPSR_Ref TObjectID="5858"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36440">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3857.000000 -818.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5794" ObjectName="SW-CX_QF.CX_QF_1016SW"/>
     <cge:Meas_Ref ObjectId="36440"/>
    <cge:TPSR_Ref TObjectID="5794"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36441">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3899.000000 -782.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5795" ObjectName="SW-CX_QF.CX_QF_10167SW"/>
     <cge:Meas_Ref ObjectId="36441"/>
    <cge:TPSR_Ref TObjectID="5795"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37119">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3857.000000 -897.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5877" ObjectName="SW-CX_QF.CX_QF_1961SW"/>
     <cge:Meas_Ref ObjectId="37119"/>
    <cge:TPSR_Ref TObjectID="5877"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37120">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3899.000000 -952.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5878" ObjectName="SW-CX_QF.CX_QF_19617SW"/>
     <cge:Meas_Ref ObjectId="37120"/>
    <cge:TPSR_Ref TObjectID="5878"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37122">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3900.000000 -1023.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5880" ObjectName="SW-CX_QF.CX_QF_19660SW"/>
     <cge:Meas_Ref ObjectId="37122"/>
    <cge:TPSR_Ref TObjectID="5880"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37123">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3901.000000 -1096.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5881" ObjectName="SW-CX_QF.CX_QF_19667SW"/>
     <cge:Meas_Ref ObjectId="37123"/>
    <cge:TPSR_Ref TObjectID="5881"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36442">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3739.000000 -689.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5796" ObjectName="SW-CX_QF.CX_QF_1701SW"/>
     <cge:Meas_Ref ObjectId="36442"/>
    <cge:TPSR_Ref TObjectID="5796"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36997">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4401.000000 -559.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5851" ObjectName="SW-CX_QF.CX_QF_4026SW"/>
     <cge:Meas_Ref ObjectId="36997"/>
    <cge:TPSR_Ref TObjectID="5851"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36996">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4401.000000 -461.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5850" ObjectName="SW-CX_QF.CX_QF_4021SW"/>
     <cge:Meas_Ref ObjectId="36996"/>
    <cge:TPSR_Ref TObjectID="5850"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36998">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4051.000000 -648.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5852" ObjectName="SW-CX_QF.CX_QF_3701SW"/>
     <cge:Meas_Ref ObjectId="36998"/>
    <cge:TPSR_Ref TObjectID="5852"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36993">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4206.000000 -648.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5848" ObjectName="SW-CX_QF.CX_QF_3702SW"/>
     <cge:Meas_Ref ObjectId="36993"/>
    <cge:TPSR_Ref TObjectID="5848"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36400">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4198.000000 -462.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5781" ObjectName="SW-CX_QF.CX_QF_4121SW"/>
     <cge:Meas_Ref ObjectId="36400"/>
    <cge:TPSR_Ref TObjectID="5781"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36401">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4303.000000 -465.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5782" ObjectName="SW-CX_QF.CX_QF_4122SW"/>
     <cge:Meas_Ref ObjectId="36401"/>
    <cge:TPSR_Ref TObjectID="5782"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36817">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3740.000000 -387.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5839" ObjectName="SW-CX_QF.CX_QF_4511SW"/>
     <cge:Meas_Ref ObjectId="36817"/>
    <cge:TPSR_Ref TObjectID="5839"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36818">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3740.000000 -209.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5840" ObjectName="SW-CX_QF.CX_QF_4516SW"/>
     <cge:Meas_Ref ObjectId="36818"/>
    <cge:TPSR_Ref TObjectID="5840"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57362">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3782.000000 -368.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10682" ObjectName="SW-CX_QF.CX_QF_45117SW"/>
     <cge:Meas_Ref ObjectId="57362"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57363">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3782.000000 -191.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10683" ObjectName="SW-CX_QF.CX_QF_45167SW"/>
     <cge:Meas_Ref ObjectId="57363"/>
    <cge:TPSR_Ref TObjectID="10683"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37030">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4062.300000 -871.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5860" ObjectName="SW-CX_QF.CX_QF_1121SW"/>
     <cge:Meas_Ref ObjectId="37030"/>
    <cge:TPSR_Ref TObjectID="5860"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37032">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4203.300000 -871.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5862" ObjectName="SW-CX_QF.CX_QF_1122SW"/>
     <cge:Meas_Ref ObjectId="37032"/>
    <cge:TPSR_Ref TObjectID="5862"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37031">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4093.300000 -817.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5861" ObjectName="SW-CX_QF.CX_QF_11217SW"/>
     <cge:Meas_Ref ObjectId="37031"/>
    <cge:TPSR_Ref TObjectID="5861"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37033">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4159.300000 -817.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5863" ObjectName="SW-CX_QF.CX_QF_11227SW"/>
     <cge:Meas_Ref ObjectId="37033"/>
    <cge:TPSR_Ref TObjectID="5863"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36430">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3699.000000 -464.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5790" ObjectName="SW-CX_QF.CX_QF_4901SW"/>
     <cge:Meas_Ref ObjectId="36430"/>
    <cge:TPSR_Ref TObjectID="5790"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3669.000000 -512.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37304">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4163.000000 -361.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5883" ObjectName="SW-CX_QF.CX_QF_4000SW"/>
     <cge:Meas_Ref ObjectId="37304"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36435">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4100.000000 -360.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5792" ObjectName="SW-CX_QF.CX_QF_4903SW"/>
     <cge:Meas_Ref ObjectId="36435"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4069.000000 -342.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36433">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4641.000000 -464.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5791" ObjectName="SW-CX_QF.CX_QF_4902SW"/>
     <cge:Meas_Ref ObjectId="36433"/>
    <cge:TPSR_Ref TObjectID="5791"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4610.000000 -512.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37041">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4650.000000 -392.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5865" ObjectName="SW-CX_QF.CX_QF_4761SW"/>
     <cge:Meas_Ref ObjectId="37041"/>
    <cge:TPSR_Ref TObjectID="5865"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37042">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4692.000000 -375.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5866" ObjectName="SW-CX_QF.CX_QF_47617SW"/>
     <cge:Meas_Ref ObjectId="37042"/>
    <cge:TPSR_Ref TObjectID="5866"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37043">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4650.000000 -224.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5867" ObjectName="SW-CX_QF.CX_QF_4766SW"/>
     <cge:Meas_Ref ObjectId="37043"/>
    <cge:TPSR_Ref TObjectID="5867"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37067">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4544.000000 -392.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5870" ObjectName="SW-CX_QF.CX_QF_4731SW"/>
     <cge:Meas_Ref ObjectId="37067"/>
    <cge:TPSR_Ref TObjectID="5870"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37068">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4586.000000 -375.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5871" ObjectName="SW-CX_QF.CX_QF_47317SW"/>
     <cge:Meas_Ref ObjectId="37068"/>
    <cge:TPSR_Ref TObjectID="5871"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37069">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4544.000000 -224.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5872" ObjectName="SW-CX_QF.CX_QF_4736SW"/>
     <cge:Meas_Ref ObjectId="37069"/>
    <cge:TPSR_Ref TObjectID="5872"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36765">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4438.000000 -392.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5832" ObjectName="SW-CX_QF.CX_QF_4721SW"/>
     <cge:Meas_Ref ObjectId="36765"/>
    <cge:TPSR_Ref TObjectID="5832"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36767">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4480.000000 -375.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5834" ObjectName="SW-CX_QF.CX_QF_47217SW"/>
     <cge:Meas_Ref ObjectId="36767"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36766">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4438.000000 -224.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5833" ObjectName="SW-CX_QF.CX_QF_4726SW"/>
     <cge:Meas_Ref ObjectId="36766"/>
    <cge:TPSR_Ref TObjectID="5833"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36737">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4332.000000 -392.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5828" ObjectName="SW-CX_QF.CX_QF_4711SW"/>
     <cge:Meas_Ref ObjectId="36737"/>
    <cge:TPSR_Ref TObjectID="5828"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36739">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4374.000000 -375.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5830" ObjectName="SW-CX_QF.CX_QF_47117SW"/>
     <cge:Meas_Ref ObjectId="36739"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36738">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4332.000000 -222.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5829" ObjectName="SW-CX_QF.CX_QF_4716SW"/>
     <cge:Meas_Ref ObjectId="36738"/>
    <cge:TPSR_Ref TObjectID="5829"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36681">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3958.000000 -392.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5820" ObjectName="SW-CX_QF.CX_QF_4521SW"/>
     <cge:Meas_Ref ObjectId="36681"/>
    <cge:TPSR_Ref TObjectID="5820"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36683">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4000.000000 -375.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5822" ObjectName="SW-CX_QF.CX_QF_45217SW"/>
     <cge:Meas_Ref ObjectId="36683"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36682">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3958.000000 -221.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5821" ObjectName="SW-CX_QF.CX_QF_4526SW"/>
     <cge:Meas_Ref ObjectId="36682"/>
    <cge:TPSR_Ref TObjectID="5821"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36709">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3860.000000 -392.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5824" ObjectName="SW-CX_QF.CX_QF_4531SW"/>
     <cge:Meas_Ref ObjectId="36709"/>
    <cge:TPSR_Ref TObjectID="5824"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36711">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3902.000000 -375.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5826" ObjectName="SW-CX_QF.CX_QF_45317SW"/>
     <cge:Meas_Ref ObjectId="36711"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36710">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3860.000000 -222.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5825" ObjectName="SW-CX_QF.CX_QF_4536SW"/>
     <cge:Meas_Ref ObjectId="36710"/>
    <cge:TPSR_Ref TObjectID="5825"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37070">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4586.000000 -208.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5873" ObjectName="SW-CX_QF.CX_QF_47367SW"/>
     <cge:Meas_Ref ObjectId="37070"/>
    <cge:TPSR_Ref TObjectID="5873"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37044">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4692.000000 -209.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5868" ObjectName="SW-CX_QF.CX_QF_47667SW"/>
     <cge:Meas_Ref ObjectId="37044"/>
    <cge:TPSR_Ref TObjectID="5868"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36438">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4524.000000 -464.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5793" ObjectName="SW-CX_QF.CX_QF_4904SW"/>
     <cge:Meas_Ref ObjectId="36438"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4493.000000 -511.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36987">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4598.000000 -831.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5844" ObjectName="SW-CX_QF.CX_QF_1702SW"/>
     <cge:Meas_Ref ObjectId="36987"/>
    <cge:TPSR_Ref TObjectID="5844"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36445">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4658.000000 -838.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5799" ObjectName="SW-CX_QF.CX_QF_3016SW"/>
     <cge:Meas_Ref ObjectId="36445"/>
    <cge:TPSR_Ref TObjectID="5799"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36444">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4756.000000 -838.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5798" ObjectName="SW-CX_QF.CX_QF_3011SW"/>
     <cge:Meas_Ref ObjectId="36444"/>
    <cge:TPSR_Ref TObjectID="5798"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36402">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4859.000000 -753.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5783" ObjectName="SW-CX_QF.CX_QF_3121SW"/>
     <cge:Meas_Ref ObjectId="36402"/>
    <cge:TPSR_Ref TObjectID="5783"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36403">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4860.000000 -662.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5784" ObjectName="SW-CX_QF.CX_QF_3122SW"/>
     <cge:Meas_Ref ObjectId="36403"/>
    <cge:TPSR_Ref TObjectID="5784"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36991">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4661.000000 -637.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5847" ObjectName="SW-CX_QF.CX_QF_3026SW"/>
     <cge:Meas_Ref ObjectId="36991"/>
    <cge:TPSR_Ref TObjectID="5847"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36990">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4758.000000 -637.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5846" ObjectName="SW-CX_QF.CX_QF_3021SW"/>
     <cge:Meas_Ref ObjectId="36990"/>
    <cge:TPSR_Ref TObjectID="5846"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36421">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4864.000000 -1051.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5786" ObjectName="SW-CX_QF.CX_QF_3901SW"/>
     <cge:Meas_Ref ObjectId="36421"/>
    <cge:TPSR_Ref TObjectID="5786"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36551">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4860.000000 -973.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5807" ObjectName="SW-CX_QF.CX_QF_3521SW"/>
     <cge:Meas_Ref ObjectId="36551"/>
    <cge:TPSR_Ref TObjectID="5807"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36579">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4959.000000 -882.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5811" ObjectName="SW-CX_QF.CX_QF_3536SW"/>
     <cge:Meas_Ref ObjectId="36579"/>
    <cge:TPSR_Ref TObjectID="5811"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36524">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4863.000000 -1125.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5804" ObjectName="SW-CX_QF.CX_QF_3511SW"/>
     <cge:Meas_Ref ObjectId="36524"/>
    <cge:TPSR_Ref TObjectID="5804"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36525">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4960.000000 -1125.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5805" ObjectName="SW-CX_QF.CX_QF_3516SW"/>
     <cge:Meas_Ref ObjectId="36525"/>
    <cge:TPSR_Ref TObjectID="5805"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36426">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4873.000000 -799.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5788" ObjectName="SW-CX_QF.CX_QF_3903SW"/>
     <cge:Meas_Ref ObjectId="36426"/>
    <cge:TPSR_Ref TObjectID="5788"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36578">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4862.000000 -882.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5810" ObjectName="SW-CX_QF.CX_QF_3531SW"/>
     <cge:Meas_Ref ObjectId="36578"/>
    <cge:TPSR_Ref TObjectID="5810"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36552">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4957.000000 -973.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5808" ObjectName="SW-CX_QF.CX_QF_3526SW"/>
     <cge:Meas_Ref ObjectId="36552"/>
    <cge:TPSR_Ref TObjectID="5808"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37094">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4859.000000 -291.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5875" ObjectName="SW-CX_QF.CX_QF_3561SW"/>
     <cge:Meas_Ref ObjectId="37094"/>
    <cge:TPSR_Ref TObjectID="5875"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37095">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4956.000000 -291.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5876" ObjectName="SW-CX_QF.CX_QF_3566SW"/>
     <cge:Meas_Ref ObjectId="37095"/>
    <cge:TPSR_Ref TObjectID="5876"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36605">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4858.000000 -603.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5813" ObjectName="SW-CX_QF.CX_QF_3541SW"/>
     <cge:Meas_Ref ObjectId="36605"/>
    <cge:TPSR_Ref TObjectID="5813"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36606">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4955.000000 -603.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5814" ObjectName="SW-CX_QF.CX_QF_3546SW"/>
     <cge:Meas_Ref ObjectId="36606"/>
    <cge:TPSR_Ref TObjectID="5814"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36632">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4857.000000 -517.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5816" ObjectName="SW-CX_QF.CX_QF_3551SW"/>
     <cge:Meas_Ref ObjectId="36632"/>
    <cge:TPSR_Ref TObjectID="5816"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36633">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4954.000000 -517.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5817" ObjectName="SW-CX_QF.CX_QF_3556SW"/>
     <cge:Meas_Ref ObjectId="36633"/>
    <cge:TPSR_Ref TObjectID="5817"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36429">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4871.000000 -435.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5789" ObjectName="SW-CX_QF.CX_QF_3904SW"/>
     <cge:Meas_Ref ObjectId="36429"/>
    <cge:TPSR_Ref TObjectID="5789"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36424">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4862.000000 -359.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5787" ObjectName="SW-CX_QF.CX_QF_3902SW"/>
     <cge:Meas_Ref ObjectId="36424"/>
    <cge:TPSR_Ref TObjectID="5787"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36793">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3596.000000 -387.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5836" ObjectName="SW-CX_QF.CX_QF_4561SW"/>
     <cge:Meas_Ref ObjectId="36793"/>
    <cge:TPSR_Ref TObjectID="5836"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36794">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3596.000000 -209.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5837" ObjectName="SW-CX_QF.CX_QF_4566SW"/>
     <cge:Meas_Ref ObjectId="36794"/>
    <cge:TPSR_Ref TObjectID="5837"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57360">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3639.000000 -368.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10680" ObjectName="SW-CX_QF.CX_QF_45617SW"/>
     <cge:Meas_Ref ObjectId="57360"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57361">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3638.000000 -191.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10681" ObjectName="SW-CX_QF.CX_QF_45667SW"/>
     <cge:Meas_Ref ObjectId="57361"/>
    <cge:TPSR_Ref TObjectID="10681"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37121">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3857.000000 -1044.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5879" ObjectName="SW-CX_QF.CX_QF_1966SW"/>
     <cge:Meas_Ref ObjectId="37121"/>
    <cge:TPSR_Ref TObjectID="5879"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37006">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4402.000000 -1044.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5856" ObjectName="SW-CX_QF.CX_QF_1976SW"/>
     <cge:Meas_Ref ObjectId="37006"/>
    <cge:TPSR_Ref TObjectID="5856"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_QF.CX_QF_3IIM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4835,-281 4835,-712 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="5780" ObjectName="BS-CX_QF.CX_QF_3IIM"/>
    <cge:TPSR_Ref TObjectID="5780"/></metadata>
   <polyline fill="none" opacity="0" points="4835,-281 4835,-712 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_QF.CX_QF_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4835,-751 4835,-1171 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="5779" ObjectName="BS-CX_QF.CX_QF_3IM"/>
    <cge:TPSR_Ref TObjectID="5779"/></metadata>
   <polyline fill="none" opacity="0" points="4835,-751 4835,-1171 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_QF.CX_QF_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3573,-465 4237,-465 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="5775" ObjectName="BS-CX_QF.CX_QF_9IM"/>
    <cge:TPSR_Ref TObjectID="5775"/></metadata>
   <polyline fill="none" opacity="0" points="3573,-465 4237,-465 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_QF.CX_QF_9IIM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4297,-465 4727,-465 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="5776" ObjectName="BS-CX_QF.CX_QF_9IIM"/>
    <cge:TPSR_Ref TObjectID="5776"/></metadata>
   <polyline fill="none" opacity="0" points="4297,-465 4727,-465 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_QF.CX_QF_1IM">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3865,-897 3882,-897 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="5777" ObjectName="BS-CX_QF.CX_QF_1IM"/>
    <cge:TPSR_Ref TObjectID="5777"/></metadata>
   <polyline fill="none" opacity="0" points="3865,-897 3882,-897 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_QF.CX_QF_1IM">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4403,-897 4422,-897 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="5777" ObjectName="BS-CX_QF.CX_QF_1IM"/>
    <cge:TPSR_Ref TObjectID="5777"/></metadata>
   <polyline fill="none" opacity="0" points="4403,-897 4422,-897 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-CX_QF.CX_QF_Cb1">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3584.000000 -84.000000)" xlink:href="#capacitor:shape14"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15555" ObjectName="CB-CX_QF.CX_QF_Cb1"/>
    <cge:TPSR_Ref TObjectID="15555"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-CX_QF.CX_QF_Cb3">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3728.000000 -82.000000)" xlink:href="#capacitor:shape14"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15558" ObjectName="CB-CX_QF.CX_QF_Cb3"/>
    <cge:TPSR_Ref TObjectID="15558"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-CX_QF.CX_QF_Cb4">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4532.000000 -110.000000)" xlink:href="#capacitor:shape14"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15557" ObjectName="CB-CX_QF.CX_QF_Cb4"/>
    <cge:TPSR_Ref TObjectID="15557"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-CX_QF.CX_QF_Cb2">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4638.000000 -110.000000)" xlink:href="#capacitor:shape14"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15556" ObjectName="CB-CX_QF.CX_QF_Cb2"/>
    <cge:TPSR_Ref TObjectID="15556"/></metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_QF.CX_QF_353_2TZyb">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="16943"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5125.000000 -950.000000)" xlink:href="#transformer2:shape3_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5125.000000 -950.000000)" xlink:href="#transformer2:shape3_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="12120" ObjectName="TF-CX_QF.CX_QF_353_2TZyb"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_QF.CX_QF_1TZyb">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="16947"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4165.000000 -250.000000)" xlink:href="#transformer2:shape3_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4165.000000 -250.000000)" xlink:href="#transformer2:shape3_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="12119" ObjectName="TF-CX_QF.CX_QF_1TZyb"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(0.700000 -0.000000 0.000000 0.477778 4098.000000 -300.000000)" xlink:href="#transformer2:shape15_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.700000 -0.000000 0.000000 0.477778 4098.000000 -300.000000)" xlink:href="#transformer2:shape15_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_3fe68c0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4373.585714 -1141.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3ffcdc0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3829.000000 -1141.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_40008e0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3807.000000 -753.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_401c8b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3775.000000 -328.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_401ecd0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3717.000000 -207.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f66ff0">
    <use class="BV-10KV" transform="matrix(0.000000 -2.166667 -2.000000 -0.000000 3736.000000 -607.916667)" xlink:href="#lightningRod:shape45"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f70ab0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4106.000000 -317.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f71590">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4169.000000 -317.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f9dd90">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4480.000000 -218.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3faa2d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4374.000000 -218.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3fb47c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4000.000000 -218.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3fcea70">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4530.000000 -548.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3fcf7b0">
    <use class="BV-10KV" transform="matrix(-1.000000 0.000000 0.000000 1.000000 4551.000000 -639.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3fd0110">
    <use class="BV-35KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4089.000000 -687.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3fd4810">
    <use class="BV-110KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 4527.000000 -767.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4034b50">
    <use class="BV-35KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 4955.500000 -834.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4035890">
    <use class="BV-35KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 4971.000000 -813.000000)" xlink:href="#lightningRod:shape29"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_403df20">
    <use class="BV-35KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 5021.500000 -930.500000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_403f190">
    <use class="BV-35KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 5064.500000 -972.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4048080">
    <use class="BV-35KV" transform="matrix(-0.000000 -1.000000 0.965517 -0.000000 5022.517241 -1173.500000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_404bda0">
    <use class="BV-35KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 4958.500000 -1099.500000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4054aa0">
    <use class="BV-35KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 5019.500000 -1021.500000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_406d8a0">
    <use class="BV-35KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 5016.500000 -565.500000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4071360">
    <use class="BV-35KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 4953.500000 -470.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_40720a0">
    <use class="BV-35KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 4969.000000 -449.000000)" xlink:href="#lightningRod:shape29"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4075750">
    <use class="BV-35KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 4956.500000 -406.500000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4076760">
    <use class="BV-35KV" transform="matrix(1.840000 -0.000000 0.000000 1.818182 5024.000000 -403.000000)" xlink:href="#lightningRod:shape45"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4077000">
    <use class="BV-35KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 5017.500000 -651.500000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_407ba70">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3779.000000 -691.000000)" xlink:href="#lightningRod:shape54"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_407c640">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4540.000000 -829.000000)" xlink:href="#lightningRod:shape54"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_407d210">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3750.000000 -272.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_407e420">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3870.000000 -288.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_407f890">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3968.000000 -288.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4080aa0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4342.000000 -288.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4082630">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4448.000000 -288.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4083840">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4554.000000 -289.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4084a50">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4660.000000 -289.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4086120">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3743.000000 -1109.000000)" xlink:href="#lightningRod:shape106"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_40876c0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4287.585714 -1109.000000)" xlink:href="#lightningRod:shape106"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_40921c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3631.000000 -328.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_40945e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3573.000000 -207.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_409f9d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3606.000000 -272.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_40d2220">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3705.000000 -549.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_40d2950">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4647.000000 -555.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_40d3190">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4968.000000 -1068.000000)" xlink:href="#lightningRod:shape132"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_40d40e0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4973.000000 -376.000000)" xlink:href="#lightningRod:shape132"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_40d5030">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3907.000000 -216.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_40d6500">
    <use class="BV-35KV" transform="matrix(2.000000 -0.000000 0.000000 -1.288462 4120.000000 -680.000000)" xlink:href="#lightningRod:shape41"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_40dc510">
    <use class="BV-10KV" transform="matrix(0.000000 -2.166667 -2.000000 -0.000000 4676.000000 -594.916667)" xlink:href="#lightningRod:shape45"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_40dcb00">
    <use class="BV-35KV" transform="matrix(1.840000 -0.000000 0.000000 1.818182 5026.000000 -1095.000000)" xlink:href="#lightningRod:shape45"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_40de970">
    <use class="BV-10KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 3742.500000 -531.500000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_40e07c0">
    <use class="BV-10KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 4695.000000 -569.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-57366" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3774.000000 -801.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57366" ObjectName="CX_QF:CX_QF_1T_Tmp"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-57365" ratioFlag="0">
    <text fill="rgb(173,173,173)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3774.000000 -787.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57365" ObjectName="CX_QF:CX_QF_1T_Tmp1"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-36312" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4332.000000 -782.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36312" ObjectName="CX_QF:CX_QF_2T_Tmp"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-36313" ratioFlag="0">
    <text fill="rgb(173,173,173)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4332.000000 -768.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36313" ObjectName="CX_QF:CX_QF_2T_Tmp1"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 0.000000 0.000000 2.335135 3205.000000 -1118.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-62627" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 0.000000 0.000000 1.395515 3273.538462 -992.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="62627" ObjectName="CX_QF:CX_QF_sumP"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-56547" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4040.000000 -1035.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="56547" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5882"/>
     <cge:Term_Ref ObjectID="8575"/>
    <cge:TPSR_Ref TObjectID="5882"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-56548" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4040.000000 -1035.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="56548" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5882"/>
     <cge:Term_Ref ObjectID="8575"/>
    <cge:TPSR_Ref TObjectID="5882"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-36332" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4040.000000 -1035.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36332" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5882"/>
     <cge:Term_Ref ObjectID="8575"/>
    <cge:TPSR_Ref TObjectID="5882"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-56541" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4354.585714 -1035.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="56541" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5853"/>
     <cge:Term_Ref ObjectID="8517"/>
    <cge:TPSR_Ref TObjectID="5853"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-56542" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4354.585714 -1035.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="56542" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5853"/>
     <cge:Term_Ref ObjectID="8517"/>
    <cge:TPSR_Ref TObjectID="5853"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-56538" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4354.585714 -1035.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="56538" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5853"/>
     <cge:Term_Ref ObjectID="8517"/>
    <cge:TPSR_Ref TObjectID="5853"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-36208" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3979.000000 -602.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36208" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5800"/>
     <cge:Term_Ref ObjectID="8411"/>
    <cge:TPSR_Ref TObjectID="5800"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-36209" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3979.000000 -602.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36209" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5800"/>
     <cge:Term_Ref ObjectID="8411"/>
    <cge:TPSR_Ref TObjectID="5800"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-36205" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3979.000000 -602.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36205" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5800"/>
     <cge:Term_Ref ObjectID="8411"/>
    <cge:TPSR_Ref TObjectID="5800"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-36309" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4376.000000 -581.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36309" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5849"/>
     <cge:Term_Ref ObjectID="8509"/>
    <cge:TPSR_Ref TObjectID="5849"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-36310" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4376.000000 -581.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36310" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5849"/>
     <cge:Term_Ref ObjectID="8509"/>
    <cge:TPSR_Ref TObjectID="5849"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-36306" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4376.000000 -581.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36306" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5849"/>
     <cge:Term_Ref ObjectID="8509"/>
    <cge:TPSR_Ref TObjectID="5849"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-36267" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3885.000000 -70.500000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36267" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5823"/>
     <cge:Term_Ref ObjectID="8457"/>
    <cge:TPSR_Ref TObjectID="5823"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-36268" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3885.000000 -70.500000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36268" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5823"/>
     <cge:Term_Ref ObjectID="8457"/>
    <cge:TPSR_Ref TObjectID="5823"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-36263" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3885.000000 -70.500000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36263" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5823"/>
     <cge:Term_Ref ObjectID="8457"/>
    <cge:TPSR_Ref TObjectID="5823"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-36259" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4011.000000 -70.500000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36259" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5819"/>
     <cge:Term_Ref ObjectID="8449"/>
    <cge:TPSR_Ref TObjectID="5819"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-36260" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4011.000000 -70.500000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36260" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5819"/>
     <cge:Term_Ref ObjectID="8449"/>
    <cge:TPSR_Ref TObjectID="5819"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-36255" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4011.000000 -70.500000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36255" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5819"/>
     <cge:Term_Ref ObjectID="8449"/>
    <cge:TPSR_Ref TObjectID="5819"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-36283" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4470.000000 -70.500000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36283" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5831"/>
     <cge:Term_Ref ObjectID="8473"/>
    <cge:TPSR_Ref TObjectID="5831"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-36284" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4470.000000 -70.500000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36284" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5831"/>
     <cge:Term_Ref ObjectID="8473"/>
    <cge:TPSR_Ref TObjectID="5831"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-36279" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4470.000000 -70.500000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36279" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5831"/>
     <cge:Term_Ref ObjectID="8473"/>
    <cge:TPSR_Ref TObjectID="5831"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-36275" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4341.000000 -70.500000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36275" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5827"/>
     <cge:Term_Ref ObjectID="8465"/>
    <cge:TPSR_Ref TObjectID="5827"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-36276" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4341.000000 -70.500000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36276" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5827"/>
     <cge:Term_Ref ObjectID="8465"/>
    <cge:TPSR_Ref TObjectID="5827"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-36271" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4341.000000 -70.500000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36271" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5827"/>
     <cge:Term_Ref ObjectID="8465"/>
    <cge:TPSR_Ref TObjectID="5827"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-36216" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5185.000000 -1172.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36216" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5803"/>
     <cge:Term_Ref ObjectID="8417"/>
    <cge:TPSR_Ref TObjectID="5803"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-36217" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5185.000000 -1172.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36217" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5803"/>
     <cge:Term_Ref ObjectID="8417"/>
    <cge:TPSR_Ref TObjectID="5803"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-36212" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5185.000000 -1172.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36212" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5803"/>
     <cge:Term_Ref ObjectID="8417"/>
    <cge:TPSR_Ref TObjectID="5803"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-36224" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5185.000000 -1018.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36224" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5806"/>
     <cge:Term_Ref ObjectID="8423"/>
    <cge:TPSR_Ref TObjectID="5806"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-36225" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5185.000000 -1018.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36225" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5806"/>
     <cge:Term_Ref ObjectID="8423"/>
    <cge:TPSR_Ref TObjectID="5806"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-36220" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5185.000000 -1018.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36220" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5806"/>
     <cge:Term_Ref ObjectID="8423"/>
    <cge:TPSR_Ref TObjectID="5806"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-36232" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5184.000000 -924.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36232" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5809"/>
     <cge:Term_Ref ObjectID="8429"/>
    <cge:TPSR_Ref TObjectID="5809"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-36233" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5184.000000 -924.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36233" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5809"/>
     <cge:Term_Ref ObjectID="8429"/>
    <cge:TPSR_Ref TObjectID="5809"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-36228" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5184.000000 -924.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36228" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5809"/>
     <cge:Term_Ref ObjectID="8429"/>
    <cge:TPSR_Ref TObjectID="5809"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-36252" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4973.000000 -743.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36252" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5818"/>
     <cge:Term_Ref ObjectID="8447"/>
    <cge:TPSR_Ref TObjectID="5818"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-36240" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5184.000000 -650.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36240" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5812"/>
     <cge:Term_Ref ObjectID="8435"/>
    <cge:TPSR_Ref TObjectID="5812"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-36241" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5184.000000 -650.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36241" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5812"/>
     <cge:Term_Ref ObjectID="8435"/>
    <cge:TPSR_Ref TObjectID="5812"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-36236" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5184.000000 -650.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36236" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5812"/>
     <cge:Term_Ref ObjectID="8435"/>
    <cge:TPSR_Ref TObjectID="5812"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-36248" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5184.000000 -565.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36248" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5815"/>
     <cge:Term_Ref ObjectID="8441"/>
    <cge:TPSR_Ref TObjectID="5815"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-36249" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5184.000000 -565.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36249" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5815"/>
     <cge:Term_Ref ObjectID="8441"/>
    <cge:TPSR_Ref TObjectID="5815"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-36244" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5184.000000 -565.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36244" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5815"/>
     <cge:Term_Ref ObjectID="8441"/>
    <cge:TPSR_Ref TObjectID="5815"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-36328" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5184.000000 -334.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36328" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5874"/>
     <cge:Term_Ref ObjectID="8559"/>
    <cge:TPSR_Ref TObjectID="5874"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-36329" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5184.000000 -334.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36329" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5874"/>
     <cge:Term_Ref ObjectID="8559"/>
    <cge:TPSR_Ref TObjectID="5874"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-36325" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5184.000000 -334.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36325" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5874"/>
     <cge:Term_Ref ObjectID="8559"/>
    <cge:TPSR_Ref TObjectID="5874"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-36201" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4728.000000 -852.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36201" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5797"/>
     <cge:Term_Ref ObjectID="8405"/>
    <cge:TPSR_Ref TObjectID="5797"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-36202" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4728.000000 -852.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36202" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5797"/>
     <cge:Term_Ref ObjectID="8405"/>
    <cge:TPSR_Ref TObjectID="5797"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-36194" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4728.000000 -852.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36194" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5797"/>
     <cge:Term_Ref ObjectID="8405"/>
    <cge:TPSR_Ref TObjectID="5797"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-36303" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4728.000000 -738.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36303" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5845"/>
     <cge:Term_Ref ObjectID="8501"/>
    <cge:TPSR_Ref TObjectID="5845"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-36304" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4728.000000 -738.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36304" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5845"/>
     <cge:Term_Ref ObjectID="8501"/>
    <cge:TPSR_Ref TObjectID="5845"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-56535" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4728.000000 -738.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="56535" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5845"/>
     <cge:Term_Ref ObjectID="8501"/>
    <cge:TPSR_Ref TObjectID="5845"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-36288" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3610.000000 -61.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36288" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5835"/>
     <cge:Term_Ref ObjectID="8481"/>
    <cge:TPSR_Ref TObjectID="5835"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-36287" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3610.000000 -61.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36287" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5835"/>
     <cge:Term_Ref ObjectID="8481"/>
    <cge:TPSR_Ref TObjectID="5835"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-36291" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3752.000000 -61.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36291" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5838"/>
     <cge:Term_Ref ObjectID="8487"/>
    <cge:TPSR_Ref TObjectID="5838"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-36290" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3752.000000 -61.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36290" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5838"/>
     <cge:Term_Ref ObjectID="8487"/>
    <cge:TPSR_Ref TObjectID="5838"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-36323" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4594.000000 -62.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36323" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5869"/>
     <cge:Term_Ref ObjectID="8549"/>
    <cge:TPSR_Ref TObjectID="5869"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-36322" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4594.000000 -62.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36322" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5869"/>
     <cge:Term_Ref ObjectID="8549"/>
    <cge:TPSR_Ref TObjectID="5869"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-36320" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4714.000000 -62.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36320" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5864"/>
     <cge:Term_Ref ObjectID="8539"/>
    <cge:TPSR_Ref TObjectID="5864"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-36319" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4714.000000 -62.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36319" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5864"/>
     <cge:Term_Ref ObjectID="8539"/>
    <cge:TPSR_Ref TObjectID="5864"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-36142" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3536.000000 -551.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36142" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5775"/>
     <cge:Term_Ref ObjectID="8367"/>
    <cge:TPSR_Ref TObjectID="5775"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-36143" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3536.000000 -551.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36143" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5775"/>
     <cge:Term_Ref ObjectID="8367"/>
    <cge:TPSR_Ref TObjectID="5775"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-36144" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3536.000000 -551.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36144" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5775"/>
     <cge:Term_Ref ObjectID="8367"/>
    <cge:TPSR_Ref TObjectID="5775"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uo" PreSymbol="0" appendix="" decimal="2" id="ME-36148" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3536.000000 -551.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36148" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5775"/>
     <cge:Term_Ref ObjectID="8367"/>
    <cge:TPSR_Ref TObjectID="5775"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-36150" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3536.000000 -551.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36150" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5775"/>
     <cge:Term_Ref ObjectID="8367"/>
    <cge:TPSR_Ref TObjectID="5775"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-36145" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4777.000000 -561.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36145" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5776"/>
     <cge:Term_Ref ObjectID="8368"/>
    <cge:TPSR_Ref TObjectID="5776"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-36146" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4777.000000 -561.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36146" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5776"/>
     <cge:Term_Ref ObjectID="8368"/>
    <cge:TPSR_Ref TObjectID="5776"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-36147" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4777.000000 -561.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36147" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5776"/>
     <cge:Term_Ref ObjectID="8368"/>
    <cge:TPSR_Ref TObjectID="5776"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uo" PreSymbol="0" appendix="" decimal="2" id="ME-36149" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4777.000000 -561.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36149" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5776"/>
     <cge:Term_Ref ObjectID="8368"/>
    <cge:TPSR_Ref TObjectID="5776"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-36153" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4777.000000 -561.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36153" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5776"/>
     <cge:Term_Ref ObjectID="8368"/>
    <cge:TPSR_Ref TObjectID="5776"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-36174" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4811.000000 -1252.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36174" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5779"/>
     <cge:Term_Ref ObjectID="8371"/>
    <cge:TPSR_Ref TObjectID="5779"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-36175" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4811.000000 -1252.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36175" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5779"/>
     <cge:Term_Ref ObjectID="8371"/>
    <cge:TPSR_Ref TObjectID="5779"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-36176" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4811.000000 -1252.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36176" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5779"/>
     <cge:Term_Ref ObjectID="8371"/>
    <cge:TPSR_Ref TObjectID="5779"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uo" PreSymbol="0" appendix="" decimal="2" id="ME-36180" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4811.000000 -1252.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36180" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5779"/>
     <cge:Term_Ref ObjectID="8371"/>
    <cge:TPSR_Ref TObjectID="5779"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-36182" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4811.000000 -1252.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36182" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5779"/>
     <cge:Term_Ref ObjectID="8371"/>
    <cge:TPSR_Ref TObjectID="5779"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-36177" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4847.000000 -264.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36177" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5780"/>
     <cge:Term_Ref ObjectID="8372"/>
    <cge:TPSR_Ref TObjectID="5780"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-36178" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4847.000000 -264.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36178" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5780"/>
     <cge:Term_Ref ObjectID="8372"/>
    <cge:TPSR_Ref TObjectID="5780"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-36179" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4847.000000 -264.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36179" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5780"/>
     <cge:Term_Ref ObjectID="8372"/>
    <cge:TPSR_Ref TObjectID="5780"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uo" PreSymbol="0" appendix="" decimal="2" id="ME-36181" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4847.000000 -264.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36181" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5780"/>
     <cge:Term_Ref ObjectID="8372"/>
    <cge:TPSR_Ref TObjectID="5780"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-36185" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4847.000000 -264.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36185" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5780"/>
     <cge:Term_Ref ObjectID="8372"/>
    <cge:TPSR_Ref TObjectID="5780"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="44" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-36190" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3781.000000 -815.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36190" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5885"/>
     <cge:Term_Ref ObjectID="8584"/>
    <cge:TPSR_Ref TObjectID="5885"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="44" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-36296" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4339.000000 -797.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36296" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5886"/>
     <cge:Term_Ref ObjectID="8591"/>
    <cge:TPSR_Ref TObjectID="5886"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-36293" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4258.000000 -572.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36293" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5841"/>
     <cge:Term_Ref ObjectID="8493"/>
    <cge:TPSR_Ref TObjectID="5841"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-36314" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4135.300000 -938.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36314" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5859"/>
     <cge:Term_Ref ObjectID="8529"/>
    <cge:TPSR_Ref TObjectID="5859"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="43" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-70601" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3799.000000 -884.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="70601" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5794"/>
     <cge:Term_Ref ObjectID="8399"/>
    <cge:TPSR_Ref TObjectID="5794"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="43" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-70602" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3799.000000 -884.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="70602" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5794"/>
     <cge:Term_Ref ObjectID="8399"/>
    <cge:TPSR_Ref TObjectID="5794"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="43" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-70605" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3799.000000 -884.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="70605" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5794"/>
     <cge:Term_Ref ObjectID="8399"/>
    <cge:TPSR_Ref TObjectID="5794"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="43" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-70606" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4346.000000 -884.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="70606" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5842"/>
     <cge:Term_Ref ObjectID="8495"/>
    <cge:TPSR_Ref TObjectID="5842"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="43" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-70604" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4346.000000 -884.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="70604" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5842"/>
     <cge:Term_Ref ObjectID="8495"/>
    <cge:TPSR_Ref TObjectID="5842"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="43" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-70603" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4346.000000 -884.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="70603" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5842"/>
     <cge:Term_Ref ObjectID="8495"/>
    <cge:TPSR_Ref TObjectID="5842"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259" style="fill-opacity:0">
    <a>
     
     <rect height="41" qtmmishow="hidden" width="138" x="3217" y="-1177"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="3217" y="-1177"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124" style="fill-opacity:0">
    <a>
     
     <rect height="69" qtmmishow="hidden" width="77" x="3168" y="-1194"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3168" y="-1194"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" style="fill-opacity:0">
    <a>
     <polygon fill="rgb(255,255,255)" points="3402,-1187 3399,-1190 3399,-1122 3402,-1125 3402,-1187" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(255,255,255)" points="3402,-1187 3399,-1190 3470,-1190 3467,-1187 3402,-1187" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(127,127,127)" points="3402,-1125 3399,-1122 3470,-1122 3467,-1125 3402,-1125" stroke="rgb(127,127,127)"/>
     <polygon fill="rgb(127,127,127)" points="3467,-1187 3470,-1190 3470,-1122 3467,-1125 3467,-1187" stroke="rgb(127,127,127)"/>
     <rect fill="rgb(255,255,255)" height="62" stroke="rgb(255,255,255)" width="65" x="3402" y="-1187"/>
     <rect height="62" qtmmishow="hidden" stroke="rgb(0,0,0)" width="65" x="3402" y="-1187"/>
    </a>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" style="fill-opacity:0">
    <a>
     <rect height="22" qtmmishow="hidden" width="22" x="5084" y="-1195"/>
    </a>
   <metadata/><rect fill="white" height="22" opacity="0" stroke="white" transform="" width="22" x="5084" y="-1195"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" style="fill-opacity:0">
    <a>
     <rect height="19" qtmmishow="hidden" width="24" x="5082" y="-669"/>
    </a>
   <metadata/><rect fill="white" height="19" opacity="0" stroke="white" transform="" width="24" x="5082" y="-669"/></g>
  </g><g id="MotifButton_Layer">
   <g href="lf_索引_接线图.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="3217" y="-1177"/></g>
   <g href="cx_索引_接线图_局属变110.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3168" y="-1194"/></g>
   <g href="AVC勤丰站.svg" style="fill-opacity:0"><rect height="62" qtmmishow="hidden" stroke="rgb(0,0,0)" width="65" x="3402" y="-1187"/></g>
   <g href="cx_地调_重要用电用户表.svg" style="fill-opacity:0"><rect height="22" qtmmishow="hidden" width="22" x="5084" y="-1195"/></g>
   <g href="cx_地调_重要用电用户表.svg" style="fill-opacity:0"><rect height="19" qtmmishow="hidden" width="24" x="5082" y="-669"/></g>
  </g><g id="PolygonFilled_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="none" points="5083,-651 5105,-651 5094,-668 5083,-651 " stroke="rgb(255,255,255)"/>
  </g><g id="Transformer_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_QF.CX_QF_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="8581"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3831.000000 -690.000000)" xlink:href="#transformer:shape16_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="8583"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3831.000000 -690.000000)" xlink:href="#transformer:shape16_1"/>
    </g>
    <g id="WD-2">
     <metadata>
      <cge:PSR_Ref ObjectId="8585"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3831.000000 -690.000000)" xlink:href="#transformer:shape16-2"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="5885" ObjectName="TF-CX_QF.CX_QF_1T"/>
    <cge:TPSR_Ref TObjectID="5885"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_QF.CX_QF_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="8588"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4379.000000 -691.000000)" xlink:href="#transformer:shape16_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="8590"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4379.000000 -691.000000)" xlink:href="#transformer:shape16_1"/>
    </g>
    <g id="WD-2">
     <metadata>
      <cge:PSR_Ref ObjectId="8592"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4379.000000 -691.000000)" xlink:href="#transformer:shape16-2"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="5886" ObjectName="TF-CX_QF.CX_QF_2T"/>
    <cge:TPSR_Ref TObjectID="5886"/></metadata>
   </g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-CX_QF.CX_QF_351Ld">
    <use class="BKBV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5087.000000 -1142.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15388" ObjectName="EC-CX_QF.CX_QF_351Ld"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_QF.CX_QF_352Ld">
    <use class="BKBV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5074.000000 -990.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15389" ObjectName="EC-CX_QF.CX_QF_352Ld"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_QF.CX_QF_353Ld">
    <use class="BKBV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5076.000000 -899.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15390" ObjectName="EC-CX_QF.CX_QF_353Ld"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_QF.CX_QF_354Ld">
    <use class="BKBV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5076.000000 -620.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15391" ObjectName="EC-CX_QF.CX_QF_354Ld"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_QF.CX_QF_355Ld">
    <use class="BKBV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5071.000000 -534.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15392" ObjectName="EC-CX_QF.CX_QF_355Ld"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4338.000000 -106.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4444.000000 -113.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-10KV" id="g_3f04810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3870,-696 3870,-649 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="switch" ObjectIDND0="5885@2" ObjectIDZND0="5802@1" Pin0InfoVect0LinkObjId="SW-36448_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4018ad0_2" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3870,-696 3870,-649 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3f04a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3870,-613 3870,-595 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="5802@0" ObjectIDZND0="5800@0" Pin0InfoVect0LinkObjId="SW-36446_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36448_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3870,-613 3870,-595 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3f04cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3870,-568 3870,-551 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="5800@1" ObjectIDZND0="5801@1" Pin0InfoVect0LinkObjId="SW-36447_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36446_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3870,-568 3870,-551 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3f04f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3870,-515 3870,-465 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="5801@0" ObjectIDZND0="5775@0" Pin0InfoVect0LinkObjId="g_40135d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36447_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3870,-515 3870,-465 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ea1e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4418,-808 4436,-808 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5886@x" ObjectIDND1="5842@x" ObjectIDZND0="5843@0" Pin0InfoVect0LinkObjId="SW-36986_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3f60680_0" Pin1InfoVect1LinkObjId="SW-36985_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4418,-808 4436,-808 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ea20f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4472,-808 4489,-808 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="5843@1" ObjectIDZND0="g_3ea2350@0" Pin0InfoVect0LinkObjId="g_3ea2350_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36986_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4472,-808 4489,-808 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ea8140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4417,-978 4435,-978 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="5854@x" ObjectIDND1="5853@x" ObjectIDZND0="5855@0" Pin0InfoVect0LinkObjId="SW-37005_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-37004_0" Pin1InfoVect1LinkObjId="SW-37000_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4417,-978 4435,-978 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ea83a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4471,-978 4488,-978 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="5855@1" ObjectIDZND0="g_3ea8600@0" Pin0InfoVect0LinkObjId="g_3ea8600_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37005_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4471,-978 4488,-978 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3eadb90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4418,-1049 4436,-1049 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5853@x" ObjectIDND1="5856@x" ObjectIDZND0="5857@0" Pin0InfoVect0LinkObjId="SW-37007_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-37000_0" Pin1InfoVect1LinkObjId="SW-37006_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4418,-1049 4436,-1049 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3eaddf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4472,-1049 4489,-1049 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="5857@1" ObjectIDZND0="g_3eae050@0" Pin0InfoVect0LinkObjId="g_3eae050_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37007_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4472,-1049 4489,-1049 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3fe5bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4471,-1122 4488,-1122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="5858@1" ObjectIDZND0="g_3fe5e30@0" Pin0InfoVect0LinkObjId="g_3fe5e30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37008_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4471,-1122 4488,-1122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3fec9d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3872,-808 3890,-808 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5885@x" ObjectIDND1="5794@x" ObjectIDZND0="5795@0" Pin0InfoVect0LinkObjId="SW-36441_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_4018ad0_0" Pin1InfoVect1LinkObjId="SW-36440_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3872,-808 3890,-808 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3fecc30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3926,-808 3943,-808 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="5795@1" ObjectIDZND0="g_3fece90@0" Pin0InfoVect0LinkObjId="g_3fece90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36441_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3926,-808 3943,-808 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ff2c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3872,-978 3890,-978 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="5877@x" ObjectIDND1="5882@x" ObjectIDZND0="5878@0" Pin0InfoVect0LinkObjId="SW-37120_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-37119_0" Pin1InfoVect1LinkObjId="SW-37124_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3872,-978 3890,-978 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ff2ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3926,-978 3943,-978 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="5878@1" ObjectIDZND0="g_3ff3140@0" Pin0InfoVect0LinkObjId="g_3ff3140_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37120_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3926,-978 3943,-978 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ff86d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3873,-1049 3891,-1049 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5882@x" ObjectIDND1="5879@x" ObjectIDZND0="5880@0" Pin0InfoVect0LinkObjId="SW-37122_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-37124_0" Pin1InfoVect1LinkObjId="SW-37121_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3873,-1049 3891,-1049 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ff8930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3927,-1049 3944,-1049 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="5880@1" ObjectIDZND0="g_3ff8b90@0" Pin0InfoVect0LinkObjId="g_3ff8b90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37122_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3927,-1049 3944,-1049 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ffc0d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3928,-1122 3945,-1122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="5881@1" ObjectIDZND0="g_3ffc330@0" Pin0InfoVect0LinkObjId="g_3ffc330_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37123_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3928,-1122 3945,-1122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ffdb70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3785,-760 3785,-748 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="transformer" EndDevType0="lightningRod" ObjectIDND0="5796@x" ObjectIDND1="g_40008e0@0" ObjectIDND2="5885@x" ObjectIDZND0="g_407ba70@0" Pin0InfoVect0LinkObjId="g_407ba70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-36442_0" Pin1InfoVect1LinkObjId="g_40008e0_0" Pin1InfoVect2LinkObjId="g_4018ad0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3785,-760 3785,-748 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ffddd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3785,-760 3754,-760 3754,-747 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="transformer" EndDevType0="switch" ObjectIDND0="g_407ba70@0" ObjectIDND1="g_40008e0@0" ObjectIDND2="5885@x" ObjectIDZND0="5796@1" Pin0InfoVect0LinkObjId="SW-36442_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_407ba70_0" Pin1InfoVect1LinkObjId="g_40008e0_0" Pin1InfoVect2LinkObjId="g_4018ad0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3785,-760 3754,-760 3754,-747 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_400b0a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4416,-465 4416,-483 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5776@0" ObjectIDZND0="5850@0" Pin0InfoVect0LinkObjId="SW-36996_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4416,-465 4416,-483 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_400b300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4416,-519 4416,-536 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="5850@1" ObjectIDZND0="5849@1" Pin0InfoVect0LinkObjId="SW-36995_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36996_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4416,-519 4416,-536 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_400b560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4416,-563 4416,-581 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="5849@0" ObjectIDZND0="5851@0" Pin0InfoVect0LinkObjId="SW-36997_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36995_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4416,-563 4416,-581 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40135d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4213,-484 4213,-465 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="5781@0" ObjectIDZND0="5775@0" Pin0InfoVect0LinkObjId="g_3f04f30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36400_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4213,-484 4213,-465 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40160e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4318,-465 4318,-487 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5776@0" ObjectIDZND0="5782@0" Pin0InfoVect0LinkObjId="SW-36401_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4318,-465 4318,-487 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4018150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4318,-523 4318,-531 4278,-531 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="5782@1" ObjectIDZND0="5841@0" Pin0InfoVect0LinkObjId="SW-36841_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36401_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4318,-523 4318,-531 4278,-531 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40183b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4251,-531 4213,-531 4213,-520 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="5841@1" ObjectIDZND0="5781@1" Pin0InfoVect0LinkObjId="SW-36400_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36841_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4251,-531 4213,-531 4213,-520 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_4018610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3814,-760 3814,-748 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="transformer" EndDevType0="lightningRod" ObjectIDND0="5796@x" ObjectIDND1="g_407ba70@0" ObjectIDND2="5885@x" ObjectIDZND0="g_40008e0@0" Pin0InfoVect0LinkObjId="g_40008e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-36442_0" Pin1InfoVect1LinkObjId="g_407ba70_0" Pin1InfoVect2LinkObjId="g_4018ad0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3814,-760 3814,-748 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_4018870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3785,-760 3814,-760 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="transformer" ObjectIDND0="5796@x" ObjectIDND1="g_407ba70@0" ObjectIDZND0="g_40008e0@0" ObjectIDZND1="5885@x" Pin0InfoVect0LinkObjId="g_40008e0_0" Pin0InfoVect1LinkObjId="g_4018ad0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-36442_0" Pin1InfoVect1LinkObjId="g_407ba70_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3785,-760 3814,-760 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_4018ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3814,-760 3873,-760 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="transformer" ObjectIDND0="5796@x" ObjectIDND1="g_407ba70@0" ObjectIDND2="g_40008e0@0" ObjectIDZND0="5885@x" Pin0InfoVect0LinkObjId="g_407b810_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-36442_0" Pin1InfoVect1LinkObjId="g_407ba70_0" Pin1InfoVect2LinkObjId="g_40008e0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3814,-760 3873,-760 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_4018d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3754,-711 3754,-700 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="5796@0" ObjectIDZND0="g_4001690@0" Pin0InfoVect0LinkObjId="g_4001690_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36442_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3754,-711 3754,-700 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_401afe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3755,-394 3773,-394 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5838@x" ObjectIDND1="5839@x" ObjectIDZND0="10682@0" Pin0InfoVect0LinkObjId="SW-57362_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-36816_0" Pin1InfoVect1LinkObjId="SW-36817_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3755,-394 3773,-394 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_401b240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3809,-394 3826,-394 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="10682@1" ObjectIDZND0="g_401b4a0@0" Pin0InfoVect0LinkObjId="g_401b4a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57362_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3809,-394 3826,-394 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_401bf30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3755,-380 3755,-394 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="5838@1" ObjectIDZND0="10682@x" ObjectIDZND1="5839@x" Pin0InfoVect0LinkObjId="SW-57362_0" Pin0InfoVect1LinkObjId="SW-36817_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36816_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3755,-380 3755,-394 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_401c190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3755,-394 3755,-409 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5838@x" ObjectIDND1="10682@x" ObjectIDZND0="5839@0" Pin0InfoVect0LinkObjId="SW-36817_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-36816_0" Pin1InfoVect1LinkObjId="SW-57362_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3755,-394 3755,-409 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_401c3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3755,-339 3782,-339 3782,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="5838@x" ObjectIDND1="g_407d210@0" ObjectIDZND0="g_401c8b0@0" Pin0InfoVect0LinkObjId="g_401c8b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-36816_0" Pin1InfoVect1LinkObjId="g_407d210_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3755,-339 3782,-339 3782,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_401c650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3755,-339 3755,-353 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="breaker" ObjectIDND0="g_401c8b0@0" ObjectIDND1="g_407d210@0" ObjectIDZND0="5838@0" Pin0InfoVect0LinkObjId="SW-36816_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_401c8b0_0" Pin1InfoVect1LinkObjId="g_407d210_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3755,-339 3755,-353 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_401d660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3755,-217 3773,-217 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="capacitor" EndDevType0="switch" ObjectIDND0="5840@x" ObjectIDND1="g_401ecd0@0" ObjectIDND2="15558@x" ObjectIDZND0="10683@0" Pin0InfoVect0LinkObjId="SW-57363_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-36818_0" Pin1InfoVect1LinkObjId="g_401ecd0_0" Pin1InfoVect2LinkObjId="CB-CX_QF.CX_QF_Cb3_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3755,-217 3773,-217 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_401d8c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3809,-217 3826,-217 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="10683@1" ObjectIDZND0="g_401db20@0" Pin0InfoVect0LinkObjId="g_401db20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57363_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3809,-217 3826,-217 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_401e5b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3755,-231 3755,-217 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="capacitor" ObjectIDND0="5840@0" ObjectIDZND0="10683@x" ObjectIDZND1="g_401ecd0@0" ObjectIDZND2="15558@x" Pin0InfoVect0LinkObjId="SW-57363_0" Pin0InfoVect1LinkObjId="g_401ecd0_0" Pin0InfoVect2LinkObjId="CB-CX_QF.CX_QF_Cb3_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36818_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3755,-231 3755,-217 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_401e810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3755,-217 3755,-190 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="capacitor" ObjectIDND0="5840@x" ObjectIDND1="10683@x" ObjectIDND2="g_401ecd0@0" ObjectIDZND0="15558@0" Pin0InfoVect0LinkObjId="CB-CX_QF.CX_QF_Cb3_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-36818_0" Pin1InfoVect1LinkObjId="SW-57363_0" Pin1InfoVect2LinkObjId="g_401ecd0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3755,-217 3755,-190 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_401ea70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3755,-217 3724,-217 3724,-202 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="capacitor" EndDevType0="lightningRod" ObjectIDND0="5840@x" ObjectIDND1="10683@x" ObjectIDND2="15558@x" ObjectIDZND0="g_401ecd0@0" Pin0InfoVect0LinkObjId="g_401ecd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-36818_0" Pin1InfoVect1LinkObjId="SW-57363_0" Pin1InfoVect2LinkObjId="CB-CX_QF.CX_QF_Cb3_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3755,-217 3724,-217 3724,-202 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3f53360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3755,-465 3755,-445 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5775@0" ObjectIDZND0="5839@1" Pin0InfoVect0LinkObjId="SW-36817_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3f04f30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3755,-465 3755,-445 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f58b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3882,-897 4053,-897 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5777@0" ObjectIDZND0="5860@0" Pin0InfoVect0LinkObjId="SW-37030_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_40e2eb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3882,-897 4053,-897 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f58d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4089,-897 4108,-897 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="5860@1" ObjectIDZND0="5859@x" ObjectIDZND1="5861@x" Pin0InfoVect0LinkObjId="SW-37014_0" Pin0InfoVect1LinkObjId="SW-37031_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37030_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4089,-897 4108,-897 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f58fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4108,-897 4125,-897 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="5860@x" ObjectIDND1="5861@x" ObjectIDZND0="5859@1" Pin0InfoVect0LinkObjId="SW-37014_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-37030_0" Pin1InfoVect1LinkObjId="SW-37031_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4108,-897 4125,-897 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f59240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4152,-897 4174,-897 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="5859@0" ObjectIDZND0="5862@x" ObjectIDZND1="5863@x" Pin0InfoVect0LinkObjId="SW-37032_0" Pin0InfoVect1LinkObjId="SW-37033_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37014_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4152,-897 4174,-897 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f594a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4174,-897 4194,-897 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5859@x" ObjectIDND1="5863@x" ObjectIDZND0="5862@0" Pin0InfoVect0LinkObjId="SW-37032_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-37014_0" Pin1InfoVect1LinkObjId="SW-37033_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4174,-897 4194,-897 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f5bfb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4108,-897 4108,-875 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5859@x" ObjectIDND1="5860@x" ObjectIDZND0="5861@1" Pin0InfoVect0LinkObjId="SW-37031_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-37014_0" Pin1InfoVect1LinkObjId="SW-37030_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4108,-897 4108,-875 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f5c210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4108,-839 4108,-824 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="5861@0" ObjectIDZND0="g_3f5f1e0@0" Pin0InfoVect0LinkObjId="g_3f5f1e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37031_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4108,-839 4108,-824 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f5ed20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4174,-897 4174,-875 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="5862@x" ObjectIDND1="5859@x" ObjectIDZND0="5863@1" Pin0InfoVect0LinkObjId="SW-37033_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-37032_0" Pin1InfoVect1LinkObjId="SW-37014_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4174,-897 4174,-875 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f5ef80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4174,-839 4174,-824 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="5863@0" ObjectIDZND0="g_3f5fc30@0" Pin0InfoVect0LinkObjId="g_3f5fc30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37033_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4174,-839 4174,-824 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3f60680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4416,-617 4416,-694 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer" ObjectIDND0="5851@1" ObjectIDZND0="5886@2" Pin0InfoVect0LinkObjId="g_3fcfeb0_2" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36997_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4416,-617 4416,-694 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3f63190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3714,-465 3714,-486 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5775@0" ObjectIDZND0="5790@0" Pin0InfoVect0LinkObjId="SW-36430_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3f04f30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3714,-465 3714,-486 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3f633f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3714,-522 3714,-538 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="5790@1" ObjectIDZND0="0@x" ObjectIDZND1="g_40d2220@0" ObjectIDZND2="g_40de970@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_40d2220_0" Pin0InfoVect2LinkObjId="g_40de970_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36430_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3714,-522 3714,-538 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3f660a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3714,-538 3696,-538 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="5790@x" ObjectIDND1="g_40d2220@0" ObjectIDND2="g_40de970@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-36430_0" Pin1InfoVect1LinkObjId="g_40d2220_0" Pin1InfoVect2LinkObjId="g_40de970_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3714,-538 3696,-538 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3f66300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3660,-538 3647,-538 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_3f66560@0" Pin0InfoVect0LinkObjId="g_3f66560_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3660,-538 3647,-538 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3f6a140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4178,-465 4178,-440 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="5775@0" ObjectIDZND0="5792@x" ObjectIDZND1="5883@x" Pin0InfoVect0LinkObjId="SW-36435_0" Pin0InfoVect1LinkObjId="SW-37304_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3f04f30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4178,-465 4178,-440 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3f6cc50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4178,-440 4115,-440 4115,-418 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5775@0" ObjectIDND1="5883@x" ObjectIDZND0="5792@1" Pin0InfoVect0LinkObjId="SW-36435_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3f04f30_0" Pin1InfoVect1LinkObjId="SW-37304_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4178,-440 4115,-440 4115,-418 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3f6f900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4060,-368 4047,-368 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_3f6fb60@0" Pin0InfoVect0LinkObjId="g_3f6fb60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4060,-368 4047,-368 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3f705f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4115,-368 4096,-368 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="5792@x" ObjectIDND1="g_3f70ab0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-36435_0" Pin1InfoVect1LinkObjId="g_3f70ab0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4115,-368 4096,-368 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3f70850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4115,-382 4115,-368 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="5792@0" ObjectIDZND0="0@x" ObjectIDZND1="g_3f70ab0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_3f70ab0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36435_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4115,-382 4115,-368 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3f71330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4115,-368 4115,-353 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="5792@x" ObjectIDND1="0@x" ObjectIDZND0="g_3f70ab0@1" Pin0InfoVect0LinkObjId="g_3f70ab0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-36435_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4115,-368 4115,-353 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3f71e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4178,-353 4178,-383 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3f71590@1" ObjectIDZND0="5883@0" Pin0InfoVect0LinkObjId="SW-37304_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3f71590_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4178,-353 4178,-383 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3f72070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4178,-419 4178,-440 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="5883@1" ObjectIDZND0="5792@x" ObjectIDZND1="5775@0" Pin0InfoVect0LinkObjId="SW-36435_0" Pin0InfoVect1LinkObjId="g_3f04f30_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37304_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4178,-419 4178,-440 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3f78920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4656,-465 4656,-486 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5776@0" ObjectIDZND0="5791@0" Pin0InfoVect0LinkObjId="SW-36433_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4656,-465 4656,-486 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3f7b5d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4601,-538 4588,-538 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_3f7b830@0" Pin0InfoVect0LinkObjId="g_3f7b830_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4601,-538 4588,-538 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3f7eb70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4665,-401 4683,-401 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="5865@x" ObjectIDND1="5864@x" ObjectIDZND0="5866@0" Pin0InfoVect0LinkObjId="SW-37042_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-37041_0" Pin1InfoVect1LinkObjId="SW-37039_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4665,-401 4683,-401 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3f7edd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4719,-401 4736,-401 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="5866@1" ObjectIDZND0="g_3f7f030@0" Pin0InfoVect0LinkObjId="g_3f7f030_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37042_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4719,-401 4736,-401 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3f82570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4665,-414 4665,-401 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="5865@0" ObjectIDZND0="5866@x" ObjectIDZND1="5864@x" Pin0InfoVect0LinkObjId="SW-37042_0" Pin0InfoVect1LinkObjId="SW-37039_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37041_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4665,-414 4665,-401 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3f84820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4665,-401 4665,-385 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="5865@x" ObjectIDND1="5866@x" ObjectIDZND0="5864@1" Pin0InfoVect0LinkObjId="SW-37039_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-37041_0" Pin1InfoVect1LinkObjId="SW-37042_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4665,-401 4665,-385 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3f87330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4665,-465 4665,-450 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5776@0" ObjectIDZND0="5865@1" Pin0InfoVect0LinkObjId="SW-37041_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4665,-465 4665,-450 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3f89e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4559,-401 4577,-401 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="5870@x" ObjectIDND1="5869@x" ObjectIDZND0="5871@0" Pin0InfoVect0LinkObjId="SW-37068_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-37067_0" Pin1InfoVect1LinkObjId="SW-37065_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4559,-401 4577,-401 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3f8a0a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4613,-401 4630,-401 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="5871@1" ObjectIDZND0="g_3f8a300@0" Pin0InfoVect0LinkObjId="g_3f8a300_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37068_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4613,-401 4630,-401 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3f8d840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4559,-414 4559,-401 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="5870@0" ObjectIDZND0="5871@x" ObjectIDZND1="5869@x" Pin0InfoVect0LinkObjId="SW-37068_0" Pin0InfoVect1LinkObjId="SW-37065_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37067_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4559,-414 4559,-401 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3f8faf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4559,-401 4559,-385 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="5870@x" ObjectIDND1="5871@x" ObjectIDZND0="5869@1" Pin0InfoVect0LinkObjId="SW-37065_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-37067_0" Pin1InfoVect1LinkObjId="SW-37068_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4559,-401 4559,-385 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3f92600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4559,-465 4559,-450 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5776@0" ObjectIDZND0="5870@1" Pin0InfoVect0LinkObjId="SW-37067_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4559,-465 4559,-450 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3f95110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4453,-401 4471,-401 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="5832@x" ObjectIDND1="5831@x" ObjectIDZND0="5834@0" Pin0InfoVect0LinkObjId="SW-36767_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-36765_0" Pin1InfoVect1LinkObjId="SW-36764_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4453,-401 4471,-401 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3f95370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4507,-401 4524,-401 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="5834@1" ObjectIDZND0="g_3f955d0@0" Pin0InfoVect0LinkObjId="g_3f955d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36767_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4507,-401 4524,-401 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3f98b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4453,-414 4453,-401 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="5832@0" ObjectIDZND0="5834@x" ObjectIDZND1="5831@x" Pin0InfoVect0LinkObjId="SW-36767_0" Pin0InfoVect1LinkObjId="SW-36764_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36765_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4453,-414 4453,-401 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3f9adc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4453,-401 4453,-385 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="5832@x" ObjectIDND1="5834@x" ObjectIDZND0="5831@1" Pin0InfoVect0LinkObjId="SW-36764_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-36765_0" Pin1InfoVect1LinkObjId="SW-36767_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4453,-401 4453,-385 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3f9d8d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4453,-231 4487,-231 4487,-213 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="5833@x" ObjectIDND1="0@x" ObjectIDZND0="g_3f9dd90@0" Pin0InfoVect0LinkObjId="g_3f9dd90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-36766_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4453,-231 4487,-231 4487,-213 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3f9db30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4453,-231 4453,-140 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_3f9dd90@0" ObjectIDND1="5833@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3f9dd90_0" Pin1InfoVect1LinkObjId="SW-36766_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4453,-231 4453,-140 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3f9eb40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4453,-465 4453,-450 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5776@0" ObjectIDZND0="5832@1" Pin0InfoVect0LinkObjId="SW-36765_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4453,-465 4453,-450 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3fa1650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4347,-401 4365,-401 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="5828@x" ObjectIDND1="5827@x" ObjectIDZND0="5830@0" Pin0InfoVect0LinkObjId="SW-36739_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-36737_0" Pin1InfoVect1LinkObjId="SW-36736_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4347,-401 4365,-401 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3fa18b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4401,-401 4418,-401 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="5830@1" ObjectIDZND0="g_3fa1b10@0" Pin0InfoVect0LinkObjId="g_3fa1b10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36739_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4401,-401 4418,-401 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3fa5050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4347,-414 4347,-401 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="5828@0" ObjectIDZND0="5830@x" ObjectIDZND1="5827@x" Pin0InfoVect0LinkObjId="SW-36739_0" Pin0InfoVect1LinkObjId="SW-36736_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36737_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4347,-414 4347,-401 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3fa7300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4347,-401 4347,-385 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="5828@x" ObjectIDND1="5830@x" ObjectIDZND0="5827@1" Pin0InfoVect0LinkObjId="SW-36736_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-36737_0" Pin1InfoVect1LinkObjId="SW-36739_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4347,-401 4347,-385 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3fa9e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4347,-231 4381,-231 4381,-213 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="5829@x" ObjectIDND1="0@x" ObjectIDZND0="g_3faa2d0@0" Pin0InfoVect0LinkObjId="g_3faa2d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-36738_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4347,-231 4381,-231 4381,-213 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3faa070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4347,-231 4347,-133 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_3faa2d0@0" ObjectIDND1="5829@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3faa2d0_0" Pin1InfoVect1LinkObjId="SW-36738_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4347,-231 4347,-133 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3fab080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4347,-465 4347,-450 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5776@0" ObjectIDZND0="5828@1" Pin0InfoVect0LinkObjId="SW-36737_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4347,-465 4347,-450 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3fadb90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3973,-401 3991,-401 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="5820@x" ObjectIDND1="5819@x" ObjectIDZND0="5822@0" Pin0InfoVect0LinkObjId="SW-36683_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-36681_0" Pin1InfoVect1LinkObjId="SW-36680_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3973,-401 3991,-401 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3faddf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4027,-401 4044,-401 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="5822@1" ObjectIDZND0="g_3fae050@0" Pin0InfoVect0LinkObjId="g_3fae050_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36683_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4027,-401 4044,-401 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3fb1590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3973,-414 3973,-401 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="5820@0" ObjectIDZND0="5822@x" ObjectIDZND1="5819@x" Pin0InfoVect0LinkObjId="SW-36683_0" Pin0InfoVect1LinkObjId="SW-36680_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36681_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3973,-414 3973,-401 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3fb17f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3973,-401 3973,-385 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="5820@x" ObjectIDND1="5822@x" ObjectIDZND0="5819@1" Pin0InfoVect0LinkObjId="SW-36680_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-36681_0" Pin1InfoVect1LinkObjId="SW-36683_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3973,-401 3973,-385 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3fb4300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3973,-231 4007,-231 4007,-213 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="5821@x" ObjectIDZND0="g_3fb47c0@0" Pin0InfoVect0LinkObjId="g_3fb47c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36682_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3973,-231 4007,-231 4007,-213 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3fb4560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3973,-231 3973,-101 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" ObjectIDND0="g_3fb47c0@0" ObjectIDND1="5821@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3fb47c0_0" Pin1InfoVect1LinkObjId="SW-36682_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3973,-231 3973,-101 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3fb5570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3973,-465 3973,-450 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5775@0" ObjectIDZND0="5820@1" Pin0InfoVect0LinkObjId="SW-36681_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3f04f30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3973,-465 3973,-450 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3fb8080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3875,-401 3893,-401 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="5824@x" ObjectIDND1="5823@x" ObjectIDZND0="5826@0" Pin0InfoVect0LinkObjId="SW-36711_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-36709_0" Pin1InfoVect1LinkObjId="SW-36708_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3875,-401 3893,-401 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3fb82e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3929,-401 3946,-401 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="5826@1" ObjectIDZND0="g_3fb8540@0" Pin0InfoVect0LinkObjId="g_3fb8540_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36711_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3929,-401 3946,-401 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3fbba80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3875,-414 3875,-401 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="5824@0" ObjectIDZND0="5826@x" ObjectIDZND1="5823@x" Pin0InfoVect0LinkObjId="SW-36711_0" Pin0InfoVect1LinkObjId="SW-36708_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36709_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3875,-414 3875,-401 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3fbdd30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3875,-401 3875,-385 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="5824@x" ObjectIDND1="5826@x" ObjectIDZND0="5823@1" Pin0InfoVect0LinkObjId="SW-36708_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-36709_0" Pin1InfoVect1LinkObjId="SW-36711_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3875,-401 3875,-385 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3fc0840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3875,-465 3875,-450 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5775@0" ObjectIDZND0="5824@1" Pin0InfoVect0LinkObjId="SW-36709_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3f04f30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3875,-465 3875,-450 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3fc0aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4559,-234 4577,-234 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" EndDevType0="switch" ObjectIDND0="5872@x" ObjectIDND1="15557@x" ObjectIDZND0="5873@0" Pin0InfoVect0LinkObjId="SW-37070_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-37069_0" Pin1InfoVect1LinkObjId="CB-CX_QF.CX_QF_Cb4_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4559,-234 4577,-234 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3fc0d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4613,-234 4630,-234 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="5873@1" ObjectIDZND0="g_3fc0f60@0" Pin0InfoVect0LinkObjId="g_3fc0f60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37070_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4613,-234 4630,-234 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3fc44a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4719,-235 4736,-235 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="5868@1" ObjectIDZND0="g_3fc4700@0" Pin0InfoVect0LinkObjId="g_3fc4700_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37044_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4719,-235 4736,-235 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3fc7c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4665,-235 4683,-235 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" EndDevType0="switch" ObjectIDND0="5867@x" ObjectIDND1="15556@x" ObjectIDZND0="5868@0" Pin0InfoVect0LinkObjId="SW-37044_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-37043_0" Pin1InfoVect1LinkObjId="CB-CX_QF.CX_QF_Cb2_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4665,-235 4683,-235 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3fc7ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4559,-234 4559,-218 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="capacitor" ObjectIDND0="5873@x" ObjectIDND1="5872@x" ObjectIDZND0="15557@0" Pin0InfoVect0LinkObjId="CB-CX_QF.CX_QF_Cb4_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-37070_0" Pin1InfoVect1LinkObjId="SW-37069_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4559,-234 4559,-218 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3fc8100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4665,-235 4665,-218 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="capacitor" ObjectIDND0="5868@x" ObjectIDND1="5867@x" ObjectIDZND0="15556@0" Pin0InfoVect0LinkObjId="CB-CX_QF.CX_QF_Cb2_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-37044_0" Pin1InfoVect1LinkObjId="SW-37043_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4665,-235 4665,-218 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3fcac10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4539,-465 4539,-486 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5776@0" ObjectIDZND0="5793@0" Pin0InfoVect0LinkObjId="SW-36438_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4539,-465 4539,-486 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3fcd8c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4484,-537 4471,-537 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_3fcdb20@0" Pin0InfoVect0LinkObjId="g_3fcdb20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4484,-537 4471,-537 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3fce5b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4539,-537 4520,-537 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="5793@x" ObjectIDND1="g_3fcea70@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-36438_0" Pin1InfoVect1LinkObjId="g_3fcea70_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4539,-537 4520,-537 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3fce810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4539,-522 4539,-537 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="5793@1" ObjectIDZND0="0@x" ObjectIDZND1="g_3fcea70@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_3fcea70_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36438_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4539,-522 4539,-537 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3fcf2f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4539,-537 4539,-553 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="5793@x" ObjectIDND1="0@x" ObjectIDZND0="g_3fcea70@0" Pin0InfoVect0LinkObjId="g_3fcea70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-36438_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4539,-537 4539,-553 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3fcf550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4539,-584 4539,-603 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3fcea70@1" ObjectIDZND0="g_3fcf7b0@0" Pin0InfoVect0LinkObjId="g_3fcf7b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3fcea70_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4539,-584 4539,-603 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3fcfeb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4233,-674 4457,-674 4457,-737 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer" ObjectIDND0="5848@1" ObjectIDZND0="5886@x" Pin0InfoVect0LinkObjId="g_3f60680_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36993_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4233,-674 4457,-674 4457,-737 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3fd0ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4096,-674 4096,-692 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="5852@x" ObjectIDND1="5848@x" ObjectIDND2="g_40d6500@0" ObjectIDZND0="g_3fd0110@0" Pin0InfoVect0LinkObjId="g_3fd0110_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-36998_0" Pin1InfoVect1LinkObjId="SW-36993_0" Pin1InfoVect2LinkObjId="g_40d6500_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4096,-674 4096,-692 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3fd1120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4078,-674 4096,-674 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="5852@1" ObjectIDZND0="g_3fd0110@0" ObjectIDZND1="5848@x" ObjectIDZND2="g_40d6500@0" Pin0InfoVect0LinkObjId="g_3fd0110_0" Pin0InfoVect1LinkObjId="SW-36993_0" Pin0InfoVect2LinkObjId="g_40d6500_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36998_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4078,-674 4096,-674 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3fd1380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4133,-674 4133,-692 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="5848@x" ObjectIDND1="5852@x" ObjectIDND2="g_3fd0110@0" ObjectIDZND0="g_40d6500@0" Pin0InfoVect0LinkObjId="g_40d6500_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-36993_0" Pin1InfoVect1LinkObjId="SW-36998_0" Pin1InfoVect2LinkObjId="g_3fd0110_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4133,-674 4133,-692 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3fd15e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4133,-674 4197,-674 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="5852@x" ObjectIDND1="g_3fd0110@0" ObjectIDND2="g_40d6500@0" ObjectIDZND0="5848@0" Pin0InfoVect0LinkObjId="SW-36993_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-36998_0" Pin1InfoVect1LinkObjId="g_3fd0110_0" Pin1InfoVect2LinkObjId="g_40d6500_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4133,-674 4197,-674 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3fd1840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4096,-674 4133,-674 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="5852@x" ObjectIDND1="g_3fd0110@0" ObjectIDZND0="5848@x" ObjectIDZND1="g_40d6500@0" Pin0InfoVect0LinkObjId="SW-36993_0" Pin0InfoVect1LinkObjId="g_40d6500_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-36998_0" Pin1InfoVect1LinkObjId="g_3fd0110_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4096,-674 4133,-674 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3fd1aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4520,-760 4520,-772 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="5886@x" ObjectIDND1="5844@x" ObjectIDND2="g_407c640@0" ObjectIDZND0="g_3fd4810@0" Pin0InfoVect0LinkObjId="g_3fd4810_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3f60680_0" Pin1InfoVect1LinkObjId="SW-36987_0" Pin1InfoVect2LinkObjId="g_407c640_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4520,-760 4520,-772 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3fd45b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4583,-812 4583,-824 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="5844@0" ObjectIDZND0="g_3fd55c0@0" Pin0InfoVect0LinkObjId="g_3fd55c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36987_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4583,-812 4583,-824 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3fd6010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4546,-760 4546,-772 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="5844@x" ObjectIDND1="5886@x" ObjectIDND2="g_3fd4810@0" ObjectIDZND0="g_407c640@0" Pin0InfoVect0LinkObjId="g_407c640_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-36987_0" Pin1InfoVect1LinkObjId="g_3f60680_0" Pin1InfoVect2LinkObjId="g_3fd4810_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4546,-760 4546,-772 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3fd6270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4546,-760 4583,-760 4583,-773 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_407c640@0" ObjectIDND1="5886@x" ObjectIDND2="g_3fd4810@0" ObjectIDZND0="5844@1" Pin0InfoVect0LinkObjId="SW-36987_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_407c640_0" Pin1InfoVect1LinkObjId="g_3f60680_0" Pin1InfoVect2LinkObjId="g_3fd4810_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4546,-760 4583,-760 4583,-773 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3fd64d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4419,-760 4520,-760 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="5886@x" ObjectIDZND0="g_3fd4810@0" ObjectIDZND1="5844@x" ObjectIDZND2="g_407c640@0" Pin0InfoVect0LinkObjId="g_3fd4810_0" Pin0InfoVect1LinkObjId="SW-36987_0" Pin0InfoVect2LinkObjId="g_407c640_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3f60680_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4419,-760 4520,-760 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3fd6730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4520,-760 4546,-760 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="5886@x" ObjectIDND1="g_3fd4810@0" ObjectIDZND0="5844@x" ObjectIDZND1="g_407c640@0" Pin0InfoVect0LinkObjId="SW-36987_0" Pin0InfoVect1LinkObjId="g_407c640_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3f60680_0" Pin1InfoVect1LinkObjId="g_3fd4810_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4520,-760 4546,-760 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4022020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4685,-864 4702,-864 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="5799@1" ObjectIDZND0="5797@1" Pin0InfoVect0LinkObjId="SW-36443_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36445_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4685,-864 4702,-864 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4022280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4729,-864 4746,-864 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="5797@0" ObjectIDZND0="5798@0" Pin0InfoVect0LinkObjId="SW-36444_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36443_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4729,-864 4746,-864 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4024f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4835,-779 4850,-779 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5779@0" ObjectIDZND0="5783@0" Pin0InfoVect0LinkObjId="SW-36402_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_407b5c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4835,-779 4850,-779 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4027c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4851,-688 4835,-688 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="5784@0" ObjectIDZND0="5780@0" Pin0InfoVect0LinkObjId="g_4031720_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36403_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4851,-688 4835,-688 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4029ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4886,-779 4903,-779 4903,-749 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="5783@1" ObjectIDZND0="5818@1" Pin0InfoVect0LinkObjId="SW-36660_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36402_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4886,-779 4903,-779 4903,-749 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_402a150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4903,-722 4903,-688 4887,-688 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="5818@0" ObjectIDZND0="5784@1" Pin0InfoVect0LinkObjId="SW-36403_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36660_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4903,-722 4903,-688 4887,-688 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4031720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4785,-663 4835,-663 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="5846@1" ObjectIDZND0="5780@0" Pin0InfoVect0LinkObjId="g_4027c40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36990_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4785,-663 4835,-663 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4031980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4688,-663 4705,-663 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="5847@1" ObjectIDZND0="5845@1" Pin0InfoVect0LinkObjId="SW-36989_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36991_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4688,-663 4705,-663 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4031be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4732,-663 4749,-663 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="5845@0" ObjectIDZND0="5846@0" Pin0InfoVect0LinkObjId="SW-36990_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36989_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4732,-663 4749,-663 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_40348f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4835,-825 4864,-825 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5779@0" ObjectIDZND0="5788@0" Pin0InfoVect0LinkObjId="SW-36426_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_407b5c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4835,-825 4864,-825 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_40353d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4900,-825 4919,-825 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="5788@1" ObjectIDZND0="g_4034b50@1" Pin0InfoVect0LinkObjId="g_4034b50_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36426_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4900,-825 4919,-825 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4035630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4950,-825 4976,-825 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_4034b50@0" ObjectIDZND0="g_4035890@0" Pin0InfoVect0LinkObjId="g_4035890_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4034b50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4950,-825 4976,-825 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_403d340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4835,-908 4853,-908 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5779@0" ObjectIDZND0="5810@0" Pin0InfoVect0LinkObjId="SW-36578_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_407b5c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4835,-908 4853,-908 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_403d5a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4889,-908 4906,-908 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="5810@1" ObjectIDZND0="5809@1" Pin0InfoVect0LinkObjId="SW-36577_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36578_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4889,-908 4906,-908 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_403d800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4933,-908 4950,-908 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="5809@0" ObjectIDZND0="5811@0" Pin0InfoVect0LinkObjId="SW-36579_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36577_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4933,-908 4950,-908 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_403da60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4986,-908 5009,-908 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="5811@1" ObjectIDZND0="g_403df20@0" ObjectIDZND1="g_403f190@0" ObjectIDZND2="15390@x" Pin0InfoVect0LinkObjId="g_403df20_0" Pin0InfoVect1LinkObjId="g_403f190_0" Pin0InfoVect2LinkObjId="EC-CX_QF.CX_QF_353Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36579_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4986,-908 5009,-908 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_403dcc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5009,-908 5049,-908 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="load" ObjectIDND0="5811@x" ObjectIDND1="g_403df20@0" ObjectIDND2="g_403f190@0" ObjectIDZND0="15390@0" Pin0InfoVect0LinkObjId="EC-CX_QF.CX_QF_353Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-36579_0" Pin1InfoVect1LinkObjId="g_403df20_0" Pin1InfoVect2LinkObjId="g_403f190_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5009,-908 5049,-908 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_403ecd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5009,-908 5009,-937 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="5811@x" ObjectIDND1="15390@x" ObjectIDZND0="g_403df20@0" ObjectIDZND1="g_403f190@0" Pin0InfoVect0LinkObjId="g_403df20_0" Pin0InfoVect1LinkObjId="g_403f190_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-36579_0" Pin1InfoVect1LinkObjId="EC-CX_QF.CX_QF_353Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5009,-908 5009,-937 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_403ef30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5009,-937 5026,-937 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="5811@x" ObjectIDND1="15390@x" ObjectIDND2="g_403f190@0" ObjectIDZND0="g_403df20@0" Pin0InfoVect0LinkObjId="g_403df20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-36579_0" Pin1InfoVect1LinkObjId="EC-CX_QF.CX_QF_353Ld_0" Pin1InfoVect2LinkObjId="g_403f190_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5009,-937 5026,-937 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_403fa10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5009,-937 5009,-963 5028,-963 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="load" EndDevType0="lightningRod" ObjectIDND0="g_403df20@0" ObjectIDND1="5811@x" ObjectIDND2="15390@x" ObjectIDZND0="g_403f190@1" Pin0InfoVect0LinkObjId="g_403f190_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_403df20_0" Pin1InfoVect1LinkObjId="SW-36579_0" Pin1InfoVect2LinkObjId="EC-CX_QF.CX_QF_353Ld_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5009,-937 5009,-963 5028,-963 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_403fc70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5059,-963 5080,-963 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_403f190@0" ObjectIDZND0="12120@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_403f190_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5059,-963 5080,-963 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4047240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4835,-1151 4854,-1151 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5779@0" ObjectIDZND0="5804@0" Pin0InfoVect0LinkObjId="SW-36524_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_407b5c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4835,-1151 4854,-1151 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_40474a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4890,-1151 4907,-1151 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="5804@1" ObjectIDZND0="5803@1" Pin0InfoVect0LinkObjId="SW-36523_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36524_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4890,-1151 4907,-1151 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4047700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4934,-1151 4951,-1151 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="5803@0" ObjectIDZND0="5805@0" Pin0InfoVect0LinkObjId="SW-36525_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36523_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4934,-1151 4951,-1151 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4047960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5010,-1151 5010,-1180 5027,-1180 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="5805@x" ObjectIDND1="15388@x" ObjectIDZND0="g_4048080@0" Pin0InfoVect0LinkObjId="g_4048080_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-36525_0" Pin1InfoVect1LinkObjId="EC-CX_QF.CX_QF_351Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5010,-1151 5010,-1180 5027,-1180 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4047bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4987,-1151 5010,-1151 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="5805@1" ObjectIDZND0="g_4048080@0" ObjectIDZND1="15388@x" Pin0InfoVect0LinkObjId="g_4048080_0" Pin0InfoVect1LinkObjId="EC-CX_QF.CX_QF_351Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36525_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4987,-1151 5010,-1151 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4047e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5010,-1151 5060,-1151 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="5805@x" ObjectIDND1="g_4048080@0" ObjectIDZND0="15388@0" Pin0InfoVect0LinkObjId="EC-CX_QF.CX_QF_351Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-36525_0" Pin1InfoVect1LinkObjId="g_4048080_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5010,-1151 5060,-1151 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_404b8e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4835,-1077 4855,-1077 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5779@0" ObjectIDZND0="5786@0" Pin0InfoVect0LinkObjId="SW-36421_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_407b5c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4835,-1077 4855,-1077 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_404bb40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4946,-1077 4946,-1106 4963,-1106 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="5786@x" ObjectIDND1="g_40d3190@0" ObjectIDZND0="g_404bda0@0" Pin0InfoVect0LinkObjId="g_404bda0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-36421_0" Pin1InfoVect1LinkObjId="g_40d3190_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4946,-1077 4946,-1106 4963,-1106 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_404cb50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4946,-1077 4891,-1077 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_404bda0@0" ObjectIDND1="g_40d3190@0" ObjectIDZND0="5786@1" Pin0InfoVect0LinkObjId="SW-36421_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_404bda0_0" Pin1InfoVect1LinkObjId="g_40d3190_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4946,-1077 4891,-1077 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4054120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4835,-999 4851,-999 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5779@0" ObjectIDZND0="5807@0" Pin0InfoVect0LinkObjId="SW-36551_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_407b5c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4835,-999 4851,-999 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4054380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4887,-999 4904,-999 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="5807@1" ObjectIDZND0="5806@1" Pin0InfoVect0LinkObjId="SW-36550_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36551_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4887,-999 4904,-999 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_40545e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5007,-999 5007,-1028 5024,-1028 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="5808@x" ObjectIDND1="15389@x" ObjectIDZND0="g_4054aa0@0" Pin0InfoVect0LinkObjId="g_4054aa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-36552_0" Pin1InfoVect1LinkObjId="EC-CX_QF.CX_QF_352Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5007,-999 5007,-1028 5024,-1028 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4054840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5007,-999 5047,-999 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_4054aa0@0" ObjectIDND1="5808@x" ObjectIDZND0="15389@0" Pin0InfoVect0LinkObjId="EC-CX_QF.CX_QF_352Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_4054aa0_0" Pin1InfoVect1LinkObjId="SW-36552_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5007,-999 5047,-999 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_405cbc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4835,-317 4850,-317 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5780@0" ObjectIDZND0="5875@0" Pin0InfoVect0LinkObjId="SW-37094_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4027c40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4835,-317 4850,-317 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_405ce20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4886,-317 4903,-317 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="5875@1" ObjectIDZND0="5874@1" Pin0InfoVect0LinkObjId="SW-37092_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37094_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4886,-317 4903,-317 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_405d080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4930,-317 4947,-317 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="5874@0" ObjectIDZND0="5876@0" Pin0InfoVect0LinkObjId="SW-37095_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37092_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4930,-317 4947,-317 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_405d2e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4983,-317 5081,-317 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="5876@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37095_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4983,-317 5081,-317 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_40648b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4835,-629 4849,-629 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5780@0" ObjectIDZND0="5813@0" Pin0InfoVect0LinkObjId="SW-36605_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4027c40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4835,-629 4849,-629 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4064b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4885,-629 4902,-629 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="5813@1" ObjectIDZND0="5812@1" Pin0InfoVect0LinkObjId="SW-36604_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36605_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4885,-629 4902,-629 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4064d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4929,-629 4946,-629 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="5812@0" ObjectIDZND0="5814@0" Pin0InfoVect0LinkObjId="SW-36606_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36604_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4929,-629 4946,-629 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4064fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5005,-629 5005,-658 5022,-658 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="5814@x" ObjectIDND1="15391@x" ObjectIDZND0="g_4077000@0" Pin0InfoVect0LinkObjId="g_4077000_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-36606_0" Pin1InfoVect1LinkObjId="EC-CX_QF.CX_QF_354Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5005,-629 5005,-658 5022,-658 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4065230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4982,-629 5005,-629 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="5814@1" ObjectIDZND0="g_4077000@0" ObjectIDZND1="15391@x" Pin0InfoVect0LinkObjId="g_4077000_0" Pin0InfoVect1LinkObjId="EC-CX_QF.CX_QF_354Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36606_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4982,-629 5005,-629 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4065490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5005,-629 5049,-629 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="5814@x" ObjectIDND1="g_4077000@0" ObjectIDZND0="15391@0" Pin0InfoVect0LinkObjId="EC-CX_QF.CX_QF_354Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-36606_0" Pin1InfoVect1LinkObjId="g_4077000_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5005,-629 5049,-629 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_406ca60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4835,-543 4848,-543 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5780@0" ObjectIDZND0="5816@0" Pin0InfoVect0LinkObjId="SW-36632_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4027c40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4835,-543 4848,-543 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_406ccc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4884,-543 4901,-543 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="5816@1" ObjectIDZND0="5815@1" Pin0InfoVect0LinkObjId="SW-36631_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36632_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4884,-543 4901,-543 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_406cf20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4928,-543 4945,-543 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="5815@0" ObjectIDZND0="5817@0" Pin0InfoVect0LinkObjId="SW-36633_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36631_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4928,-543 4945,-543 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_406d180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5004,-543 5004,-572 5021,-572 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="5817@x" ObjectIDND1="15392@x" ObjectIDZND0="g_406d8a0@0" Pin0InfoVect0LinkObjId="g_406d8a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-36633_0" Pin1InfoVect1LinkObjId="EC-CX_QF.CX_QF_355Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5004,-543 5004,-572 5021,-572 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_406d3e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4981,-543 5004,-543 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="5817@1" ObjectIDZND0="g_406d8a0@0" ObjectIDZND1="15392@x" Pin0InfoVect0LinkObjId="g_406d8a0_0" Pin0InfoVect1LinkObjId="EC-CX_QF.CX_QF_355Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36633_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4981,-543 5004,-543 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_406d640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5004,-543 5044,-543 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="5817@x" ObjectIDND1="g_406d8a0@0" ObjectIDZND0="15392@0" Pin0InfoVect0LinkObjId="EC-CX_QF.CX_QF_355Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-36633_0" Pin1InfoVect1LinkObjId="g_406d8a0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5004,-543 5044,-543 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4071100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4835,-461 4862,-461 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5780@0" ObjectIDZND0="5789@0" Pin0InfoVect0LinkObjId="SW-36429_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4027c40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4835,-461 4862,-461 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4071be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4898,-461 4917,-461 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="5789@1" ObjectIDZND0="g_4071360@1" Pin0InfoVect0LinkObjId="g_4071360_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36429_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4898,-461 4917,-461 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4071e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4948,-461 4974,-461 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_4071360@0" ObjectIDZND0="g_40720a0@0" Pin0InfoVect0LinkObjId="g_40720a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4071360_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4948,-461 4974,-461 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4075290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4835,-385 4853,-385 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5780@0" ObjectIDZND0="5787@0" Pin0InfoVect0LinkObjId="SW-36424_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4027c40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4835,-385 4853,-385 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_40754f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4944,-385 4944,-414 4961,-414 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="5787@x" ObjectIDND1="g_40d40e0@0" ObjectIDZND0="g_4075750@0" Pin0InfoVect0LinkObjId="g_4075750_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-36424_0" Pin1InfoVect1LinkObjId="g_40d40e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4944,-385 4944,-414 4961,-414 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4076500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4944,-385 4889,-385 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_4075750@0" ObjectIDND1="g_40d40e0@0" ObjectIDZND0="5787@1" Pin0InfoVect0LinkObjId="SW-36424_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_4075750_0" Pin1InfoVect1LinkObjId="g_40d40e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4944,-385 4889,-385 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_407b5c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4783,-864 4835,-864 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="5798@1" ObjectIDZND0="5779@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36444_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4783,-864 4835,-864 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_407b810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4042,-674 3912,-674 3912,-736 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer" ObjectIDND0="5852@0" ObjectIDZND0="5885@x" Pin0InfoVect0LinkObjId="g_4018ad0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36998_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4042,-674 3912,-674 3912,-736 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_407df60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3755,-267 3755,-277 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="5840@1" ObjectIDZND0="g_407d210@1" Pin0InfoVect0LinkObjId="g_407d210_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36818_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3755,-267 3755,-277 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_407e1c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3755,-330 3755,-339 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="lightningRod" ObjectIDND0="g_407d210@0" ObjectIDZND0="5838@x" ObjectIDZND1="g_401c8b0@0" Pin0InfoVect0LinkObjId="SW-36816_0" Pin0InfoVect1LinkObjId="g_401c8b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_407d210_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3755,-330 3755,-339 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_407f170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3973,-231 3973,-243 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3fb47c0@0" ObjectIDZND0="5821@0" Pin0InfoVect0LinkObjId="SW-36682_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3fb47c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3973,-231 3973,-243 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_407f3d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3875,-280 3875,-293 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="5825@1" ObjectIDZND0="g_407e420@1" Pin0InfoVect0LinkObjId="g_407e420_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36710_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3875,-280 3875,-293 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_407f630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3875,-346 3875,-358 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_407e420@0" ObjectIDZND0="5823@0" Pin0InfoVect0LinkObjId="SW-36708_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_407e420_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3875,-346 3875,-358 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40805e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3973,-279 3973,-293 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="5821@1" ObjectIDZND0="g_407f890@1" Pin0InfoVect0LinkObjId="g_407f890_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36682_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3973,-279 3973,-293 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4080840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3973,-346 3973,-358 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_407f890@0" ObjectIDZND0="5819@0" Pin0InfoVect0LinkObjId="SW-36680_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_407f890_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3973,-346 3973,-358 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40817f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4347,-231 4347,-244 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_3faa2d0@0" ObjectIDND1="0@x" ObjectIDZND0="5829@0" Pin0InfoVect0LinkObjId="SW-36738_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3faa2d0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4347,-231 4347,-244 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4081a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4665,-235 4665,-246 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" EndDevType0="switch" ObjectIDND0="5868@x" ObjectIDND1="15556@x" ObjectIDZND0="5867@0" Pin0InfoVect0LinkObjId="SW-37043_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-37044_0" Pin1InfoVect1LinkObjId="CB-CX_QF.CX_QF_Cb2_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4665,-235 4665,-246 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4081cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4453,-231 4453,-246 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_3f9dd90@0" ObjectIDND1="0@x" ObjectIDZND0="5833@0" Pin0InfoVect0LinkObjId="SW-36766_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3f9dd90_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4453,-231 4453,-246 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4081f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4559,-234 4559,-246 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" EndDevType0="switch" ObjectIDND0="5873@x" ObjectIDND1="15557@x" ObjectIDZND0="5872@0" Pin0InfoVect0LinkObjId="SW-37069_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-37070_0" Pin1InfoVect1LinkObjId="CB-CX_QF.CX_QF_Cb4_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4559,-234 4559,-246 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4082170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4347,-280 4347,-293 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="5829@1" ObjectIDZND0="g_4080aa0@1" Pin0InfoVect0LinkObjId="g_4080aa0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36738_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4347,-280 4347,-293 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40823d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4347,-346 4347,-358 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_4080aa0@0" ObjectIDZND0="5827@0" Pin0InfoVect0LinkObjId="SW-36736_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4080aa0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4347,-346 4347,-358 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4083380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4453,-282 4453,-293 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="5833@1" ObjectIDZND0="g_4082630@1" Pin0InfoVect0LinkObjId="g_4082630_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36766_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4453,-282 4453,-293 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40835e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4453,-346 4453,-358 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_4082630@0" ObjectIDZND0="5831@0" Pin0InfoVect0LinkObjId="SW-36764_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4082630_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4453,-346 4453,-358 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4084590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4559,-282 4559,-294 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="5872@1" ObjectIDZND0="g_4083840@1" Pin0InfoVect0LinkObjId="g_4083840_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37069_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4559,-282 4559,-294 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40847f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4559,-347 4559,-358 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_4083840@0" ObjectIDZND0="5869@0" Pin0InfoVect0LinkObjId="SW-37065_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4083840_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4559,-347 4559,-358 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40857a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4665,-282 4665,-294 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="5867@1" ObjectIDZND0="g_4084a50@1" Pin0InfoVect0LinkObjId="g_4084a50_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37043_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4665,-282 4665,-294 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4085a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4665,-347 4665,-358 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_4084a50@0" ObjectIDZND0="5864@0" Pin0InfoVect0LinkObjId="SW-37039_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4084a50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4665,-347 4665,-358 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4085c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4931,-999 4948,-999 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="5806@0" ObjectIDZND0="5808@0" Pin0InfoVect0LinkObjId="SW-36552_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36550_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4931,-999 4948,-999 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4085ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4984,-999 5007,-999 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="5808@1" ObjectIDZND0="g_4054aa0@0" ObjectIDZND1="15389@x" Pin0InfoVect0LinkObjId="g_4054aa0_0" Pin0InfoVect1LinkObjId="EC-CX_QF.CX_QF_352Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36552_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4984,-999 5007,-999 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_408a250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3872,-778 3872,-808 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="5885@1" ObjectIDZND0="5795@x" ObjectIDZND1="5794@x" Pin0InfoVect0LinkObjId="SW-36441_0" Pin0InfoVect1LinkObjId="SW-36440_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4018ad0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3872,-778 3872,-808 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_408a440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4418,-778 4418,-808 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="5886@1" ObjectIDZND0="5843@x" ObjectIDZND1="5842@x" Pin0InfoVect0LinkObjId="SW-36986_0" Pin0InfoVect1LinkObjId="SW-36985_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3f60680_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4418,-778 4418,-808 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_408a630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4418,-808 4418,-841 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5886@x" ObjectIDND1="5843@x" ObjectIDZND0="5842@0" Pin0InfoVect0LinkObjId="SW-36985_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3f60680_0" Pin1InfoVect1LinkObjId="SW-36986_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4418,-808 4418,-841 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_408a820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3872,-840 3872,-808 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer" EndDevType1="switch" ObjectIDND0="5794@0" ObjectIDZND0="5885@x" ObjectIDZND1="5795@x" Pin0InfoVect0LinkObjId="g_4018ad0_0" Pin0InfoVect1LinkObjId="SW-36441_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36440_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3872,-840 3872,-808 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_408aa50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4417,-955 4417,-978 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="5854@1" ObjectIDZND0="5855@x" ObjectIDZND1="5853@x" Pin0InfoVect0LinkObjId="SW-37005_0" Pin0InfoVect1LinkObjId="SW-37000_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37004_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4417,-955 4417,-978 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_408ac80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3872,-955 3872,-978 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="5877@1" ObjectIDZND0="5878@x" ObjectIDZND1="5882@x" Pin0InfoVect0LinkObjId="SW-37120_0" Pin0InfoVect1LinkObjId="SW-37124_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37119_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3872,-955 3872,-978 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_408aeb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4417,-978 4417,-999 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="5854@x" ObjectIDND1="5855@x" ObjectIDZND0="5853@0" Pin0InfoVect0LinkObjId="SW-37000_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-37004_0" Pin1InfoVect1LinkObjId="SW-37005_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4417,-978 4417,-999 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_408b0e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3872,-978 3872,-1002 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="5877@x" ObjectIDND1="5878@x" ObjectIDZND0="5882@0" Pin0InfoVect0LinkObjId="SW-37124_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-37119_0" Pin1InfoVect1LinkObjId="SW-37120_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3872,-978 3872,-1002 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_408b310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3872,-1029 3872,-1049 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="5882@1" ObjectIDZND0="5880@x" ObjectIDZND1="5879@x" Pin0InfoVect0LinkObjId="SW-37122_0" Pin0InfoVect1LinkObjId="SW-37121_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37124_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3872,-1029 3872,-1049 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_408b540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4417,-1026 4417,-1049 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="5853@1" ObjectIDZND0="5857@x" ObjectIDZND1="5856@x" Pin0InfoVect0LinkObjId="SW-37007_0" Pin0InfoVect1LinkObjId="SW-37006_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37000_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4417,-1026 4417,-1049 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40908f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3611,-394 3630,-394 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5835@x" ObjectIDND1="5836@x" ObjectIDZND0="10680@0" Pin0InfoVect0LinkObjId="SW-57360_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-36792_0" Pin1InfoVect1LinkObjId="SW-36793_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3611,-394 3630,-394 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4090b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3666,-394 3683,-394 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="10680@1" ObjectIDZND0="g_4090db0@0" Pin0InfoVect0LinkObjId="g_4090db0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57360_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3666,-394 3683,-394 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4091840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3611,-380 3611,-394 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="5835@1" ObjectIDZND0="10680@x" ObjectIDZND1="5836@x" Pin0InfoVect0LinkObjId="SW-57360_0" Pin0InfoVect1LinkObjId="SW-36793_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36792_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3611,-380 3611,-394 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4091aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3611,-394 3611,-409 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5835@x" ObjectIDND1="10680@x" ObjectIDZND0="5836@0" Pin0InfoVect0LinkObjId="SW-36793_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-36792_0" Pin1InfoVect1LinkObjId="SW-57360_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3611,-394 3611,-409 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4091d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3611,-339 3638,-339 3638,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="5835@x" ObjectIDND1="g_409f9d0@0" ObjectIDZND0="g_40921c0@0" Pin0InfoVect0LinkObjId="g_40921c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-36792_0" Pin1InfoVect1LinkObjId="g_409f9d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3611,-339 3638,-339 3638,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4091f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3611,-339 3611,-353 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="breaker" ObjectIDND0="g_40921c0@0" ObjectIDND1="g_409f9d0@0" ObjectIDZND0="5835@0" Pin0InfoVect0LinkObjId="SW-36792_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_40921c0_0" Pin1InfoVect1LinkObjId="g_409f9d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3611,-339 3611,-353 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4092f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3611,-217 3629,-217 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="capacitor" EndDevType0="switch" ObjectIDND0="5837@x" ObjectIDND1="g_40945e0@0" ObjectIDND2="15555@x" ObjectIDZND0="10681@0" Pin0InfoVect0LinkObjId="SW-57361_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-36794_0" Pin1InfoVect1LinkObjId="g_40945e0_0" Pin1InfoVect2LinkObjId="CB-CX_QF.CX_QF_Cb1_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3611,-217 3629,-217 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40931d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3665,-217 3682,-217 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="10681@1" ObjectIDZND0="g_4093430@0" Pin0InfoVect0LinkObjId="g_4093430_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57361_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3665,-217 3682,-217 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4093ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3611,-231 3611,-217 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="capacitor" ObjectIDND0="5837@0" ObjectIDZND0="10681@x" ObjectIDZND1="g_40945e0@0" ObjectIDZND2="15555@x" Pin0InfoVect0LinkObjId="SW-57361_0" Pin0InfoVect1LinkObjId="g_40945e0_0" Pin0InfoVect2LinkObjId="CB-CX_QF.CX_QF_Cb1_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36794_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3611,-231 3611,-217 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4094120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3611,-217 3611,-192 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="capacitor" ObjectIDND0="5837@x" ObjectIDND1="10681@x" ObjectIDND2="g_40945e0@0" ObjectIDZND0="15555@0" Pin0InfoVect0LinkObjId="CB-CX_QF.CX_QF_Cb1_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-36794_0" Pin1InfoVect1LinkObjId="SW-57361_0" Pin1InfoVect2LinkObjId="g_40945e0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3611,-217 3611,-192 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4094380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3611,-217 3580,-217 3580,-202 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="capacitor" EndDevType0="lightningRod" ObjectIDND0="5837@x" ObjectIDND1="10681@x" ObjectIDND2="15555@x" ObjectIDZND0="g_40945e0@0" Pin0InfoVect0LinkObjId="g_40945e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-36794_0" Pin1InfoVect1LinkObjId="SW-57361_0" Pin1InfoVect2LinkObjId="CB-CX_QF.CX_QF_Cb1_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3611,-217 3580,-217 3580,-202 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40a0720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3611,-267 3611,-277 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="5837@1" ObjectIDZND0="g_409f9d0@1" Pin0InfoVect0LinkObjId="g_409f9d0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36794_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3611,-267 3611,-277 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40a0980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3611,-330 3611,-339 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="lightningRod" ObjectIDND0="g_409f9d0@0" ObjectIDZND0="5835@x" ObjectIDZND1="g_40921c0@0" Pin0InfoVect0LinkObjId="SW-36792_0" Pin0InfoVect1LinkObjId="g_40921c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_409f9d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3611,-330 3611,-339 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40a0be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3611,-465 3611,-445 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5775@0" ObjectIDZND0="5836@1" Pin0InfoVect0LinkObjId="SW-36793_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3f04f30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3611,-465 3611,-445 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_40a9b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4247,-181 4226,-181 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="breaker" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4247,-181 4226,-181 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_40a9d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4199,-181 4178,-181 4178,-207 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4199,-181 4178,-181 4178,-207 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40a9f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4115,-298 4115,-322 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_3f70ab0@0" Pin0InfoVect0LinkObjId="g_3f70ab0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4115,-298 4115,-322 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_40cf2c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4652,-663 4631,-663 4631,-738 4475,-738 4473,-737 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer" ObjectIDND0="5847@0" ObjectIDZND0="5886@0" Pin0InfoVect0LinkObjId="g_3f60680_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36991_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4652,-663 4631,-663 4631,-738 4475,-738 4473,-737 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_40cf4b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3927,-737 3957,-737 3957,-663 4604,-663 4604,-864 4649,-864 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="switch" ObjectIDND0="5885@0" ObjectIDZND0="5799@0" Pin0InfoVect0LinkObjId="SW-36445_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4018ad0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3927,-737 3957,-737 3957,-663 4604,-663 4604,-864 4649,-864 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_40d13d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5120,-963 5146,-963 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="12120@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_403fc70_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5120,-963 5146,-963 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_40d1630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5173,-963 5194,-963 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" ObjectIDND0="0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5173,-963 5194,-963 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_40d3c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5035,-1077 5005,-1077 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_40dcb00@0" ObjectIDZND0="g_40d3190@1" Pin0InfoVect0LinkObjId="g_40d3190_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_40dcb00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5035,-1077 5005,-1077 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_40d3e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4973,-1077 4946,-1077 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_40d3190@0" ObjectIDZND0="5786@x" ObjectIDZND1="g_404bda0@0" Pin0InfoVect0LinkObjId="SW-36421_0" Pin0InfoVect1LinkObjId="g_404bda0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_40d3190_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4973,-1077 4946,-1077 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_40d4b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5033,-385 5010,-385 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_4076760@0" ObjectIDZND0="g_40d40e0@1" Pin0InfoVect0LinkObjId="g_40d40e0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4076760_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5033,-385 5010,-385 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_40d4dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4978,-385 4944,-385 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_40d40e0@0" ObjectIDZND0="5787@x" ObjectIDZND1="g_4075750@0" Pin0InfoVect0LinkObjId="SW-36424_0" Pin0InfoVect1LinkObjId="g_4075750_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_40d40e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4978,-385 4944,-385 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40d5de0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3914,-211 3914,-223 3875,-223 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_40d5030@0" ObjectIDZND0="5825@x" Pin0InfoVect0LinkObjId="SW-36710_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_40d5030_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3914,-211 3914,-223 3875,-223 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40d6040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3875,-244 3875,-223 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="5825@0" ObjectIDZND0="g_40d5030@0" Pin0InfoVect0LinkObjId="g_40d5030_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36710_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3875,-244 3875,-223 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40d62a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3875,-223 3875,-101 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" ObjectIDND0="5825@x" ObjectIDND1="g_40d5030@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-36710_0" Pin1InfoVect1LinkObjId="g_40d5030_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3875,-223 3875,-101 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40de4b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4178,-322 4178,-297 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_3f71590@0" ObjectIDZND0="12119@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3f71590_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4178,-322 4178,-297 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_40de710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4178,-255 4178,-234 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="12119@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_40de4b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4178,-255 4178,-234 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40df720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3714,-538 3747,-538 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="5790@x" ObjectIDND2="g_40d2220@0" ObjectIDZND0="g_40de970@0" Pin0InfoVect0LinkObjId="g_40de970_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-36430_0" Pin1InfoVect2LinkObjId="g_40d2220_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3714,-538 3747,-538 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40df980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3714,-609 3714,-586 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3f66ff0@0" ObjectIDZND0="g_40d2220@0" Pin0InfoVect0LinkObjId="g_40d2220_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3f66ff0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3714,-609 3714,-586 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40dfbe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3714,-554 3714,-538 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_40d2220@1" ObjectIDZND0="0@x" ObjectIDZND1="5790@x" ObjectIDZND2="g_40de970@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-36430_0" Pin0InfoVect2LinkObjId="g_40de970_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_40d2220_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3714,-554 3714,-538 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40dfe40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4656,-592 4656,-605 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_40d2950@0" ObjectIDZND0="g_40dc510@0" Pin0InfoVect0LinkObjId="g_40dc510_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_40d2950_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4656,-592 4656,-605 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40e00a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4637,-538 4656,-538 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="5791@x" ObjectIDZND1="g_40e07c0@0" ObjectIDZND2="g_40d2950@0" Pin0InfoVect0LinkObjId="SW-36433_0" Pin0InfoVect1LinkObjId="g_40e07c0_0" Pin0InfoVect2LinkObjId="g_40d2950_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4637,-538 4656,-538 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40e0300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4656,-522 4656,-538 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="5791@1" ObjectIDZND0="0@x" ObjectIDZND1="g_40e07c0@0" ObjectIDZND2="g_40d2950@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_40e07c0_0" Pin0InfoVect2LinkObjId="g_40d2950_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36433_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4656,-522 4656,-538 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40e0560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4656,-538 4656,-560 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="5791@x" ObjectIDND1="0@x" ObjectIDND2="g_40e07c0@0" ObjectIDZND0="g_40d2950@1" Pin0InfoVect0LinkObjId="g_40d2950_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-36433_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_40e07c0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4656,-538 4656,-560 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40e1570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4656,-538 4688,-538 4688,-574 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="5791@x" ObjectIDND1="0@x" ObjectIDND2="g_40d2950@0" ObjectIDZND0="g_40e07c0@0" Pin0InfoVect0LinkObjId="g_40e07c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-36433_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_40d2950_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4656,-538 4688,-538 4688,-574 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_40e2eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4230,-897 4403,-897 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="5862@1" ObjectIDZND0="5777@0" Pin0InfoVect0LinkObjId="g_40e3110_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37032_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4230,-897 4403,-897 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_40e3110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4417,-919 4417,-897 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="5854@0" ObjectIDZND0="5777@0" Pin0InfoVect0LinkObjId="g_40e2eb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37004_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4417,-919 4417,-897 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_40e3370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4418,-876 4418,-897 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="5842@1" ObjectIDZND0="5777@0" Pin0InfoVect0LinkObjId="g_40e2eb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36985_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4418,-876 4418,-897 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_40e35d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3872,-919 3872,-897 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="5877@0" ObjectIDZND0="5777@0" Pin0InfoVect0LinkObjId="g_40e2eb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37119_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3872,-919 3872,-897 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_40e3830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3872,-876 3872,-897 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="5794@1" ObjectIDZND0="5777@0" Pin0InfoVect0LinkObjId="g_40e2eb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36440_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3872,-876 3872,-897 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_40e3a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4381,-1149 4417,-1149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="powerLine" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_40876c0@0" ObjectIDND1="g_3fe68c0@0" ObjectIDZND0="11498@1" ObjectIDZND1="5858@x" ObjectIDZND2="5856@x" Pin0InfoVect0LinkObjId="g_40e3cf0_1" Pin0InfoVect1LinkObjId="SW-37008_0" Pin0InfoVect2LinkObjId="SW-37006_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_40876c0_0" Pin1InfoVect1LinkObjId="g_3fe68c0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4381,-1149 4417,-1149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_40e3cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4342,-1149 4381,-1149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="powerLine" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_40876c0@0" ObjectIDZND0="11498@1" ObjectIDZND1="5858@x" ObjectIDZND2="5856@x" Pin0InfoVect0LinkObjId="g_40e3a90_1" Pin0InfoVect1LinkObjId="SW-37008_0" Pin0InfoVect2LinkObjId="SW-37006_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_40876c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4342,-1149 4381,-1149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_40e3f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4381,-1149 4381,-1136 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="powerLine" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_40876c0@0" ObjectIDND1="11498@1" ObjectIDND2="5858@x" ObjectIDZND0="g_3fe68c0@0" Pin0InfoVect0LinkObjId="g_3fe68c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_40876c0_0" Pin1InfoVect1LinkObjId="g_40e3a90_1" Pin1InfoVect2LinkObjId="SW-37008_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4381,-1149 4381,-1136 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_40e41b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4417,-1149 4417,-1183 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_40876c0@0" ObjectIDND1="g_3fe68c0@0" ObjectIDND2="5858@x" ObjectIDZND0="11498@1" Pin0InfoVect0LinkObjId="g_40e3a90_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_40876c0_0" Pin1InfoVect1LinkObjId="g_3fe68c0_0" Pin1InfoVect2LinkObjId="SW-37008_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4417,-1149 4417,-1183 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_40e4410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4435,-1122 4417,-1122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="powerLine" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="5858@0" ObjectIDZND0="11498@1" ObjectIDZND1="g_40876c0@0" ObjectIDZND2="g_3fe68c0@0" Pin0InfoVect0LinkObjId="g_40e3a90_1" Pin0InfoVect1LinkObjId="g_40876c0_0" Pin0InfoVect2LinkObjId="g_3fe68c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37008_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4435,-1122 4417,-1122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_40e4670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4417,-1122 4417,-1149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="powerLine" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="5858@x" ObjectIDND1="5856@x" ObjectIDZND0="11498@1" ObjectIDZND1="g_40876c0@0" ObjectIDZND2="g_3fe68c0@0" Pin0InfoVect0LinkObjId="g_40e3a90_1" Pin0InfoVect1LinkObjId="g_40876c0_0" Pin0InfoVect2LinkObjId="g_3fe68c0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-37008_0" Pin1InfoVect1LinkObjId="SW-37006_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4417,-1122 4417,-1149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_40e48d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3892,-1122 3872,-1122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="5881@0" ObjectIDZND0="g_4086120@0" ObjectIDZND1="g_3ffcdc0@0" ObjectIDZND2="11431@1" Pin0InfoVect0LinkObjId="g_4086120_0" Pin0InfoVect1LinkObjId="g_3ffcdc0_0" Pin0InfoVect2LinkObjId="g_40e54b0_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37123_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3892,-1122 3872,-1122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_40e4b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3836,-1149 3872,-1149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="powerLine" ObjectIDND0="g_4086120@0" ObjectIDND1="g_3ffcdc0@0" ObjectIDZND0="5881@x" ObjectIDZND1="5879@x" ObjectIDZND2="11431@1" Pin0InfoVect0LinkObjId="SW-37123_0" Pin0InfoVect1LinkObjId="SW-37121_0" Pin0InfoVect2LinkObjId="g_40e54b0_1" Pin0Num="2" Pin1InfoVect0LinkObjId="g_4086120_0" Pin1InfoVect1LinkObjId="g_3ffcdc0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3836,-1149 3872,-1149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_40e4d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3797,-1149 3836,-1149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="powerLine" ObjectIDND0="g_4086120@0" ObjectIDZND0="5881@x" ObjectIDZND1="5879@x" ObjectIDZND2="11431@1" Pin0InfoVect0LinkObjId="SW-37123_0" Pin0InfoVect1LinkObjId="SW-37121_0" Pin0InfoVect2LinkObjId="g_40e54b0_1" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4086120_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3797,-1149 3836,-1149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_40e4ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3836,-1149 3836,-1136 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_4086120@0" ObjectIDND1="5881@x" ObjectIDND2="5879@x" ObjectIDZND0="g_3ffcdc0@0" Pin0InfoVect0LinkObjId="g_3ffcdc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_4086120_0" Pin1InfoVect1LinkObjId="SW-37123_0" Pin1InfoVect2LinkObjId="SW-37121_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3836,-1149 3836,-1136 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_40e5250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3872,-1122 3872,-1149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="5881@x" ObjectIDND1="5879@x" ObjectIDZND0="g_4086120@0" ObjectIDZND1="g_3ffcdc0@0" ObjectIDZND2="11431@1" Pin0InfoVect0LinkObjId="g_4086120_0" Pin0InfoVect1LinkObjId="g_3ffcdc0_0" Pin0InfoVect2LinkObjId="g_40e54b0_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-37123_0" Pin1InfoVect1LinkObjId="SW-37121_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3872,-1122 3872,-1149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_40e54b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3872,-1149 3872,-1182 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="powerLine" ObjectIDND0="5881@x" ObjectIDND1="5879@x" ObjectIDND2="g_4086120@0" ObjectIDZND0="11431@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-37123_0" Pin1InfoVect1LinkObjId="SW-37121_0" Pin1InfoVect2LinkObjId="g_4086120_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3872,-1149 3872,-1182 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_40e7f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3872,-1122 3872,-1102 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" ObjectIDND0="g_4086120@0" ObjectIDND1="g_3ffcdc0@0" ObjectIDND2="11431@1" ObjectIDZND0="5879@1" Pin0InfoVect0LinkObjId="SW-37121_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_4086120_0" Pin1InfoVect1LinkObjId="g_3ffcdc0_0" Pin1InfoVect2LinkObjId="g_40e54b0_1" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3872,-1122 3872,-1102 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_40e81e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3872,-1066 3872,-1049 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="5879@0" ObjectIDZND0="5882@x" ObjectIDZND1="5880@x" Pin0InfoVect0LinkObjId="SW-37124_0" Pin0InfoVect1LinkObjId="SW-37122_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37121_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3872,-1066 3872,-1049 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_40eacb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4417,-1122 4417,-1102 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="11498@1" ObjectIDND1="g_40876c0@0" ObjectIDND2="g_3fe68c0@0" ObjectIDZND0="5856@1" Pin0InfoVect0LinkObjId="SW-37006_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_40e3a90_1" Pin1InfoVect1LinkObjId="g_40876c0_0" Pin1InfoVect2LinkObjId="g_3fe68c0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4417,-1122 4417,-1102 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_40eaf10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4417,-1066 4417,-1049 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="5856@0" ObjectIDZND0="5853@x" ObjectIDZND1="5857@x" Pin0InfoVect0LinkObjId="SW-37000_0" Pin0InfoVect1LinkObjId="SW-37007_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37006_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4417,-1066 4417,-1049 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" id="DYN-18" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3420.000000 -1086.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18" ObjectName="DYN-CX_QF"/>
     <cge:Meas_Ref ObjectId="18"/>
    </metadata>
   </g>
  </g><g id="CircleFilled_Layer">
   <circle DF8003:Layer="PUBLIC" cx="5095" cy="-1184" fill="rgb(0,0,0)" r="10" stroke="rgb(255,255,255)" stroke-width="1.75238"/>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_YZ" endPointId="0" endStationName="CX_QF" flowDrawDirect="1" flowShape="0" id="AC-110kV.yaoqin_line" runFlow="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3872,-1181 3872,-1223 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11431" ObjectName="AC-110kV.yaoqin_line"/>
    <cge:TPSR_Ref TObjectID="11431_SS-18"/></metadata>
   <polyline fill="none" opacity="0" points="3872,-1181 3872,-1223 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_LF" endPointId="0" endStationName="CX_QF" flowDrawDirect="1" flowShape="0" id="AC-110kV.luqinfeng_line" runFlow="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4417,-1181 4417,-1226 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11498" ObjectName="AC-110kV.luqinfeng_line"/>
    <cge:TPSR_Ref TObjectID="11498_SS-18"/></metadata>
   <polyline fill="none" opacity="0" points="4417,-1181 4417,-1226 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="5775" cx="3870" cy="-465" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5776" cx="4416" cy="-465" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5775" cx="4213" cy="-465" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5776" cx="4318" cy="-465" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5775" cx="3755" cy="-465" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5775" cx="3714" cy="-465" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5775" cx="4178" cy="-465" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5776" cx="4656" cy="-465" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5776" cx="4665" cy="-465" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5776" cx="4559" cy="-465" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5776" cx="4453" cy="-465" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5776" cx="4347" cy="-465" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5775" cx="3973" cy="-465" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5775" cx="3875" cy="-465" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5776" cx="4539" cy="-465" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5779" cx="4835" cy="-779" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5780" cx="4835" cy="-688" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5780" cx="4835" cy="-663" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5779" cx="4835" cy="-825" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5779" cx="4835" cy="-908" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5779" cx="4835" cy="-1151" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5779" cx="4835" cy="-1077" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5779" cx="4835" cy="-999" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5780" cx="4835" cy="-317" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5780" cx="4835" cy="-629" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5780" cx="4835" cy="-543" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5780" cx="4835" cy="-461" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5780" cx="4835" cy="-385" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5779" cx="4835" cy="-864" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5775" cx="3611" cy="-465" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5777" cx="3882" cy="-897" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5777" cx="4403" cy="-897" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5777" cx="4417" cy="-897" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5777" cx="4418" cy="-897" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5777" cx="3872" cy="-897" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5777" cx="3872" cy="-897" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-36446">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3861.000000 -604.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5800" ObjectName="SW-CX_QF.CX_QF_401BK"/>
     <cge:Meas_Ref ObjectId="36446"/>
    <cge:TPSR_Ref TObjectID="5800"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37000">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4407.585714 -991.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5853" ObjectName="SW-CX_QF.CX_QF_197BK"/>
     <cge:Meas_Ref ObjectId="37000"/>
    <cge:TPSR_Ref TObjectID="5853"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37124">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3863.000000 -994.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5882" ObjectName="SW-CX_QF.CX_QF_196BK"/>
     <cge:Meas_Ref ObjectId="37124"/>
    <cge:TPSR_Ref TObjectID="5882"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37014">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4116.300000 -887.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5859" ObjectName="SW-CX_QF.CX_QF_112BK"/>
     <cge:Meas_Ref ObjectId="37014"/>
    <cge:TPSR_Ref TObjectID="5859"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36995">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4407.000000 -572.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5849" ObjectName="SW-CX_QF.CX_QF_402BK"/>
     <cge:Meas_Ref ObjectId="36995"/>
    <cge:TPSR_Ref TObjectID="5849"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36841">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4242.000000 -521.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5841" ObjectName="SW-CX_QF.CX_QF_412BK"/>
     <cge:Meas_Ref ObjectId="36841"/>
    <cge:TPSR_Ref TObjectID="5841"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36816">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3746.000000 -345.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5838" ObjectName="SW-CX_QF.CX_QF_451BK"/>
     <cge:Meas_Ref ObjectId="36816"/>
    <cge:TPSR_Ref TObjectID="5838"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4190.000000 -171.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4169.000000 -199.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37039">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4656.000000 -350.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5864" ObjectName="SW-CX_QF.CX_QF_476BK"/>
     <cge:Meas_Ref ObjectId="37039"/>
    <cge:TPSR_Ref TObjectID="5864"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37065">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4550.000000 -350.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5869" ObjectName="SW-CX_QF.CX_QF_473BK"/>
     <cge:Meas_Ref ObjectId="37065"/>
    <cge:TPSR_Ref TObjectID="5869"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36764">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4444.000000 -350.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5831" ObjectName="SW-CX_QF.CX_QF_472BK"/>
     <cge:Meas_Ref ObjectId="36764"/>
    <cge:TPSR_Ref TObjectID="5831"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36736">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4338.000000 -350.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5827" ObjectName="SW-CX_QF.CX_QF_471BK"/>
     <cge:Meas_Ref ObjectId="36736"/>
    <cge:TPSR_Ref TObjectID="5827"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36708">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3866.000000 -350.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5823" ObjectName="SW-CX_QF.CX_QF_453BK"/>
     <cge:Meas_Ref ObjectId="36708"/>
    <cge:TPSR_Ref TObjectID="5823"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36443">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4693.000000 -854.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5797" ObjectName="SW-CX_QF.CX_QF_301BK"/>
     <cge:Meas_Ref ObjectId="36443"/>
    <cge:TPSR_Ref TObjectID="5797"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36660">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4894.000000 -714.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5818" ObjectName="SW-CX_QF.CX_QF_312BK"/>
     <cge:Meas_Ref ObjectId="36660"/>
    <cge:TPSR_Ref TObjectID="5818"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36989">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4696.000000 -653.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5845" ObjectName="SW-CX_QF.CX_QF_302BK"/>
     <cge:Meas_Ref ObjectId="36989"/>
    <cge:TPSR_Ref TObjectID="5845"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36577">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4897.000000 -918.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5809" ObjectName="SW-CX_QF.CX_QF_353BK"/>
     <cge:Meas_Ref ObjectId="36577"/>
    <cge:TPSR_Ref TObjectID="5809"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36523">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4898.000000 -1161.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5803" ObjectName="SW-CX_QF.CX_QF_351BK"/>
     <cge:Meas_Ref ObjectId="36523"/>
    <cge:TPSR_Ref TObjectID="5803"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36550">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4895.000000 -1009.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5806" ObjectName="SW-CX_QF.CX_QF_352BK"/>
     <cge:Meas_Ref ObjectId="36550"/>
    <cge:TPSR_Ref TObjectID="5806"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37092">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4894.000000 -327.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5874" ObjectName="SW-CX_QF.CX_QF_356BK"/>
     <cge:Meas_Ref ObjectId="37092"/>
    <cge:TPSR_Ref TObjectID="5874"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36604">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4893.000000 -639.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5812" ObjectName="SW-CX_QF.CX_QF_354BK"/>
     <cge:Meas_Ref ObjectId="36604"/>
    <cge:TPSR_Ref TObjectID="5812"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36631">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4892.000000 -553.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5815" ObjectName="SW-CX_QF.CX_QF_355BK"/>
     <cge:Meas_Ref ObjectId="36631"/>
    <cge:TPSR_Ref TObjectID="5815"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36792">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3602.000000 -345.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5835" ObjectName="SW-CX_QF.CX_QF_456BK"/>
     <cge:Meas_Ref ObjectId="36792"/>
    <cge:TPSR_Ref TObjectID="5835"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5137.000000 -973.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36680">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3964.000000 -350.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5819" ObjectName="SW-CX_QF.CX_QF_452BK"/>
     <cge:Meas_Ref ObjectId="36680"/>
    <cge:TPSR_Ref TObjectID="5819"/></metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3143.000000 -1034.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3143.000000 -1034.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3143.000000 -1034.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3143.000000 -1034.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3143.000000 -1034.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3143.000000 -1034.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3143.000000 -1034.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3143.000000 -589.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3143.000000 -589.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3143.000000 -589.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3143.000000 -589.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3143.000000 -589.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3143.000000 -589.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3143.000000 -589.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3143.000000 -589.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3143.000000 -589.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3143.000000 -589.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3143.000000 -589.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3143.000000 -589.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3143.000000 -589.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3143.000000 -589.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3143.000000 -589.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3143.000000 -589.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3143.000000 -589.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3143.000000 -589.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3882.000000 -1023.000000) translate(0,12)">196</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3882.000000 -950.000000) translate(0,12)">1961</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3888.000000 -1004.000000) translate(0,12)">19617</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3889.000000 -1075.000000) translate(0,12)">19660</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3889.000000 -1148.000000) translate(0,12)">19667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3878.000000 -1092.000000) translate(0,12)">1966</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3879.000000 -865.000000) translate(0,12)">1016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3888.000000 -834.000000) translate(0,12)">10167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4127.300000 -921.000000) translate(0,12)">112</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4056.300000 -923.000000) translate(0,12)">1121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4196.300000 -923.000000) translate(0,12)">1122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4115.300000 -864.000000) translate(0,12)">11217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4181.300000 -864.000000) translate(0,12)">11227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4425.585714 -1020.000000) translate(0,12)">197</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4423.585714 -944.000000) translate(0,12)">1971</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4432.585714 -1004.000000) translate(0,12)">19717</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4433.585714 -1075.000000) translate(0,12)">19760</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4432.585714 -1148.000000) translate(0,12)">19767</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4423.585714 -1093.000000) translate(0,12)">1976</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4425.000000 -865.000000) translate(0,12)">1026</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4434.000000 -834.000000) translate(0,12)">10267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4609.000000 -794.000000) translate(0,12)">1702</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3711.000000 -736.000000) translate(0,12)">1701</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3930.000000 -759.000000) translate(0,15)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4481.000000 -756.000000) translate(0,15)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4775.000000 -1170.000000) translate(0,12)">35kVⅠ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4768.000000 -296.000000) translate(0,12)">35kVⅡ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3536.000000 -459.000000) translate(0,15)">10kVⅠ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4692.000000 -459.000000) translate(0,15)">10kVⅡ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4044.000000 -700.000000) translate(0,12)">3701</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4198.000000 -700.000000) translate(0,12)">3702</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3879.000000 -589.000000) translate(0,12)">401</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3877.000000 -540.000000) translate(0,12)">4011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3877.000000 -638.000000) translate(0,12)">4016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3722.000000 -511.000000) translate(0,12)">4901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4253.000000 -555.000000) translate(0,12)">412</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4220.000000 -509.000000) translate(0,12)">4121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4325.000000 -512.000000) translate(0,12)">4122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4425.000000 -557.000000) translate(0,12)">402</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4423.000000 -508.000000) translate(0,12)">4021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4423.000000 -606.000000) translate(0,12)">4026</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4546.000000 -511.000000) translate(0,12)">4904</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4656.000000 -514.000000) translate(0,12)">4902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4703.000000 -888.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4748.000000 -890.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4651.000000 -890.000000) translate(0,12)">3016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4706.000000 -687.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4751.000000 -689.000000) translate(0,12)">3021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4653.000000 -689.000000) translate(0,12)">3026</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4912.000000 -743.000000) translate(0,12)">312</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4852.000000 -805.000000) translate(0,12)">3121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4853.000000 -714.000000) translate(0,12)">3122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4909.000000 -1176.000000) translate(0,12)">351</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4857.000000 -1177.000000) translate(0,12)">3511</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4953.000000 -1177.000000) translate(0,12)">3516</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4857.000000 -1103.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4905.000000 -1024.000000) translate(0,12)">352</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4853.000000 -1025.000000) translate(0,12)">3521</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4950.000000 -1025.000000) translate(0,12)">3526</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4907.000000 -933.000000) translate(0,12)">353</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4855.000000 -934.000000) translate(0,12)">3531</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4952.000000 -934.000000) translate(0,12)">3536</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4865.000000 -851.000000) translate(0,12)">3903</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4904.000000 -654.000000) translate(0,12)">354</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4852.000000 -655.000000) translate(0,12)">3541</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4948.000000 -655.000000) translate(0,12)">3546</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4903.000000 -568.000000) translate(0,12)">355</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4851.000000 -569.000000) translate(0,12)">3551</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4947.000000 -569.000000) translate(0,12)">3556</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4864.000000 -487.000000) translate(0,12)">3904</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4854.000000 -411.000000) translate(0,12)">3902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4904.000000 -342.000000) translate(0,12)">356</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4852.000000 -343.000000) translate(0,12)">3561</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4949.000000 -343.000000) translate(0,12)">3566</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3620.000000 -374.000000) translate(0,12)">456</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3618.000000 -434.000000) translate(0,12)">4561</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3618.000000 -256.000000) translate(0,12)">4566</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3764.000000 -374.000000) translate(0,12)">451</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3762.000000 -434.000000) translate(0,12)">1</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3762.000000 -256.000000) translate(0,12)">4516</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3884.000000 -379.000000) translate(0,12)">453</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3882.000000 -439.000000) translate(0,12)">4531</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3882.000000 -269.000000) translate(0,12)">4536</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3893.000000 -424.000000) translate(0,12)">45317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3982.000000 -379.000000) translate(0,12)">452</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3980.000000 -439.000000) translate(0,12)">4521</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3980.000000 -268.000000) translate(0,12)">4526</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3990.000000 -424.000000) translate(0,12)">45217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4122.000000 -407.000000) translate(0,12)">4903</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4185.000000 -408.000000) translate(0,12)">4000</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4356.000000 -379.000000) translate(0,12)">471</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4354.000000 -439.000000) translate(0,12)">4711</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4354.000000 -269.000000) translate(0,12)">4716</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4364.000000 -424.000000) translate(0,12)">47117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4462.000000 -379.000000) translate(0,12)">472</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4460.000000 -439.000000) translate(0,12)">4721</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4460.000000 -271.000000) translate(0,12)">4726</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4469.000000 -424.000000) translate(0,12)">47217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4568.000000 -379.000000) translate(0,12)">473</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4566.000000 -271.000000) translate(0,12)">4736</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4566.000000 -439.000000) translate(0,12)">4731</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4575.000000 -424.000000) translate(0,12)">47317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4575.000000 -257.000000) translate(0,12)">47367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4674.000000 -379.000000) translate(0,12)">476</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4672.000000 -439.000000) translate(0,12)">4761</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4681.000000 -424.000000) translate(0,12)">47617</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4672.000000 -271.000000) translate(0,12)">4766</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4681.000000 -258.000000) translate(0,12)">47667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3805.000000 -1210.000000) translate(0,15)">腰勤线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4321.000000 -1215.000000) translate(0,15)">禄勤II回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4064.000000 -775.000000) translate(0,15)">35kV消弧线圈</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3646.000000 -655.000000) translate(0,15)">Ⅰ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3646.000000 -655.000000) translate(0,33)">段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3646.000000 -655.000000) translate(0,51)">母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3646.000000 -655.000000) translate(0,69)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3646.000000 -655.000000) translate(0,87)">TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4508.000000 -655.000000) translate(0,15)">Ⅱ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4508.000000 -655.000000) translate(0,33)">段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4508.000000 -655.000000) translate(0,51)">计</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4508.000000 -655.000000) translate(0,69)">量</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4508.000000 -655.000000) translate(0,87)">TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4588.000000 -655.000000) translate(0,15)">Ⅱ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4588.000000 -655.000000) translate(0,33)">段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4588.000000 -655.000000) translate(0,51)">母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4588.000000 -655.000000) translate(0,69)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4588.000000 -655.000000) translate(0,87)">TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3643.000000 -186.000000) translate(0,15)">1</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3643.000000 -186.000000) translate(0,33)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3643.000000 -186.000000) translate(0,51)">电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3643.000000 -186.000000) translate(0,69)">容</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3643.000000 -186.000000) translate(0,87)">器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3643.000000 -186.000000) translate(0,105)">组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3793.000000 -186.000000) translate(0,15)">3</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3793.000000 -186.000000) translate(0,33)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3793.000000 -186.000000) translate(0,51)">电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3793.000000 -186.000000) translate(0,69)">容</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3793.000000 -186.000000) translate(0,87)">器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3793.000000 -186.000000) translate(0,105)">组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3886.000000 -132.000000) translate(0,15)">备</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3886.000000 -132.000000) translate(0,33)">用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3886.000000 -132.000000) translate(0,51)">二</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3987.000000 -132.000000) translate(0,15)">备</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3987.000000 -132.000000) translate(0,33)">用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3987.000000 -132.000000) translate(0,51)">一</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4077.000000 -341.000000) translate(0,15)">Ⅰ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4077.000000 -341.000000) translate(0,33)">段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4077.000000 -341.000000) translate(0,51)">计</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4077.000000 -341.000000) translate(0,69)">量</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4077.000000 -341.000000) translate(0,87)">TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4203.000000 -368.000000) translate(0,15)">10</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4203.000000 -368.000000) translate(0,33)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4203.000000 -368.000000) translate(0,51)">1</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4203.000000 -368.000000) translate(0,69)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4203.000000 -368.000000) translate(0,87)">站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4203.000000 -368.000000) translate(0,105)">用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4203.000000 -368.000000) translate(0,123)">变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4357.000000 -150.000000) translate(0,15)">电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4357.000000 -150.000000) translate(0,33)">瓶</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4357.000000 -150.000000) translate(0,51)">厂</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4357.000000 -150.000000) translate(0,69)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4461.000000 -168.000000) translate(0,15)">勤</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4461.000000 -168.000000) translate(0,33)">丰</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4461.000000 -168.000000) translate(0,51)">Ⅰ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4461.000000 -168.000000) translate(0,69)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4461.000000 -168.000000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4598.000000 -186.000000) translate(0,15)">4</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4598.000000 -186.000000) translate(0,33)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4598.000000 -186.000000) translate(0,51)">电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4598.000000 -186.000000) translate(0,69)">容</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4598.000000 -186.000000) translate(0,87)">器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4598.000000 -186.000000) translate(0,105)">组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4703.000000 -186.000000) translate(0,15)">2</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4703.000000 -186.000000) translate(0,33)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4703.000000 -186.000000) translate(0,51)">电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4703.000000 -186.000000) translate(0,69)">容</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4703.000000 -186.000000) translate(0,87)">器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4703.000000 -186.000000) translate(0,105)">组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5071.000000 -310.000000) translate(0,15)">备用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5009.000000 -469.000000) translate(0,15)">Ⅱ段计量TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5068.000000 -395.000000) translate(0,15)">Ⅱ段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5068.000000 -1086.000000) translate(0,15)">Ⅰ段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5009.000000 -833.000000) translate(0,15)">Ⅰ段计量TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5053.000000 -900.000000) translate(0,15)">碧城线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5053.000000 -996.000000) translate(0,15)">仁兴线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5038.000000 -1145.000000) translate(0,15)">果勤罗线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5053.000000 -620.000000) translate(0,15)">羊街线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5053.000000 -536.000000) translate(0,15)">勤土线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" transform="matrix(1.000000 0.000000 0.000000 1.000000 3257.000000 -1166.500000) translate(0,16)">勤丰变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3657.000000 -563.000000) translate(0,12)">49017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3646.000000 -419.000000) translate(0,12)">45617</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3647.000000 -241.000000) translate(0,12)">45667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3789.000000 -417.000000) translate(0,12)">45117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3790.000000 -239.000000) translate(0,12)">45167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4064.000000 -391.000000) translate(0,12)">49037</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4187.000000 -228.000000) translate(0,12)">K01</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4192.000000 -170.000000) translate(0,12)">K03</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4479.000000 -562.000000) translate(0,12)">49047</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4598.000000 -562.000000) translate(0,12)">49027</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5061.000000 -950.000000) translate(0,15)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5143.000000 -957.000000) translate(0,15)">K02</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3982.000000 -379.000000) translate(0,12)">452</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" transform="matrix(1.000000 0.000000 0.000000 1.000000 3415.000000 -1162.000000) translate(0,16)">AVC</text>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_3ea2350">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4484.000000 -802.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3ea8600">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4482.585714 -972.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3eae050">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4483.585714 -1043.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3fe5e30">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4482.585714 -1116.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3fece90">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3938.000000 -802.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3ff3140">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3938.000000 -972.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3ff8b90">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3939.000000 -1043.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3ffc330">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3940.000000 -1116.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4001690">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3748.000000 -683.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_401b4a0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3821.000000 -400.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_401db20">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3821.000000 -223.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f5f1e0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4102.300000 -807.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f5fc30">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4168.300000 -807.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f66560">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3629.000000 -532.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f6fb60">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4029.000000 -362.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f7b830">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4570.000000 -532.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f7f030">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4731.000000 -407.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f8a300">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4625.000000 -407.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f955d0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4519.000000 -407.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3fa1b10">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4413.000000 -407.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3fae050">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4039.000000 -407.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3fb8540">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3941.000000 -407.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3fc0f60">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4625.000000 -240.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3fc4700">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4731.000000 -241.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3fcdb20">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4453.000000 -531.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3fd55c0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4589.000000 -841.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4090db0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3678.000000 -400.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4093430">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3677.000000 -223.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_QF"/>
</svg>