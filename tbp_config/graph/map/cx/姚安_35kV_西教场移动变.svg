<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-298" aopId="4063750" id="thSvg" product="E8000V2" version="1.0" viewBox="-196 -1238 3283 1870">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="capacitor:shape11">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.974359" x1="16" x2="92" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.484375" x1="68" x2="37" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.484375" x1="68" x2="36" y1="37" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.484375" x1="31" x2="0" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.484375" x1="31" x2="0" y1="37" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.484375" x1="107" x2="76" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.484375" x1="107" x2="76" y1="37" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="22" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="52" x2="52" y1="22" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="92" x2="92" y1="22" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="8" x2="8" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape55">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="27,39 5,17 5,5 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="42"/>
    <rect height="4" stroke-width="1" width="19" x="7" y="26"/>
   </symbol>
   <symbol id="lightningRod:shape7">
    <polyline DF8003:Layer="PUBLIC" points="1,5 10,17 19,5 1,5 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="20" y2="23"/>
    <polyline DF8003:Layer="PUBLIC" points="19,44 10,32 1,44 19,44 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="29" y2="26"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="6" y2="41"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="63" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="55" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="56" y2="56"/>
   </symbol>
   <symbol id="lightningRod:shape50">
    <polyline DF8003:Layer="PUBLIC" points="5,39 0,50 11,50 5,39 5,40 5,39 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.222222" x1="5" x2="5" y1="29" y2="33"/>
    <polyline DF8003:Layer="PUBLIC" points="5,25 0,14 11,14 5,25 5,24 5,25 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="59" y2="51"/>
   </symbol>
   <symbol id="lightningRod:shape28">
    <circle cx="14" cy="13" fillStyle="0" r="13.5" stroke-width="1"/>
    <circle cx="14" cy="34" fillStyle="0" r="14" stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="5" y2="36"/>
   </symbol>
   <symbol id="lightningRod:shape37">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,63 0,73 10,73 5,63 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,28 0,18 10,18 5,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="86" y2="6"/>
   </symbol>
   <symbol id="load:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape5_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="38" x2="13" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="38" x2="47" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="5" x2="14" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="14" y2="0"/>
   </symbol>
   <symbol id="switch2:shape5_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
   </symbol>
   <symbol id="switch2:shape5-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape5-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape19_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="14" y1="30" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="26" y1="38" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="14" y1="26" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="7" y1="38" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="27" y1="17" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="5" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="5" y1="46" y2="10"/>
    <rect height="26" stroke-width="0.416609" width="14" x="-2" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="34" y1="10" y2="40"/>
    <rect height="29" stroke-width="0.416609" width="9" x="12" y="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="5" y1="46" y2="10"/>
    <rect height="26" stroke-width="0.416609" width="14" x="-2" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape8_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="18" y2="43"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="18" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
   </symbol>
   <symbol id="switch2:shape8_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="44" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
   </symbol>
   <symbol id="switch2:shape8-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="18" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="18" y2="43"/>
   </symbol>
   <symbol id="switch2:shape8-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="44" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
   </symbol>
   <symbol id="transformer2:shape12_0">
    <circle cx="16" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,57 40,57 40,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="42" y2="0"/>
    <polyline DF8003:Layer="PUBLIC" points="15,14 21,27 9,27 15,14 15,15 15,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="25" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="42" x2="39" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="15" y1="52" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="57" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="57" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="56" y2="52"/>
   </symbol>
   <symbol id="transformer2:shape12_1">
    <circle cx="16" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="82" y2="87"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="81" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="80" y2="76"/>
   </symbol>
   <symbol id="transformer2:shape20_0">
    <circle cx="20" cy="16" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="47" x2="72" y1="39" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="78" x2="78" y1="43" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="75" x2="75" y1="44" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="73" x2="73" y1="34" y2="46"/>
    <polyline DF8003:Layer="PUBLIC" points="84,14 71,20 71,7 84,14 83,14 84,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="56" x2="98" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="13" y1="19" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="11" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="19" y1="15" y2="11"/>
   </symbol>
   <symbol id="transformer2:shape20_1">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="41,15 41,40 70,40 " stroke-width="1"/>
    <circle cx="42" cy="16" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="47" y1="15" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="47" y1="15" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="36" y1="15" y2="15"/>
   </symbol>
   <symbol id="transformer2:shape70_0">
    <circle cx="39" cy="32" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="64,93 64,100 " stroke-width="1.1087"/>
    <polyline points="58,100 64,100 " stroke-width="1.1087"/>
    <polyline DF8003:Layer="PUBLIC" points="39,19 32,34 47,34 39,19 39,19 39,19 "/>
   </symbol>
   <symbol id="transformer2:shape70_1">
    <circle cx="39" cy="70" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="64,100 1,37 " stroke-width="1.1087"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape15_0">
    <circle cx="15" cy="19" fillStyle="0" r="15" stroke-width="0.306122"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.306122" x1="15" x2="15" y1="10" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.306122" x1="15" x2="20" y1="15" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.306122" x1="10" x2="15" y1="20" y2="15"/>
   </symbol>
   <symbol id="transformer2:shape15_1">
    <circle cx="15" cy="41" fillStyle="0" r="15" stroke-width="0.306122"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.306122" x1="11" x2="15" y1="50" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.306122" x1="15" x2="15" y1="40" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.306122" x1="15" x2="20" y1="45" y2="50"/>
   </symbol>
   <symbol id="voltageTransformer:shape94">
    <rect height="24" stroke-width="0.379884" width="14" x="2" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="21" y1="14" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="25" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="9" x2="9" y1="23" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="9" x2="6" y1="23" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="9" x2="9" y1="8" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="12" x2="9" y1="11" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="9" x2="6" y1="8" y2="11"/>
    <ellipse cx="21" cy="16" fillStyle="0" rx="8.5" ry="8" stroke-width="1"/>
    <circle cx="9" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="9" cy="22" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="12" x2="9" y1="26" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="23" x2="25" y1="18" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="75" y2="31"/>
   </symbol>
   <symbol id="voltageTransformer:shape56">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="1" y1="58" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="20" y1="58" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="20" y1="38" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="1" x2="13" y1="51" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="22" y1="59" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="22" y1="37" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="25" y1="61" y2="61"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="22" y1="70" y2="61"/>
    <ellipse cx="21" cy="8" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <ellipse cx="21" cy="16" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="25" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="25" y1="18" y2="18"/>
   </symbol>
   <symbol id="voltageTransformer:shape10">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="18" x2="15" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="16" x2="16" y1="4" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="3" x2="9" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="6" x2="3" y1="14" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="6" x2="9" y1="14" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="18" x2="15" y1="17" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="16" x2="16" y1="15" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="13" x2="15" y1="17" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="27" x2="24" y1="13" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="25" x2="25" y1="11" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="22" x2="24" y1="13" y2="11"/>
    <circle cx="7" cy="11" fillStyle="0" r="6" stroke-width="0.431185"/>
    <ellipse cx="15" cy="6" fillStyle="0" rx="6.5" ry="6" stroke-width="0.431185"/>
    <circle cx="24" cy="11" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="15" cy="14" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="13" x2="15" y1="6" y2="4"/>
   </symbol>
   <symbol id="voltageTransformer:shape66">
    <polyline points="12,39 12,4 " stroke-width="1"/>
    <rect height="24" stroke-width="0.361111" width="11" x="6" y="10"/>
    <ellipse cx="12" cy="52" fillStyle="0" rx="11.5" ry="13" stroke-width="0.236902"/>
    <ellipse cx="29" cy="61" fillStyle="0" rx="11.5" ry="12.5" stroke-width="0.236902"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236902" x1="13" x2="13" y1="76" y2="81"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236902" x1="17" x2="13" y1="72" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236902" x1="13" x2="10" y1="76" y2="72"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236902" x1="13" x2="13" y1="62" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236902" x1="16" x2="13" y1="54" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236902" x1="13" x2="9" y1="58" y2="54"/>
    <ellipse cx="13" cy="74" fillStyle="0" rx="11" ry="12.5" stroke-width="0.236902"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="29,66 29,58 35,61 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="33" y1="66" y2="63"/>
   </symbol>
   <symbol id="voltageTransformer:shape139">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="20" x2="20" y1="30" y2="76"/>
    <rect height="27" stroke-width="0.416667" width="14" x="13" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="50" y1="76" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="50" x2="50" y1="76" y2="41"/>
    <rect height="27" stroke-width="0.416667" width="14" x="43" y="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="56" x2="44" y1="29" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="50" x2="50" y1="28" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="46" x2="54" y1="25" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="49" x2="52" y1="22" y2="22"/>
    <circle cx="20" cy="21" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="19" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <ellipse cx="30" cy="17" fillStyle="0" rx="8.5" ry="8" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="19" x2="17" y1="24" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="22" x2="19" y1="27" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="19" x2="19" y1="24" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="30" y1="21" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="34" y1="21" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="35" y1="14" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="34" y1="76" y2="92"/>
    <ellipse cx="9" cy="18" fillStyle="0" rx="8.5" ry="8" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="9" x2="7" y1="18" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="12" x2="9" y1="21" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="9" x2="9" y1="18" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="19" x2="17" y1="10" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="22" x2="19" y1="13" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="19" x2="19" y1="10" y2="7"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_37526d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3753010" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_37539b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3754650" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_37558b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_37564d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3756f30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_37577d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3757e00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_37584b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_37584b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3759f70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3759f70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_375ac70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_375c870" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_375d460" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_375e220" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_375eb60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3760220" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3760a20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3761110" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_37618a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_37628c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3763240" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3763d30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_37646f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3765c10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3766740" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3767770" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_37683b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_37764a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3776f70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_376a3e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_376b9a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1880" width="3293" x="-201" y="-1243"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="910" x2="910" y1="-1237" y2="-1227"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.833333" x1="910" x2="919" y1="-1237" y2="-1232"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="910" x2="919" y1="-1226" y2="-1231"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,0)" stroke-width="1" x1="3078" x2="3087" y1="-340" y2="-340"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.833333" x1="2474" x2="2469" y1="-988" y2="-979"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="2463" x2="2468" y1="-988" y2="-979"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="2473" x2="2463" y1="-988" y2="-988"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-258287">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 532.000000 -877.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42352" ObjectName="SW-CX_XJCYD.CX_XJCYD_003BK"/>
     <cge:Meas_Ref ObjectId="258287"/>
    <cge:TPSR_Ref TObjectID="42352"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-258279">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 888.000000 -1057.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42349" ObjectName="SW-CX_XJCYD.CX_XJCYD_303BK"/>
     <cge:Meas_Ref ObjectId="258279"/>
    <cge:TPSR_Ref TObjectID="42349"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-258368">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 586.166667 -790.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42356" ObjectName="SW-CX_XJCYD.CX_XJCYD_011BK"/>
     <cge:Meas_Ref ObjectId="258368"/>
    <cge:TPSR_Ref TObjectID="42356"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-258390">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 585.166667 -678.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42359" ObjectName="SW-CX_XJCYD.CX_XJCYD_012BK"/>
     <cge:Meas_Ref ObjectId="258390"/>
    <cge:TPSR_Ref TObjectID="42359"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-258412">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 586.166667 -568.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42362" ObjectName="SW-CX_XJCYD.CX_XJCYD_013BK"/>
     <cge:Meas_Ref ObjectId="258412"/>
    <cge:TPSR_Ref TObjectID="42362"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-258434">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 586.166667 -455.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42365" ObjectName="SW-CX_XJCYD.CX_XJCYD_014BK"/>
     <cge:Meas_Ref ObjectId="258434"/>
    <cge:TPSR_Ref TObjectID="42365"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-258456">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 584.166667 -344.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42368" ObjectName="SW-CX_XJCYD.CX_XJCYD_015BK"/>
     <cge:Meas_Ref ObjectId="258456"/>
    <cge:TPSR_Ref TObjectID="42368"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-258478">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 587.166667 -246.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42371" ObjectName="SW-CX_XJCYD.CX_XJCYD_016BK"/>
     <cge:Meas_Ref ObjectId="258478"/>
    <cge:TPSR_Ref TObjectID="42371"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-258500">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 587.166667 -137.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42374" ObjectName="SW-CX_XJCYD.CX_XJCYD_017BK"/>
     <cge:Meas_Ref ObjectId="258500"/>
    <cge:TPSR_Ref TObjectID="42374"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-258521">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 588.166667 -26.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42377" ObjectName="SW-CX_XJCYD.CX_XJCYD_018BK"/>
     <cge:Meas_Ref ObjectId="258521"/>
    <cge:TPSR_Ref TObjectID="42377"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147610">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1730.000000 -783.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25795" ObjectName="SW-YA_XJC.YA_XJC_381BK"/>
     <cge:Meas_Ref ObjectId="147610"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147673">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1799.000000 -632.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25807" ObjectName="SW-YA_XJC.YA_XJC_301BK"/>
     <cge:Meas_Ref ObjectId="147673"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147749">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1799.000000 -385.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25812" ObjectName="SW-YA_XJC.YA_XJC_001BK"/>
     <cge:Meas_Ref ObjectId="147749"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147813">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1601.000000 -217.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25839" ObjectName="SW-YA_XJC.YA_XJC_053BK"/>
     <cge:Meas_Ref ObjectId="147813"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147631">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2146.000000 -783.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25799" ObjectName="SW-YA_XJC.YA_XJC_382BK"/>
     <cge:Meas_Ref ObjectId="147631"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147652">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2559.000000 -787.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25803" ObjectName="SW-YA_XJC.YA_XJC_383BK"/>
     <cge:Meas_Ref ObjectId="147652"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147708">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2495.000000 -632.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25809" ObjectName="SW-YA_XJC.YA_XJC_302BK"/>
     <cge:Meas_Ref ObjectId="147708"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147755">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2495.000000 -385.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25816" ObjectName="SW-YA_XJC.YA_XJC_002BK"/>
     <cge:Meas_Ref ObjectId="147755"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147862">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2133.000000 -393.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25854" ObjectName="SW-YA_XJC.YA_XJC_012BK"/>
     <cge:Meas_Ref ObjectId="147862"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147781">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1751.000000 -217.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25829" ObjectName="SW-YA_XJC.YA_XJC_054BK"/>
     <cge:Meas_Ref ObjectId="147781"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147846">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1907.000000 -217.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25849" ObjectName="SW-YA_XJC.YA_XJC_055BK"/>
     <cge:Meas_Ref ObjectId="147846"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147765">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2420.000000 -217.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25824" ObjectName="SW-YA_XJC.YA_XJC_061BK"/>
     <cge:Meas_Ref ObjectId="147765"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147829">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2588.000000 -217.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25844" ObjectName="SW-YA_XJC.YA_XJC_062BK"/>
     <cge:Meas_Ref ObjectId="147829"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-182947">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2924.000000 -217.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27838" ObjectName="SW-YA_XJC.YA_XJC_064BK"/>
     <cge:Meas_Ref ObjectId="182947"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147797">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2761.000000 -218.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25834" ObjectName="SW-YA_XJC.YA_XJC_063BK"/>
     <cge:Meas_Ref ObjectId="147797"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147872">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1447.000000 -217.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25858" ObjectName="SW-YA_XJC.YA_XJC_052BK"/>
     <cge:Meas_Ref ObjectId="147872"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-265494">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1287.000000 -210.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42944" ObjectName="SW-YA_XJC.YA_XJC_051BK"/>
     <cge:Meas_Ref ObjectId="265494"/>
    </metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2c5cd00">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 838.000000 -1139.000000)" xlink:href="#voltageTransformer:shape94"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f63a00">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1674.000000 -868.000000)" xlink:href="#voltageTransformer:shape56"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f6d450">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2090.000000 -868.000000)" xlink:href="#voltageTransformer:shape56"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c19800">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2507.000000 -931.000000)" xlink:href="#voltageTransformer:shape56"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c95450">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2140.000000 -561.000000)" xlink:href="#voltageTransformer:shape10"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3454ce0">
    <use class="BV-10KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 575.500000 60.500000)" xlink:href="#voltageTransformer:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_345ed30">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2007.000000 -124.000000)" xlink:href="#voltageTransformer:shape139"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3461a90">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2209.000000 -123.000000)" xlink:href="#voltageTransformer:shape139"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_xidalianTxjc" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="2155,-987 2155,-959 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="34316" ObjectName="AC-35kV.LN_xidalianTxjc"/>
    </metadata>
   <polyline fill="none" opacity="0" points="2155,-987 2155,-959 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_yaoxi" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="2568,-1039 2568,-1012 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="37772" ObjectName="AC-35kV.LN_yaoxi"/>
    </metadata>
   <polyline fill="none" opacity="0" points="2568,-1039 2568,-1012 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_huangxitaiXJC" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1739,-955 1739,-976 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="38083" ObjectName="AC-35kV.LN_huangxitaiXJC"/>
    </metadata>
   <polyline fill="none" opacity="0" points="1739,-955 1739,-976 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-YA_XJC.483Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1261.000000 405.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34043" ObjectName="EC-YA_XJC.483Ld"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YA_XJC.485Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1419.000000 388.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34044" ObjectName="EC-YA_XJC.485Ld"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YA_XJC.487Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1585.000000 393.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34045" ObjectName="EC-YA_XJC.487Ld"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YA_XJC.482Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2123.000000 424.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34046" ObjectName="EC-YA_XJC.482Ld"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YA_XJC.484Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2269.000000 425.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34047" ObjectName="EC-YA_XJC.484Ld"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YA_XJC.486Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2465.000000 411.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34042" ObjectName="EC-YA_XJC.486Ld"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YA_XJC.488Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2656.000000 414.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34048" ObjectName="EC-YA_XJC.488Ld"/>
    </metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2cd0020" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 675.500000 -766.500000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c88880" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 674.500000 -654.500000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d0e3c0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 675.500000 -544.500000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d5f250" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 675.500000 -431.500000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d5b740" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 673.500000 -320.500000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c82790" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 676.500000 -222.500000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2cc7c70" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 676.500000 -109.500000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c471a0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 677.500000 -1.500000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-10KV" id="g_2d38c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1310,350 1266,350 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_2aa4ea0@0" ObjectIDZND0="34043@x" ObjectIDZND1="25828@x" Pin0InfoVect0LinkObjId="EC-YA_XJC.483Ld_0" Pin0InfoVect1LinkObjId="SW-147770_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2aa4ea0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1310,350 1266,350 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2dd59d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1266,384 1266,350 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="34043@0" ObjectIDZND0="g_2aa4ea0@0" ObjectIDZND1="25828@x" Pin0InfoVect0LinkObjId="g_2aa4ea0_0" Pin0InfoVect1LinkObjId="SW-147770_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-YA_XJC.483Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1266,384 1266,350 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d20b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1266,350 1266,337 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_2aa4ea0@0" ObjectIDND1="34043@x" ObjectIDZND0="25828@0" Pin0InfoVect0LinkObjId="SW-147770_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2aa4ea0_0" Pin1InfoVect1LinkObjId="EC-YA_XJC.483Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1266,350 1266,337 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d0b5a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1468,333 1424,333 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_2d1ba20@0" ObjectIDZND0="34044@x" ObjectIDZND1="25833@x" Pin0InfoVect0LinkObjId="EC-YA_XJC.485Ld_0" Pin0InfoVect1LinkObjId="SW-147786_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d1ba20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1468,333 1424,333 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d0b790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1424,367 1424,333 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="34044@0" ObjectIDZND0="g_2d1ba20@0" ObjectIDZND1="25833@x" Pin0InfoVect0LinkObjId="g_2d1ba20_0" Pin0InfoVect1LinkObjId="SW-147786_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-YA_XJC.485Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1424,367 1424,333 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d0b980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1424,333 1424,320 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_2d1ba20@0" ObjectIDND1="34044@x" ObjectIDZND0="25833@0" Pin0InfoVect0LinkObjId="SW-147786_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2d1ba20_0" Pin1InfoVect1LinkObjId="EC-YA_XJC.485Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1424,333 1424,320 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ce3430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1632,343 1590,343 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_2ce2650@0" ObjectIDZND0="34045@x" ObjectIDZND1="25838@x" Pin0InfoVect0LinkObjId="EC-YA_XJC.487Ld_0" Pin0InfoVect1LinkObjId="SW-147802_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ce2650_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1632,343 1590,343 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ce3620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1590,372 1590,343 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="34045@0" ObjectIDZND0="g_2ce2650@0" ObjectIDZND1="25838@x" Pin0InfoVect0LinkObjId="g_2ce2650_0" Pin0InfoVect1LinkObjId="SW-147802_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-YA_XJC.487Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1590,372 1590,343 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ce3810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1590,343 1590,330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_2ce2650@0" ObjectIDND1="34045@x" ObjectIDZND0="25838@0" Pin0InfoVect0LinkObjId="SW-147802_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2ce2650_0" Pin1InfoVect1LinkObjId="EC-YA_XJC.487Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1590,343 1590,330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ccc340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2172,369 2128,369 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_2ccb490@0" ObjectIDZND0="34046@x" ObjectIDZND1="25843@x" Pin0InfoVect0LinkObjId="EC-YA_XJC.482Ld_0" Pin0InfoVect1LinkObjId="SW-147818_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ccb490_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2172,369 2128,369 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ccc530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2128,403 2128,369 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="34046@0" ObjectIDZND0="g_2ccb490@0" ObjectIDZND1="25843@x" Pin0InfoVect0LinkObjId="g_2ccb490_0" Pin0InfoVect1LinkObjId="SW-147818_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-YA_XJC.482Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2128,403 2128,369 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ccc720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2128,369 2128,356 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_2ccb490@0" ObjectIDND1="34046@x" ObjectIDZND0="25843@0" Pin0InfoVect0LinkObjId="SW-147818_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2ccb490_0" Pin1InfoVect1LinkObjId="EC-YA_XJC.482Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2128,369 2128,356 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2dbd220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2318,370 2274,370 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_2dbbfb0@0" ObjectIDZND0="34047@x" ObjectIDZND1="25848@x" Pin0InfoVect0LinkObjId="EC-YA_XJC.484Ld_0" Pin0InfoVect1LinkObjId="SW-147834_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2dbbfb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2318,370 2274,370 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2dbd410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2274,404 2274,370 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="34047@0" ObjectIDZND0="g_2dbbfb0@0" ObjectIDZND1="25848@x" Pin0InfoVect0LinkObjId="g_2dbbfb0_0" Pin0InfoVect1LinkObjId="SW-147834_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-YA_XJC.484Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2274,404 2274,370 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2dbd600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2274,370 2274,357 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_2dbbfb0@0" ObjectIDND1="34047@x" ObjectIDZND0="25848@0" Pin0InfoVect0LinkObjId="SW-147834_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2dbbfb0_0" Pin1InfoVect1LinkObjId="EC-YA_XJC.484Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2274,370 2274,357 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d665d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2514,356 2470,356 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="g_2d65360@0" ObjectIDZND0="34042@x" ObjectIDZND1="g_2d65360@0" ObjectIDZND2="34042@x" Pin0InfoVect0LinkObjId="EC-YA_XJC.486Ld_0" Pin0InfoVect1LinkObjId="g_2d65360_0" Pin0InfoVect2LinkObjId="EC-YA_XJC.486Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d65360_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2514,356 2470,356 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d667c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2470,390 2470,356 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="34042@0" ObjectIDZND0="g_2d65360@0" ObjectIDZND1="g_2d65360@0" ObjectIDZND2="34042@x" Pin0InfoVect0LinkObjId="g_2d65360_0" Pin0InfoVect1LinkObjId="g_2d65360_0" Pin0InfoVect2LinkObjId="EC-YA_XJC.486Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-YA_XJC.486Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2470,390 2470,356 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d669b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2470,356 2468,358 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" BeginDevType2="lightningRod" EndDevType0="lightningRod" EndDevType1="load" EndDevType2="lightningRod" ObjectIDND0="g_2d65360@0" ObjectIDND1="34042@x" ObjectIDND2="g_2d20d30@0" ObjectIDZND0="g_2d65360@0" ObjectIDZND1="34042@x" ObjectIDZND2="g_2d20d30@0" Pin0InfoVect0LinkObjId="g_2d65360_0" Pin0InfoVect1LinkObjId="EC-YA_XJC.486Ld_0" Pin0InfoVect2LinkObjId="g_2d20d30_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2d65360_0" Pin1InfoVect1LinkObjId="EC-YA_XJC.486Ld_0" Pin1InfoVect2LinkObjId="g_2d20d30_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2470,356 2468,358 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2da6220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2470,356 2407,356 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2d65360@0" ObjectIDND1="34042@x" ObjectIDND2="g_2d65360@0" ObjectIDZND0="g_2d20d30@0" Pin0InfoVect0LinkObjId="g_2d20d30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2d65360_0" Pin1InfoVect1LinkObjId="EC-YA_XJC.486Ld_0" Pin1InfoVect2LinkObjId="g_2d65360_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2470,356 2407,356 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2da6410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2406,410 2406,401 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_2d20d30@1" Pin0InfoVect0LinkObjId="g_2d20d30_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c5cd00_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2406,410 2406,401 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e2c310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2705,359 2661,359 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_2e2b0a0@0" ObjectIDZND0="34048@x" ObjectIDZND1="27842@x" Pin0InfoVect0LinkObjId="EC-YA_XJC.488Ld_0" Pin0InfoVect1LinkObjId="SW-182953_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e2b0a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2705,359 2661,359 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e2c500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2661,393 2661,359 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="34048@0" ObjectIDZND0="g_2e2b0a0@0" ObjectIDZND1="27842@x" Pin0InfoVect0LinkObjId="g_2e2b0a0_0" Pin0InfoVect1LinkObjId="SW-182953_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-YA_XJC.488Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2661,393 2661,359 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e2c6f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2661,359 2661,346 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_2e2b0a0@0" ObjectIDND1="34048@x" ObjectIDZND0="27842@0" Pin0InfoVect0LinkObjId="SW-182953_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2e2b0a0_0" Pin1InfoVect1LinkObjId="EC-YA_XJC.488Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2661,359 2661,346 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d1b5f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="468,-886 496,-886 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="42352@1" Pin0InfoVect0LinkObjId="SW-258287_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c5cd00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="468,-886 496,-886 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d52620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="523,-886 541,-886 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="42352@0" ObjectIDZND0="42353@1" Pin0InfoVect0LinkObjId="SW-258288_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-258287_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="523,-886 541,-886 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d53220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="607,-886 637,-886 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2d52810@0" ObjectIDND1="42353@x" ObjectIDZND0="g_2d53600@1" Pin0InfoVect0LinkObjId="g_2d53600_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2d52810_0" Pin1InfoVect1LinkObjId="SW-258288_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="607,-886 637,-886 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d53410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="607,-886 607,-914 622,-914 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2d53600@0" ObjectIDND1="42353@x" ObjectIDZND0="g_2d52810@0" Pin0InfoVect0LinkObjId="g_2d52810_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2d53600_0" Pin1InfoVect1LinkObjId="SW-258288_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="607,-886 607,-914 622,-914 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d35d40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="468,48 495,48 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="42354@0" Pin0InfoVect0LinkObjId="SW-258356_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c5cd00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="468,48 495,48 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d997c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="562,48 562,29 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="42354@x" ObjectIDND1="g_3454ce0@0" ObjectIDZND0="g_2d35f60@0" Pin0InfoVect0LinkObjId="g_2d35f60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-258356_0" Pin1InfoVect1LinkObjId="g_3454ce0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="562,48 562,29 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d999e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="578,48 562,48 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_3454ce0@0" ObjectIDZND0="g_2d35f60@0" ObjectIDZND1="42354@x" Pin0InfoVect0LinkObjId="g_2d35f60_0" Pin0InfoVect1LinkObjId="SW-258356_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3454ce0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="578,48 562,48 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cced30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="468,-799 497,-799 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="42357@1" Pin0InfoVect0LinkObjId="SW-258369_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c5cd00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="468,-799 497,-799 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ccef20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="532,-799 551,-799 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="42357@0" ObjectIDZND0="42356@1" Pin0InfoVect0LinkObjId="SW-258368_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-258369_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="532,-799 551,-799 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ccfbe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="607,-799 637,-799 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="breaker" EndDevType0="lightningRod" ObjectIDND0="g_2ccf110@0" ObjectIDND1="42358@x" ObjectIDND2="42356@x" ObjectIDZND0="g_2d3c040@1" Pin0InfoVect0LinkObjId="g_2d3c040_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2ccf110_0" Pin1InfoVect1LinkObjId="SW-258370_0" Pin1InfoVect2LinkObjId="SW-258368_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="607,-799 637,-799 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ccfe00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="578,-799 598,-799 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="42356@0" ObjectIDZND0="g_2d3c040@0" ObjectIDZND1="g_2ccf110@0" ObjectIDZND2="42358@x" Pin0InfoVect0LinkObjId="g_2d3c040_0" Pin0InfoVect1LinkObjId="g_2ccf110_0" Pin0InfoVect2LinkObjId="SW-258370_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-258368_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="578,-799 598,-799 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cd0950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="607,-799 607,-826 622,-826 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="breaker" EndDevType0="lightningRod" ObjectIDND0="g_2d3c040@0" ObjectIDND1="42358@x" ObjectIDND2="42356@x" ObjectIDZND0="g_2ccf110@0" Pin0InfoVect0LinkObjId="g_2ccf110_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2d3c040_0" Pin1InfoVect1LinkObjId="SW-258370_0" Pin1InfoVect2LinkObjId="SW-258368_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="607,-799 607,-826 622,-826 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d3be20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="647,-760 657,-760 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="42358@1" ObjectIDZND0="g_2cd0020@0" Pin0InfoVect0LinkObjId="g_2cd0020_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-258370_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="647,-760 657,-760 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d3d0f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="607,-799 598,-799 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="g_2d3c040@0" ObjectIDND1="g_2ccf110@0" ObjectIDZND0="42358@x" ObjectIDZND1="42356@x" Pin0InfoVect0LinkObjId="SW-258370_0" Pin0InfoVect1LinkObjId="SW-258368_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2d3c040_0" Pin1InfoVect1LinkObjId="g_2ccf110_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="607,-799 598,-799 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d3d2e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="598,-799 598,-760 611,-760 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="breaker" EndDevType0="switch" ObjectIDND0="g_2d3c040@0" ObjectIDND1="g_2ccf110@0" ObjectIDND2="42356@x" ObjectIDZND0="42358@0" Pin0InfoVect0LinkObjId="SW-258370_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2d3c040_0" Pin1InfoVect1LinkObjId="g_2ccf110_0" Pin1InfoVect2LinkObjId="SW-258368_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="598,-799 598,-760 611,-760 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2dd5e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="684,128 729,128 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_2d96170@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_2c5cd00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d96170_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="684,128 729,128 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d74a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="897,-1065 897,-1041 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="42349@0" ObjectIDZND0="42380@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-258279_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="897,-1065 897,-1041 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d29330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="897,-1115 926,-1115 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="42350@x" ObjectIDND1="42349@x" ObjectIDZND0="42351@0" Pin0InfoVect0LinkObjId="SW-258281_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-258280_0" Pin1InfoVect1LinkObjId="SW-258279_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="897,-1115 926,-1115 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d29590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="897,-1140 897,-1115 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="42350@0" ObjectIDZND0="42351@x" ObjectIDZND1="42349@x" Pin0InfoVect0LinkObjId="SW-258281_0" Pin0InfoVect1LinkObjId="SW-258279_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-258280_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="897,-1140 897,-1115 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d297f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="897,-1115 897,-1092 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="42350@x" ObjectIDND1="42351@x" ObjectIDZND0="42349@1" Pin0InfoVect0LinkObjId="SW-258279_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-258280_0" Pin1InfoVect1LinkObjId="SW-258281_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="897,-1115 897,-1092 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2c5caa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="946,-1213 847,-1213 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_2c5bd70@0" ObjectIDZND0="g_2c5cd00@0" Pin0InfoVect0LinkObjId="g_2c5cd00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c5bd70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="946,-1213 847,-1213 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c5e770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="607,-886 577,-886 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_2d52810@0" ObjectIDND1="g_2d53600@0" ObjectIDZND0="42353@0" Pin0InfoVect0LinkObjId="SW-258288_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2d52810_0" Pin1InfoVect1LinkObjId="g_2d53600_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="607,-886 577,-886 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d68b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="897,-958 897,-890 897,-886 676,-886 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="42380@1" ObjectIDZND0="g_2d53600@0" Pin0InfoVect0LinkObjId="g_2d53600_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d74a00_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="897,-958 897,-890 897,-886 676,-886 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d68de0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="562,48 531,48 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" EndDevType0="switch" ObjectIDND0="g_2d35f60@0" ObjectIDND1="g_3454ce0@0" ObjectIDZND0="42354@1" Pin0InfoVect0LinkObjId="SW-258356_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2d35f60_0" Pin1InfoVect1LinkObjId="g_3454ce0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="562,48 531,48 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c87150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="468,-687 496,-687 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="42360@1" Pin0InfoVect0LinkObjId="SW-258391_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c5cd00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="468,-687 496,-687 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c873b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="532,-687 550,-687 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="42360@0" ObjectIDZND0="42359@1" Pin0InfoVect0LinkObjId="SW-258390_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-258391_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="532,-687 550,-687 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c883c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="606,-687 636,-687 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2c87610@0" ObjectIDND1="42359@x" ObjectIDND2="42361@x" ObjectIDZND0="g_2ce65d0@1" Pin0InfoVect0LinkObjId="g_2ce65d0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2c87610_0" Pin1InfoVect1LinkObjId="SW-258390_0" Pin1InfoVect2LinkObjId="SW-258392_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="606,-687 636,-687 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c88620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="577,-687 597,-687 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="42359@0" ObjectIDZND0="g_2c87610@0" ObjectIDZND1="g_2ce65d0@0" ObjectIDZND2="42361@x" Pin0InfoVect0LinkObjId="g_2c87610_0" Pin0InfoVect1LinkObjId="g_2ce65d0_0" Pin0InfoVect2LinkObjId="SW-258392_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-258390_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="577,-687 597,-687 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c89310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="606,-687 606,-714 621,-714 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2ce65d0@0" ObjectIDND1="42359@x" ObjectIDND2="42361@x" ObjectIDZND0="g_2c87610@0" Pin0InfoVect0LinkObjId="g_2c87610_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2ce65d0_0" Pin1InfoVect1LinkObjId="SW-258390_0" Pin1InfoVect2LinkObjId="SW-258392_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="606,-687 606,-714 621,-714 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ce6370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="646,-648 656,-648 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="42361@1" ObjectIDZND0="g_2c88880@0" Pin0InfoVect0LinkObjId="g_2c88880_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-258392_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="646,-648 656,-648 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ce71f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="606,-687 597,-687 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="g_2c87610@0" ObjectIDND1="g_2ce65d0@0" ObjectIDZND0="42359@x" ObjectIDZND1="42361@x" Pin0InfoVect0LinkObjId="SW-258390_0" Pin0InfoVect1LinkObjId="SW-258392_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2c87610_0" Pin1InfoVect1LinkObjId="g_2ce65d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="606,-687 597,-687 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ce7450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="597,-687 597,-648 610,-648 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="breaker" EndDevType0="switch" ObjectIDND0="g_2c87610@0" ObjectIDND1="g_2ce65d0@0" ObjectIDND2="42359@x" ObjectIDZND0="42361@0" Pin0InfoVect0LinkObjId="SW-258392_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2c87610_0" Pin1InfoVect1LinkObjId="g_2ce65d0_0" Pin1InfoVect2LinkObjId="SW-258390_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="597,-687 597,-648 610,-648 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d0ce20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="468,-577 497,-577 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="42363@1" Pin0InfoVect0LinkObjId="SW-258413_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c5cd00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="468,-577 497,-577 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d0d080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="533,-577 551,-577 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="42363@0" ObjectIDZND0="42362@1" Pin0InfoVect0LinkObjId="SW-258412_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-258413_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="533,-577 551,-577 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d0df00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="607,-577 637,-577 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2d0d2e0@0" ObjectIDND1="42362@x" ObjectIDND2="42364@x" ObjectIDZND0="g_2d4d650@1" Pin0InfoVect0LinkObjId="g_2d4d650_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2d0d2e0_0" Pin1InfoVect1LinkObjId="SW-258412_0" Pin1InfoVect2LinkObjId="SW-258414_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="607,-577 637,-577 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d0e160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="578,-577 598,-577 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="42362@0" ObjectIDZND0="g_2d0d2e0@0" ObjectIDZND1="g_2d4d650@0" ObjectIDZND2="42364@x" Pin0InfoVect0LinkObjId="g_2d0d2e0_0" Pin0InfoVect1LinkObjId="g_2d4d650_0" Pin0InfoVect2LinkObjId="SW-258414_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-258412_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="578,-577 598,-577 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d0ee50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="607,-577 607,-604 622,-604 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2d4d650@0" ObjectIDND1="42362@x" ObjectIDND2="42364@x" ObjectIDZND0="g_2d0d2e0@0" Pin0InfoVect0LinkObjId="g_2d0d2e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2d4d650_0" Pin1InfoVect1LinkObjId="SW-258412_0" Pin1InfoVect2LinkObjId="SW-258414_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="607,-577 607,-604 622,-604 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d4d3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="647,-538 657,-538 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="42364@1" ObjectIDZND0="g_2d0e3c0@0" Pin0InfoVect0LinkObjId="g_2d0e3c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-258414_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="647,-538 657,-538 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d4e760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="607,-577 598,-577 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="g_2d0d2e0@0" ObjectIDND1="g_2d4d650@0" ObjectIDZND0="42362@x" ObjectIDZND1="42364@x" Pin0InfoVect0LinkObjId="SW-258412_0" Pin0InfoVect1LinkObjId="SW-258414_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2d0d2e0_0" Pin1InfoVect1LinkObjId="g_2d4d650_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="607,-577 598,-577 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d4e950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="598,-577 598,-538 611,-538 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="breaker" EndDevType0="switch" ObjectIDND0="g_2d0d2e0@0" ObjectIDND1="g_2d4d650@0" ObjectIDND2="42362@x" ObjectIDZND0="42364@0" Pin0InfoVect0LinkObjId="SW-258414_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2d0d2e0_0" Pin1InfoVect1LinkObjId="g_2d4d650_0" Pin1InfoVect2LinkObjId="SW-258412_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="598,-577 598,-538 611,-538 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d5dbe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="468,-464 497,-464 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="42366@1" Pin0InfoVect0LinkObjId="SW-258435_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c5cd00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="468,-464 497,-464 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d5de40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="533,-464 551,-464 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="42366@0" ObjectIDZND0="42365@1" Pin0InfoVect0LinkObjId="SW-258434_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-258435_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="533,-464 551,-464 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d5ed90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="607,-464 637,-464 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2d5e0a0@0" ObjectIDND1="42365@x" ObjectIDND2="42367@x" ObjectIDZND0="g_2d21550@1" Pin0InfoVect0LinkObjId="g_2d21550_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2d5e0a0_0" Pin1InfoVect1LinkObjId="SW-258434_0" Pin1InfoVect2LinkObjId="SW-258436_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="607,-464 637,-464 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d5eff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="578,-464 598,-464 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="42365@0" ObjectIDZND0="g_2d5e0a0@0" ObjectIDZND1="g_2d21550@0" ObjectIDZND2="42367@x" Pin0InfoVect0LinkObjId="g_2d5e0a0_0" Pin0InfoVect1LinkObjId="g_2d21550_0" Pin0InfoVect2LinkObjId="SW-258436_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-258434_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="578,-464 598,-464 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d5fca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="607,-464 607,-491 622,-491 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2d21550@0" ObjectIDND1="42365@x" ObjectIDND2="42367@x" ObjectIDZND0="g_2d5e0a0@0" Pin0InfoVect0LinkObjId="g_2d5e0a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2d21550_0" Pin1InfoVect1LinkObjId="SW-258434_0" Pin1InfoVect2LinkObjId="SW-258436_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="607,-464 607,-491 622,-491 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d212f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="647,-425 657,-425 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="42367@1" ObjectIDZND0="g_2d5f250@0" Pin0InfoVect0LinkObjId="g_2d5f250_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-258436_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="647,-425 657,-425 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d22930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="607,-464 598,-464 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="g_2d5e0a0@0" ObjectIDND1="g_2d21550@0" ObjectIDZND0="42365@x" ObjectIDZND1="42367@x" Pin0InfoVect0LinkObjId="SW-258434_0" Pin0InfoVect1LinkObjId="SW-258436_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2d5e0a0_0" Pin1InfoVect1LinkObjId="g_2d21550_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="607,-464 598,-464 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d22b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="598,-464 598,-425 611,-425 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="breaker" EndDevType0="switch" ObjectIDND0="g_2d5e0a0@0" ObjectIDND1="g_2d21550@0" ObjectIDND2="42365@x" ObjectIDZND0="42367@0" Pin0InfoVect0LinkObjId="SW-258436_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2d5e0a0_0" Pin1InfoVect1LinkObjId="g_2d21550_0" Pin1InfoVect2LinkObjId="SW-258434_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="598,-464 598,-425 611,-425 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d5a010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="468,-353 495,-353 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="42369@1" Pin0InfoVect0LinkObjId="SW-258457_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c5cd00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="468,-353 495,-353 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d5a270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="530,-353 549,-353 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="42369@0" ObjectIDZND0="42368@1" Pin0InfoVect0LinkObjId="SW-258456_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-258457_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="530,-353 549,-353 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d5b280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="605,-353 635,-353 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2d5a4d0@0" ObjectIDND1="42368@x" ObjectIDND2="42370@x" ObjectIDZND0="g_2cebc30@1" Pin0InfoVect0LinkObjId="g_2cebc30_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2d5a4d0_0" Pin1InfoVect1LinkObjId="SW-258456_0" Pin1InfoVect2LinkObjId="SW-258458_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="605,-353 635,-353 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d5b4e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="576,-353 596,-353 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="42368@0" ObjectIDZND0="g_2d5a4d0@0" ObjectIDZND1="g_2cebc30@0" ObjectIDZND2="42370@x" Pin0InfoVect0LinkObjId="g_2d5a4d0_0" Pin0InfoVect1LinkObjId="g_2cebc30_0" Pin0InfoVect2LinkObjId="SW-258458_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-258456_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="576,-353 596,-353 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ce9230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="605,-353 605,-380 620,-380 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2cebc30@0" ObjectIDND1="42368@x" ObjectIDND2="42370@x" ObjectIDZND0="g_2d5a4d0@0" Pin0InfoVect0LinkObjId="g_2d5a4d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2cebc30_0" Pin1InfoVect1LinkObjId="SW-258456_0" Pin1InfoVect2LinkObjId="SW-258458_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="605,-353 605,-380 620,-380 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ceb9d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="645,-314 655,-314 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="42370@1" ObjectIDZND0="g_2d5b740@0" Pin0InfoVect0LinkObjId="g_2d5b740_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-258458_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="645,-314 655,-314 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ced050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="605,-353 596,-353 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="g_2d5a4d0@0" ObjectIDND1="g_2cebc30@0" ObjectIDZND0="42368@x" ObjectIDZND1="42370@x" Pin0InfoVect0LinkObjId="SW-258456_0" Pin0InfoVect1LinkObjId="SW-258458_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2d5a4d0_0" Pin1InfoVect1LinkObjId="g_2cebc30_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="605,-353 596,-353 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ced240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="596,-353 596,-314 609,-314 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="breaker" EndDevType0="switch" ObjectIDND0="g_2d5a4d0@0" ObjectIDND1="g_2cebc30@0" ObjectIDND2="42368@x" ObjectIDZND0="42370@0" Pin0InfoVect0LinkObjId="SW-258458_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2d5a4d0_0" Pin1InfoVect1LinkObjId="g_2cebc30_0" Pin1InfoVect2LinkObjId="SW-258456_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="596,-353 596,-314 609,-314 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c81060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="468,-255 498,-255 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="42372@1" Pin0InfoVect0LinkObjId="SW-258479_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c5cd00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="468,-255 498,-255 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c812c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="530,-255 548,-255 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="42372@0" ObjectIDZND0="42371@1" Pin0InfoVect0LinkObjId="SW-258478_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-258479_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="530,-255 548,-255 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c822d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="608,-255 638,-255 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2c81520@0" ObjectIDND1="42371@x" ObjectIDND2="42373@x" ObjectIDZND0="g_2c85c20@1" Pin0InfoVect0LinkObjId="g_2c85c20_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2c81520_0" Pin1InfoVect1LinkObjId="SW-258478_0" Pin1InfoVect2LinkObjId="SW-258480_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="608,-255 638,-255 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c82530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="579,-255 599,-255 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="42371@0" ObjectIDZND0="g_2c81520@0" ObjectIDZND1="g_2c85c20@0" ObjectIDZND2="42373@x" Pin0InfoVect0LinkObjId="g_2c81520_0" Pin0InfoVect1LinkObjId="g_2c85c20_0" Pin0InfoVect2LinkObjId="SW-258480_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-258478_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="579,-255 599,-255 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c83220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="608,-255 608,-282 623,-282 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2c85c20@0" ObjectIDND1="42371@x" ObjectIDND2="42373@x" ObjectIDZND0="g_2c81520@0" Pin0InfoVect0LinkObjId="g_2c81520_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2c85c20_0" Pin1InfoVect1LinkObjId="SW-258478_0" Pin1InfoVect2LinkObjId="SW-258480_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="608,-255 608,-282 623,-282 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c859c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="648,-216 658,-216 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="42373@1" ObjectIDZND0="g_2c82790@0" Pin0InfoVect0LinkObjId="g_2c82790_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-258480_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="648,-216 658,-216 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cee720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="608,-255 599,-255 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="g_2c81520@0" ObjectIDND1="g_2c85c20@0" ObjectIDZND0="42371@x" ObjectIDZND1="42373@x" Pin0InfoVect0LinkObjId="SW-258478_0" Pin0InfoVect1LinkObjId="SW-258480_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2c81520_0" Pin1InfoVect1LinkObjId="g_2c85c20_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="608,-255 599,-255 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cee910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="599,-255 599,-216 612,-216 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="breaker" EndDevType0="switch" ObjectIDND0="g_2c81520@0" ObjectIDND1="g_2c85c20@0" ObjectIDND2="42371@x" ObjectIDZND0="42373@0" Pin0InfoVect0LinkObjId="SW-258480_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2c81520_0" Pin1InfoVect1LinkObjId="g_2c85c20_0" Pin1InfoVect2LinkObjId="SW-258478_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="599,-255 599,-216 612,-216 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cc6540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="468,-146 498,-146 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="42375@1" Pin0InfoVect0LinkObjId="SW-258501_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c5cd00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="468,-146 498,-146 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cc67a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="534,-146 552,-146 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="42375@0" ObjectIDZND0="42374@1" Pin0InfoVect0LinkObjId="SW-258500_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-258501_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="534,-146 552,-146 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cc77b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="628,-146 658,-146 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2cc6a00@0" ObjectIDND1="42374@x" ObjectIDND2="42376@x" ObjectIDZND0="g_2d030e0@1" Pin0InfoVect0LinkObjId="g_2d030e0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2cc6a00_0" Pin1InfoVect1LinkObjId="SW-258500_0" Pin1InfoVect2LinkObjId="SW-258502_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="628,-146 658,-146 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cc7a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="579,-146 599,-146 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="42374@0" ObjectIDZND0="g_2cc6a00@0" ObjectIDZND1="g_2d030e0@0" ObjectIDZND2="42376@x" Pin0InfoVect0LinkObjId="g_2cc6a00_0" Pin0InfoVect1LinkObjId="g_2d030e0_0" Pin0InfoVect2LinkObjId="SW-258502_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-258500_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="579,-146 599,-146 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cc8700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="628,-146 628,-169 643,-169 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2d030e0@0" ObjectIDND1="42374@x" ObjectIDND2="42376@x" ObjectIDZND0="g_2cc6a00@0" Pin0InfoVect0LinkObjId="g_2cc6a00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2d030e0_0" Pin1InfoVect1LinkObjId="SW-258500_0" Pin1InfoVect2LinkObjId="SW-258502_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="628,-146 628,-169 643,-169 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d02e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="648,-103 658,-103 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="42376@1" ObjectIDZND0="g_2cc7c70@0" Pin0InfoVect0LinkObjId="g_2cc7c70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-258502_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="648,-103 658,-103 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d04180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="628,-146 599,-146 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="g_2cc6a00@0" ObjectIDND1="g_2d030e0@0" ObjectIDZND0="42374@x" ObjectIDZND1="42376@x" Pin0InfoVect0LinkObjId="SW-258500_0" Pin0InfoVect1LinkObjId="SW-258502_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2cc6a00_0" Pin1InfoVect1LinkObjId="g_2d030e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="628,-146 599,-146 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d04370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="599,-146 599,-103 612,-103 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="breaker" EndDevType0="switch" ObjectIDND0="g_2cc6a00@0" ObjectIDND1="g_2d030e0@0" ObjectIDND2="42374@x" ObjectIDZND0="42376@0" Pin0InfoVect0LinkObjId="SW-258502_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2cc6a00_0" Pin1InfoVect1LinkObjId="g_2d030e0_0" Pin1InfoVect2LinkObjId="SW-258500_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="599,-146 599,-103 612,-103 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c45a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="468,-35 499,-35 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="42378@1" Pin0InfoVect0LinkObjId="SW-258522_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c5cd00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="468,-35 499,-35 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c45cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="533,-35 551,-35 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="42378@0" ObjectIDZND0="42377@1" Pin0InfoVect0LinkObjId="SW-258521_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-258522_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="533,-35 551,-35 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c46ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="609,-35 639,-35 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2c45f30@0" ObjectIDND1="42377@x" ObjectIDND2="42379@x" ObjectIDZND0="g_2c4a630@1" Pin0InfoVect0LinkObjId="g_2c4a630_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2c45f30_0" Pin1InfoVect1LinkObjId="SW-258521_0" Pin1InfoVect2LinkObjId="SW-258523_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="609,-35 639,-35 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c46f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="580,-35 600,-35 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="42377@0" ObjectIDZND0="g_2c45f30@0" ObjectIDZND1="g_2c4a630@0" ObjectIDZND2="42379@x" Pin0InfoVect0LinkObjId="g_2c45f30_0" Pin0InfoVect1LinkObjId="g_2c4a630_0" Pin0InfoVect2LinkObjId="SW-258523_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-258521_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="580,-35 600,-35 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c47c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="609,-35 609,-62 624,-62 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2c4a630@0" ObjectIDND1="42377@x" ObjectIDND2="42379@x" ObjectIDZND0="g_2c45f30@0" Pin0InfoVect0LinkObjId="g_2c45f30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2c4a630_0" Pin1InfoVect1LinkObjId="SW-258521_0" Pin1InfoVect2LinkObjId="SW-258523_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="609,-35 609,-62 624,-62 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c4a3d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="649,4 659,4 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="42379@1" ObjectIDZND0="g_2c471a0@0" Pin0InfoVect0LinkObjId="g_2c471a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-258523_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="649,4 659,4 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cd1e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="609,-35 600,-35 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="g_2c45f30@0" ObjectIDND1="g_2c4a630@0" ObjectIDZND0="42377@x" ObjectIDZND1="42379@x" Pin0InfoVect0LinkObjId="SW-258521_0" Pin0InfoVect1LinkObjId="SW-258523_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2c45f30_0" Pin1InfoVect1LinkObjId="g_2c4a630_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="609,-35 600,-35 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cd2040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="600,-35 600,4 613,4 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="breaker" EndDevType0="switch" ObjectIDND0="g_2c45f30@0" ObjectIDND1="g_2c4a630@0" ObjectIDND2="42377@x" ObjectIDZND0="42379@0" Pin0InfoVect0LinkObjId="SW-258523_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2c45f30_0" Pin1InfoVect1LinkObjId="g_2c4a630_0" Pin1InfoVect2LinkObjId="SW-258521_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="600,-35 600,4 613,4 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cdbe90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="468,128 495,128 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="42355@1" Pin0InfoVect0LinkObjId="SW-258359_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c5cd00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="468,128 495,128 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bff740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="562,128 531,128 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="42355@0" Pin0InfoVect0LinkObjId="SW-258359_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c5cd00_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="562,128 531,128 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2bff9a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="645,128 615,128 615,101 630,101 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2d96170@1" ObjectIDZND0="g_2dd6000@0" Pin0InfoVect0LinkObjId="g_2dd6000_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d96170_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="645,128 615,128 615,101 630,101 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c0ac50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="897,-1232 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="voltageTransformer" EndDevType0="switch" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="42350@x" ObjectIDND1="25805@x" ObjectIDND2="g_2c19800@0" ObjectIDZND0="42350@x" ObjectIDZND1="25805@x" ObjectIDZND2="g_2c19800@0" Pin0InfoVect0LinkObjId="SW-258280_0" Pin0InfoVect1LinkObjId="SW-147655_0" Pin0InfoVect2LinkObjId="g_2c19800_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-258280_0" Pin1InfoVect1LinkObjId="SW-147655_0" Pin1InfoVect2LinkObjId="g_2c19800_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="897,-1232 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c0ae80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="897,-1232 897,-1176 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="voltageTransformer" EndDevType0="switch" ObjectIDND0="42350@x" ObjectIDND1="25805@x" ObjectIDND2="g_2c19800@0" ObjectIDZND0="42350@1" Pin0InfoVect0LinkObjId="SW-258280_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-258280_0" Pin1InfoVect1LinkObjId="SW-147655_0" Pin1InfoVect2LinkObjId="g_2c19800_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="897,-1232 897,-1176 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c3ee00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1739,-725 1739,-739 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="25791@0" ObjectIDZND0="25796@0" Pin0InfoVect0LinkObjId="SW-147612_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3416bf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1739,-725 1739,-739 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c3eff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1739,-775 1739,-791 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25796@1" ObjectIDZND0="25795@0" Pin0InfoVect0LinkObjId="SW-147610_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147612_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1739,-775 1739,-791 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c3f1e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1739,-818 1739,-838 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25795@1" ObjectIDZND0="25798@0" Pin0InfoVect0LinkObjId="SW-147614_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147610_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1739,-818 1739,-838 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f63080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1761,-891 1739,-891 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="powerLine" ObjectIDND0="25797@0" ObjectIDZND0="25798@x" ObjectIDZND1="g_2f63a00@0" ObjectIDZND2="38083@1" Pin0InfoVect0LinkObjId="SW-147614_0" Pin0InfoVect1LinkObjId="g_2f63a00_0" Pin0InfoVect2LinkObjId="g_2f637a0_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147613_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1761,-891 1739,-891 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f632e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1739,-874 1739,-891 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="powerLine" ObjectIDND0="25798@1" ObjectIDZND0="25797@x" ObjectIDZND1="g_2f63a00@0" ObjectIDZND2="38083@1" Pin0InfoVect0LinkObjId="SW-147613_0" Pin0InfoVect1LinkObjId="g_2f63a00_0" Pin0InfoVect2LinkObjId="g_2f637a0_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147614_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1739,-874 1739,-891 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f63540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1739,-891 1739,-938 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" EndDevType1="powerLine" EndDevType2="lightningRod" ObjectIDND0="25797@x" ObjectIDND1="25798@x" ObjectIDZND0="g_2f63a00@0" ObjectIDZND1="38083@1" ObjectIDZND2="g_345c850@0" Pin0InfoVect0LinkObjId="g_2f63a00_0" Pin0InfoVect1LinkObjId="g_2f637a0_1" Pin0InfoVect2LinkObjId="g_345c850_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147613_0" Pin1InfoVect1LinkObjId="SW-147614_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1739,-891 1739,-938 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f637a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1739,-938 1739,-957 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="voltageTransformer" EndDevType0="powerLine" ObjectIDND0="25797@x" ObjectIDND1="25798@x" ObjectIDND2="g_2f63a00@0" ObjectIDZND0="38083@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-147613_0" Pin1InfoVect1LinkObjId="SW-147614_0" Pin1InfoVect2LinkObjId="g_2f63a00_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1739,-938 1739,-957 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f64ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1696,-938 1739,-938 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" EndDevType1="switch" EndDevType2="powerLine" ObjectIDND0="g_2f63a00@0" ObjectIDZND0="25797@x" ObjectIDZND1="25798@x" ObjectIDZND2="38083@1" Pin0InfoVect0LinkObjId="SW-147613_0" Pin0InfoVect1LinkObjId="SW-147614_0" Pin0InfoVect2LinkObjId="g_2f637a0_1" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f63a00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1696,-938 1739,-938 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f67a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2155,-818 2155,-838 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25799@1" ObjectIDZND0="25802@0" Pin0InfoVect0LinkObjId="SW-147635_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147631_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2155,-818 2155,-838 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f6cad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2177,-891 2155,-891 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="powerLine" ObjectIDND0="25801@0" ObjectIDZND0="25802@x" ObjectIDZND1="g_2f6d450@0" ObjectIDZND2="34316@1" Pin0InfoVect0LinkObjId="SW-147635_0" Pin0InfoVect1LinkObjId="g_2f6d450_0" Pin0InfoVect2LinkObjId="g_2f6d1f0_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147634_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2177,-891 2155,-891 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f6cd30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2155,-874 2155,-891 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="powerLine" ObjectIDND0="25802@1" ObjectIDZND0="25801@x" ObjectIDZND1="g_2f6d450@0" ObjectIDZND2="34316@1" Pin0InfoVect0LinkObjId="SW-147634_0" Pin0InfoVect1LinkObjId="g_2f6d450_0" Pin0InfoVect2LinkObjId="g_2f6d1f0_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147635_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2155,-874 2155,-891 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f6cf90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2155,-891 2155,-938 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" EndDevType1="powerLine" EndDevType2="lightningRod" ObjectIDND0="25801@x" ObjectIDND1="25802@x" ObjectIDZND0="g_2f6d450@0" ObjectIDZND1="34316@1" ObjectIDZND2="g_345d2d0@0" Pin0InfoVect0LinkObjId="g_2f6d450_0" Pin0InfoVect1LinkObjId="g_2f6d1f0_1" Pin0InfoVect2LinkObjId="g_345d2d0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147634_0" Pin1InfoVect1LinkObjId="SW-147635_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2155,-891 2155,-938 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f6d1f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2155,-938 2155,-960 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="voltageTransformer" EndDevType0="powerLine" ObjectIDND0="25801@x" ObjectIDND1="25802@x" ObjectIDND2="g_2f6d450@0" ObjectIDZND0="34316@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-147634_0" Pin1InfoVect1LinkObjId="SW-147635_0" Pin1InfoVect2LinkObjId="g_2f6d450_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2155,-938 2155,-960 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f6ea40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2112,-938 2155,-938 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" EndDevType1="switch" EndDevType2="powerLine" ObjectIDND0="g_2f6d450@0" ObjectIDZND0="25801@x" ObjectIDZND1="25802@x" ObjectIDZND2="34316@1" Pin0InfoVect0LinkObjId="SW-147634_0" Pin0InfoVect1LinkObjId="SW-147635_0" Pin0InfoVect2LinkObjId="g_2f6d1f0_1" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f6d450_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2112,-938 2155,-938 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c10fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2155,-725 2155,-738 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="25791@0" ObjectIDZND0="25800@0" Pin0InfoVect0LinkObjId="SW-147633_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3416bf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2155,-725 2155,-738 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c11210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2155,-791 2155,-774 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25799@0" ObjectIDZND0="25800@1" Pin0InfoVect0LinkObjId="SW-147633_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147631_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2155,-791 2155,-774 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c11470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2179,-938 2155,-938 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="g_345d2d0@0" ObjectIDZND0="25801@x" ObjectIDZND1="25802@x" ObjectIDZND2="g_2f6d450@0" Pin0InfoVect0LinkObjId="SW-147634_0" Pin0InfoVect1LinkObjId="SW-147635_0" Pin0InfoVect2LinkObjId="g_2f6d450_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_345d2d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2179,-938 2155,-938 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c116d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1739,-938 1763,-938 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="25797@x" ObjectIDND1="25798@x" ObjectIDND2="g_2f63a00@0" ObjectIDZND0="g_345c850@0" Pin0InfoVect0LinkObjId="g_345c850_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-147613_0" Pin1InfoVect1LinkObjId="SW-147614_0" Pin1InfoVect2LinkObjId="g_2f63a00_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1739,-938 1763,-938 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c14010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2568,-822 2568,-847 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25803@1" ObjectIDZND0="25806@0" Pin0InfoVect0LinkObjId="SW-147656_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147652_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2568,-822 2568,-847 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c190e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2594,-932 2568,-932 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="25805@0" ObjectIDZND0="42350@x" ObjectIDZND1="42350@x" ObjectIDZND2="25805@x" Pin0InfoVect0LinkObjId="SW-258280_0" Pin0InfoVect1LinkObjId="SW-258280_0" Pin0InfoVect2LinkObjId="SW-147655_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147655_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2594,-932 2568,-932 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c195a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2568,-1001 2568,-1012 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_2c19800@0" ObjectIDND1="g_345e000@0" ObjectIDND2="25805@x" ObjectIDZND0="37772@1" Pin0InfoVect0LinkObjId="g_2c1adf0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2c19800_0" Pin1InfoVect1LinkObjId="g_345e000_0" Pin1InfoVect2LinkObjId="SW-147655_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2568,-1001 2568,-1012 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c1adf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2529,-1001 2568,-1001 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="powerLine" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_2c19800@0" ObjectIDZND0="37772@1" ObjectIDZND1="g_345e000@0" ObjectIDZND2="25805@x" Pin0InfoVect0LinkObjId="g_2c195a0_1" Pin0InfoVect1LinkObjId="g_345e000_0" Pin0InfoVect2LinkObjId="SW-147655_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c19800_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2529,-1001 2568,-1001 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c1d850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2568,-725 2568,-738 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="25791@0" ObjectIDZND0="25804@0" Pin0InfoVect0LinkObjId="SW-147654_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3416bf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2568,-725 2568,-738 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c1dab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2568,-795 2568,-774 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25803@0" ObjectIDZND0="25804@1" Pin0InfoVect0LinkObjId="SW-147654_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147652_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2568,-795 2568,-774 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c1dd10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2596,-1001 2568,-1001 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" EndDevType1="powerLine" EndDevType2="switch" ObjectIDND0="g_345e000@0" ObjectIDZND0="g_2c19800@0" ObjectIDZND1="37772@1" ObjectIDZND2="25805@x" Pin0InfoVect0LinkObjId="g_2c19800_0" Pin0InfoVect1LinkObjId="g_2c195a0_1" Pin0InfoVect2LinkObjId="SW-147655_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_345e000_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2596,-1001 2568,-1001 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c214c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1808,-725 1808,-715 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="25791@0" ObjectIDZND0="25808@1" Pin0InfoVect0LinkObjId="SW-147676_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3416bf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1808,-725 1808,-715 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c21720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1808,-679 1808,-667 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25808@0" ObjectIDZND0="25807@1" Pin0InfoVect0LinkObjId="SW-147673_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147676_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1808,-679 1808,-667 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c21980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1808,-640 1808,-628 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="25807@0" ObjectIDZND0="25862@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147673_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1808,-640 1808,-628 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c21be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1808,-549 1808,-537 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="25862@0" ObjectIDZND0="g_2c1df70@0" Pin0InfoVect0LinkObjId="g_2c1df70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c21980_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1808,-549 1808,-537 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c21e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1808,-484 1808,-470 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2c1df70@1" ObjectIDZND0="25813@1" Pin0InfoVect0LinkObjId="SW-147752_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c1df70_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1808,-484 1808,-470 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c220a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1808,-434 1808,-420 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25813@0" ObjectIDZND0="25812@1" Pin0InfoVect0LinkObjId="SW-147749_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147752_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1808,-434 1808,-420 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c250a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1808,-383 1878,-383 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="25812@x" ObjectIDND1="25814@x" ObjectIDZND0="25815@0" Pin0InfoVect0LinkObjId="SW-147754_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147749_0" Pin1InfoVect1LinkObjId="SW-147753_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1808,-383 1878,-383 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c25300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1808,-393 1808,-383 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="25812@0" ObjectIDZND0="25815@x" ObjectIDZND1="25814@x" Pin0InfoVect0LinkObjId="SW-147754_0" Pin0InfoVect1LinkObjId="SW-147753_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147749_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1808,-393 1808,-383 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c25560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1808,-383 1808,-373 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="25815@x" ObjectIDND1="25812@x" ObjectIDZND0="25814@1" Pin0InfoVect0LinkObjId="SW-147753_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147754_0" Pin1InfoVect1LinkObjId="SW-147749_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1808,-383 1808,-373 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c8c3d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2504,-725 2504,-715 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="25791@0" ObjectIDZND0="25810@1" Pin0InfoVect0LinkObjId="SW-147711_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3416bf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2504,-725 2504,-715 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c8c630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2504,-679 2504,-667 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25810@0" ObjectIDZND0="25809@1" Pin0InfoVect0LinkObjId="SW-147708_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147711_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2504,-679 2504,-667 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c8c890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2504,-640 2504,-628 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="25809@0" ObjectIDZND0="25863@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147708_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2504,-640 2504,-628 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c8caf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2504,-549 2504,-537 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="25863@0" ObjectIDZND0="g_2c2e960@0" Pin0InfoVect0LinkObjId="g_2c2e960_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c8c890_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2504,-549 2504,-537 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c8cd50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2504,-434 2504,-420 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25817@0" ObjectIDZND0="25816@1" Pin0InfoVect0LinkObjId="SW-147755_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147758_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2504,-434 2504,-420 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c8fd50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2504,-383 2574,-383 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="25816@x" ObjectIDND1="25818@x" ObjectIDZND0="25819@0" Pin0InfoVect0LinkObjId="SW-147760_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147755_0" Pin1InfoVect1LinkObjId="SW-147759_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2504,-383 2574,-383 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c8ffb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2504,-393 2504,-383 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="25816@0" ObjectIDZND0="25819@x" ObjectIDZND1="25818@x" Pin0InfoVect0LinkObjId="SW-147760_0" Pin0InfoVect1LinkObjId="SW-147759_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147755_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2504,-393 2504,-383 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c90210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2504,-383 2504,-373 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="25819@x" ObjectIDND1="25816@x" ObjectIDZND0="25818@1" Pin0InfoVect0LinkObjId="SW-147759_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147760_0" Pin1InfoVect1LinkObjId="SW-147755_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2504,-383 2504,-373 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c90ae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2504,-484 2504,-470 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2c2e960@1" ObjectIDZND0="25817@1" Pin0InfoVect0LinkObjId="SW-147758_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c2e960_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2504,-484 2504,-470 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c92880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1951,-725 1951,-701 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="lightningRod" ObjectIDND0="25791@0" ObjectIDZND0="g_2c90cd0@1" Pin0InfoVect0LinkObjId="g_2c90cd0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3416bf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1951,-725 1951,-701 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c92aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1951,-656 1951,-639 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_2c90cd0@0" ObjectIDZND0="25864@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c90cd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1951,-656 1951,-639 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c97f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2155,-656 2202,-656 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_33eee20@0" ObjectIDZND0="g_2c971e0@0" Pin0InfoVect0LinkObjId="g_2c971e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2c5cd00_0" Pin1InfoVect1LinkObjId="g_33eee20_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2155,-656 2202,-656 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c98170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2155,-667 2155,-656 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_2c971e0@0" ObjectIDZND1="g_33eee20@0" Pin0InfoVect0LinkObjId="g_2c971e0_0" Pin0InfoVect1LinkObjId="g_33eee20_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c5cd00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2155,-667 2155,-656 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ca6720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1475,-81 1456,-81 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="capacitor" ObjectIDND0="g_2ca6020@0" ObjectIDZND0="g_3430a70@0" ObjectIDZND1="41921@x" Pin0InfoVect0LinkObjId="g_3430a70_0" Pin0InfoVect1LinkObjId="CB-YA_XJC.YA_XJC_Cb1_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ca6020_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1475,-81 1456,-81 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33b39d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2101,-259 2041,-259 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" ObjectIDND0="25821@0" ObjectIDZND0="25820@x" ObjectIDZND1="g_345ed30@0" Pin0InfoVect0LinkObjId="SW-265405_0" Pin0InfoVect1LinkObjId="g_345ed30_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-265406_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2101,-259 2041,-259 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33b3c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2041,-267 2041,-259 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" ObjectIDND0="25820@0" ObjectIDZND0="25821@x" ObjectIDZND1="g_345ed30@0" Pin0InfoVect0LinkObjId="SW-265406_0" Pin0InfoVect1LinkObjId="g_345ed30_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-265405_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2041,-267 2041,-259 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33b3e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2041,-259 2041,-217 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" ObjectIDND0="25821@x" ObjectIDND1="25820@x" ObjectIDZND0="g_345ed30@0" Pin0InfoVect0LinkObjId="g_345ed30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-265406_0" Pin1InfoVect1LinkObjId="SW-265405_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2041,-259 2041,-217 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33b7280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2243,-316 2243,-302 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="25793@0" ObjectIDZND0="25822@1" Pin0InfoVect0LinkObjId="SW-265409_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34008f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2243,-316 2243,-302 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33b9e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2303,-258 2243,-258 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" ObjectIDND0="25823@0" ObjectIDZND0="25822@x" ObjectIDZND1="g_3461a90@0" Pin0InfoVect0LinkObjId="SW-265409_0" Pin0InfoVect1LinkObjId="g_3461a90_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-265408_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2303,-258 2243,-258 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33ba0f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2243,-266 2243,-258 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" ObjectIDND0="25822@0" ObjectIDZND0="25823@x" ObjectIDZND1="g_3461a90@0" Pin0InfoVect0LinkObjId="SW-265408_0" Pin0InfoVect1LinkObjId="g_3461a90_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-265409_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2243,-266 2243,-258 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33ba350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2243,-258 2243,-216 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" ObjectIDND0="25823@x" ObjectIDND1="25822@x" ObjectIDZND0="g_3461a90@0" Pin0InfoVect0LinkObjId="g_3461a90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-265408_0" Pin1InfoVect1LinkObjId="SW-265409_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2243,-258 2243,-216 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33c1680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2429,-268 2429,-260 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="25825@0" ObjectIDZND0="25824@x" ObjectIDZND1="25827@x" Pin0InfoVect0LinkObjId="SW-147765_0" Pin0InfoVect1LinkObjId="SW-147769_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147767_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2429,-268 2429,-260 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33c18e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2429,-260 2429,-252 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="25825@x" ObjectIDND1="25827@x" ObjectIDZND0="25824@1" Pin0InfoVect0LinkObjId="SW-147765_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147767_0" Pin1InfoVect1LinkObjId="SW-147769_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2429,-260 2429,-252 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33ef490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2155,-656 2155,-636 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2c971e0@0" ObjectIDND1="0@x" ObjectIDZND0="g_33eee20@0" Pin0InfoVect0LinkObjId="g_33eee20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2c971e0_0" Pin1InfoVect1LinkObjId="g_2c5cd00_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2155,-656 2155,-636 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33ef6c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2155,-605 2155,-583 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_33eee20@1" ObjectIDZND0="g_2c95450@0" Pin0InfoVect0LinkObjId="g_2c95450_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33eee20_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2155,-605 2155,-583 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33ef920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1759,-260 1721,-260 1721,-235 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="25831@x" ObjectIDND1="25829@x" ObjectIDZND0="25832@0" Pin0InfoVect0LinkObjId="SW-147785_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147784_0" Pin1InfoVect1LinkObjId="SW-147781_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1759,-260 1721,-260 1721,-235 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33f2d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1610,-260 1572,-260 1572,-235 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="25841@x" ObjectIDND1="25839@x" ObjectIDZND0="25842@0" Pin0InfoVect0LinkObjId="SW-147817_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147816_0" Pin1InfoVect1LinkObjId="SW-147813_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1610,-260 1572,-260 1572,-235 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33f6270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1610,-225 1610,-210 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25839@0" ObjectIDZND0="25840@1" Pin0InfoVect0LinkObjId="SW-147815_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147813_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1610,-225 1610,-210 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33f64d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1610,-268 1610,-260 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="25841@0" ObjectIDZND0="25842@x" ObjectIDZND1="25839@x" Pin0InfoVect0LinkObjId="SW-147817_0" Pin0InfoVect1LinkObjId="SW-147813_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147816_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1610,-268 1610,-260 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33f6730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1610,-260 1610,-252 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="25842@x" ObjectIDND1="25841@x" ObjectIDZND0="25839@1" Pin0InfoVect0LinkObjId="SW-147813_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147817_0" Pin1InfoVect1LinkObjId="SW-147816_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1610,-260 1610,-252 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33f6990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1760,-225 1760,-210 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25829@0" ObjectIDZND0="25830@1" Pin0InfoVect0LinkObjId="SW-147783_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147781_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1760,-225 1760,-210 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33f6bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1916,-225 1916,-210 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25849@0" ObjectIDZND0="25850@1" Pin0InfoVect0LinkObjId="SW-147848_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147846_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1916,-225 1916,-210 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33f6e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1760,-268 1760,-260 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="25831@0" ObjectIDZND0="25832@x" ObjectIDZND1="25829@x" Pin0InfoVect0LinkObjId="SW-147785_0" Pin0InfoVect1LinkObjId="SW-147781_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147784_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1760,-268 1760,-260 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33f70b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1760,-260 1760,-252 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="25832@x" ObjectIDND1="25831@x" ObjectIDZND0="25829@1" Pin0InfoVect0LinkObjId="SW-147781_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147785_0" Pin1InfoVect1LinkObjId="SW-147784_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1760,-260 1760,-252 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33f7310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1915,-262 1877,-262 1877,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="25851@x" ObjectIDND1="25849@x" ObjectIDZND0="25852@0" Pin0InfoVect0LinkObjId="SW-147850_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147849_0" Pin1InfoVect1LinkObjId="SW-147846_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1915,-262 1877,-262 1877,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33fa860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1916,-268 1916,-262 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="25851@0" ObjectIDZND0="25852@x" ObjectIDZND1="25849@x" Pin0InfoVect0LinkObjId="SW-147850_0" Pin0InfoVect1LinkObjId="SW-147846_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147849_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1916,-268 1916,-262 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33faac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1916,-262 1916,-252 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="25852@x" ObjectIDND1="25851@x" ObjectIDZND0="25849@1" Pin0InfoVect0LinkObjId="SW-147846_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147850_0" Pin1InfoVect1LinkObjId="SW-147849_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1916,-262 1916,-252 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33fad20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1610,-174 1610,-14 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="25840@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147815_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1610,-174 1610,-14 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33faf80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1760,-174 1760,-14 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="25830@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147783_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1760,-174 1760,-14 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33fb1e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2041,-303 2041,-316 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25820@1" ObjectIDZND0="25792@0" Pin0InfoVect0LinkObjId="g_33fb440_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-265405_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2041,-303 2041,-316 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33fb440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1808,-337 1808,-316 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25814@0" ObjectIDZND0="25792@0" Pin0InfoVect0LinkObjId="g_33fb1e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147753_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1808,-337 1808,-316 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33fb6a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2933,-225 2933,-210 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="27838@0" ObjectIDZND0="27839@1" Pin0InfoVect0LinkObjId="SW-182949_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-182947_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2933,-225 2933,-210 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33fb900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2770,-226 2770,-211 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25834@0" ObjectIDZND0="25836@1" Pin0InfoVect0LinkObjId="SW-147800_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147797_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2770,-226 2770,-211 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33fbb60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2597,-225 2597,-210 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25844@0" ObjectIDZND0="25846@1" Pin0InfoVect0LinkObjId="SW-147832_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147829_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2597,-225 2597,-210 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33fbdc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2429,-225 2429,-210 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25824@0" ObjectIDZND0="25826@1" Pin0InfoVect0LinkObjId="SW-147768_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147765_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2429,-225 2429,-210 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34008f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2504,-337 2504,-316 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25818@0" ObjectIDZND0="25793@0" Pin0InfoVect0LinkObjId="g_3400b50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147759_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2504,-337 2504,-316 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3400b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2429,-304 2429,-316 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25825@1" ObjectIDZND0="25793@0" Pin0InfoVect0LinkObjId="g_34008f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147767_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2429,-304 2429,-316 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3400db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2770,-304 2770,-316 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25835@1" ObjectIDZND0="25793@0" Pin0InfoVect0LinkObjId="g_34008f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147799_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2770,-304 2770,-316 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3401010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2933,-304 2933,-316 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27841@1" ObjectIDZND0="25793@0" Pin0InfoVect0LinkObjId="g_34008f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-182952_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2933,-304 2933,-316 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3401270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2429,-30 2429,-174 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="25826@0" Pin0InfoVect0LinkObjId="SW-147768_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2429,-30 2429,-174 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34014d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2597,-27 2597,-174 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="25846@0" Pin0InfoVect0LinkObjId="SW-147832_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2597,-27 2597,-174 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3401730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2770,-25 2770,-175 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="25836@0" Pin0InfoVect0LinkObjId="SW-147800_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2770,-25 2770,-175 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3401990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2933,-23 2933,-174 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="27839@0" Pin0InfoVect0LinkObjId="SW-182949_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2933,-23 2933,-174 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3401bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2770,-261 2732,-261 2732,-236 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="25835@x" ObjectIDND1="25834@x" ObjectIDZND0="25837@0" Pin0InfoVect0LinkObjId="SW-147801_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147799_0" Pin1InfoVect1LinkObjId="SW-147797_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2770,-261 2732,-261 2732,-236 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3405140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2597,-261 2559,-261 2559,-236 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="25845@x" ObjectIDND1="25844@x" ObjectIDZND0="25847@0" Pin0InfoVect0LinkObjId="SW-147833_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147831_0" Pin1InfoVect1LinkObjId="SW-147829_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2597,-261 2559,-261 2559,-236 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3408690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2933,-260 2895,-260 2895,-235 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="27841@x" ObjectIDND1="27838@x" ObjectIDZND0="27840@0" Pin0InfoVect0LinkObjId="SW-182951_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-182952_0" Pin1InfoVect1LinkObjId="SW-182947_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2933,-260 2895,-260 2895,-235 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_340bbe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2429,-260 2391,-260 2391,-235 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="25825@x" ObjectIDND1="25824@x" ObjectIDZND0="25827@0" Pin0InfoVect0LinkObjId="SW-147769_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147767_0" Pin1InfoVect1LinkObjId="SW-147765_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2429,-260 2391,-260 2391,-235 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_340f130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2597,-268 2597,-261 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="25845@0" ObjectIDZND0="25847@x" ObjectIDZND1="25844@x" Pin0InfoVect0LinkObjId="SW-147833_0" Pin0InfoVect1LinkObjId="SW-147829_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147831_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2597,-268 2597,-261 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_340f390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2597,-261 2597,-252 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="25847@x" ObjectIDND1="25845@x" ObjectIDZND0="25844@1" Pin0InfoVect0LinkObjId="SW-147829_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147833_0" Pin1InfoVect1LinkObjId="SW-147831_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2597,-261 2597,-252 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_340f5f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2770,-268 2770,-261 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="25835@0" ObjectIDZND0="25837@x" ObjectIDZND1="25834@x" Pin0InfoVect0LinkObjId="SW-147801_0" Pin0InfoVect1LinkObjId="SW-147797_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147799_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2770,-268 2770,-261 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_340f850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2770,-261 2770,-253 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="25837@x" ObjectIDND1="25835@x" ObjectIDZND0="25834@1" Pin0InfoVect0LinkObjId="SW-147797_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147801_0" Pin1InfoVect1LinkObjId="SW-147799_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2770,-261 2770,-253 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_340fab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2933,-268 2933,-260 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="27841@0" ObjectIDZND0="27840@x" ObjectIDZND1="27838@x" Pin0InfoVect0LinkObjId="SW-182951_0" Pin0InfoVect1LinkObjId="SW-182947_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-182952_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2933,-268 2933,-260 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_340fd10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2933,-260 2933,-252 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="27840@x" ObjectIDND1="27841@x" ObjectIDZND0="27838@1" Pin0InfoVect0LinkObjId="SW-182947_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-182951_0" Pin1InfoVect1LinkObjId="SW-182952_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2933,-260 2933,-252 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_340ff70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2597,-304 2597,-316 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25845@1" ObjectIDZND0="25793@0" Pin0InfoVect0LinkObjId="g_34008f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147831_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2597,-304 2597,-316 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34101d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1610,-304 1610,-316 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25841@1" ObjectIDZND0="25792@0" Pin0InfoVect0LinkObjId="g_33fb1e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147816_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1610,-304 1610,-316 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3410430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1760,-304 1760,-316 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25831@1" ObjectIDZND0="25792@0" Pin0InfoVect0LinkObjId="g_33fb1e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147784_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1760,-304 1760,-316 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3410690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1916,-304 1916,-316 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25851@1" ObjectIDZND0="25792@0" Pin0InfoVect0LinkObjId="g_33fb1e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147849_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1916,-304 1916,-316 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3416810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2088,-340 2088,-316 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25855@0" ObjectIDZND0="25792@0" Pin0InfoVect0LinkObjId="g_33fb1e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147864_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2088,-340 2088,-316 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3416a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2222,-340 2222,-316 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25857@0" ObjectIDZND0="25793@0" Pin0InfoVect0LinkObjId="g_34008f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147866_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2222,-340 2222,-316 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3416bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2155,-703 2155,-725 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="25791@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c5cd00_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2155,-703 2155,-725 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3416de0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2088,-376 2088,-403 2142,-403 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25855@1" ObjectIDZND0="25854@1" Pin0InfoVect0LinkObjId="SW-147862_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147864_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2088,-376 2088,-403 2142,-403 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3417010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2248,-394 2222,-394 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="25856@0" ObjectIDZND0="25854@x" ObjectIDZND1="25857@x" Pin0InfoVect0LinkObjId="SW-147862_0" Pin0InfoVect1LinkObjId="SW-147866_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147865_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2248,-394 2222,-394 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3417240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2169,-403 2222,-403 2222,-394 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="25854@0" ObjectIDZND0="25856@x" ObjectIDZND1="25857@x" Pin0InfoVect0LinkObjId="SW-147865_0" Pin0InfoVect1LinkObjId="SW-147866_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147862_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2169,-403 2222,-403 2222,-394 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3417470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2222,-394 2222,-376 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="25856@x" ObjectIDND1="25854@x" ObjectIDZND0="25857@1" Pin0InfoVect0LinkObjId="SW-147866_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147865_0" Pin1InfoVect1LinkObjId="SW-147862_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2222,-394 2222,-376 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_341e5b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1456,-108 1456,-81 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="capacitor" ObjectIDND0="g_3430a70@1" ObjectIDZND0="g_2ca6020@0" ObjectIDZND1="41921@x" Pin0InfoVect0LinkObjId="g_2ca6020_0" Pin0InfoVect1LinkObjId="CB-YA_XJC.YA_XJC_Cb1_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3430a70_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1456,-108 1456,-81 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_341e810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1456,-81 1456,-51 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="capacitor" ObjectIDND0="g_2ca6020@0" ObjectIDND1="g_3430a70@0" ObjectIDZND0="41921@0" Pin0InfoVect0LinkObjId="CB-YA_XJC.YA_XJC_Cb1_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2ca6020_0" Pin1InfoVect1LinkObjId="g_3430a70_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1456,-81 1456,-51 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3429480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1296,-116 1258,-116 1258,-91 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="42946@x" ObjectIDZND0="42949@0" Pin0InfoVect0LinkObjId="SW-265499_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-265497_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1296,-116 1258,-116 1258,-91 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3429670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1296,-167 1296,-116 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="42946@0" ObjectIDZND0="42949@x" Pin0InfoVect0LinkObjId="SW-265499_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-265497_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1296,-167 1296,-116 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3429860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1296,-116 1296,-46 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="42949@x" ObjectIDND1="42946@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-265499_0" Pin1InfoVect1LinkObjId="SW-265497_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1296,-116 1296,-46 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3429a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1296,-253 1258,-253 1258,-228 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="42945@x" ObjectIDND1="42944@x" ObjectIDZND0="42947@0" Pin0InfoVect0LinkObjId="SW-265498_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-265496_0" Pin1InfoVect1LinkObjId="SW-265494_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1296,-253 1258,-253 1258,-228 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3429c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1296,-261 1296,-253 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="42945@0" ObjectIDZND0="42947@x" ObjectIDZND1="42944@x" Pin0InfoVect0LinkObjId="SW-265498_0" Pin0InfoVect1LinkObjId="SW-265494_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-265496_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1296,-261 1296,-253 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3429eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1296,-253 1296,-245 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="42947@x" ObjectIDND1="42945@x" ObjectIDZND0="42944@1" Pin0InfoVect0LinkObjId="SW-265494_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-265498_0" Pin1InfoVect1LinkObjId="SW-265496_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1296,-253 1296,-245 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_342a0e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1296,-218 1296,-203 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="42944@0" ObjectIDZND0="42946@1" Pin0InfoVect0LinkObjId="SW-265497_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-265494_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1296,-218 1296,-203 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34305b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1456,-225 1456,-210 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25858@0" ObjectIDZND0="25859@1" Pin0InfoVect0LinkObjId="SW-147874_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147872_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1456,-225 1456,-210 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3430810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1456,-174 1456,-161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="25859@0" ObjectIDZND0="g_3430a70@0" Pin0InfoVect0LinkObjId="g_3430a70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147874_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1456,-174 1456,-161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34317c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1455,-262 1416,-262 1416,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="25860@x" ObjectIDND1="25858@x" ObjectIDZND0="25861@0" Pin0InfoVect0LinkObjId="SW-147876_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147875_0" Pin1InfoVect1LinkObjId="SW-147872_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1455,-262 1416,-262 1416,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3434d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1456,-269 1456,-262 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="25860@0" ObjectIDZND0="25861@x" ObjectIDZND1="25858@x" Pin0InfoVect0LinkObjId="SW-147876_0" Pin0InfoVect1LinkObjId="SW-147872_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147875_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1456,-269 1456,-262 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3434f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1456,-262 1456,-252 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="25861@x" ObjectIDND1="25860@x" ObjectIDZND0="25858@1" Pin0InfoVect0LinkObjId="SW-147872_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147876_0" Pin1InfoVect1LinkObjId="SW-147875_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1456,-262 1456,-252 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34351d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1296,-297 1296,-316 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="42945@1" ObjectIDZND0="25792@0" Pin0InfoVect0LinkObjId="g_33fb1e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-265496_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1296,-297 1296,-316 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3435430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1456,-305 1456,-316 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25860@1" ObjectIDZND0="25792@0" Pin0InfoVect0LinkObjId="g_33fb1e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147875_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1456,-305 1456,-316 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3443040">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="1" points="2567,-910 2468,-911 2468,-1232 897,-1232 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" BeginDevType2="powerLine" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="25805@x" ObjectIDND1="g_2c19800@0" ObjectIDND2="37772@1" ObjectIDZND0="42350@x" ObjectIDZND1="42350@x" ObjectIDZND2="25805@x" Pin0InfoVect0LinkObjId="SW-258280_0" Pin0InfoVect1LinkObjId="SW-258280_0" Pin0InfoVect2LinkObjId="SW-147655_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-147655_0" Pin1InfoVect1LinkObjId="g_2c19800_0" Pin1InfoVect2LinkObjId="g_2c195a0_1" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2567,-910 2468,-911 2468,-1232 897,-1232 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3454450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1916,-174 1916,-4 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="25850@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147848_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1916,-174 1916,-4 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3456ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1266,301 1266,281 1153,281 1153,-799 1000,-799 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="25828@1" ObjectIDZND0="g_3456110@0" Pin0InfoVect0LinkObjId="g_3456110_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147770_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1266,301 1266,281 1153,281 1153,-799 1000,-799 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3456d40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="951,-799 676,-799 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3456110@1" ObjectIDZND0="g_2d3c040@0" Pin0InfoVect0LinkObjId="g_2d3c040_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3456110_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="951,-799 676,-799 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34579c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1424,284 1424,269 1376,269 1376,454 1112,454 1112,-687 974,-687 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="25833@1" ObjectIDZND0="g_3456fa0@0" Pin0InfoVect0LinkObjId="g_3456fa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147786_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1424,284 1424,269 1376,269 1376,454 1112,454 1112,-687 974,-687 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3457c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="925,-687 675,-687 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3456fa0@1" ObjectIDZND0="g_2ce65d0@0" Pin0InfoVect0LinkObjId="g_2ce65d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3456fa0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="925,-687 675,-687 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34588c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="676,-577 886,-577 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2d4d650@0" ObjectIDZND0="g_3457ea0@1" Pin0InfoVect0LinkObjId="g_3457ea0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d4d650_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="676,-577 886,-577 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3458b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="935,-577 1063,-577 1063,484 1552,484 1552,272 1590,272 1590,294 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3457ea0@0" ObjectIDZND0="25838@1" Pin0InfoVect0LinkObjId="SW-147802_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3457ea0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="935,-577 1063,-577 1063,484 1552,484 1552,272 1590,272 1590,294 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34597c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="676,-464 865,-464 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2d21550@0" ObjectIDZND0="g_3458da0@1" Pin0InfoVect0LinkObjId="g_3458da0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d21550_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="676,-464 865,-464 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3459a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="914,-464 1016,-464 1016,520 2089,520 2089,299 2128,299 2128,320 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3458da0@0" ObjectIDZND0="25843@1" Pin0InfoVect0LinkObjId="SW-147818_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3458da0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="914,-464 1016,-464 1016,520 2089,520 2089,299 2128,299 2128,320 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_345a6c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="674,-353 856,-353 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2cebc30@0" ObjectIDZND0="g_3459ca0@1" Pin0InfoVect0LinkObjId="g_3459ca0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2cebc30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="674,-353 856,-353 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_345b340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="677,-255 840,-255 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2c85c20@0" ObjectIDZND0="g_345a920@1" Pin0InfoVect0LinkObjId="g_345a920_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c85c20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="677,-255 840,-255 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_345bfc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="698,-146 821,-146 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2d030e0@0" ObjectIDZND0="g_345b5a0@1" Pin0InfoVect0LinkObjId="g_345b5a0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d030e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="698,-146 821,-146 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34647f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="905,-353 987,-353 987,553 2236,553 2236,297 2274,297 2274,321 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3459ca0@0" ObjectIDZND0="25848@1" Pin0InfoVect0LinkObjId="SW-147834_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3459ca0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="905,-353 987,-353 987,553 2236,553 2236,297 2274,297 2274,321 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3464a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="889,-255 960,-255 960,585 2364,585 2364,287 2470,287 2470,307 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_345a920@0" ObjectIDZND0="25853@1" Pin0InfoVect0LinkObjId="SW-147851_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_345a920_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="889,-255 960,-255 960,585 2364,585 2364,287 2470,287 2470,307 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3464cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="870,-146 931,-146 931,631 2625,631 2625,290 2661,290 2661,310 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_345b5a0@0" ObjectIDZND0="27842@1" Pin0InfoVect0LinkObjId="SW-182953_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_345b5a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="870,-146 931,-146 931,631 2625,631 2625,290 2661,290 2661,310 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35ec290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2568,-911 2568,-932 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="powerLine" ObjectIDND0="42350@x" ObjectIDND1="42350@x" ObjectIDND2="25805@x" ObjectIDZND0="25805@x" ObjectIDZND1="g_2c19800@0" ObjectIDZND2="37772@1" Pin0InfoVect0LinkObjId="SW-147655_0" Pin0InfoVect1LinkObjId="g_2c19800_0" Pin0InfoVect2LinkObjId="g_2c195a0_1" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-258280_0" Pin1InfoVect1LinkObjId="SW-258280_0" Pin1InfoVect2LinkObjId="SW-147655_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2568,-911 2568,-932 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35ec480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2568,-932 2568,-1001 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="voltageTransformer" EndDevType1="powerLine" EndDevType2="lightningRod" ObjectIDND0="25805@x" ObjectIDND1="42350@x" ObjectIDND2="42350@x" ObjectIDZND0="g_2c19800@0" ObjectIDZND1="37772@1" ObjectIDZND2="g_345e000@0" Pin0InfoVect0LinkObjId="g_2c19800_0" Pin0InfoVect1LinkObjId="g_2c195a0_1" Pin0InfoVect2LinkObjId="g_345e000_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-147655_0" Pin1InfoVect1LinkObjId="SW-258280_0" Pin1InfoVect2LinkObjId="SW-258280_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2568,-932 2568,-1001 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34653a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2568,-883 2568,-911 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="25806@1" ObjectIDZND0="42350@x" ObjectIDZND1="42350@x" ObjectIDZND2="25805@x" Pin0InfoVect0LinkObjId="SW-258280_0" Pin0InfoVect1LinkObjId="SW-258280_0" Pin0InfoVect2LinkObjId="SW-147655_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147656_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2568,-883 2568,-911 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="468" cy="-886" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="468" cy="-799" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="468" cy="-687" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="468" cy="-577" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="468" cy="-464" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="468" cy="-353" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="468" cy="-255" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="468" cy="-146" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="468" cy="-35" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="468" cy="48" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="468" cy="128" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25793" cx="2243" cy="-316" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25792" cx="2041" cy="-316" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25792" cx="1808" cy="-316" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25793" cx="2504" cy="-316" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25793" cx="2429" cy="-316" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25793" cx="2770" cy="-316" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25793" cx="2933" cy="-316" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25793" cx="2597" cy="-316" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25792" cx="1610" cy="-316" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25792" cx="1760" cy="-316" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25792" cx="1916" cy="-316" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25792" cx="2088" cy="-316" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25793" cx="2222" cy="-316" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25792" cx="1296" cy="-316" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25792" cx="1456" cy="-316" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25791" cx="1739" cy="-725" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25791" cx="2155" cy="-725" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25791" cx="2568" cy="-725" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25791" cx="1808" cy="-725" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25791" cx="2504" cy="-725" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25791" cx="1951" cy="-725" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25791" cx="2155" cy="-725" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-258117" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 85.500000 -921.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42334" ObjectName="DYN-CX_XJCYD"/>
     <cge:Meas_Ref ObjectId="258117"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_2db6460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -38.000000 -1034.500000) translate(0,16)">西教场移动变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a99de0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a99de0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a99de0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a99de0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a99de0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a99de0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a99de0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a99de0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a99de0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a99de0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a99de0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a99de0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a99de0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a99de0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a99de0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a99de0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a99de0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a99de0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2dd62b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -895.000000) translate(0,17)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2dd62b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -895.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2dd62b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -895.000000) translate(0,59)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2dd62b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -895.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2dd62b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -895.000000) translate(0,101)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2dd62b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -895.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2dd62b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -895.000000) translate(0,143)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2dd62b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -895.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2dd62b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -895.000000) translate(0,185)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2da6600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2361.000000 328.000000) translate(0,12)">10kV2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d66ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2135.000000 331.000000) translate(0,12)">4826</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2da6930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1595.000000 305.000000) translate(0,12)">4876</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2da6c60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2281.000000 332.000000) translate(0,12)">4846</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2da6f90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1431.000000 295.000000) translate(0,12)">4856</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2da7140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1273.000000 312.000000) translate(0,12)">4836</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2da7470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2477.000000 318.000000) translate(0,12)">4866</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e2c8e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2668.000000 321.000000) translate(0,12)">4886</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_2e2cad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 157.000000 -1031.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_2d1a7b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 157.000000 -1068.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cfd770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1242.000000 420.000000) translate(0,12)">麻纺厂线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cfd920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1401.000000 404.000000) translate(0,12)">龙岗线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cfdc50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1563.000000 411.000000) translate(0,12)">官屯线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cfdf80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2105.000000 431.000000) translate(0,12)">城区线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cfe2b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2243.000000 434.000000) translate(0,12)">观测站线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c7d240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2451.000000 422.000000) translate(0,12)">仁和线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c7d3f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2631.000000 420.000000) translate(0,12)">金龟街线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c7d560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -196.000000 -56.000000) translate(0,17)">姚安巡维中心：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2d0a820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -42.000000 -66.500000) translate(0,17)">18787878958</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2d0a820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -42.000000 -66.500000) translate(0,38)">18787878954</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2d0b140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -42.000000 -96.000000) translate(0,17)">5712404</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_2d96ea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 158.000000 -953.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2d33130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 540.000000 -875.000000) translate(0,15)">0032</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_2d99c00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 657.000000 37.000000) translate(0,13)">10kV环网柜母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_2d99c00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 657.000000 37.000000) translate(0,29)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2d3ca50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 487.000000 -830.000000) translate(0,15)">0111</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2d3d4d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 688.000000 -764.000000) translate(0,15)">01167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_2d3db90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 698.000000 -834.000000) translate(0,18)">麻纺厂线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_2cafab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 590.000000 158.000000) translate(0,18)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_2d7d190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 394.000000 -950.000000) translate(0,18)">10kV环网柜母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ce76b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 686.000000 -659.000000) translate(0,15)">01267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_2ce7ce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 693.000000 -720.000000) translate(0,18)">龙岗线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2d4e270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 494.000000 -610.000000) translate(0,15)">0131</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2d4eb40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 687.000000 -549.000000) translate(0,15)">01367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_2d4ee60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 694.000000 -610.000000) translate(0,18)">官屯线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2d22130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 479.000000 -494.000000) translate(0,15)">0141</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2d22d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 687.000000 -436.000000) translate(0,15)">01467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_2d230c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 694.000000 -497.000000) translate(0,18)">城区线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2cec850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 477.000000 -383.000000) translate(0,15)">0151</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ced430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 685.000000 -325.000000) translate(0,15)">01567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_2ced7e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 692.000000 -386.000000) translate(0,18)">观测站线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2cee230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 480.000000 -285.000000) translate(0,15)">0161</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ceeb00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 688.000000 -227.000000) translate(0,15)">01667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_2ceee20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 695.000000 -288.000000) translate(0,18)">仁和线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2d03c90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 480.000000 -172.000000) translate(0,15)">0171</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2d04560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 688.000000 -114.000000) translate(0,15)">01767</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_2d04880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 695.000000 -175.000000) translate(0,18)">金龟街线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c4b250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 481.000000 -65.000000) translate(0,15)">0181</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2cd2230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 689.000000 -7.000000) translate(0,15)">01867</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_2cd25e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 696.000000 -68.000000) translate(0,18)">备用一线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2bffbf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 757.000000 -1029.000000) translate(0,15)">3号移动变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2c00be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 847.000000 -1089.000000) translate(0,16)">303</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2c01290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 901.000000 -1158.000000) translate(0,16)">3036</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2c01660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 910.000000 -1108.000000) translate(0,16)">30360</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c01ac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 497.000000 -915.000000) translate(0,15)">003</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c01d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 490.000000 -714.000000) translate(0,15)">0121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c01f40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 496.000000 22.000000) translate(0,12)">0903</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c02460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 497.000000 102.000000) translate(0,12)">4903</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c027e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 551.000000 -829.000000) translate(0,12)">011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c02c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 549.000000 -718.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c03080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 551.000000 -609.000000) translate(0,12)">013</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c034d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 551.000000 -493.000000) translate(0,12)">014</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c03920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 549.000000 -384.000000) translate(0,12)">015</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c03d70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 551.000000 -286.000000) translate(0,12)">016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c041c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 554.000000 -171.000000) translate(0,12)">017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c04610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 554.000000 -63.000000) translate(0,12)">018</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c04a60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3.000000 -614.000000) translate(0,12)">公共信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c38340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2101.000000 -1009.000000) translate(0,12)">35kV西大连线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c39020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2533.000000 -1055.000000) translate(0,12)">35kV姚西线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c3e310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1699.000000 -1011.000000) translate(0,12)">35kV黄西太线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f5db50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1654.000000 -596.000000) translate(0,12)">SZ11-10000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f5db50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1654.000000 -596.000000) translate(0,27)">35±3×2.5%/10.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f5db50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1654.000000 -596.000000) translate(0,42)">YNd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f5db50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1654.000000 -596.000000) translate(0,57)">Ud=7.38%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c90470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2350.000000 -596.000000) translate(0,12)">SZ9-3150/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c90470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2350.000000 -596.000000) translate(0,27)">35±3×2.5%/10.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c90470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2350.000000 -596.000000) translate(0,42)">Yd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c90470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2350.000000 -596.000000) translate(0,57)">Ud=7.25%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33af030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2003.000000 -102.400000) translate(0,12)">10kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33af030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2003.000000 -102.400000) translate(0,27)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33b68f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2205.000000 -101.400000) translate(0,12)">10kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33b68f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2205.000000 -101.400000) translate(0,27)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33cb410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1463.000000 -294.000000) translate(0,12)">0521</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33cba40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1463.000000 -199.000000) translate(0,12)">0522</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33cbc80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1465.000000 -246.000000) translate(0,12)">052</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33cbec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2436.000000 -293.000000) translate(0,12)">0612</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33cc100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2436.000000 -199.000000) translate(0,12)">0613</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33cc340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2438.000000 -246.000000) translate(0,12)">061</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33cc580" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2951.000000 -338.000000) translate(0,12)">10kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33cc7c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1923.000000 -293.000000) translate(0,12)">0551</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33cca00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1923.000000 -199.000000) translate(0,12)">0552</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33ccc40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2604.000000 -293.000000) translate(0,12)">0622</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33cce80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2604.000000 -199.000000) translate(0,12)">0623</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33cd0c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2606.000000 -246.000000) translate(0,12)">062</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33cd300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1767.000000 -293.000000) translate(0,12)">0541</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33cd540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1767.000000 -199.000000) translate(0,12)">0542</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33cd780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1769.000000 -246.000000) translate(0,12)">054</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33cd9c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1617.000000 -293.000000) translate(0,12)">0531</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33cdc00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1617.000000 -199.000000) translate(0,12)">0532</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33cde40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1619.000000 -246.000000) translate(0,12)">053</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33ce080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2263.000000 -420.000000) translate(0,12)">01220</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33ce2c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2229.000000 -365.000000) translate(0,12)">0122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33ce500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2095.000000 -365.000000) translate(0,12)">0121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33ce740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2144.000000 -427.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33ce980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2104.000000 -285.000000) translate(0,12)">09017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33cebc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2048.000000 -292.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33cee00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2781.000000 -293.000000) translate(0,12)">0632</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33cf040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2777.000000 -200.000000) translate(0,12)">0633</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33cf280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2779.000000 -247.000000) translate(0,12)">063</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33cf4c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2306.000000 -284.000000) translate(0,12)">09027</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33cf700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2250.000000 -291.000000) translate(0,12)">0902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33cf940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1903.000000 -577.000000) translate(0,12)">35kV1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33cfb90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1882.000000 -409.000000) translate(0,12)">00117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33cfdc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1815.000000 -362.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d0000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1815.000000 -459.000000) translate(0,12)">0016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d0240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1818.000000 -414.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d0480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1815.000000 -704.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d06c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1816.000000 -661.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d0900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2577.000000 -409.000000) translate(0,12)">00227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d0b40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2511.000000 -362.000000) translate(0,12)">0022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d0d80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2511.000000 -459.000000) translate(0,12)">0026</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d0fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2513.000000 -414.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d1200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2511.000000 -704.000000) translate(0,12)">3021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d1440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2513.000000 -661.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d1680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1746.000000 -764.000000) translate(0,12)">3811</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d18c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1775.000000 -917.000000) translate(0,12)">38167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d1b00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1746.000000 -863.000000) translate(0,12)">3816</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d1d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1748.000000 -812.000000) translate(0,12)">381</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d1f80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2162.000000 -692.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d21c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2162.000000 -763.000000) translate(0,12)">3821</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d2400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2191.000000 -917.000000) translate(0,12)">38267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d2640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2162.000000 -863.000000) translate(0,12)">3826</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d2880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2164.000000 -812.000000) translate(0,12)">382</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d2ac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2579.000000 -763.000000) translate(0,12)">3831</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d2d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2608.000000 -958.000000) translate(0,12)">38367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d2f40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2579.000000 -872.000000) translate(0,12)">3836</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d3180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2581.000000 -816.000000) translate(0,12)">383</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33e24e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2940.000000 -293.000000) translate(0,12)">0642</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33e2b10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2940.000000 -199.000000) translate(0,12)">0643</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33e2d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2942.000000 -246.000000) translate(0,12)">064</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33e3700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2104.000000 -555.000000) translate(0,12)">35kV母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33e3b50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1737.000000 25.000000) translate(0,12)">龙岗线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33e3e30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2399.000000 20.000000) translate(0,12)">麻纺厂线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33e4070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2560.000000 19.000000) translate(0,12)">观测站线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33e42b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2752.000000 11.000000) translate(0,12)">官屯线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33e44f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2903.000000 15.000000) translate(0,12)">金龟街线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33ee120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1656.000000 -616.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33eea50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2349.000000 -614.000000) translate(0,12)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34108f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1416.000000 6.000000) translate(0,12)">1号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3411410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1517.000000 -232.000000) translate(0,12)">05310</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34118d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1667.000000 -225.000000) translate(0,12)">05410</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3411b10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1825.000000 -231.000000) translate(0,12)">05510</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3411d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2334.000000 -225.000000) translate(0,12)">06120</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3411f90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2503.000000 -226.000000) translate(0,12)">06220</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34121d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2676.000000 -230.000000) translate(0,12)">06320</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3412410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2838.000000 -228.000000) translate(0,12)">06420</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3415dc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1925.000000 -246.000000) translate(0,12)">055</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3416500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1589.000000 24.000000) translate(0,12)">城区线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_341ea70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1221.000000 -343.000000) translate(0,12)">10kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34276b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1303.000000 -286.000000) translate(0,12)">0511</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3427ce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1303.000000 -192.000000) translate(0,12)">0512</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3427f20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1305.000000 -239.000000) translate(0,12)">051</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3428160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1272.000000 -14.000000) translate(0,12)">备用一</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3435690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1205.000000 -208.000000) translate(0,12)">05110</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3435cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1192.000000 -88.000000) translate(0,12)">05120</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3435f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1362.000000 -230.000000) translate(0,12)">05210</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="27" graphid="g_3450ce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1708.000000 215.000000) translate(0,22)">引流解脱断面</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34546b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1900.000000 19.000000) translate(0,12)">仁和线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_345c220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1477.000000 -759.000000) translate(0,12)">35kV母线</text>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="45" stroke="rgb(255,255,0)" stroke-width="1" width="1471" x="1214" y="206"/>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="468,220 468,-902 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="468,220 468,-902 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-YA_XJC.YA_XJC_3M">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1455,-725 2895,-725 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="25791" ObjectName="BS-YA_XJC.YA_XJC_3M"/>
    </metadata>
   <polyline fill="none" opacity="0" points="1455,-725 2895,-725 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-YA_XJC.YA_XJC_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1213,-316 2127,-316 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="25792" ObjectName="BS-YA_XJC.YA_XJC_9IM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="1213,-316 2127,-316 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-YA_XJC.YA_XJC_9IIM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2186,-316 3038,-316 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="25793" ObjectName="BS-YA_XJC.YA_XJC_9IIM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="2186,-316 3038,-316 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2391.000000 503.000000)" xlink:href="#transformer2:shape12_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2391.000000 503.000000)" xlink:href="#transformer2:shape12_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.898990 0.000000 0.000000 -0.869565 724.500000 141.500000)" xlink:href="#transformer2:shape20_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.898990 0.000000 0.000000 -0.869565 724.500000 141.500000)" xlink:href="#transformer2:shape20_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_XJCYD.CX_XJCYD_3T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="17973"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(-0.909091 -0.000000 -0.000000 0.901961 933.000000 -1046.000000)" xlink:href="#transformer2:shape70_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(-0.909091 -0.000000 -0.000000 0.901961 933.000000 -1046.000000)" xlink:href="#transformer2:shape70_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="42380" ObjectName="TF-CX_XJCYD.CX_XJCYD_3T"/>
    <cge:TPSR_Ref TObjectID="42380"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-YA_XJC.YA_XJC_Zyb">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1936.000000 -583.000000)" xlink:href="#transformer2:shape15_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1936.000000 -583.000000)" xlink:href="#transformer2:shape15_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="25864" ObjectName="TF-YA_XJC.YA_XJC_Zyb"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-YA_XJC.YA_XJC_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(0.909091 -0.000000 0.000000 -0.901961 1773.000000 -545.000000)" xlink:href="#transformer2:shape70_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(0.909091 -0.000000 0.000000 -0.901961 1773.000000 -545.000000)" xlink:href="#transformer2:shape70_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="25862" ObjectName="TF-YA_XJC.YA_XJC_1T"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-YA_XJC.YA_XJC_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(0.909091 -0.000000 0.000000 -0.901961 2469.000000 -545.000000)" xlink:href="#transformer2:shape70_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(0.909091 -0.000000 0.000000 -0.901961 2469.000000 -545.000000)" xlink:href="#transformer2:shape70_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="25863" ObjectName="TF-YA_XJC.YA_XJC_2T"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -69.000000 -994.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-217887" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -43.000000 -814.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217887" ObjectName="YA_XJC:YA_XJC_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-219742" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -42.000000 -774.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="219742" ObjectName="YA_XJC:YA_XJC_sumQ"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-217887" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -44.000000 -895.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217887" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-217887" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -43.000000 -854.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217887" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="146" y="-1038"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="146" y="-1038"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="-54" y="-1044"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="-54" y="-1044"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-102" y="-1064"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-102" y="-1064"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="146" y="-1077"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="146" y="-1077"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <polygon fill="rgb(255,255,255)" points="150,-969 147,-972 147,-917 150,-920 150,-969" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(255,255,255)" points="150,-969 147,-972 206,-972 203,-969 150,-969" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(127,127,127)" points="150,-920 147,-917 206,-917 203,-920 150,-920" stroke="rgb(127,127,127)"/>
     <polygon fill="rgb(127,127,127)" points="203,-969 206,-972 206,-917 203,-920 203,-969" stroke="rgb(127,127,127)"/>
     <rect fill="rgb(255,255,255)" height="49" stroke="rgb(255,255,255)" width="53" x="150" y="-969"/>
     <rect fill="none" height="49" qtmmishow="hidden" stroke="rgb(0,0,0)" width="53" x="150" y="-969"/>
    </a>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="117" x="740" y="-1030"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="117" x="740" y="-1030"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="550" y="-829"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="550" y="-829"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="549" y="-718"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="549" y="-718"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="551" y="-609"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="551" y="-609"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="551" y="-493"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="551" y="-493"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="549" y="-384"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="549" y="-384"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="551" y="-286"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="551" y="-286"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="554" y="-171"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="554" y="-171"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="554" y="-63"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="554" y="-63"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="60" x="3" y="-614"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="60" x="3" y="-614"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="1748" y="-812"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="1748" y="-812"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="2164" y="-812"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="2164" y="-812"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="2581" y="-816"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="2581" y="-816"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="52" x="1656" y="-616"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="52" x="1656" y="-616"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="53" x="2349" y="-614"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="53" x="2349" y="-614"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="1465" y="-246"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="1465" y="-246"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1619" y="-246"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1619" y="-246"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1769" y="-246"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1769" y="-246"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="2144" y="-427"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="2144" y="-427"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="2438" y="-246"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="2438" y="-246"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="2606" y="-246"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="2606" y="-246"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="2779" y="-247"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="2779" y="-247"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="2942" y="-246"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="2942" y="-246"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1924" y="-247"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1924" y="-247"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="1305" y="-239"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="1305" y="-239"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c07ad0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 333.000000 809.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c08d90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 322.000000 794.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c09920" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 347.000000 779.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c0b650" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 947.000000 948.000000) translate(0,12)">油温（℃）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c0c9c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 947.000000 963.000000) translate(0,12)">档位（档）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c0d920" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 984.000000 1096.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c0db70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 973.000000 1081.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c0dd70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 998.000000 1066.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c0e500" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 701.000000 940.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c0e770" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 690.000000 925.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c0e980" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 715.000000 910.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c0ecb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 694.000000 1104.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c2fd00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 694.000000 1119.000000) translate(0,12)">Ua（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c2ff80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 686.000000 1076.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c301c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 694.000000 1090.000000) translate(0,12)">Uc（kV）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c30760" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 330.000000 902.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c30a10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 330.000000 917.000000) translate(0,12)">Ua（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c30c50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 322.000000 874.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c30e90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 330.000000 888.000000) translate(0,12)">Uc（kV）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c343f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 333.000000 700.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c346b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 322.000000 685.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c348f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 347.000000 670.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c34d10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 333.000000 592.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c34fd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 322.000000 577.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c35210" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 347.000000 562.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c35630" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 333.000000 481.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c358f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 322.000000 466.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c35b30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 347.000000 451.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c35f50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 333.000000 381.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c36210" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 322.000000 366.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c36450" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 347.000000 351.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c36870" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 333.000000 279.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c36b30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 322.000000 264.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c36d70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 347.000000 249.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c37190" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 333.000000 161.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c37450" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 322.000000 146.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c37690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 347.000000 131.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c37ab0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 333.000000 56.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c37d70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 322.000000 41.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c37fb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 347.000000 26.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d34b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1437.000000 690.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d3720" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1437.000000 705.000000) translate(0,12)">Ua（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d3960" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1429.000000 662.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d3ba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1437.000000 676.000000) translate(0,12)">Uc（kV）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d3fc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1806.000000 828.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d4280" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1795.000000 813.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d44c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1820.000000 798.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d47f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1856.000000 527.000000) translate(0,12)">油温（℃）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d4a50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1856.000000 542.000000) translate(0,12)">档位（档）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d6530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1693.000000 679.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d67c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1682.000000 664.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d6a00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1707.000000 649.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d7450" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1695.000000 430.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d76e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1684.000000 415.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d7920" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1709.000000 400.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33da380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2549.000000 527.000000) translate(0,12)">油温（℃）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33da5d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2549.000000 542.000000) translate(0,12)">档位（档）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33daed0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2884.000000 391.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33db130" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2884.000000 406.000000) translate(0,12)">Ua（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33db370" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2876.000000 363.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33db5b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2884.000000 377.000000) translate(0,12)">Uc（kV）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33e4910" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2221.000000 827.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33e4bd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2210.000000 812.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33e4e10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2235.000000 797.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 -2.000000)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33e5230" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2628.000000 826.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33e54f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2617.000000 811.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33e5730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2642.000000 796.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33e5b50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2400.000000 681.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33e5e10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2389.000000 666.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33e6050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2414.000000 651.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33e6470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2397.000000 431.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33e6730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2386.000000 416.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33e6970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2411.000000 401.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33e6d90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2089.000000 476.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33e7050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2078.000000 461.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33e7290" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2103.000000 446.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33e76b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1545.000000 -58.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33e7970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1534.000000 -73.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33e7bb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1559.000000 -88.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33e7fd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1692.000000 -60.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33e8290" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1681.000000 -75.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33e84d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1706.000000 -90.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33e88f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1873.000000 -60.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33e8bb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1862.000000 -75.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33e8df0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1887.000000 -90.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33e9210" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2364.000000 -57.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33e94d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2353.000000 -72.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33e9710" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2378.000000 -87.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33e9b30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2532.000000 -58.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33e9df0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2521.000000 -73.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33ea030" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2546.000000 -88.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33ea450" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2730.000000 -63.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33ea710" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2719.000000 -78.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33ea950" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2744.000000 -93.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33ead70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2896.000000 -59.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33eb030" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2885.000000 -74.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33eb270" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2910.000000 -89.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_341f190" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1325.000000 392.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_341f400" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1325.000000 407.000000) translate(0,12)">Ua（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_341f640" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1317.000000 364.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_341f880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1325.000000 378.000000) translate(0,12)">Uc（kV）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_341fca0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1391.000000 -59.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_341ff60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1380.000000 -74.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34201a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1405.000000 -89.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3428b20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1223.000000 -66.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3429000" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1212.000000 -81.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3429240" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1237.000000 -96.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="1469" x2="1500" y1="-248" y2="-216"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="1473" x2="1494" y1="-215" y2="-252"/>
   <metadata/><line fill="none" opacity="0" stroke="white" stroke-width="10" transform="" x1="1469" x2="1500" y1="-248" y2="-216"/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="1272" x2="1303" y1="-244" y2="-212"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="1276" x2="1297" y1="-211" y2="-248"/>
   <metadata/><line fill="none" opacity="0" stroke="white" stroke-width="10" transform="" x1="1272" x2="1303" y1="-244" y2="-212"/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="2125" x2="2156" y1="-242" y2="-210"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="2129" x2="2150" y1="-209" y2="-246"/>
   <metadata/><line fill="none" opacity="0" stroke="white" stroke-width="10" transform="" x1="2125" x2="2156" y1="-242" y2="-210"/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="2329" x2="2360" y1="-240" y2="-208"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="2333" x2="2354" y1="-207" y2="-244"/>
   <metadata/><line fill="none" opacity="0" stroke="white" stroke-width="10" transform="" x1="2329" x2="2360" y1="-240" y2="-208"/></g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-YA_XJC.YA_XJC_Cb1">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1404.000000 -14.000000)" xlink:href="#capacitor:shape11"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41921" ObjectName="CB-YA_XJC.YA_XJC_Cb1"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-258169" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 387.000000 -807.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="258169" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42356"/>
     <cge:Term_Ref ObjectID="17923"/>
    <cge:TPSR_Ref TObjectID="42356"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-258170" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 387.000000 -807.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="258170" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42356"/>
     <cge:Term_Ref ObjectID="17923"/>
    <cge:TPSR_Ref TObjectID="42356"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-258159" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 387.000000 -807.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="258159" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42356"/>
     <cge:Term_Ref ObjectID="17923"/>
    <cge:TPSR_Ref TObjectID="42356"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="1" id="ME-258157" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1034.000000 -961.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="258157" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42380"/>
     <cge:Term_Ref ObjectID="17971"/>
    <cge:TPSR_Ref TObjectID="42380"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="1" id="ME-258276" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1034.000000 -961.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="258276" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42380"/>
     <cge:Term_Ref ObjectID="17971"/>
    <cge:TPSR_Ref TObjectID="42380"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-258142" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1047.000000 -1096.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="258142" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42349"/>
     <cge:Term_Ref ObjectID="17909"/>
    <cge:TPSR_Ref TObjectID="42349"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-258143" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1047.000000 -1096.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="258143" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42349"/>
     <cge:Term_Ref ObjectID="17909"/>
    <cge:TPSR_Ref TObjectID="42349"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-258132" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1047.000000 -1096.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="258132" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42349"/>
     <cge:Term_Ref ObjectID="17909"/>
    <cge:TPSR_Ref TObjectID="42349"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-258155" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 761.000000 -942.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="258155" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42352"/>
     <cge:Term_Ref ObjectID="17915"/>
    <cge:TPSR_Ref TObjectID="42352"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-258156" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 761.000000 -942.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="258156" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42352"/>
     <cge:Term_Ref ObjectID="17915"/>
    <cge:TPSR_Ref TObjectID="42352"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-258145" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 761.000000 -942.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="258145" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42352"/>
     <cge:Term_Ref ObjectID="17915"/>
    <cge:TPSR_Ref TObjectID="42352"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-258135" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 771.000000 -1116.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="258135" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42349"/>
     <cge:Term_Ref ObjectID="17909"/>
    <cge:TPSR_Ref TObjectID="42349"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-258136" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 771.000000 -1116.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="258136" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42349"/>
     <cge:Term_Ref ObjectID="17909"/>
    <cge:TPSR_Ref TObjectID="42349"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-258137" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 771.000000 -1116.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="258137" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42349"/>
     <cge:Term_Ref ObjectID="17909"/>
    <cge:TPSR_Ref TObjectID="42349"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-258138" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 771.000000 -1116.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="258138" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42349"/>
     <cge:Term_Ref ObjectID="17909"/>
    <cge:TPSR_Ref TObjectID="42349"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-258148" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 406.000000 -914.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="258148" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42352"/>
     <cge:Term_Ref ObjectID="17915"/>
    <cge:TPSR_Ref TObjectID="42352"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-258149" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 406.000000 -914.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="258149" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42352"/>
     <cge:Term_Ref ObjectID="17915"/>
    <cge:TPSR_Ref TObjectID="42352"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-258150" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 406.000000 -914.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="258150" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42352"/>
     <cge:Term_Ref ObjectID="17915"/>
    <cge:TPSR_Ref TObjectID="42352"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-258151" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 406.000000 -914.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="258151" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42352"/>
     <cge:Term_Ref ObjectID="17915"/>
    <cge:TPSR_Ref TObjectID="42352"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-258182" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 387.000000 -700.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="258182" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42359"/>
     <cge:Term_Ref ObjectID="17929"/>
    <cge:TPSR_Ref TObjectID="42359"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-258183" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 387.000000 -700.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="258183" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42359"/>
     <cge:Term_Ref ObjectID="17929"/>
    <cge:TPSR_Ref TObjectID="42359"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-258172" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 387.000000 -700.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="258172" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42359"/>
     <cge:Term_Ref ObjectID="17929"/>
    <cge:TPSR_Ref TObjectID="42359"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-258195" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 387.000000 -593.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="258195" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42362"/>
     <cge:Term_Ref ObjectID="17935"/>
    <cge:TPSR_Ref TObjectID="42362"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-258196" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 387.000000 -593.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="258196" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42362"/>
     <cge:Term_Ref ObjectID="17935"/>
    <cge:TPSR_Ref TObjectID="42362"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-258185" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 387.000000 -593.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="258185" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42362"/>
     <cge:Term_Ref ObjectID="17935"/>
    <cge:TPSR_Ref TObjectID="42362"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-258208" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 387.000000 -483.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="258208" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42365"/>
     <cge:Term_Ref ObjectID="17941"/>
    <cge:TPSR_Ref TObjectID="42365"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-258209" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 387.000000 -483.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="258209" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42365"/>
     <cge:Term_Ref ObjectID="17941"/>
    <cge:TPSR_Ref TObjectID="42365"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-258198" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 387.000000 -483.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="258198" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42365"/>
     <cge:Term_Ref ObjectID="17941"/>
    <cge:TPSR_Ref TObjectID="42365"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-258221" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 387.000000 -382.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="258221" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42368"/>
     <cge:Term_Ref ObjectID="17947"/>
    <cge:TPSR_Ref TObjectID="42368"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-258222" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 387.000000 -382.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="258222" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42368"/>
     <cge:Term_Ref ObjectID="17947"/>
    <cge:TPSR_Ref TObjectID="42368"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-258211" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 387.000000 -382.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="258211" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42368"/>
     <cge:Term_Ref ObjectID="17947"/>
    <cge:TPSR_Ref TObjectID="42368"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-258234" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 387.000000 -280.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="258234" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42371"/>
     <cge:Term_Ref ObjectID="17953"/>
    <cge:TPSR_Ref TObjectID="42371"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-258235" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 387.000000 -280.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="258235" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42371"/>
     <cge:Term_Ref ObjectID="17953"/>
    <cge:TPSR_Ref TObjectID="42371"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-258224" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 387.000000 -280.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="258224" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42371"/>
     <cge:Term_Ref ObjectID="17953"/>
    <cge:TPSR_Ref TObjectID="42371"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-258247" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 387.000000 -161.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="258247" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42374"/>
     <cge:Term_Ref ObjectID="17959"/>
    <cge:TPSR_Ref TObjectID="42374"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-258248" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 387.000000 -161.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="258248" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42374"/>
     <cge:Term_Ref ObjectID="17959"/>
    <cge:TPSR_Ref TObjectID="42374"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-258237" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 387.000000 -161.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="258237" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42374"/>
     <cge:Term_Ref ObjectID="17959"/>
    <cge:TPSR_Ref TObjectID="42374"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-258260" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 387.000000 -55.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="258260" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42377"/>
     <cge:Term_Ref ObjectID="17965"/>
    <cge:TPSR_Ref TObjectID="42377"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-258261" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 387.000000 -55.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="258261" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42377"/>
     <cge:Term_Ref ObjectID="17965"/>
    <cge:TPSR_Ref TObjectID="42377"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-258250" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 387.000000 -55.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="258250" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42377"/>
     <cge:Term_Ref ObjectID="17965"/>
    <cge:TPSR_Ref TObjectID="42377"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-147498" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1864.000000 -829.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147498" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25795"/>
     <cge:Term_Ref ObjectID="36392"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-147499" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1864.000000 -829.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147499" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25795"/>
     <cge:Term_Ref ObjectID="36392"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-147496" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1864.000000 -829.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147496" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25795"/>
     <cge:Term_Ref ObjectID="36392"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-147503" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2281.000000 -824.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147503" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25799"/>
     <cge:Term_Ref ObjectID="36400"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-147504" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2281.000000 -824.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147504" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25799"/>
     <cge:Term_Ref ObjectID="36400"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-147501" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2281.000000 -824.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147501" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25799"/>
     <cge:Term_Ref ObjectID="36400"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-147508" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2688.000000 -829.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147508" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25803"/>
     <cge:Term_Ref ObjectID="36408"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-147509" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2688.000000 -829.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147509" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25803"/>
     <cge:Term_Ref ObjectID="36408"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-147506" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2688.000000 -829.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147506" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25803"/>
     <cge:Term_Ref ObjectID="36408"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-147514" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1753.000000 -677.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147514" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25807"/>
     <cge:Term_Ref ObjectID="36416"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-147515" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1753.000000 -677.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147515" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25807"/>
     <cge:Term_Ref ObjectID="36416"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-147511" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1753.000000 -677.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147511" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25807"/>
     <cge:Term_Ref ObjectID="36416"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-147530" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1751.000000 -428.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147530" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25812"/>
     <cge:Term_Ref ObjectID="36426"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-147531" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1751.000000 -428.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147531" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25812"/>
     <cge:Term_Ref ObjectID="36426"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-147527" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1751.000000 -428.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147527" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25812"/>
     <cge:Term_Ref ObjectID="36426"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-147538" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2456.000000 -430.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147538" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25816"/>
     <cge:Term_Ref ObjectID="36434"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-147539" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2456.000000 -430.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147539" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25816"/>
     <cge:Term_Ref ObjectID="36434"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-147535" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2456.000000 -430.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147535" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25816"/>
     <cge:Term_Ref ObjectID="36434"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-147520" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2456.000000 -680.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147520" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25809"/>
     <cge:Term_Ref ObjectID="36420"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-147521" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2456.000000 -680.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147521" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25809"/>
     <cge:Term_Ref ObjectID="36420"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-147517" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2456.000000 -680.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147517" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25809"/>
     <cge:Term_Ref ObjectID="36420"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-147588" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1448.000000 60.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147588" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25858"/>
     <cge:Term_Ref ObjectID="36518"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-147589" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1448.000000 60.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147589" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25858"/>
     <cge:Term_Ref ObjectID="36518"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-147586" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1448.000000 60.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147586" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25858"/>
     <cge:Term_Ref ObjectID="36518"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-147558" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1750.000000 60.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147558" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25829"/>
     <cge:Term_Ref ObjectID="36460"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-147559" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1750.000000 60.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147559" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25829"/>
     <cge:Term_Ref ObjectID="36460"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-147556" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1750.000000 60.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147556" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25829"/>
     <cge:Term_Ref ObjectID="36460"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-147573" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2588.000000 59.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147573" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25844"/>
     <cge:Term_Ref ObjectID="36490"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-147574" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2588.000000 59.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147574" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25844"/>
     <cge:Term_Ref ObjectID="36490"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-147571" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2588.000000 59.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147571" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25844"/>
     <cge:Term_Ref ObjectID="36490"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-147583" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2145.000000 -476.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147583" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25854"/>
     <cge:Term_Ref ObjectID="36510"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-147584" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2145.000000 -476.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147584" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25854"/>
     <cge:Term_Ref ObjectID="36510"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-147581" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2145.000000 -476.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147581" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25854"/>
     <cge:Term_Ref ObjectID="36510"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-147523" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1526.000000 -706.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147523" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25791"/>
     <cge:Term_Ref ObjectID="36387"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-147524" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1526.000000 -706.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147524" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25791"/>
     <cge:Term_Ref ObjectID="36387"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-147525" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1526.000000 -706.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147525" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25791"/>
     <cge:Term_Ref ObjectID="36387"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-147526" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1526.000000 -706.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147526" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25791"/>
     <cge:Term_Ref ObjectID="36387"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-147547" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2973.000000 -407.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147547" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25793"/>
     <cge:Term_Ref ObjectID="36389"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-147548" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2973.000000 -407.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147548" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25793"/>
     <cge:Term_Ref ObjectID="36389"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-147549" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2973.000000 -407.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147549" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25793"/>
     <cge:Term_Ref ObjectID="36389"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-147550" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2973.000000 -407.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147550" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25793"/>
     <cge:Term_Ref ObjectID="36389"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-182933" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2950.000000 59.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="182933" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27838"/>
     <cge:Term_Ref ObjectID="39403"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-182934" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2950.000000 59.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="182934" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27838"/>
     <cge:Term_Ref ObjectID="39403"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-182925" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2950.000000 59.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="182925" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27838"/>
     <cge:Term_Ref ObjectID="39403"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-147533" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1949.000000 -540.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147533" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25862"/>
     <cge:Term_Ref ObjectID="36526"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-147534" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1949.000000 -540.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147534" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25862"/>
     <cge:Term_Ref ObjectID="36526"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-147541" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2642.000000 -542.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147541" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25863"/>
     <cge:Term_Ref ObjectID="36533"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-147542" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2642.000000 -542.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147542" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25863"/>
     <cge:Term_Ref ObjectID="36533"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-147568" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1597.000000 59.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147568" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25839"/>
     <cge:Term_Ref ObjectID="36480"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-147569" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1597.000000 59.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147569" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25839"/>
     <cge:Term_Ref ObjectID="36480"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-147566" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1597.000000 59.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147566" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25839"/>
     <cge:Term_Ref ObjectID="36480"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-147578" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1927.000000 59.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147578" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25849"/>
     <cge:Term_Ref ObjectID="36500"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-147579" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1927.000000 59.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147579" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25849"/>
     <cge:Term_Ref ObjectID="36500"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-147576" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1927.000000 59.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147576" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25849"/>
     <cge:Term_Ref ObjectID="36500"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-147553" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2423.000000 56.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147553" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25824"/>
     <cge:Term_Ref ObjectID="36450"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-147554" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2423.000000 56.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147554" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25824"/>
     <cge:Term_Ref ObjectID="36450"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-147551" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2423.000000 56.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147551" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25824"/>
     <cge:Term_Ref ObjectID="36450"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-147563" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2781.000000 63.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147563" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25834"/>
     <cge:Term_Ref ObjectID="36470"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-147564" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2781.000000 63.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147564" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25834"/>
     <cge:Term_Ref ObjectID="36470"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-147561" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2781.000000 63.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147561" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25834"/>
     <cge:Term_Ref ObjectID="36470"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-147543" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1416.000000 -408.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147543" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25792"/>
     <cge:Term_Ref ObjectID="36388"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-147544" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1416.000000 -408.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147544" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25792"/>
     <cge:Term_Ref ObjectID="36388"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-147545" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1416.000000 -408.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147545" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25792"/>
     <cge:Term_Ref ObjectID="36388"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-147546" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1416.000000 -408.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147546" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25792"/>
     <cge:Term_Ref ObjectID="36388"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-265801" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1282.000000 67.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="265801" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42944"/>
     <cge:Term_Ref ObjectID="18876"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-265802" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1282.000000 67.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="265802" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42944"/>
     <cge:Term_Ref ObjectID="18876"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-265798" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1282.000000 67.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="265798" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42944"/>
     <cge:Term_Ref ObjectID="18876"/>
    </metadata>
   </g>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-147770">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1257.000000 342.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25828" ObjectName="SW-YA_XJC.YA_XJC_4836SW"/>
     <cge:Meas_Ref ObjectId="147770"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147786">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1415.000000 325.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25833" ObjectName="SW-YA_XJC.YA_XJC_4856SW"/>
     <cge:Meas_Ref ObjectId="147786"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147802">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1581.000000 335.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25838" ObjectName="SW-YA_XJC.YA_XJC_4876SW"/>
     <cge:Meas_Ref ObjectId="147802"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147818">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2119.000000 361.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25843" ObjectName="SW-YA_XJC.YA_XJC_4826SW"/>
     <cge:Meas_Ref ObjectId="147818"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147834">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2265.000000 362.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25848" ObjectName="SW-YA_XJC.YA_XJC_4846SW"/>
     <cge:Meas_Ref ObjectId="147834"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147851">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2461.000000 348.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25853" ObjectName="SW-YA_XJC.YA_XJC_4866SW"/>
     <cge:Meas_Ref ObjectId="147851"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-182953">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2652.000000 351.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27842" ObjectName="SW-YA_XJC.YA_XJC_4886SW"/>
     <cge:Meas_Ref ObjectId="182953"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-258288">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.000000 1.000000 -1.000000 0.000000 582.000000 -895.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42353" ObjectName="SW-CX_XJCYD.CX_XJCYD_0032SW"/>
     <cge:Meas_Ref ObjectId="258288"/>
    <cge:TPSR_Ref TObjectID="42353"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-258356">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 490.000000 39.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42354" ObjectName="SW-CX_XJCYD.CX_XJCYD_0903SW"/>
     <cge:Meas_Ref ObjectId="258356"/>
    <cge:TPSR_Ref TObjectID="42354"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-258370">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 606.000000 -755.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42358" ObjectName="SW-CX_XJCYD.CX_XJCYD_01167SW"/>
     <cge:Meas_Ref ObjectId="258370"/>
    <cge:TPSR_Ref TObjectID="42358"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-258281">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 921.000000 -1108.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42351" ObjectName="SW-CX_XJCYD.CX_XJCYD_30360SW"/>
     <cge:Meas_Ref ObjectId="258281"/>
    <cge:TPSR_Ref TObjectID="42351"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-258280">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 888.000000 -1135.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42350" ObjectName="SW-CX_XJCYD.CX_XJCYD_3036SW"/>
     <cge:Meas_Ref ObjectId="258280"/>
    <cge:TPSR_Ref TObjectID="42350"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-258369">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.000000 1.000000 -1.000000 0.000000 538.000000 -808.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42357" ObjectName="SW-CX_XJCYD.CX_XJCYD_0111SW"/>
     <cge:Meas_Ref ObjectId="258369"/>
    <cge:TPSR_Ref TObjectID="42357"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-258392">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 605.000000 -643.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42361" ObjectName="SW-CX_XJCYD.CX_XJCYD_01267SW"/>
     <cge:Meas_Ref ObjectId="258392"/>
    <cge:TPSR_Ref TObjectID="42361"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-258391">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.000000 1.000000 -1.000000 0.000000 537.000000 -696.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42360" ObjectName="SW-CX_XJCYD.CX_XJCYD_0121SW"/>
     <cge:Meas_Ref ObjectId="258391"/>
    <cge:TPSR_Ref TObjectID="42360"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-258414">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 606.000000 -533.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42364" ObjectName="SW-CX_XJCYD.CX_XJCYD_01367SW"/>
     <cge:Meas_Ref ObjectId="258414"/>
    <cge:TPSR_Ref TObjectID="42364"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-258413">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.000000 1.000000 -1.000000 0.000000 538.000000 -586.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42363" ObjectName="SW-CX_XJCYD.CX_XJCYD_0131SW"/>
     <cge:Meas_Ref ObjectId="258413"/>
    <cge:TPSR_Ref TObjectID="42363"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-258436">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 606.000000 -420.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42367" ObjectName="SW-CX_XJCYD.CX_XJCYD_01467SW"/>
     <cge:Meas_Ref ObjectId="258436"/>
    <cge:TPSR_Ref TObjectID="42367"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-258435">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.000000 1.000000 -1.000000 0.000000 538.000000 -473.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42366" ObjectName="SW-CX_XJCYD.CX_XJCYD_0141SW"/>
     <cge:Meas_Ref ObjectId="258435"/>
    <cge:TPSR_Ref TObjectID="42366"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-258458">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 604.000000 -309.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42370" ObjectName="SW-CX_XJCYD.CX_XJCYD_01567SW"/>
     <cge:Meas_Ref ObjectId="258458"/>
    <cge:TPSR_Ref TObjectID="42370"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-258457">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.000000 1.000000 -1.000000 0.000000 536.000000 -362.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42369" ObjectName="SW-CX_XJCYD.CX_XJCYD_0151SW"/>
     <cge:Meas_Ref ObjectId="258457"/>
    <cge:TPSR_Ref TObjectID="42369"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-258480">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 607.000000 -211.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42373" ObjectName="SW-CX_XJCYD.CX_XJCYD_01667SW"/>
     <cge:Meas_Ref ObjectId="258480"/>
    <cge:TPSR_Ref TObjectID="42373"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-258479">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.000000 1.000000 -1.000000 0.000000 539.000000 -264.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42372" ObjectName="SW-CX_XJCYD.CX_XJCYD_0161SW"/>
     <cge:Meas_Ref ObjectId="258479"/>
    <cge:TPSR_Ref TObjectID="42372"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-258502">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 607.000000 -98.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42376" ObjectName="SW-CX_XJCYD.CX_XJCYD_01767SW"/>
     <cge:Meas_Ref ObjectId="258502"/>
    <cge:TPSR_Ref TObjectID="42376"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-258501">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.000000 1.000000 -1.000000 0.000000 539.000000 -155.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42375" ObjectName="SW-CX_XJCYD.CX_XJCYD_0171SW"/>
     <cge:Meas_Ref ObjectId="258501"/>
    <cge:TPSR_Ref TObjectID="42375"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-258523">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 608.000000 9.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42379" ObjectName="SW-CX_XJCYD.CX_XJCYD_01867SW"/>
     <cge:Meas_Ref ObjectId="258523"/>
    <cge:TPSR_Ref TObjectID="42379"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-258522">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.000000 1.000000 -1.000000 0.000000 540.000000 -44.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42378" ObjectName="SW-CX_XJCYD.CX_XJCYD_0181SW"/>
     <cge:Meas_Ref ObjectId="258522"/>
    <cge:TPSR_Ref TObjectID="42378"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-258359">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.000000 1.000000 -1.000000 0.000000 536.000000 119.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42355" ObjectName="SW-CX_XJCYD.CX_XJCYD_4903SW"/>
     <cge:Meas_Ref ObjectId="258359"/>
    <cge:TPSR_Ref TObjectID="42355"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 615.000000 133.000000)" xlink:href="#switch2:shape19_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147612">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1730.000000 -734.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25796" ObjectName="SW-YA_XJC.YA_XJC_3811SW"/>
     <cge:Meas_Ref ObjectId="147612"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147614">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1730.000000 -833.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25798" ObjectName="SW-YA_XJC.YA_XJC_3816SW"/>
     <cge:Meas_Ref ObjectId="147614"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147676">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1799.000000 -674.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25808" ObjectName="SW-YA_XJC.YA_XJC_3011SW"/>
     <cge:Meas_Ref ObjectId="147676"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147753">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1799.000000 -332.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25814" ObjectName="SW-YA_XJC.YA_XJC_0011SW"/>
     <cge:Meas_Ref ObjectId="147753"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147816">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1601.000000 -263.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25841" ObjectName="SW-YA_XJC.YA_XJC_0531SW"/>
     <cge:Meas_Ref ObjectId="147816"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147613">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1756.000000 -884.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25797" ObjectName="SW-YA_XJC.YA_XJC_38167SW"/>
     <cge:Meas_Ref ObjectId="147613"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147635">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2146.000000 -833.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25802" ObjectName="SW-YA_XJC.YA_XJC_3826SW"/>
     <cge:Meas_Ref ObjectId="147635"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147634">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2172.000000 -884.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25801" ObjectName="SW-YA_XJC.YA_XJC_38267SW"/>
     <cge:Meas_Ref ObjectId="147634"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147633">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2146.000000 -733.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25800" ObjectName="SW-YA_XJC.YA_XJC_3821SW"/>
     <cge:Meas_Ref ObjectId="147633"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147656">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2559.000000 -842.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25806" ObjectName="SW-YA_XJC.YA_XJC_3836SW"/>
     <cge:Meas_Ref ObjectId="147656"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147655">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2589.000000 -925.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25805" ObjectName="SW-YA_XJC.YA_XJC_38367SW"/>
     <cge:Meas_Ref ObjectId="147655"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147654">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2559.000000 -733.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25804" ObjectName="SW-YA_XJC.YA_XJC_3831SW"/>
     <cge:Meas_Ref ObjectId="147654"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147752">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1799.000000 -429.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25813" ObjectName="SW-YA_XJC.YA_XJC_0016SW"/>
     <cge:Meas_Ref ObjectId="147752"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147754">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1873.000000 -376.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25815" ObjectName="SW-YA_XJC.YA_XJC_00117SW"/>
     <cge:Meas_Ref ObjectId="147754"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147711">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2495.000000 -674.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25810" ObjectName="SW-YA_XJC.YA_XJC_3021SW"/>
     <cge:Meas_Ref ObjectId="147711"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147759">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2495.000000 -332.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25818" ObjectName="SW-YA_XJC.YA_XJC_0022SW"/>
     <cge:Meas_Ref ObjectId="147759"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147758">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2495.000000 -429.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25817" ObjectName="SW-YA_XJC.YA_XJC_0026SW"/>
     <cge:Meas_Ref ObjectId="147758"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147760">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2569.000000 -376.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25819" ObjectName="SW-YA_XJC.YA_XJC_00227SW"/>
     <cge:Meas_Ref ObjectId="147760"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147745">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2146.000000 -662.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="147745"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147864">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2079.000000 -335.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25855" ObjectName="SW-YA_XJC.YA_XJC_0121SW"/>
     <cge:Meas_Ref ObjectId="147864"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147866">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2213.000000 -335.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25857" ObjectName="SW-YA_XJC.YA_XJC_0122SW"/>
     <cge:Meas_Ref ObjectId="147866"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147865">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2243.000000 -387.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25856" ObjectName="SW-YA_XJC.YA_XJC_01220SW"/>
     <cge:Meas_Ref ObjectId="147865"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147815">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1601.000000 -169.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25840" ObjectName="SW-YA_XJC.YA_XJC_0532SW"/>
     <cge:Meas_Ref ObjectId="147815"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147784">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1751.000000 -263.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25831" ObjectName="SW-YA_XJC.YA_XJC_0541SW"/>
     <cge:Meas_Ref ObjectId="147784"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147783">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1751.000000 -169.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25830" ObjectName="SW-YA_XJC.YA_XJC_0542SW"/>
     <cge:Meas_Ref ObjectId="147783"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147849">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1907.000000 -263.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25851" ObjectName="SW-YA_XJC.YA_XJC_0551SW"/>
     <cge:Meas_Ref ObjectId="147849"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147848">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1907.000000 -169.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25850" ObjectName="SW-YA_XJC.YA_XJC_0552SW"/>
     <cge:Meas_Ref ObjectId="147848"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-265405">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2032.000000 -262.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25820" ObjectName="SW-YA_XJC.YA_XJC_0901SW"/>
     <cge:Meas_Ref ObjectId="265405"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-265406">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2096.000000 -252.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25821" ObjectName="SW-YA_XJC.YA_XJC_09017SW"/>
     <cge:Meas_Ref ObjectId="265406"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-265409">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2234.000000 -261.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25822" ObjectName="SW-YA_XJC.YA_XJC_0902SW"/>
     <cge:Meas_Ref ObjectId="265409"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-265408">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2298.000000 -251.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25823" ObjectName="SW-YA_XJC.YA_XJC_09027SW"/>
     <cge:Meas_Ref ObjectId="265408"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147767">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2420.000000 -263.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25825" ObjectName="SW-YA_XJC.YA_XJC_0612SW"/>
     <cge:Meas_Ref ObjectId="147767"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147768">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2420.000000 -169.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25826" ObjectName="SW-YA_XJC.YA_XJC_0613SW"/>
     <cge:Meas_Ref ObjectId="147768"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147831">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2588.000000 -263.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25845" ObjectName="SW-YA_XJC.YA_XJC_0622SW"/>
     <cge:Meas_Ref ObjectId="147831"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147832">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2588.000000 -169.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25846" ObjectName="SW-YA_XJC.YA_XJC_0623SW"/>
     <cge:Meas_Ref ObjectId="147832"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147800">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2761.000000 -170.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25836" ObjectName="SW-YA_XJC.YA_XJC_0633SW"/>
     <cge:Meas_Ref ObjectId="147800"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-182952">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2924.000000 -263.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27841" ObjectName="SW-YA_XJC.YA_XJC_0642SW"/>
     <cge:Meas_Ref ObjectId="182952"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-182949">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2924.000000 -169.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27839" ObjectName="SW-YA_XJC.YA_XJC_0643SW"/>
     <cge:Meas_Ref ObjectId="182949"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147785">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1712.000000 -184.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25832" ObjectName="SW-YA_XJC.YA_XJC_05410SW"/>
     <cge:Meas_Ref ObjectId="147785"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147817">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1563.000000 -184.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25842" ObjectName="SW-YA_XJC.YA_XJC_05310SW"/>
     <cge:Meas_Ref ObjectId="147817"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147850">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1868.000000 -186.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25852" ObjectName="SW-YA_XJC.YA_XJC_05510SW"/>
     <cge:Meas_Ref ObjectId="147850"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147799">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2761.000000 -263.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25835" ObjectName="SW-YA_XJC.YA_XJC_0632SW"/>
     <cge:Meas_Ref ObjectId="147799"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147801">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2723.000000 -185.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25837" ObjectName="SW-YA_XJC.YA_XJC_06320SW"/>
     <cge:Meas_Ref ObjectId="147801"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147833">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2550.000000 -185.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25847" ObjectName="SW-YA_XJC.YA_XJC_06220SW"/>
     <cge:Meas_Ref ObjectId="147833"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-182951">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2886.000000 -184.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27840" ObjectName="SW-YA_XJC.YA_XJC_06420SW"/>
     <cge:Meas_Ref ObjectId="182951"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147769">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2382.000000 -184.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25827" ObjectName="SW-YA_XJC.YA_XJC_06120SW"/>
     <cge:Meas_Ref ObjectId="147769"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147875">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1447.000000 -264.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25860" ObjectName="SW-YA_XJC.YA_XJC_0521SW"/>
     <cge:Meas_Ref ObjectId="147875"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147874">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1447.000000 -169.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25859" ObjectName="SW-YA_XJC.YA_XJC_0522SW"/>
     <cge:Meas_Ref ObjectId="147874"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-265496">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1287.000000 -256.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42945" ObjectName="SW-YA_XJC.YA_XJC_0511SW"/>
     <cge:Meas_Ref ObjectId="265496"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-265497">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1287.000000 -162.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42946" ObjectName="SW-YA_XJC.YA_XJC_0512SW"/>
     <cge:Meas_Ref ObjectId="265497"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-265498">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1249.000000 -177.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42947" ObjectName="SW-YA_XJC.YA_XJC_05110SW"/>
     <cge:Meas_Ref ObjectId="265498"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-265499">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1249.000000 -40.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42949" ObjectName="SW-YA_XJC.YA_XJC_05120SW"/>
     <cge:Meas_Ref ObjectId="265499"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147876">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1407.000000 -186.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25861" ObjectName="SW-YA_XJC.YA_XJC_05210SW"/>
     <cge:Meas_Ref ObjectId="147876"/>
    </metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="cx_配调_配网接线图35_姚安.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="146" y="-1038"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="-54" y="-1044"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-102" y="-1064"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="146" y="-1077"/></g>
   <g href="AVC西教场站.svg" style="fill-opacity:0"><rect height="49" qtmmishow="hidden" stroke="rgb(0,0,0)" width="53" x="150" y="-969"/></g>
   <g href="35kV西教场移动变3号移动变主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="117" x="740" y="-1030"/></g>
   <g href="35kV西教场移动变CX_XJCYD_011间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="550" y="-829"/></g>
   <g href="35kV西教场移动变CX_XJCYD_012间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="549" y="-718"/></g>
   <g href="35kV西教场移动变CX_XJCYD_013间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="551" y="-609"/></g>
   <g href="35kV西教场移动变CX_XJCYD_014间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="551" y="-493"/></g>
   <g href="35kV西教场移动变CX_XJCYD_015间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="549" y="-384"/></g>
   <g href="35kV西教场移动变CX_XJCYD_016间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="551" y="-286"/></g>
   <g href="35kV西教场移动变CX_XJCYD_017间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="554" y="-171"/></g>
   <g href="35kV西教场移动变CX_XJCYD_018间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="554" y="-63"/></g>
   <g href="35kV西教场移动变GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="60" x="3" y="-614"/></g>
   <g href="35kV西教场变35kV黄西太线381间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="1748" y="-812"/></g>
   <g href="35kV西教场变35kV西大连线382间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="2164" y="-812"/></g>
   <g href="35kV西教场变35kV姚西线383间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="2581" y="-816"/></g>
   <g href="35kV西教场变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="52" x="1656" y="-616"/></g>
   <g href="35kV西教场变2号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="53" x="2349" y="-614"/></g>
   <g href="35kV西教场变10kV1号电容器052间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="1465" y="-246"/></g>
   <g href="35kV西教场变10kV城区线053间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1619" y="-246"/></g>
   <g href="35kV西教场变10kV龙岗线054间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1769" y="-246"/></g>
   <g href="35kV西教场变10kV分段012间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="2144" y="-427"/></g>
   <g href="35kV西教场变10kV麻纺厂线061间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="2438" y="-246"/></g>
   <g href="35kV西教场变10kV观测站线062间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="2606" y="-246"/></g>
   <g href="35kV西教场变10kV官屯线063间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="2779" y="-247"/></g>
   <g href="35kV西教场变10kV金龟街线064间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="2942" y="-246"/></g>
   <g href="35kV西教场变10kV仁和线055间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1924" y="-247"/></g>
   <g href="35kV西教场变10kV备用一051间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="1305" y="-239"/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2aa4ea0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1303.000000 404.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d20d30">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 2411.000000 351.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d1ba20">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1461.000000 387.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ce2650">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1625.000000 397.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ccb490">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2165.000000 423.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2dbbfb0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2311.000000 424.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d65360">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2507.000000 410.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e2b0a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2698.000000 413.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d52810">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 681.000000 -907.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d53600">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 681.500000 -876.500000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d35f60">
    <use class="BV-10KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 557.000000 36.148208)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ccf110">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 680.166667 -819.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d3c040">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 680.666667 -789.500000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2dd6000">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 688.166667 109.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d96170">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 688.666667 138.500000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c5bd70">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 939.000000 -1159.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c87610">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 679.166667 -707.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ce65d0">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 679.666667 -677.500000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d0d2e0">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 680.166667 -597.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d4d650">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 680.666667 -567.500000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d5e0a0">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 680.166667 -484.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d21550">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 680.666667 -454.500000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d5a4d0">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 678.166667 -373.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2cebc30">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 678.666667 -343.500000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c81520">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 681.166667 -275.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c85c20">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 681.666667 -245.500000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2cc6a00">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 701.166667 -162.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d030e0">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 701.666667 -136.500000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c45f30">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 682.166667 -55.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c4a630">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 682.666667 -25.500000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c1df70">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1803.000000 -479.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c2e960">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2499.000000 -479.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c90cd0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1946.000000 -651.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c971e0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2195.000000 -602.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ca6020">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1523.500000 -67.500000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33eee20">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2146.000000 -600.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3430a70">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1451.000000 -103.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3456110">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -0.606742 -0.000000 1003.303371 -794.500000)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3456fa0">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -0.606742 -0.000000 977.303371 -682.500000)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3457ea0">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -0.606742 -0.000000 938.303371 -572.500000)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3458da0">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -0.606742 -0.000000 917.303371 -459.500000)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3459ca0">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -0.606742 -0.000000 908.303371 -348.500000)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_345a920">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -0.606742 -0.000000 892.303371 -250.500000)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_345b5a0">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -0.606742 -0.000000 873.303371 -141.500000)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_345c850">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1817.500000 -931.500000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_345d2d0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 2233.500000 -931.500000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_345e000">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 2650.500000 -994.500000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_XJCYD"/>
</svg>