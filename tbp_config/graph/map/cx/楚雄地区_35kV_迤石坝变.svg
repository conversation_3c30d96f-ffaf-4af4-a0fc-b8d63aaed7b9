<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-283" aopId="1835526" id="thSvg" product="E8000V2" version="1.0" viewBox="16 -1164 2399 1380">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="capacitor:shape45">
    <polyline arcFlag="1" points="19,100 17,100 15,99 14,99 12,98 11,97 9,96 8,94 7,92 7,91 6,89 6,87 6,85 7,83 7,82 8,80 9,79 11,77 12,76 14,75 15,75 17,74 19,74 21,74 23,75 24,75 26,76 27,77 29,79 30,80 31,82 31,83 32,85 32,87 " stroke-width="0.0972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="32" x2="19" y1="87" y2="87"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="19" x2="19" y1="100" y2="111"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="36" y1="14" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="36" y1="55" y2="47"/>
    <polyline arcFlag="1" points="36,14 37,14 38,14 38,14 39,14 39,15 40,15 40,16 41,16 41,17 41,17 41,18 42,19 42,19 42,20 41,21 41,21 41,22 41,22 40,23 40,23 39,24 39,24 38,24 38,25 37,25 36,25 " stroke-width="1"/>
    <polyline arcFlag="1" points="36,36 37,36 38,36 38,37 39,37 39,37 40,38 40,38 41,39 41,39 41,40 41,40 42,41 42,42 42,42 41,43 41,44 41,44 41,45 40,45 40,46 39,46 39,47 38,47 38,47 37,47 36,47 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.423529" x1="19" x2="19" y1="87" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.368819" x1="2" x2="35" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.44164" x1="19" x2="36" y1="8" y2="8"/>
    <rect height="23" stroke-width="0.369608" width="12" x="13" y="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.356919" x1="19" x2="36" y1="55" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="2" x2="2" y1="2" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="35" x2="35" y1="2" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72286" x1="19" x2="19" y1="14" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="10" x2="26" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="11" x2="26" y1="21" y2="21"/>
    <polyline arcFlag="1" points="36,25 37,25 38,25 38,25 39,26 39,26 40,26 40,27 41,27 41,28 41,29 41,29 42,30 42,31 42,31 41,32 41,33 41,33 41,34 40,34 40,35 39,35 39,35 38,36 38,36 37,36 36,36 " stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="13" x2="4" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="earth:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="14" y2="14"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="8" x2="8" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="5" y2="36"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="14" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="5" x2="8" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="3" x2="10" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="12" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
   </symbol>
   <symbol id="lightningRod:shape157">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,36 0,46 10,46 5,36 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,24 0,14 10,14 5,24 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="59" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape193">
    <polyline DF8003:Layer="PUBLIC" points="1,5 10,17 19,5 1,5 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="19,39 10,27 1,39 19,39 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="27" y2="17"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="63" x2="63" y1="6" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="61" x2="61" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="58" x2="58" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="58" x2="49" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape175">
    <polyline DF8003:Layer="PUBLIC" points="6,4 0,16 12,16 6,4 6,5 6,4 "/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <rect height="14" stroke-width="1" width="27" x="18" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="5" x2="5" y1="11" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="9" x2="18" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="8" x2="8" y1="2" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="58" x2="23" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape4">
    <rect height="31" stroke-width="2" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="9" x2="9" y1="35" y2="6"/>
   </symbol>
   <symbol id="load:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="6" y2="15"/>
    <polyline DF8003:Layer="PUBLIC" points="1,15 10,15 5,25 0,15 1,15 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="15" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="15" y2="25"/>
   </symbol>
   <symbol id="load:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape45_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="5" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="34" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="25" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape45_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="9" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="34" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="14" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape45-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="5" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="34" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="25" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape45-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="9" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="34" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="14" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="4" y2="14"/>
   </symbol>
   <symbol id="transformer2:shape8_0">
    <circle cx="42" cy="16" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="41,15 41,40 70,40 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="56" x2="98" y1="14" y2="14"/>
    <polyline DF8003:Layer="PUBLIC" points="84,14 71,20 71,7 84,14 83,14 84,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="73" x2="73" y1="34" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="75" x2="75" y1="44" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="78" x2="78" y1="43" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="47" x2="72" y1="39" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="36" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="47" y1="15" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="47" y1="15" y2="11"/>
   </symbol>
   <symbol id="transformer2:shape8_1">
    <circle cx="20" cy="16" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="11" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="22" y1="15" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="22" y1="15" y2="11"/>
   </symbol>
   <symbol id="transformer2:shape4_0">
    <circle cx="31" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.911765" x1="1" x2="56" y1="49" y2="80"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="56" x2="56" y1="77" y2="77"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="56" x2="54" y1="80" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="51" x2="56" y1="80" y2="80"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="42" x2="26" y1="24" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="42" x2="26" y1="24" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="26" y1="33" y2="16"/>
   </symbol>
   <symbol id="transformer2:shape4_1">
    <ellipse cx="31" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="30" x2="30" y1="58" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="30" x2="38" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="22" x2="30" y1="74" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape40_0">
    <circle cx="30" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="32" y1="53" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="26" y2="26"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,56 6,56 6,28 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="32,14 26,27 39,27 32,14 32,15 32,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="42" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="35" y1="57" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="26" y1="57" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="57" y2="62"/>
   </symbol>
   <symbol id="transformer2:shape40_1">
    <circle cx="31" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="81" y2="81"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="87" y2="87"/>
    <polyline DF8003:Layer="PUBLIC" points="30,87 26,78 36,78 30,87 "/>
   </symbol>
   <symbol id="voltageTransformer:shape135">
    <rect height="24" stroke-width="0.379884" width="14" x="1" y="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="6" y2="52"/>
    <ellipse cx="8" cy="67" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <ellipse cx="8" cy="59" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="6" y1="69" y2="69"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="5" y1="57" y2="57"/>
   </symbol>
   <symbol id="voltageTransformer:shape144">
    <circle cx="17" cy="36" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.16541" x1="0" x2="10" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.07362" x1="3" x2="7" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.16541" x1="2" x2="8" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.16541" x1="5" x2="10" y1="37" y2="37"/>
    <polyline points="5,7 5,37 " stroke-width="1"/>
    <circle cx="17" cy="25" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="19" x2="17" y1="33" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="19" x2="17" y1="39" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="17" x2="15" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="30" x2="30" y1="20" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="34" x2="30" y1="22" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="34" x2="30" y1="24" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="43" x2="41" y1="27" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="43" x2="41" y1="33" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="41" x2="38" y1="30" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="32" x2="30" y1="33" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="30" x2="27" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="32" x2="30" y1="39" y2="36"/>
    <circle cx="30" cy="23" r="7.5" stroke-width="0.804311"/>
    <circle cx="29" cy="36" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="19" x2="17" y1="22" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="19" x2="17" y1="28" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="17" x2="14" y1="25" y2="25"/>
    <circle cx="40" cy="30" r="7.5" stroke-width="0.804311"/>
   </symbol>
   <symbol id="voltageTransformer:shape75">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="12" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649573" x1="6" x2="28" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.605383" x1="22" x2="22" y1="25" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="22" x2="18" y1="25" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="26" x2="22" y1="23" y2="25"/>
    <circle cx="22" cy="25" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.605383" x1="35" x2="35" y1="25" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="35" x2="31" y1="25" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="39" x2="35" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.605383" x1="49" x2="49" y1="12" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="49" x2="45" y1="12" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="53" x2="49" y1="10" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.605383" x1="35" x2="35" y1="12" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="35" x2="31" y1="12" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="39" x2="35" y1="10" y2="12"/>
    <circle cx="35" cy="12" r="7.5" stroke-width="0.804311"/>
    <circle cx="35" cy="25" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="46" x2="51" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="47" x2="46" y1="24" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="50" x2="51" y1="24" y2="28"/>
    <circle cx="48" cy="12" r="7.5" stroke-width="0.804311"/>
    <circle cx="48" cy="25" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.475524" x1="6" x2="6" y1="27" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="3" x2="9" y1="30" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="5" x2="7" y1="33" y2="33"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a8e690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a8f050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2a8f9d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2a906b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2a918b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2a924c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a93070" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2a93aa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2a950b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2a950b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a96a20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a96a20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a98890" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a98890" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_2a998a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a9b530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2a9c180" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2a9d060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2a9d940" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a9f100" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a9f900" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a9fff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2aa0a10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2aa1bf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2aa2570" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2aa3060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2aa3a20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2aa4f10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2aa5a70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2aa6aa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2aa76e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2ab5eb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2aa8d60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2aa9f00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2aab4e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1390" width="2409" x="11" y="-1169"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-234108">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1267.000000 -282.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39001" ObjectName="SW-CX_YSB.CX_YSB_001BK"/>
     <cge:Meas_Ref ObjectId="234108"/>
    <cge:TPSR_Ref TObjectID="39001"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-234126">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 970.000000 -111.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39010" ObjectName="SW-CX_YSB.CX_YSB_032BK"/>
     <cge:Meas_Ref ObjectId="234126"/>
    <cge:TPSR_Ref TObjectID="39010"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-234164">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1530.127932 -117.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39042" ObjectName="SW-CX_YSB.CX_YSB_012BK"/>
     <cge:Meas_Ref ObjectId="234164"/>
    <cge:TPSR_Ref TObjectID="39042"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-234136">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1624.118337 -113.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39018" ObjectName="SW-CX_YSB.CX_YSB_041BK"/>
     <cge:Meas_Ref ObjectId="234136"/>
    <cge:TPSR_Ref TObjectID="39018"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-234146">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1913.286780 -113.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39026" ObjectName="SW-CX_YSB.CX_YSB_043BK"/>
     <cge:Meas_Ref ObjectId="234146"/>
    <cge:TPSR_Ref TObjectID="39026"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-234141">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1775.689765 -110.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39022" ObjectName="SW-CX_YSB.CX_YSB_042BK"/>
     <cge:Meas_Ref ObjectId="234141"/>
    <cge:TPSR_Ref TObjectID="39022"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-234158">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2236.189765 -112.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39036" ObjectName="SW-CX_YSB.CX_YSB_045BK"/>
     <cge:Meas_Ref ObjectId="234158"/>
    <cge:TPSR_Ref TObjectID="39036"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-234119">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1623.708955 -278.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39007" ObjectName="SW-CX_YSB.CX_YSB_002BK"/>
     <cge:Meas_Ref ObjectId="234119"/>
    <cge:TPSR_Ref TObjectID="39007"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-234104">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1267.000000 -582.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38998" ObjectName="SW-CX_YSB.CX_YSB_301BK"/>
     <cge:Meas_Ref ObjectId="234104"/>
    <cge:TPSR_Ref TObjectID="38998"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-234085">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1432.000000 -752.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38992" ObjectName="SW-CX_YSB.CX_YSB_327BK"/>
     <cge:Meas_Ref ObjectId="234085"/>
    <cge:TPSR_Ref TObjectID="38992"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-234115">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1623.708955 -582.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39004" ObjectName="SW-CX_YSB.CX_YSB_302BK"/>
     <cge:Meas_Ref ObjectId="234115"/>
    <cge:TPSR_Ref TObjectID="39004"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-234131">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1267.000000 -109.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39014" ObjectName="SW-CX_YSB.CX_YSB_034BK"/>
     <cge:Meas_Ref ObjectId="234131"/>
    <cge:TPSR_Ref TObjectID="39014"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-234151">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 817.000000 -112.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39030" ObjectName="SW-CX_YSB.CX_YSB_031BK"/>
     <cge:Meas_Ref ObjectId="234151"/>
    <cge:TPSR_Ref TObjectID="39030"/></metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2c186b0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 1375.000000 -845.000000)" xlink:href="#voltageTransformer:shape135"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2cd21e0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1425.000000 -463.000000)" xlink:href="#voltageTransformer:shape144"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d2b8c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 870.000000 -429.000000)" xlink:href="#voltageTransformer:shape75"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d30a90">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1957.089552 -422.000000)" xlink:href="#voltageTransformer:shape75"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2226.189765 180.000000)" xlink:href="#capacitor:shape45"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 807.000000 162.000000)" xlink:href="#capacitor:shape45"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 1137.000000 -23.000000)" xlink:href="#transformer2:shape8_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 1137.000000 -23.000000)" xlink:href="#transformer2:shape8_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_YSB.CX_YSB_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="58624"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1245.000000 -377.000000)" xlink:href="#transformer2:shape4_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1245.000000 -377.000000)" xlink:href="#transformer2:shape4_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="39057" ObjectName="TF-CX_YSB.CX_YSB_1T"/>
    <cge:TPSR_Ref TObjectID="39057"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_YSB.CX_YSB_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="58628"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1601.708955 -376.000000)" xlink:href="#transformer2:shape4_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1601.708955 -376.000000)" xlink:href="#transformer2:shape4_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="39058" ObjectName="TF-CX_YSB.CX_YSB_2T"/>
    <cge:TPSR_Ref TObjectID="39058"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2030.892324 62.000000)" xlink:href="#transformer2:shape40_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2030.892324 62.000000)" xlink:href="#transformer2:shape40_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2c17bf0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1501.000000 -803.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c19be0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1432.000000 -517.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c1a2c0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 1385.000000 -564.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f2bbd0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 973.000000 -8.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f2faa0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 939.000000 -27.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f36b90">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1069.000000 17.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c77b50">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1627.118337 -10.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c7ba20">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1596.000000 -29.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c84be0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1916.286780 -10.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c88ab0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1889.000000 -29.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c92390">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1779.689765 -7.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c96260">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1749.000000 -26.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c9fb40">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2018.892324 -7.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ca91c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2240.189765 -9.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2cce0e0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1539.500000 -1083.500000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ce5ea0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1436.000000 -864.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2cf3570">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1113.000000 -52.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2cfa8d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1236.000000 -25.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d036a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1266.000000 -25.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d0bf20">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2279.000000 67.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d0cc90">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2198.000000 81.000000)" xlink:href="#lightningRod:shape175"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d0d370">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 2215.500000 177.500000)" xlink:href="#lightningRod:shape175"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d0e630">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2172.000000 -73.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d26200">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 766.000000 66.000000)" xlink:href="#lightningRod:shape175"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d268e0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 783.500000 159.500000)" xlink:href="#lightningRod:shape175"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d276e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 740.000000 -73.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d286f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 816.000000 -27.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d293d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 847.000000 49.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d2a140">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 896.000000 -356.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d2ab10">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 961.000000 -344.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d2f680">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1983.089552 -349.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d2fda0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2048.089552 -337.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d361c0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1271.000000 -474.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d384f0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1628.000000 -476.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 107.000000 -953.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-234035" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 722.000000 -158.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234035" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39030"/>
     <cge:Term_Ref ObjectID="58568"/>
    <cge:TPSR_Ref TObjectID="39030"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-234032" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 722.000000 -158.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234032" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39030"/>
     <cge:Term_Ref ObjectID="58568"/>
    <cge:TPSR_Ref TObjectID="39030"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-234036" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 722.000000 -158.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234036" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39030"/>
     <cge:Term_Ref ObjectID="58568"/>
    <cge:TPSR_Ref TObjectID="39030"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-234069" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 953.000000 163.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234069" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39010"/>
     <cge:Term_Ref ObjectID="58528"/>
    <cge:TPSR_Ref TObjectID="39010"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-234070" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 953.000000 163.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234070" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39010"/>
     <cge:Term_Ref ObjectID="58528"/>
    <cge:TPSR_Ref TObjectID="39010"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-234066" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 953.000000 163.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234066" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39010"/>
     <cge:Term_Ref ObjectID="58528"/>
    <cge:TPSR_Ref TObjectID="39010"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-234063" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1258.000000 163.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234063" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39014"/>
     <cge:Term_Ref ObjectID="58536"/>
    <cge:TPSR_Ref TObjectID="39014"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-234064" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1258.000000 163.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234064" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39014"/>
     <cge:Term_Ref ObjectID="58536"/>
    <cge:TPSR_Ref TObjectID="39014"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-234060" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1258.000000 163.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234060" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39014"/>
     <cge:Term_Ref ObjectID="58536"/>
    <cge:TPSR_Ref TObjectID="39014"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-234075" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1476.000000 -71.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234075" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39042"/>
     <cge:Term_Ref ObjectID="58592"/>
    <cge:TPSR_Ref TObjectID="39042"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-234076" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1476.000000 -71.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234076" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39042"/>
     <cge:Term_Ref ObjectID="58592"/>
    <cge:TPSR_Ref TObjectID="39042"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-234072" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1476.000000 -71.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234072" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39042"/>
     <cge:Term_Ref ObjectID="58592"/>
    <cge:TPSR_Ref TObjectID="39042"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-234057" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1625.000000 163.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234057" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39018"/>
     <cge:Term_Ref ObjectID="58544"/>
    <cge:TPSR_Ref TObjectID="39018"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-234058" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1625.000000 163.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234058" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39018"/>
     <cge:Term_Ref ObjectID="58544"/>
    <cge:TPSR_Ref TObjectID="39018"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-234054" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1625.000000 163.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234054" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39018"/>
     <cge:Term_Ref ObjectID="58544"/>
    <cge:TPSR_Ref TObjectID="39018"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-234051" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1774.000000 163.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234051" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39022"/>
     <cge:Term_Ref ObjectID="58552"/>
    <cge:TPSR_Ref TObjectID="39022"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-234052" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1774.000000 163.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234052" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39022"/>
     <cge:Term_Ref ObjectID="58552"/>
    <cge:TPSR_Ref TObjectID="39022"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-234048" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1774.000000 163.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234048" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39022"/>
     <cge:Term_Ref ObjectID="58552"/>
    <cge:TPSR_Ref TObjectID="39022"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-234045" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1916.000000 163.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234045" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39026"/>
     <cge:Term_Ref ObjectID="58560"/>
    <cge:TPSR_Ref TObjectID="39026"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-234046" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1916.000000 163.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234046" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39026"/>
     <cge:Term_Ref ObjectID="58560"/>
    <cge:TPSR_Ref TObjectID="39026"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-234042" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1916.000000 163.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234042" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39026"/>
     <cge:Term_Ref ObjectID="58560"/>
    <cge:TPSR_Ref TObjectID="39026"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-234040" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2384.000000 -157.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234040" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39036"/>
     <cge:Term_Ref ObjectID="58580"/>
    <cge:TPSR_Ref TObjectID="39036"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-234037" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2384.000000 -157.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234037" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39036"/>
     <cge:Term_Ref ObjectID="58580"/>
    <cge:TPSR_Ref TObjectID="39036"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-234041" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2384.000000 -157.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234041" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39036"/>
     <cge:Term_Ref ObjectID="58580"/>
    <cge:TPSR_Ref TObjectID="39036"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-233980" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1175.000000 -327.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="233980" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39001"/>
     <cge:Term_Ref ObjectID="58510"/>
    <cge:TPSR_Ref TObjectID="39001"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-233981" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1175.000000 -327.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="233981" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39001"/>
     <cge:Term_Ref ObjectID="58510"/>
    <cge:TPSR_Ref TObjectID="39001"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-233971" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1175.000000 -327.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="233971" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39001"/>
     <cge:Term_Ref ObjectID="58510"/>
    <cge:TPSR_Ref TObjectID="39001"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-234006" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1756.000000 -317.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234006" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39007"/>
     <cge:Term_Ref ObjectID="58522"/>
    <cge:TPSR_Ref TObjectID="39007"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-234007" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1756.000000 -317.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234007" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39007"/>
     <cge:Term_Ref ObjectID="58522"/>
    <cge:TPSR_Ref TObjectID="39007"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-233997" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1756.000000 -317.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="233997" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39007"/>
     <cge:Term_Ref ObjectID="58522"/>
    <cge:TPSR_Ref TObjectID="39007"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-233956" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1561.000000 -781.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="233956" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38992"/>
     <cge:Term_Ref ObjectID="58492"/>
    <cge:TPSR_Ref TObjectID="38992"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-233957" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1561.000000 -781.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="233957" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38992"/>
     <cge:Term_Ref ObjectID="58492"/>
    <cge:TPSR_Ref TObjectID="38992"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-233947" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1561.000000 -781.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="233947" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38992"/>
     <cge:Term_Ref ObjectID="58492"/>
    <cge:TPSR_Ref TObjectID="38992"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-234011" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1211.000000 -793.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234011" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38988"/>
     <cge:Term_Ref ObjectID="58487"/>
    <cge:TPSR_Ref TObjectID="38988"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-234012" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1211.000000 -793.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234012" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38988"/>
     <cge:Term_Ref ObjectID="58487"/>
    <cge:TPSR_Ref TObjectID="38988"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-234013" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1211.000000 -793.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234013" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38988"/>
     <cge:Term_Ref ObjectID="58487"/>
    <cge:TPSR_Ref TObjectID="38988"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-234017" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1211.000000 -793.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234017" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38988"/>
     <cge:Term_Ref ObjectID="58487"/>
    <cge:TPSR_Ref TObjectID="38988"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-234014" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1211.000000 -793.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234014" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38988"/>
     <cge:Term_Ref ObjectID="58487"/>
    <cge:TPSR_Ref TObjectID="38988"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-234018" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 732.000000 -337.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234018" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38989"/>
     <cge:Term_Ref ObjectID="58488"/>
    <cge:TPSR_Ref TObjectID="38989"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-234019" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 732.000000 -337.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234019" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38989"/>
     <cge:Term_Ref ObjectID="58488"/>
    <cge:TPSR_Ref TObjectID="38989"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-234020" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 732.000000 -337.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234020" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38989"/>
     <cge:Term_Ref ObjectID="58488"/>
    <cge:TPSR_Ref TObjectID="38989"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-234024" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 732.000000 -337.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234024" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38989"/>
     <cge:Term_Ref ObjectID="58488"/>
    <cge:TPSR_Ref TObjectID="38989"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-234021" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 732.000000 -337.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234021" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38989"/>
     <cge:Term_Ref ObjectID="58488"/>
    <cge:TPSR_Ref TObjectID="38989"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-234025" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2338.000000 -329.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234025" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38990"/>
     <cge:Term_Ref ObjectID="58489"/>
    <cge:TPSR_Ref TObjectID="38990"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-234026" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2338.000000 -329.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234026" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38990"/>
     <cge:Term_Ref ObjectID="58489"/>
    <cge:TPSR_Ref TObjectID="38990"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-234027" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2338.000000 -329.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234027" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38990"/>
     <cge:Term_Ref ObjectID="58489"/>
    <cge:TPSR_Ref TObjectID="38990"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-234031" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2338.000000 -329.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234031" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38990"/>
     <cge:Term_Ref ObjectID="58489"/>
    <cge:TPSR_Ref TObjectID="38990"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-234028" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2338.000000 -329.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234028" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38990"/>
     <cge:Term_Ref ObjectID="58489"/>
    <cge:TPSR_Ref TObjectID="38990"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-233968" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1157.000000 -599.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="233968" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38998"/>
     <cge:Term_Ref ObjectID="58504"/>
    <cge:TPSR_Ref TObjectID="38998"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-233969" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1157.000000 -599.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="233969" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38998"/>
     <cge:Term_Ref ObjectID="58504"/>
    <cge:TPSR_Ref TObjectID="38998"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-233959" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1157.000000 -599.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="233959" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38998"/>
     <cge:Term_Ref ObjectID="58504"/>
    <cge:TPSR_Ref TObjectID="38998"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-233994" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1751.000000 -618.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="233994" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39004"/>
     <cge:Term_Ref ObjectID="58516"/>
    <cge:TPSR_Ref TObjectID="39004"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-233995" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1751.000000 -618.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="233995" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39004"/>
     <cge:Term_Ref ObjectID="58516"/>
    <cge:TPSR_Ref TObjectID="39004"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-233985" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1751.000000 -618.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="233985" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39004"/>
     <cge:Term_Ref ObjectID="58516"/>
    <cge:TPSR_Ref TObjectID="39004"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="2" id="ME-233983" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1171.000000 -507.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="233983" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39057"/>
     <cge:Term_Ref ObjectID="58625"/>
    <cge:TPSR_Ref TObjectID="39057"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-233984" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1171.000000 -507.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="233984" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39057"/>
     <cge:Term_Ref ObjectID="58625"/>
    <cge:TPSR_Ref TObjectID="39057"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="2" id="ME-234009" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1820.000000 -489.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234009" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39058"/>
     <cge:Term_Ref ObjectID="58626"/>
    <cge:TPSR_Ref TObjectID="39058"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-234010" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1820.000000 -489.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234010" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39058"/>
     <cge:Term_Ref ObjectID="58626"/>
    <cge:TPSR_Ref TObjectID="39058"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="119" y="-1012"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="119" y="-1012"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="70" y="-1029"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="70" y="-1029"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="1450" y="-781"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="1450" y="-781"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="52" x="1304" y="-429"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="52" x="1304" y="-429"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="53" x="1661" y="-428"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="53" x="1661" y="-428"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="835" y="-141"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="835" y="-141"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="2255" y="-141"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="2255" y="-141"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1932" y="-142"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1932" y="-142"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="1794" y="-139"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="1794" y="-139"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="1642" y="-142"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="1642" y="-142"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1285" y="-138"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1285" y="-138"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="988" y="-140"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="988" y="-140"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="1549" y="-146"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="1549" y="-146"/></g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_客户变35.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="119" y="-1012"/></g>
   <g href="cx_索引_接线图_客户变110.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="70" y="-1029"/></g>
   <g href="35kV迤石坝变35kV迤石坝T接线327断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="1450" y="-781"/></g>
   <g href="35kV迤石坝变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="52" x="1304" y="-429"/></g>
   <g href="35kV迤石坝变2号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="53" x="1661" y="-428"/></g>
   <g href="35kV迤石坝变10kV1号电容器组031断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="835" y="-141"/></g>
   <g href="35kV迤石坝变10kV2号电容器组045断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="2255" y="-141"/></g>
   <g href="35kV迤石坝变10kV备用一043断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1932" y="-142"/></g>
   <g href="35kV迤石坝变10kV飒河线042断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="1794" y="-139"/></g>
   <g href="35kV迤石坝变10kV伍庄村1号支洞线041断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="1642" y="-142"/></g>
   <g href="35kV迤石坝变10kV凤河线034断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1285" y="-138"/></g>
   <g href="35kV迤石坝变10kV迤石坝料厂线032断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="988" y="-140"/></g>
   <g href="35kV迤石坝变10kV分段012断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="1549" y="-146"/></g>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-234087">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1432.000000 -983.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38995" ObjectName="SW-CX_YSB.CX_YSB_3276SW"/>
     <cge:Meas_Ref ObjectId="234087"/>
    <cge:TPSR_Ref TObjectID="38995"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-234089">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1454.000000 -1038.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38997" ObjectName="SW-CX_YSB.CX_YSB_32767SW"/>
     <cge:Meas_Ref ObjectId="234089"/>
    <cge:TPSR_Ref TObjectID="38997"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-234088">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1453.000000 -966.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38996" ObjectName="SW-CX_YSB.CX_YSB_32760SW"/>
     <cge:Meas_Ref ObjectId="234088"/>
    <cge:TPSR_Ref TObjectID="38996"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-234109">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1266.000000 -256.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39002" ObjectName="SW-CX_YSB.CX_YSB_001XC"/>
     <cge:Meas_Ref ObjectId="234109"/>
    <cge:TPSR_Ref TObjectID="39002"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-234109">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1266.000000 -321.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39003" ObjectName="SW-CX_YSB.CX_YSB_001XC1"/>
     <cge:Meas_Ref ObjectId="234109"/>
    <cge:TPSR_Ref TObjectID="39003"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-234128">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1004.000000 -40.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39013" ObjectName="SW-CX_YSB.CX_YSB_03260SW"/>
     <cge:Meas_Ref ObjectId="234128"/>
    <cge:TPSR_Ref TObjectID="39013"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-234127">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 969.000000 -150.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39011" ObjectName="SW-CX_YSB.CX_YSB_032XC"/>
     <cge:Meas_Ref ObjectId="234127"/>
    <cge:TPSR_Ref TObjectID="39011"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-234127">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 969.000000 -85.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39012" ObjectName="SW-CX_YSB.CX_YSB_032XC1"/>
     <cge:Meas_Ref ObjectId="234127"/>
    <cge:TPSR_Ref TObjectID="39012"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-234165">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1529.127932 -91.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39044" ObjectName="SW-CX_YSB.CX_YSB_012XC1"/>
     <cge:Meas_Ref ObjectId="234165"/>
    <cge:TPSR_Ref TObjectID="39044"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-234166">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1374.127932 -149.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39045" ObjectName="SW-CX_YSB.CX_YSB_0121XC"/>
     <cge:Meas_Ref ObjectId="234166"/>
    <cge:TPSR_Ref TObjectID="39045"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-234166">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1374.127932 -98.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39046" ObjectName="SW-CX_YSB.CX_YSB_0121XC1"/>
     <cge:Meas_Ref ObjectId="234166"/>
    <cge:TPSR_Ref TObjectID="39046"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-234165">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1529.127932 -156.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39043" ObjectName="SW-CX_YSB.CX_YSB_012XC"/>
     <cge:Meas_Ref ObjectId="234165"/>
    <cge:TPSR_Ref TObjectID="39043"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-234138">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1661.000000 -42.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39021" ObjectName="SW-CX_YSB.CX_YSB_04160SW"/>
     <cge:Meas_Ref ObjectId="234138"/>
    <cge:TPSR_Ref TObjectID="39021"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-234137">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1623.118337 -152.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39019" ObjectName="SW-CX_YSB.CX_YSB_041XC"/>
     <cge:Meas_Ref ObjectId="234137"/>
    <cge:TPSR_Ref TObjectID="39019"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-234137">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1623.118337 -87.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39020" ObjectName="SW-CX_YSB.CX_YSB_041XC1"/>
     <cge:Meas_Ref ObjectId="234137"/>
    <cge:TPSR_Ref TObjectID="39020"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-234148">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1954.000000 -42.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39029" ObjectName="SW-CX_YSB.CX_YSB_04360SW"/>
     <cge:Meas_Ref ObjectId="234148"/>
    <cge:TPSR_Ref TObjectID="39029"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-234147">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1912.286780 -152.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39027" ObjectName="SW-CX_YSB.CX_YSB_043XC"/>
     <cge:Meas_Ref ObjectId="234147"/>
    <cge:TPSR_Ref TObjectID="39027"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-234147">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1912.286780 -87.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39028" ObjectName="SW-CX_YSB.CX_YSB_043XC1"/>
     <cge:Meas_Ref ObjectId="234147"/>
    <cge:TPSR_Ref TObjectID="39028"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-234143">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1814.000000 -39.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39025" ObjectName="SW-CX_YSB.CX_YSB_04260SW"/>
     <cge:Meas_Ref ObjectId="234143"/>
    <cge:TPSR_Ref TObjectID="39025"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-234142">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1774.689765 -149.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39023" ObjectName="SW-CX_YSB.CX_YSB_042XC"/>
     <cge:Meas_Ref ObjectId="234142"/>
    <cge:TPSR_Ref TObjectID="39023"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-234142">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1774.689765 -84.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39024" ObjectName="SW-CX_YSB.CX_YSB_042XC1"/>
     <cge:Meas_Ref ObjectId="234142"/>
    <cge:TPSR_Ref TObjectID="39024"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-234171">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2051.892324 -153.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39051" ObjectName="SW-CX_YSB.CX_YSB_0441XC"/>
     <cge:Meas_Ref ObjectId="234171"/>
    <cge:TPSR_Ref TObjectID="39051"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-234171">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2051.892324 -88.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39052" ObjectName="SW-CX_YSB.CX_YSB_0441XC1"/>
     <cge:Meas_Ref ObjectId="234171"/>
    <cge:TPSR_Ref TObjectID="39052"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-234161">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2284.000000 -39.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39040" ObjectName="SW-CX_YSB.CX_YSB_04560SW"/>
     <cge:Meas_Ref ObjectId="234161"/>
    <cge:TPSR_Ref TObjectID="39040"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-234159">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2235.189765 -151.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39037" ObjectName="SW-CX_YSB.CX_YSB_045XC"/>
     <cge:Meas_Ref ObjectId="234159"/>
    <cge:TPSR_Ref TObjectID="39037"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-234162">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2195.000000 55.000000)" xlink:href="#switch2:shape45_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39041" ObjectName="SW-CX_YSB.CX_YSB_04567SW"/>
     <cge:Meas_Ref ObjectId="234162"/>
    <cge:TPSR_Ref TObjectID="39041"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-234160">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2236.189765 54.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39039" ObjectName="SW-CX_YSB.CX_YSB_0456SW"/>
     <cge:Meas_Ref ObjectId="234160"/>
    <cge:TPSR_Ref TObjectID="39039"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-234159">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2235.189765 -86.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39038" ObjectName="SW-CX_YSB.CX_YSB_045XC1"/>
     <cge:Meas_Ref ObjectId="234159"/>
    <cge:TPSR_Ref TObjectID="39038"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-234120">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1622.708955 -252.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39008" ObjectName="SW-CX_YSB.CX_YSB_002XC"/>
     <cge:Meas_Ref ObjectId="234120"/>
    <cge:TPSR_Ref TObjectID="39008"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-234120">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1622.708955 -317.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39009" ObjectName="SW-CX_YSB.CX_YSB_002XC1"/>
     <cge:Meas_Ref ObjectId="234120"/>
    <cge:TPSR_Ref TObjectID="39009"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-234105">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1266.000000 -556.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39000" ObjectName="SW-CX_YSB.CX_YSB_301XC1"/>
     <cge:Meas_Ref ObjectId="234105"/>
    <cge:TPSR_Ref TObjectID="39000"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-234105">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1266.000000 -624.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38999" ObjectName="SW-CX_YSB.CX_YSB_301XC"/>
     <cge:Meas_Ref ObjectId="234105"/>
    <cge:TPSR_Ref TObjectID="38999"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-234086">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1431.000000 -726.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38993" ObjectName="SW-CX_YSB.CX_YSB_327XC"/>
     <cge:Meas_Ref ObjectId="234086"/>
    <cge:TPSR_Ref TObjectID="38993"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-234086">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1431.000000 -791.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38994" ObjectName="SW-CX_YSB.CX_YSB_327XC1"/>
     <cge:Meas_Ref ObjectId="234086"/>
    <cge:TPSR_Ref TObjectID="38994"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-234116">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1622.708955 -556.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39006" ObjectName="SW-CX_YSB.CX_YSB_302XC1"/>
     <cge:Meas_Ref ObjectId="234116"/>
    <cge:TPSR_Ref TObjectID="39006"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-234116">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1622.708955 -624.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39005" ObjectName="SW-CX_YSB.CX_YSB_302XC"/>
     <cge:Meas_Ref ObjectId="234116"/>
    <cge:TPSR_Ref TObjectID="39005"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-234133">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1301.000000 -38.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39017" ObjectName="SW-CX_YSB.CX_YSB_03460SW"/>
     <cge:Meas_Ref ObjectId="234133"/>
    <cge:TPSR_Ref TObjectID="39017"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-234132">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1266.000000 -148.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39015" ObjectName="SW-CX_YSB.CX_YSB_034XC"/>
     <cge:Meas_Ref ObjectId="234132"/>
    <cge:TPSR_Ref TObjectID="39015"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-234132">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1266.000000 -83.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39016" ObjectName="SW-CX_YSB.CX_YSB_034XC1"/>
     <cge:Meas_Ref ObjectId="234132"/>
    <cge:TPSR_Ref TObjectID="39016"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-234154">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 852.000000 -39.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39034" ObjectName="SW-CX_YSB.CX_YSB_03160SW"/>
     <cge:Meas_Ref ObjectId="234154"/>
    <cge:TPSR_Ref TObjectID="39034"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-234152">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 816.000000 -151.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39031" ObjectName="SW-CX_YSB.CX_YSB_031XC"/>
     <cge:Meas_Ref ObjectId="234152"/>
    <cge:TPSR_Ref TObjectID="39031"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-234155">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 763.000000 37.000000)" xlink:href="#switch2:shape45_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39035" ObjectName="SW-CX_YSB.CX_YSB_03167SW"/>
     <cge:Meas_Ref ObjectId="234155"/>
    <cge:TPSR_Ref TObjectID="39035"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-234153">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 817.000000 36.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39033" ObjectName="SW-CX_YSB.CX_YSB_0316SW"/>
     <cge:Meas_Ref ObjectId="234153"/>
    <cge:TPSR_Ref TObjectID="39033"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-234152">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 816.000000 -86.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39032" ObjectName="SW-CX_YSB.CX_YSB_031XC1"/>
     <cge:Meas_Ref ObjectId="234152"/>
    <cge:TPSR_Ref TObjectID="39032"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-234173">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1981.892324 -279.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39056" ObjectName="SW-CX_YSB.CX_YSB_0902XC1"/>
     <cge:Meas_Ref ObjectId="234173"/>
    <cge:TPSR_Ref TObjectID="39056"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-234173">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1981.892324 -232.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39055" ObjectName="SW-CX_YSB.CX_YSB_0902XC"/>
     <cge:Meas_Ref ObjectId="234173"/>
    <cge:TPSR_Ref TObjectID="39055"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-234169">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1430.892324 -629.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39047" ObjectName="SW-CX_YSB.CX_YSB_3901XC"/>
     <cge:Meas_Ref ObjectId="234169"/>
    <cge:TPSR_Ref TObjectID="39047"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-234169">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1430.892324 -582.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39048" ObjectName="SW-CX_YSB.CX_YSB_3901XC1"/>
     <cge:Meas_Ref ObjectId="234169"/>
    <cge:TPSR_Ref TObjectID="39048"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-234172">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 894.892324 -238.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39053" ObjectName="SW-CX_YSB.CX_YSB_0901XC"/>
     <cge:Meas_Ref ObjectId="234172"/>
    <cge:TPSR_Ref TObjectID="39053"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-234172">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 894.892324 -285.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39054" ObjectName="SW-CX_YSB.CX_YSB_0901XC1"/>
     <cge:Meas_Ref ObjectId="234172"/>
    <cge:TPSR_Ref TObjectID="39054"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-234170">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1111.892324 -163.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39049" ObjectName="SW-CX_YSB.CX_YSB_0331XC"/>
     <cge:Meas_Ref ObjectId="234170"/>
    <cge:TPSR_Ref TObjectID="39049"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-234170">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1111.892324 -116.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39050" ObjectName="SW-CX_YSB.CX_YSB_0331XC1"/>
     <cge:Meas_Ref ObjectId="234170"/>
    <cge:TPSR_Ref TObjectID="39050"/></metadata>
   </g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1436.000000 -1111.000000)" xlink:href="#load:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 973.000000 109.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1118.000000 111.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1271.000000 108.277778)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1627.118337 110.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1779.689765 114.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1916.286780 112.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2058.000000 113.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_2c2c1a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1276,-648 1276,-680 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="38999@0" ObjectIDZND0="38988@0" Pin0InfoVect0LinkObjId="g_2c199f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-234105_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1276,-648 1276,-680 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c15450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1494,-971 1508,-971 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="38996@1" ObjectIDZND0="g_2c15910@0" Pin0InfoVect0LinkObjId="g_2c15910_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-234088_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1494,-971 1508,-971 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c156b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1495,-1043 1508,-1043 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="38997@1" ObjectIDZND0="g_2c163a0@0" Pin0InfoVect0LinkObjId="g_2c163a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-234089_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1495,-1043 1508,-1043 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c199f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1441,-653 1441,-680 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="39047@0" ObjectIDZND0="38988@0" Pin0InfoVect0LinkObjId="g_2c2c1a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-234169_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1441,-653 1441,-680 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f25fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1276,-317 1276,-328 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="39001@1" ObjectIDZND0="39003@1" Pin0InfoVect0LinkObjId="SW-234109_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-234108_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1276,-317 1276,-328 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f261e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1276,-280 1276,-290 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="39002@1" ObjectIDZND0="39001@0" Pin0InfoVect0LinkObjId="SW-234108_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-234109_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1276,-280 1276,-290 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f26440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1276,-263 1276,-204 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="39002@0" ObjectIDZND0="38989@0" Pin0InfoVect0LinkObjId="g_2f307d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-234109_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1276,-263 1276,-204 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f2b710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="979,-146 979,-157 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="39010@1" ObjectIDZND0="39011@1" Pin0InfoVect0LinkObjId="SW-234127_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-234126_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="979,-146 979,-157 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f2b970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="979,-109 979,-119 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="39012@1" ObjectIDZND0="39010@0" Pin0InfoVect0LinkObjId="SW-234126_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-234127_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="979,-109 979,-119 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f2f840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1013,-36 1013,-44 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2f2edf0@0" ObjectIDZND0="39013@0" Pin0InfoVect0LinkObjId="SW-234128_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f2edf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1013,-36 1013,-44 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f307d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="979,-174 979,-204 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="39011@0" ObjectIDZND0="38989@0" Pin0InfoVect0LinkObjId="g_2f26440_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-234127_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="979,-174 979,-204 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f33870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="979,-92 979,-66 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="39012@0" ObjectIDZND0="g_2f2bbd0@1" Pin0InfoVect0LinkObjId="g_2f2bbd0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-234127_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="979,-92 979,-66 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f378c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1122,-37 1122,-18 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" BeginDevType2="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_2cf3570@0" ObjectIDND1="0@x" ObjectIDND2="g_2f36b90@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_2c186b0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2cf3570_0" Pin1InfoVect1LinkObjId="g_2c186b0_0" Pin1InfoVect2LinkObjId="g_2f36b90_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1122,-37 1122,-18 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f3ea90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1539,-152 1539,-163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="39042@1" ObjectIDZND0="39043@1" Pin0InfoVect0LinkObjId="SW-234165_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-234164_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1539,-152 1539,-163 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f3ecf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1539,-115 1539,-125 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="39044@1" ObjectIDZND0="39042@0" Pin0InfoVect0LinkObjId="SW-234164_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-234165_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1539,-115 1539,-125 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f3ef50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1539,-180 1539,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="39043@0" ObjectIDZND0="38990@0" Pin0InfoVect0LinkObjId="g_2c897e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-234165_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1539,-180 1539,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f3f1b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1384,-173 1384,-204 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="39045@0" ObjectIDZND0="38989@0" Pin0InfoVect0LinkObjId="g_2f26440_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-234166_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1384,-173 1384,-204 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f3f410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1384,-105 1384,-81 1539,-81 1539,-98 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="39046@0" ObjectIDZND0="39044@0" Pin0InfoVect0LinkObjId="SW-234165_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-234166_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1384,-105 1384,-81 1539,-81 1539,-98 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c73bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1384,-156 1384,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="39045@1" ObjectIDZND0="39046@1" Pin0InfoVect0LinkObjId="SW-234166_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-234166_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1384,-156 1384,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c77690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1633,-148 1633,-159 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="39018@1" ObjectIDZND0="39019@1" Pin0InfoVect0LinkObjId="SW-234137_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-234136_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1633,-148 1633,-159 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c778f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1633,-111 1633,-121 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="39020@1" ObjectIDZND0="39018@0" Pin0InfoVect0LinkObjId="SW-234136_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-234137_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1633,-111 1633,-121 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c7b7c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1670,-38 1670,-46 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2c7ad70@0" ObjectIDZND0="39021@0" Pin0InfoVect0LinkObjId="SW-234138_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c7ad70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1670,-38 1670,-46 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c7f590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1603,-83 1670,-83 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2c7ba20@0" ObjectIDZND0="39021@1" Pin0InfoVect0LinkObjId="SW-234138_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c7ba20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1603,-83 1670,-83 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c7f7f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1633,-94 1633,-68 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="39020@0" ObjectIDZND0="g_2c77b50@1" Pin0InfoVect0LinkObjId="g_2c77b50_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-234137_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1633,-94 1633,-68 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c88850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1963,-38 1963,-46 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2c87e00@0" ObjectIDZND0="39029@0" Pin0InfoVect0LinkObjId="SW-234148_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c87e00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1963,-38 1963,-46 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c897e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1922,-176 1922,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="39027@0" ObjectIDZND0="38990@0" Pin0InfoVect0LinkObjId="g_2f3ef50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-234147_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1922,-176 1922,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c8c880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1896,-83 1963,-83 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2c88ab0@0" ObjectIDZND0="39029@1" Pin0InfoVect0LinkObjId="SW-234148_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c88ab0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1896,-83 1963,-83 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c8cae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1922,-94 1922,-68 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="39028@0" ObjectIDZND0="g_2c84be0@1" Pin0InfoVect0LinkObjId="g_2c84be0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-234147_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1922,-94 1922,-68 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c91ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1922,-148 1922,-159 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="39026@1" ObjectIDZND0="39027@1" Pin0InfoVect0LinkObjId="SW-234147_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-234146_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1922,-148 1922,-159 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c92130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1922,-111 1922,-121 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="39028@1" ObjectIDZND0="39026@0" Pin0InfoVect0LinkObjId="SW-234146_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-234147_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1922,-111 1922,-121 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c96000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1823,-35 1823,-43 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2c955b0@0" ObjectIDZND0="39025@0" Pin0InfoVect0LinkObjId="SW-234143_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c955b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1823,-35 1823,-43 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c96f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1785,-173 1785,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="39023@0" ObjectIDZND0="38990@0" Pin0InfoVect0LinkObjId="g_2f3ef50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-234142_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1785,-173 1785,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c9a030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1756,-80 1823,-80 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2c96260@0" ObjectIDZND0="39025@1" Pin0InfoVect0LinkObjId="SW-234143_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c96260_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1756,-80 1823,-80 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c9a290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1785,-91 1785,-65 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="39024@0" ObjectIDZND0="g_2c92390@1" Pin0InfoVect0LinkObjId="g_2c92390_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-234142_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1785,-91 1785,-65 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c9f680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1785,-145 1785,-156 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="39022@1" ObjectIDZND0="39023@1" Pin0InfoVect0LinkObjId="SW-234142_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-234141_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1785,-145 1785,-156 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c9f8e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1785,-108 1785,-118 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="39024@1" ObjectIDZND0="39022@0" Pin0InfoVect0LinkObjId="SW-234141_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-234142_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1785,-108 1785,-118 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ca0870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2062,-177 2062,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="39051@0" ObjectIDZND0="38990@0" Pin0InfoVect0LinkObjId="g_2f3ef50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-234171_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2062,-177 2062,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ca69d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2061,-111 2061,-160 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="39052@1" ObjectIDZND0="39051@1" Pin0InfoVect0LinkObjId="SW-234171_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-234171_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2061,-111 2061,-160 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ca8d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2245,-147 2245,-158 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="39036@1" ObjectIDZND0="39037@1" Pin0InfoVect0LinkObjId="SW-234159_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-234158_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2245,-147 2245,-158 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ca8f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2245,-110 2245,-120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="39038@1" ObjectIDZND0="39036@0" Pin0InfoVect0LinkObjId="SW-234158_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-234159_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2245,-110 2245,-120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cace30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2293,-38 2293,-46 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2cac3e0@0" ObjectIDZND0="39040@0" Pin0InfoVect0LinkObjId="SW-234161_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2cac3e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2293,-38 2293,-46 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cad090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2245,-14 2245,13 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2ca91c0@0" ObjectIDZND0="39039@1" Pin0InfoVect0LinkObjId="SW-234160_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ca91c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2245,-14 2245,13 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cad2f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2245,-175 2245,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="39037@0" ObjectIDZND0="38990@0" Pin0InfoVect0LinkObjId="g_2f3ef50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-234159_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2245,-175 2245,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cb6320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2228,5 2228,14 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2cb4e00@0" ObjectIDZND0="39041@1" Pin0InfoVect0LinkObjId="SW-234162_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2cb4e00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2228,5 2228,14 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cb8d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2245,-93 2245,-67 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="39038@0" ObjectIDZND0="g_2ca91c0@1" Pin0InfoVect0LinkObjId="g_2ca91c0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-234159_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2245,-93 2245,-67 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cc14c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1633,-313 1633,-324 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="39007@1" ObjectIDZND0="39009@1" Pin0InfoVect0LinkObjId="SW-234120_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-234119_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1633,-313 1633,-324 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cc1720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1633,-276 1633,-286 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="39008@1" ObjectIDZND0="39007@0" Pin0InfoVect0LinkObjId="SW-234119_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-234120_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1633,-276 1633,-286 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cc1980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1633,-341 1633,-381 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="39009@0" ObjectIDZND0="39058@0" Pin0InfoVect0LinkObjId="g_2cf1630_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-234120_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1633,-341 1633,-381 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ccd2b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1276,-345 1276,-382 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="39003@0" ObjectIDZND0="39057@0" Pin0InfoVect0LinkObjId="g_2ce7ad0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-234109_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1276,-345 1276,-382 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ccee10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1441,-1044 1441,-1104 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="38995@x" ObjectIDND1="38997@x" ObjectIDZND0="0@x" ObjectIDZND1="g_2cce0e0@0" Pin0InfoVect0LinkObjId="g_2c186b0_0" Pin0InfoVect1LinkObjId="g_2cce0e0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-234087_0" Pin1InfoVect1LinkObjId="SW-234089_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1441,-1044 1441,-1104 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ccf070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1441,-732 1441,-680 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="38993@0" ObjectIDZND0="38988@0" Pin0InfoVect0LinkObjId="g_2c2c1a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-234086_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1441,-732 1441,-680 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ccf2d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1441,-857 1508,-857 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2ce5ea0@0" ObjectIDND1="g_2c186b0@0" ObjectIDND2="38994@x" ObjectIDZND0="g_2c17bf0@0" Pin0InfoVect0LinkObjId="g_2c17bf0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2ce5ea0_0" Pin1InfoVect1LinkObjId="g_2c186b0_0" Pin1InfoVect2LinkObjId="SW-234086_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1441,-857 1508,-857 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ccfdc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1441,-869 1441,-857 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="voltageTransformer" EndDevType2="switch" ObjectIDND0="g_2ce5ea0@0" ObjectIDZND0="g_2c17bf0@0" ObjectIDZND1="g_2c186b0@0" ObjectIDZND2="38994@x" Pin0InfoVect0LinkObjId="g_2c17bf0_0" Pin0InfoVect1LinkObjId="g_2c186b0_0" Pin0InfoVect2LinkObjId="SW-234086_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ce5ea0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1441,-869 1441,-857 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2cd0020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1447,-804 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1447,-804 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cd0280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1383,-840 1441,-840 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_2c186b0@0" ObjectIDZND0="g_2c17bf0@0" ObjectIDZND1="g_2ce5ea0@0" ObjectIDZND2="38994@x" Pin0InfoVect0LinkObjId="g_2c17bf0_0" Pin0InfoVect1LinkObjId="g_2ce5ea0_0" Pin0InfoVect2LinkObjId="SW-234086_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c186b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1383,-840 1441,-840 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cd0d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1441,-857 1441,-840 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="voltageTransformer" EndDevType1="switch" ObjectIDND0="g_2c17bf0@0" ObjectIDND1="g_2ce5ea0@0" ObjectIDZND0="g_2c186b0@0" ObjectIDZND1="38994@x" Pin0InfoVect0LinkObjId="g_2c186b0_0" Pin0InfoVect1LinkObjId="SW-234086_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2c17bf0_0" Pin1InfoVect1LinkObjId="g_2ce5ea0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1441,-857 1441,-840 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cd0fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1441,-840 1441,-818 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_2c186b0@0" ObjectIDND1="g_2c17bf0@0" ObjectIDND2="g_2ce5ea0@0" ObjectIDZND0="38994@0" Pin0InfoVect0LinkObjId="SW-234086_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2c186b0_0" Pin1InfoVect1LinkObjId="g_2c17bf0_0" Pin1InfoVect2LinkObjId="g_2ce5ea0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1441,-840 1441,-818 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cd1230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1441,-572 1392,-572 1392,-559 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2c19be0@0" ObjectIDND1="39048@x" ObjectIDZND0="g_2c1a2c0@0" Pin0InfoVect0LinkObjId="g_2c1a2c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2c19be0_0" Pin1InfoVect1LinkObjId="SW-234169_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1441,-572 1392,-572 1392,-559 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cd1d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1441,-588 1441,-572 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="39048@0" ObjectIDZND0="g_2c1a2c0@0" ObjectIDZND1="g_2c19be0@0" Pin0InfoVect0LinkObjId="g_2c1a2c0_0" Pin0InfoVect1LinkObjId="g_2c19be0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-234169_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1441,-588 1441,-572 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cd1f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1441,-572 1441,-553 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2c1a2c0@0" ObjectIDND1="39048@x" ObjectIDZND0="g_2c19be0@0" Pin0InfoVect0LinkObjId="g_2c19be0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2c1a2c0_0" Pin1InfoVect1LinkObjId="SW-234169_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1441,-572 1441,-553 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cd4e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1441,-522 1441,-508 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_2c19be0@1" ObjectIDZND0="g_2cd21e0@0" Pin0InfoVect0LinkObjId="g_2cd21e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c19be0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1441,-522 1441,-508 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cda250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1276,-617 1276,-631 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="38998@1" ObjectIDZND0="38999@1" Pin0InfoVect0LinkObjId="SW-234105_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-234104_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1276,-617 1276,-631 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cda4b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1276,-580 1276,-590 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="39000@1" ObjectIDZND0="38998@0" Pin0InfoVect0LinkObjId="SW-234104_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-234105_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1276,-580 1276,-590 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ce2720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1441,-787 1441,-798 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="38992@1" ObjectIDZND0="38994@1" Pin0InfoVect0LinkObjId="SW-234086_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-234085_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1441,-787 1441,-798 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ce2980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1441,-750 1441,-760 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="38993@1" ObjectIDZND0="38992@0" Pin0InfoVect0LinkObjId="SW-234085_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-234086_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1441,-750 1441,-760 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2ce59e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1441,-811 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1441,-811 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2ce5c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1379,-530 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1379,-530 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ce68c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1441,-971 1441,-922 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="38995@x" ObjectIDND1="38996@x" ObjectIDZND0="g_2ce5ea0@1" Pin0InfoVect0LinkObjId="g_2ce5ea0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-234087_0" Pin1InfoVect1LinkObjId="SW-234088_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1441,-971 1441,-922 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ce73b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1441,-985 1441,-971 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="38995@0" ObjectIDZND0="g_2ce5ea0@0" ObjectIDZND1="38996@x" Pin0InfoVect0LinkObjId="g_2ce5ea0_0" Pin0InfoVect1LinkObjId="SW-234088_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-234087_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1441,-985 1441,-971 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ce7610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1441,-971 1458,-971 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_2ce5ea0@0" ObjectIDND1="38995@x" ObjectIDZND0="38996@0" Pin0InfoVect0LinkObjId="SW-234088_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2ce5ea0_0" Pin1InfoVect1LinkObjId="SW-234087_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1441,-971 1458,-971 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ce7870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1276,-563 1276,-532 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="39000@0" ObjectIDZND0="g_2d361c0@1" Pin0InfoVect0LinkObjId="g_2d361c0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-234105_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1276,-563 1276,-532 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ce7ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1276,-479 1276,-462 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_2d361c0@0" ObjectIDZND0="39057@1" Pin0InfoVect0LinkObjId="g_2ccd2b0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d361c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1276,-479 1276,-462 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ce85c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1441,-1104 1441,-1116 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_2cce0e0@0" ObjectIDND1="38995@x" ObjectIDND2="38997@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_2c186b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2cce0e0_0" Pin1InfoVect1LinkObjId="SW-234087_0" Pin1InfoVect2LinkObjId="SW-234089_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1441,-1104 1441,-1116 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ce8820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1485,-1090 1441,-1090 1441,-1104 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2cce0e0@0" ObjectIDZND0="0@x" ObjectIDZND1="38995@x" ObjectIDZND2="38997@x" Pin0InfoVect0LinkObjId="g_2c186b0_0" Pin0InfoVect1LinkObjId="SW-234087_0" Pin0InfoVect2LinkObjId="SW-234089_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2cce0e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1485,-1090 1441,-1090 1441,-1104 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cedc10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1633,-617 1633,-631 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="39004@1" ObjectIDZND0="39005@1" Pin0InfoVect0LinkObjId="SW-234116_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-234115_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1633,-617 1633,-631 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cede70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1633,-580 1633,-590 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="39006@1" ObjectIDZND0="39004@0" Pin0InfoVect0LinkObjId="SW-234115_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-234116_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1633,-580 1633,-590 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2cf0f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1669,-529 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1669,-529 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cf1170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1633,-680 1633,-648 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="38988@0" ObjectIDZND0="39005@0" Pin0InfoVect0LinkObjId="SW-234116_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c2c1a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1633,-680 1633,-648 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2cf13d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1592,-492 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1592,-492 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cf1630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1633,-481 1633,-461 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_2d384f0@0" ObjectIDZND0="39058@1" Pin0InfoVect0LinkObjId="g_2cc1980_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d384f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1633,-481 1633,-461 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cf1890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1633,-563 1633,-534 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="39006@0" ObjectIDZND0="g_2d384f0@1" Pin0InfoVect0LinkObjId="g_2d384f0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-234116_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1633,-563 1633,-534 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cf2700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="978,88 978,-13 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_2f2bbd0@0" Pin0InfoVect0LinkObjId="g_2f2bbd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c186b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="978,88 978,-13 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cf3df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1122,-123 1122,-88 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="39050@0" ObjectIDZND0="g_2cf3570@0" Pin0InfoVect0LinkObjId="g_2cf3570_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-234170_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1122,-123 1122,-88 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cf4050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1122,-57 1122,-37 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" EndDevType1="transformer2" EndDevType2="lightningRod" ObjectIDND0="g_2cf3570@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="g_2f36b90@0" Pin0InfoVect0LinkObjId="g_2c186b0_0" Pin0InfoVect1LinkObjId="g_2c186b0_0" Pin0InfoVect2LinkObjId="g_2f36b90_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2cf3570_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1122,-57 1122,-37 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cf42b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1122,-37 1122,-15 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="transformer2" ObjectIDND0="0@x" ObjectIDND1="g_2cf3570@0" ObjectIDND2="g_2f36b90@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="g_2c186b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2c186b0_0" Pin1InfoVect1LinkObjId="g_2cf3570_0" Pin1InfoVect2LinkObjId="g_2f36b90_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1122,-37 1122,-15 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2cf4510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1123,67 1123,90 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="load" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_2c186b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1123,67 1123,90 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cf4770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1122,-37 1076,-37 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="lightningRod" BeginDevType2="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_2cf3570@0" ObjectIDND2="0@x" ObjectIDZND0="g_2f36b90@0" Pin0InfoVect0LinkObjId="g_2f36b90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2c186b0_0" Pin1InfoVect1LinkObjId="g_2cf3570_0" Pin1InfoVect2LinkObjId="g_2c186b0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1122,-37 1076,-37 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cf49d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1122,-186 1122,-204 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="39049@0" ObjectIDZND0="38989@0" Pin0InfoVect0LinkObjId="g_2f26440_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-234170_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1122,-186 1122,-204 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cf4c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="946,-81 1013,-81 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2f2faa0@0" ObjectIDZND0="39013@1" Pin0InfoVect0LinkObjId="SW-234128_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f2faa0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="946,-81 1013,-81 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cf6f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1276,-144 1276,-155 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="39014@1" ObjectIDZND0="39015@1" Pin0InfoVect0LinkObjId="SW-234132_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-234131_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1276,-144 1276,-155 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cf71c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1276,-107 1276,-117 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="39016@1" ObjectIDZND0="39014@0" Pin0InfoVect0LinkObjId="SW-234131_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-234132_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1276,-107 1276,-117 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cfa670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1310,-34 1310,-42 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2cf9c20@0" ObjectIDZND0="39017@0" Pin0InfoVect0LinkObjId="SW-234133_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2cf9c20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1310,-34 1310,-42 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cfb600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1276,-172 1276,-204 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="39015@0" ObjectIDZND0="38989@0" Pin0InfoVect0LinkObjId="g_2f26440_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-234132_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1276,-172 1276,-204 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cfe6a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1276,-90 1276,-64 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="39016@0" ObjectIDZND0="g_2d036a0@1" Pin0InfoVect0LinkObjId="g_2d036a0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-234132_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1276,-90 1276,-64 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d025d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1243,-79 1310,-79 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2cfa8d0@0" ObjectIDZND0="39017@1" Pin0InfoVect0LinkObjId="SW-234133_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2cfa8d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1243,-79 1310,-79 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d03440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1632,-15 1632,88 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_2c77b50@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_2c186b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c77b50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1632,-15 1632,88 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d04120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1276,-30 1276,86 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_2d036a0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_2c186b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d036a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1276,-30 1276,86 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d04f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1785,-12 1785,93 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_2c92390@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_2c186b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c92390_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1785,-12 1785,93 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d05e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1921,-15 1921,87 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_2c84be0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_2c186b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c84be0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1921,-15 1921,87 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2d08900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2063,109 2063,61 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="2063,109 2063,61 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d08b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2026,-61 2062,-61 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="transformer2" ObjectIDND0="g_2c9fb40@0" ObjectIDZND0="39052@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-234171_0" Pin0InfoVect1LinkObjId="g_2c186b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c9fb40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2026,-61 2062,-61 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d09690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2062,-95 2062,-61 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="transformer2" ObjectIDND0="39052@0" ObjectIDZND0="g_2c9fb40@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_2c9fb40_0" Pin0InfoVect1LinkObjId="g_2c186b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-234171_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2062,-95 2062,-61 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d098f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2062,-61 2062,-32 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="transformer2" ObjectIDND0="g_2c9fb40@0" ObjectIDND1="39052@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_2c186b0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2c9fb40_0" Pin1InfoVect1LinkObjId="SW-234171_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2062,-61 2062,-32 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d0da50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2245,49 2245,69 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" ObjectIDND0="39039@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_2c186b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-234160_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2245,49 2245,69 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d0dcb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2283,58 2229,58 2228,50 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2d0bf20@0" ObjectIDZND0="39041@0" Pin0InfoVect0LinkObjId="SW-234162_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d0bf20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2283,58 2229,58 2228,50 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2d0df10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2204,50 2204,65 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_2d0cc90@0" Pin0InfoVect0LinkObjId="g_2d0cc90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2204,50 2204,65 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2d0e170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2258,177 2258,183 2231,183 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_2d0d370@0" Pin0InfoVect0LinkObjId="g_2d0d370_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2258,177 2258,183 2231,183 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2d0e3d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2220,183 2204,183 2204,76 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2d0d370@1" ObjectIDZND0="g_2d0cc90@1" Pin0InfoVect0LinkObjId="g_2d0cc90_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d0d370_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2220,183 2204,183 2204,76 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d0f3e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2229,-80 2293,-80 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2d0e630@0" ObjectIDZND0="39040@1" Pin0InfoVect0LinkObjId="SW-234161_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d0e630_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2229,-80 2293,-80 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d11710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="826,-147 826,-158 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="39030@1" ObjectIDZND0="39031@1" Pin0InfoVect0LinkObjId="SW-234152_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-234151_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="826,-147 826,-158 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d11970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="826,-110 826,-120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="39032@1" ObjectIDZND0="39030@0" Pin0InfoVect0LinkObjId="SW-234151_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-234152_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="826,-110 826,-120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d14e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="861,-38 861,-46 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2d143d0@0" ObjectIDZND0="39034@0" Pin0InfoVect0LinkObjId="SW-234154_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d143d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="861,-38 861,-46 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d15080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="826,-32 826,-5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2d286f0@0" ObjectIDZND0="39033@1" Pin0InfoVect0LinkObjId="SW-234153_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d286f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="826,-32 826,-5 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d152e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="826,-175 826,-204 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="39031@0" ObjectIDZND0="38989@0" Pin0InfoVect0LinkObjId="g_2f26440_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-234152_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="826,-175 826,-204 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d1e310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="796,-13 796,-4 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2d1cdf0@0" ObjectIDZND0="39035@1" Pin0InfoVect0LinkObjId="SW-234155_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d1cdf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="796,-13 796,-4 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d26fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="826,31 826,51 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" ObjectIDND0="39033@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_2c186b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-234153_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="826,31 826,51 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d27220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="851,40 797,40 796,32 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2d293d0@0" ObjectIDZND0="39035@0" Pin0InfoVect0LinkObjId="SW-234155_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d293d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="851,40 797,40 796,32 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2d27480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="826,159 826,165 799,165 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_2d268e0@0" Pin0InfoVect0LinkObjId="g_2d268e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="826,159 826,165 799,165 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d28490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="797,-80 861,-80 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2d276e0@0" ObjectIDZND0="39034@1" Pin0InfoVect0LinkObjId="SW-234154_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d276e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="797,-80 861,-80 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d29170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="826,-93 826,-66 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="39032@0" ObjectIDZND0="g_2d286f0@1" Pin0InfoVect0LinkObjId="g_2d286f0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-234152_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="826,-93 826,-66 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d2e470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="905,-434 905,-393 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_2d2b8c0@0" ObjectIDZND0="g_2d2a140@0" Pin0InfoVect0LinkObjId="g_2d2a140_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d2b8c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="905,-434 905,-393 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d2e6d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="905,-245 905,-204 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="39053@0" ObjectIDZND0="38989@0" Pin0InfoVect0LinkObjId="g_2f26440_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-234172_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="905,-245 905,-204 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d2e930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="905,-332 968,-332 968,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2d2a140@0" ObjectIDND1="39054@x" ObjectIDZND0="g_2d2ab10@0" Pin0InfoVect0LinkObjId="g_2d2ab10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2d2a140_0" Pin1InfoVect1LinkObjId="SW-234172_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="905,-332 968,-332 968,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d2eb90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="905,-361 905,-332 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2d2a140@1" ObjectIDZND0="g_2d2ab10@0" ObjectIDZND1="39054@x" Pin0InfoVect0LinkObjId="g_2d2ab10_0" Pin0InfoVect1LinkObjId="SW-234172_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d2a140_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="905,-361 905,-332 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d2edf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="905,-332 905,-308 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_2d2a140@0" ObjectIDND1="g_2d2ab10@0" ObjectIDZND0="39054@0" Pin0InfoVect0LinkObjId="SW-234172_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2d2a140_0" Pin1InfoVect1LinkObjId="g_2d2ab10_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="905,-332 905,-308 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d335d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1992,-427 1992,-386 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_2d30a90@0" ObjectIDZND0="g_2d2f680@0" Pin0InfoVect0LinkObjId="g_2d2f680_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d30a90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1992,-427 1992,-386 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d33830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1992,-327 2055,-327 2055,-341 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2d2f680@0" ObjectIDND1="39056@x" ObjectIDZND0="g_2d2fda0@0" Pin0InfoVect0LinkObjId="g_2d2fda0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2d2f680_0" Pin1InfoVect1LinkObjId="SW-234173_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1992,-327 2055,-327 2055,-341 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d34360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1992,-303 1992,-327 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="39056@0" ObjectIDZND0="g_2d2fda0@0" ObjectIDZND1="g_2d2f680@0" Pin0InfoVect0LinkObjId="g_2d2fda0_0" Pin0InfoVect1LinkObjId="g_2d2f680_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-234173_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1992,-303 1992,-327 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d345c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1992,-327 1992,-354 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2d2fda0@0" ObjectIDND1="39056@x" ObjectIDZND0="g_2d2f680@1" Pin0InfoVect0LinkObjId="g_2d2f680_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2d2fda0_0" Pin1InfoVect1LinkObjId="SW-234173_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1992,-327 1992,-354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d34820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1992,-205 1992,-240 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="38990@0" ObjectIDZND0="39055@0" Pin0InfoVect0LinkObjId="SW-234173_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f3ef50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1992,-205 1992,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d38f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1633,-259 1633,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="39008@0" ObjectIDZND0="38990@0" Pin0InfoVect0LinkObjId="g_2f3ef50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-234120_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1633,-259 1633,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d39740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1633,-176 1633,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="39019@0" ObjectIDZND0="38990@0" Pin0InfoVect0LinkObjId="g_2f3ef50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-234137_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1633,-176 1633,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d3e260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1441,-1028 1441,-1043 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="38995@1" ObjectIDZND0="0@x" ObjectIDZND1="g_2cce0e0@0" ObjectIDZND2="38997@x" Pin0InfoVect0LinkObjId="g_2c186b0_0" Pin0InfoVect1LinkObjId="g_2cce0e0_0" Pin0InfoVect2LinkObjId="SW-234089_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-234087_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1441,-1028 1441,-1043 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d3e450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1441,-1043 1459,-1043 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_2cce0e0@0" ObjectIDND2="38995@x" ObjectIDZND0="38997@0" Pin0InfoVect0LinkObjId="SW-234089_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2c186b0_0" Pin1InfoVect1LinkObjId="g_2cce0e0_0" Pin1InfoVect2LinkObjId="SW-234087_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1441,-1043 1459,-1043 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2d400f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="772,32 772,50 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_2d26200@0" Pin0InfoVect0LinkObjId="g_2d26200_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="772,32 772,50 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2d402e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="772,61 772,165 788,165 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2d26200@1" ObjectIDZND0="g_2d268e0@1" Pin0InfoVect0LinkObjId="g_2d268e0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d26200_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="772,61 772,165 788,165 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d4f230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1992,-256 1992,-286 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="39055@1" ObjectIDZND0="39056@1" Pin0InfoVect0LinkObjId="SW-234173_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-234173_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1992,-256 1992,-286 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d55390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1441,-606 1441,-636 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="39048@1" ObjectIDZND0="39047@1" Pin0InfoVect0LinkObjId="SW-234169_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-234169_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1441,-606 1441,-636 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d58fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="905,-262 905,-292 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="39053@1" ObjectIDZND0="39054@1" Pin0InfoVect0LinkObjId="SW-234172_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-234172_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="905,-262 905,-292 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d5cb60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1122,-140 1122,-170 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="39050@1" ObjectIDZND0="39049@1" Pin0InfoVect0LinkObjId="SW-234170_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-234170_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1122,-140 1122,-170 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-233906" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 288.500000 -911.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38974" ObjectName="DYN-CX_YSB"/>
     <cge:Meas_Ref ObjectId="233906"/>
    </metadata>
   </g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c2c480" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 683.000000 294.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c2a090" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 667.000000 279.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c2a2d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 677.000000 340.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c2a510" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 677.000000 325.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c2a750" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 677.000000 310.000000) translate(0,12)">Uc(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bb6120" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 874.000000 -163.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bb6380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 888.000000 -193.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bb65c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 864.000000 -178.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bb68f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1102.000000 600.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bb6b50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1116.000000 570.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bb6d90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1092.000000 585.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bb70c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1698.000000 318.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bb7320" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1712.000000 288.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bb7560" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1688.000000 303.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bb7890" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1691.000000 621.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bb7af0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1705.000000 591.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bb7d30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1681.000000 606.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bb8060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1501.000000 782.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bb82c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1515.000000 752.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bb8500" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1491.000000 767.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bb8830" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2286.000000 283.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bb8aa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2270.000000 268.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bb8ce0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2280.000000 329.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bb8f20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2280.000000 314.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bb9160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2280.000000 299.000000) translate(0,12)">Uc(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bb9490" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1162.000000 750.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bb9700" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1146.000000 735.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bb9940" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1156.000000 796.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bb9b80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1156.000000 781.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bb9dc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1156.000000 766.000000) translate(0,12)">Uc(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bba0f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1723.000000 473.000000) translate(0,12)">油温（℃）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bba350" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1723.000000 488.000000) translate(0,12)">档位（档）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c73f00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1416.000000 71.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c74550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1430.000000 41.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c74790" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1406.000000 56.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cc9f20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1115.000000 327.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cca180" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1129.000000 297.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cca3c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1105.000000 312.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d45fe0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2311.000000 157.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d46210" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2336.000000 142.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d46450" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2342.000000 127.000000) translate(0,12)">Cos:</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d46780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 646.000000 158.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d469e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 671.000000 143.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d46c20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 677.000000 128.000000) translate(0,12)">Cos:</text>
   <metadata/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2c15910" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1503.000000 -965.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c163a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1503.000000 -1037.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f2edf0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1007.000000 -18.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c7ad70" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1664.000000 -20.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c87e00" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1957.000000 -20.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c955b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1817.000000 -17.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2cac3e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2287.000000 -20.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2cb4e00" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2222.000000 10.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2cb5890" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2198.000000 19.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2cf9c20" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1304.000000 -16.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d143d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 855.000000 -20.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d1cdf0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 790.000000 -8.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d1d880" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 766.000000 1.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c2c8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c2c8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c2c8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c2c8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c2c8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c2c8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c2c8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c2c8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c2c8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c2c8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c2c8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c2c8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c2c8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c2c8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c2c8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c2c8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c2c8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c2c8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c30660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c30660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c30660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c30660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c30660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c30660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c30660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_2c28d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 152.000000 -1001.500000) translate(0,16)">迤石坝变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c30200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1079.000000 -466.000000) translate(0,15)">1号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c30200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1079.000000 -466.000000) translate(0,33)">SZ11-6300/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c30200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1079.000000 -466.000000) translate(0,51)">35±3x2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c30200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1079.000000 -466.000000) translate(0,69)">Yd11 Uk%=7%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c2bf90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 36.000000 -611.000000) translate(0,15)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c16e30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1303.000000 -1164.000000) translate(0,15)">35kV飒马场线及迤石坝T接线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c19460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1717.000000 -450.000000) translate(0,15)">2号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c19460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1717.000000 -450.000000) translate(0,33)">SZ11-6300/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c19460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1717.000000 -450.000000) translate(0,51)">35±3x2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c19460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1717.000000 -450.000000) translate(0,69)">Yd11 Uk%=7%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c1afb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1398.500000 -460.000000) translate(0,15)">35kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cbc0a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1956.000000 -483.000000) translate(0,12)">10kVⅡ母TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2cc49e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1121.000000 -705.000000) translate(0,15)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2cc5010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 680.000000 -237.000000) translate(0,15)">10kVI母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cc6c50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2205.000000 201.000000) translate(0,12)">2号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cc7280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2038.000000 123.000000) translate(0,12)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cc74c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1909.000000 128.000000) translate(0,12)">备用一</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cc7700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1767.000000 126.000000) translate(0,12)">飒河线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cc7ca0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1588.000000 124.000000) translate(0,12)">伍庄村1号支洞线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cc85a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1089.000000 127.000000) translate(0,12)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cc87a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1256.000000 127.000000) translate(0,12)">凤河线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cc89e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 929.000000 124.000000) translate(0,12)">迤石坝料厂线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cc9be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 768.000000 184.000000) translate(0,12)">1号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2d2f050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 860.000000 -496.000000) translate(0,15)">10kVⅠ母TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d3cdf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1450.000000 -781.000000) translate(0,12)">327</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d3d420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1455.000000 -997.000000) translate(0,12)">32760</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d3d660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1448.000000 -1013.000000) translate(0,12)">3276</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d3d8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1456.000000 -1069.000000) translate(0,12)">32767</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d3e640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1285.000000 -612.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d3ea70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1285.000000 -311.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d3ecb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1642.000000 -611.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d3eef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1642.000000 -307.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d3f130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1304.000000 -429.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d3f370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1661.000000 -428.000000) translate(0,12)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d3f5b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2269.000000 -228.000000) translate(0,12)">10kVⅡ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d3f7f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 835.000000 -141.000000) translate(0,12)">031</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d3fa30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 833.000000 5.000000) translate(0,12)">0316</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d3fc70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 868.000000 -69.000000) translate(0,12)">03160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d3feb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 711.000000 9.000000) translate(0,12)">03167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d404d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 988.000000 -140.000000) translate(0,12)">032</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d407f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1020.000000 -70.000000) translate(0,12)">03260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d40a30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1285.000000 -138.000000) translate(0,12)">034</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d41160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1317.000000 -68.000000) translate(0,12)">03460</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d41480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1395.000000 -146.000000) translate(0,12)">0121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d416c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1549.000000 -146.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d41900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1642.000000 -142.000000) translate(0,12)">041</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d41b40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1677.000000 -72.000000) translate(0,12)">04160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d41d80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1794.000000 -139.000000) translate(0,12)">042</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d41fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1830.000000 -69.000000) translate(0,12)">04260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d42200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1932.000000 -142.000000) translate(0,12)">043</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d42440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1970.000000 -72.000000) translate(0,12)">04360</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d42680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2072.000000 -148.000000) translate(0,12)">0441</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d428c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2255.000000 -141.000000) translate(0,12)">045</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d42b00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2300.000000 -69.000000) translate(0,12)">04560</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d42d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2252.000000 23.000000) translate(0,12)">0456</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d42f80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2151.000000 23.000000) translate(0,12)">04567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d55bc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2000.000000 -278.000000) translate(0,12)">0902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d5c530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1448.000000 -630.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d628d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 914.000000 -284.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d633f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1133.000000 -163.000000) translate(0,12)">0331</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d65310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1082.000000 -491.000000) translate(0,12)">油温（℃）：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d65550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1082.000000 -506.000000) translate(0,12)">档位（档）：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_316d630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 131.000000 -67.000000) translate(0,15)">13698775535</text>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_YSB.CX_YSB_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1105,-680 1722,-680 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="38988" ObjectName="BS-CX_YSB.CX_YSB_3IM"/>
    <cge:TPSR_Ref TObjectID="38988"/></metadata>
   <polyline fill="none" opacity="0" points="1105,-680 1722,-680 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_YSB.CX_YSB_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="648,-204 1415,-204 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="38989" ObjectName="BS-CX_YSB.CX_YSB_9IM"/>
    <cge:TPSR_Ref TObjectID="38989"/></metadata>
   <polyline fill="none" opacity="0" points="648,-204 1415,-204 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_YSB.CX_YSB_9IIM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1510,-205 2361,-205 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="38990" ObjectName="BS-CX_YSB.CX_YSB_9IIM"/>
    <cge:TPSR_Ref TObjectID="38990"/></metadata>
   <polyline fill="none" opacity="0" points="1510,-205 2361,-205 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="38990" cx="1922" cy="-205" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38990" cx="1785" cy="-205" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38990" cx="2062" cy="-205" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38990" cx="2245" cy="-205" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38990" cx="1539" cy="-205" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38990" cx="1633" cy="-205" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38990" cx="1633" cy="-205" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38989" cx="1276" cy="-204" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38989" cx="979" cy="-204" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38989" cx="1384" cy="-204" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38989" cx="826" cy="-204" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38989" cx="905" cy="-204" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38988" cx="1276" cy="-680" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38988" cx="1441" cy="-680" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38988" cx="1633" cy="-680" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38989" cx="1276" cy="-204" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38990" cx="1992" cy="-205" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38988" cx="1441" cy="-680" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38989" cx="1122" cy="-204" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_YSB"/>
</svg>