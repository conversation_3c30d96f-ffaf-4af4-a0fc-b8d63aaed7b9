<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-64" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="3115 -1198 1743 1196">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="29" x2="29" y1="7" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="4" x2="22" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="22" x2="22" y1="0" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="25" x2="25" y1="6" y2="13"/>
   </symbol>
   <symbol id="earth:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="14" y2="14"/>
   </symbol>
   <symbol id="lightningRod:shape131">
    <circle cx="8" cy="17" fillStyle="0" r="7.5" stroke-width="1"/>
    <circle cx="8" cy="8" fillStyle="0" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="41" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="11" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="11" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape55">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="50" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="9" y1="41" y2="41"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="27,39 5,17 5,5 " stroke-width="1"/>
    <rect height="4" stroke-width="1" width="19" x="7" y="26"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="9" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="1" y2="1"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
   </symbol>
   <symbol id="lightningRod:shape204">
    <rect height="31" stroke-width="0.5" width="16" x="12" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="36" y1="30" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="2" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="2" y1="30" y2="10"/>
   </symbol>
   <symbol id="lightningRod:shape123">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="4" y2="4"/>
    <ellipse cx="14" cy="18" fillStyle="0" rx="9" ry="7.5" stroke-width="0.155709"/>
    <ellipse cx="22" cy="8" fillStyle="0" rx="8.5" ry="7.5" stroke-width="0.155709"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="14" x2="14" y1="17" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="16" x2="14" y1="22" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="14" x2="11" y1="20" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="8" x2="8" y1="6" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="10" x2="8" y1="11" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="8" x2="5" y1="9" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="24" x2="24" y1="12" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="20" x2="24" y1="8" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="19" x2="24" y1="10" y2="12"/>
    <ellipse cx="8" cy="10" fillStyle="0" rx="8.5" ry="7.5" stroke-width="0.155709"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="7" x2="15" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="15" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="7" x2="15" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="15" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="transformer2:shape11_0">
    <ellipse cx="13" cy="34" fillStyle="0" rx="13" ry="12.5" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="40" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="36" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="32" y2="36"/>
   </symbol>
   <symbol id="transformer2:shape11_1">
    <circle cx="13" cy="16" fillStyle="0" r="13" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="20" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="16" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="12" y2="16"/>
   </symbol>
   <symbol id="transformer2:shape39_0">
    <ellipse cx="37" cy="29" fillStyle="0" rx="26.5" ry="25.5" stroke-width="0.62032"/>
    <polyline points="64,96 64,89 " stroke-width="1.07692"/>
    <polyline points="58,96 64,96 " stroke-width="1.07692"/>
    <polyline DF8003:Layer="PUBLIC" points="38,34 31,19 46,19 38,34 38,34 38,34 "/>
   </symbol>
   <symbol id="transformer2:shape39_1">
    <circle cx="37" cy="66" fillStyle="0" r="26.5" stroke-width="0.63865"/>
    <polyline points="64,96 1,33 " stroke-width="1.07692"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="38" y1="70" y2="79"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="38" y1="70" y2="79"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="38" y1="64" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="38" y1="64" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="28" y1="71" y2="64"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="28" y1="71" y2="64"/>
   </symbol>
   <symbol id="transformer2:shape48_0">
    <ellipse cx="25" cy="29" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="17" x2="25" y1="32" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="33" y1="24" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="25" y1="16" y2="24"/>
   </symbol>
   <symbol id="transformer2:shape48_1">
    <circle cx="25" cy="61" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="16" x2="33" y1="59" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="33" y1="75" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="16" y1="75" y2="59"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2a147c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22a88a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_22a44b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2a16c30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_28c7bd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_28c87a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_28c9230" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_28c9c90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_22eabc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_22eabc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_28cd2b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_28cd2b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_28cefa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_28cefa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_28cfff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_28d1950" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_28d24f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_28d3360" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_28d3ab0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_28d5330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_28d5b20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_28d6180" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_28d6ba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_28d7d80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_28d8700" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_28d91f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_28d9bb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_28db1a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_28dbc20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_28dce20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_28dda90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_28e3fd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_28e4dc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_28df430" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_28e0960" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1206" width="1753" x="3110" y="-1203"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,0)" stroke-width="1" x1="4856" x2="4856" y1="-477" y2="-464"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-96930">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4011.996999 -556.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20191" ObjectName="SW-CX_SHJ.CX_SHJ_001BK"/>
     <cge:Meas_Ref ObjectId="96930"/>
    <cge:TPSR_Ref TObjectID="20191"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-96928">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4013.130332 -721.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20189" ObjectName="SW-CX_SHJ.CX_SHJ_301BK"/>
     <cge:Meas_Ref ObjectId="96928"/>
    <cge:TPSR_Ref TObjectID="20189"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-96705">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4195.789644 -925.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20167" ObjectName="SW-CX_SHJ.CX_SHJ_381BK"/>
     <cge:Meas_Ref ObjectId="96705"/>
    <cge:TPSR_Ref TObjectID="20167"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-102126">
    <use class="BV-4KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4617.000000 -607.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20474" ObjectName="SW-CX_SHJ.CX_SHJ_412BK"/>
     <cge:Meas_Ref ObjectId="102126"/>
    <cge:TPSR_Ref TObjectID="20474"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-96769">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4486.333333 -357.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20186" ObjectName="SW-CX_SHJ.CX_SHJ_086BK"/>
     <cge:Meas_Ref ObjectId="96769"/>
    <cge:TPSR_Ref TObjectID="20186"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-96759">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4332.733333 -357.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20183" ObjectName="SW-CX_SHJ.CX_SHJ_085BK"/>
     <cge:Meas_Ref ObjectId="96759"/>
    <cge:TPSR_Ref TObjectID="20183"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-96749">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4181.133333 -357.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20180" ObjectName="SW-CX_SHJ.CX_SHJ_084BK"/>
     <cge:Meas_Ref ObjectId="96749"/>
    <cge:TPSR_Ref TObjectID="20180"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-96739">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4027.533333 -357.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20177" ObjectName="SW-CX_SHJ.CX_SHJ_083BK"/>
     <cge:Meas_Ref ObjectId="96739"/>
    <cge:TPSR_Ref TObjectID="20177"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-96729">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3875.933333 -357.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20174" ObjectName="SW-CX_SHJ.CX_SHJ_082BK"/>
     <cge:Meas_Ref ObjectId="96729"/>
    <cge:TPSR_Ref TObjectID="20174"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-96719">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3723.333333 -357.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20171" ObjectName="SW-CX_SHJ.CX_SHJ_081BK"/>
     <cge:Meas_Ref ObjectId="96719"/>
    <cge:TPSR_Ref TObjectID="20171"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-102124">
    <use class="BV-4KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4618.000000 -720.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20473" ObjectName="SW-CX_SHJ.CX_SHJ_411BK"/>
     <cge:Meas_Ref ObjectId="102124"/>
    <cge:TPSR_Ref TObjectID="20473"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_SHJ.CX_SHJ_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3662,-847 4774,-847 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="20121" ObjectName="BS-CX_SHJ.CX_SHJ_3IM"/>
    <cge:TPSR_Ref TObjectID="20121"/></metadata>
   <polyline fill="none" opacity="0" points="3662,-847 4774,-847 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_SHJ.CX_SHJ_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3649,-473 4774,-473 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="20122" ObjectName="BS-CX_SHJ.CX_SHJ_9IM"/>
    <cge:TPSR_Ref TObjectID="20122"/></metadata>
   <polyline fill="none" opacity="0" points="3649,-473 4774,-473 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4559,-664 4707,-664 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4559,-664 4707,-664 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-CX_SHJ.082Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3876.000000 -158.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34494" ObjectName="EC-CX_SHJ.082Ld"/>
    <cge:TPSR_Ref TObjectID="34494"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_SHJ.083Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4028.000000 -158.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34495" ObjectName="EC-CX_SHJ.083Ld"/>
    <cge:TPSR_Ref TObjectID="34495"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_SHJ.084Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4181.000000 -158.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34496" ObjectName="EC-CX_SHJ.084Ld"/>
    <cge:TPSR_Ref TObjectID="34496"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_SHJ.085Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4333.000000 -158.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34497" ObjectName="EC-CX_SHJ.085Ld"/>
    <cge:TPSR_Ref TObjectID="34497"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_SHJ.086Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4486.000000 -158.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34498" ObjectName="EC-CX_SHJ.086Ld"/>
    <cge:TPSR_Ref TObjectID="34498"/></metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2dfb990" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4135.834689 -1032.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3c1cb90" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4380.559839 -753.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_324bae0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4327.000000 -624.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f52c10" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4704.000000 -228.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-10KV" id="g_31ef450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4021,-591 4021,-617 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="20191@1" ObjectIDZND0="20198@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-96930_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4021,-591 4021,-617 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22d6030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4021,-473 4021,-505 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="20122@0" ObjectIDZND0="20192@0" Pin0InfoVect0LinkObjId="SW-96931_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22f5dd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4021,-473 4021,-505 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22d6290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4021,-541 4021,-564 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="20192@1" ObjectIDZND0="20191@0" Pin0InfoVect0LinkObjId="SW-96930_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-96931_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4021,-541 4021,-564 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_229add0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4022,-697 4022,-729 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="20198@1" ObjectIDZND0="20189@0" Pin0InfoVect0LinkObjId="SW-96928_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_31ef450_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4022,-697 4022,-729 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_229afc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4022,-756 4022,-773 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="20189@1" ObjectIDZND0="20190@0" Pin0InfoVect0LinkObjId="SW-96929_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-96928_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4022,-756 4022,-773 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_229b1b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4022,-809 4022,-847 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="20190@1" ObjectIDZND0="20121@0" Pin0InfoVect0LinkObjId="g_3c1c6d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-96929_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4022,-809 4022,-847 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c25e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4206,-1071 4259,-1071 4259,-1053 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="powerLine" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_31d7bc0@0" ObjectIDND1="38081@1" ObjectIDND2="20170@x" ObjectIDZND0="g_3164870@0" Pin0InfoVect0LinkObjId="g_3164870_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_31d7bc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="SW-96708_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4206,-1071 4259,-1071 4259,-1053 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2dfadb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4205,-1100 4205,-1087 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="38081@1" ObjectIDZND0="20170@x" ObjectIDZND1="20169@x" ObjectIDZND2="g_3164870@0" Pin0InfoVect0LinkObjId="SW-96708_0" Pin0InfoVect1LinkObjId="SW-96707_0" Pin0InfoVect2LinkObjId="g_3164870_0" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4205,-1100 4205,-1087 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2dfb010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4205,-1087 4205,-1071 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="powerLine" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_31d7bc0@0" ObjectIDND1="38081@1" ObjectIDZND0="20170@x" ObjectIDZND1="20169@x" ObjectIDZND2="g_3164870@0" Pin0InfoVect0LinkObjId="SW-96708_0" Pin0InfoVect1LinkObjId="SW-96707_0" Pin0InfoVect2LinkObjId="g_3164870_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_31d7bc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4205,-1087 4205,-1071 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2dfb270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4205,-1087 4296,-1087 4296,-1074 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="20170@x" ObjectIDND1="20169@x" ObjectIDND2="g_3164870@0" ObjectIDZND0="g_31d7bc0@0" Pin0InfoVect0LinkObjId="g_31d7bc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-96708_0" Pin1InfoVect1LinkObjId="SW-96707_0" Pin1InfoVect2LinkObjId="g_3164870_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4205,-1087 4296,-1087 4296,-1074 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2dfb4d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4296,-1029 4296,-1014 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_31d7bc0@1" ObjectIDZND0="g_22acb50@0" Pin0InfoVect0LinkObjId="g_22acb50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_31d7bc0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4296,-1029 4296,-1014 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2dfb730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4148,-1042 4130,-1042 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="20170@0" ObjectIDZND0="g_2dfb990@0" Pin0InfoVect0LinkObjId="g_2dfb990_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-96708_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4148,-1042 4130,-1042 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22ac690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4184,-1042 4205,-1042 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="powerLine" EndDevType2="lightningRod" ObjectIDND0="20170@1" ObjectIDZND0="g_31d7bc0@0" ObjectIDZND1="38081@1" ObjectIDZND2="g_3164870@0" Pin0InfoVect0LinkObjId="g_31d7bc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="g_3164870_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-96708_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4184,-1042 4205,-1042 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22ac8f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4205,-1042 4205,-1071 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="powerLine" EndDevType2="lightningRod" ObjectIDND0="20170@x" ObjectIDND1="20169@x" ObjectIDZND0="g_31d7bc0@0" ObjectIDZND1="38081@1" ObjectIDZND2="g_3164870@0" Pin0InfoVect0LinkObjId="g_31d7bc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="g_3164870_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-96708_0" Pin1InfoVect1LinkObjId="SW-96707_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4205,-1042 4205,-1071 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3239a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4300,-763 4252,-763 4252,-746 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="20194@x" ObjectIDND1="g_3c8aea0@0" ObjectIDND2="20193@x" ObjectIDZND0="g_3c68750@0" Pin0InfoVect0LinkObjId="g_3c68750_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-96933_0" Pin1InfoVect1LinkObjId="g_3c8aea0_0" Pin1InfoVect2LinkObjId="SW-96932_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4300,-763 4252,-763 4252,-746 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c1c6d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4300,-823 4300,-847 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="20193@1" ObjectIDZND0="20121@0" Pin0InfoVect0LinkObjId="g_229b1b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-96932_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4300,-823 4300,-847 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c1c930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4368,-763 4386,-763 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="20194@1" ObjectIDZND0="g_3c1cb90@0" Pin0InfoVect0LinkObjId="g_3c1cb90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-96933_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4368,-763 4386,-763 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c8a9e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4332,-763 4300,-763 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="20194@0" ObjectIDZND0="g_3c8aea0@0" ObjectIDZND1="20193@x" ObjectIDZND2="g_3c68750@0" Pin0InfoVect0LinkObjId="g_3c8aea0_0" Pin0InfoVect1LinkObjId="SW-96932_0" Pin0InfoVect2LinkObjId="g_3c68750_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-96933_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4332,-763 4300,-763 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c8ac40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4300,-787 4300,-763 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="20193@0" ObjectIDZND0="20194@x" ObjectIDZND1="g_3c8aea0@0" ObjectIDZND2="g_3c68750@0" Pin0InfoVect0LinkObjId="SW-96933_0" Pin0InfoVect1LinkObjId="g_3c8aea0_0" Pin0InfoVect2LinkObjId="g_3c68750_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-96932_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4300,-787 4300,-763 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29fd0c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4300,-696 4300,-711 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_324c7a0@0" ObjectIDZND0="g_3c8aea0@0" Pin0InfoVect0LinkObjId="g_3c8aea0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_324c7a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4300,-696 4300,-711 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29fd320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4300,-742 4300,-763 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_3c8aea0@1" ObjectIDZND0="20194@x" ObjectIDZND1="20193@x" ObjectIDZND2="g_3c68750@0" Pin0InfoVect0LinkObjId="SW-96933_0" Pin0InfoVect1LinkObjId="SW-96932_0" Pin0InfoVect2LinkObjId="g_3c68750_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3c8aea0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4300,-742 4300,-763 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-4KV" id="g_2dd7e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4626,-642 4626,-664 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="20474@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_2dfb990_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-102126_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4626,-642 4626,-664 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_347c620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4494,-473 4494,-451 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="20122@0" ObjectIDZND0="20187@1" Pin0InfoVect0LinkObjId="SW-96770_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22f5dd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4494,-473 4494,-451 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_347c880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4495,-412 4495,-392 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="20187@0" ObjectIDZND0="20186@1" Pin0InfoVect0LinkObjId="SW-96769_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-96770_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4495,-412 4495,-392 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f4b0c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4495,-365 4495,-344 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="20186@0" ObjectIDZND0="20188@1" Pin0InfoVect0LinkObjId="SW-96771_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-96769_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4495,-365 4495,-344 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3002170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4495,-287 4495,-185 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="20188@x" ObjectIDND1="g_309e0d0@0" ObjectIDZND0="34498@0" Pin0InfoVect0LinkObjId="EC-CX_SHJ.086Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-96771_0" Pin1InfoVect1LinkObjId="g_309e0d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4495,-287 4495,-185 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_30023d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4535,-269 4535,-287 4495,-287 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_309e0d0@0" ObjectIDZND0="20188@x" ObjectIDZND1="34498@x" Pin0InfoVect0LinkObjId="SW-96771_0" Pin0InfoVect1LinkObjId="EC-CX_SHJ.086Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_309e0d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4535,-269 4535,-287 4495,-287 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3002630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4495,-287 4495,-308 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_309e0d0@0" ObjectIDND1="34498@x" ObjectIDZND0="20188@0" Pin0InfoVect0LinkObjId="SW-96771_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_309e0d0_0" Pin1InfoVect1LinkObjId="EC-CX_SHJ.086Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4495,-287 4495,-308 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d928f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4678,-297 4678,-320 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2f53420@0" ObjectIDZND0="g_3003100@1" Pin0InfoVect0LinkObjId="g_3003100_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f53420_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4678,-297 4678,-320 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22f5dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4677,-437 4677,-473 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="20195@1" ObjectIDZND0="20122@0" Pin0InfoVect0LinkObjId="g_2f30fd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-96934_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4677,-437 4677,-473 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22f6030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4677,-380 4738,-380 4738,-362 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_3003100@0" ObjectIDND1="20195@x" ObjectIDZND0="g_309d320@0" Pin0InfoVect0LinkObjId="g_309d320_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3003100_0" Pin1InfoVect1LinkObjId="SW-96934_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4677,-380 4738,-380 4738,-362 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22f6290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4677,-365 4677,-380 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_3003100@0" ObjectIDZND0="g_309d320@0" ObjectIDZND1="20195@x" Pin0InfoVect0LinkObjId="g_309d320_0" Pin0InfoVect1LinkObjId="SW-96934_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3003100_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4677,-365 4677,-380 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_221cbd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4677,-380 4677,-401 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_3003100@0" ObjectIDND1="g_309d320@0" ObjectIDZND0="20195@0" Pin0InfoVect0LinkObjId="SW-96934_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3003100_0" Pin1InfoVect1LinkObjId="g_309d320_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4677,-380 4677,-401 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_233a080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4342,-473 4342,-448 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="20122@0" ObjectIDZND0="20184@1" Pin0InfoVect0LinkObjId="SW-96760_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22f5dd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4342,-473 4342,-448 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_233a2e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4342,-412 4342,-392 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="20184@0" ObjectIDZND0="20183@1" Pin0InfoVect0LinkObjId="SW-96759_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-96760_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4342,-412 4342,-392 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33fcb20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4342,-365 4342,-344 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="20183@0" ObjectIDZND0="20185@1" Pin0InfoVect0LinkObjId="SW-96761_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-96759_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4342,-365 4342,-344 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33fcd80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4342,-287 4342,-185 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="20185@x" ObjectIDND1="g_2e56310@0" ObjectIDZND0="34497@0" Pin0InfoVect0LinkObjId="EC-CX_SHJ.085Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-96761_0" Pin1InfoVect1LinkObjId="g_2e56310_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4342,-287 4342,-185 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33fcfe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4382,-269 4382,-287 4342,-287 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_2e56310@0" ObjectIDZND0="20185@x" ObjectIDZND1="34497@x" Pin0InfoVect0LinkObjId="SW-96761_0" Pin0InfoVect1LinkObjId="EC-CX_SHJ.085Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e56310_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4382,-269 4382,-287 4342,-287 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33fd240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4342,-287 4342,-308 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_2e56310@0" ObjectIDND1="34497@x" ObjectIDZND0="20185@0" Pin0InfoVect0LinkObjId="SW-96761_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2e56310_0" Pin1InfoVect1LinkObjId="EC-CX_SHJ.085Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4342,-287 4342,-308 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34bc450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4190,-473 4190,-448 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="20122@0" ObjectIDZND0="20181@1" Pin0InfoVect0LinkObjId="SW-96750_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22f5dd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4190,-473 4190,-448 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3bc8190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4190,-412 4190,-392 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="20181@0" ObjectIDZND0="20180@1" Pin0InfoVect0LinkObjId="SW-96749_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-96750_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4190,-412 4190,-392 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31a9160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4190,-365 4190,-344 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="20180@0" ObjectIDZND0="20182@1" Pin0InfoVect0LinkObjId="SW-96751_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-96749_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4190,-365 4190,-344 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31a93c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4190,-287 4190,-185 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="20182@x" ObjectIDND1="g_2e57050@0" ObjectIDZND0="34496@0" Pin0InfoVect0LinkObjId="EC-CX_SHJ.084Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-96751_0" Pin1InfoVect1LinkObjId="g_2e57050_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4190,-287 4190,-185 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31a9620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4230,-269 4230,-287 4190,-287 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_2e57050@0" ObjectIDZND0="20182@x" ObjectIDZND1="34496@x" Pin0InfoVect0LinkObjId="SW-96751_0" Pin0InfoVect1LinkObjId="EC-CX_SHJ.084Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e57050_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4230,-269 4230,-287 4190,-287 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31a9880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4190,-287 4190,-308 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_2e57050@0" ObjectIDND1="34496@x" ObjectIDZND0="20182@0" Pin0InfoVect0LinkObjId="SW-96751_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2e57050_0" Pin1InfoVect1LinkObjId="EC-CX_SHJ.084Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4190,-287 4190,-308 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_327f300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4037,-473 4037,-448 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="20122@0" ObjectIDZND0="20178@1" Pin0InfoVect0LinkObjId="SW-96740_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22f5dd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4037,-473 4037,-448 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_327f560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4037,-412 4037,-392 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="20178@0" ObjectIDZND0="20177@1" Pin0InfoVect0LinkObjId="SW-96739_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-96740_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4037,-412 4037,-392 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ff9710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4037,-365 4037,-344 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="20177@0" ObjectIDZND0="20179@1" Pin0InfoVect0LinkObjId="SW-96741_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-96739_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4037,-365 4037,-344 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ff9970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4037,-287 4037,-185 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="20179@x" ObjectIDND1="g_328a570@0" ObjectIDZND0="34495@0" Pin0InfoVect0LinkObjId="EC-CX_SHJ.083Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-96741_0" Pin1InfoVect1LinkObjId="g_328a570_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4037,-287 4037,-185 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ff9bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4077,-269 4077,-287 4037,-287 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_328a570@0" ObjectIDZND0="20179@x" ObjectIDZND1="34495@x" Pin0InfoVect0LinkObjId="SW-96741_0" Pin0InfoVect1LinkObjId="EC-CX_SHJ.083Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_328a570_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4077,-269 4077,-287 4037,-287 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ff9e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4037,-287 4037,-308 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_328a570@0" ObjectIDND1="34495@x" ObjectIDZND0="20179@0" Pin0InfoVect0LinkObjId="SW-96741_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_328a570_0" Pin1InfoVect1LinkObjId="EC-CX_SHJ.083Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4037,-287 4037,-308 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23436c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3885,-473 3885,-448 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="20122@0" ObjectIDZND0="20175@1" Pin0InfoVect0LinkObjId="SW-96730_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22f5dd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3885,-473 3885,-448 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2343920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3885,-412 3885,-392 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="20175@0" ObjectIDZND0="20174@1" Pin0InfoVect0LinkObjId="SW-96729_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-96730_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3885,-412 3885,-392 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b8fc30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3885,-365 3885,-344 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="20174@0" ObjectIDZND0="20176@1" Pin0InfoVect0LinkObjId="SW-96731_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-96729_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3885,-365 3885,-344 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b8fe90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3885,-287 3885,-185 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="20176@x" ObjectIDND1="g_32897c0@0" ObjectIDZND0="34494@0" Pin0InfoVect0LinkObjId="EC-CX_SHJ.082Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-96731_0" Pin1InfoVect1LinkObjId="g_32897c0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3885,-287 3885,-185 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b900f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3925,-269 3925,-287 3885,-287 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_32897c0@0" ObjectIDZND0="20176@x" ObjectIDZND1="34494@x" Pin0InfoVect0LinkObjId="SW-96731_0" Pin0InfoVect1LinkObjId="EC-CX_SHJ.082Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_32897c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3925,-269 3925,-287 3885,-287 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b90350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3885,-287 3885,-308 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_32897c0@0" ObjectIDND1="34494@x" ObjectIDZND0="20176@0" Pin0InfoVect0LinkObjId="SW-96731_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_32897c0_0" Pin1InfoVect1LinkObjId="EC-CX_SHJ.082Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3885,-287 3885,-308 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33f6d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3732,-473 3732,-448 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="20122@0" ObjectIDZND0="20172@1" Pin0InfoVect0LinkObjId="SW-96720_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22f5dd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3732,-473 3732,-448 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33f6f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3732,-412 3732,-392 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="20172@0" ObjectIDZND0="20171@1" Pin0InfoVect0LinkObjId="SW-96719_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-96720_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3732,-412 3732,-392 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2eab3e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3732,-365 3732,-344 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="20171@0" ObjectIDZND0="20173@1" Pin0InfoVect0LinkObjId="SW-96721_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-96719_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3732,-365 3732,-344 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2eab640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3732,-287 3732,-185 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" ObjectIDND0="g_3165620@0" ObjectIDND1="20173@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3165620_0" Pin1InfoVect1LinkObjId="SW-96721_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3732,-287 3732,-185 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2eab8a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3772,-269 3772,-287 3732,-287 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3165620@0" ObjectIDZND0="20173@x" Pin0InfoVect0LinkObjId="SW-96721_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3165620_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3772,-269 3772,-287 3732,-287 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2eabb00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3732,-287 3732,-308 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3165620@0" ObjectIDZND0="20173@0" Pin0InfoVect0LinkObjId="SW-96721_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3165620_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3732,-287 3732,-308 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-4KV" id="g_30cdd40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4627,-684 4627,-664 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="20473@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_2dfb990_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-102124_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4627,-684 4627,-664 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_31d79d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4627,-847 4627,-830 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="lightningRod" ObjectIDND0="20121@0" ObjectIDZND0="g_3c41f30@0" Pin0InfoVect0LinkObjId="g_3c41f30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_229b1b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4627,-847 4627,-830 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_307f440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4205,-847 4205,-873 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="20121@0" ObjectIDZND0="20168@0" Pin0InfoVect0LinkObjId="SW-96706_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_229b1b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4205,-847 4205,-873 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_307f630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4205,-909 4205,-933 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="20168@1" ObjectIDZND0="20167@0" Pin0InfoVect0LinkObjId="SW-96705_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-96706_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4205,-909 4205,-933 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_307f820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4205,-960 4205,-980 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="20167@1" ObjectIDZND0="20169@0" Pin0InfoVect0LinkObjId="SW-96707_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-96705_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4205,-960 4205,-980 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_307fa10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4205,-1016 4205,-1042 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="20169@1" ObjectIDZND0="20170@x" ObjectIDZND1="g_31d7bc0@0" ObjectIDZND2="38081@1" Pin0InfoVect0LinkObjId="SW-96708_0" Pin0InfoVect1LinkObjId="g_31d7bc0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-96707_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4205,-1016 4205,-1042 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f30b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4627,-785 4627,-770 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_3c41f30@1" ObjectIDZND0="20197@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3c41f30_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4627,-785 4627,-770 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-4KV" id="g_2f30d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4627,-728 4627,-711 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="20197@1" ObjectIDZND0="20473@0" Pin0InfoVect0LinkObjId="SW-102124_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f30b10_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4627,-728 4627,-711 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f30fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4627,-492 4627,-473 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="busSection" ObjectIDND0="g_29fe010@1" ObjectIDZND0="20122@0" Pin0InfoVect0LinkObjId="g_22f5dd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29fe010_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4627,-492 4627,-473 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-4KV" id="g_33f7720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4626,-615 4626,-596 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="20474@0" ObjectIDZND0="20147@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-102126_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4626,-615 4626,-596 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33f7950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4626,-554 4626,-537 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="20147@1" ObjectIDZND0="g_29fe010@0" Pin0InfoVect0LinkObjId="g_29fe010_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33f7720_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4626,-554 4626,-537 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_324c540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4321,-619 4321,-668 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="lightningRod" ObjectIDND0="g_324bae0@0" ObjectIDZND0="g_324b190@0" Pin0InfoVect0LinkObjId="g_324b190_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_324bae0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4321,-619 4321,-668 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_28aa780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4306,-679 4321,-679 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4306,-679 4321,-679 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_28aa9e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4321,-679 4321,-668 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4321,-679 4321,-668 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_28aac40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4301,-690 4316,-690 4321,-690 4321,-679 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4301,-690 4316,-690 4321,-690 4321,-679 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2f9ea40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4683,-283 4698,-283 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="earth" ObjectIDZND0="g_2f52c10@0" Pin0InfoVect0LinkObjId="g_2f52c10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4683,-283 4698,-283 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2f9eca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4678,-294 4693,-294 4698,-294 4698,-283 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="earth" ObjectIDZND0="g_2f52c10@0" Pin0InfoVect0LinkObjId="g_2f52c10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4678,-294 4693,-294 4698,-294 4698,-283 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2848500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4698,-283 4698,-223 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="earth" ObjectIDZND0="g_2f52c10@0" Pin0InfoVect0LinkObjId="g_2f52c10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4698,-283 4698,-223 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="20121" cx="4022" cy="-847" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20121" cx="4205" cy="-847" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20121" cx="4300" cy="-847" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20122" cx="4021" cy="-473" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20122" cx="4677" cy="-473" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20122" cx="4342" cy="-473" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20122" cx="4190" cy="-473" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20122" cx="4037" cy="-473" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20122" cx="3885" cy="-473" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20122" cx="3732" cy="-473" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20121" cx="4627" cy="-847" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20122" cx="4627" cy="-473" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4626" cy="-664" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4627" cy="-664" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20122" cx="4494" cy="-473" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-37333" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3409.000000 -1090.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5915" ObjectName="DYN-CX_SHJ"/>
     <cge:Meas_Ref ObjectId="37333"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e3ef00" transform="matrix(1.000000 0.000000 0.000000 1.000000 4154.000000 -1154.000000) translate(0,15)">35kV三树线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22a3780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4241.000000 -599.000000) translate(0,15)">35kV母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_30cdfa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4618.000000 -196.000000) translate(0,15)">10kV母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_30ce740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3706.000000 -222.000000) translate(0,15)">10</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_30ce740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3706.000000 -222.000000) translate(0,33)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_30ce740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3706.000000 -222.000000) translate(0,51)">备</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_30ce740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3706.000000 -222.000000) translate(0,69)">用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_344a5a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3859.000000 -222.000000) translate(0,15)">10</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_344a5a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3859.000000 -222.000000) translate(0,33)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_344a5a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3859.000000 -222.000000) translate(0,51)">二</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_344a5a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3859.000000 -222.000000) translate(0,69)">街</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_344a5a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3859.000000 -222.000000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_344ab00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4012.000000 -222.000000) translate(0,15)">10</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_344ab00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4012.000000 -222.000000) translate(0,33)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_344ab00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4012.000000 -222.000000) translate(0,51)">九</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_344ab00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4012.000000 -222.000000) translate(0,69)">街</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_344ab00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4012.000000 -222.000000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_344b060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4163.000000 -222.000000) translate(0,15)">10</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_344b060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4163.000000 -222.000000) translate(0,33)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_344b060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4163.000000 -222.000000) translate(0,51)">迤</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_344b060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4163.000000 -222.000000) translate(0,69)">能</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_344b060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4163.000000 -222.000000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_344b5c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4320.000000 -222.000000) translate(0,15)">10</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_344b5c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4320.000000 -222.000000) translate(0,33)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_344b5c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4320.000000 -222.000000) translate(0,51)">集</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_344b5c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4320.000000 -222.000000) translate(0,69)">镇</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_344b5c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4320.000000 -222.000000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_31d71b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4473.000000 -222.000000) translate(0,15)">10</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_31d71b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4473.000000 -222.000000) translate(0,33)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_31d71b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4473.000000 -222.000000) translate(0,51)">上</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_31d71b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4473.000000 -222.000000) translate(0,69)">网</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_31d71b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4473.000000 -222.000000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31d85c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4215.000000 -954.000000) translate(0,12)">381</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_307ed80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4213.000000 -900.000000) translate(0,12)">3811</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_307efc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4213.000000 -1004.000000) translate(0,12)">3816</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_307f200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4146.000000 -1068.000000) translate(0,12)">38167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_307fc40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4038.000000 -752.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3080000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4028.000000 -805.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3080240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4031.000000 -585.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3080480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4029.000000 -530.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2f2f580" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4693.000000 -871.000000) translate(0,15)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f2f7c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4307.000000 -812.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f2fa00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4330.000000 -789.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_33f7bb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4485.500000 -768.000000) translate(0,15)">35kV1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33f83b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4684.000000 -426.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33f8620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4507.000000 -385.000000) translate(0,12)">086</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33f8860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4503.000000 -437.000000) translate(0,12)">0861</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33f8aa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4504.000000 -332.000000) translate(0,12)">0866</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33f8ce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4353.000000 -385.000000) translate(0,12)">085</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33f8f20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4350.000000 -437.000000) translate(0,12)">0851</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33f9160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4351.000000 -332.000000) translate(0,12)">0856</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c66c50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4198.000000 -437.000000) translate(0,12)">0841</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c66e90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4199.000000 -332.000000) translate(0,12)">0846</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c670d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4202.000000 -385.000000) translate(0,12)">084</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c67310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4048.000000 -385.000000) translate(0,12)">083</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c67550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4046.000000 -332.000000) translate(0,12)">0836</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c67790" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4045.000000 -437.000000) translate(0,12)">0831</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c679d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3896.000000 -385.000000) translate(0,12)">082</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c67c10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3894.000000 -332.000000) translate(0,12)">0826</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c67e50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3893.000000 -437.000000) translate(0,12)">0821</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c68090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3744.000000 -385.000000) translate(0,12)">081</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c682d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3740.000000 -437.000000) translate(0,12)">0811</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c68510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3741.000000 -332.000000) translate(0,12)">0816</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_4320c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3280.500000 -1170.500000) translate(0,16)">树苴变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_4321dc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -592.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_4321dc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -592.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_4321dc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -592.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_4321dc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -592.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_4321dc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -592.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_4321dc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -592.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_4321dc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -592.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_4321dc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -592.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_4321dc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -592.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_4321dc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -592.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_4321dc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -592.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_4321dc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -592.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_4321dc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -592.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_4321dc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -592.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_4321dc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -592.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_4321dc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -592.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_4321dc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -592.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_4321dc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -592.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_43220a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -1030.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_43220a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -1030.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_43220a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -1030.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_43220a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -1030.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_43220a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -1030.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_43220a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -1030.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_43220a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -1030.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39438e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4527.000000 -748.000000) translate(0,12)">S11-50kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3943b30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4585.000000 -704.000000) translate(0,12)">411</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3943d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4588.000000 -637.000000) translate(0,12)">412</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3943fa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4523.000000 -568.000000) translate(0,12)">S11-50kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_30400f0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4265.000000 -965.000000) translate(0,15)">35kV电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_30402f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3245.000000 -236.000000) translate(0,17)">3812338</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_30406a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3466.000000 -1155.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_3040b50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3466.000000 -1190.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3040e40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3151.000000 -771.000000) translate(0,15)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_324a310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3870.000000 -644.000000) translate(0,15)">SZ11-3150/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f524e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4725.000000 -672.000000) translate(0,12)">0.4kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2f529d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4694.000000 -500.000000) translate(0,15)">10kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2849160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3115.000000 -198.000000) translate(0,17)">楚雄巡维中</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2849160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3115.000000 -198.000000) translate(0,38)">心变运三班：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2963700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3242.000000 -208.500000) translate(0,17)">18787878955</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2963700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3242.000000 -208.500000) translate(0,38)">18787878953</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2963700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3242.000000 -208.500000) translate(0,59)">18787878979</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_2963ac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3404.000000 -1174.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2965190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3914.000000 -681.000000) translate(0,15)">#1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_309a550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4493.000000 -595.000000) translate(0,15)">10kV2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_309aa50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3687.000000 -549.500000) translate(0,12)">Uc(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_309acd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3687.000000 -564.750000) translate(0,12)">Ub(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_309aee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3687.000000 -580.000000) translate(0,12)">Ua(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_309b120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3693.000000 -532.250000) translate(0,12)">U0(V):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_309b360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3679.000000 -517.000000) translate(0,12)">Uab(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_309b5a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3679.000000 -501.000000) translate(0,12)">Ubc(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" graphid="g_309b7e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3144.000000 -726.000000) translate(0,20)">隔刀远控</text>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-1082"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-602"/>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_SJ" endPointId="0" endStationName="CX_SHJ" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_sanshu" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4205,-1100 4205,-1131 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="38081" ObjectName="AC-35kV.LN_sanshu"/>
    <cge:TPSR_Ref TObjectID="38081_SS-64"/></metadata>
   <polyline fill="none" opacity="0" points="4205,-1100 4205,-1131 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_22acb50">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4288.456311 -970.000000)" xlink:href="#lightningRod:shape131"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3c8aea0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4290.938217 -706.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29fe010">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4632.000000 -487.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3003100">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4683.000000 -315.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3c41f30">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4632.000000 -780.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_31d7bc0">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4301.000000 -1024.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3c68750">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4245.000000 -688.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3164870">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4252.000000 -995.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3165620">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3765.000000 -211.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_309d320">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4731.000000 -304.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_309e0d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4528.000000 -211.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e56310">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4375.000000 -211.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e57050">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4223.000000 -211.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_32897c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3918.000000 -211.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_328a570">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4070.000000 -211.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_324b190">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4341.000000 -673.000000)" xlink:href="#lightningRod:shape204"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_324c7a0">
    <use class="BV-35KV" transform="matrix(-1.000000 0.000000 0.000000 -1.000000 4314.000000 -670.000000)" xlink:href="#lightningRod:shape123"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f53420">
    <use class="BV-10KV" transform="matrix(-1.000000 0.000000 0.000000 -1.000000 4692.000000 -271.000000)" xlink:href="#lightningRod:shape123"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f9ef10">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4718.000000 -277.000000)" xlink:href="#lightningRod:shape204"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-96865" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4134.000000 -972.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96865" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20167"/>
     <cge:Term_Ref ObjectID="28073"/>
    <cge:TPSR_Ref TObjectID="20167"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-96866" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4134.000000 -972.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96866" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20167"/>
     <cge:Term_Ref ObjectID="28073"/>
    <cge:TPSR_Ref TObjectID="20167"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-96863" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4134.000000 -972.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96863" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20167"/>
     <cge:Term_Ref ObjectID="28073"/>
    <cge:TPSR_Ref TObjectID="20167"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-96693" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3744.000000 -932.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96693" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20121"/>
     <cge:Term_Ref ObjectID="28071"/>
    <cge:TPSR_Ref TObjectID="20121"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-96694" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3744.000000 -932.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96694" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20121"/>
     <cge:Term_Ref ObjectID="28071"/>
    <cge:TPSR_Ref TObjectID="20121"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-96695" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3744.000000 -932.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96695" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20121"/>
     <cge:Term_Ref ObjectID="28071"/>
    <cge:TPSR_Ref TObjectID="20121"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-96703" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3744.000000 -932.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96703" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20121"/>
     <cge:Term_Ref ObjectID="28071"/>
    <cge:TPSR_Ref TObjectID="20121"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-96696" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3744.000000 -932.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96696" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20121"/>
     <cge:Term_Ref ObjectID="28071"/>
    <cge:TPSR_Ref TObjectID="20121"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-96698" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3742.000000 -578.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96698" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20122"/>
     <cge:Term_Ref ObjectID="28072"/>
    <cge:TPSR_Ref TObjectID="20122"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-96699" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3742.000000 -578.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96699" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20122"/>
     <cge:Term_Ref ObjectID="28072"/>
    <cge:TPSR_Ref TObjectID="20122"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-96700" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3742.000000 -578.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96700" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20122"/>
     <cge:Term_Ref ObjectID="28072"/>
    <cge:TPSR_Ref TObjectID="20122"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-96704" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3742.000000 -578.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96704" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20122"/>
     <cge:Term_Ref ObjectID="28072"/>
    <cge:TPSR_Ref TObjectID="20122"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-96701" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3742.000000 -578.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96701" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20122"/>
     <cge:Term_Ref ObjectID="28072"/>
    <cge:TPSR_Ref TObjectID="20122"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ubc" PreSymbol="0" appendix="" decimal="2" id="ME-96702" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3742.000000 -578.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96702" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20122"/>
     <cge:Term_Ref ObjectID="28072"/>
    <cge:TPSR_Ref TObjectID="20122"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-96907" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3945.000000 -605.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96907" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20191"/>
     <cge:Term_Ref ObjectID="28135"/>
    <cge:TPSR_Ref TObjectID="20191"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-96908" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3945.000000 -605.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96908" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20191"/>
     <cge:Term_Ref ObjectID="28135"/>
    <cge:TPSR_Ref TObjectID="20191"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-96904" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3945.000000 -605.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96904" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20191"/>
     <cge:Term_Ref ObjectID="28135"/>
    <cge:TPSR_Ref TObjectID="20191"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-96870" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3728.000000 -120.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96870" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20171"/>
     <cge:Term_Ref ObjectID="28081"/>
    <cge:TPSR_Ref TObjectID="20171"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-96871" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3728.000000 -120.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96871" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20171"/>
     <cge:Term_Ref ObjectID="28081"/>
    <cge:TPSR_Ref TObjectID="20171"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-96868" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3728.000000 -120.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96868" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20171"/>
     <cge:Term_Ref ObjectID="28081"/>
    <cge:TPSR_Ref TObjectID="20171"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-96875" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3859.000000 -120.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96875" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20174"/>
     <cge:Term_Ref ObjectID="28087"/>
    <cge:TPSR_Ref TObjectID="20174"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-96876" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3859.000000 -120.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96876" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20174"/>
     <cge:Term_Ref ObjectID="28087"/>
    <cge:TPSR_Ref TObjectID="20174"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-96873" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3859.000000 -120.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96873" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20174"/>
     <cge:Term_Ref ObjectID="28087"/>
    <cge:TPSR_Ref TObjectID="20174"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-96880" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4002.000000 -120.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96880" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20177"/>
     <cge:Term_Ref ObjectID="28093"/>
    <cge:TPSR_Ref TObjectID="20177"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-96881" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4002.000000 -120.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96881" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20177"/>
     <cge:Term_Ref ObjectID="28093"/>
    <cge:TPSR_Ref TObjectID="20177"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-96878" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4002.000000 -120.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96878" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20177"/>
     <cge:Term_Ref ObjectID="28093"/>
    <cge:TPSR_Ref TObjectID="20177"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-96885" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4166.000000 -121.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96885" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20180"/>
     <cge:Term_Ref ObjectID="28099"/>
    <cge:TPSR_Ref TObjectID="20180"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-96886" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4166.000000 -121.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96886" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20180"/>
     <cge:Term_Ref ObjectID="28099"/>
    <cge:TPSR_Ref TObjectID="20180"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-96883" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4166.000000 -121.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96883" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20180"/>
     <cge:Term_Ref ObjectID="28099"/>
    <cge:TPSR_Ref TObjectID="20180"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-96890" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4307.000000 -120.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96890" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20183"/>
     <cge:Term_Ref ObjectID="28105"/>
    <cge:TPSR_Ref TObjectID="20183"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-96891" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4307.000000 -120.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96891" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20183"/>
     <cge:Term_Ref ObjectID="28105"/>
    <cge:TPSR_Ref TObjectID="20183"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-96888" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4307.000000 -120.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96888" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20183"/>
     <cge:Term_Ref ObjectID="28105"/>
    <cge:TPSR_Ref TObjectID="20183"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-96895" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4468.000000 -120.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96895" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20186"/>
     <cge:Term_Ref ObjectID="28111"/>
    <cge:TPSR_Ref TObjectID="20186"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-96896" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4468.000000 -120.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96896" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20186"/>
     <cge:Term_Ref ObjectID="28111"/>
    <cge:TPSR_Ref TObjectID="20186"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-96893" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4468.000000 -120.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96893" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20186"/>
     <cge:Term_Ref ObjectID="28111"/>
    <cge:TPSR_Ref TObjectID="20186"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-96916" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4721.000000 -732.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96916" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20473"/>
     <cge:Term_Ref ObjectID="28530"/>
    <cge:TPSR_Ref TObjectID="20473"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-96917" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4721.000000 -732.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96917" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20473"/>
     <cge:Term_Ref ObjectID="28530"/>
    <cge:TPSR_Ref TObjectID="20473"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-96913" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4721.000000 -732.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96913" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20473"/>
     <cge:Term_Ref ObjectID="28530"/>
    <cge:TPSR_Ref TObjectID="20473"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-96690" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4721.000000 -646.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96690" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20474"/>
     <cge:Term_Ref ObjectID="28532"/>
    <cge:TPSR_Ref TObjectID="20474"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-96691" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4721.000000 -646.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96691" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20474"/>
     <cge:Term_Ref ObjectID="28532"/>
    <cge:TPSR_Ref TObjectID="20474"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-96687" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4721.000000 -646.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96687" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20474"/>
     <cge:Term_Ref ObjectID="28532"/>
    <cge:TPSR_Ref TObjectID="20474"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-96910" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4721.000000 -784.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96910" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20197"/>
     <cge:Term_Ref ObjectID="28150"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-96911" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4721.000000 -784.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96911" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20197"/>
     <cge:Term_Ref ObjectID="28150"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-96912" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4721.000000 -784.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96912" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20197"/>
     <cge:Term_Ref ObjectID="28150"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-96684" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4721.000000 -594.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96684" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20147"/>
     <cge:Term_Ref ObjectID="28123"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-96685" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4721.000000 -594.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96685" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20147"/>
     <cge:Term_Ref ObjectID="28123"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-96686" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4721.000000 -594.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96686" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20147"/>
     <cge:Term_Ref ObjectID="28123"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="2" id="ME-96922" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4151.000000 -684.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96922" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20198"/>
     <cge:Term_Ref ObjectID="28154"/>
    <cge:TPSR_Ref TObjectID="20198"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-96921" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4151.000000 -684.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96921" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20198"/>
     <cge:Term_Ref ObjectID="28154"/>
    <cge:TPSR_Ref TObjectID="20198"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-96901" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3936.000000 -785.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96901" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20189"/>
     <cge:Term_Ref ObjectID="28131"/>
    <cge:TPSR_Ref TObjectID="20189"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-96902" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3936.000000 -785.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96902" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20189"/>
     <cge:Term_Ref ObjectID="28131"/>
    <cge:TPSR_Ref TObjectID="20189"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-96898" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3936.000000 -785.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96898" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20189"/>
     <cge:Term_Ref ObjectID="28131"/>
    <cge:TPSR_Ref TObjectID="20189"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-96903" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3936.000000 -785.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96903" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20189"/>
     <cge:Term_Ref ObjectID="28131"/>
    <cge:TPSR_Ref TObjectID="20189"/></metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="139" x="3240" y="-1181"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3192" y="-1198"/></g>
   <g href="35kV树苴变10kV备用线081断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="3744" y="-385"/></g>
   <g href="35kV树苴变10kV二街线082断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="3896" y="-385"/></g>
   <g href="35kV树苴变10kV九街线083断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4048" y="-385"/></g>
   <g href="35kV树苴变10kV迤能线084断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4202" y="-385"/></g>
   <g href="35kV树苴变10kV集镇线085断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4353" y="-385"/></g>
   <g href="35kV树苴变10kV上网线086断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4507" y="-385"/></g>
   <g href="35kV树苴变35kV三树线381断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4215" y="-954"/></g>
   <g href="cx_配调_配网接线图35_楚雄.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3455" y="-1163"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3455" y="-1198"/></g>
   <g href="35kV树苴变GG间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="72" x="3151" y="-771"/></g>
   <g href="AVC树苴站.svg" style="fill-opacity:0"><rect height="47" qtmmishow="hidden" stroke="rgb(0,0,0)" width="45" x="3400" y="-1188"/></g>
   <g href="35kV树苴变1号主变间隔接线图.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="73" x="3914" y="-681"/></g>
   <g href="35kV树苴变隔刀开关远方遥控清单.svg" style="fill-opacity:0"><rect height="24" qtmmishow="hidden" width="96" x="3142" y="-727"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4322530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3664.000000 884.000000) translate(0,12)">U0（V）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4322870" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3657.000000 900.000000) translate(0,12)">Uc（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3943210" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3657.000000 913.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3943460" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3649.000000 869.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39436a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3657.000000 931.000000) translate(0,12)">Ua（kV）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d17ed0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4054.000000 670.000000) translate(0,12)">油温（℃）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d184a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4054.000000 685.000000) translate(0,12)">档位（档）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_323e1c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4092.000000 940.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_323e3f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4078.000000 970.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3150150" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4067.000000 955.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3150480" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3902.000000 573.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31506e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3888.000000 603.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3150920" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3877.000000 588.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3150c50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3684.000000 91.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3150eb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3670.000000 121.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31510f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3659.000000 106.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3151f10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4679.000000 703.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2927ce0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4665.000000 733.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2927f10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4654.000000 718.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2928240" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4678.000000 615.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29284a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4664.000000 645.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29286e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4653.000000 630.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2929580" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4672.000000 784.000000) translate(0,12)">Ua(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29297b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4672.000000 769.000000) translate(0,12)">Ub(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29299f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4672.000000 754.000000) translate(0,12)">Uc(V):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_292c430" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4672.000000 594.000000) translate(0,12)">Ua(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_292c650" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4672.000000 579.000000) translate(0,12)">Ub(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_292c890" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4672.000000 564.000000) translate(0,12)">Uc(V):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_292dce0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3862.000000 772.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_303f4c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3887.000000 757.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_303f700" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3892.000000 741.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_303f940" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3873.000000 787.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_SHJ.CX_SHJ_1Zyb">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="28149"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4614.000000 -723.000000)" xlink:href="#transformer2:shape11_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-4KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4614.000000 -723.000000)" xlink:href="#transformer2:shape11_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="20197" ObjectName="TF-CX_SHJ.CX_SHJ_1Zyb"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_SHJ.CX_SHJ_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="28153"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.090909 -0.000000 0.000000 -0.928571 3981.000000 -611.500000)" xlink:href="#transformer2:shape39_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.090909 -0.000000 0.000000 -0.928571 3981.000000 -611.500000)" xlink:href="#transformer2:shape39_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="20198" ObjectName="TF-CX_SHJ.CX_SHJ_1T"/>
    <cge:TPSR_Ref TObjectID="20198"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_SHJ.CX_SHJ_2Zyb">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="28125"/>
     </metadata>
     <use class="BV-4KV" transform="matrix(-0.520000 -0.000000 -0.000000 0.577778 4639.000000 -603.000000)" xlink:href="#transformer2:shape48_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(-0.520000 -0.000000 -0.000000 0.577778 4639.000000 -603.000000)" xlink:href="#transformer2:shape48_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="20147" ObjectName="TF-CX_SHJ.CX_SHJ_2Zyb"/>
    </metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="139" x="3240" y="-1181"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="139" x="3240" y="-1181"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3192" y="-1198"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3192" y="-1198"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="3744" y="-385"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="3744" y="-385"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="3896" y="-385"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="3896" y="-385"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4048" y="-385"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4048" y="-385"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4202" y="-385"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4202" y="-385"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4353" y="-385"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4353" y="-385"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4507" y="-385"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4507" y="-385"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4215" y="-954"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4215" y="-954"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3455" y="-1163"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3455" y="-1163"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3455" y="-1198"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3455" y="-1198"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="72" x="3151" y="-771"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="72" x="3151" y="-771"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <polygon fill="rgb(255,255,255)" points="3400,-1188 3397,-1191 3397,-1138 3400,-1141 3400,-1188" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(255,255,255)" points="3400,-1188 3397,-1191 3448,-1191 3445,-1188 3400,-1188" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(127,127,127)" points="3400,-1141 3397,-1138 3448,-1138 3445,-1141 3400,-1141" stroke="rgb(127,127,127)"/>
     <polygon fill="rgb(127,127,127)" points="3445,-1188 3448,-1191 3448,-1138 3445,-1141 3445,-1188" stroke="rgb(127,127,127)"/>
     <rect fill="rgb(255,255,255)" height="47" stroke="rgb(255,255,255)" width="45" x="3400" y="-1188"/>
     <rect fill="none" height="47" qtmmishow="hidden" stroke="rgb(0,0,0)" width="45" x="3400" y="-1188"/>
    </a>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="73" x="3914" y="-681"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="73" x="3914" y="-681"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="24" qtmmishow="hidden" width="96" x="3142" y="-727"/>
    </a>
   <metadata/><rect fill="white" height="24" opacity="0" stroke="white" transform="" width="96" x="3142" y="-727"/></g>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-96931">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4006.196999 -483.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20192" ObjectName="SW-CX_SHJ.CX_SHJ_0011SW"/>
     <cge:Meas_Ref ObjectId="96931"/>
    <cge:TPSR_Ref TObjectID="20192"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-96929">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4007.130332 -751.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20190" ObjectName="SW-CX_SHJ.CX_SHJ_3011SW"/>
     <cge:Meas_Ref ObjectId="96929"/>
    <cge:TPSR_Ref TObjectID="20190"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-96706">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4189.789644 -851.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20168" ObjectName="SW-CX_SHJ.CX_SHJ_3811SW"/>
     <cge:Meas_Ref ObjectId="96706"/>
    <cge:TPSR_Ref TObjectID="20168"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-96707">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4189.789644 -958.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20169" ObjectName="SW-CX_SHJ.CX_SHJ_3816SW"/>
     <cge:Meas_Ref ObjectId="96707"/>
    <cge:TPSR_Ref TObjectID="20169"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-96708">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4157.456311 -1016.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20170" ObjectName="SW-CX_SHJ.CX_SHJ_38167SW"/>
     <cge:Meas_Ref ObjectId="96708"/>
    <cge:TPSR_Ref TObjectID="20170"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-96932">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4284.938217 -765.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20193" ObjectName="SW-CX_SHJ.CX_SHJ_3901SW"/>
     <cge:Meas_Ref ObjectId="96932"/>
    <cge:TPSR_Ref TObjectID="20193"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-96933">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4340.938217 -737.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20194" ObjectName="SW-CX_SHJ.CX_SHJ_39017SW"/>
     <cge:Meas_Ref ObjectId="96933"/>
    <cge:TPSR_Ref TObjectID="20194"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-96770">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4480.333333 -390.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20187" ObjectName="SW-CX_SHJ.CX_SHJ_0861SW"/>
     <cge:Meas_Ref ObjectId="96770"/>
    <cge:TPSR_Ref TObjectID="20187"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-96771">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4480.333333 -286.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20188" ObjectName="SW-CX_SHJ.CX_SHJ_0866SW"/>
     <cge:Meas_Ref ObjectId="96771"/>
    <cge:TPSR_Ref TObjectID="20188"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-96934">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4662.000000 -379.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20195" ObjectName="SW-CX_SHJ.CX_SHJ_0901SW"/>
     <cge:Meas_Ref ObjectId="96934"/>
    <cge:TPSR_Ref TObjectID="20195"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-96760">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4326.733333 -390.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20184" ObjectName="SW-CX_SHJ.CX_SHJ_0851SW"/>
     <cge:Meas_Ref ObjectId="96760"/>
    <cge:TPSR_Ref TObjectID="20184"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-96761">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4326.733333 -286.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20185" ObjectName="SW-CX_SHJ.CX_SHJ_0856SW"/>
     <cge:Meas_Ref ObjectId="96761"/>
    <cge:TPSR_Ref TObjectID="20185"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-96750">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4175.133333 -390.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20181" ObjectName="SW-CX_SHJ.CX_SHJ_0841SW"/>
     <cge:Meas_Ref ObjectId="96750"/>
    <cge:TPSR_Ref TObjectID="20181"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-96751">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4175.133333 -286.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20182" ObjectName="SW-CX_SHJ.CX_SHJ_0846SW"/>
     <cge:Meas_Ref ObjectId="96751"/>
    <cge:TPSR_Ref TObjectID="20182"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-96740">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4021.533333 -390.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20178" ObjectName="SW-CX_SHJ.CX_SHJ_0831SW"/>
     <cge:Meas_Ref ObjectId="96740"/>
    <cge:TPSR_Ref TObjectID="20178"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-96741">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4021.533333 -286.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20179" ObjectName="SW-CX_SHJ.CX_SHJ_0836SW"/>
     <cge:Meas_Ref ObjectId="96741"/>
    <cge:TPSR_Ref TObjectID="20179"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-96730">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3869.933333 -390.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20175" ObjectName="SW-CX_SHJ.CX_SHJ_0821SW"/>
     <cge:Meas_Ref ObjectId="96730"/>
    <cge:TPSR_Ref TObjectID="20175"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-96731">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3869.933333 -286.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20176" ObjectName="SW-CX_SHJ.CX_SHJ_0826SW"/>
     <cge:Meas_Ref ObjectId="96731"/>
    <cge:TPSR_Ref TObjectID="20176"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-96720">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3717.333333 -390.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20172" ObjectName="SW-CX_SHJ.CX_SHJ_0811SW"/>
     <cge:Meas_Ref ObjectId="96720"/>
    <cge:TPSR_Ref TObjectID="20172"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-96721">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3717.333333 -286.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20173" ObjectName="SW-CX_SHJ.CX_SHJ_0816SW"/>
     <cge:Meas_Ref ObjectId="96721"/>
    <cge:TPSR_Ref TObjectID="20173"/></metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3228.500000 -1122.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_SHJ"/>
</svg>