<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-166" aopId="786686" id="thSvg" product="E8000V2" version="1.0" viewBox="27 -1287 2146 1324">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape8_0">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="91" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="82" y2="91"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="91" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="15" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="breaker2:shape8_1">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="7" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="99" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
   </symbol>
   <symbol id="breaker2:shape8-UnNor1">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="15" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="91" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="82" y2="91"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="91" y2="82"/>
   </symbol>
   <symbol id="breaker2:shape8-UnNor2">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="7" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="98" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="generator:shape4">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="13" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="9" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="5" y2="36"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="8" x2="5" y1="1" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="10" x2="3" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="8" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="1" x2="12" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <rect height="13" stroke-width="0.424575" width="29" x="21" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="5" x2="44" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="59" x2="50" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="59" x2="59" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="62" x2="62" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="65" x2="65" y1="6" y2="10"/>
   </symbol>
   <symbol id="lightningRod:shape193">
    <polyline DF8003:Layer="PUBLIC" points="1,5 10,17 19,5 1,5 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="19,39 10,27 1,39 19,39 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="27" y2="17"/>
   </symbol>
   <symbol id="lightningRod:shape10">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="17" y1="7" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="24" y1="19" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="15" y1="24" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="24" y1="17" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="10" y1="17" y2="24"/>
    <circle cx="17" cy="17" fillStyle="0" r="16" stroke-width="1.0625"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="17" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape28">
    <circle cx="14" cy="13" fillStyle="0" r="13.5" stroke-width="1"/>
    <circle cx="14" cy="34" fillStyle="0" r="14" stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape146">
    <rect height="19" stroke-width="1" width="35" x="0" y="0"/>
    <polyline points="17,19 17,30 " stroke-width="1"/>
    <text font-family="SimSun" font-size="15" graphid="g_2cf2e20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 17.000000) translate(0,12)">SVG</text>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape18_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="57" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="13" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="22" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="13" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="66" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="65" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="74" y2="65"/>
   </symbol>
   <symbol id="switch2:shape18_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="74" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="5" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="65" y2="74"/>
   </symbol>
   <symbol id="switch2:shape18-UnNor1">
    <circle cx="10" cy="69" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="14" y2="65"/>
    <circle cx="10" cy="11" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="13" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="22" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="66" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="65" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="74" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="57" y2="66"/>
   </symbol>
   <symbol id="switch2:shape18-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="57" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="66"/>
    <circle cx="10" cy="11" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="13" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="22" y2="13"/>
    <circle cx="10" cy="69" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="66" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="65" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="74" y2="65"/>
   </symbol>
   <symbol id="transformer2:shape13_0">
    <ellipse cx="38" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="66" x2="71" y1="83" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="71" x2="69" y1="83" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="71" x2="71" y1="80" y2="80"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.911765" x1="1" x2="69" y1="44" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="30" x2="38" y1="74" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="46" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="38" y1="58" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape13_1">
    <circle cx="38" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="29" x2="46" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="46" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="29" y1="34" y2="18"/>
   </symbol>
   <symbol id="transformer2:shape46_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="61" y2="56"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="62" y2="67"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="16" y1="56" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="42" x2="38" y1="25" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="31" y2="31"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,62 40,62 40,31 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="47" y2="5"/>
    <circle cx="16" cy="62" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="61" y2="56"/>
   </symbol>
   <symbol id="transformer2:shape46_1">
    <circle cx="16" cy="84" fillStyle="0" r="15" stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="16,93 10,81 22,81 16,93 16,92 16,93 "/>
   </symbol>
   <symbol id="voltageTransformer:shape75">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="12" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649573" x1="6" x2="28" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.605383" x1="22" x2="22" y1="25" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="22" x2="18" y1="25" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="26" x2="22" y1="23" y2="25"/>
    <circle cx="22" cy="25" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.605383" x1="35" x2="35" y1="25" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="35" x2="31" y1="25" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="39" x2="35" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.605383" x1="49" x2="49" y1="12" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="49" x2="45" y1="12" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="53" x2="49" y1="10" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.605383" x1="35" x2="35" y1="12" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="35" x2="31" y1="12" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="39" x2="35" y1="10" y2="12"/>
    <circle cx="35" cy="12" r="7.5" stroke-width="0.804311"/>
    <circle cx="35" cy="25" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="46" x2="51" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="47" x2="46" y1="24" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="50" x2="51" y1="24" y2="28"/>
    <circle cx="48" cy="12" r="7.5" stroke-width="0.804311"/>
    <circle cx="48" cy="25" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.475524" x1="6" x2="6" y1="27" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="3" x2="9" y1="30" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="5" x2="7" y1="33" y2="33"/>
   </symbol>
   <symbol id="voltageTransformer:shape11">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="18" x2="15" y1="11" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="13" x2="15" y1="11" y2="9"/>
    <circle cx="15" cy="19" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="24" cy="16" fillStyle="0" r="6" stroke-width="0.431185"/>
    <ellipse cx="15" cy="11" fillStyle="0" rx="6.5" ry="6" stroke-width="0.431185"/>
    <circle cx="7" cy="16" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="22" x2="24" y1="18" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="25" x2="25" y1="16" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="27" x2="24" y1="18" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="13" x2="15" y1="22" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="16" x2="16" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="18" x2="15" y1="22" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="6" x2="9" y1="19" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="6" x2="3" y1="19" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="3" x2="9" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="16" x2="16" y1="9" y2="7"/>
   </symbol>
   <symbol id="voltageTransformer:shape6">
    <circle cx="18" cy="15" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="11" cy="11" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="18" cy="7" fillStyle="0" r="6" stroke-width="0.431185"/>
   </symbol>
   <symbol id="voltageTransformer:shape50">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="12" y1="8" y2="11"/>
    <circle cx="9" cy="22" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="9" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="23" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="6" y1="23" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="12" y1="23" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="8" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="6" y1="7" y2="11"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2da9ea0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2daac60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2dab610" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2dac2e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2dad540" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2dae160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2daebc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2daf4a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_293ce30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_293ce30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2db2160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2db2160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2db3af0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2db3af0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_2db4790" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2db6440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2db7090" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2db7df0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2db8710" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2db9ea0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2dbaba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2dbb460" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2dbbc20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2dbcd60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2dbd680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2dbe170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2dbeb30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2dbffb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2dc0b10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2dc1b60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2dc27b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2dd0ac0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2dd1590" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2dc4ff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2dc65d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1334" width="2156" x="22" y="-1292"/>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b12470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 563.000000 930.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b5c0e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 563.000000 945.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2aee170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 563.000000 960.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_234eab0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 555.000000 915.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25532b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 571.000000 900.000000) translate(0,12)">F(Hz):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c6ef40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 367.000000 45.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b5bbe0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 356.000000 30.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_256fa60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 381.000000 15.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c95c90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1939.000000 510.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_235aad0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1939.000000 525.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_235ca50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1939.000000 540.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b55340" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1931.000000 495.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2554600" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1947.000000 480.000000) translate(0,12)">F(Hz):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b51210" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 475.000000 504.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_257d350" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 475.000000 519.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_257d590" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 475.000000 534.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b477b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 467.000000 489.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b479c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 483.000000 474.000000) translate(0,12)">F(Hz):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c52ab0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1440.000000 823.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2971bc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1429.000000 808.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2971e00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1454.000000 793.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c57330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1410.000000 551.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2550cd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1399.000000 536.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2550ee0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1424.000000 521.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_254f4d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 961.000000 554.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b2b220" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 950.000000 539.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b2b460" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 975.000000 524.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_236fc30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 989.000000 820.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c53430" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 978.000000 805.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c53650" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1003.000000 790.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2564ab0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 863.000000 1004.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22239e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 852.000000 989.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2223bf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 877.000000 974.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d255f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1366.000000 1006.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d25850" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1355.000000 991.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d25a90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1380.000000 976.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
  </g><g id="MultiLine_Layer">
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="558,-87 564,-79 " stroke="rgb(255,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="574,-87 566,-78 " stroke="rgb(255,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="568,-88 532,-102 " stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="PolygonFilled_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1838,-1113 1833,-1124 1843,-1124 1838,-1113 1838,-1114 1838,-1113 " stroke="rgb(60,120,255)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="2051,-1113 2046,-1124 2056,-1124 2051,-1113 2051,-1114 2051,-1113 " stroke="rgb(60,120,255)"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-112350">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 797.000000 -962.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21450" ObjectName="SW-CX_AP.CX_AP_281BK"/>
     <cge:Meas_Ref ObjectId="112350"/>
    <cge:TPSR_Ref TObjectID="21450"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-112362">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 894.565184 -777.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21462" ObjectName="SW-CX_AP.CX_AP_201BK"/>
     <cge:Meas_Ref ObjectId="112362"/>
    <cge:TPSR_Ref TObjectID="21462"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-112368">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1341.000000 -778.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21468" ObjectName="SW-CX_AP.CX_AP_202BK"/>
     <cge:Meas_Ref ObjectId="112368"/>
    <cge:TPSR_Ref TObjectID="21468"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-112356">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1302.000000 -962.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21456" ObjectName="SW-CX_AP.CX_AP_282BK"/>
     <cge:Meas_Ref ObjectId="112356"/>
    <cge:TPSR_Ref TObjectID="21456"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-112377">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 894.000000 -474.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21477" ObjectName="SW-CX_AP.CX_AP_301BK"/>
     <cge:Meas_Ref ObjectId="112377"/>
    <cge:TPSR_Ref TObjectID="21477"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-112379">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1340.000000 -474.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21479" ObjectName="SW-CX_AP.CX_AP_302BK"/>
     <cge:Meas_Ref ObjectId="112379"/>
    <cge:TPSR_Ref TObjectID="21479"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-112385">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 661.000000 -334.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21485" ObjectName="SW-CX_AP.CX_AP_383BK"/>
     <cge:Meas_Ref ObjectId="112385"/>
    <cge:TPSR_Ref TObjectID="21485"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-112387">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 756.000000 -335.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21487" ObjectName="SW-CX_AP.CX_AP_384BK"/>
     <cge:Meas_Ref ObjectId="112387"/>
    <cge:TPSR_Ref TObjectID="21487"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-112389">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 851.000000 -334.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21489" ObjectName="SW-CX_AP.CX_AP_385BK"/>
     <cge:Meas_Ref ObjectId="112389"/>
    <cge:TPSR_Ref TObjectID="21489"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-112391">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 957.000000 -334.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21491" ObjectName="SW-CX_AP.CX_AP_386BK"/>
     <cge:Meas_Ref ObjectId="112391"/>
    <cge:TPSR_Ref TObjectID="21491"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-112381">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 450.000000 -331.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21481" ObjectName="SW-CX_AP.CX_AP_381BK"/>
     <cge:Meas_Ref ObjectId="112381"/>
    <cge:TPSR_Ref TObjectID="21481"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-112399">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1461.000000 -332.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21499" ObjectName="SW-CX_AP.CX_AP_392BK"/>
     <cge:Meas_Ref ObjectId="112399"/>
    <cge:TPSR_Ref TObjectID="21499"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-112401">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1566.000000 -331.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21501" ObjectName="SW-CX_AP.CX_AP_393BK"/>
     <cge:Meas_Ref ObjectId="112401"/>
    <cge:TPSR_Ref TObjectID="21501"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-112403">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1663.000000 -331.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21503" ObjectName="SW-CX_AP.CX_AP_394BK"/>
     <cge:Meas_Ref ObjectId="112403"/>
    <cge:TPSR_Ref TObjectID="21503"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-112405">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1766.000000 -331.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21505" ObjectName="SW-CX_AP.CX_AP_395BK"/>
     <cge:Meas_Ref ObjectId="112405"/>
    <cge:TPSR_Ref TObjectID="21505"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-112383">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 555.000000 -332.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21483" ObjectName="SW-CX_AP.CX_AP_382BK"/>
     <cge:Meas_Ref ObjectId="112383"/>
    <cge:TPSR_Ref TObjectID="21483"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-112411">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1220.000000 -340.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21511" ObjectName="SW-CX_AP.CX_AP_312BK"/>
     <cge:Meas_Ref ObjectId="112411"/>
    <cge:TPSR_Ref TObjectID="21511"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1828.000000 -881.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2041.000000 -881.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1988.000000 -881.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1883.000000 -736.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-112393">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1052.000000 -331.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21493" ObjectName="SW-CX_AP.CX_AP_387BK"/>
     <cge:Meas_Ref ObjectId="112393"/>
    <cge:TPSR_Ref TObjectID="21493"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-112395">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1147.000000 -331.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21495" ObjectName="SW-CX_AP.CX_AP_388BK"/>
     <cge:Meas_Ref ObjectId="112395"/>
    <cge:TPSR_Ref TObjectID="21495"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-112397">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1349.000000 -332.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21497" ObjectName="SW-CX_AP.CX_AP_391BK"/>
     <cge:Meas_Ref ObjectId="112397"/>
    <cge:TPSR_Ref TObjectID="21497"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-112407">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1868.000000 -330.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21507" ObjectName="SW-CX_AP.CX_AP_396BK"/>
     <cge:Meas_Ref ObjectId="112407"/>
    <cge:TPSR_Ref TObjectID="21507"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-112409">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1991.000000 -328.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21509" ObjectName="SW-CX_AP.CX_AP_397BK"/>
     <cge:Meas_Ref ObjectId="112409"/>
    <cge:TPSR_Ref TObjectID="21509"/></metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2b55ad0">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1060.000000 -1033.000000)" xlink:href="#voltageTransformer:shape75"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d3afd0">
    <use class="BV-35KV" transform="matrix(1.516129 -0.000000 0.000000 -1.461538 601.000000 -584.000000)" xlink:href="#voltageTransformer:shape11"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d3cab0">
    <use class="BV-35KV" transform="matrix(1.516129 -0.000000 0.000000 -1.461538 1815.000000 -585.000000)" xlink:href="#voltageTransformer:shape11"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d3e840">
    <use class="BV-220KV" transform="matrix(-1.640000 -0.000000 0.000000 -1.545455 787.000000 -1148.000000)" xlink:href="#voltageTransformer:shape6"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d3f0e0">
    <use class="BV-220KV" transform="matrix(-1.640000 -0.000000 0.000000 -1.545455 1290.000000 -1148.000000)" xlink:href="#voltageTransformer:shape6"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2cfaf10">
    <use class="BV-35KV" transform="matrix(1.611111 -0.000000 0.000000 -1.444444 1144.000000 -133.000000)" xlink:href="#voltageTransformer:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2cd41f0">
    <use class="BV-35KV" transform="matrix(1.611111 -0.000000 0.000000 -1.444444 1346.000000 -134.000000)" xlink:href="#voltageTransformer:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_AP.CX_AP_3IIM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1276,-453 2044,-453 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="21448" ObjectName="BS-CX_AP.CX_AP_3IIM"/>
    <cge:TPSR_Ref TObjectID="21448"/></metadata>
   <polyline fill="none" opacity="0" points="1276,-453 2044,-453 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_AP.CX_AP_2IM">
    <g class="BV-220KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="551,-879 1625,-879 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="21446" ObjectName="BS-CX_AP.CX_AP_2IM"/>
    <cge:TPSR_Ref TObjectID="21446"/></metadata>
   <polyline fill="none" opacity="0" points="551,-879 1625,-879 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_AP.CX_AP_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="429,-453 1246,-453 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="21447" ObjectName="BS-CX_AP.CX_AP_3IM"/>
    <cge:TPSR_Ref TObjectID="21447"/></metadata>
   <polyline fill="none" opacity="0" points="429,-453 1246,-453 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_AP.CX_AP_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="30084"/>
     </metadata>
     <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 866.000000 -615.000000)" xlink:href="#transformer2:shape13_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 866.000000 -615.000000)" xlink:href="#transformer2:shape13_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="21515" ObjectName="TF-CX_AP.CX_AP_1T"/>
    <cge:TPSR_Ref TObjectID="21515"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_AP.CX_AP_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="30088"/>
     </metadata>
     <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1312.000000 -615.000000)" xlink:href="#transformer2:shape13_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1312.000000 -615.000000)" xlink:href="#transformer2:shape13_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="21516" ObjectName="TF-CX_AP.CX_AP_2T"/>
    <cge:TPSR_Ref TObjectID="21516"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1822.000000 -999.000000)" xlink:href="#transformer2:shape46_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1822.000000 -999.000000)" xlink:href="#transformer2:shape46_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2035.000000 -999.000000)" xlink:href="#transformer2:shape46_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2035.000000 -999.000000)" xlink:href="#transformer2:shape46_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2b5b4c0">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1123.000000 -1009.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b318e0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 616.000000 -540.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b4ec90">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 590.000000 -521.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_254ef70">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1830.000000 -543.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2af0870">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1804.000000 -524.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25bc5b0">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 829.000000 -1112.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b1e640">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1333.000000 -1112.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ca46a0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 866.000000 -526.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ca25b0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1312.000000 -526.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2390590">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 673.000000 -226.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c67270">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 768.000000 -226.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25bae00">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 863.000000 -226.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c4e7c0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 969.000000 -226.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c53ed0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 417.477768 -237.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_257efe0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 450.000000 -212.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2552bc0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 443.000000 -154.000000)" xlink:href="#lightningRod:shape10"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b5a6d0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 422.000000 -102.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c6d940">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1473.000000 -224.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b4b800">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1578.000000 -223.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c9d150">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1675.000000 -223.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c5e2a0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1778.000000 -223.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2aed720">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 555.000000 -181.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c695f0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 568.000000 -234.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c6a500">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 552.000000 -113.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2cf1220">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1064.000000 -227.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2cf28a0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1141.477768 -91.000000)" xlink:href="#lightningRod:shape146"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2cf47a0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1147.000000 -188.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2cf9ac0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1161.000000 -232.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2cfc1f0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1343.477768 -92.000000)" xlink:href="#lightningRod:shape146"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2cfd400">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1349.000000 -189.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2cd2e80">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1363.000000 -232.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2cdc8a0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1880.000000 -222.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d09910">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1958.477768 -234.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d0a3d0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1991.000000 -209.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d0af60">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1984.000000 -151.000000)" xlink:href="#lightningRod:shape10"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d0be60">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1962.000000 -99.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 78.000000 -1150.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-116482" ratioFlag="0">
    <text fill="rgb(127,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 146.000000 -1034.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="116482" ObjectName="CX_AP:CX_AP_ZJ_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-116483" ratioFlag="0">
    <text fill="rgb(127,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 145.000000 -953.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="116483" ObjectName="CX_AP:CX_AP_ZJ_sumQ"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-111523" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 918.000000 -1004.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111523" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21450"/>
     <cge:Term_Ref ObjectID="29952"/>
    <cge:TPSR_Ref TObjectID="21450"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-111524" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 918.000000 -1004.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111524" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21450"/>
     <cge:Term_Ref ObjectID="29952"/>
    <cge:TPSR_Ref TObjectID="21450"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-111522" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 918.000000 -1004.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111522" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21450"/>
     <cge:Term_Ref ObjectID="29952"/>
    <cge:TPSR_Ref TObjectID="21450"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-111527" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1421.000000 -1006.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111527" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21456"/>
     <cge:Term_Ref ObjectID="29964"/>
    <cge:TPSR_Ref TObjectID="21456"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-111528" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1421.000000 -1006.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111528" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21456"/>
     <cge:Term_Ref ObjectID="29964"/>
    <cge:TPSR_Ref TObjectID="21456"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-111526" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1421.000000 -1006.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111526" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21456"/>
     <cge:Term_Ref ObjectID="29964"/>
    <cge:TPSR_Ref TObjectID="21456"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-111531" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1045.000000 -819.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111531" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21462"/>
     <cge:Term_Ref ObjectID="29976"/>
    <cge:TPSR_Ref TObjectID="21462"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-111532" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1045.000000 -819.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111532" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21462"/>
     <cge:Term_Ref ObjectID="29976"/>
    <cge:TPSR_Ref TObjectID="21462"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-111530" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1045.000000 -819.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111530" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21462"/>
     <cge:Term_Ref ObjectID="29976"/>
    <cge:TPSR_Ref TObjectID="21462"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-111548" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1496.000000 -823.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111548" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21468"/>
     <cge:Term_Ref ObjectID="29988"/>
    <cge:TPSR_Ref TObjectID="21468"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-111549" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1496.000000 -823.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111549" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21468"/>
     <cge:Term_Ref ObjectID="29988"/>
    <cge:TPSR_Ref TObjectID="21468"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-111547" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1496.000000 -823.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111547" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21468"/>
     <cge:Term_Ref ObjectID="29988"/>
    <cge:TPSR_Ref TObjectID="21468"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-111540" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1016.000000 -555.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111540" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21477"/>
     <cge:Term_Ref ObjectID="30006"/>
    <cge:TPSR_Ref TObjectID="21477"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-111541" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1016.000000 -555.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111541" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21477"/>
     <cge:Term_Ref ObjectID="30006"/>
    <cge:TPSR_Ref TObjectID="21477"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-111539" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1016.000000 -555.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111539" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21477"/>
     <cge:Term_Ref ObjectID="30006"/>
    <cge:TPSR_Ref TObjectID="21477"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-111557" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1466.000000 -552.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111557" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21479"/>
     <cge:Term_Ref ObjectID="30010"/>
    <cge:TPSR_Ref TObjectID="21479"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-111558" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1466.000000 -552.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111558" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21479"/>
     <cge:Term_Ref ObjectID="30010"/>
    <cge:TPSR_Ref TObjectID="21479"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-111556" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1466.000000 -552.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111556" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21479"/>
     <cge:Term_Ref ObjectID="30010"/>
    <cge:TPSR_Ref TObjectID="21479"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-111580" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 429.000000 -43.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111580" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21481"/>
     <cge:Term_Ref ObjectID="30014"/>
    <cge:TPSR_Ref TObjectID="21481"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-111581" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 429.000000 -43.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111581" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21481"/>
     <cge:Term_Ref ObjectID="30014"/>
    <cge:TPSR_Ref TObjectID="21481"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-111579" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 429.000000 -43.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111579" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21481"/>
     <cge:Term_Ref ObjectID="30014"/>
    <cge:TPSR_Ref TObjectID="21481"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-111588" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 553.000000 -39.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111588" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21483"/>
     <cge:Term_Ref ObjectID="30018"/>
    <cge:TPSR_Ref TObjectID="21483"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-111589" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 553.000000 -39.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111589" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21483"/>
     <cge:Term_Ref ObjectID="30018"/>
    <cge:TPSR_Ref TObjectID="21483"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-111587" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 553.000000 -39.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111587" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21483"/>
     <cge:Term_Ref ObjectID="30018"/>
    <cge:TPSR_Ref TObjectID="21483"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-111600" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 656.000000 -9.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111600" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21485"/>
     <cge:Term_Ref ObjectID="30022"/>
    <cge:TPSR_Ref TObjectID="21485"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-111601" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 656.000000 -9.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111601" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21485"/>
     <cge:Term_Ref ObjectID="30022"/>
    <cge:TPSR_Ref TObjectID="21485"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-111599" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 656.000000 -9.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111599" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21485"/>
     <cge:Term_Ref ObjectID="30022"/>
    <cge:TPSR_Ref TObjectID="21485"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-111604" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 753.000000 -8.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111604" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21487"/>
     <cge:Term_Ref ObjectID="30026"/>
    <cge:TPSR_Ref TObjectID="21487"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-111605" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 753.000000 -8.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111605" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21487"/>
     <cge:Term_Ref ObjectID="30026"/>
    <cge:TPSR_Ref TObjectID="21487"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-111603" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 753.000000 -8.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111603" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21487"/>
     <cge:Term_Ref ObjectID="30026"/>
    <cge:TPSR_Ref TObjectID="21487"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-111608" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 852.000000 -8.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111608" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21489"/>
     <cge:Term_Ref ObjectID="30030"/>
    <cge:TPSR_Ref TObjectID="21489"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-111609" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 852.000000 -8.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111609" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21489"/>
     <cge:Term_Ref ObjectID="30030"/>
    <cge:TPSR_Ref TObjectID="21489"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-111607" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 852.000000 -8.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111607" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21489"/>
     <cge:Term_Ref ObjectID="30030"/>
    <cge:TPSR_Ref TObjectID="21489"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-111612" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 954.000000 -8.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111612" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21491"/>
     <cge:Term_Ref ObjectID="30034"/>
    <cge:TPSR_Ref TObjectID="21491"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-111613" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 954.000000 -8.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111613" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21491"/>
     <cge:Term_Ref ObjectID="30034"/>
    <cge:TPSR_Ref TObjectID="21491"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-111611" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 954.000000 -8.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111611" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21491"/>
     <cge:Term_Ref ObjectID="30034"/>
    <cge:TPSR_Ref TObjectID="21491"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-111616" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1054.000000 -10.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111616" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21493"/>
     <cge:Term_Ref ObjectID="30038"/>
    <cge:TPSR_Ref TObjectID="21493"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-111617" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1054.000000 -10.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111617" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21493"/>
     <cge:Term_Ref ObjectID="30038"/>
    <cge:TPSR_Ref TObjectID="21493"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-111615" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1054.000000 -10.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111615" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21493"/>
     <cge:Term_Ref ObjectID="30038"/>
    <cge:TPSR_Ref TObjectID="21493"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-111592" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1149.000000 -42.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111592" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21495"/>
     <cge:Term_Ref ObjectID="30042"/>
    <cge:TPSR_Ref TObjectID="21495"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-111593" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1149.000000 -42.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111593" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21495"/>
     <cge:Term_Ref ObjectID="30042"/>
    <cge:TPSR_Ref TObjectID="21495"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-111591" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1149.000000 -42.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111591" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21495"/>
     <cge:Term_Ref ObjectID="30042"/>
    <cge:TPSR_Ref TObjectID="21495"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-111640" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1247.000000 -301.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111640" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21511"/>
     <cge:Term_Ref ObjectID="30074"/>
    <cge:TPSR_Ref TObjectID="21511"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-111641" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1247.000000 -301.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111641" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21511"/>
     <cge:Term_Ref ObjectID="30074"/>
    <cge:TPSR_Ref TObjectID="21511"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-111639" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1247.000000 -301.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111639" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21511"/>
     <cge:Term_Ref ObjectID="30074"/>
    <cge:TPSR_Ref TObjectID="21511"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-111596" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1352.000000 -42.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111596" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21497"/>
     <cge:Term_Ref ObjectID="30046"/>
    <cge:TPSR_Ref TObjectID="21497"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-111597" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1352.000000 -42.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111597" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21497"/>
     <cge:Term_Ref ObjectID="30046"/>
    <cge:TPSR_Ref TObjectID="21497"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-111595" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1352.000000 -42.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111595" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21497"/>
     <cge:Term_Ref ObjectID="30046"/>
    <cge:TPSR_Ref TObjectID="21497"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-111620" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1464.000000 -11.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111620" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21499"/>
     <cge:Term_Ref ObjectID="30050"/>
    <cge:TPSR_Ref TObjectID="21499"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-111621" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1464.000000 -11.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111621" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21499"/>
     <cge:Term_Ref ObjectID="30050"/>
    <cge:TPSR_Ref TObjectID="21499"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-111619" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1464.000000 -11.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111619" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21499"/>
     <cge:Term_Ref ObjectID="30050"/>
    <cge:TPSR_Ref TObjectID="21499"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-111624" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1571.000000 -12.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111624" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21501"/>
     <cge:Term_Ref ObjectID="30054"/>
    <cge:TPSR_Ref TObjectID="21501"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-111625" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1571.000000 -12.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111625" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21501"/>
     <cge:Term_Ref ObjectID="30054"/>
    <cge:TPSR_Ref TObjectID="21501"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-111623" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1571.000000 -12.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111623" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21501"/>
     <cge:Term_Ref ObjectID="30054"/>
    <cge:TPSR_Ref TObjectID="21501"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-111628" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1671.000000 -13.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111628" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21503"/>
     <cge:Term_Ref ObjectID="30058"/>
    <cge:TPSR_Ref TObjectID="21503"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-111629" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1671.000000 -13.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111629" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21503"/>
     <cge:Term_Ref ObjectID="30058"/>
    <cge:TPSR_Ref TObjectID="21503"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-111627" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1671.000000 -13.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111627" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21503"/>
     <cge:Term_Ref ObjectID="30058"/>
    <cge:TPSR_Ref TObjectID="21503"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-111632" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1773.000000 -13.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111632" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21505"/>
     <cge:Term_Ref ObjectID="30062"/>
    <cge:TPSR_Ref TObjectID="21505"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-111633" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1773.000000 -13.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111633" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21505"/>
     <cge:Term_Ref ObjectID="30062"/>
    <cge:TPSR_Ref TObjectID="21505"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-111631" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1773.000000 -13.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111631" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21505"/>
     <cge:Term_Ref ObjectID="30062"/>
    <cge:TPSR_Ref TObjectID="21505"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-111636" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1872.000000 -12.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111636" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21507"/>
     <cge:Term_Ref ObjectID="30066"/>
    <cge:TPSR_Ref TObjectID="21507"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-111637" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1872.000000 -12.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111637" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21507"/>
     <cge:Term_Ref ObjectID="30066"/>
    <cge:TPSR_Ref TObjectID="21507"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-111635" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1872.000000 -12.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111635" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21507"/>
     <cge:Term_Ref ObjectID="30066"/>
    <cge:TPSR_Ref TObjectID="21507"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-111584" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1990.000000 -39.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111584" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21509"/>
     <cge:Term_Ref ObjectID="30070"/>
    <cge:TPSR_Ref TObjectID="21509"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-111585" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1990.000000 -39.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111585" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21509"/>
     <cge:Term_Ref ObjectID="30070"/>
    <cge:TPSR_Ref TObjectID="21509"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-111583" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1990.000000 -39.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111583" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21509"/>
     <cge:Term_Ref ObjectID="30070"/>
    <cge:TPSR_Ref TObjectID="21509"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-111570" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 531.000000 -535.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111570" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21447"/>
     <cge:Term_Ref ObjectID="29948"/>
    <cge:TPSR_Ref TObjectID="21447"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-111571" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 531.000000 -535.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111571" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21447"/>
     <cge:Term_Ref ObjectID="29948"/>
    <cge:TPSR_Ref TObjectID="21447"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-111572" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 531.000000 -535.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111572" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21447"/>
     <cge:Term_Ref ObjectID="29948"/>
    <cge:TPSR_Ref TObjectID="21447"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-111573" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 531.000000 -535.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111573" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21447"/>
     <cge:Term_Ref ObjectID="29948"/>
    <cge:TPSR_Ref TObjectID="21447"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-111569" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 531.000000 -535.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111569" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21447"/>
     <cge:Term_Ref ObjectID="29948"/>
    <cge:TPSR_Ref TObjectID="21447"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-111575" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1995.000000 -540.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111575" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21448"/>
     <cge:Term_Ref ObjectID="29949"/>
    <cge:TPSR_Ref TObjectID="21448"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-111576" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1995.000000 -540.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111576" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21448"/>
     <cge:Term_Ref ObjectID="29949"/>
    <cge:TPSR_Ref TObjectID="21448"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-111577" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1995.000000 -540.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111577" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21448"/>
     <cge:Term_Ref ObjectID="29949"/>
    <cge:TPSR_Ref TObjectID="21448"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-111578" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1995.000000 -540.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111578" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21448"/>
     <cge:Term_Ref ObjectID="29949"/>
    <cge:TPSR_Ref TObjectID="21448"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-111574" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1995.000000 -540.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111574" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21448"/>
     <cge:Term_Ref ObjectID="29949"/>
    <cge:TPSR_Ref TObjectID="21448"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-111565" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 620.000000 -959.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111565" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21446"/>
     <cge:Term_Ref ObjectID="29947"/>
    <cge:TPSR_Ref TObjectID="21446"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-111566" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 620.000000 -959.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111566" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21446"/>
     <cge:Term_Ref ObjectID="29947"/>
    <cge:TPSR_Ref TObjectID="21446"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-111567" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 620.000000 -959.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111567" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21446"/>
     <cge:Term_Ref ObjectID="29947"/>
    <cge:TPSR_Ref TObjectID="21446"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-111568" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 620.000000 -959.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111568" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21446"/>
     <cge:Term_Ref ObjectID="29947"/>
    <cge:TPSR_Ref TObjectID="21446"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-111564" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 620.000000 -959.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111564" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21446"/>
     <cge:Term_Ref ObjectID="29947"/>
    <cge:TPSR_Ref TObjectID="21446"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-111555" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1458.000000 -655.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111555" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21516"/>
     <cge:Term_Ref ObjectID="30089"/>
    <cge:TPSR_Ref TObjectID="21516"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-111538" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1005.000000 -655.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="111538" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21515"/>
     <cge:Term_Ref ObjectID="30082"/>
    <cge:TPSR_Ref TObjectID="21515"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="151" x="90" y="-1209"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="151" x="90" y="-1209"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="41" y="-1226"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="41" y="-1226"/></g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_省调直调电厂_风电.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="151" x="90" y="-1209"/></g>
   <g href="cx_索引_接线图_省地共调_风电.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="41" y="-1226"/></g>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="0.423034" x1="836" x2="836" y1="-681" y2="-667"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="836" x2="836" y1="-626" y2="-658"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="856" x2="856" y1="-626" y2="-653"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="856" x2="856" y1="-681" y2="-657"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="816" x2="856" y1="-626" y2="-626"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="0.423034" x1="1282" x2="1282" y1="-681" y2="-667"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="1282" x2="1282" y1="-626" y2="-658"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="1302" x2="1302" y1="-626" y2="-653"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="1302" x2="1302" y1="-681" y2="-657"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="1262" x2="1302" y1="-626" y2="-626"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.510204" x1="558" x2="566" y1="-132" y2="-124"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.510204" x1="566" x2="574" y1="-124" y2="-132"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.510204" x1="566" x2="566" y1="-116" y2="-124"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="2" x1="1821" x2="1906" y1="-853" y2="-853"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="2" x1="1983" x2="2089" y1="-853" y2="-853"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="566" x2="531" y1="-123" y2="-123"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="531" x2="531" y1="-122" y2="-85"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="567" x2="567" y1="-113" y2="-89"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="559" x2="574" y1="-88" y2="-88"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="565" x2="565" y1="-79" y2="-54"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="525" x2="538" y1="-84" y2="-84"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="527" x2="536" y1="-81" y2="-81"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="528" x2="535" y1="-77" y2="-77"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="28" y="-1230"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="25" stroke="rgb(255,255,255)" stroke-width="1" width="12" x="830" y="-667"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="25" stroke="rgb(255,255,255)" stroke-width="1" width="12" x="1276" y="-667"/>
  </g><g id="Generator_Layer">
   <g DF8003:Layer="PUBLIC" id="SM-CX_AP.P1">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 666.000000 -85.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43361" ObjectName="SM-CX_AP.P1"/>
    <cge:TPSR_Ref TObjectID="43361"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_AP.P2">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 761.000000 -85.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43362" ObjectName="SM-CX_AP.P2"/>
    <cge:TPSR_Ref TObjectID="43362"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_AP.P3">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 856.000000 -85.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43363" ObjectName="SM-CX_AP.P3"/>
    <cge:TPSR_Ref TObjectID="43363"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_AP.P4">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 962.000000 -85.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43364" ObjectName="SM-CX_AP.P4"/>
    <cge:TPSR_Ref TObjectID="43364"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_AP.P5">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1057.000000 -82.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43365" ObjectName="SM-CX_AP.P5"/>
    <cge:TPSR_Ref TObjectID="43365"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_AP.P6">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1466.000000 -83.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43366" ObjectName="SM-CX_AP.P6"/>
    <cge:TPSR_Ref TObjectID="43366"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_AP.P7">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1571.000000 -82.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43367" ObjectName="SM-CX_AP.P7"/>
    <cge:TPSR_Ref TObjectID="43367"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_AP.P8">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1668.000000 -82.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43368" ObjectName="SM-CX_AP.P8"/>
    <cge:TPSR_Ref TObjectID="43368"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_AP.P9">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1771.000000 -82.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43369" ObjectName="SM-CX_AP.P9"/>
    <cge:TPSR_Ref TObjectID="43369"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_AP.P10">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1873.000000 -81.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43370" ObjectName="SM-CX_AP.P10"/>
    <cge:TPSR_Ref TObjectID="43370"/></metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2b2d990" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 735.000000 -1071.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2939570" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 735.000000 -1003.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24366b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 734.000000 -943.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_255bbd0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1021.000000 -912.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22ccc70" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1021.000000 -1002.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2572d90" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 955.565184 -817.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_257fbc0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 955.565184 -769.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25510b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 955.565184 -706.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_254fe10" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1402.000000 -818.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c46390" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1402.000000 -770.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25bca40" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1402.000000 -712.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c9bc70" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1239.000000 -1071.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c5c300" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1239.000000 -1003.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c4c2d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1238.000000 -943.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29385f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 810.000000 -608.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c52390" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1256.000000 -608.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c9c180" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 694.477768 -254.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c8d060" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 789.477768 -255.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c6b550" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 884.477768 -254.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2381cb0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 990.477768 -254.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c94fb0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 482.477768 -250.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b20a50" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1494.477768 -252.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c979f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1599.477768 -251.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b589d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1696.477768 -251.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c7bad0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1799.477768 -251.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2940940" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 588.477768 -252.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d44110" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1085.477768 -251.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2cd6340" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1901.477768 -250.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d03890" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2023.477768 -247.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d1b3a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1181.477768 -252.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d227a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1383.477768 -253.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="21446" cx="1095" cy="-879" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21446" cx="1350" cy="-879" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21446" cx="806" cy="-879" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21446" cx="904" cy="-879" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21447" cx="904" cy="-452" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21447" cx="625" cy="-452" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21447" cx="671" cy="-452" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21447" cx="766" cy="-452" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21447" cx="861" cy="-452" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21447" cx="967" cy="-452" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21447" cx="460" cy="-452" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21447" cx="565" cy="-452" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21447" cx="1062" cy="-452" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21447" cx="1157" cy="-452" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21447" cx="1230" cy="-452" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21448" cx="1350" cy="-453" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21448" cx="1840" cy="-453" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21448" cx="1471" cy="-453" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21448" cx="1576" cy="-453" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21448" cx="1673" cy="-453" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21448" cx="1776" cy="-453" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21448" cx="1359" cy="-453" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21448" cx="1296" cy="-453" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21448" cx="1878" cy="-453" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21448" cx="2001" cy="-453" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ca2270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,17)">危险点说明</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ca2270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ca2270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ca2270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ca2270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ca2270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ca2270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ca2270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ca2270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ca2270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ca2270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ca2270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ca2270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ca2270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ca2270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ca2270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ca2270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ca2270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,374)">联系方式</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2589ac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 36.000000 -1080.000000) translate(0,17)">频率</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2589ac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 36.000000 -1080.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2589ac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 36.000000 -1080.000000) translate(0,59)">全站有功</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2589ac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 36.000000 -1080.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2589ac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 36.000000 -1080.000000) translate(0,101)">风机出力</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2589ac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 36.000000 -1080.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2589ac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 36.000000 -1080.000000) translate(0,143)">全站无功</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2589ac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 36.000000 -1080.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2589ac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 36.000000 -1080.000000) translate(0,185)">并网联络点的电压和交换功率</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_259c820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 120.000000 -1198.500000) translate(0,16)">阿普风电场</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ac0960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 952.000000 -684.000000) translate(0,15)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b968f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 748.000000 -975.000000) translate(0,12)">28117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2382f10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 812.000000 -920.000000) translate(0,12)">2811</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" graphid="g_28c92b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1041.000000 -1112.500000) translate(0,16)">220kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" graphid="g_28c92b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1041.000000 -1112.500000) translate(0,35)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2903110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1036.000000 -943.000000) translate(0,12)">29010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22cc8c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1102.000000 -971.000000) translate(0,12)">2901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c79210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1038.000000 -1034.000000) translate(0,12)">29017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29816e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 817.000000 -992.000000) translate(0,12)">281</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c92400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 751.000000 -1034.000000) translate(0,12)">28160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c6bca0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 751.000000 -1099.000000) translate(0,12)">28167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c62080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 813.000000 -1052.000000) translate(0,12)">2816</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" graphid="g_2ac17a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 776.000000 -1286.500000) translate(0,16)">台阿线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2af5180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 863.000000 -806.000000) translate(0,12)">201</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25bbb50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 859.000000 -856.000000) translate(0,12)">2011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25757f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 860.000000 -750.000000) translate(0,12)">2016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c96c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 925.000000 -847.000000) translate(0,12)">20117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b2ce10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 925.000000 -798.000000) translate(0,12)">20160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b0f3f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 925.000000 -740.000000) translate(0,12)">20167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c42ae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1306.000000 -856.000000) translate(0,12)">2021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2978160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1310.000000 -806.000000) translate(0,12)">202</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ac1930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1306.000000 -747.000000) translate(0,12)">2026</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_290bde0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1370.000000 -740.000000) translate(0,12)">20267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2551480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1370.000000 -798.000000) translate(0,12)">20260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29433f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1370.000000 -847.000000) translate(0,12)">20217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2582640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1216.000000 -661.000000) translate(0,12)">2020</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" graphid="g_2382380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 371.000000 -446.000000) translate(0,16)">35kVⅠ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" graphid="g_29470b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2023.000000 -448.000000) translate(0,16)">35kVⅡ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b0d620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 636.000000 -497.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c556a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 750.000000 -168.000000) translate(0,12)">阿</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c556a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 750.000000 -168.000000) translate(0,27)">普</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c556a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 750.000000 -168.000000) translate(0,42)">Ⅱ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c556a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 750.000000 -168.000000) translate(0,57)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c556a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 750.000000 -168.000000) translate(0,72)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_2c4f090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 721.000000 -63.000000) translate(0,12)">(18、19、44</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_2c4f090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 721.000000 -63.000000) translate(0,26)">~46、58、61、</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_2c4f090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 721.000000 -63.000000) translate(0,40)">63号发电机)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2977b00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 655.000000 -168.000000) translate(0,12)">阿</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2977b00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 655.000000 -168.000000) translate(0,27)">普</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2977b00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 655.000000 -168.000000) translate(0,42)">Ⅰ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2977b00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 655.000000 -168.000000) translate(0,57)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2977b00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 655.000000 -168.000000) translate(0,72)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b29b20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 914.257519 -533.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cce400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1361.257519 -531.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b4d800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1793.000000 -1160.000000) translate(0,12)">35kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b16260" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1788.500000 -849.000000) translate(0,12)">0.4kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29460f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1990.500000 -849.000000) translate(0,12)">0.4kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2554a20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 698.000000 -800.000000) translate(0,12)">1号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2554a20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 698.000000 -800.000000) translate(0,27)">SZ11-100000-230/37GY</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2554a20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 698.000000 -800.000000) translate(0,42)">230±8×1.25%/35kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2554a20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 698.000000 -800.000000) translate(0,57)">100000kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2554a20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 698.000000 -800.000000) translate(0,72)">YN，d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2554a20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 698.000000 -800.000000) translate(0,87)">Ud%=13.47</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ca4a20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1851.000000 -499.000000) translate(0,12)">3902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" graphid="g_2c59bd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 568.000000 -659.500000) translate(0,16)">35kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" graphid="g_2c59bd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 568.000000 -659.500000) translate(0,35)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" graphid="g_28e1c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1790.000000 -660.500000) translate(0,16)">35kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" graphid="g_28e1c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1790.000000 -660.500000) translate(0,35)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c97440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1421.000000 -655.000000) translate(0,12)">档位:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b0be60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1255.000000 -973.000000) translate(0,12)">28217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b0d020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1314.000000 -920.000000) translate(0,12)">2821</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c5bdd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1320.000000 -992.000000) translate(0,12)">282</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25598f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1255.000000 -1034.000000) translate(0,12)">28260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c8f0b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1255.000000 -1099.000000) translate(0,12)">28267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c8f2f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1317.000000 -1052.000000) translate(0,12)">2826</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" graphid="g_2c55a00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1282.000000 -1282.500000) translate(0,16)">桃阿线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b0d800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 966.000000 -655.000000) translate(0,12)">档位:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c4bc90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 770.000000 -661.000000) translate(0,12)">2010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2af6d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1402.000000 -684.000000) translate(0,15)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22962c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 704.257519 -306.000000) translate(0,12)">38367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29473d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 683.257519 -393.000000) translate(0,12)">383</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_255fd00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 799.257519 -306.000000) translate(0,12)">38467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c58560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 778.257519 -393.000000) translate(0,12)">384</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_2c5a180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 626.000000 -60.000000) translate(0,12)">(50~57、74、</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_2c5a180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 626.000000 -60.000000) translate(0,26)">75号发电机)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ac3060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 845.000000 -168.000000) translate(0,12)">阿</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ac3060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 845.000000 -168.000000) translate(0,27)">普</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ac3060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 845.000000 -168.000000) translate(0,42)">Ⅲ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ac3060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 845.000000 -168.000000) translate(0,57)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ac3060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 845.000000 -168.000000) translate(0,72)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_254db40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 815.000000 -62.000000) translate(0,12)">(8、12、14、16</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_254db40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 815.000000 -62.000000) translate(0,26)">17、20、29、41</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_254db40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 815.000000 -62.000000) translate(0,40)">42、60号发电机)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c99e30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 894.257519 -306.000000) translate(0,12)">38567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_255dad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 873.257519 -393.000000) translate(0,12)">385</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2af2320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 951.000000 -168.000000) translate(0,12)">阿</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2af2320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 951.000000 -168.000000) translate(0,27)">普</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2af2320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 951.000000 -168.000000) translate(0,42)">Ⅳ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2af2320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 951.000000 -168.000000) translate(0,57)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2af2320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 951.000000 -168.000000) translate(0,72)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_293ef10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1000.257519 -306.000000) translate(0,12)">38667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b31040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 979.257519 -393.000000) translate(0,12)">386</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_2c9e690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 930.000000 -59.000000) translate(0,12)">(1~6、9</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_2c9e690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 930.000000 -59.000000) translate(0,26)">13、15</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_2c9e690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 930.000000 -59.000000) translate(0,40)">21号发电机)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b4cb90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 496.257519 -303.000000) translate(0,12)">38167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b4d080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 472.257519 -390.000000) translate(0,12)">381</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b49850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 444.000000 -143.000000) translate(0,12)">35kV1号小</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b49850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 444.000000 -143.000000) translate(0,27)">电阻接地</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b49850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 444.000000 -143.000000) translate(0,42)">成套装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ccff70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1452.000000 -169.000000) translate(0,12)">阿</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ccff70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1452.000000 -169.000000) translate(0,27)">普</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ccff70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1452.000000 -169.000000) translate(0,42)">Ⅵ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ccff70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1452.000000 -169.000000) translate(0,57)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ccff70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1452.000000 -169.000000) translate(0,72)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_2cd0790" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1543.000000 -66.000000) translate(0,12)">(35~39</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_2cd0790" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1543.000000 -66.000000) translate(0,26)">127~130</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_2cd0790" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1543.000000 -66.000000) translate(0,40)">135号发电机)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2553f50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1504.257519 -304.000000) translate(0,12)">39267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c65490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1483.257519 -391.000000) translate(0,12)">392</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b105d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1607.257519 -303.000000) translate(0,12)">39367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b10d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1588.257519 -390.000000) translate(0,12)">393</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_2b4c4c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1430.000000 -66.000000) translate(0,12)">(24、27、28</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_2b4c4c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1430.000000 -66.000000) translate(0,26)">32~34、47~49</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_2b4c4c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1430.000000 -66.000000) translate(0,40)">62号发电机)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b2e2d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1555.000000 -170.000000) translate(0,12)">阿</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b2e2d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1555.000000 -170.000000) translate(0,27)">普</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b2e2d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1555.000000 -170.000000) translate(0,42)">Ⅶ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b2e2d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1555.000000 -170.000000) translate(0,57)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b2e2d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1555.000000 -170.000000) translate(0,72)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_2b2e7c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1643.000000 -67.000000) translate(0,12)">(131~134</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_2b2e7c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1643.000000 -67.000000) translate(0,26)">136~140</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_2b2e7c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1643.000000 -67.000000) translate(0,40)">号发电机)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b59660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1706.257519 -303.000000) translate(0,12)">39467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b18830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1685.257519 -390.000000) translate(0,12)">394</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c9dca0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1653.000000 -172.000000) translate(0,12)">阿</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c9dca0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1653.000000 -172.000000) translate(0,27)">普</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c9dca0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1653.000000 -172.000000) translate(0,42)">Ⅷ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c9dca0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1653.000000 -172.000000) translate(0,57)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c9dca0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1653.000000 -172.000000) translate(0,72)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2574560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1809.257519 -303.000000) translate(0,12)">39567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2574c40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1787.257519 -390.000000) translate(0,12)">395</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_2c60040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1744.000000 -66.000000) translate(0,12)">(141~150</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_2c60040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1744.000000 -66.000000) translate(0,26)">号发电机)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2aec8f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 603.257519 -304.000000) translate(0,12)">38267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2aecfd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 509.000000 -70.000000) translate(0,12)">35kV1号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2aecfd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 509.000000 -70.000000) translate(0,27)">站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2aed4a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 584.000000 -142.000000) translate(0,12)">400kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c50de0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 577.257519 -391.000000) translate(0,12)">382</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d9d380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1242.257519 -396.000000) translate(0,12)">312</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cc6fe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1303.257519 -404.000000) translate(0,12)">3122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2da3df0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2004.000000 -1160.000000) translate(0,12)">10kV芹菜沟支线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2da4fd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2067.500000 -1113.000000) translate(0,12)">10kV备用站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2da4fd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2067.500000 -1113.000000) translate(0,27)">（施工变）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d3a9a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1754.500000 -1081.000000) translate(0,12)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d44e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1095.257519 -303.000000) translate(0,12)">38767</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d454e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1074.257519 -390.000000) translate(0,12)">387</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cf1e90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1046.000000 -165.000000) translate(0,12)">阿</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cf1e90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1046.000000 -165.000000) translate(0,27)">普</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cf1e90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1046.000000 -165.000000) translate(0,42)">Ⅴ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cf1e90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1046.000000 -165.000000) translate(0,57)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cf1e90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1046.000000 -165.000000) translate(0,72)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_2cf24d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1038.000000 -59.000000) translate(0,12)">(7、10、11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_2cf24d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1038.000000 -59.000000) translate(0,26)">22、23、25</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_2cf24d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1038.000000 -59.000000) translate(0,40)">30号发电机)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cf3280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1130.000000 -84.000000) translate(0,12)">1号动态无功</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cf3280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1130.000000 -84.000000) translate(0,27)">补偿装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cf4520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1136.000000 -56.000000) translate(0,12)">±36MVar</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cf4f70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1169.257519 -390.000000) translate(0,12)">388</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cfcf80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1332.000000 -85.000000) translate(0,12)">2号动态无功</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cfcf80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1332.000000 -85.000000) translate(0,27)">补偿装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cfd1c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1338.000000 -57.000000) translate(0,12)">±36MVar</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cfdbd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1371.257519 -391.000000) translate(0,12)">391</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cd5b10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1756.000000 -172.000000) translate(0,12)">阿</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cd5b10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1756.000000 -172.000000) translate(0,27)">普</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cd5b10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1756.000000 -172.000000) translate(0,42)">Ⅸ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cd5b10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1756.000000 -172.000000) translate(0,57)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cd5b10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1756.000000 -172.000000) translate(0,72)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cd6dc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1912.257519 -302.000000) translate(0,12)">39667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cd75e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1889.257519 -389.000000) translate(0,12)">396</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_2cdd5d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1857.000000 -65.000000) translate(0,12)">(64~73</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_2cdd5d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1857.000000 -65.000000) translate(0,26)">号发电机)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cddc00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1858.000000 -171.000000) translate(0,12)">阿</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cddc00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1858.000000 -171.000000) translate(0,27)">普</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cddc00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1858.000000 -171.000000) translate(0,42)">Ⅹ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cddc00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1858.000000 -171.000000) translate(0,57)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cddc00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1858.000000 -171.000000) translate(0,72)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d04520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2013.257519 -387.000000) translate(0,12)">397</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d092c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1985.000000 -140.000000) translate(0,12)">35kV2号小</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d092c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1985.000000 -140.000000) translate(0,27)">电阻接地</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d092c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1985.000000 -140.000000) translate(0,42)">成套装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d14ab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2037.257519 -298.000000) translate(0,12)">39767</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d150e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 405.000000 -203.000000) translate(0,12)">800kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d15320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1942.000000 -199.000000) translate(0,12)">800kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d15750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1765.000000 -1063.000000) translate(0,12)">400kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d15a00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2084.000000 -1062.000000) translate(0,12)">400kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" graphid="g_2d15c40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 49.000000 -1259.500000) translate(0,16)">风力发电机单机：2000kW,0.69kV,50Hz</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d1f7a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1193.257519 -303.000000) translate(0,12)">38867</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d24a70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1395.257519 -304.000000) translate(0,12)">39167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35bba60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1151.000000 -800.000000) translate(0,12)">2号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35bba60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1151.000000 -800.000000) translate(0,27)">SZ11-100000-230/37GY</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35bba60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1151.000000 -800.000000) translate(0,42)">230±8×1.25%/35kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35bba60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1151.000000 -800.000000) translate(0,57)">100000kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35bba60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1151.000000 -800.000000) translate(0,72)">YN，d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35bba60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1151.000000 -800.000000) translate(0,87)">Ud%=13.46</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" graphid="g_35bbd20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 144.000000 -264.000000) translate(0,16)">4770</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" graphid="g_35bf2a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 144.000000 -230.000000) translate(0,16)">4837862</text>
  </g><g id="CircleFilled_Layer">
   <circle DF8003:Layer="PUBLIC" cx="1838" cy="-853" fill="rgb(0,255,0)" fillStyle="1" r="2" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="1838" cy="-992" fill="rgb(0,255,0)" fillStyle="1" r="2" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="1998" cy="-853" fill="rgb(0,255,0)" fillStyle="1" r="2" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="2051" cy="-853" fill="rgb(0,255,0)" fillStyle="1" r="2" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="1893" cy="-853" fill="rgb(0,255,0)" fillStyle="1" r="2" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="2051" cy="-992" fill="rgb(0,255,0)" fillStyle="1" r="2" stroke="rgb(60,120,255)" stroke-width="1"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-112351">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 797.000000 -893.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21451" ObjectName="SW-CX_AP.CX_AP_2811SW"/>
     <cge:Meas_Ref ObjectId="112351"/>
    <cge:TPSR_Ref TObjectID="21451"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-112352">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 797.000000 -1022.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21452" ObjectName="SW-CX_AP.CX_AP_2816SW"/>
     <cge:Meas_Ref ObjectId="112352"/>
    <cge:TPSR_Ref TObjectID="21452"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-112354">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 755.000000 -1004.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21454" ObjectName="SW-CX_AP.CX_AP_28160SW"/>
     <cge:Meas_Ref ObjectId="112354"/>
    <cge:TPSR_Ref TObjectID="21454"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-112355">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 755.000000 -1072.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21455" ObjectName="SW-CX_AP.CX_AP_28167SW"/>
     <cge:Meas_Ref ObjectId="112355"/>
    <cge:TPSR_Ref TObjectID="21455"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-112374">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1086.000000 -941.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21474" ObjectName="SW-CX_AP.CX_AP_2901SW"/>
     <cge:Meas_Ref ObjectId="112374"/>
    <cge:TPSR_Ref TObjectID="21474"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-112376">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1042.000000 -1003.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21476" ObjectName="SW-CX_AP.CX_AP_29017SW"/>
     <cge:Meas_Ref ObjectId="112376"/>
    <cge:TPSR_Ref TObjectID="21476"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-112363">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 894.565184 -827.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21463" ObjectName="SW-CX_AP.CX_AP_2011SW"/>
     <cge:Meas_Ref ObjectId="112363"/>
    <cge:TPSR_Ref TObjectID="21463"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-112364">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 894.565184 -723.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21464" ObjectName="SW-CX_AP.CX_AP_2016SW"/>
     <cge:Meas_Ref ObjectId="112364"/>
    <cge:TPSR_Ref TObjectID="21464"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-112365">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 910.565184 -818.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21465" ObjectName="SW-CX_AP.CX_AP_20117SW"/>
     <cge:Meas_Ref ObjectId="112365"/>
    <cge:TPSR_Ref TObjectID="21465"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-112366">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 910.565184 -770.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21466" ObjectName="SW-CX_AP.CX_AP_20160SW"/>
     <cge:Meas_Ref ObjectId="112366"/>
    <cge:TPSR_Ref TObjectID="21466"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-112367">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 910.565184 -707.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21467" ObjectName="SW-CX_AP.CX_AP_20167SW"/>
     <cge:Meas_Ref ObjectId="112367"/>
    <cge:TPSR_Ref TObjectID="21467"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-112369">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1341.000000 -828.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21469" ObjectName="SW-CX_AP.CX_AP_2021SW"/>
     <cge:Meas_Ref ObjectId="112369"/>
    <cge:TPSR_Ref TObjectID="21469"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-112370">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1341.000000 -724.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21470" ObjectName="SW-CX_AP.CX_AP_2026SW"/>
     <cge:Meas_Ref ObjectId="112370"/>
    <cge:TPSR_Ref TObjectID="21470"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-112371">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1357.000000 -819.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21471" ObjectName="SW-CX_AP.CX_AP_20217SW"/>
     <cge:Meas_Ref ObjectId="112371"/>
    <cge:TPSR_Ref TObjectID="21471"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-112372">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1357.000000 -771.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21472" ObjectName="SW-CX_AP.CX_AP_20260SW"/>
     <cge:Meas_Ref ObjectId="112372"/>
    <cge:TPSR_Ref TObjectID="21472"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-112373">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1357.000000 -713.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21473" ObjectName="SW-CX_AP.CX_AP_20267SW"/>
     <cge:Meas_Ref ObjectId="112373"/>
    <cge:TPSR_Ref TObjectID="21473"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-112357">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1302.000000 -893.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21457" ObjectName="SW-CX_AP.CX_AP_2821SW"/>
     <cge:Meas_Ref ObjectId="112357"/>
    <cge:TPSR_Ref TObjectID="21457"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-112358">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1302.000000 -1022.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21458" ObjectName="SW-CX_AP.CX_AP_2826SW"/>
     <cge:Meas_Ref ObjectId="112358"/>
    <cge:TPSR_Ref TObjectID="21458"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-112359">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1259.000000 -944.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21459" ObjectName="SW-CX_AP.CX_AP_28217SW"/>
     <cge:Meas_Ref ObjectId="112359"/>
    <cge:TPSR_Ref TObjectID="21459"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-112360">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1259.000000 -1004.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21460" ObjectName="SW-CX_AP.CX_AP_28260SW"/>
     <cge:Meas_Ref ObjectId="112360"/>
    <cge:TPSR_Ref TObjectID="21460"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-112361">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1259.000000 -1072.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21461" ObjectName="SW-CX_AP.CX_AP_28267SW"/>
     <cge:Meas_Ref ObjectId="112361"/>
    <cge:TPSR_Ref TObjectID="21461"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-112353">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 755.000000 -944.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21453" ObjectName="SW-CX_AP.CX_AP_28117SW"/>
     <cge:Meas_Ref ObjectId="112353"/>
    <cge:TPSR_Ref TObjectID="21453"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-112378">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 807.000000 -633.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21478" ObjectName="SW-CX_AP.CX_AP_2010SW"/>
     <cge:Meas_Ref ObjectId="112378"/>
    <cge:TPSR_Ref TObjectID="21478"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-112380">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1253.000000 -633.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21480" ObjectName="SW-CX_AP.CX_AP_2020SW"/>
     <cge:Meas_Ref ObjectId="112380"/>
    <cge:TPSR_Ref TObjectID="21480"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-112413">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 615.000000 -474.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21513" ObjectName="SW-CX_AP.CX_AP_3901SW"/>
     <cge:Meas_Ref ObjectId="112413"/>
    <cge:TPSR_Ref TObjectID="21513"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-112414">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1830.000000 -475.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21514" ObjectName="SW-CX_AP.CX_AP_3902SW"/>
     <cge:Meas_Ref ObjectId="112414"/>
    <cge:TPSR_Ref TObjectID="21514"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-112386">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 691.477768 -277.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21486" ObjectName="SW-CX_AP.CX_AP_38367SW"/>
     <cge:Meas_Ref ObjectId="112386"/>
    <cge:TPSR_Ref TObjectID="21486"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-112388">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 786.477768 -278.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21488" ObjectName="SW-CX_AP.CX_AP_38467SW"/>
     <cge:Meas_Ref ObjectId="112388"/>
    <cge:TPSR_Ref TObjectID="21488"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-112390">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 881.477768 -277.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21490" ObjectName="SW-CX_AP.CX_AP_38567SW"/>
     <cge:Meas_Ref ObjectId="112390"/>
    <cge:TPSR_Ref TObjectID="21490"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-112392">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 987.477768 -277.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21492" ObjectName="SW-CX_AP.CX_AP_38667SW"/>
     <cge:Meas_Ref ObjectId="112392"/>
    <cge:TPSR_Ref TObjectID="21492"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-112382">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 480.477768 -276.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21482" ObjectName="SW-CX_AP.CX_AP_38167SW"/>
     <cge:Meas_Ref ObjectId="112382"/>
    <cge:TPSR_Ref TObjectID="21482"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-112400">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1491.477768 -275.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21500" ObjectName="SW-CX_AP.CX_AP_39267SW"/>
     <cge:Meas_Ref ObjectId="112400"/>
    <cge:TPSR_Ref TObjectID="21500"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-112402">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1596.477768 -274.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21502" ObjectName="SW-CX_AP.CX_AP_39367SW"/>
     <cge:Meas_Ref ObjectId="112402"/>
    <cge:TPSR_Ref TObjectID="21502"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-112404">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1693.477768 -274.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21504" ObjectName="SW-CX_AP.CX_AP_39467SW"/>
     <cge:Meas_Ref ObjectId="112404"/>
    <cge:TPSR_Ref TObjectID="21504"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-112406">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1796.477768 -274.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21506" ObjectName="SW-CX_AP.CX_AP_39567SW"/>
     <cge:Meas_Ref ObjectId="112406"/>
    <cge:TPSR_Ref TObjectID="21506"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-112384">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 585.477768 -275.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21484" ObjectName="SW-CX_AP.CX_AP_38267SW"/>
     <cge:Meas_Ref ObjectId="112384"/>
    <cge:TPSR_Ref TObjectID="21484"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-112412">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1286.000000 -361.000000)" xlink:href="#switch2:shape18_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21512" ObjectName="SW-CX_AP.CX_AP_3122SW"/>
     <cge:Meas_Ref ObjectId="112412"/>
    <cge:TPSR_Ref TObjectID="21512"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-112394">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1082.477768 -274.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21494" ObjectName="SW-CX_AP.CX_AP_38767SW"/>
     <cge:Meas_Ref ObjectId="112394"/>
    <cge:TPSR_Ref TObjectID="21494"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-112408">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1898.477768 -273.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21508" ObjectName="SW-CX_AP.CX_AP_39667SW"/>
     <cge:Meas_Ref ObjectId="112408"/>
    <cge:TPSR_Ref TObjectID="21508"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-112410">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2021.477768 -273.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21510" ObjectName="SW-CX_AP.CX_AP_39767SW"/>
     <cge:Meas_Ref ObjectId="112410"/>
    <cge:TPSR_Ref TObjectID="21510"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-112375">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1043.000000 -913.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21475" ObjectName="SW-CX_AP.CX_AP_29010SW"/>
     <cge:Meas_Ref ObjectId="112375"/>
    <cge:TPSR_Ref TObjectID="21475"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-112396">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1178.477768 -275.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21496" ObjectName="SW-CX_AP.CX_AP_38867SW"/>
     <cge:Meas_Ref ObjectId="112396"/>
    <cge:TPSR_Ref TObjectID="21496"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-112398">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1380.477768 -276.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21498" ObjectName="SW-CX_AP.CX_AP_39167SW"/>
     <cge:Meas_Ref ObjectId="112398"/>
    <cge:TPSR_Ref TObjectID="21498"/></metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-220KV" id="g_2abf4b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="760,-949 752,-949 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21453@0" ObjectIDZND0="g_24366b0@0" Pin0InfoVect0LinkObjId="g_24366b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112353_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="760,-949 752,-949 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_255cf30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="760,-1009 753,-1009 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21454@0" ObjectIDZND0="g_2939570@0" Pin0InfoVect0LinkObjId="g_2939570_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112354_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="760,-1009 753,-1009 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_295c1c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="806,-1077 796,-1077 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" ObjectIDND0="21452@x" ObjectIDND1="g_25bc5b0@0" ObjectIDND2="25324@1" ObjectIDZND0="21455@1" Pin0InfoVect0LinkObjId="SW-112355_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-112352_0" Pin1InfoVect1LinkObjId="g_25bc5b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="806,-1077 796,-1077 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2b54a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="760,-1077 753,-1077 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21455@0" ObjectIDZND0="g_2b2d990@0" Pin0InfoVect0LinkObjId="g_2b2d990_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112355_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="760,-1077 753,-1077 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_295c8a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="806,-1077 806,-1063 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" ObjectIDND0="21455@x" ObjectIDND1="g_25bc5b0@0" ObjectIDND2="25324@1" ObjectIDZND0="21452@1" Pin0InfoVect0LinkObjId="SW-112352_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-112355_0" Pin1InfoVect1LinkObjId="g_25bc5b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="806,-1077 806,-1063 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2b515e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="806,-1009 796,-1009 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="21452@x" ObjectIDND1="21450@x" ObjectIDZND0="21454@1" Pin0InfoVect0LinkObjId="SW-112354_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-112352_0" Pin1InfoVect1LinkObjId="SW-112350_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="806,-1009 796,-1009 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2b52f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="778,-1163 806,-1163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2d3e840@0" ObjectIDZND0="21452@x" ObjectIDZND1="21455@x" ObjectIDZND2="g_25bc5b0@0" Pin0InfoVect0LinkObjId="SW-112352_0" Pin0InfoVect1LinkObjId="SW-112355_0" Pin0InfoVect2LinkObjId="g_25bc5b0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d3e840_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="778,-1163 806,-1163 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2b15ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="836,-1170 836,-1181 806,-1181 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="g_25bc5b0@0" ObjectIDZND0="21452@x" ObjectIDZND1="21455@x" ObjectIDZND2="g_2d3e840@0" Pin0InfoVect0LinkObjId="SW-112352_0" Pin0InfoVect1LinkObjId="SW-112355_0" Pin0InfoVect2LinkObjId="g_2d3e840_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25bc5b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="836,-1170 836,-1181 806,-1181 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_255b730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="806,-1163 806,-1077 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="powerLine" BeginDevType2="voltageTransformer" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_25bc5b0@0" ObjectIDND1="25324@1" ObjectIDND2="g_2d3e840@0" ObjectIDZND0="21452@x" ObjectIDZND1="21455@x" Pin0InfoVect0LinkObjId="SW-112352_0" Pin0InfoVect1LinkObjId="SW-112355_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_25bc5b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="g_2d3e840_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="806,-1163 806,-1077 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2af49c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1095,-918 1095,-879 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="21474@x" ObjectIDND1="21475@x" ObjectIDZND0="21446@0" Pin0InfoVect0LinkObjId="g_29496c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-112374_0" Pin1InfoVect1LinkObjId="SW-112375_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1095,-918 1095,-879 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29496c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1095,-946 1095,-918 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" EndDevType1="switch" ObjectIDND0="21474@0" ObjectIDZND0="21446@0" ObjectIDZND1="21475@x" Pin0InfoVect0LinkObjId="g_2af49c0_0" Pin0InfoVect1LinkObjId="SW-112375_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112374_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1095,-946 1095,-918 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2b241c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1047,-1008 1039,-1008 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21476@0" ObjectIDZND0="g_22ccc70@0" Pin0InfoVect0LinkObjId="g_22ccc70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112376_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1047,-1008 1039,-1008 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29430c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="904,-879 904,-868 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="21446@0" ObjectIDZND0="21463@1" Pin0InfoVect0LinkObjId="SW-112363_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2af49c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="904,-879 904,-868 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_245fdd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="960,-823 952,-823 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2572d90@0" ObjectIDZND0="21465@1" Pin0InfoVect0LinkObjId="SW-112365_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2572d90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="960,-823 952,-823 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_226c040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="960,-775 952,-775 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_257fbc0@0" ObjectIDZND0="21466@1" Pin0InfoVect0LinkObjId="SW-112366_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_257fbc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="960,-775 952,-775 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_28f45e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="916,-775 904,-775 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="21466@0" ObjectIDZND0="21464@x" ObjectIDZND1="21462@x" Pin0InfoVect0LinkObjId="SW-112364_0" Pin0InfoVect1LinkObjId="SW-112362_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112366_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="916,-775 904,-775 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_22c3d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="960,-712 952,-712 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_25510b0@0" ObjectIDZND0="21467@1" Pin0InfoVect0LinkObjId="SW-112367_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25510b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="960,-712 952,-712 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_22d5ae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="904,-832 904,-823 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="21463@0" ObjectIDZND0="21462@x" ObjectIDZND1="21465@x" Pin0InfoVect0LinkObjId="SW-112362_0" Pin0InfoVect1LinkObjId="SW-112365_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112363_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="904,-832 904,-823 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2960870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="904,-823 916,-823 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="21462@x" ObjectIDND1="21463@x" ObjectIDZND0="21465@0" Pin0InfoVect0LinkObjId="SW-112365_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-112362_0" Pin1InfoVect1LinkObjId="SW-112363_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="904,-823 916,-823 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_22dc520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="904,-823 904,-812 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="21465@x" ObjectIDND1="21463@x" ObjectIDZND0="21462@1" Pin0InfoVect0LinkObjId="SW-112362_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-112365_0" Pin1InfoVect1LinkObjId="SW-112363_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="904,-823 904,-812 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2c458a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="904,-785 904,-775 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="21462@0" ObjectIDZND0="21464@x" ObjectIDZND1="21466@x" Pin0InfoVect0LinkObjId="SW-112364_0" Pin0InfoVect1LinkObjId="SW-112366_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112362_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="904,-785 904,-775 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29787a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="904,-764 904,-775 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="21464@1" ObjectIDZND0="21462@x" ObjectIDZND1="21466@x" Pin0InfoVect0LinkObjId="SW-112362_0" Pin0InfoVect1LinkObjId="SW-112366_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112364_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="904,-764 904,-775 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2b44400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1350,-879 1350,-869 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="21446@0" ObjectIDZND0="21469@1" Pin0InfoVect0LinkObjId="SW-112369_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2af49c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1350,-879 1350,-869 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2ca59c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1406,-824 1398,-824 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_254fe10@0" ObjectIDZND0="21471@1" Pin0InfoVect0LinkObjId="SW-112371_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_254fe10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1406,-824 1398,-824 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2c682b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1406,-776 1398,-776 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2c46390@0" ObjectIDZND0="21472@1" Pin0InfoVect0LinkObjId="SW-112372_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c46390_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1406,-776 1398,-776 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29738d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1362,-776 1350,-776 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="21472@0" ObjectIDZND0="21468@x" ObjectIDZND1="21470@x" Pin0InfoVect0LinkObjId="SW-112368_0" Pin0InfoVect1LinkObjId="SW-112370_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112372_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1362,-776 1350,-776 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2383c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1406,-718 1398,-718 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_25bca40@0" ObjectIDZND0="21473@1" Pin0InfoVect0LinkObjId="SW-112373_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25bca40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1406,-718 1398,-718 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2c5f210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1362,-718 1350,-718 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="transformer2" ObjectIDND0="21473@0" ObjectIDZND0="21470@x" ObjectIDZND1="21516@x" Pin0InfoVect0LinkObjId="SW-112370_0" Pin0InfoVect1LinkObjId="g_2c92010_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112373_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1362,-718 1350,-718 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2552340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1350,-833 1350,-824 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="21469@0" ObjectIDZND0="21471@x" ObjectIDZND1="21468@x" Pin0InfoVect0LinkObjId="SW-112371_0" Pin0InfoVect1LinkObjId="SW-112368_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112369_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1350,-833 1350,-824 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2325fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1350,-824 1362,-824 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="21469@x" ObjectIDND1="21468@x" ObjectIDZND0="21471@0" Pin0InfoVect0LinkObjId="SW-112371_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-112369_0" Pin1InfoVect1LinkObjId="SW-112368_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1350,-824 1362,-824 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2b339b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1350,-824 1350,-813 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="21469@x" ObjectIDND1="21471@x" ObjectIDZND0="21468@1" Pin0InfoVect0LinkObjId="SW-112368_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-112369_0" Pin1InfoVect1LinkObjId="SW-112371_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1350,-824 1350,-813 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2c56690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1350,-786 1350,-776 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="21468@0" ObjectIDZND0="21472@x" ObjectIDZND1="21470@x" Pin0InfoVect0LinkObjId="SW-112372_0" Pin0InfoVect1LinkObjId="SW-112370_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112368_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1350,-786 1350,-776 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2565000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1350,-729 1350,-718 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="transformer2" ObjectIDND0="21470@0" ObjectIDZND0="21473@x" ObjectIDZND1="21516@x" Pin0InfoVect0LinkObjId="SW-112373_0" Pin0InfoVect1LinkObjId="g_2c92010_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112370_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1350,-729 1350,-718 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2c74950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1350,-765 1350,-776 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="21470@1" ObjectIDZND0="21468@x" ObjectIDZND1="21472@x" Pin0InfoVect0LinkObjId="SW-112368_0" Pin0InfoVect1LinkObjId="SW-112372_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112370_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1350,-765 1350,-776 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2b433c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="806,-898 806,-879 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="21451@0" ObjectIDZND0="21446@0" Pin0InfoVect0LinkObjId="g_2af49c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112351_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="806,-898 806,-879 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_296eb50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="796,-949 806,-949 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="21453@1" ObjectIDZND0="21450@x" ObjectIDZND1="21451@x" Pin0InfoVect0LinkObjId="SW-112350_0" Pin0InfoVect1LinkObjId="SW-112351_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112353_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="796,-949 806,-949 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2b5c330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="806,-970 806,-949 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="21450@0" ObjectIDZND0="21451@x" ObjectIDZND1="21453@x" Pin0InfoVect0LinkObjId="SW-112351_0" Pin0InfoVect1LinkObjId="SW-112353_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112350_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="806,-970 806,-949 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2b5b790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="806,-949 806,-934 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="21450@x" ObjectIDND1="21453@x" ObjectIDZND0="21451@1" Pin0InfoVect0LinkObjId="SW-112351_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-112350_0" Pin1InfoVect1LinkObjId="SW-112353_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="806,-949 806,-934 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2da3310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="806,-1027 806,-1009 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="21452@0" ObjectIDZND0="21454@x" ObjectIDZND1="21450@x" Pin0InfoVect0LinkObjId="SW-112354_0" Pin0InfoVect1LinkObjId="SW-112350_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112352_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="806,-1027 806,-1009 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2af4240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="806,-1009 806,-997 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="21454@x" ObjectIDND1="21452@x" ObjectIDZND0="21450@1" Pin0InfoVect0LinkObjId="SW-112350_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-112354_0" Pin1InfoVect1LinkObjId="SW-112352_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="806,-1009 806,-997 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2aec070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1083,-1008 1095,-1008 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="voltageTransformer" ObjectIDND0="21476@1" ObjectIDZND0="21474@x" ObjectIDZND1="g_2b5b4c0@0" ObjectIDZND2="g_2b55ad0@0" Pin0InfoVect0LinkObjId="SW-112374_0" Pin0InfoVect1LinkObjId="g_2b5b4c0_0" Pin0InfoVect2LinkObjId="g_2b55ad0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112376_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1083,-1008 1095,-1008 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2564ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1095,-982 1095,-1008 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="voltageTransformer" ObjectIDND0="21474@1" ObjectIDZND0="21476@x" ObjectIDZND1="g_2b5b4c0@0" ObjectIDZND2="g_2b55ad0@0" Pin0InfoVect0LinkObjId="SW-112376_0" Pin0InfoVect1LinkObjId="g_2b5b4c0_0" Pin0InfoVect2LinkObjId="g_2b55ad0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112374_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1095,-982 1095,-1008 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2b32e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1095,-1008 1095,-1038 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="21474@x" ObjectIDND1="21476@x" ObjectIDND2="g_2b5b4c0@0" ObjectIDZND0="g_2b55ad0@0" Pin0InfoVect0LinkObjId="g_2b55ad0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-112374_0" Pin1InfoVect1LinkObjId="SW-112376_0" Pin1InfoVect2LinkObjId="g_2b5b4c0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1095,-1008 1095,-1038 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2910ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1095,-1008 1130,-1008 1130,-1013 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="21474@x" ObjectIDND1="21476@x" ObjectIDND2="g_2b55ad0@0" ObjectIDZND0="g_2b5b4c0@0" Pin0InfoVect0LinkObjId="g_2b5b4c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-112374_0" Pin1InfoVect1LinkObjId="SW-112376_0" Pin1InfoVect2LinkObjId="g_2b55ad0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1095,-1008 1130,-1008 1130,-1013 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b29800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="625,-576 625,-590 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_2b318e0@0" ObjectIDZND0="g_2d3afd0@0" Pin0InfoVect0LinkObjId="g_2d3afd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b318e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="625,-576 625,-590 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b13d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="597,-525 625,-525 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2b4ec90@0" ObjectIDZND0="g_2b318e0@0" ObjectIDZND1="21513@x" Pin0InfoVect0LinkObjId="g_2b318e0_0" Pin0InfoVect1LinkObjId="SW-112413_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b4ec90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="597,-525 625,-525 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b2aec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="625,-525 625,-545 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2b4ec90@0" ObjectIDND1="21513@x" ObjectIDZND0="g_2b318e0@1" Pin0InfoVect0LinkObjId="g_2b318e0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2b4ec90_0" Pin1InfoVect1LinkObjId="SW-112413_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="625,-525 625,-545 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_257fdf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1839,-579 1839,-591 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_254ef70@0" ObjectIDZND0="g_2d3cab0@0" Pin0InfoVect0LinkObjId="g_2d3cab0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_254ef70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1839,-579 1839,-591 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22dc8e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1811,-528 1839,-528 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2af0870@0" ObjectIDZND0="g_254ef70@0" ObjectIDZND1="21514@x" Pin0InfoVect0LinkObjId="g_254ef70_0" Pin0InfoVect1LinkObjId="SW-112414_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2af0870_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1811,-528 1839,-528 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29111c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1839,-528 1839,-548 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2af0870@0" ObjectIDND1="21514@x" ObjectIDZND0="g_254ef70@1" Pin0InfoVect0LinkObjId="g_254ef70_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2af0870_0" Pin1InfoVect1LinkObjId="SW-112414_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1839,-528 1839,-548 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2ac1020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="904,-728 904,-712 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" EndDevType1="switch" ObjectIDND0="21464@0" ObjectIDZND0="21515@x" ObjectIDZND1="21467@x" Pin0InfoVect0LinkObjId="g_2b31d80_0" Pin0InfoVect1LinkObjId="SW-112367_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112364_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="904,-728 904,-712 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2c599e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="904,-712 916,-712 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="21515@x" ObjectIDND1="21464@x" ObjectIDZND0="21467@0" Pin0InfoVect0LinkObjId="SW-112367_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2ac1020_0" Pin1InfoVect1LinkObjId="SW-112364_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="904,-712 916,-712 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2c8a4c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="806,-1163 806,-1181 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="voltageTransformer" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="21452@x" ObjectIDND1="21455@x" ObjectIDND2="g_2d3e840@0" ObjectIDZND0="g_25bc5b0@0" ObjectIDZND1="25324@1" Pin0InfoVect0LinkObjId="g_25bc5b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-112352_0" Pin1InfoVect1LinkObjId="SW-112355_0" Pin1InfoVect2LinkObjId="g_2d3e840_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="806,-1163 806,-1181 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_25be530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="806,-1228 806,-1181 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="25324@1" ObjectIDZND0="g_25bc5b0@0" ObjectIDZND1="21452@x" ObjectIDZND2="21455@x" Pin0InfoVect0LinkObjId="g_25bc5b0_0" Pin0InfoVect1LinkObjId="SW-112352_0" Pin0InfoVect2LinkObjId="SW-112355_0" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="806,-1228 806,-1181 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2b0d260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1264,-949 1256,-949 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21459@0" ObjectIDZND0="g_2c4c2d0@0" Pin0InfoVect0LinkObjId="g_2c4c2d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112359_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1264,-949 1256,-949 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2c81c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1264,-1009 1257,-1009 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21460@0" ObjectIDZND0="g_2c5c300@0" Pin0InfoVect0LinkObjId="g_2c5c300_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112360_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1264,-1009 1257,-1009 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2c81e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1311,-1077 1300,-1077 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" ObjectIDND0="21458@x" ObjectIDND1="g_2b1e640@0" ObjectIDND2="46457@1" ObjectIDZND0="21461@1" Pin0InfoVect0LinkObjId="SW-112361_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-112358_0" Pin1InfoVect1LinkObjId="g_2b1e640_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1311,-1077 1300,-1077 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2c9ba40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1264,-1077 1257,-1077 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21461@0" ObjectIDZND0="g_2c9bc70@0" Pin0InfoVect0LinkObjId="g_2c9bc70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112361_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1264,-1077 1257,-1077 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_25be080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1311,-1077 1311,-1063 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" ObjectIDND0="21461@x" ObjectIDND1="g_2b1e640@0" ObjectIDND2="46457@1" ObjectIDZND0="21458@1" Pin0InfoVect0LinkObjId="SW-112358_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-112361_0" Pin1InfoVect1LinkObjId="g_2b1e640_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1311,-1077 1311,-1063 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_25be2e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1311,-1009 1300,-1009 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="21458@x" ObjectIDND1="21456@x" ObjectIDZND0="21460@1" Pin0InfoVect0LinkObjId="SW-112360_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-112358_0" Pin1InfoVect1LinkObjId="SW-112356_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1311,-1009 1300,-1009 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2c9de80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1281,-1163 1311,-1163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2d3f0e0@0" ObjectIDZND0="21458@x" ObjectIDZND1="21461@x" ObjectIDZND2="g_2b1e640@0" Pin0InfoVect0LinkObjId="SW-112358_0" Pin0InfoVect1LinkObjId="SW-112361_0" Pin0InfoVect2LinkObjId="g_2b1e640_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d3f0e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1281,-1163 1311,-1163 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2c9e0e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1340,-1170 1340,-1181 1311,-1181 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="g_2b1e640@0" ObjectIDZND0="21458@x" ObjectIDZND1="21461@x" ObjectIDZND2="g_2d3f0e0@0" Pin0InfoVect0LinkObjId="SW-112358_0" Pin0InfoVect1LinkObjId="SW-112361_0" Pin0InfoVect2LinkObjId="g_2d3f0e0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b1e640_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1340,-1170 1340,-1181 1311,-1181 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2c5bba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1311,-1163 1311,-1077 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="powerLine" BeginDevType2="voltageTransformer" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2b1e640@0" ObjectIDND1="46457@1" ObjectIDND2="g_2d3f0e0@0" ObjectIDZND0="21458@x" ObjectIDZND1="21461@x" Pin0InfoVect0LinkObjId="SW-112358_0" Pin0InfoVect1LinkObjId="SW-112361_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2b1e640_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="g_2d3f0e0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1311,-1163 1311,-1077 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2b0f780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1300,-949 1311,-949 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="21459@1" ObjectIDZND0="21456@x" ObjectIDZND1="21457@x" Pin0InfoVect0LinkObjId="SW-112356_0" Pin0InfoVect1LinkObjId="SW-112357_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112359_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1300,-949 1311,-949 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2b0f970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1311,-970 1311,-949 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="21456@0" ObjectIDZND0="21459@x" ObjectIDZND1="21457@x" Pin0InfoVect0LinkObjId="SW-112359_0" Pin0InfoVect1LinkObjId="SW-112357_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112356_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1311,-970 1311,-949 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2b54580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1311,-949 1311,-934 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="21456@x" ObjectIDND1="21459@x" ObjectIDZND0="21457@1" Pin0InfoVect0LinkObjId="SW-112357_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-112356_0" Pin1InfoVect1LinkObjId="SW-112359_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1311,-949 1311,-934 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2b54770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1311,-1027 1311,-1009 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="21458@0" ObjectIDZND0="21460@x" ObjectIDZND1="21456@x" Pin0InfoVect0LinkObjId="SW-112360_0" Pin0InfoVect1LinkObjId="SW-112356_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112358_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1311,-1027 1311,-1009 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2b1e410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1311,-1009 1311,-997 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="21458@x" ObjectIDND1="21460@x" ObjectIDZND0="21456@1" Pin0InfoVect0LinkObjId="SW-112356_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-112358_0" Pin1InfoVect1LinkObjId="SW-112360_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1311,-1009 1311,-997 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2383730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1311,-1163 1311,-1181 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="voltageTransformer" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="21458@x" ObjectIDND1="21461@x" ObjectIDND2="g_2d3f0e0@0" ObjectIDZND0="g_2b1e640@0" ObjectIDZND1="46457@1" Pin0InfoVect0LinkObjId="g_2b1e640_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-112358_0" Pin1InfoVect1LinkObjId="SW-112361_0" Pin1InfoVect2LinkObjId="g_2d3f0e0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1311,-1163 1311,-1181 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2383990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1311,-1223 1311,-1181 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="switch" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="46457@1" ObjectIDZND0="21458@x" ObjectIDZND1="21461@x" ObjectIDZND2="g_2d3f0e0@0" Pin0InfoVect0LinkObjId="SW-112358_0" Pin0InfoVect1LinkObjId="SW-112361_0" Pin0InfoVect2LinkObjId="g_2d3f0e0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1311,-1223 1311,-1181 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2544f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1311,-898 1311,-879 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="21457@0" ObjectIDZND0="21446@0" Pin0InfoVect0LinkObjId="g_2af49c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112357_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1311,-898 1311,-879 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2c9a390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1095,-918 1084,-918 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="busSection" EndDevType0="switch" ObjectIDND0="21474@x" ObjectIDND1="21446@0" ObjectIDZND0="21475@1" Pin0InfoVect0LinkObjId="SW-112375_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-112374_0" Pin1InfoVect1LinkObjId="g_2af49c0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1095,-918 1084,-918 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2c9a5f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1048,-918 1039,-918 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21475@0" ObjectIDZND0="g_255bbd0@0" Pin0InfoVect0LinkObjId="g_255bbd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112375_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1048,-918 1039,-918 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2b31d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="816,-674 816,-681 904,-681 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="21478@1" ObjectIDZND0="21515@x" Pin0InfoVect0LinkObjId="g_2ac1020_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112378_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="816,-674 816,-681 904,-681 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2b45010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="816,-626 816,-638 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_29385f0@0" ObjectIDZND0="21478@0" Pin0InfoVect0LinkObjId="SW-112378_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29385f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="816,-626 816,-638 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2c91db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1262,-626 1262,-638 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2c52390@0" ObjectIDZND0="21480@0" Pin0InfoVect0LinkObjId="SW-112380_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c52390_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1262,-626 1262,-638 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2c92010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1262,-674 1262,-681 1350,-681 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="21480@1" ObjectIDZND0="21516@x" Pin0InfoVect0LinkObjId="g_2ca44b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112380_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1262,-674 1262,-681 1350,-681 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2ca42c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="904,-712 904,-700 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="transformer2" ObjectIDND0="21467@x" ObjectIDND1="21464@x" ObjectIDZND0="21515@0" Pin0InfoVect0LinkObjId="g_2ac1020_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-112367_0" Pin1InfoVect1LinkObjId="SW-112364_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="904,-712 904,-700 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2ca44b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1350,-718 1350,-700 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="transformer2" ObjectIDND0="21470@x" ObjectIDND1="21473@x" ObjectIDZND0="21516@0" Pin0InfoVect0LinkObjId="g_2c92010_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-112370_0" Pin1InfoVect1LinkObjId="SW-112373_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1350,-718 1350,-700 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b325a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="873,-584 873,-595 904,-595 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" EndDevType1="breaker" ObjectIDND0="g_2ca46a0@0" ObjectIDZND0="21515@x" ObjectIDZND1="21477@x" Pin0InfoVect0LinkObjId="g_2ac1020_0" Pin0InfoVect1LinkObjId="SW-112377_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ca46a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="873,-584 873,-595 904,-595 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c70d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1319,-584 1319,-595 1350,-595 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="transformer2" ObjectIDND0="g_2ca25b0@0" ObjectIDZND0="21479@x" ObjectIDZND1="21516@x" Pin0InfoVect0LinkObjId="SW-112379_0" Pin0InfoVect1LinkObjId="g_2c92010_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ca25b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1319,-584 1319,-595 1350,-595 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25639c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="904,-481 904,-452 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="21477@1" ObjectIDZND0="21447@0" Pin0InfoVect0LinkObjId="g_25814f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112377_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="904,-481 904,-452 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c426c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1350,-481 1350,-453 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="21479@1" ObjectIDZND0="21448@0" Pin0InfoVect0LinkObjId="g_2b25930_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112379_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1350,-481 1350,-453 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2944170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="904,-620 904,-595 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="21515@1" ObjectIDZND0="g_2ca46a0@0" ObjectIDZND1="21477@x" Pin0InfoVect0LinkObjId="g_2ca46a0_0" Pin0InfoVect1LinkObjId="SW-112377_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ac1020_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="904,-620 904,-595 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29443d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="904,-573 904,-595 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" EndDevType1="lightningRod" ObjectIDND0="21477@0" ObjectIDZND0="21515@x" ObjectIDZND1="g_2ca46a0@0" Pin0InfoVect0LinkObjId="g_2ac1020_0" Pin0InfoVect1LinkObjId="g_2ca46a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112377_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="904,-573 904,-595 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c72c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1350,-620 1350,-595 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="21516@1" ObjectIDZND0="g_2ca25b0@0" ObjectIDZND1="21479@x" Pin0InfoVect0LinkObjId="g_2ca25b0_0" Pin0InfoVect1LinkObjId="SW-112379_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c92010_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1350,-620 1350,-595 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c72eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1350,-595 1350,-573 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" EndDevType0="breaker" ObjectIDND0="g_2ca25b0@0" ObjectIDND1="21516@x" ObjectIDZND0="21479@0" Pin0InfoVect0LinkObjId="SW-112379_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2ca25b0_0" Pin1InfoVect1LinkObjId="g_2c92010_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1350,-595 1350,-573 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2581290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="625,-525 625,-498 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_2b318e0@0" ObjectIDND1="g_2b4ec90@0" ObjectIDZND0="21513@1" Pin0InfoVect0LinkObjId="SW-112413_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2b318e0_0" Pin1InfoVect1LinkObjId="g_2b4ec90_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="625,-525 625,-498 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25814f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="625,-481 625,-452 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="21513@0" ObjectIDZND0="21447@0" Pin0InfoVect0LinkObjId="g_25639c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112413_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="625,-481 625,-452 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b256d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1839,-528 1839,-500 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_2af0870@0" ObjectIDND1="g_254ef70@0" ObjectIDZND0="21514@1" Pin0InfoVect0LinkObjId="SW-112414_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2af0870_0" Pin1InfoVect1LinkObjId="g_254ef70_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1839,-528 1839,-500 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b25930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1840,-482 1840,-453 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="21514@0" ObjectIDZND0="21448@0" Pin0InfoVect0LinkObjId="g_2c426c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112414_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1840,-482 1840,-453 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_254d710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="700,-282 700,-272 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21486@0" ObjectIDZND0="g_2c9c180@0" Pin0InfoVect0LinkObjId="g_2c9c180_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112386_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="700,-282 700,-272 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22968b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="700,-318 700,-327 671,-327 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="lightningRod" EndDevType2="generator" ObjectIDND0="21486@1" ObjectIDZND0="21485@x" ObjectIDZND1="g_2390590@0" ObjectIDZND2="43361@x" Pin0InfoVect0LinkObjId="SW-112385_0" Pin0InfoVect1LinkObjId="g_2390590_0" Pin0InfoVect2LinkObjId="SM-CX_AP.P1_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112386_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="700,-318 700,-327 671,-327 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c79ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="671,-433 671,-452 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="21485@0" ObjectIDZND0="21447@0" Pin0InfoVect0LinkObjId="g_25639c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112385_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="671,-433 671,-452 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c7a220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="671,-327 671,-341 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="generator" EndDevType0="breaker" ObjectIDND0="21486@x" ObjectIDND1="g_2390590@0" ObjectIDND2="43361@x" ObjectIDZND0="21485@1" Pin0InfoVect0LinkObjId="SW-112385_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-112386_0" Pin1InfoVect1LinkObjId="g_2390590_0" Pin1InfoVect2LinkObjId="SM-CX_AP.P1_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="671,-327 671,-341 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c7a480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="678,-234 671,-234 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" EndDevType2="generator" ObjectIDND0="g_2390590@0" ObjectIDZND0="21486@x" ObjectIDZND1="21485@x" ObjectIDZND2="43361@x" Pin0InfoVect0LinkObjId="SW-112386_0" Pin0InfoVect1LinkObjId="SW-112385_0" Pin0InfoVect2LinkObjId="SM-CX_AP.P1_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2390590_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="678,-234 671,-234 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_23900d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="671,-106 671,-234 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="breaker" ObjectIDND0="43361@0" ObjectIDZND0="g_2390590@0" ObjectIDZND1="21486@x" ObjectIDZND2="21485@x" Pin0InfoVect0LinkObjId="g_2390590_0" Pin0InfoVect1LinkObjId="SW-112386_0" Pin0InfoVect2LinkObjId="SW-112385_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-CX_AP.P1_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="671,-106 671,-234 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2390330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="671,-234 671,-327 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="generator" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="g_2390590@0" ObjectIDND1="43361@x" ObjectIDZND0="21486@x" ObjectIDZND1="21485@x" Pin0InfoVect0LinkObjId="SW-112386_0" Pin0InfoVect1LinkObjId="SW-112385_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2390590_0" Pin1InfoVect1LinkObjId="SM-CX_AP.P1_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="671,-234 671,-327 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c93920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="795,-283 795,-273 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21488@0" ObjectIDZND0="g_2c8d060@0" Pin0InfoVect0LinkObjId="g_2c8d060_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112388_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="795,-283 795,-273 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2560290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="795,-319 795,-328 766,-328 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="lightningRod" EndDevType2="generator" ObjectIDND0="21488@1" ObjectIDZND0="21487@x" ObjectIDZND1="g_2c67270@0" ObjectIDZND2="43362@x" Pin0InfoVect0LinkObjId="SW-112387_0" Pin0InfoVect1LinkObjId="g_2c67270_0" Pin0InfoVect2LinkObjId="SM-CX_AP.P2_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112388_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="795,-319 795,-328 766,-328 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2546190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="766,-434 766,-452 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="21487@0" ObjectIDZND0="21447@0" Pin0InfoVect0LinkObjId="g_25639c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112387_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="766,-434 766,-452 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b52560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="766,-328 766,-342 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="generator" EndDevType0="breaker" ObjectIDND0="21488@x" ObjectIDND1="g_2c67270@0" ObjectIDND2="43362@x" ObjectIDZND0="21487@1" Pin0InfoVect0LinkObjId="SW-112387_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-112388_0" Pin1InfoVect1LinkObjId="g_2c67270_0" Pin1InfoVect2LinkObjId="SM-CX_AP.P2_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="766,-328 766,-342 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b527c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="773,-234 766,-234 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" EndDevType2="generator" ObjectIDND0="g_2c67270@0" ObjectIDZND0="21488@x" ObjectIDZND1="21487@x" ObjectIDZND2="43362@x" Pin0InfoVect0LinkObjId="SW-112388_0" Pin0InfoVect1LinkObjId="SW-112387_0" Pin0InfoVect2LinkObjId="SM-CX_AP.P2_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c67270_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="773,-234 766,-234 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b52a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="766,-106 766,-234 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="breaker" ObjectIDND0="43362@0" ObjectIDZND0="g_2c67270@0" ObjectIDZND1="21488@x" ObjectIDZND2="21487@x" Pin0InfoVect0LinkObjId="g_2c67270_0" Pin0InfoVect1LinkObjId="SW-112388_0" Pin0InfoVect2LinkObjId="SW-112387_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-CX_AP.P2_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="766,-106 766,-234 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b52c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="766,-234 766,-328 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="generator" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="g_2c67270@0" ObjectIDND1="43362@x" ObjectIDZND0="21488@x" ObjectIDZND1="21487@x" Pin0InfoVect0LinkObjId="SW-112388_0" Pin0InfoVect1LinkObjId="SW-112387_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2c67270_0" Pin1InfoVect1LinkObjId="SM-CX_AP.P2_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="766,-234 766,-328 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c99bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="890,-282 890,-272 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21490@0" ObjectIDZND0="g_2c6b550@0" Pin0InfoVect0LinkObjId="g_2c6b550_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112390_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="890,-282 890,-272 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_255d8e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="890,-318 890,-327 861,-327 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="lightningRod" EndDevType2="generator" ObjectIDND0="21490@1" ObjectIDZND0="21489@x" ObjectIDZND1="g_25bae00@0" ObjectIDZND2="43363@x" Pin0InfoVect0LinkObjId="SW-112389_0" Pin0InfoVect1LinkObjId="g_25bae00_0" Pin0InfoVect2LinkObjId="SM-CX_AP.P3_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112390_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="890,-318 890,-327 861,-327 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c8d780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="861,-433 861,-452 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="21489@0" ObjectIDZND0="21447@0" Pin0InfoVect0LinkObjId="g_25639c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112389_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="861,-433 861,-452 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c8d9e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="861,-327 861,-341 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="generator" EndDevType0="breaker" ObjectIDND0="21490@x" ObjectIDND1="g_25bae00@0" ObjectIDND2="43363@x" ObjectIDZND0="21489@1" Pin0InfoVect0LinkObjId="SW-112389_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-112390_0" Pin1InfoVect1LinkObjId="g_25bae00_0" Pin1InfoVect2LinkObjId="SM-CX_AP.P3_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="861,-327 861,-341 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c8dc40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="868,-234 861,-234 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" EndDevType2="generator" ObjectIDND0="g_25bae00@0" ObjectIDZND0="21490@x" ObjectIDZND1="21489@x" ObjectIDZND2="43363@x" Pin0InfoVect0LinkObjId="SW-112390_0" Pin0InfoVect1LinkObjId="SW-112389_0" Pin0InfoVect2LinkObjId="SM-CX_AP.P3_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25bae00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="868,-234 861,-234 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25ba940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="861,-106 861,-234 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="breaker" ObjectIDND0="43363@0" ObjectIDZND0="g_25bae00@0" ObjectIDZND1="21490@x" ObjectIDZND2="21489@x" Pin0InfoVect0LinkObjId="g_25bae00_0" Pin0InfoVect1LinkObjId="SW-112390_0" Pin0InfoVect2LinkObjId="SW-112389_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-CX_AP.P3_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="861,-106 861,-234 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25baba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="861,-234 861,-327 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="generator" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="g_25bae00@0" ObjectIDND1="43363@x" ObjectIDZND0="21490@x" ObjectIDZND1="21489@x" Pin0InfoVect0LinkObjId="SW-112390_0" Pin0InfoVect1LinkObjId="SW-112389_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_25bae00_0" Pin1InfoVect1LinkObjId="SM-CX_AP.P3_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="861,-234 861,-327 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_293ecb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="996,-282 996,-272 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21492@0" ObjectIDZND0="g_2381cb0@0" Pin0InfoVect0LinkObjId="g_2381cb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112392_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="996,-282 996,-272 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b30e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="996,-318 996,-327 967,-327 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="lightningRod" EndDevType2="generator" ObjectIDND0="21492@1" ObjectIDZND0="21491@x" ObjectIDZND1="g_2c4e7c0@0" ObjectIDZND2="43364@x" Pin0InfoVect0LinkObjId="SW-112391_0" Pin0InfoVect1LinkObjId="g_2c4e7c0_0" Pin0InfoVect2LinkObjId="SM-CX_AP.P4_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112392_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="996,-318 996,-327 967,-327 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b24880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="967,-433 967,-452 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="21491@0" ObjectIDZND0="21447@0" Pin0InfoVect0LinkObjId="g_25639c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112391_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="967,-433 967,-452 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b24ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="967,-327 967,-341 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="generator" EndDevType0="breaker" ObjectIDND0="21492@x" ObjectIDND1="g_2c4e7c0@0" ObjectIDND2="43364@x" ObjectIDZND0="21491@1" Pin0InfoVect0LinkObjId="SW-112391_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-112392_0" Pin1InfoVect1LinkObjId="g_2c4e7c0_0" Pin1InfoVect2LinkObjId="SM-CX_AP.P4_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="967,-327 967,-341 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b24d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="974,-234 967,-234 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" EndDevType2="generator" ObjectIDND0="g_2c4e7c0@0" ObjectIDZND0="21492@x" ObjectIDZND1="21491@x" ObjectIDZND2="43364@x" Pin0InfoVect0LinkObjId="SW-112392_0" Pin0InfoVect1LinkObjId="SW-112391_0" Pin0InfoVect2LinkObjId="SM-CX_AP.P4_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c4e7c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="974,-234 967,-234 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b24f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="967,-106 967,-234 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="breaker" ObjectIDND0="43364@0" ObjectIDZND0="g_2c4e7c0@0" ObjectIDZND1="21492@x" ObjectIDZND2="21491@x" Pin0InfoVect0LinkObjId="g_2c4e7c0_0" Pin0InfoVect1LinkObjId="SW-112392_0" Pin0InfoVect2LinkObjId="SW-112391_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-CX_AP.P4_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="967,-106 967,-234 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c4e560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="967,-234 967,-327 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="generator" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="g_2c4e7c0@0" ObjectIDND1="43364@x" ObjectIDZND0="21492@x" ObjectIDZND1="21491@x" Pin0InfoVect0LinkObjId="SW-112392_0" Pin0InfoVect1LinkObjId="SW-112391_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2c4e7c0_0" Pin1InfoVect1LinkObjId="SM-CX_AP.P4_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="967,-234 967,-327 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b4c930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="488,-278 488,-268 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21482@0" ObjectIDZND0="g_2c94fb0@0" Pin0InfoVect0LinkObjId="g_2c94fb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112382_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="488,-278 488,-268 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c79c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="460,-430 460,-452 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="21481@0" ObjectIDZND0="21447@0" Pin0InfoVect0LinkObjId="g_25639c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112381_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="460,-430 460,-452 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2552960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="460,-187 460,-213 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2552bc0@0" ObjectIDZND0="g_257efe0@0" Pin0InfoVect0LinkObjId="g_257efe0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2552bc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="460,-187 460,-213 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b5a470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="429,-160 429,-171 461,-171 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_2b5a6d0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b5a6d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="429,-160 429,-171 461,-171 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2553cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1500,-280 1500,-270 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21500@0" ObjectIDZND0="g_2b20a50@0" Pin0InfoVect0LinkObjId="g_2b20a50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112400_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1500,-280 1500,-270 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c652a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1500,-316 1500,-325 1471,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="lightningRod" EndDevType2="generator" ObjectIDND0="21500@1" ObjectIDZND0="21499@x" ObjectIDZND1="g_2c6d940@0" ObjectIDZND2="43366@x" Pin0InfoVect0LinkObjId="SW-112399_0" Pin0InfoVect1LinkObjId="g_2c6d940_0" Pin0InfoVect2LinkObjId="SM-CX_AP.P6_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112400_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1500,-316 1500,-325 1471,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c836b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1471,-431 1471,-453 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="21499@0" ObjectIDZND0="21448@0" Pin0InfoVect0LinkObjId="g_2c426c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112399_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1471,-431 1471,-453 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c83910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1471,-325 1471,-339 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="generator" EndDevType0="breaker" ObjectIDND0="21500@x" ObjectIDND1="g_2c6d940@0" ObjectIDND2="43366@x" ObjectIDZND0="21499@1" Pin0InfoVect0LinkObjId="SW-112399_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-112400_0" Pin1InfoVect1LinkObjId="g_2c6d940_0" Pin1InfoVect2LinkObjId="SM-CX_AP.P6_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1471,-325 1471,-339 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c6d220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1478,-232 1471,-232 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" EndDevType2="generator" ObjectIDND0="g_2c6d940@0" ObjectIDZND0="21500@x" ObjectIDZND1="21499@x" ObjectIDZND2="43366@x" Pin0InfoVect0LinkObjId="SW-112400_0" Pin0InfoVect1LinkObjId="SW-112399_0" Pin0InfoVect2LinkObjId="SM-CX_AP.P6_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c6d940_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1478,-232 1471,-232 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c6d480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1471,-104 1471,-232 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="breaker" ObjectIDND0="43366@0" ObjectIDZND0="g_2c6d940@0" ObjectIDZND1="21500@x" ObjectIDZND2="21499@x" Pin0InfoVect0LinkObjId="g_2c6d940_0" Pin0InfoVect1LinkObjId="SW-112400_0" Pin0InfoVect2LinkObjId="SW-112399_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-CX_AP.P6_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1471,-104 1471,-232 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c6d6e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1471,-232 1471,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="generator" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="g_2c6d940@0" ObjectIDND1="43366@x" ObjectIDZND0="21500@x" ObjectIDZND1="21499@x" Pin0InfoVect0LinkObjId="SW-112400_0" Pin0InfoVect1LinkObjId="SW-112399_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2c6d940_0" Pin1InfoVect1LinkObjId="SM-CX_AP.P6_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1471,-232 1471,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c98480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1605,-279 1605,-269 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21502@0" ObjectIDZND0="g_2c979f0@0" Pin0InfoVect0LinkObjId="g_2c979f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112402_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1605,-279 1605,-269 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b10b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1605,-315 1605,-324 1576,-324 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="lightningRod" EndDevType2="generator" ObjectIDND0="21502@1" ObjectIDZND0="21501@x" ObjectIDZND1="g_2b4b800@0" ObjectIDZND2="43367@x" Pin0InfoVect0LinkObjId="SW-112401_0" Pin0InfoVect1LinkObjId="g_2b4b800_0" Pin0InfoVect2LinkObjId="SM-CX_AP.P7_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112402_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1605,-315 1605,-324 1576,-324 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b27ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1576,-430 1576,-453 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="21501@0" ObjectIDZND0="21448@0" Pin0InfoVect0LinkObjId="g_2c426c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112401_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1576,-430 1576,-453 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b28140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1576,-324 1576,-338 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="generator" EndDevType0="breaker" ObjectIDND0="21502@x" ObjectIDND1="g_2b4b800@0" ObjectIDND2="43367@x" ObjectIDZND0="21501@1" Pin0InfoVect0LinkObjId="SW-112401_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-112402_0" Pin1InfoVect1LinkObjId="g_2b4b800_0" Pin1InfoVect2LinkObjId="SM-CX_AP.P7_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1576,-324 1576,-338 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b283a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1583,-231 1576,-231 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" EndDevType2="generator" ObjectIDND0="g_2b4b800@0" ObjectIDZND0="21502@x" ObjectIDZND1="21501@x" ObjectIDZND2="43367@x" Pin0InfoVect0LinkObjId="SW-112402_0" Pin0InfoVect1LinkObjId="SW-112401_0" Pin0InfoVect2LinkObjId="SM-CX_AP.P7_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b4b800_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1583,-231 1576,-231 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b28600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1576,-103 1576,-231 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="breaker" ObjectIDND0="43367@0" ObjectIDZND0="g_2b4b800@0" ObjectIDZND1="21502@x" ObjectIDZND2="21501@x" Pin0InfoVect0LinkObjId="g_2b4b800_0" Pin0InfoVect1LinkObjId="SW-112402_0" Pin0InfoVect2LinkObjId="SW-112401_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-CX_AP.P7_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1576,-103 1576,-231 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b28860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1576,-231 1576,-324 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="generator" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="g_2b4b800@0" ObjectIDND1="43367@x" ObjectIDZND0="21502@x" ObjectIDZND1="21501@x" Pin0InfoVect0LinkObjId="SW-112402_0" Pin0InfoVect1LinkObjId="SW-112401_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2b4b800_0" Pin1InfoVect1LinkObjId="SM-CX_AP.P7_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1576,-231 1576,-324 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b59400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1702,-279 1702,-269 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21504@0" ObjectIDZND0="g_2b589d0@0" Pin0InfoVect0LinkObjId="g_2b589d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112404_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1702,-279 1702,-269 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b18640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1702,-315 1702,-324 1673,-324 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="lightningRod" EndDevType2="generator" ObjectIDND0="21504@1" ObjectIDZND0="21503@x" ObjectIDZND1="g_2c9d150@0" ObjectIDZND2="43368@x" Pin0InfoVect0LinkObjId="SW-112403_0" Pin0InfoVect1LinkObjId="g_2c9d150_0" Pin0InfoVect2LinkObjId="SM-CX_AP.P8_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112404_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1702,-315 1702,-324 1673,-324 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b1f640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1673,-430 1673,-453 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="21503@0" ObjectIDZND0="21448@0" Pin0InfoVect0LinkObjId="g_2c426c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112403_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1673,-430 1673,-453 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b1f8a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1673,-324 1673,-338 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="generator" EndDevType0="breaker" ObjectIDND0="21504@x" ObjectIDND1="g_2c9d150@0" ObjectIDND2="43368@x" ObjectIDZND0="21503@1" Pin0InfoVect0LinkObjId="SW-112403_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-112404_0" Pin1InfoVect1LinkObjId="g_2c9d150_0" Pin1InfoVect2LinkObjId="SM-CX_AP.P8_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1673,-324 1673,-338 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b1fb00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1680,-231 1673,-231 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" EndDevType2="generator" ObjectIDND0="g_2c9d150@0" ObjectIDZND0="21504@x" ObjectIDZND1="21503@x" ObjectIDZND2="43368@x" Pin0InfoVect0LinkObjId="SW-112404_0" Pin0InfoVect1LinkObjId="SW-112403_0" Pin0InfoVect2LinkObjId="SM-CX_AP.P8_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c9d150_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1680,-231 1673,-231 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c9cc90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1673,-103 1673,-231 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="breaker" ObjectIDND0="43368@0" ObjectIDZND0="g_2c9d150@0" ObjectIDZND1="21504@x" ObjectIDZND2="21503@x" Pin0InfoVect0LinkObjId="g_2c9d150_0" Pin0InfoVect1LinkObjId="SW-112404_0" Pin0InfoVect2LinkObjId="SW-112403_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-CX_AP.P8_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1673,-103 1673,-231 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c9cef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1673,-231 1673,-324 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="generator" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="g_2c9d150@0" ObjectIDND1="43368@x" ObjectIDZND0="21504@x" ObjectIDZND1="21503@x" Pin0InfoVect0LinkObjId="SW-112404_0" Pin0InfoVect1LinkObjId="SW-112403_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2c9d150_0" Pin1InfoVect1LinkObjId="SM-CX_AP.P8_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1673,-231 1673,-324 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2574300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1805,-279 1805,-269 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21506@0" ObjectIDZND0="g_2c7bad0@0" Pin0InfoVect0LinkObjId="g_2c7bad0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112406_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1805,-279 1805,-269 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2574a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1805,-315 1805,-324 1776,-324 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="lightningRod" EndDevType2="generator" ObjectIDND0="21506@1" ObjectIDZND0="21505@x" ObjectIDZND1="g_2c5e2a0@0" ObjectIDZND2="43369@x" Pin0InfoVect0LinkObjId="SW-112405_0" Pin0InfoVect1LinkObjId="g_2c5e2a0_0" Pin0InfoVect2LinkObjId="SM-CX_AP.P9_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112406_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1805,-315 1805,-324 1776,-324 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c5d6c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1776,-430 1776,-453 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="21505@0" ObjectIDZND0="21448@0" Pin0InfoVect0LinkObjId="g_2c426c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112405_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1776,-430 1776,-453 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c5d920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1776,-324 1776,-338 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="generator" EndDevType0="breaker" ObjectIDND0="21506@x" ObjectIDND1="g_2c5e2a0@0" ObjectIDND2="43369@x" ObjectIDZND0="21505@1" Pin0InfoVect0LinkObjId="SW-112405_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-112406_0" Pin1InfoVect1LinkObjId="g_2c5e2a0_0" Pin1InfoVect2LinkObjId="SM-CX_AP.P9_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1776,-324 1776,-338 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c5db80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1783,-231 1776,-231 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" EndDevType2="generator" ObjectIDND0="g_2c5e2a0@0" ObjectIDZND0="21506@x" ObjectIDZND1="21505@x" ObjectIDZND2="43369@x" Pin0InfoVect0LinkObjId="SW-112406_0" Pin0InfoVect1LinkObjId="SW-112405_0" Pin0InfoVect2LinkObjId="SM-CX_AP.P9_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c5e2a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1783,-231 1776,-231 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c5dde0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1776,-103 1776,-231 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="breaker" ObjectIDND0="43369@0" ObjectIDZND0="g_2c5e2a0@0" ObjectIDZND1="21506@x" ObjectIDZND2="21505@x" Pin0InfoVect0LinkObjId="g_2c5e2a0_0" Pin0InfoVect1LinkObjId="SW-112406_0" Pin0InfoVect2LinkObjId="SW-112405_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-CX_AP.P9_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1776,-103 1776,-231 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c5e040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1776,-231 1776,-324 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="generator" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="g_2c5e2a0@0" ObjectIDND1="43369@x" ObjectIDZND0="21506@x" ObjectIDZND1="21505@x" Pin0InfoVect0LinkObjId="SW-112406_0" Pin0InfoVect1LinkObjId="SW-112405_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2c5e2a0_0" Pin1InfoVect1LinkObjId="SM-CX_AP.P9_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1776,-231 1776,-324 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29413d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="594,-280 594,-270 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21484@0" ObjectIDZND0="g_2940940@0" Pin0InfoVect0LinkObjId="g_2940940_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112384_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="594,-280 594,-270 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2aecde0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="594,-316 594,-325 565,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="21484@1" ObjectIDZND0="21483@x" ObjectIDZND1="g_2aed720@0" ObjectIDZND2="g_2c695f0@0" Pin0InfoVect0LinkObjId="SW-112383_0" Pin0InfoVect1LinkObjId="g_2aed720_0" Pin0InfoVect2LinkObjId="g_2c695f0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112384_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="594,-316 594,-325 565,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c50b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="566,-182 566,-161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2aed720@0" ObjectIDZND0="g_2c6a500@0" Pin0InfoVect0LinkObjId="g_2c6a500_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2aed720_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="566,-182 566,-161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c69130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="565,-325 565,-339 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="breaker" ObjectIDND0="21484@x" ObjectIDND1="g_2aed720@0" ObjectIDND2="g_2c695f0@0" ObjectIDZND0="21483@1" Pin0InfoVect0LinkObjId="SW-112383_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-112384_0" Pin1InfoVect1LinkObjId="g_2aed720_0" Pin1InfoVect2LinkObjId="g_2c695f0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="565,-325 565,-339 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c69390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="573,-242 565,-242 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" EndDevType2="lightningRod" ObjectIDND0="g_2c695f0@0" ObjectIDZND0="21484@x" ObjectIDZND1="21483@x" ObjectIDZND2="g_2aed720@0" Pin0InfoVect0LinkObjId="SW-112384_0" Pin0InfoVect1LinkObjId="SW-112383_0" Pin0InfoVect2LinkObjId="g_2aed720_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c695f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="573,-242 565,-242 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c6a2a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="565,-431 565,-452 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="21483@0" ObjectIDZND0="21447@0" Pin0InfoVect0LinkObjId="g_25639c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112383_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="565,-431 565,-452 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2c87160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1838,-1138 1838,-1098 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="transformer2" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1838,-1138 1838,-1098 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2c873c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1838,-888 1838,-853 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" ObjectIDND0="0@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1838,-888 1838,-853 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2cc5e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2051,-1138 2051,-1098 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="transformer2" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2051,-1138 2051,-1098 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2cc60e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2051,-888 2051,-853 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" ObjectIDND0="0@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="2051,-888 2051,-853 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2d97280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1998,-888 1998,-853 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" ObjectIDND0="0@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1998,-888 1998,-853 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2d974e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1838,-992 1998,-992 1998,-980 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="breaker" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1838,-992 1998,-992 1998,-980 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2d97fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1838,-1004 1838,-992 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" EndDevType1="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1838,-1004 1838,-992 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2d98230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1838,-992 1838,-980 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="transformer2" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1838,-992 1838,-980 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2d39460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1893,-743 1893,-725 2135,-725 2135,-992 2051,-992 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" EndDevType1="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1893,-743 1893,-725 2135,-725 2135,-992 2051,-992 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2d396d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1893,-853 1893,-835 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="breaker" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1893,-853 1893,-835 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2d3a350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2051,-1004 2051,-992 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" EndDevType1="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2051,-1004 2051,-992 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2d3a5b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2051,-992 2051,-980 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="transformer2" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2051,-992 2051,-980 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d3f980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="489,-317 489,-326 460,-326 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="21482@1" ObjectIDZND0="21481@x" ObjectIDZND1="g_2c53ed0@0" ObjectIDZND2="g_257efe0@0" Pin0InfoVect0LinkObjId="SW-112381_0" Pin0InfoVect1LinkObjId="g_2c53ed0_0" Pin0InfoVect2LinkObjId="g_257efe0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112382_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="489,-317 489,-326 460,-326 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d3fbe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="460,-338 460,-326 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="21481@1" ObjectIDZND0="21482@x" ObjectIDZND1="g_2c53ed0@0" ObjectIDZND2="g_257efe0@0" Pin0InfoVect0LinkObjId="SW-112382_0" Pin0InfoVect1LinkObjId="g_2c53ed0_0" Pin0InfoVect2LinkObjId="g_257efe0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112381_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="460,-338 460,-326 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d3fe40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="424,-295 424,-312 460,-312 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" EndDevType2="lightningRod" ObjectIDND0="g_2c53ed0@0" ObjectIDZND0="21482@x" ObjectIDZND1="21481@x" ObjectIDZND2="g_257efe0@0" Pin0InfoVect0LinkObjId="SW-112382_0" Pin0InfoVect1LinkObjId="SW-112381_0" Pin0InfoVect2LinkObjId="g_257efe0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c53ed0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="424,-295 424,-312 460,-312 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d400a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="460,-312 460,-326 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="g_2c53ed0@0" ObjectIDND1="g_257efe0@0" ObjectIDZND0="21482@x" ObjectIDZND1="21481@x" Pin0InfoVect0LinkObjId="SW-112382_0" Pin0InfoVect1LinkObjId="SW-112381_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2c53ed0_0" Pin1InfoVect1LinkObjId="g_257efe0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="460,-312 460,-326 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d40300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="460,-251 460,-312 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="breaker" ObjectIDND0="g_257efe0@1" ObjectIDZND0="g_2c53ed0@0" ObjectIDZND1="21482@x" ObjectIDZND2="21481@x" Pin0InfoVect0LinkObjId="g_2c53ed0_0" Pin0InfoVect1LinkObjId="SW-112382_0" Pin0InfoVect2LinkObjId="SW-112381_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_257efe0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="460,-251 460,-312 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d40560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="565,-242 565,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="g_2aed720@0" ObjectIDND1="g_2c695f0@0" ObjectIDZND0="21484@x" ObjectIDZND1="21483@x" Pin0InfoVect0LinkObjId="SW-112384_0" Pin0InfoVect1LinkObjId="SW-112383_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2aed720_0" Pin1InfoVect1LinkObjId="g_2c695f0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="565,-242 565,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d407c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="565,-220 565,-242 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" EndDevType2="lightningRod" ObjectIDND0="g_2aed720@1" ObjectIDZND0="21484@x" ObjectIDZND1="21483@x" ObjectIDZND2="g_2c695f0@0" Pin0InfoVect0LinkObjId="SW-112384_0" Pin0InfoVect1LinkObjId="SW-112383_0" Pin0InfoVect2LinkObjId="g_2c695f0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2aed720_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="565,-220 565,-242 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d44ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1091,-279 1091,-269 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21494@0" ObjectIDZND0="g_2d44110@0" Pin0InfoVect0LinkObjId="g_2d44110_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112394_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1091,-279 1091,-269 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d452f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1091,-315 1091,-324 1062,-324 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="lightningRod" EndDevType2="generator" ObjectIDND0="21494@1" ObjectIDZND0="21493@x" ObjectIDZND1="g_2cf1220@0" ObjectIDZND2="43365@x" Pin0InfoVect0LinkObjId="SW-112393_0" Pin0InfoVect1LinkObjId="g_2cf1220_0" Pin0InfoVect2LinkObjId="SM-CX_AP.P5_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112394_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1091,-315 1091,-324 1062,-324 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cf0660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1062,-430 1062,-452 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="21493@0" ObjectIDZND0="21447@0" Pin0InfoVect0LinkObjId="g_25639c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112393_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1062,-430 1062,-452 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cf08a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1062,-324 1062,-338 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="generator" EndDevType0="breaker" ObjectIDND0="21494@x" ObjectIDND1="g_2cf1220@0" ObjectIDND2="43365@x" ObjectIDZND0="21493@1" Pin0InfoVect0LinkObjId="SW-112393_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-112394_0" Pin1InfoVect1LinkObjId="g_2cf1220_0" Pin1InfoVect2LinkObjId="SM-CX_AP.P5_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1062,-324 1062,-338 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cf0b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1069,-235 1062,-235 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" EndDevType2="generator" ObjectIDND0="g_2cf1220@0" ObjectIDZND0="21494@x" ObjectIDZND1="21493@x" ObjectIDZND2="43365@x" Pin0InfoVect0LinkObjId="SW-112394_0" Pin0InfoVect1LinkObjId="SW-112393_0" Pin0InfoVect2LinkObjId="SM-CX_AP.P5_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2cf1220_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1069,-235 1062,-235 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cf0d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1062,-103 1062,-231 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="breaker" ObjectIDND0="43365@0" ObjectIDZND0="g_2cf1220@0" ObjectIDZND1="21494@x" ObjectIDZND2="21493@x" Pin0InfoVect0LinkObjId="g_2cf1220_0" Pin0InfoVect1LinkObjId="SW-112394_0" Pin0InfoVect2LinkObjId="SW-112393_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-CX_AP.P5_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1062,-103 1062,-231 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cf0fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1062,-231 1062,-324 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="generator" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="g_2cf1220@0" ObjectIDND1="43365@x" ObjectIDZND0="21494@x" ObjectIDZND1="21493@x" Pin0InfoVect0LinkObjId="SW-112394_0" Pin0InfoVect1LinkObjId="SW-112393_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2cf1220_0" Pin1InfoVect1LinkObjId="SM-CX_AP.P5_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1062,-231 1062,-324 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cfa7f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1157,-430 1157,-452 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="21495@0" ObjectIDZND0="21447@0" Pin0InfoVect0LinkObjId="g_25639c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112395_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1157,-430 1157,-452 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cfaa50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1166,-240 1157,-240 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="breaker" EndDevType2="switch" ObjectIDND0="g_2cf9ac0@0" ObjectIDZND0="g_2cf47a0@0" ObjectIDZND1="21495@x" ObjectIDZND2="21496@x" Pin0InfoVect0LinkObjId="g_2cf47a0_0" Pin0InfoVect1LinkObjId="SW-112395_0" Pin0InfoVect2LinkObjId="SW-112396_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2cf9ac0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1166,-240 1157,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cfacb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1157,-193 1157,-178 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_2cf47a0@0" ObjectIDZND0="g_2cfaf10@0" Pin0InfoVect0LinkObjId="g_2cfaf10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2cf47a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1157,-193 1157,-178 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cfbf90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1158,-134 1158,-121 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_2cf28a0@0" Pin0InfoVect0LinkObjId="g_2cf28a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1158,-134 1158,-121 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cd3ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1359,-431 1359,-453 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="21497@0" ObjectIDZND0="21448@0" Pin0InfoVect0LinkObjId="g_2c426c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112397_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1359,-431 1359,-453 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cd3d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1368,-240 1359,-240 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="breaker" ObjectIDND0="g_2cd2e80@0" ObjectIDZND0="g_2cfd400@0" ObjectIDZND1="21498@x" ObjectIDZND2="21497@x" Pin0InfoVect0LinkObjId="g_2cfd400_0" Pin0InfoVect1LinkObjId="SW-112398_0" Pin0InfoVect2LinkObjId="SW-112397_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2cd2e80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1368,-240 1359,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cd3f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1359,-194 1359,-179 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_2cfd400@0" ObjectIDZND0="g_2cd41f0@0" Pin0InfoVect0LinkObjId="g_2cd41f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2cfd400_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1359,-194 1359,-179 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cd51a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1360,-135 1360,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_2cfc1f0@0" Pin0InfoVect0LinkObjId="g_2cfc1f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1360,-135 1360,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cd5400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1230,-439 1230,-452 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="21511@0" ObjectIDZND0="21447@0" Pin0InfoVect0LinkObjId="g_25639c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112411_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1230,-439 1230,-452 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cd5660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1230,-347 1230,-310 1296,-310 1296,-366 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="21511@1" ObjectIDZND0="21512@0" Pin0InfoVect0LinkObjId="SW-112412_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112411_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1230,-347 1230,-310 1296,-310 1296,-366 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cd58b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1296,-435 1296,-453 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="21512@1" ObjectIDZND0="21448@0" Pin0InfoVect0LinkObjId="g_2c426c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112412_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1296,-435 1296,-453 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cd6b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1907,-278 1907,-268 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21508@0" ObjectIDZND0="g_2cd6340@0" Pin0InfoVect0LinkObjId="g_2cd6340_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112408_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1907,-278 1907,-268 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cd73f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1907,-314 1907,-323 1878,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="lightningRod" EndDevType2="generator" ObjectIDND0="21508@1" ObjectIDZND0="21507@x" ObjectIDZND1="g_2cdc8a0@0" ObjectIDZND2="43370@x" Pin0InfoVect0LinkObjId="SW-112407_0" Pin0InfoVect1LinkObjId="g_2cdc8a0_0" Pin0InfoVect2LinkObjId="SM-CX_AP.P10_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112408_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1907,-314 1907,-323 1878,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cdbcc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1878,-429 1878,-453 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="21507@0" ObjectIDZND0="21448@0" Pin0InfoVect0LinkObjId="g_2c426c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112407_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1878,-429 1878,-453 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cdbf20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1878,-323 1878,-337 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="generator" EndDevType0="breaker" ObjectIDND0="21508@x" ObjectIDND1="g_2cdc8a0@0" ObjectIDND2="43370@x" ObjectIDZND0="21507@1" Pin0InfoVect0LinkObjId="SW-112407_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-112408_0" Pin1InfoVect1LinkObjId="g_2cdc8a0_0" Pin1InfoVect2LinkObjId="SM-CX_AP.P10_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1878,-323 1878,-337 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cdc180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1885,-230 1878,-230 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="switch" EndDevType2="generator" ObjectIDND0="g_2cdc8a0@0" ObjectIDZND0="21507@x" ObjectIDZND1="21508@x" ObjectIDZND2="43370@x" Pin0InfoVect0LinkObjId="SW-112407_0" Pin0InfoVect1LinkObjId="SW-112408_0" Pin0InfoVect2LinkObjId="SM-CX_AP.P10_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2cdc8a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1885,-230 1878,-230 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cdc3e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1878,-102 1878,-230 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" EndDevType0="lightningRod" EndDevType1="breaker" EndDevType2="switch" ObjectIDND0="43370@0" ObjectIDZND0="g_2cdc8a0@0" ObjectIDZND1="21507@x" ObjectIDZND2="21508@x" Pin0InfoVect0LinkObjId="g_2cdc8a0_0" Pin0InfoVect1LinkObjId="SW-112407_0" Pin0InfoVect2LinkObjId="SW-112408_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-CX_AP.P10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1878,-102 1878,-230 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cdc640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1878,-230 1878,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="generator" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="g_2cdc8a0@0" ObjectIDND1="43370@x" ObjectIDZND0="21507@x" ObjectIDZND1="21508@x" Pin0InfoVect0LinkObjId="SW-112407_0" Pin0InfoVect1LinkObjId="SW-112408_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2cdc8a0_0" Pin1InfoVect1LinkObjId="SM-CX_AP.P10_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1878,-230 1878,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d042c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2029,-275 2029,-265 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21510@0" ObjectIDZND0="g_2d03890@0" Pin0InfoVect0LinkObjId="g_2d03890_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112410_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2029,-275 2029,-265 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d09060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2001,-427 2001,-453 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="21509@0" ObjectIDZND0="21448@0" Pin0InfoVect0LinkObjId="g_2c426c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112409_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2001,-427 2001,-453 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d0ad00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2001,-184 2001,-210 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2d0af60@0" ObjectIDZND0="g_2d0a3d0@0" Pin0InfoVect0LinkObjId="g_2d0a3d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d0af60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2001,-184 2001,-210 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d0cbd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2030,-314 2030,-323 2001,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="21510@1" ObjectIDZND0="21509@x" ObjectIDZND1="g_2d09910@0" ObjectIDZND2="g_2d0a3d0@0" Pin0InfoVect0LinkObjId="SW-112409_0" Pin0InfoVect1LinkObjId="g_2d09910_0" Pin0InfoVect2LinkObjId="g_2d0a3d0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112410_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2030,-314 2030,-323 2001,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d0ce30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2001,-335 2001,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="21509@1" ObjectIDZND0="21510@x" ObjectIDZND1="g_2d09910@0" ObjectIDZND2="g_2d0a3d0@0" Pin0InfoVect0LinkObjId="SW-112410_0" Pin0InfoVect1LinkObjId="g_2d09910_0" Pin0InfoVect2LinkObjId="g_2d0a3d0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112409_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2001,-335 2001,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d0d090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1965,-292 1965,-309 2001,-309 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2d09910@0" ObjectIDZND0="21509@x" ObjectIDZND1="21510@x" ObjectIDZND2="g_2d0a3d0@0" Pin0InfoVect0LinkObjId="SW-112409_0" Pin0InfoVect1LinkObjId="SW-112410_0" Pin0InfoVect2LinkObjId="g_2d0a3d0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d09910_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1965,-292 1965,-309 2001,-309 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d0d2f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2001,-309 2001,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="g_2d09910@0" ObjectIDND1="g_2d0a3d0@0" ObjectIDZND0="21509@x" ObjectIDZND1="21510@x" Pin0InfoVect0LinkObjId="SW-112409_0" Pin0InfoVect1LinkObjId="SW-112410_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2d09910_0" Pin1InfoVect1LinkObjId="g_2d0a3d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2001,-309 2001,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d0d550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2001,-248 2001,-309 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2d0a3d0@1" ObjectIDZND0="21509@x" ObjectIDZND1="21510@x" ObjectIDZND2="g_2d09910@0" Pin0InfoVect0LinkObjId="SW-112409_0" Pin0InfoVect1LinkObjId="SW-112410_0" Pin0InfoVect2LinkObjId="g_2d09910_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d0a3d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2001,-248 2001,-309 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2d15560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1969,-157 1969,-168 2001,-168 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_2d0be60@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d0be60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1969,-157 1969,-168 2001,-168 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d1b140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1157,-227 1157,-244 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2cf47a0@1" ObjectIDZND0="21495@x" ObjectIDZND1="21496@x" ObjectIDZND2="g_2cf9ac0@0" Pin0InfoVect0LinkObjId="SW-112395_0" Pin0InfoVect1LinkObjId="SW-112396_0" Pin0InfoVect2LinkObjId="g_2cf9ac0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2cf47a0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1157,-227 1157,-244 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d1bdd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1187,-280 1187,-270 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21496@0" ObjectIDZND0="g_2d1b3a0@0" Pin0InfoVect0LinkObjId="g_2d1b3a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112396_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1187,-280 1187,-270 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d1c030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1187,-316 1187,-325 1158,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="21496@1" ObjectIDZND0="21495@x" ObjectIDZND1="g_2cf47a0@0" ObjectIDZND2="g_2cf9ac0@0" Pin0InfoVect0LinkObjId="SW-112395_0" Pin0InfoVect1LinkObjId="g_2cf47a0_0" Pin0InfoVect2LinkObjId="g_2cf9ac0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112396_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1187,-316 1187,-325 1158,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d1f2e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1157,-338 1157,-322 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="21495@1" ObjectIDZND0="g_2cf47a0@0" ObjectIDZND1="g_2cf9ac0@0" ObjectIDZND2="21496@x" Pin0InfoVect0LinkObjId="g_2cf47a0_0" Pin0InfoVect1LinkObjId="g_2cf9ac0_0" Pin0InfoVect2LinkObjId="SW-112396_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112395_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1157,-338 1157,-322 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d1f540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1157,-322 1157,-244 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="21495@x" ObjectIDND1="21496@x" ObjectIDZND0="g_2cf47a0@0" ObjectIDZND1="g_2cf9ac0@0" Pin0InfoVect0LinkObjId="g_2cf47a0_0" Pin0InfoVect1LinkObjId="g_2cf9ac0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-112395_0" Pin1InfoVect1LinkObjId="SW-112396_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1157,-322 1157,-244 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d1fdd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1389,-281 1389,-271 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21498@0" ObjectIDZND0="g_2d227a0@0" Pin0InfoVect0LinkObjId="g_2d227a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112398_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1389,-281 1389,-271 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d1ffc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1389,-317 1389,-326 1360,-326 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="21498@1" ObjectIDZND0="21497@x" ObjectIDZND1="g_2cfd400@0" ObjectIDZND2="g_2cd2e80@0" Pin0InfoVect0LinkObjId="SW-112397_0" Pin0InfoVect1LinkObjId="g_2cfd400_0" Pin0InfoVect2LinkObjId="g_2cd2e80_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112398_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1389,-317 1389,-326 1360,-326 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d23ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1359,-228 1359,-240 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" EndDevType2="lightningRod" ObjectIDND0="g_2cfd400@1" ObjectIDZND0="21498@x" ObjectIDZND1="21497@x" ObjectIDZND2="g_2cd2e80@0" Pin0InfoVect0LinkObjId="SW-112398_0" Pin0InfoVect1LinkObjId="SW-112397_0" Pin0InfoVect2LinkObjId="g_2cd2e80_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2cfd400_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1359,-228 1359,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d245b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1359,-339 1359,-326 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="21497@1" ObjectIDZND0="21498@x" ObjectIDZND1="g_2cfd400@0" ObjectIDZND2="g_2cd2e80@0" Pin0InfoVect0LinkObjId="SW-112398_0" Pin0InfoVect1LinkObjId="g_2cfd400_0" Pin0InfoVect2LinkObjId="g_2cd2e80_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-112397_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1359,-339 1359,-326 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d24810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1359,-326 1359,-240 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="21498@x" ObjectIDND1="21497@x" ObjectIDZND0="g_2cfd400@0" ObjectIDZND1="g_2cd2e80@0" Pin0InfoVect0LinkObjId="g_2cfd400_0" Pin0InfoVect1LinkObjId="g_2cd2e80_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-112398_0" Pin1InfoVect1LinkObjId="SW-112397_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1359,-326 1359,-240 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Polygon_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="none" points="575,-154 566,-142 557,-154 575,-154 " stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-111495" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 253.000000 -1133.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21420" ObjectName="DYN-CX_AP"/>
     <cge:Meas_Ref ObjectId="111495"/>
    </metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_TYGF" endPointId="0" endStationName="CX_AP" flowDrawDirect="1" flowShape="0" id="AC-220kV.TaoA_line" runFlow="0">
    <g class="BV-220KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1311,-1221 1311,-1255 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="46457" ObjectName="AC-220kV.TaoA_line"/>
    <cge:TPSR_Ref TObjectID="46457_SS-166"/></metadata>
   <polyline fill="none" opacity="0" points="1311,-1221 1311,-1255 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_AP" endPointId="0" endStationName="CX_STP" flowDrawDirect="1" flowShape="0" id="AC-220kV.taia_line" runFlow="0">
    <g class="BV-220KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="806,-1227 806,-1260 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="25324" ObjectName="AC-220kV.taia_line"/>
    <cge:TPSR_Ref TObjectID="25324_SS-166"/></metadata>
   <polyline fill="none" opacity="0" points="806,-1227 806,-1260 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_AP"/>
</svg>