<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-87" aopId="256" id="thSvg" viewBox="3114 -1200 2123 1201">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="0" x2="12" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="9" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="1" y2="1"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape123">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="2" y2="2"/>
    <ellipse cx="8" cy="8" rx="8.5" ry="7.5" stroke-width="0.155709"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="25" x2="20" y1="8" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="25" x2="20" y1="8" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="20" x2="20" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="8" x2="5" y1="7" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="10" x2="8" y1="9" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="8" x2="8" y1="4" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="14" x2="11" y1="18" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="16" x2="14" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="14" x2="14" y1="15" y2="18"/>
    <ellipse cx="19" cy="8" rx="8.5" ry="7.5" stroke-width="0.155709"/>
    <ellipse cx="14" cy="16" rx="9" ry="7.5" stroke-width="0.155709"/>
   </symbol>
   <symbol id="lightningRod:shape133">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="77" x2="117" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="117" x2="117" y1="85" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="83" x2="117" y1="85" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="77" x2="77" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="34" x2="54" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="34" x2="54" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="44" x2="44" y1="20" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="10" x2="10" y1="2" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="0" x2="20" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="0" x2="20" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="10" x2="10" y1="20" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="77" x2="77" y1="2" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="67" x2="87" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="67" x2="87" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="77" x2="77" y1="20" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="77" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="44" x2="44" y1="2" y2="12"/>
    <ellipse cx="83" cy="103" rx="13" ry="12.5" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="79" x2="83" y1="89" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="83" x2="87" y1="85" y2="89"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="83" x2="83" y1="81" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="79" x2="83" y1="109" y2="105"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="83" x2="87" y1="105" y2="109"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="83" x2="83" y1="101" y2="105"/>
    <circle cx="83" cy="85" r="13" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="81" x2="85" y1="48" y2="48"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="83" x2="83" y1="73" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="74" x2="92" y1="55" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="80" x2="87" y1="52" y2="52"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="6" y2="41"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="63" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="55" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="56" y2="56"/>
   </symbol>
   <symbol id="lightningRod:shape136">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="2" y2="2"/>
    <ellipse cx="19" cy="8" rx="8.5" ry="7.5" stroke-width="0.155709"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="8" x2="8" y1="4" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="10" x2="8" y1="9" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="8" x2="5" y1="7" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="20" x2="20" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="25" x2="20" y1="8" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="25" x2="20" y1="8" y2="10"/>
    <ellipse cx="8" cy="8" rx="8.5" ry="7.5" stroke-width="0.155709"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape19_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="50" y2="42"/>
    <rect height="4" stroke-width="1" width="19" x="7" y="26"/>
    <polyline fill="none" points="27,39 5,17 5,5 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="9" y1="41" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="42"/>
    <rect height="4" stroke-width="1" width="19" x="-15" y="26"/>
    <polyline fill="none" points="-16,39 6,17 6,5 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="2" y1="41" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="50" y2="42"/>
    <rect height="4" stroke-width="1" width="19" x="7" y="26"/>
    <polyline fill="none" points="27,39 5,17 5,5 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="9" y1="41" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="42"/>
    <rect height="4" stroke-width="1" width="19" x="-15" y="26"/>
    <polyline fill="none" points="-16,39 6,17 6,5 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="2" y1="41" y2="41"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="transformer2:shape0_0">
    <circle cx="25" cy="29" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="17" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="34" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="17" x2="34" y1="18" y2="18"/>
   </symbol>
   <symbol id="transformer2:shape0_1">
    <ellipse cx="25" cy="60" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="24" y1="58" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="32" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="16" x2="24" y1="74" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape11_0">
    <ellipse cx="13" cy="34" rx="13" ry="12.5" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="40" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="36" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="32" y2="36"/>
   </symbol>
   <symbol id="transformer2:shape11_1">
    <circle cx="13" cy="16" r="13" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="20" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="16" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="12" y2="16"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">开关检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注1</text>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注2</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">禁止操作</text>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="78" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.246377 -0.000000 0.000000 -1.035714 2.739130 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="32" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(0.512795 -0.000000 0.000000 -1.035714 2.846957 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape28">
    
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(0,0,0)" height="1211" width="2133" x="3109" y="-1205"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(0,0,0)" stroke-width="1" width="360" x="3115" y="-1079"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(0,0,0)" stroke-width="1" width="360" x="3115" y="-1199"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(0,0,0)" stroke-width="1" width="360" x="3115" y="-599"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-60185">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4252.000000 -901.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11597" ObjectName="SW-LF_YJ.LF_YJ_3311SW"/>
     <cge:Meas_Ref ObjectId="60185"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-60220">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4252.000000 -617.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11632" ObjectName="SW-LF_YJ.LF_YJ_4311SW"/>
     <cge:Meas_Ref ObjectId="60220"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-60183">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4378.000000 -975.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11595" ObjectName="SW-LF_YJ.LF_YJ_3011SW"/>
     <cge:Meas_Ref ObjectId="60183"/>
    <cge:TPSR_Ref TObjectID="11595"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3957.000000 -879.000000)" xlink:href="#switch2:shape19-UnNor1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-60186">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4100.000000 -903.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11598" ObjectName="SW-LF_YJ.LF_YJ_3601SW"/>
     <cge:Meas_Ref ObjectId="60186"/>
    <cge:TPSR_Ref TObjectID="11598"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4296.000000 -636.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-60184">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4473.000000 -899.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11596" ObjectName="SW-LF_YJ.LF_YJ_3211SW"/>
     <cge:Meas_Ref ObjectId="60184"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-60219">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4473.000000 -617.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11631" ObjectName="SW-LF_YJ.LF_YJ_4211SW"/>
     <cge:Meas_Ref ObjectId="60219"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4517.000000 -636.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-60218">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4070.000000 -634.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11630" ObjectName="SW-LF_YJ.LF_YJ_4151SW"/>
     <cge:Meas_Ref ObjectId="60218"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-60217">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3768.000000 -536.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11629" ObjectName="SW-LF_YJ.LF_YJ_4141SW"/>
     <cge:Meas_Ref ObjectId="60217"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-60216">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3924.000000 -536.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11628" ObjectName="SW-LF_YJ.LF_YJ_4131SW"/>
     <cge:Meas_Ref ObjectId="60216"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-60207">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4166.000000 -538.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11619" ObjectName="SW-LF_YJ.LF_YJ_4091SW"/>
     <cge:Meas_Ref ObjectId="60207"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-60208">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4166.000000 -396.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11620" ObjectName="SW-LF_YJ.LF_YJ_4092SW"/>
     <cge:Meas_Ref ObjectId="60208"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-60197">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4704.000000 -538.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11609" ObjectName="SW-LF_YJ.LF_YJ_4051SW"/>
     <cge:Meas_Ref ObjectId="60197"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-60193">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4928.000000 -537.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11605" ObjectName="SW-LF_YJ.LF_YJ_4031SW"/>
     <cge:Meas_Ref ObjectId="60193"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-60213">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3542.000000 -536.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11625" ObjectName="SW-LF_YJ.LF_YJ_4121SW"/>
     <cge:Meas_Ref ObjectId="60213"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-60214">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3542.000000 -440.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11626" ObjectName="SW-LF_YJ.LF_YJ_4122SW"/>
     <cge:Meas_Ref ObjectId="60214"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4688.000000 -902.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4688.000000 -614.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4732.000000 -633.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4414.000000 -1000.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-60204">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4262.000000 -536.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11616" ObjectName="SW-LF_YJ.LF_YJ_4081SW"/>
     <cge:Meas_Ref ObjectId="60204"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-60201">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4364.000000 -536.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11613" ObjectName="SW-LF_YJ.LF_YJ_4071SW"/>
     <cge:Meas_Ref ObjectId="60201"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-60198">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4479.000000 -536.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11610" ObjectName="SW-LF_YJ.LF_YJ_4061SW"/>
     <cge:Meas_Ref ObjectId="60198"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-60202">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4364.000000 -440.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11614" ObjectName="SW-LF_YJ.LF_YJ_4072SW"/>
     <cge:Meas_Ref ObjectId="60202"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-60205">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4262.000000 -440.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11617" ObjectName="SW-LF_YJ.LF_YJ_4082SW"/>
     <cge:Meas_Ref ObjectId="60205"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-60206">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4297.000000 -319.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11618" ObjectName="SW-LF_YJ.LF_YJ_4084SW"/>
     <cge:Meas_Ref ObjectId="60206"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-60203">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4399.000000 -319.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11615" ObjectName="SW-LF_YJ.LF_YJ_4074SW"/>
     <cge:Meas_Ref ObjectId="60203"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-60200">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4514.000000 -319.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11612" ObjectName="SW-LF_YJ.LF_YJ_4064SW"/>
     <cge:Meas_Ref ObjectId="60200"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-60196">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4833.000000 -319.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11608" ObjectName="SW-LF_YJ.LF_YJ_4044SW"/>
     <cge:Meas_Ref ObjectId="60196"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-60192">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5040.000000 -319.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11604" ObjectName="SW-LF_YJ.LF_YJ_4024SW"/>
     <cge:Meas_Ref ObjectId="60192"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-60189">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5155.000000 -319.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11601" ObjectName="SW-LF_YJ.LF_YJ_4014SW"/>
     <cge:Meas_Ref ObjectId="60189"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-60199">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4479.000000 -440.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11611" ObjectName="SW-LF_YJ.LF_YJ_4062SW"/>
     <cge:Meas_Ref ObjectId="60199"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-60195">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4798.000000 -440.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11607" ObjectName="SW-LF_YJ.LF_YJ_4042SW"/>
     <cge:Meas_Ref ObjectId="60195"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-60191">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5005.000000 -440.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11603" ObjectName="SW-LF_YJ.LF_YJ_4022SW"/>
     <cge:Meas_Ref ObjectId="60191"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-60188">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5120.000000 -440.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11600" ObjectName="SW-LF_YJ.LF_YJ_4012SW"/>
     <cge:Meas_Ref ObjectId="60188"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-60212">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3664.000000 -319.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11624" ObjectName="SW-LF_YJ.LF_YJ_4104SW"/>
     <cge:Meas_Ref ObjectId="60212"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-60211">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3629.000000 -440.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11623" ObjectName="SW-LF_YJ.LF_YJ_4102SW"/>
     <cge:Meas_Ref ObjectId="60211"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-60210">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3629.000000 -536.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11622" ObjectName="SW-LF_YJ.LF_YJ_4101SW"/>
     <cge:Meas_Ref ObjectId="60210"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-60215">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3577.000000 -319.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11627" ObjectName="SW-LF_YJ.LF_YJ_4124SW"/>
     <cge:Meas_Ref ObjectId="60215"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5120.000000 -374.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5005.000000 -374.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4798.000000 -374.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4479.000000 -374.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4364.000000 -374.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4262.000000 -374.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3542.000000 -374.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3629.000000 -374.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-60194">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4798.000000 -536.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11606" ObjectName="SW-LF_YJ.LF_YJ_4041SW"/>
     <cge:Meas_Ref ObjectId="60194"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-60190">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5005.000000 -536.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11602" ObjectName="SW-LF_YJ.LF_YJ_4021SW"/>
     <cge:Meas_Ref ObjectId="60190"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-60187">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5120.000000 -536.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11599" ObjectName="SW-LF_YJ.LF_YJ_4011SW"/>
     <cge:Meas_Ref ObjectId="60187"/>
    <cge:TPSR_Ref TObjectID="11599"/></metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4236.000000 -755.000000)" xlink:href="#transformer2:shape0_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4236.000000 -755.000000)" xlink:href="#transformer2:shape0_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-LF_YJ.LF_YJ_1Zyb">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="16282"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3949.000000 -794.000000)" xlink:href="#transformer2:shape11_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3949.000000 -794.000000)" xlink:href="#transformer2:shape11_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="17468" ObjectName="TF-LF_YJ.LF_YJ_1Zyb"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-LF_YJ.LF_YJ_1Zyb">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="16282"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4700.000000 -425.000000)" xlink:href="#transformer2:shape11_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4700.000000 -425.000000)" xlink:href="#transformer2:shape11_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="17468" ObjectName="TF-LF_YJ.LF_YJ_1Zyb"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4672.000000 -752.000000)" xlink:href="#transformer2:shape0_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4672.000000 -752.000000)" xlink:href="#transformer2:shape0_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_120c160">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4064.000000 -825.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_120cb70">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4100.000000 -829.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_120d540">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4096.000000 -792.000000)" xlink:href="#lightningRod:shape123"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1224200">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3733.000000 -336.000000)" xlink:href="#lightningRod:shape133"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1203a30">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3889.000000 -336.000000)" xlink:href="#lightningRod:shape133"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1232bd0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4401.333333 -436.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1234920">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4385.333333 -212.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1237e00">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4516.333333 -436.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1239b50">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4500.333333 -212.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_123f0e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4203.333333 -479.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1242ee0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4208.000000 -323.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1246820">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4704.000000 -486.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_124d420">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4835.333333 -436.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_124f150">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4819.333333 -212.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1252310">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4928.000000 -482.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12531e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4891.000000 -464.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1253ec0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4924.000000 -404.000000)" xlink:href="#lightningRod:shape123"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1255ed0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4951.000000 -449.000000)" xlink:href="#lightningRod:shape136"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_125e440">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3580.000000 -436.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1260290">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3564.000000 -212.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1264f80">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3666.333333 -436.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1266dd0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3650.333333 -212.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_126adb0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4299.333333 -436.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_126cc00">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4283.333333 -212.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1272e60">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5042.333333 -436.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1274cb0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5026.333333 -212.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_127af10">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5157.333333 -436.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_127cd60">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5141.333333 -212.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 0.000000 0.000000 2.335135 3236.000000 -1118.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-79513" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3540.000000 -156.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79513" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17465"/>
     <cge:Term_Ref ObjectID="16190"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-79514" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3540.000000 -156.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79514" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17465"/>
     <cge:Term_Ref ObjectID="16190"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="1" id="ME-79512" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3540.000000 -156.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79512" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17465"/>
     <cge:Term_Ref ObjectID="16190"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-79509" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3698.000000 -156.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79509" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17466"/>
     <cge:Term_Ref ObjectID="16192"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-79510" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3698.000000 -156.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79510" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17466"/>
     <cge:Term_Ref ObjectID="16192"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="1" id="ME-79508" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3698.000000 -156.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79508" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17466"/>
     <cge:Term_Ref ObjectID="16192"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-79523" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3971.000000 -238.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79523" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17467"/>
     <cge:Term_Ref ObjectID="16194"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-79520" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3971.000000 -238.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79520" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17467"/>
     <cge:Term_Ref ObjectID="16194"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="1" id="ME-79521" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3971.000000 -238.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79521" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17467"/>
     <cge:Term_Ref ObjectID="16194"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="1" id="ME-79522" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3971.000000 -238.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79522" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17467"/>
     <cge:Term_Ref ObjectID="16194"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-79517" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4192.000000 -260.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79517" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17458"/>
     <cge:Term_Ref ObjectID="16176"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-79518" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4192.000000 -260.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79518" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17458"/>
     <cge:Term_Ref ObjectID="16176"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="1" id="ME-79516" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4192.000000 -260.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79516" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17458"/>
     <cge:Term_Ref ObjectID="16176"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-79505" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4255.000000 -156.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79505" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17464"/>
     <cge:Term_Ref ObjectID="16188"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-79506" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4255.000000 -156.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79506" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17464"/>
     <cge:Term_Ref ObjectID="16188"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="1" id="ME-79504" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4255.000000 -156.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79504" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17464"/>
     <cge:Term_Ref ObjectID="16188"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-79501" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4416.000000 -156.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79501" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17463"/>
     <cge:Term_Ref ObjectID="16186"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-79502" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4416.000000 -156.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79502" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17463"/>
     <cge:Term_Ref ObjectID="16186"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="1" id="ME-79500" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4416.000000 -156.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79500" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17463"/>
     <cge:Term_Ref ObjectID="16186"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-79497" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4576.000000 -156.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79497" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17462"/>
     <cge:Term_Ref ObjectID="16184"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-79498" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4576.000000 -156.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79498" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17462"/>
     <cge:Term_Ref ObjectID="16184"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="1" id="ME-79496" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4576.000000 -156.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79496" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17462"/>
     <cge:Term_Ref ObjectID="16184"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-79493" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4836.000000 -156.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79493" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17461"/>
     <cge:Term_Ref ObjectID="16182"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-79494" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4836.000000 -156.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79494" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17461"/>
     <cge:Term_Ref ObjectID="16182"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="1" id="ME-79492" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4836.000000 -156.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79492" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17461"/>
     <cge:Term_Ref ObjectID="16182"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-79489" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5021.000000 -156.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79489" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17460"/>
     <cge:Term_Ref ObjectID="16180"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-79490" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5021.000000 -156.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79490" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17460"/>
     <cge:Term_Ref ObjectID="16180"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="1" id="ME-79488" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5021.000000 -156.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79488" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17460"/>
     <cge:Term_Ref ObjectID="16180"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-79485" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5202.000000 -156.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79485" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17459"/>
     <cge:Term_Ref ObjectID="16178"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-79486" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5202.000000 -156.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79486" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17459"/>
     <cge:Term_Ref ObjectID="16178"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="1" id="ME-79484" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5202.000000 -156.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79484" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17459"/>
     <cge:Term_Ref ObjectID="16178"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-79479" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4390.000000 -747.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79479" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11639"/>
     <cge:Term_Ref ObjectID="16174"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-79480" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4390.000000 -747.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79480" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11639"/>
     <cge:Term_Ref ObjectID="16174"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-79477" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4390.000000 -747.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79477" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11639"/>
     <cge:Term_Ref ObjectID="16174"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-79474" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4391.000000 -884.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79474" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11637"/>
     <cge:Term_Ref ObjectID="16172"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-79475" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4391.000000 -884.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79475" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11637"/>
     <cge:Term_Ref ObjectID="16172"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-79472" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4391.000000 -884.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79472" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11637"/>
     <cge:Term_Ref ObjectID="16172"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-79464" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4609.000000 -884.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79464" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11633"/>
     <cge:Term_Ref ObjectID="16168"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-79465" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4609.000000 -884.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79465" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11633"/>
     <cge:Term_Ref ObjectID="16168"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-79462" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4609.000000 -884.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79462" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11633"/>
     <cge:Term_Ref ObjectID="16168"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-79469" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4614.000000 -745.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79469" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11635"/>
     <cge:Term_Ref ObjectID="16170"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-79470" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4614.000000 -745.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79470" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11635"/>
     <cge:Term_Ref ObjectID="16170"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-79467" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4614.000000 -745.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79467" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11635"/>
     <cge:Term_Ref ObjectID="16170"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -1025.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -1025.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -1025.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -1025.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -1025.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -1025.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -1025.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" transform="matrix(1.000000 0.000000 0.000000 1.000000 3283.000000 -1166.500000) translate(0,16)">羊街变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4341.000000 -1143.000000) translate(0,18)">35kV羊街线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4275.000000 -885.000000) translate(0,15)">331</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4291.000000 -812.000000) translate(0,18)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 3508.000000 -633.000000) translate(0,18)">10kVI母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 3888.000000 -989.000000) translate(0,18)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 3914.000000 -793.000000) translate(0,18)">35kV站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4062.000000 -781.000000) translate(0,18)">35kV TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4455.000000 -809.000000) translate(0,18)">已拆除</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 3749.666667 -280.000000) translate(0,18)">设备停用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3786.000000 -520.000000) translate(0,15)">414</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 3890.000000 -280.000000) translate(0,18)">电容器450千乏</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4154.333333 -296.000000) translate(0,18)">旁路</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4684.000000 -415.000000) translate(0,18)">站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 3592.000000 -289.000000) translate(0,18)">磷</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 3592.000000 -289.000000) translate(0,40)">肥</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 3592.000000 -289.000000) translate(0,62)">II</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 3592.000000 -289.000000) translate(0,84)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 3592.000000 -289.000000) translate(0,106)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 3680.333333 -272.000000) translate(0,18)">磷</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 3680.333333 -272.000000) translate(0,40)">肥</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 3680.333333 -272.000000) translate(0,62)">I</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 3680.333333 -272.000000) translate(0,84)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 3680.333333 -272.000000) translate(0,106)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4311.333333 -272.000000) translate(0,18)">铁</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4311.333333 -272.000000) translate(0,40)">路</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4311.333333 -272.000000) translate(0,62)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4412.333333 -273.000000) translate(0,18)">勤</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4412.333333 -273.000000) translate(0,40)">丰</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4412.333333 -273.000000) translate(0,62)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4525.333333 -284.000000) translate(0,18)">六</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4525.333333 -284.000000) translate(0,40)">七</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4525.333333 -284.000000) translate(0,62)">六</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4525.333333 -284.000000) translate(0,84)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4849.333333 -274.000000) translate(0,18)">云</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4849.333333 -274.000000) translate(0,40)">铜</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4849.333333 -274.000000) translate(0,62)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 5167.333333 -289.000000) translate(0,18)">四</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 5167.333333 -289.000000) translate(0,40)">八</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 5167.333333 -289.000000) translate(0,62)">七</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 5167.333333 -289.000000) translate(0,84)">六</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 5167.333333 -289.000000) translate(0,106)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 5058.333333 -283.000000) translate(0,18)">三</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 5058.333333 -283.000000) translate(0,40)">二</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 5058.333333 -283.000000) translate(0,62)">九</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 5058.333333 -283.000000) translate(0,84)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4711.000000 -885.000000) translate(0,15)">311</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4711.000000 -723.000000) translate(0,15)">411</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4727.000000 -809.000000) translate(0,18)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 5134.000000 -633.000000) translate(0,18)">10kVII母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3564.000000 -521.000000) translate(0,15)">412</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3558.000000 -568.000000) translate(0,15)">4121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3648.000000 -519.000000) translate(0,15)">410</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3645.000000 -568.000000) translate(0,15)">4101</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3645.000000 -472.000000) translate(0,15)">4102</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3558.000000 -472.000000) translate(0,15)">4122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3784.000000 -568.000000) translate(0,15)">4141</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3942.000000 -519.000000) translate(0,15)">413</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3940.000000 -568.000000) translate(0,15)">4131</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4074.000000 -668.000000) translate(0,15)">4151</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4133.000000 -520.000000) translate(0,15)">409</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4182.000000 -570.000000) translate(0,15)">4091</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4281.000000 -519.000000) translate(0,15)">408</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4278.000000 -568.000000) translate(0,15)">4081</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4224.000000 -471.000000) translate(0,15)">4082</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4383.000000 -519.000000) translate(0,15)">407</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4380.000000 -568.000000) translate(0,15)">4071</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4322.000000 -474.000000) translate(0,15)">4072</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4498.000000 -519.000000) translate(0,15)">406</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4495.000000 -568.000000) translate(0,15)">4061</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4438.000000 -478.000000) translate(0,15)">4062</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4817.000000 -519.000000) translate(0,15)">404</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4814.000000 -568.000000) translate(0,15)">4041</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4752.000000 -478.000000) translate(0,15)">4042</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5024.000000 -519.000000) translate(0,15)">402</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5021.000000 -568.000000) translate(0,15)">4021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4962.000000 -494.000000) translate(0,15)">4022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5139.000000 -519.000000) translate(0,15)">401</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5136.000000 -568.000000) translate(0,15)">4011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5076.000000 -470.000000) translate(0,15)">4012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4268.000000 -933.000000) translate(0,15)">3311</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4491.000000 -877.000000) translate(0,15)">321</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4489.000000 -931.000000) translate(0,15)">3211</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4491.000000 -728.000000) translate(0,15)">421</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4422.000000 -653.000000) translate(0,15)">4211</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4270.000000 -728.000000) translate(0,15)">431</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4261.000000 -650.000000) translate(0,15)">4311</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4116.000000 -935.000000) translate(0,15)">3601</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4323.000000 -1012.000000) translate(0,15)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4720.000000 -570.000000) translate(0,15)">4051</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4944.000000 -569.000000) translate(0,15)">4031</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5056.000000 -351.000000) translate(0,15)">4024</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4849.000000 -351.000000) translate(0,15)">4044</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4530.000000 -351.000000) translate(0,15)">4064</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4415.000000 -351.000000) translate(0,15)">4074</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4313.000000 -351.000000) translate(0,15)">4084</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3593.000000 -351.000000) translate(0,15)">4124</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3680.000000 -351.000000) translate(0,15)">4104</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4182.000000 -428.000000) translate(0,15)">4092</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5171.000000 -351.000000) translate(0,15)">4014</text>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-60237">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4252.000000 -851.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11637" ObjectName="SW-LF_YJ.LF_YJ_331BK"/>
     <cge:Meas_Ref ObjectId="60237"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-60239">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4252.000000 -697.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11639" ObjectName="SW-LF_YJ.LF_YJ_431BK"/>
     <cge:Meas_Ref ObjectId="60239"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-60227">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4473.000000 -846.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11633" ObjectName="SW-LF_YJ.LF_YJ_321BK"/>
     <cge:Meas_Ref ObjectId="60227"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-60229">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4473.000000 -697.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11635" ObjectName="SW-LF_YJ.LF_YJ_421BK"/>
     <cge:Meas_Ref ObjectId="60229"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3767.666667 -488.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-79609">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3924.000000 -488.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17467" ObjectName="SW-LF_YJ.LF_YJ_413BK"/>
     <cge:Meas_Ref ObjectId="79609"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-79588">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4364.333333 -488.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17463" ObjectName="SW-LF_YJ.LF_YJ_407BK"/>
     <cge:Meas_Ref ObjectId="79588"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-79583">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4479.333333 -488.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17462" ObjectName="SW-LF_YJ.LF_YJ_406BK"/>
     <cge:Meas_Ref ObjectId="79583"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-79563">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4166.000000 -491.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17458" ObjectName="SW-LF_YJ.LF_YJ_409BK"/>
     <cge:Meas_Ref ObjectId="79563"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-79578">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4798.333333 -488.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17461" ObjectName="SW-LF_YJ.LF_YJ_404BK"/>
     <cge:Meas_Ref ObjectId="79578"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-79600">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3542.000000 -488.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17465" ObjectName="SW-LF_YJ.LF_YJ_412BK"/>
     <cge:Meas_Ref ObjectId="79600"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-79605">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3629.333333 -488.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17466" ObjectName="SW-LF_YJ.LF_YJ_410BK"/>
     <cge:Meas_Ref ObjectId="79605"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-79595">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4262.333333 -488.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17464" ObjectName="SW-LF_YJ.LF_YJ_408BK"/>
     <cge:Meas_Ref ObjectId="79595"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-79573">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5005.333333 -488.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17460" ObjectName="SW-LF_YJ.LF_YJ_402BK"/>
     <cge:Meas_Ref ObjectId="79573"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-79568">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5120.333333 -488.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17459" ObjectName="SW-LF_YJ.LF_YJ_401BK"/>
     <cge:Meas_Ref ObjectId="79568"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4688.000000 -852.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4688.000000 -694.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_13744d0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4417.000000 -976.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1374f60">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4299.000000 -611.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13759f0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4520.000000 -611.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1376480">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4735.000000 -608.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ConnectNode_Layer"/><g id="MotifButton_Layer">
   <g href="lf_索引_接线图.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="3248" y="-1177"/></g>
   <g href="lf_索引_接线图.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3199" y="-1194"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4544.000000 744.000000) translate(0,16)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4560.000000 714.000000) translate(0,16)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4529.000000 729.000000) translate(0,16)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4541.000000 884.000000) translate(0,16)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4557.000000 854.000000) translate(0,16)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4526.000000 869.000000) translate(0,16)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4324.000000 884.000000) translate(0,16)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4340.000000 854.000000) translate(0,16)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4309.000000 869.000000) translate(0,16)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4323.000000 748.000000) translate(0,16)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4339.000000 718.000000) translate(0,16)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4308.000000 733.000000) translate(0,16)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3488.000000 126.000000) translate(0,16)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3457.000000 141.000000) translate(0,16)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3472.000000 156.000000) translate(0,16)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3641.000000 126.000000) translate(0,16)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3610.000000 141.000000) translate(0,16)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3625.000000 156.000000) translate(0,16)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3916.000000 221.666667) translate(0,16)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3916.000000 203.333333) translate(0,16)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3917.000000 185.000000) translate(0,16)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3885.000000 239.000000) translate(0,16)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4140.000000 229.000000) translate(0,16)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4109.000000 244.000000) translate(0,16)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4124.000000 259.000000) translate(0,16)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4202.000000 126.000000) translate(0,16)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4171.000000 141.000000) translate(0,16)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4186.000000 156.000000) translate(0,16)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4365.000000 126.000000) translate(0,16)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4334.000000 141.000000) translate(0,16)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4349.000000 156.000000) translate(0,16)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4519.000000 126.000000) translate(0,16)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4488.000000 141.000000) translate(0,16)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4503.000000 156.000000) translate(0,16)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4782.000000 126.000000) translate(0,16)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4751.000000 141.000000) translate(0,16)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4766.000000 156.000000) translate(0,16)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4967.000000 126.000000) translate(0,16)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4936.000000 141.000000) translate(0,16)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4951.000000 156.000000) translate(0,16)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5147.000000 126.000000) translate(0,16)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5116.000000 141.000000) translate(0,16)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5131.000000 156.000000) translate(0,16)">P(MW):</text>
   <metadata/></g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_1066610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4261,-959 4261,-942 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17455@0" ObjectIDZND0="11597@1" Pin0InfoVect0LinkObjId="SW-60185_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4261,-959 4261,-942 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_11faaa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4261,-906 4261,-886 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="11597@0" ObjectIDZND0="11637@1" Pin0InfoVect0LinkObjId="SW-60237_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-60185_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4261,-906 4261,-886 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1202170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4261,-760 4261,-732 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="11639@1" Pin0InfoVect0LinkObjId="SW-60239_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4261,-760 4261,-732 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1204b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4261,-859 4261,-840 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="11637@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-60237_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4261,-859 4261,-840 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1206d40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4423,-1005 4423,-994 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_13744d0@0" Pin0InfoVect0LinkObjId="g_13744d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4423,-1005 4423,-994 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1208f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3963,-958 3963,-929 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17455@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3963,-958 3963,-929 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1209100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3962,-884 3962,-841 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="0@0" ObjectIDZND0="17468@0" Pin0InfoVect0LinkObjId="g_1247260_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3962,-884 3962,-841 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_120bb90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4109,-959 4109,-944 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17455@0" ObjectIDZND0="11598@1" Pin0InfoVect0LinkObjId="SW-60186_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4109,-959 4109,-944 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_120bd80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4109,-894 4071,-894 4071,-883 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_120cb70@0" ObjectIDND1="11598@x" ObjectIDZND0="g_120c160@0" Pin0InfoVect0LinkObjId="g_120c160_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_120cb70_0" Pin1InfoVect1LinkObjId="SW-60186_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4109,-894 4071,-894 4071,-883 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_120bf70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4109,-908 4109,-894 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="11598@0" ObjectIDZND0="g_120c160@0" ObjectIDZND1="g_120cb70@0" Pin0InfoVect0LinkObjId="g_120c160_0" Pin0InfoVect1LinkObjId="g_120cb70_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-60186_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4109,-908 4109,-894 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_120d160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4109,-894 4109,-865 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_120c160@0" ObjectIDND1="11598@x" ObjectIDZND0="g_120cb70@1" Pin0InfoVect0LinkObjId="g_120cb70_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_120c160_0" Pin1InfoVect1LinkObjId="SW-60186_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4109,-894 4109,-865 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_120d350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4109,-834 4109,-816 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_120cb70@0" ObjectIDZND0="g_120d540@0" Pin0InfoVect0LinkObjId="g_120d540_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_120cb70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4109,-834 4109,-816 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_120eae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4261,-596 4261,-622 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17484@0" ObjectIDZND0="11632@0" Pin0InfoVect0LinkObjId="SW-60220_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1371500_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4261,-596 4261,-622 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1210920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4261,-690 4305,-690 4305,-677 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="11639@x" ObjectIDND1="11632@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-60239_0" Pin1InfoVect1LinkObjId="SW-60220_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4261,-690 4305,-690 4305,-677 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1210b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4305,-641 4305,-629 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_1374f60@0" Pin0InfoVect0LinkObjId="g_1374f60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4305,-641 4305,-629 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1210d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4261,-690 4261,-705 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="11632@x" ObjectIDND1="0@x" ObjectIDZND0="11639@0" Pin0InfoVect0LinkObjId="SW-60239_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-60220_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4261,-690 4261,-705 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1210f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4261,-690 4261,-658 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="11639@x" ObjectIDND1="0@x" ObjectIDZND0="11632@1" Pin0InfoVect0LinkObjId="SW-60220_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-60239_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4261,-690 4261,-658 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1213330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4482,-959 4482,-940 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17455@0" ObjectIDZND0="11596@1" Pin0InfoVect0LinkObjId="SW-60184_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4482,-959 4482,-940 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12151b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4482,-904 4482,-881 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="11596@0" ObjectIDZND0="11633@1" Pin0InfoVect0LinkObjId="SW-60227_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-60184_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4482,-904 4482,-881 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1217030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4482,-760 4482,-732 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="breaker" ObjectIDZND0="11635@1" Pin0InfoVect0LinkObjId="SW-60229_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4482,-760 4482,-732 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12193e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4482,-854 4482,-805 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" ObjectIDND0="11633@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-60227_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4482,-854 4482,-805 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1219600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4482,-596 4482,-622 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17484@0" ObjectIDZND0="11631@0" Pin0InfoVect0LinkObjId="SW-60219_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1371500_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4482,-596 4482,-622 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_121b9b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4482,-690 4526,-690 4526,-677 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="11635@x" ObjectIDND1="11631@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-60229_0" Pin1InfoVect1LinkObjId="SW-60219_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4482,-690 4526,-690 4526,-677 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_121bbd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4526,-641 4526,-629 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_13759f0@0" Pin0InfoVect0LinkObjId="g_13759f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4526,-641 4526,-629 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_121bdf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4482,-690 4482,-705 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="11631@x" ObjectIDND1="0@x" ObjectIDZND0="11635@0" Pin0InfoVect0LinkObjId="SW-60229_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-60219_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4482,-690 4482,-705 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_121c010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4482,-690 4482,-658 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="11635@x" ObjectIDND1="0@x" ObjectIDZND0="11631@1" Pin0InfoVect0LinkObjId="SW-60219_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-60229_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4482,-690 4482,-658 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_121c740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4387,-959 4387,-980 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17455@0" ObjectIDZND0="11595@0" Pin0InfoVect0LinkObjId="SW-60183_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4387,-959 4387,-980 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_121c930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4423,-1041 4423,-1066 4387,-1066 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="11595@x" Pin0InfoVect0LinkObjId="SW-60183_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4423,-1041 4423,-1066 4387,-1066 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_121cb20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4387,-1016 4387,-1066 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="11595@1" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-60183_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4387,-1016 4387,-1066 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_121cd10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4387,-1066 4387,-1107 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="11595@x" ObjectIDND1="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-60183_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4387,-1066 4387,-1107 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1221690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3777,-596 3777,-577 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17456@0" ObjectIDZND0="11629@1" Pin0InfoVect0LinkObjId="SW-60217_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3777,-596 3777,-577 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12233f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3777,-541 3777,-523 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="11629@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-60217_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3777,-541 3777,-523 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1226f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3816,-450 3816,-469 3777,-469 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="lightningRod" ObjectIDND0="g_1224200@0" ObjectIDZND0="0@x" ObjectIDZND1="g_1224200@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_1224200_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1224200_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3816,-450 3816,-469 3777,-469 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_12271b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3777,-496 3777,-467 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_1224200@0" ObjectIDZND1="g_1224200@0" Pin0InfoVect0LinkObjId="g_1224200_0" Pin0InfoVect1LinkObjId="g_1224200_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3777,-496 3777,-467 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1227410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3777,-467 3777,-366 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_1224200@0" ObjectIDZND0="g_1224200@1" Pin0InfoVect0LinkObjId="g_1224200_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_1224200_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3777,-467 3777,-366 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1229c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3933,-596 3933,-577 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17456@0" ObjectIDZND0="11628@1" Pin0InfoVect0LinkObjId="SW-60216_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3933,-596 3933,-577 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_122ba00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3933,-541 3933,-523 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="11628@0" ObjectIDZND0="17467@1" Pin0InfoVect0LinkObjId="SW-79609_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-60216_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3933,-541 3933,-523 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_122fc80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3972,-450 3972,-469 3933,-469 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="lightningRod" ObjectIDND0="g_1203a30@0" ObjectIDZND0="17467@x" ObjectIDZND1="g_1203a30@0" Pin0InfoVect0LinkObjId="SW-79609_0" Pin0InfoVect1LinkObjId="g_1203a30_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1203a30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3972,-450 3972,-469 3933,-469 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_122fee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3933,-496 3933,-469 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="17467@0" ObjectIDZND0="g_1203a30@0" ObjectIDZND1="g_1203a30@0" Pin0InfoVect0LinkObjId="g_1203a30_0" Pin0InfoVect1LinkObjId="g_1203a30_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-79609_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3933,-496 3933,-469 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1230140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3933,-469 3933,-366 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="17467@x" ObjectIDND1="g_1203a30@0" ObjectIDZND0="g_1203a30@1" Pin0InfoVect0LinkObjId="g_1203a30_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-79609_0" Pin1InfoVect1LinkObjId="g_1203a30_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3933,-469 3933,-366 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12303a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4373,-596 4373,-577 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17484@0" ObjectIDZND0="11613@1" Pin0InfoVect0LinkObjId="SW-60201_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1371500_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4373,-596 4373,-577 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1232250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4373,-541 4373,-523 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="11613@0" ObjectIDZND0="17463@1" Pin0InfoVect0LinkObjId="SW-79588_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-60201_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4373,-541 4373,-523 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12324b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4373,-496 4373,-481 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="17463@0" ObjectIDZND0="11614@1" Pin0InfoVect0LinkObjId="SW-60202_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-79588_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4373,-496 4373,-481 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1232710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4373,-432 4408,-432 4408,-441 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="11614@x" ObjectIDND1="0@x" ObjectIDZND0="g_1232bd0@0" Pin0InfoVect0LinkObjId="g_1232bd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-60202_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4373,-432 4408,-432 4408,-441 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1232970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4373,-445 4373,-432 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="11614@0" ObjectIDZND0="g_1232bd0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_1232bd0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-60202_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4373,-445 4373,-432 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1233880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4373,-432 4373,-415 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="11614@x" ObjectIDND1="g_1232bd0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-60202_0" Pin1InfoVect1LinkObjId="g_1232bd0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4373,-432 4373,-415 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1233ae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4373,-379 4373,-369 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="g_1234920@0" ObjectIDZND1="11615@x" Pin0InfoVect0LinkObjId="g_1234920_0" Pin0InfoVect1LinkObjId="SW-60203_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4373,-379 4373,-369 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1233d40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4373,-369 4408,-369 4408,-360 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_1234920@0" ObjectIDND1="0@x" ObjectIDZND0="11615@1" Pin0InfoVect0LinkObjId="SW-60203_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1234920_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4373,-369 4408,-369 4408,-360 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1233fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4408,-324 4408,-308 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="11615@0" ObjectIDZND0="17457@0" Pin0InfoVect0LinkObjId="g_12391d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-60203_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4408,-324 4408,-308 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1234200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4373,-288 4392,-288 4392,-272 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="11615@x" ObjectIDND1="0@x" ObjectIDZND0="g_1234920@0" Pin0InfoVect0LinkObjId="g_1234920_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-60203_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4373,-288 4392,-288 4392,-272 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1234460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4373,-369 4373,-288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="11615@x" ObjectIDND1="0@x" ObjectIDZND0="g_1234920@0" Pin0InfoVect0LinkObjId="g_1234920_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-60203_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4373,-369 4373,-288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12346c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4373,-288 4373,-204 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" ObjectIDND0="11615@x" ObjectIDND1="0@x" ObjectIDND2="g_1234920@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-60203_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_1234920_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4373,-288 4373,-204 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12355d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4488,-596 4488,-577 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17484@0" ObjectIDZND0="11610@1" Pin0InfoVect0LinkObjId="SW-60198_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1371500_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4488,-596 4488,-577 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1237480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4488,-541 4488,-523 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="11610@0" ObjectIDZND0="17462@1" Pin0InfoVect0LinkObjId="SW-79583_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-60198_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4488,-541 4488,-523 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12376e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4488,-496 4488,-481 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="17462@0" ObjectIDZND0="11611@1" Pin0InfoVect0LinkObjId="SW-60199_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-79583_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4488,-496 4488,-481 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1237940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4488,-432 4523,-432 4523,-441 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="11611@x" ObjectIDND1="0@x" ObjectIDZND0="g_1237e00@0" Pin0InfoVect0LinkObjId="g_1237e00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-60199_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4488,-432 4523,-432 4523,-441 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1237ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4488,-445 4488,-432 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="11611@0" ObjectIDZND0="g_1237e00@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_1237e00_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-60199_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4488,-445 4488,-432 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1238ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4488,-432 4488,-415 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_1237e00@0" ObjectIDND1="11611@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1237e00_0" Pin1InfoVect1LinkObjId="SW-60199_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4488,-432 4488,-415 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1238d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4488,-379 4488,-369 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="g_1239b50@0" ObjectIDZND1="11612@x" Pin0InfoVect0LinkObjId="g_1239b50_0" Pin0InfoVect1LinkObjId="SW-60200_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4488,-379 4488,-369 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1238f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4488,-369 4523,-369 4523,-360 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_1239b50@0" ObjectIDND1="0@x" ObjectIDZND0="11612@1" Pin0InfoVect0LinkObjId="SW-60200_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1239b50_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4488,-369 4523,-369 4523,-360 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12391d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4523,-324 4523,-308 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="11612@0" ObjectIDZND0="17457@0" Pin0InfoVect0LinkObjId="g_1233fa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-60200_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4523,-324 4523,-308 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1239430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4488,-288 4507,-288 4507,-270 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="11612@x" ObjectIDND1="0@x" ObjectIDZND0="g_1239b50@0" Pin0InfoVect0LinkObjId="g_1239b50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-60200_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4488,-288 4507,-288 4507,-270 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1239690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4488,-369 4488,-288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="11612@x" ObjectIDND1="0@x" ObjectIDZND0="g_1239b50@0" Pin0InfoVect0LinkObjId="g_1239b50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-60200_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4488,-369 4488,-288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12398f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4488,-288 4488,-204 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" ObjectIDND0="11612@x" ObjectIDND1="0@x" ObjectIDND2="g_1239b50@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-60200_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_1239b50_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4488,-288 4488,-204 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_123cd70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4175,-596 4175,-579 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17484@0" ObjectIDZND0="11619@1" Pin0InfoVect0LinkObjId="SW-60207_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1371500_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4175,-596 4175,-579 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_123ec20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4175,-543 4175,-526 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="11619@0" ObjectIDZND0="17458@1" Pin0InfoVect0LinkObjId="SW-79563_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-60207_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4175,-543 4175,-526 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_123ee80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4175,-475 4211,-475 4211,-483 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="17458@x" ObjectIDND1="11620@x" ObjectIDZND0="g_123f0e0@0" Pin0InfoVect0LinkObjId="g_123f0e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-79563_0" Pin1InfoVect1LinkObjId="SW-60208_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4175,-475 4211,-475 4211,-483 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_123fd90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4175,-499 4175,-475 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="17458@0" ObjectIDZND0="11620@x" ObjectIDZND1="g_123f0e0@0" Pin0InfoVect0LinkObjId="SW-60208_0" Pin0InfoVect1LinkObjId="g_123f0e0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-79563_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4175,-499 4175,-475 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1242560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4175,-437 4175,-475 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="lightningRod" ObjectIDND0="11620@1" ObjectIDZND0="17458@x" ObjectIDZND1="g_123f0e0@0" Pin0InfoVect0LinkObjId="SW-79563_0" Pin0InfoVect1LinkObjId="g_123f0e0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-60208_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4175,-437 4175,-475 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12427c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4175,-392 4215,-392 4215,-381 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="17457@0" ObjectIDND1="11620@x" ObjectIDZND0="g_1242ee0@0" Pin0InfoVect0LinkObjId="g_1242ee0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1233fa0_0" Pin1InfoVect1LinkObjId="SW-60208_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4175,-392 4215,-392 4215,-381 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1242a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4175,-308 4175,-392 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="17457@0" ObjectIDZND0="11620@x" ObjectIDZND1="g_1242ee0@0" Pin0InfoVect0LinkObjId="SW-60208_0" Pin0InfoVect1LinkObjId="g_1242ee0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1233fa0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4175,-308 4175,-392 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1242c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4175,-392 4175,-401 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="17457@0" ObjectIDND1="g_1242ee0@0" ObjectIDZND0="11620@0" Pin0InfoVect0LinkObjId="SW-60208_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1233fa0_0" Pin1InfoVect1LinkObjId="g_1242ee0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4175,-392 4175,-401 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12465c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4713,-596 4713,-579 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17484@0" ObjectIDZND0="11609@1" Pin0InfoVect0LinkObjId="SW-60197_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1371500_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4713,-596 4713,-579 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1247000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4713,-543 4713,-522 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="11609@0" ObjectIDZND0="g_1246820@1" Pin0InfoVect0LinkObjId="g_1246820_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-60197_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4713,-543 4713,-522 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1247260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4713,-493 4713,-472 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_1246820@0" ObjectIDZND0="17468@0" Pin0InfoVect0LinkObjId="g_1209100_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1246820_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4713,-493 4713,-472 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_124ae60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4807,-596 4807,-577 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17484@0" ObjectIDZND0="11606@1" Pin0InfoVect0LinkObjId="SW-60194_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1371500_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4807,-596 4807,-577 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_124cab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4807,-541 4807,-523 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="11606@0" ObjectIDZND0="17461@1" Pin0InfoVect0LinkObjId="SW-79578_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-60194_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4807,-541 4807,-523 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_124cd30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4807,-496 4807,-481 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="17461@0" ObjectIDZND0="11607@1" Pin0InfoVect0LinkObjId="SW-60195_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-79578_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4807,-496 4807,-481 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_124cf60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4807,-432 4842,-432 4842,-442 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="11607@x" ObjectIDND1="0@x" ObjectIDZND0="g_124d420@0" Pin0InfoVect0LinkObjId="g_124d420_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-60195_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4807,-432 4842,-432 4842,-442 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_124d1c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4807,-445 4807,-432 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="11607@0" ObjectIDZND0="g_124d420@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_124d420_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-60195_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4807,-445 4807,-432 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_124e0b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4807,-432 4807,-415 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="11607@x" ObjectIDND1="g_124d420@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-60195_0" Pin1InfoVect1LinkObjId="g_124d420_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4807,-432 4807,-415 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_124e310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4807,-379 4807,-369 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="g_124f150@0" ObjectIDZND1="11608@x" Pin0InfoVect0LinkObjId="g_124f150_0" Pin0InfoVect1LinkObjId="SW-60196_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4807,-379 4807,-369 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_124e570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4807,-369 4842,-369 4842,-360 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_124f150@0" ObjectIDND1="0@x" ObjectIDZND0="11608@1" Pin0InfoVect0LinkObjId="SW-60196_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_124f150_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4807,-369 4842,-369 4842,-360 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_124e7d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4842,-324 4842,-308 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="11608@0" ObjectIDZND0="17457@0" Pin0InfoVect0LinkObjId="g_1233fa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-60196_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4842,-324 4842,-308 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_124ea30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4807,-288 4827,-288 4827,-270 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="11608@x" ObjectIDND1="0@x" ObjectIDZND0="g_124f150@0" Pin0InfoVect0LinkObjId="g_124f150_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-60196_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4807,-288 4827,-288 4827,-270 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_124ec90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4807,-369 4807,-288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="11608@x" ObjectIDND1="0@x" ObjectIDZND0="g_124f150@0" Pin0InfoVect0LinkObjId="g_124f150_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-60196_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4807,-369 4807,-288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_124eef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4807,-288 4807,-204 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" ObjectIDND0="11608@x" ObjectIDND1="0@x" ObjectIDND2="g_124f150@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-60196_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_124f150_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4807,-288 4807,-204 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1252ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4937,-596 4937,-578 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17484@0" ObjectIDZND0="11605@1" Pin0InfoVect0LinkObjId="SW-60193_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1371500_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4937,-596 4937,-578 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1252d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4937,-531 4898,-531 4898,-522 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_1252310@0" ObjectIDND1="11605@x" ObjectIDZND0="g_12531e0@0" Pin0InfoVect0LinkObjId="g_12531e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1252310_0" Pin1InfoVect1LinkObjId="SW-60193_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4937,-531 4898,-531 4898,-522 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1252f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4937,-531 4937,-542 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_12531e0@0" ObjectIDND1="g_1252310@0" ObjectIDZND0="11605@0" Pin0InfoVect0LinkObjId="SW-60193_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_12531e0_0" Pin1InfoVect1LinkObjId="g_1252310_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4937,-531 4937,-542 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1255580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4937,-518 4937,-531 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_1252310@1" ObjectIDZND0="g_12531e0@0" ObjectIDZND1="11605@x" Pin0InfoVect0LinkObjId="g_12531e0_0" Pin0InfoVect1LinkObjId="SW-60193_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1252310_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4937,-518 4937,-531 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12557b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4937,-471 4964,-471 4964,-463 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1253ec0@0" ObjectIDND1="g_1252310@0" ObjectIDZND0="g_1255ed0@0" Pin0InfoVect0LinkObjId="g_1255ed0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1253ec0_0" Pin1InfoVect1LinkObjId="g_1252310_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4937,-471 4964,-471 4964,-463 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1255a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4937,-428 4937,-471 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="g_1253ec0@0" ObjectIDZND0="g_1255ed0@0" ObjectIDZND1="g_1252310@0" Pin0InfoVect0LinkObjId="g_1255ed0_0" Pin0InfoVect1LinkObjId="g_1252310_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1253ec0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4937,-428 4937,-471 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1255c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4937,-471 4937,-487 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1253ec0@0" ObjectIDND1="g_1255ed0@0" ObjectIDZND0="g_1252310@0" Pin0InfoVect0LinkObjId="g_1252310_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1253ec0_0" Pin1InfoVect1LinkObjId="g_1255ed0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4937,-471 4937,-487 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12594c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3551,-596 3551,-577 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17456@0" ObjectIDZND0="11625@1" Pin0InfoVect0LinkObjId="SW-60213_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3551,-596 3551,-577 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_125b300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3551,-541 3551,-523 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="11625@0" ObjectIDZND0="17465@1" Pin0InfoVect0LinkObjId="SW-79600_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-60213_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3551,-541 3551,-523 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_125dd20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3551,-496 3551,-481 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="17465@0" ObjectIDZND0="11626@1" Pin0InfoVect0LinkObjId="SW-60214_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-79600_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3551,-496 3551,-481 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_125df80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3551,-432 3587,-432 3587,-441 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="11626@x" ObjectIDND1="0@x" ObjectIDZND0="g_125e440@0" Pin0InfoVect0LinkObjId="g_125e440_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-60214_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3551,-432 3587,-432 3587,-441 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_125e1e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3551,-445 3551,-432 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="11626@0" ObjectIDZND0="g_125e440@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_125e440_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-60214_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3551,-445 3551,-432 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_125f1f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3551,-432 3551,-415 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_125e440@0" ObjectIDND1="11626@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_125e440_0" Pin1InfoVect1LinkObjId="SW-60214_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3551,-432 3551,-415 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_125f450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3551,-379 3551,-369 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="g_1260290@0" ObjectIDZND1="11627@x" Pin0InfoVect0LinkObjId="g_1260290_0" Pin0InfoVect1LinkObjId="SW-60215_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3551,-379 3551,-369 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_125f6b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3551,-369 3586,-369 3586,-360 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_1260290@0" ObjectIDND1="0@x" ObjectIDZND0="11627@1" Pin0InfoVect0LinkObjId="SW-60215_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1260290_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3551,-369 3586,-369 3586,-360 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_125f910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3586,-324 3586,-308 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="11627@0" ObjectIDZND0="17457@0" Pin0InfoVect0LinkObjId="g_1233fa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-60215_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3586,-324 3586,-308 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_125fb70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3551,-288 3571,-288 3571,-270 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="11627@x" ObjectIDND1="0@x" ObjectIDZND0="g_1260290@0" Pin0InfoVect0LinkObjId="g_1260290_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-60215_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3551,-288 3571,-288 3571,-270 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_125fdd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3551,-369 3551,-288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="11627@x" ObjectIDND1="0@x" ObjectIDZND0="g_1260290@0" Pin0InfoVect0LinkObjId="g_1260290_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-60215_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3551,-369 3551,-288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1260030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3551,-288 3551,-204 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" ObjectIDND0="11627@x" ObjectIDND1="0@x" ObjectIDND2="g_1260290@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-60215_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_1260290_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3551,-288 3551,-204 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_122cec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3638,-596 3638,-577 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17456@0" ObjectIDZND0="11622@1" Pin0InfoVect0LinkObjId="SW-60210_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3638,-596 3638,-577 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1264600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3638,-541 3638,-523 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="11622@0" ObjectIDZND0="17466@1" Pin0InfoVect0LinkObjId="SW-79605_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-60210_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3638,-541 3638,-523 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1264860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3638,-496 3638,-481 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="17466@0" ObjectIDZND0="11623@1" Pin0InfoVect0LinkObjId="SW-60211_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-79605_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3638,-496 3638,-481 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1264ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3638,-432 3673,-432 3673,-441 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="11623@x" ObjectIDND1="0@x" ObjectIDZND0="g_1264f80@0" Pin0InfoVect0LinkObjId="g_1264f80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-60211_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3638,-432 3673,-432 3673,-441 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1264d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3638,-445 3638,-432 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="11623@0" ObjectIDZND0="g_1264f80@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_1264f80_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-60211_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3638,-445 3638,-432 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1265d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3638,-432 3638,-415 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_1264f80@0" ObjectIDND1="11623@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1264f80_0" Pin1InfoVect1LinkObjId="SW-60211_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3638,-432 3638,-415 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1265f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3638,-379 3638,-369 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="g_1266dd0@0" ObjectIDZND1="11624@x" Pin0InfoVect0LinkObjId="g_1266dd0_0" Pin0InfoVect1LinkObjId="SW-60212_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3638,-379 3638,-369 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12661f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3638,-369 3673,-369 3673,-360 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_1266dd0@0" ObjectIDND1="0@x" ObjectIDZND0="11624@1" Pin0InfoVect0LinkObjId="SW-60212_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1266dd0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3638,-369 3673,-369 3673,-360 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1266450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3673,-324 3673,-308 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="11624@0" ObjectIDZND0="17457@0" Pin0InfoVect0LinkObjId="g_1233fa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-60212_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3673,-324 3673,-308 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12666b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3638,-288 3657,-288 3657,-270 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="11624@x" ObjectIDND1="0@x" ObjectIDZND0="g_1266dd0@0" Pin0InfoVect0LinkObjId="g_1266dd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-60212_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3638,-288 3657,-288 3657,-270 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1266910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3638,-369 3638,-288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="11624@x" ObjectIDND1="0@x" ObjectIDZND0="g_1266dd0@0" Pin0InfoVect0LinkObjId="g_1266dd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-60212_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3638,-369 3638,-288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1266b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3638,-288 3638,-204 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" ObjectIDND0="11624@x" ObjectIDND1="0@x" ObjectIDND2="g_1266dd0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-60212_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_1266dd0_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3638,-288 3638,-204 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1268590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4271,-596 4271,-577 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17484@0" ObjectIDZND0="11616@1" Pin0InfoVect0LinkObjId="SW-60204_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1371500_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4271,-596 4271,-577 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_126a430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4271,-541 4271,-523 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="11616@0" ObjectIDZND0="17464@1" Pin0InfoVect0LinkObjId="SW-79595_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-60204_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4271,-541 4271,-523 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_126a690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4271,-496 4271,-481 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="17464@0" ObjectIDZND0="11617@1" Pin0InfoVect0LinkObjId="SW-60205_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-79595_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4271,-496 4271,-481 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_126a8f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4271,-432 4306,-432 4306,-441 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="11617@x" ObjectIDND1="0@x" ObjectIDZND0="g_126adb0@0" Pin0InfoVect0LinkObjId="g_126adb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-60205_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4271,-432 4306,-432 4306,-441 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_126ab50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4271,-445 4271,-432 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="11617@0" ObjectIDZND0="g_126adb0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_126adb0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-60205_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4271,-445 4271,-432 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_126bb60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4271,-432 4271,-415 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="11617@x" ObjectIDND1="g_126adb0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-60205_0" Pin1InfoVect1LinkObjId="g_126adb0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4271,-432 4271,-415 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_126bdc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4271,-379 4271,-369 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="g_126cc00@0" ObjectIDZND1="11618@x" Pin0InfoVect0LinkObjId="g_126cc00_0" Pin0InfoVect1LinkObjId="SW-60206_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4271,-379 4271,-369 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_126c020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4271,-369 4306,-369 4306,-360 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_126cc00@0" ObjectIDND1="0@x" ObjectIDZND0="11618@1" Pin0InfoVect0LinkObjId="SW-60206_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_126cc00_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4271,-369 4306,-369 4306,-360 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_126c280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4306,-324 4306,-308 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="11618@0" ObjectIDZND0="17457@0" Pin0InfoVect0LinkObjId="g_1233fa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-60206_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4306,-324 4306,-308 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_126c4e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4271,-288 4290,-288 4290,-272 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="11618@x" ObjectIDND1="0@x" ObjectIDZND0="g_126cc00@0" Pin0InfoVect0LinkObjId="g_126cc00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-60206_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4271,-288 4290,-288 4290,-272 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_126c740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4271,-369 4271,-288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="11618@x" ObjectIDND1="0@x" ObjectIDZND0="g_126cc00@0" Pin0InfoVect0LinkObjId="g_126cc00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-60206_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4271,-369 4271,-288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_126c9a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4271,-288 4271,-204 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" ObjectIDND0="11618@x" ObjectIDND1="0@x" ObjectIDND2="g_126cc00@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-60206_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_126cc00_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4271,-288 4271,-204 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12701b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5014,-596 5014,-577 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17484@0" ObjectIDZND0="11602@1" Pin0InfoVect0LinkObjId="SW-60190_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1371500_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5014,-596 5014,-577 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12724e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5014,-541 5014,-523 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="11602@0" ObjectIDZND0="17460@1" Pin0InfoVect0LinkObjId="SW-79573_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-60190_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5014,-541 5014,-523 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1272740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5014,-496 5014,-481 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="17460@0" ObjectIDZND0="11603@1" Pin0InfoVect0LinkObjId="SW-60191_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-79573_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5014,-496 5014,-481 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12729a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5014,-432 5049,-432 5049,-442 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="11603@x" ObjectIDND1="0@x" ObjectIDZND0="g_1272e60@0" Pin0InfoVect0LinkObjId="g_1272e60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-60191_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5014,-432 5049,-432 5049,-442 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1272c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5014,-445 5014,-432 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="11603@0" ObjectIDZND0="g_1272e60@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_1272e60_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-60191_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5014,-445 5014,-432 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1273c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5014,-432 5014,-415 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="11603@x" ObjectIDND1="g_1272e60@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-60191_0" Pin1InfoVect1LinkObjId="g_1272e60_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5014,-432 5014,-415 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1273e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5014,-379 5014,-369 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="g_1274cb0@0" ObjectIDZND1="11604@x" Pin0InfoVect0LinkObjId="g_1274cb0_0" Pin0InfoVect1LinkObjId="SW-60192_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5014,-379 5014,-369 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12740d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5014,-369 5049,-369 5049,-360 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_1274cb0@0" ObjectIDND1="0@x" ObjectIDZND0="11604@1" Pin0InfoVect0LinkObjId="SW-60192_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1274cb0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5014,-369 5049,-369 5049,-360 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1274330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5049,-324 5049,-308 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="11604@0" ObjectIDZND0="17457@0" Pin0InfoVect0LinkObjId="g_1233fa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-60192_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5049,-324 5049,-308 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1274590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5014,-288 5034,-288 5034,-270 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="11604@x" ObjectIDND1="0@x" ObjectIDZND0="g_1274cb0@0" Pin0InfoVect0LinkObjId="g_1274cb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-60192_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5014,-288 5034,-288 5034,-270 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12747f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5014,-369 5014,-288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="11604@x" ObjectIDND1="0@x" ObjectIDZND0="g_1274cb0@0" Pin0InfoVect0LinkObjId="g_1274cb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-60192_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5014,-369 5014,-288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1274a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5014,-288 5014,-204 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" ObjectIDND0="11604@x" ObjectIDND1="0@x" ObjectIDND2="g_1274cb0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-60192_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_1274cb0_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5014,-288 5014,-204 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1278260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5129,-596 5129,-577 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17484@0" ObjectIDZND0="11599@1" Pin0InfoVect0LinkObjId="SW-60187_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1371500_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5129,-596 5129,-577 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_127a590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5129,-541 5129,-523 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="11599@0" ObjectIDZND0="17459@1" Pin0InfoVect0LinkObjId="SW-79568_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-60187_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5129,-541 5129,-523 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_127a7f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5129,-496 5129,-481 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="17459@0" ObjectIDZND0="11600@1" Pin0InfoVect0LinkObjId="SW-60188_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-79568_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5129,-496 5129,-481 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_127aa50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5129,-432 5164,-432 5164,-442 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="11600@x" ObjectIDZND0="g_127af10@0" Pin0InfoVect0LinkObjId="g_127af10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-60188_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5129,-432 5164,-432 5164,-442 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_127acb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5129,-445 5129,-432 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="11600@0" ObjectIDZND0="0@x" ObjectIDZND1="g_127af10@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_127af10_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-60188_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5129,-445 5129,-432 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_127bcc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5129,-432 5129,-415 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="11600@x" ObjectIDND1="g_127af10@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-60188_0" Pin1InfoVect1LinkObjId="g_127af10_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5129,-432 5129,-415 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_127bf20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5129,-379 5129,-369 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="g_127cd60@0" ObjectIDZND1="11601@x" Pin0InfoVect0LinkObjId="g_127cd60_0" Pin0InfoVect1LinkObjId="SW-60189_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5129,-379 5129,-369 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_127c180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5129,-369 5164,-369 5164,-360 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_127cd60@0" ObjectIDND1="0@x" ObjectIDZND0="11601@1" Pin0InfoVect0LinkObjId="SW-60189_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_127cd60_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5129,-369 5164,-369 5164,-360 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_127c3e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5164,-324 5164,-308 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="11601@0" ObjectIDZND0="17457@0" Pin0InfoVect0LinkObjId="g_1233fa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-60189_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5164,-324 5164,-308 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_127c640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5129,-288 5149,-288 5149,-270 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="11601@x" ObjectIDND1="0@x" ObjectIDZND0="g_127cd60@0" Pin0InfoVect0LinkObjId="g_127cd60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-60189_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5129,-288 5149,-288 5149,-270 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_127c8a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5129,-369 5129,-288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="11601@x" ObjectIDND1="0@x" ObjectIDZND0="g_127cd60@0" Pin0InfoVect0LinkObjId="g_127cd60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-60189_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5129,-369 5129,-288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_127cb00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5129,-288 5129,-204 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" ObjectIDND0="11601@x" ObjectIDND1="0@x" ObjectIDND2="g_127cd60@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-60189_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_127cd60_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5129,-288 5129,-204 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1283b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4697,-959 4697,-943 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17455@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4697,-959 4697,-943 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1285e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4697,-907 4697,-887 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4697,-907 4697,-887 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_134ffb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4697,-757 4697,-729 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4697,-757 4697,-729 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_13530e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4697,-860 4697,-837 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4697,-860 4697,-837 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1353580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4697,-596 4697,-619 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17484@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1371500_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4697,-596 4697,-619 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1355cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4697,-687 4741,-687 4741,-674 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4697,-687 4741,-687 4741,-674 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1355f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4741,-638 4741,-626 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_1376480@0" Pin0InfoVect0LinkObjId="g_1376480_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4741,-638 4741,-626 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1356190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4697,-687 4697,-702 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4697,-687 4697,-702 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_13563f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4697,-687 4697,-655 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4697,-687 4697,-655 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1370cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4041,-596 4041,-639 4075,-639 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17456@0" ObjectIDZND0="11630@0" Pin0InfoVect0LinkObjId="SW-60218_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4041,-596 4041,-639 4075,-639 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1371500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4111,-639 4142,-639 4142,-596 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="11630@1" ObjectIDZND0="17484@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-60218_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4111,-639 4142,-639 4142,-596 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="AC-LF_YJ.LF_YJ_10IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3510,-596 4061,-596 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="17456" ObjectName="BS-LF_YJ.LF_YJ_10IM"/>
    <cge:TPSR_Ref TObjectID="17456"/></metadata>
   <polyline fill="none" opacity="0" points="3510,-596 4061,-596 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="AC-LF_YJ.LF_YJ_35M">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3894,-959 4813,-959 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="17455" ObjectName="BS-LF_YJ.LF_YJ_35M"/>
    </metadata>
   <polyline fill="none" opacity="0" points="3894,-959 4813,-959 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="AC-LF_YJ.LF_YJ_10M">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3485,-308 5237,-308 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="17457" ObjectName="BS-LF_YJ.LF_YJ_10M"/>
    </metadata>
   <polyline fill="none" opacity="0" points="3485,-308 5237,-308 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="AC-LF_YJ.LF_YJ_10IIM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4126,-596 5197,-596 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="17484" ObjectName="BS-LF_YJ.LF_YJ_10IIM"/>
    <cge:TPSR_Ref TObjectID="17484"/></metadata>
   <polyline fill="none" opacity="0" points="4126,-596 5197,-596 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259" style="fill-opacity:0">
    <a>
     
     <rect height="41" qtmmishow="hidden" width="138" x="3248" y="-1177"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="3248" y="-1177"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124" style="fill-opacity:0">
    <a>
     
     <rect height="69" qtmmishow="hidden" width="77" x="3199" y="-1194"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3199" y="-1194"/></g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" stationName="LF_YJ"/>
</svg>