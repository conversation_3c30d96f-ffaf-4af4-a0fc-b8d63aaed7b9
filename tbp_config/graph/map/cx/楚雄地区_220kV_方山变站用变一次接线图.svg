<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-139" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="2968 -1199 2234 744">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="transformer2:shape85_0">
    <circle cx="25" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="17" y1="14" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="34" y1="14" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="17" x2="34" y1="30" y2="30"/>
   </symbol>
   <symbol id="transformer2:shape85_1">
    <ellipse cx="25" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="24" y1="76" y2="68"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="32" y1="68" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="16" x2="24" y1="60" y2="68"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a50790" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a51250" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1a51a40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1a52450" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1a53400" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1a53c80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a544d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a54c90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a55440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a55d10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a55d10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a56e80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a56e80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_1a57a40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a592f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1a59d50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1a5aa70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1a5b1c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a5c500" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a5ccd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a5d270" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1a5dc90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a5e630" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a5ee40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a5f980" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1a60110" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1a60c10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1a620d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1a63270" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1a63ee0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1a6a420" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a6aef0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1a64df0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1a662b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="754" width="2244" x="2963" y="-1204"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3120" y="-1198"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="640" stroke="rgb(0,205,0)" stroke-width="2.28813" width="394" x="4410" y="-1118"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="191" stroke="rgb(0,205,0)" stroke-width="1.40234" width="403" x="3188" y="-950"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="191" stroke="rgb(0,205,0)" stroke-width="1.40234" width="403" x="3188" y="-759"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="521" stroke="rgb(0,205,0)" stroke-width="2.28813" width="394" x="4805" y="-1118"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-116044">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3706.000000 -903.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21911" ObjectName="SW-CX_FS.CX_FS_3641SW"/>
     <cge:Meas_Ref ObjectId="116044"/>
    <cge:TPSR_Ref TObjectID="21911"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-252302">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3986.000000 -904.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41981" ObjectName="SW-CX_FS.CX_FS_3732SW"/>
     <cge:Meas_Ref ObjectId="252302"/>
    <cge:TPSR_Ref TObjectID="41981"/></metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3690.000000 -712.000000)" xlink:href="#transformer2:shape85_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3690.000000 -712.000000)" xlink:href="#transformer2:shape85_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3970.000000 -713.000000)" xlink:href="#transformer2:shape85_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3970.000000 -713.000000)" xlink:href="#transformer2:shape85_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3229.500000 -1118.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-115234" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3500.000000 -943.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="115234" ObjectName="CX_FS:CX_FS_364BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-115235" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3500.000000 -903.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="115235" ObjectName="CX_FS:CX_FS_364BK_Ic"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-115236" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3500.000000 -863.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="115236" ObjectName="CX_FS:CX_FS_364BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-115237" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3500.000000 -829.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="115237" ObjectName="CX_FS:CX_FS_364BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-115238" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3500.000000 -788.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="115238" ObjectName="CX_FS:CX_FS_364BK_Cos"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-252554" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3500.000000 -752.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="252554" ObjectName="CX_FS:CX_FS_373BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-252556" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3500.000000 -712.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="252556" ObjectName="CX_FS:CX_FS_373BK_Ic"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-252551" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3500.000000 -672.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="252551" ObjectName="CX_FS:CX_FS_373BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-252552" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3500.000000 -637.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="252552" ObjectName="CX_FS:CX_FS_373BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-252553" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3500.000000 -597.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="252553" ObjectName="CX_FS:CX_FS_373BK_Cos"/>
    </metadata>
   </g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-172289" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4410.000000 -1077.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="172289"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-172290" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4410.000000 -1037.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="172290"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-172291" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4410.000000 -997.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="172291"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-172292" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4410.000000 -957.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="172292"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-172293" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4410.000000 -917.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="172293"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-172294" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4410.000000 -877.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="172294"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-172295" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4410.000000 -837.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="172295"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-172296" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4410.000000 -797.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="172296"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-172297" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4410.000000 -758.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="172297"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-172298" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4410.000000 -718.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="172298"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-172299" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4410.000000 -678.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="172299"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-172300" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4410.000000 -638.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="172300"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-172301" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4409.000000 -597.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="172301"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-172302" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4409.000000 -557.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="172302"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-172303" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4409.000000 -517.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="172303"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-172304" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4410.000000 -478.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="172304"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-252303" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4809.000000 -885.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="252303"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-252304" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4809.000000 -845.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="252304"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-252305" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4809.000000 -805.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="252305"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-252306" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4809.000000 -765.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="252306"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-252307" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4809.000000 -725.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="252307"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-252308" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4809.000000 -685.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="252308"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-252309" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4809.000000 -645.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="252309"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-252295" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4809.000000 -1084.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="252295"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-252296" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4809.000000 -1045.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="252296"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-252297" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4809.000000 -1005.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="252297"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-252298" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4809.000000 -965.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="252298"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-252300" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4809.000000 -925.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="252300"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-252310" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4810.000000 -603.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="252310"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_11bc9f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3271.500000 -1166.500000) translate(0,16)">方山变站用变一次接线图</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_14be750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3639.000000 -1033.000000) translate(0,14)">35kVⅠ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14d16f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3663.000000 -934.000000) translate(0,12)">3641</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_147b410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3672.000000 -874.000000) translate(0,12)">364</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1288680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3671.000000 -685.000000) translate(0,12)">K01</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13d1970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3883.000000 -596.000000) translate(0,12)">K03</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1508e60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4016.000000 -685.000000) translate(0,12)">K02</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1508fd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3617.000000 -758.000000) translate(0,12)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1509140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4035.000000 -766.000000) translate(0,12)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_1439390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4455.000000 -1101.000000) translate(0,15)">35kV1号站用变364SF6气压低报警</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_143aa90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4455.000000 -1061.000000) translate(0,15)">35kV1号站用变364SF6气压低闭锁操作</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_14e1d70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4455.000000 -1021.000000) translate(0,15)">35kV1号站用变364开关柜加热器断线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_1473a40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4455.000000 -981.000000) translate(0,15)">35kV1号站用变364储能电源空开跳闸</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_14751f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4455.000000 -941.000000) translate(0,15)">35kV1号站用变364电度表空开跳闸</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_1455cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4455.000000 -901.000000) translate(0,15)">35kV1号站用变364电流速断动作</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_14e9780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4455.000000 -861.000000) translate(0,15)">35kV1号站用变364过流Ⅰ段动作</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_14ead50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4455.000000 -821.000000) translate(0,15)">35kV1号站用变364过流Ⅱ段动作</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_143f1e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4455.000000 -782.000000) translate(0,15)">35kV1号站用变364保护TA断线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_14405b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4455.000000 -742.000000) translate(0,15)">35kV1号站用变364母线TV断线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_14b9a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4455.000000 -702.000000) translate(0,15)">35kV1号站用变364合闸弹簧未储能</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_14badb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4455.000000 -662.000000) translate(0,15)">35kV1号站用变364保护装置故障</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_14263e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4454.000000 -621.000000) translate(0,15)">35kV1号站用变364保护运行异常</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_1491590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4454.000000 -581.000000) translate(0,15)">35kV1号站用变364保护动作</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_14b2e40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4454.000000 -541.000000) translate(0,15)">35kV1号站用变364控制回路断线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_14b54b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4455.000000 -502.000000) translate(0,15)">35kV1号站用变364保护装置通讯正常</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_1376110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3194.000000 -941.000000) translate(0,15)">#1站用变364断路器A相电流Ia</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_1377130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3194.000000 -901.000000) translate(0,15)">#1站用变364断路器C相电流Ic</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_14cda30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3194.000000 -861.000000) translate(0,15)">#1站用变364断路器有功功率P</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_14cf9a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3557.000000 -941.000000) translate(0,15)"> V</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_14cfca0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3557.000000 -901.000000) translate(0,15)"> V</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_14cfe80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3557.000000 -861.000000) translate(0,15)"> V</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_14d0060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3194.000000 -827.000000) translate(0,15)">#1站用变364断路器无功功率Q</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_142d190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3557.000000 -827.000000) translate(0,15)"> V</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_142d690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3194.000000 -786.000000) translate(0,15)">#1站用变364断路器功率因数Cos</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_142e750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3557.000000 -786.000000) translate(0,15)"> V</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_144c370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3947.000000 -937.000000) translate(0,12)">3732</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_13f6590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3940.000000 -1035.000000) translate(0,14)">35kVⅡ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_149ae10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3662.000000 -571.000000) translate(0,12)">0.4kV发电车</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_149ae10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3662.000000 -571.000000) translate(0,27)">快速接入箱</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_149ae10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3662.000000 -571.000000) translate(0,42)">接入电源开关</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14a2f30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3890.000000 -485.000000) translate(0,12)">0.4kV发电车快速接入箱</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14a2f30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3890.000000 -485.000000) translate(0,27)">输出电源开关</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14a38e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3955.000000 -872.000000) translate(0,12)">373</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_14a3c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3194.000000 -750.000000) translate(0,15)">#2号站用变373断路器A相电流Ia</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_14a4630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3194.000000 -710.000000) translate(0,15)">#2号站用变373断路器C相电流Ic</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_14a48c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3194.000000 -670.000000) translate(0,15)">#2号站用变373断路器有功功率P</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_14a57b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3557.000000 -750.000000) translate(0,15)"> V</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_14a59f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3557.000000 -710.000000) translate(0,15)"> V</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_14a5c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3557.000000 -670.000000) translate(0,15)"> V</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_14a72a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3194.000000 -636.000000) translate(0,15)">#2号站用变373断路器无功功率Q</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_14a7920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3557.000000 -636.000000) translate(0,15)"> V</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_14a7fe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3194.000000 -595.000000) translate(0,15)">#2号站用变373断路器功率因数Cos</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_14a8820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3557.000000 -595.000000) translate(0,15)"> V</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_13ce2e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4854.000000 -909.000000) translate(0,15)">35kV2号站用变373SF6气压低告警</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_13d0650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4854.000000 -869.000000) translate(0,15)">35kV2号站用变373SF6气压低闭锁操作</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_146ce80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4854.000000 -829.000000) translate(0,15)">35kV2号站用变373电压空开跳闸</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_146eeb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4854.000000 -789.000000) translate(0,15)">35kV2号站用变373操作电源空开跳闸</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_143cd90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4854.000000 -749.000000) translate(0,15)">35kV2号站用变373储能电源空开跳闸</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_14f0200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4854.000000 -709.000000) translate(0,15)">35kV2号站用变373操显装置电源空开跳闸</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_14f2550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4854.000000 -669.000000) translate(0,15)">35kV2号站用变电度表辅助电源空开跳闸</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_1465b10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4854.000000 -1108.000000) translate(0,15)">35kV2号站用变373装置故障</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_1467b40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4854.000000 -1069.000000) translate(0,15)">35kV2号站用变373装置告警</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_147e710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4854.000000 -1029.000000) translate(0,15)">35kV2号站用变373保护动作</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_1480740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4854.000000 -989.000000) translate(0,15)">35kV2号站用变373控制回路断线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_1416200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4854.000000 -949.000000) translate(0,15)">35kV2号站用变373装置电源空开跳闸</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_13c23e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4855.000000 -627.000000) translate(0,15)">35kV2号站用变373装置通讯中断</text>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-116043">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3706.000000 -846.634146)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21910" ObjectName="SW-CX_FS.CX_FS_364BK"/>
     <cge:Meas_Ref ObjectId="116043"/>
    <cge:TPSR_Ref TObjectID="21910"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3705.000000 -657.634146)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3919.865854 -599.500000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3985.000000 -655.634146)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-252301">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3986.000000 -844.634146)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41980" ObjectName="SW-CX_FS.CX_FS_373BK"/>
     <cge:Meas_Ref ObjectId="252301"/>
    <cge:TPSR_Ref TObjectID="41980"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3767.000000 -531.634146)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3964.865854 -493.500000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_150c8d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3715,-1001 3715,-944 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="21923@0" ObjectIDZND0="21911@1" Pin0InfoVect0LinkObjId="SW-116044_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3715,-1001 3715,-944 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14d1500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3715,-908 3715,-881 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="21911@0" ObjectIDZND0="21910@1" Pin0InfoVect0LinkObjId="SW-116043_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-116044_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3715,-908 3715,-881 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_147b220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3715,-855 3715,-799 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="21910@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="TF-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-116043_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3715,-855 3715,-799 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14c4400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3714,-719 3714,-692 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="TF-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="TF-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3714,-719 3714,-692 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_13d1c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3994,-665 3994,-613 3995,-609 3911,-609 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="TF-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="TF-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3994,-665 3994,-613 3995,-609 3911,-609 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_13d2880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3994,-718 3994,-691 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="TF-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="TF-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3994,-718 3994,-691 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14a1680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3995,-853 3995,-797 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="41980@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="TF-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-252301_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3995,-853 3995,-797 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_144bf30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3995,-1001 3995,-945 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="41997@0" ObjectIDZND0="41981@1" Pin0InfoVect0LinkObjId="SW-252302_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3995,-1001 3995,-945 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_144c150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3995,-909 3995,-880 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="41981@0" ObjectIDZND0="41980@1" Pin0InfoVect0LinkObjId="SW-252301_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-252302_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3995,-909 3995,-880 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_144d100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3884,-609 3776,-609 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="breaker" EndDevType1="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="TF-0_0" Pin0InfoVect1LinkObjId="TF-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="TF-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3884,-609 3776,-609 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_144d2f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3776,-609 3713,-609 3714,-608 3714,-666 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="breaker" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="TF-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="TF-0_0" Pin1InfoVect1LinkObjId="TF-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3776,-609 3713,-609 3714,-608 3714,-666 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_148ea10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3776,-609 3776,-567 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="breaker" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="TF-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="TF-0_0" Pin1InfoVect1LinkObjId="TF-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3776,-609 3776,-567 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_149a980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3776,-540 3776,-503 3929,-503 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="TF-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="TF-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3776,-540 3776,-503 3929,-503 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_19fb1c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3956,-503 4187,-503 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" ObjectIDND0="0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="TF-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3956,-503 4187,-503 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="267" x="3230" y="-1177"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="267" x="3230" y="-1177"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3193" y="-1194"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3193" y="-1194"/></g>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,205,0)" stroke-width="1" x1="4411" x2="4804" y1="-1079" y2="-1079"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,205,0)" stroke-width="1" x1="4411" x2="4804" y1="-1039" y2="-1039"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,205,0)" stroke-width="1" x1="4410" x2="4804" y1="-999" y2="-999"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,205,0)" stroke-width="1" x1="4411" x2="4806" y1="-959" y2="-959"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,205,0)" stroke-width="1" x1="4412" x2="4805" y1="-919" y2="-919"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,205,0)" stroke-width="1" x1="4412" x2="4805" y1="-879" y2="-879"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,205,0)" stroke-width="1" x1="4412" x2="4805" y1="-839" y2="-839"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,205,0)" stroke-width="1" x1="4411" x2="4805" y1="-799" y2="-799"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,205,0)" stroke-width="1" x1="4410" x2="4806" y1="-760" y2="-760"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,205,0)" stroke-width="1" x1="4411" x2="4804" y1="-720" y2="-720"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,205,0)" stroke-width="1" x1="4411" x2="4804" y1="-680" y2="-680"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,205,0)" stroke-width="1" x1="4410" x2="4804" y1="-640" y2="-640"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,205,0)" stroke-width="1" x1="4411" x2="4806" y1="-599" y2="-599"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,205,0)" stroke-width="1" x1="4412" x2="4805" y1="-558" y2="-558"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,205,0)" stroke-width="1" x1="4412" x2="4805" y1="-519" y2="-519"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,205,0)" stroke-width="1" x1="3188" x2="3590" y1="-912" y2="-912"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,205,0)" stroke-width="1" x1="3189" x2="3589" y1="-874" y2="-874"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,205,0)" stroke-width="1" x1="3189" x2="3589" y1="-836" y2="-836"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,205,0)" stroke-width="1" x1="3188" x2="3588" y1="-797" y2="-797"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(21,40,56)" stroke-width="1" x1="2970" x2="2970" y1="-1105" y2="-510"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(21,40,56)" stroke-width="1" x1="4726" x2="4726" y1="-1078" y2="-483"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,205,0)" stroke-width="1" x1="3188" x2="3590" y1="-721" y2="-721"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,205,0)" stroke-width="1" x1="3189" x2="3589" y1="-683" y2="-683"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,205,0)" stroke-width="1" x1="3189" x2="3589" y1="-645" y2="-645"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,205,0)" stroke-width="1" x1="3485" x2="3484" y1="-951" y2="-567"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,205,0)" stroke-width="1" x1="3188" x2="3588" y1="-606" y2="-606"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,205,0)" stroke-width="1" x1="4806" x2="5199" y1="-1079" y2="-1079"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,205,0)" stroke-width="1" x1="4806" x2="5199" y1="-1039" y2="-1039"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,205,0)" stroke-width="1" x1="4805" x2="5199" y1="-999" y2="-999"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,205,0)" stroke-width="1" x1="4806" x2="5201" y1="-959" y2="-959"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,205,0)" stroke-width="1" x1="4807" x2="5200" y1="-919" y2="-919"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,205,0)" stroke-width="1" x1="4807" x2="5200" y1="-879" y2="-879"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,205,0)" stroke-width="1" x1="4807" x2="5200" y1="-839" y2="-839"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,205,0)" stroke-width="1" x1="4806" x2="5200" y1="-799" y2="-799"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,205,0)" stroke-width="1" x1="4804" x2="5197" y1="-760" y2="-760"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,205,0)" stroke-width="1" x1="4806" x2="5199" y1="-720" y2="-720"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,205,0)" stroke-width="1" x1="4806" x2="5199" y1="-680" y2="-680"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,205,0)" stroke-width="1" x1="4805" x2="5199" y1="-640" y2="-640"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(21,40,56)" stroke-width="1" x1="5121" x2="5121" y1="-1078" y2="-483"/>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="21923" cx="3715" cy="-1001" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="41997" cx="3995" cy="-1001" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_FS.CX_FS_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3612,-1001 3807,-1001 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="21923" ObjectName="BS-CX_FS.CX_FS_3IM"/>
    <cge:TPSR_Ref TObjectID="21923"/></metadata>
   <polyline fill="none" opacity="0" points="3612,-1001 3807,-1001 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_FS.CX_FS_3ⅡM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3899,-1001 4063,-1001 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="41997" ObjectName="BS-CX_FS.CX_FS_3ⅡM"/>
    <cge:TPSR_Ref TObjectID="41997"/></metadata>
   <polyline fill="none" opacity="0" points="3899,-1001 4063,-1001 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="MotifButton_Layer">
   <g href="楚雄地区_220kV_方山变电站.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="267" x="3230" y="-1177"/></g>
   <g href="楚雄地区_220kV_方山变电站.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3193" y="-1194"/></g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1"/>
</svg>