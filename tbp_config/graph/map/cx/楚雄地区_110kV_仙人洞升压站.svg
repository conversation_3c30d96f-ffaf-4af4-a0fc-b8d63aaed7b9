<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-104" aopId="786686" id="thSvg" product="E8000V2" version="1.0" viewBox="-330 -957 2022 1244">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="5" x2="5" y1="13" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="2" x2="2" y1="11" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="26" x2="9" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="9" x2="9" y1="18" y2="1"/>
   </symbol>
   <symbol id="generator:shape4">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
   </symbol>
   <symbol id="lightningRod:shape92">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="12" y1="30" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="11" y1="76" y2="68"/>
    <polyline arcFlag="1" points="11,30 10,30 9,30 9,30 8,31 8,31 7,32 7,32 6,33 6,33 6,34 6,35 5,36 5,37 5,37 6,38 6,39 6,40 6,40 7,41 7,41 8,42 8,42 9,43 9,43 10,43 11,43 " stroke-width="1"/>
    <polyline arcFlag="1" points="11,42 10,42 9,42 9,42 8,43 8,43 7,44 7,44 6,45 6,45 6,46 6,47 5,48 5,49 5,49 6,50 6,51 6,52 6,52 7,53 7,53 8,54 8,54 9,55 9,55 10,55 11,55 " stroke-width="1"/>
    <polyline arcFlag="1" points="11,55 10,55 9,55 9,55 8,56 8,56 7,57 7,57 6,58 6,58 6,59 6,60 5,61 5,62 5,62 6,63 6,64 6,65 6,65 7,66 7,66 8,67 8,67 9,68 9,68 10,68 11,68 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.501492" x1="28" x2="28" y1="87" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.368819" x1="45" x2="12" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.395152" x1="28" x2="13" y1="22" y2="22"/>
    <rect height="28" stroke-width="0.398039" width="12" x="41" y="40"/>
    <rect height="26" stroke-width="0.398039" width="12" x="22" y="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.338133" x1="46" x2="12" y1="77" y2="77"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="0.708333" x1="47" x2="47" y1="76" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.275463" x1="46" x2="46" y1="15" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.275463" x1="11" x2="11" y1="15" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.908209" x1="28" x2="28" y1="29" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="38" x2="18" y1="29" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="38" x2="18" y1="37" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="50" x2="47" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="51" x2="43" y1="29" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="41" x2="53" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="47" x2="47" y1="32" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.431962" x1="2" x2="2" y1="65" y2="31"/>
   </symbol>
   <symbol id="lightningRod:shape106">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="26" y1="40" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="54" x2="43" y1="40" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="42" x2="42" y1="48" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="35" x2="35" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.676705" x1="34" x2="16" y1="40" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="16" x2="16" y1="48" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="9" x2="9" y1="48" y2="31"/>
    <circle cx="30" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="26" cy="15" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="20" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="0" x2="9" y1="40" y2="40"/>
   </symbol>
   <symbol id="lightningRod:shape36">
    <rect height="19" stroke-width="0.75" width="8" x="4" y="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.278409" x1="6" x2="9" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.351519" x1="5" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.351519" x1="1" x2="14" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.351519" x1="8" x2="8" y1="21" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="7" x2="7" y1="46" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="6" x2="7" y1="29" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="8" x2="9" y1="27" y2="29"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape40">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.282524" x1="6" x2="6" y1="17" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.337605" x1="7" x2="7" y1="47" y2="36"/>
    <polyline arcFlag="1" points="7,24 7,24 7,24 7,24 8,24 8,24 8,25 8,25 9,25 9,25 9,26 9,26 9,26 9,27 9,27 9,28 9,28 9,28 9,29 8,29 8,29 8,29 8,30 7,30 7,30 7,30 " stroke-width="0.0170053"/>
    <polyline arcFlag="1" points="6,18 7,18 7,18 7,18 7,18 8,18 8,18 8,19 8,19 8,19 8,20 9,20 9,20 9,21 9,21 9,21 8,22 8,22 8,22 8,23 8,23 8,23 7,23 7,24 7,24 7,24 " stroke-width="0.0170053"/>
    <polyline arcFlag="1" points="7,30 7,30 7,30 7,30 8,30 8,31 8,31 8,31 9,31 9,32 9,32 9,32 9,33 9,33 9,33 9,34 9,34 9,35 9,35 8,35 8,35 8,36 8,36 7,36 7,36 7,36 " stroke-width="0.0170053"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.266312" x1="5" x2="8" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.282524" x1="4" x2="9" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.282524" x1="0" x2="13" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape146">
    <rect height="19" stroke-width="1" width="35" x="0" y="0"/>
    <polyline points="17,19 17,30 " stroke-width="1"/>
    <text font-family="SimSun" font-size="15" graphid="g_3f48d20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 17.000000) translate(0,12)">SVG</text>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="7" x2="15" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="15" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="7" x2="15" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="15" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="20" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="25" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="20" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="25" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
   </symbol>
   <symbol id="transformer2:shape22_0">
    <circle cx="37" cy="66" fillStyle="0" r="26.5" stroke-width="0.63865"/>
    <polyline points="64,100 1,37 " stroke-width="1.05405"/>
    <polyline points="58,100 64,100 " stroke-width="1.05405"/>
    <polyline points="64,100 64,93 " stroke-width="1.05405"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="30" y1="71" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="30" y1="71" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="45" x2="38" y1="79" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="45" x2="38" y1="79" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="38" y1="62" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="38" y1="62" y2="71"/>
   </symbol>
   <symbol id="transformer2:shape22_1">
    <ellipse cx="37" cy="29" fillStyle="0" rx="26.5" ry="25.5" stroke-width="0.62032"/>
    <polyline DF8003:Layer="PUBLIC" points="38,34 31,19 46,19 38,34 38,34 38,34 "/>
   </symbol>
   <symbol id="voltageTransformer:shape14">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="3" x2="7" y1="3" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="6,19 26,19 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="25" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.37768" x1="2" x2="9" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="28" x2="30" y1="6" y2="4"/>
    <circle cx="30" cy="14" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="39" cy="11" fillStyle="0" r="6" stroke-width="0.431185"/>
    <ellipse cx="30" cy="6" fillStyle="0" rx="6.5" ry="6" stroke-width="0.431185"/>
    <circle cx="22" cy="11" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="37" x2="39" y1="13" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="40" x2="40" y1="11" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="42" x2="39" y1="13" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="28" x2="30" y1="17" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="31" x2="31" y1="15" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="33" x2="30" y1="17" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="21" x2="24" y1="14" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="21" x2="18" y1="14" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="18" x2="24" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="31" x2="31" y1="4" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="33" x2="30" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.37768" x1="0" x2="12" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251787" x1="6" x2="6" y1="19" y2="9"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_23be810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23eb170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_23ebb10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_236fe60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2370eb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2371990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23723b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2439880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_236e7b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_236e7b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_243ca20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_243ca20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_24360c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_24360c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_24370e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2438d50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_250ff40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2510cf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2511440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_24171b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_25132e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2513ba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_24125d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2413390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2413d10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2414800" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_24151c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_24783b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2415c00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2440690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_24412b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2689790" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2442080" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_23904c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2391aa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1254" width="2032" x="-335" y="-962"/>
  </g><g id="ArcThreePoints_Layer">
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="1530,115 1528,115 1527,115 1526,115 1525,116 1524,117 1523,118 1522,119 1522,120 1521,121 1521,123 1521,124 1521,125 1521,127 1522,128 1522,129 1523,130 1524,131 1525,132 1526,133 1527,133 1528,133 1530,133 1531,133 1532,133 1533,133 1534,132 1535,131 1536,130 1537,129 1537,128 1538,127 1538,125 1538,124 " stroke="rgb(255,255,0)" stroke-width="0.0635538"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(170,85,127)" stroke-width="1" x1="593" x2="711" y1="-466" y2="-466"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(170,85,127)" stroke-width="0.469769" x1="593" x2="593" y1="-457" y2="-466"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(170,85,127)" stroke-width="0.75" x1="643" x2="644" y1="-447" y2="-449"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(170,85,127)" stroke-width="0.75" x1="641" x2="643" y1="-449" y2="-447"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(170,85,127)" stroke-width="0.75" x1="642" x2="642" y1="-466" y2="-447"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(170,85,127)" stroke-width="0.351519" x1="643" x2="643" y1="-441" y2="-427"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(170,85,127)" stroke-width="0.351519" x1="636" x2="649" y1="-427" y2="-427"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(170,85,127)" stroke-width="0.351519" x1="640" x2="646" y1="-425" y2="-425"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(170,85,127)" stroke-width="0.278409" x1="641" x2="644" y1="-422" y2="-422"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(170,85,127)" stroke-width="0.327323" x1="607" x2="607" y1="-430" y2="-417"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(170,85,127)" stroke-width="0.521966" x1="593" x2="593" y1="-429" y2="-439"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(170,85,127)" stroke-width="0.375" x1="592" x2="594" y1="-457" y2="-457"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(170,85,127)" stroke-width="0.920279" x1="621" x2="621" y1="-466" y2="-458"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(170,85,127)" stroke-width="0.920279" x1="621" x2="621" y1="-430" y2="-437"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(170,85,127)" stroke-width="0.327323" x1="604" x2="611" y1="-415" y2="-415"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(170,85,127)" stroke-width="0.327323" x1="598" x2="616" y1="-417" y2="-417"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(170,85,127)" stroke-width="0.327323" x1="605" x2="609" y1="-412" y2="-412"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="555" x2="575" y1="90" y2="90"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="555" x2="555" y1="90" y2="90"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="559" x2="557" y1="95" y2="97"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="555" x2="555" y1="84" y2="84"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="531" x2="556" y1="120" y2="145"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.25" x1="528" x2="531" y1="151" y2="151"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.39375" x1="526" x2="534" y1="148" y2="148"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.611465" x1="536" x2="524" y1="146" y2="146"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="556" x2="556" y1="129" y2="171"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="555" x2="559" y1="116" y2="120"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="555" x2="550" y1="115" y2="120"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="555" x2="555" y1="114" y2="109"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="555" x2="559" y1="91" y2="95"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="555" x2="550" y1="90" y2="95"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="555" x2="555" y1="89" y2="84"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.364327" x1="1529" x2="1529" y1="-3" y2="-3"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.337988" x1="1538" x2="1530" y1="124" y2="124"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.627692" x1="1529" x2="1529" y1="139" y2="124"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.728521" x1="1530" x2="1530" y1="114" y2="108"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1025,101 1025,69 1063,69 " stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="Polygon_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="none" points="618,-458 621,-449 624,-458 618,-458 " stroke="rgb(170,85,127)" stroke-width="0.3"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="618,-437 621,-446 624,-437 618,-437 " stroke="rgb(170,85,127)" stroke-width="0.3"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-56404">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 704.000000 -268.500000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10300" ObjectName="SW-CX_XRD.CX_XRD_301BK"/>
     <cge:Meas_Ref ObjectId="56404"/>
    <cge:TPSR_Ref TObjectID="10300"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56431">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1304.000000 -68.500000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10327" ObjectName="SW-CX_XRD.CX_XRD_384BK"/>
     <cge:Meas_Ref ObjectId="56431"/>
    <cge:TPSR_Ref TObjectID="10327"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56424">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1059.000000 -66.500000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10320" ObjectName="SW-CX_XRD.CX_XRD_383BK"/>
     <cge:Meas_Ref ObjectId="56424"/>
    <cge:TPSR_Ref TObjectID="10320"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56419">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 546.000000 -56.500000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10315" ObjectName="SW-CX_XRD.CX_XRD_382BK"/>
     <cge:Meas_Ref ObjectId="56419"/>
    <cge:TPSR_Ref TObjectID="10315"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56408">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 339.000000 -69.642292)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10304" ObjectName="SW-CX_XRD.CX_XRD_381BK"/>
     <cge:Meas_Ref ObjectId="56408"/>
    <cge:TPSR_Ref TObjectID="10304"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56442">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1519.000000 -65.500000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10338" ObjectName="SW-CX_XRD.CX_XRD_385BK"/>
     <cge:Meas_Ref ObjectId="56442"/>
    <cge:TPSR_Ref TObjectID="10338"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56397">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 703.000000 -596.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10293" ObjectName="SW-CX_XRD.CX_XRD_181BK"/>
     <cge:Meas_Ref ObjectId="56397"/>
    <cge:TPSR_Ref TObjectID="10293"/></metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_35ab090">
    <use class="BV-35KV" transform="matrix(1.546686 -0.000000 0.000000 -1.627792 769.500000 57.000000)" xlink:href="#voltageTransformer:shape14"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_XRD.CX_XRD_M">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="187,-172 1692,-172 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="10357" ObjectName="BS-CX_XRD.CX_XRD_M"/>
    <cge:TPSR_Ref TObjectID="10357"/></metadata>
   <polyline fill="none" opacity="0" points="187,-172 1692,-172 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_XRD.XM">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="701,-576 727,-576 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="48135" ObjectName="BS-CX_XRD.XM"/>
    <cge:TPSR_Ref TObjectID="48135"/></metadata>
   <polyline fill="none" opacity="0" points="701,-576 727,-576 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_XRD.CX_XRD_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="14409"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(0.969697 -0.000000 0.000000 -0.948723 675.000000 -400.000000)" xlink:href="#transformer2:shape22_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(0.969697 -0.000000 0.000000 -0.948723 675.000000 -400.000000)" xlink:href="#transformer2:shape22_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="10350" ObjectName="TF-CX_XRD.CX_XRD_1T"/>
    <cge:TPSR_Ref TObjectID="10350"/></metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_3d48880">
    <use class="BV-35KV" transform="matrix(-0.666667 -0.000000 0.000000 -0.681319 1548.000000 207.000000)" xlink:href="#lightningRod:shape92"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3559da0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 610.000000 -806.000000)" xlink:href="#lightningRod:shape106"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b68130">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 754.000000 -734.000000)" xlink:href="#lightningRod:shape36"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_32a7b90">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 756.000000 -282.000000)" xlink:href="#lightningRod:shape36"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23133e0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1339.000000 32.000000)" xlink:href="#lightningRod:shape36"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2603840">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 807.000000 -8.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26695e0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 653.000000 157.000000)" xlink:href="#lightningRod:shape40"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3fbb8c0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 593.000000 157.000000)" xlink:href="#lightningRod:shape36"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3275c00">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 841.000000 -29.000000)" xlink:href="#lightningRod:shape36"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a48680">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 581.000000 50.000000)" xlink:href="#lightningRod:shape36"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3d94d80">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 374.000000 31.071146)" xlink:href="#lightningRod:shape36"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3680800">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1555.000000 32.000000)" xlink:href="#lightningRod:shape36"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3effa50">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1094.000000 34.000000)" xlink:href="#lightningRod:shape36"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f487a0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1051.000000 172.000000)" xlink:href="#lightningRod:shape146"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -239.000000 -881.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-79745" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 -198.461538 -749.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79745" ObjectName="CX_XRD:CX_XRD_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-79746" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 -202.461538 -707.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79746" ObjectName="CX_XRD:CX_XRD_sumQ"/>
    </metadata>
   </g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="10357" cx="348" cy="-172" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10357" cx="555" cy="-172" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10357" cx="816" cy="-172" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10357" cx="713" cy="-172" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10357" cx="1313" cy="-172" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10357" cx="1528" cy="-172" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10357" cx="1068" cy="-172" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48135" cx="714" cy="-576" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48135" cx="714" cy="-576" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-56321" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -53.000000 -849.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10285" ObjectName="DYN-CX_XRD"/>
     <cge:Meas_Ref ObjectId="56321"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3f9b9a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 502.000000 177.000000) translate(0,15)">35kV1号所用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_29bf790" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1545.000000 131.000000) translate(0,15)">1号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_29bf790" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1545.000000 131.000000) translate(0,33)">   4.5Mvar</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33cc160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1537.000000 223.000000) translate(0,12)">38500</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_250cad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1075.000000 63.000000) translate(0,12)">3836</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_32e24a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 289.000000 99.000000) translate(0,15)">仙人洞Ⅰ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_32d5770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 763.000000 67.000000) translate(0,15)">35kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_32d5770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 763.000000 67.000000) translate(0,33)">TV及避雷器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3407930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 775.000000 -493.000000) translate(0,15)">1号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3407930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 775.000000 -493.000000) translate(0,33)">SZ11-50000/115GY</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3407930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 775.000000 -493.000000) translate(0,51)">115±8×1.25%/36.75kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3407930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 775.000000 -493.000000) translate(0,69)">YN,d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3407930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 775.000000 -493.000000) translate(0,87)">U%=10.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3407d90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 726.000000 -892.000000) translate(0,15)">仙</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3407d90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 726.000000 -892.000000) translate(0,33)">果</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3407d90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 726.000000 -892.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23c7300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 628.500000 -754.000000) translate(0,12)">18167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23c77f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 720.000000 -698.000000) translate(0,12)">1816</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23c7a30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 628.500000 -686.000000) translate(0,12)">18160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23c7c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 722.000000 -625.000000) translate(0,12)">181</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23c7eb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 720.000000 -542.000000) translate(0,12)">1811</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23c80f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 629.000000 -532.000000) translate(0,12)">18117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_344e150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 721.000000 -372.000000) translate(0,12)">3016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_344e390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 723.000000 -298.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_344e5d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 772.000000 -284.000000) translate(0,12)">30117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_344e810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 721.000000 -221.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_344ea50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 355.000000 -151.000000) translate(0,12)">3811</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_344ec90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 274.000000 -142.000000) translate(0,12)">38117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_344eed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 357.000000 -99.000000) translate(0,12)">381</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_344f110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 355.000000 -51.000000) translate(0,12)">3816</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_344f350" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 274.000000 -40.000000) translate(0,12)">38167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_344f590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 562.000000 -151.000000) translate(0,12)">3821</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_344f7d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 481.000000 -133.000000) translate(0,12)">38217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e5df10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 564.000000 -86.000000) translate(0,12)">382</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e5e380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 562.000000 -36.000000) translate(0,12)">3826</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e5e5c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 480.000000 -21.000000) translate(0,12)">38267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a36190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 823.000000 -151.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a36680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 742.000000 -132.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a368c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1075.000000 -151.000000) translate(0,12)">3831</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a36b00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 994.000000 -140.000000) translate(0,12)">38317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a36d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1077.000000 -96.000000) translate(0,12)">383</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a36f80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1320.000000 -150.000000) translate(0,12)">3841</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a371c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1239.000000 -141.000000) translate(0,12)">38417</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a37400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1322.000000 -98.000000) translate(0,12)">384</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a37640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1320.000000 -50.000000) translate(0,12)">3846</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32ec350" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1239.000000 -39.000000) translate(0,12)">38467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32ec590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1535.000000 -150.000000) translate(0,12)">3851</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32ec7d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1454.000000 -138.000000) translate(0,12)">38517</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32eca10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1537.000000 -95.000000) translate(0,12)">385</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32ecc50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1536.000000 -50.000000) translate(0,12)">3853</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32ece90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1454.000000 -39.000000) translate(0,12)">38537</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32ed0d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 606.000000 -399.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32ed310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 187.000000 -192.000000) translate(0,12)">35kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3441970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 629.000000 -614.000000) translate(0,12)">18110</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_263ebf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 722.000000 -625.000000) translate(0,12)">181</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26400c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1001.000000 -465.000000) translate(0,12)">档位（档）：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35225f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 708.000000 -939.000000) translate(0,12)">N57</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23dead0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 993.000000 -37.000000) translate(0,12)">38337</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23df100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1075.000000 -48.000000) translate(0,12)">3833</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3db18c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 979.000000 181.000000) translate(0,15)">1号动态无功补偿装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3db23a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1536.000000 57.000000) translate(0,12)">3856</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34f2990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1258.000000 100.000000) translate(0,15)">仙人洞Ⅱ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3352740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 551.000000 -455.000000) translate(0,12)">1010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_35ad270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_35ad270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_35ad270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_35ad270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_35ad270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_35ad270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_35ad270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_35ad270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_35ad270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_35ad270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_35ad270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_35ad270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_35ad270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_35ad270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_35ad270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_35ad270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_35ad270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_35ad270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_35ad800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -788.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_35ad800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -788.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_35ad800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -788.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_35ad800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -788.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_35ad800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -788.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_35ad800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -788.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_35ad800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -788.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_35adac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -200.000000 -929.500000) translate(0,16)">仙人洞升压站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ad3010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 607.000000 67.000000) translate(0,12)">3823</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3579c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1003.000000 111.000000) translate(0,12)">38367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_357a160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1467.000000 73.000000) translate(0,12)">38567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_33d2470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -218.000000 -8.000000) translate(0,17)">4861696</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2af1120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 468.000000 85.000000) translate(0,12)">630kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3f482e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1008.500000 199.000000) translate(0,15)">(-10~10MVar)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_3401390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -65.000000 -782.000000) translate(0,16)">AGC/AVC</text>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4184a60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 290.000000 -171.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a57520" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 279.000000 -186.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34faa10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 304.000000 -201.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b6a7e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 648.000000 -171.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25ef110" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 637.000000 -186.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3680330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 662.000000 -201.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b5dbd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1289.000000 -172.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34f0b00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1278.000000 -187.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3549f70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1303.000000 -202.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33606a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1589.000000 -172.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_345b850" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1578.000000 -187.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3263530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1603.000000 -202.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_347fa50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1027.000000 -225.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34f76d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1016.000000 -240.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32a84f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1041.000000 -255.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_263f460" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 813.000000 345.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_263f690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 802.000000 330.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_263f8a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 827.000000 315.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a6fa10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 812.000000 638.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a6fc80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 802.000000 623.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a6fec0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 827.000000 608.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a70100" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 805.000000 594.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a70340" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 821.000000 579.000000) translate(0,12)">F(Hz):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fb9080" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 184.000000 263.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fb9300" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 190.000000 247.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fb9540" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 174.000000 232.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fb9780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 184.000000 293.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2af0ca0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 190.000000 216.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2af0ee0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 184.000000 278.000000) translate(0,12)">Ub(kV):</text>
   <metadata/></g>
  </g><g id="PolygonFilled_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="none" points="556,157 550,144 563,144 556,157 556,156 556,157 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1526,37 1532,37 1529,29 1526,37 1526,37 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1532,1 1526,1 1529,10 1532,2 1532,1 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="552,52 558,52 555,44 552,52 552,52 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="558,26 552,26 555,35 558,27 558,26 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1065,33 1071,33 1068,25 1065,33 1065,33 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1071,-3 1065,-3 1068,6 1071,-2 1071,-3 " stroke="rgb(255,255,0)"/>
  </g><g id="Generator_Layer">
   <g DF8003:Layer="PUBLIC" id="SM-CX_XRD.P1">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 343.000000 85.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43311" ObjectName="SM-CX_XRD.P1"/>
    <cge:TPSR_Ref TObjectID="43311"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_XRD.P2">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1308.000000 90.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43312" ObjectName="SM-CX_XRD.P2"/>
    <cge:TPSR_Ref TObjectID="43312"/></metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_33391d0" refnum="0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1539.000000 287.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a88d60" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 592.000000 -720.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3521360" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 590.000000 -653.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f52e50" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 590.000000 -580.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a89c80" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 589.000000 -498.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36457e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 237.000000 -108.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2352a60" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 237.000000 -6.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_231bb90" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 444.000000 -99.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3482e10" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 444.000000 13.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34800b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 705.000000 -98.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3585660" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 957.000000 -106.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26826f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1202.000000 -107.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23c5c30" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1202.000000 -5.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f65c30" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1417.000000 -104.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3e72370" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1417.000000 -5.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3643a60" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 836.000000 -248.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a493e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 969.000000 96.021277)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_351d0c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 957.000000 -2.021277)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3d07650" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 1430.000000 91.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ConnectPoint_Layer"/><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="155" x="-227" y="-940"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="155" x="-227" y="-940"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-276" y="-957"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-276" y="-957"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <polygon fill="rgb(255,255,255)" points="-91,-796 -94,-799 -94,-745 -91,-748 -91,-796" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(255,255,255)" points="-91,-796 -94,-799 55,-799 52,-796 -91,-796" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(112,119,119)" points="-91,-748 -94,-745 55,-745 52,-748 -91,-748" stroke="rgb(112,119,119)"/>
     <polygon fill="rgb(112,119,119)" points="52,-796 55,-799 55,-745 52,-748 52,-796" stroke="rgb(112,119,119)"/>
     <rect fill="rgb(224,238,238)" height="48" stroke="rgb(224,238,238)" width="143" x="-91" y="-796"/>
     <rect fill="none" height="48" qtmmishow="hidden" stroke="rgb(255,0,0)" width="143" x="-91" y="-796"/>
    </a>
   <metadata/></g>
  </g><g id="CircleFilled_Layer">
   <circle DF8003:Layer="PUBLIC" cx="554" cy="114" fill="none" fillStyle="0" r="15" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="554" cy="92" fill="none" fillStyle="0" r="15" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-56446">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1515.000000 270.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10342" ObjectName="SW-CX_XRD.CX_XRD_38500SW"/>
     <cge:Meas_Ref ObjectId="56446"/>
    <cge:TPSR_Ref TObjectID="10342"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56399">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 697.000000 -657.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10295" ObjectName="SW-CX_XRD.CX_XRD_1816SW"/>
     <cge:Meas_Ref ObjectId="56399"/>
    <cge:TPSR_Ref TObjectID="10295"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56398">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 697.000000 -495.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10294" ObjectName="SW-CX_XRD.CX_XRD_1811SW"/>
     <cge:Meas_Ref ObjectId="56398"/>
    <cge:TPSR_Ref TObjectID="10294"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56406">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 698.000000 -325.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10302" ObjectName="SW-CX_XRD.CX_XRD_3016SW"/>
     <cge:Meas_Ref ObjectId="56406"/>
    <cge:TPSR_Ref TObjectID="10302"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56433">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1297.700000 -3.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10329" ObjectName="SW-CX_XRD.CX_XRD_3846SW"/>
     <cge:Meas_Ref ObjectId="56433"/>
    <cge:TPSR_Ref TObjectID="10329"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56426">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1052.700000 -0.978723)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10322" ObjectName="SW-CX_XRD.CX_XRD_3833SW"/>
     <cge:Meas_Ref ObjectId="56426"/>
    <cge:TPSR_Ref TObjectID="10322"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56421">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 539.700000 11.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10317" ObjectName="SW-CX_XRD.CX_XRD_3826SW"/>
     <cge:Meas_Ref ObjectId="56421"/>
    <cge:TPSR_Ref TObjectID="10317"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56410">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 332.700000 -4.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10306" ObjectName="SW-CX_XRD.CX_XRD_3816SW"/>
     <cge:Meas_Ref ObjectId="56410"/>
    <cge:TPSR_Ref TObjectID="10306"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56444">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1513.700000 -3.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10340" ObjectName="SW-CX_XRD.CX_XRD_3853SW"/>
     <cge:Meas_Ref ObjectId="56444"/>
    <cge:TPSR_Ref TObjectID="10340"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56405">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 698.000000 -176.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10301" ObjectName="SW-CX_XRD.CX_XRD_3011SW"/>
     <cge:Meas_Ref ObjectId="56405"/>
    <cge:TPSR_Ref TObjectID="10301"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56409">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 333.000000 -104.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10305" ObjectName="SW-CX_XRD.CX_XRD_3811SW"/>
     <cge:Meas_Ref ObjectId="56409"/>
    <cge:TPSR_Ref TObjectID="10305"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56420">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 539.700000 -104.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10316" ObjectName="SW-CX_XRD.CX_XRD_3821SW"/>
     <cge:Meas_Ref ObjectId="56420"/>
    <cge:TPSR_Ref TObjectID="10316"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56450">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 801.100000 -104.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10346" ObjectName="SW-CX_XRD.CX_XRD_3901SW"/>
     <cge:Meas_Ref ObjectId="56450"/>
    <cge:TPSR_Ref TObjectID="10346"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56425">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1052.800000 -104.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10321" ObjectName="SW-CX_XRD.CX_XRD_3831SW"/>
     <cge:Meas_Ref ObjectId="56425"/>
    <cge:TPSR_Ref TObjectID="10321"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56432">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1298.500000 -103.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10328" ObjectName="SW-CX_XRD.CX_XRD_3841SW"/>
     <cge:Meas_Ref ObjectId="56432"/>
    <cge:TPSR_Ref TObjectID="10328"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56443">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1513.000000 -103.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10339" ObjectName="SW-CX_XRD.CX_XRD_3851SW"/>
     <cge:Meas_Ref ObjectId="56443"/>
    <cge:TPSR_Ref TObjectID="10339"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56403">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 609.000000 -743.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10299" ObjectName="SW-CX_XRD.CX_XRD_18167SW"/>
     <cge:Meas_Ref ObjectId="56403"/>
    <cge:TPSR_Ref TObjectID="10299"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56402">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 607.000000 -676.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10298" ObjectName="SW-CX_XRD.CX_XRD_18160SW"/>
     <cge:Meas_Ref ObjectId="56402"/>
    <cge:TPSR_Ref TObjectID="10298"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56401">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 606.000000 -521.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10297" ObjectName="SW-CX_XRD.CX_XRD_18117SW"/>
     <cge:Meas_Ref ObjectId="56401"/>
    <cge:TPSR_Ref TObjectID="10297"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56411">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 254.000000 -131.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10307" ObjectName="SW-CX_XRD.CX_XRD_38117SW"/>
     <cge:Meas_Ref ObjectId="56411"/>
    <cge:TPSR_Ref TObjectID="10307"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56412">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 254.000000 -29.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10308" ObjectName="SW-CX_XRD.CX_XRD_38167SW"/>
     <cge:Meas_Ref ObjectId="56412"/>
    <cge:TPSR_Ref TObjectID="10308"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56422">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 461.000000 -122.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10318" ObjectName="SW-CX_XRD.CX_XRD_38217SW"/>
     <cge:Meas_Ref ObjectId="56422"/>
    <cge:TPSR_Ref TObjectID="10318"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56423">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 461.000000 -10.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10319" ObjectName="SW-CX_XRD.CX_XRD_38267SW"/>
     <cge:Meas_Ref ObjectId="56423"/>
    <cge:TPSR_Ref TObjectID="10319"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56451">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 722.000000 -121.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10347" ObjectName="SW-CX_XRD.CX_XRD_39017SW"/>
     <cge:Meas_Ref ObjectId="56451"/>
    <cge:TPSR_Ref TObjectID="10347"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56428">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 974.000000 -128.978723)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10324" ObjectName="SW-CX_XRD.CX_XRD_38317SW"/>
     <cge:Meas_Ref ObjectId="56428"/>
    <cge:TPSR_Ref TObjectID="10324"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56434">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1219.000000 -130.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10330" ObjectName="SW-CX_XRD.CX_XRD_38417SW"/>
     <cge:Meas_Ref ObjectId="56434"/>
    <cge:TPSR_Ref TObjectID="10330"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56435">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1219.000000 -28.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10331" ObjectName="SW-CX_XRD.CX_XRD_38467SW"/>
     <cge:Meas_Ref ObjectId="56435"/>
    <cge:TPSR_Ref TObjectID="10331"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56447">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1434.000000 -127.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10343" ObjectName="SW-CX_XRD.CX_XRD_38517SW"/>
     <cge:Meas_Ref ObjectId="56447"/>
    <cge:TPSR_Ref TObjectID="10343"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56448">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1434.000000 -28.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10344" ObjectName="SW-CX_XRD.CX_XRD_38537SW"/>
     <cge:Meas_Ref ObjectId="56448"/>
    <cge:TPSR_Ref TObjectID="10344"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56453">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 1.000000 -1.000000 0.000000 819.000000 -271.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10351" ObjectName="SW-CX_XRD.CX_XRD_30117SW"/>
     <cge:Meas_Ref ObjectId="56453"/>
    <cge:TPSR_Ref TObjectID="10351"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-235623">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 579.000000 75.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39269" ObjectName="SW-CX_XRD.CX_XRD_3823SW"/>
     <cge:Meas_Ref ObjectId="235623"/>
    <cge:TPSR_Ref TObjectID="39269"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56400">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 638.000000 -562.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10296" ObjectName="SW-CX_XRD.CX_XRD_18110SW"/>
     <cge:Meas_Ref ObjectId="56400"/>
    <cge:TPSR_Ref TObjectID="10296"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56427">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1052.700000 110.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10323" ObjectName="SW-CX_XRD.CX_XRD_3836SW"/>
     <cge:Meas_Ref ObjectId="56427"/>
    <cge:TPSR_Ref TObjectID="10323"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56429">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 974.000000 -26.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10325" ObjectName="SW-CX_XRD.CX_XRD_38337SW"/>
     <cge:Meas_Ref ObjectId="56429"/>
    <cge:TPSR_Ref TObjectID="10325"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56445">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1513.700000 104.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10341" ObjectName="SW-CX_XRD.CX_XRD_3856SW"/>
     <cge:Meas_Ref ObjectId="56445"/>
    <cge:TPSR_Ref TObjectID="10341"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56449">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1447.000000 84.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10345" ObjectName="SW-CX_XRD.CX_XRD_38567SW"/>
     <cge:Meas_Ref ObjectId="56449"/>
    <cge:TPSR_Ref TObjectID="10345"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56430">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 986.000000 90.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10326" ObjectName="SW-CX_XRD.CX_XRD_38367SW"/>
     <cge:Meas_Ref ObjectId="56430"/>
    <cge:TPSR_Ref TObjectID="10326"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56407">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(0.857143 -0.000000 0.000000 -0.586957 579.857143 -423.021739)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10303" ObjectName="SW-CX_XRD.CX_XRD_1010SW"/>
     <cge:Meas_Ref ObjectId="56407"/>
    <cge:TPSR_Ref TObjectID="10303"/></metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_3f16740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1529,139 1529,148 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_3d48880@1" Pin0InfoVect0LinkObjId="g_3d48880_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1529,139 1529,148 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b67170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1529,203 1529,212 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3d48880@0" ObjectIDZND0="10342@0" Pin0InfoVect0LinkObjId="SW-56446_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3d48880_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1529,203 1529,212 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f8ae40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1530,261 1530,248 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_33391d0@0" ObjectIDZND0="10342@1" Pin0InfoVect0LinkObjId="SW-56446_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33391d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1530,261 1530,248 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32a7930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1068,105 1068,142 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="10326@x" ObjectIDND1="10323@x" ObjectIDZND0="g_3f487a0@0" Pin0InfoVect0LinkObjId="g_3f487a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-56430_0" Pin1InfoVect1LinkObjId="SW-56427_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1068,105 1068,142 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f49d40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="763,-327 713,-327 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="g_32a7b90@0" ObjectIDZND0="10302@x" ObjectIDZND1="10300@x" Pin0InfoVect0LinkObjId="SW-56406_0" Pin0InfoVect1LinkObjId="SW-56404_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_32a7b90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="763,-327 713,-327 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22d5a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="713,-347 713,-331 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="10302@0" ObjectIDZND0="g_32a7b90@0" ObjectIDZND1="10300@x" Pin0InfoVect0LinkObjId="g_32a7b90_0" Pin0InfoVect1LinkObjId="SW-56404_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56406_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="713,-347 713,-331 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22d6350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="713,-331 713,-304 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="g_32a7b90@0" ObjectIDND1="10302@x" ObjectIDZND0="10300@1" Pin0InfoVect0LinkObjId="SW-56404_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_32a7b90_0" Pin1InfoVect1LinkObjId="SW-56406_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="713,-331 713,-304 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3fbaf50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="664,-766 712,-766 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_3559da0@0" ObjectIDZND0="10299@x" ObjectIDZND1="10295@x" ObjectIDZND2="10299@x" Pin0InfoVect0LinkObjId="SW-56403_0" Pin0InfoVect1LinkObjId="SW-56399_0" Pin0InfoVect2LinkObjId="SW-56403_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3559da0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="664,-766 712,-766 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_362a190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1313,-77 1313,-61 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="10327@0" ObjectIDZND0="10329@1" Pin0InfoVect0LinkObjId="SW-56433_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56431_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1313,-77 1313,-61 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_261c610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1068,-75 1068,-59 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="10320@0" ObjectIDZND0="10322@1" Pin0InfoVect0LinkObjId="SW-56426_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56424_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1068,-75 1068,-59 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22d6a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="816,-17 816,21 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_2603840@0" ObjectIDZND0="g_35ab090@0" Pin0InfoVect0LinkObjId="g_35ab090_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2603840_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="816,-17 816,21 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32e8980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="348,-78 348,-62 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="10304@0" ObjectIDZND0="10306@1" Pin0InfoVect0LinkObjId="SW-56410_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56408_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="348,-78 348,-62 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_355f480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="348,-26 348,-14 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="generator" ObjectIDND0="10306@0" ObjectIDZND0="g_3d94d80@0" ObjectIDZND1="10308@x" ObjectIDZND2="43311@x" Pin0InfoVect0LinkObjId="g_3d94d80_0" Pin0InfoVect1LinkObjId="SW-56412_0" Pin0InfoVect2LinkObjId="SM-CX_XRD.P1_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56410_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="348,-26 348,-14 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_355f6e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="348,-14 348,64 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="generator" ObjectIDND0="10306@x" ObjectIDND1="g_3d94d80@0" ObjectIDND2="10308@x" ObjectIDZND0="43311@0" Pin0InfoVect0LinkObjId="SM-CX_XRD.P1_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-56410_0" Pin1InfoVect1LinkObjId="g_3d94d80_0" Pin1InfoVect2LinkObjId="SW-56412_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="348,-14 348,64 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_355f940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="381,-14 348,-14 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="generator" ObjectIDND0="g_3d94d80@0" ObjectIDZND0="10306@x" ObjectIDZND1="10308@x" ObjectIDZND2="43311@x" Pin0InfoVect0LinkObjId="SW-56410_0" Pin0InfoVect1LinkObjId="SW-56412_0" Pin0InfoVect2LinkObjId="SM-CX_XRD.P1_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3d94d80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="381,-14 348,-14 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a4c740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="848,-74 816,-74 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_3275c00@0" ObjectIDZND0="g_2603840@0" ObjectIDZND1="10347@x" ObjectIDZND2="10346@x" Pin0InfoVect0LinkObjId="g_2603840_0" Pin0InfoVect1LinkObjId="SW-56451_0" Pin0InfoVect2LinkObjId="SW-56450_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3275c00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="848,-74 816,-74 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3dae250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="816,-44 816,-74 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2603840@1" ObjectIDZND0="g_3275c00@0" ObjectIDZND1="10347@x" ObjectIDZND2="10346@x" Pin0InfoVect0LinkObjId="g_3275c00_0" Pin0InfoVect1LinkObjId="SW-56451_0" Pin0InfoVect2LinkObjId="SW-56450_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2603840_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="816,-44 816,-74 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26415b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1313,-25 1313,-13 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="generator" ObjectIDND0="10329@0" ObjectIDZND0="g_23133e0@0" ObjectIDZND1="10331@x" ObjectIDZND2="43312@x" Pin0InfoVect0LinkObjId="g_23133e0_0" Pin0InfoVect1LinkObjId="SW-56435_0" Pin0InfoVect2LinkObjId="SM-CX_XRD.P2_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56433_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1313,-25 1313,-13 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2641810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1313,-13 1313,69 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="generator" ObjectIDND0="10329@x" ObjectIDND1="g_23133e0@0" ObjectIDND2="10331@x" ObjectIDZND0="43312@0" Pin0InfoVect0LinkObjId="SM-CX_XRD.P2_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-56433_0" Pin1InfoVect1LinkObjId="g_23133e0_0" Pin1InfoVect2LinkObjId="SW-56435_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1313,-13 1313,69 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cfebb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1346,-13 1313,-13 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="generator" ObjectIDND0="g_23133e0@0" ObjectIDZND0="10329@x" ObjectIDZND1="10331@x" ObjectIDZND2="43312@x" Pin0InfoVect0LinkObjId="SW-56433_0" Pin0InfoVect1LinkObjId="SW-56435_0" Pin0InfoVect2LinkObjId="SM-CX_XRD.P2_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_23133e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1346,-13 1313,-13 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2367d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1529,-25 1529,-13 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="10340@0" ObjectIDZND0="g_3680800@0" ObjectIDZND1="10344@x" ObjectIDZND2="10341@x" Pin0InfoVect0LinkObjId="g_3680800_0" Pin0InfoVect1LinkObjId="SW-56448_0" Pin0InfoVect2LinkObjId="SW-56445_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56444_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1529,-25 1529,-13 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2367f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1562,-13 1529,-13 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_3680800@0" ObjectIDZND0="10340@x" ObjectIDZND1="10344@x" ObjectIDZND2="10341@x" Pin0InfoVect0LinkObjId="SW-56444_0" Pin0InfoVect1LinkObjId="SW-56448_0" Pin0InfoVect2LinkObjId="SW-56445_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3680800_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1562,-13 1529,-13 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32607c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1528,-74 1529,-61 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="10338@0" ObjectIDZND0="10340@1" Pin0InfoVect0LinkObjId="SW-56444_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56442_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1528,-74 1529,-61 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32e2270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="713,-404 713,-383 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="10350@1" ObjectIDZND0="10302@1" Pin0InfoVect0LinkObjId="SW-56406_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_250da60_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="713,-404 713,-383 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2a4b4b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="618,-728 631,-728 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2a88d60@0" ObjectIDZND0="10299@1" Pin0InfoVect0LinkObjId="SW-56403_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a88d60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="618,-728 631,-728 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f52bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="616,-661 629,-661 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3521360@0" ObjectIDZND0="10298@1" Pin0InfoVect0LinkObjId="SW-56402_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3521360_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="616,-661 629,-661 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2ad1210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="616,-588 629,-588 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3f52e50@0" ObjectIDZND0="10296@1" Pin0InfoVect0LinkObjId="SW-56400_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3f52e50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="616,-588 629,-588 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_34f3ae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="615,-506 628,-506 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2a89c80@0" ObjectIDZND0="10297@1" Pin0InfoVect0LinkObjId="SW-56401_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a89c80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="615,-506 628,-506 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3646270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="263,-116 276,-116 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_36457e0@0" ObjectIDZND0="10307@1" Pin0InfoVect0LinkObjId="SW-56411_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36457e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="263,-116 276,-116 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_23534f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="263,-14 276,-14 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2352a60@0" ObjectIDZND0="10308@1" Pin0InfoVect0LinkObjId="SW-56412_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2352a60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="263,-14 276,-14 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_231c620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="470,-107 483,-107 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_231bb90@0" ObjectIDZND0="10318@1" Pin0InfoVect0LinkObjId="SW-56422_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_231bb90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="470,-107 483,-107 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3483890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="470,5 483,5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3482e10@0" ObjectIDZND0="10319@1" Pin0InfoVect0LinkObjId="SW-56423_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3482e10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="470,5 483,5 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3480b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="731,-106 744,-106 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_34800b0@0" ObjectIDZND0="10347@1" Pin0InfoVect0LinkObjId="SW-56451_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34800b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="731,-106 744,-106 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35860e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="983,-114 996,-114 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3585660@0" ObjectIDZND0="10324@1" Pin0InfoVect0LinkObjId="SW-56428_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3585660_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="983,-114 996,-114 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4119d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1228,-115 1241,-115 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_26826f0@0" ObjectIDZND0="10330@1" Pin0InfoVect0LinkObjId="SW-56434_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26826f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1228,-115 1241,-115 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32cb950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1228,-13 1241,-13 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_23c5c30@0" ObjectIDZND0="10331@1" Pin0InfoVect0LinkObjId="SW-56435_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_23c5c30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1228,-13 1241,-13 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f66680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1443,-112 1456,-112 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3f65c30@0" ObjectIDZND0="10343@1" Pin0InfoVect0LinkObjId="SW-56447_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3f65c30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1443,-112 1456,-112 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b0c260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1443,-13 1456,-13 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3e72370@0" ObjectIDZND0="10344@1" Pin0InfoVect0LinkObjId="SW-56448_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3e72370_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1443,-13 1456,-13 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b0c720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="348,-162 348,-172 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="10305@1" ObjectIDZND0="10357@0" Pin0InfoVect0LinkObjId="g_2b0c980_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56409_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="348,-162 348,-172 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b0c980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="555,-162 555,-172 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="10316@1" ObjectIDZND0="10357@0" Pin0InfoVect0LinkObjId="g_2b0c720_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56420_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="555,-162 555,-172 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b0cbe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="816,-162 816,-172 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="10346@1" ObjectIDZND0="10357@0" Pin0InfoVect0LinkObjId="g_2b0c720_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56450_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="816,-162 816,-172 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b0ce40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1068,-162 1068,-172 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="10321@1" ObjectIDZND0="10357@0" Pin0InfoVect0LinkObjId="g_2b0c720_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56425_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1068,-162 1068,-172 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b0d0a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1313,-161 1313,-172 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="10328@1" ObjectIDZND0="10357@0" Pin0InfoVect0LinkObjId="g_2b0c720_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56432_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1313,-161 1313,-172 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b0d300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1528,-161 1528,-172 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="10339@1" ObjectIDZND0="10357@0" Pin0InfoVect0LinkObjId="g_2b0c720_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56443_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1528,-161 1528,-172 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b0d560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="713,-198 713,-172 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="10301@0" ObjectIDZND0="10357@0" Pin0InfoVect0LinkObjId="g_2b0c720_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56405_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="713,-198 713,-172 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3558200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="810,-256 797,-256 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3643a60@0" ObjectIDZND0="10351@1" Pin0InfoVect0LinkObjId="SW-56453_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3643a60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="810,-256 797,-256 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3558460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="665,-661 712,-661 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="10298@0" ObjectIDZND0="10293@x" ObjectIDZND1="10295@x" Pin0InfoVect0LinkObjId="SW-56397_0" Pin0InfoVect1LinkObjId="SW-56399_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56402_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="665,-661 712,-661 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3558f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="712,-679 712,-661 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="10295@0" ObjectIDZND0="10293@x" ObjectIDZND1="10298@x" Pin0InfoVect0LinkObjId="SW-56397_0" Pin0InfoVect1LinkObjId="SW-56402_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56399_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="712,-679 712,-661 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_35591b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="712,-661 712,-631 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="10295@x" ObjectIDND1="10298@x" ObjectIDZND0="10293@1" Pin0InfoVect0LinkObjId="SW-56397_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-56399_0" Pin1InfoVect1LinkObjId="SW-56402_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="712,-661 712,-631 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3559410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="665,-588 712,-588 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="busSection" ObjectIDND0="10296@0" ObjectIDZND0="10293@x" ObjectIDZND1="48135@0" Pin0InfoVect0LinkObjId="SW-56397_0" Pin0InfoVect1LinkObjId="g_250d800_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56400_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="665,-588 712,-588 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_250d5a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="712,-604 712,-588 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="10293@0" ObjectIDZND0="10296@x" ObjectIDZND1="48135@0" Pin0InfoVect0LinkObjId="SW-56400_0" Pin0InfoVect1LinkObjId="g_250d800_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56397_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="712,-604 712,-588 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_250d800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="712,-588 714,-576 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="busSection" ObjectIDND0="10296@x" ObjectIDND1="10293@x" ObjectIDZND0="48135@0" Pin0InfoVect0LinkObjId="g_34019c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-56400_0" Pin1InfoVect1LinkObjId="SW-56397_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="712,-588 714,-576 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_250da60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="664,-506 712,-506 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" EndDevType1="switch" ObjectIDND0="10297@0" ObjectIDZND0="10350@x" ObjectIDZND1="10294@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="SW-56398_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56401_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="664,-506 712,-506 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_250dcc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="312,-116 348,-116 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="10307@0" ObjectIDZND0="10304@x" ObjectIDZND1="10305@x" Pin0InfoVect0LinkObjId="SW-56408_0" Pin0InfoVect1LinkObjId="SW-56409_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56411_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="312,-116 348,-116 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4133ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="348,-105 348,-116 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="10304@1" ObjectIDZND0="10307@x" ObjectIDZND1="10305@x" Pin0InfoVect0LinkObjId="SW-56411_0" Pin0InfoVect1LinkObjId="SW-56409_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56408_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="348,-105 348,-116 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4134100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="348,-116 348,-126 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="10307@x" ObjectIDND1="10304@x" ObjectIDZND0="10305@0" Pin0InfoVect0LinkObjId="SW-56409_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-56411_0" Pin1InfoVect1LinkObjId="SW-56408_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="348,-116 348,-126 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4134360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="519,-107 555,-107 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="10318@0" ObjectIDZND0="10316@x" ObjectIDZND1="10315@x" Pin0InfoVect0LinkObjId="SW-56420_0" Pin0InfoVect1LinkObjId="SW-56419_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56422_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="519,-107 555,-107 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4134e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="555,-126 555,-107 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="10316@0" ObjectIDZND0="10318@x" ObjectIDZND1="10315@x" Pin0InfoVect0LinkObjId="SW-56422_0" Pin0InfoVect1LinkObjId="SW-56419_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56420_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="555,-126 555,-107 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4135090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="555,-107 555,-92 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="10318@x" ObjectIDND1="10316@x" ObjectIDZND0="10315@1" Pin0InfoVect0LinkObjId="SW-56419_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-56422_0" Pin1InfoVect1LinkObjId="SW-56420_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="555,-107 555,-92 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35a23a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="312,-14 348,-14 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="generator" ObjectIDND0="10308@0" ObjectIDZND0="10306@x" ObjectIDZND1="g_3d94d80@0" ObjectIDZND2="43311@x" Pin0InfoVect0LinkObjId="SW-56410_0" Pin0InfoVect1LinkObjId="g_3d94d80_0" Pin0InfoVect2LinkObjId="SM-CX_XRD.P1_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56412_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="312,-14 348,-14 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35a25d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="780,-106 816,-106 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="10347@0" ObjectIDZND0="10346@x" ObjectIDZND1="g_3275c00@0" ObjectIDZND2="g_2603840@0" Pin0InfoVect0LinkObjId="SW-56450_0" Pin0InfoVect1LinkObjId="g_3275c00_0" Pin0InfoVect2LinkObjId="g_2603840_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56451_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="780,-106 816,-106 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35a30a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="816,-126 816,-106 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="10346@0" ObjectIDZND0="10347@x" ObjectIDZND1="g_3275c00@0" ObjectIDZND2="g_2603840@0" Pin0InfoVect0LinkObjId="SW-56451_0" Pin0InfoVect1LinkObjId="g_3275c00_0" Pin0InfoVect2LinkObjId="g_2603840_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56450_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="816,-126 816,-106 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35a3300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="816,-106 816,-74 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="10347@x" ObjectIDND1="10346@x" ObjectIDZND0="g_3275c00@0" ObjectIDZND1="g_2603840@0" Pin0InfoVect0LinkObjId="g_3275c00_0" Pin0InfoVect1LinkObjId="g_2603840_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-56451_0" Pin1InfoVect1LinkObjId="SW-56450_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="816,-106 816,-74 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35a3560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1032,-114 1068,-114 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="10324@0" ObjectIDZND0="10321@x" ObjectIDZND1="10320@x" Pin0InfoVect0LinkObjId="SW-56425_0" Pin0InfoVect1LinkObjId="SW-56424_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56428_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1032,-114 1068,-114 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35a37c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1068,-126 1068,-114 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="10321@0" ObjectIDZND0="10324@x" ObjectIDZND1="10320@x" Pin0InfoVect0LinkObjId="SW-56428_0" Pin0InfoVect1LinkObjId="SW-56424_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56425_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1068,-126 1068,-114 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35a3a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1068,-114 1068,-102 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="10324@x" ObjectIDND1="10321@x" ObjectIDZND0="10320@1" Pin0InfoVect0LinkObjId="SW-56424_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-56428_0" Pin1InfoVect1LinkObjId="SW-56425_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1068,-114 1068,-102 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2af8590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1277,-115 1313,-115 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="10330@0" ObjectIDZND0="10328@x" ObjectIDZND1="10327@x" Pin0InfoVect0LinkObjId="SW-56432_0" Pin0InfoVect1LinkObjId="SW-56431_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56434_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1277,-115 1313,-115 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2af9030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1313,-125 1313,-115 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="10328@0" ObjectIDZND0="10330@x" ObjectIDZND1="10327@x" Pin0InfoVect0LinkObjId="SW-56434_0" Pin0InfoVect1LinkObjId="SW-56431_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56432_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1313,-125 1313,-115 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2af9290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1313,-115 1313,-104 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="10330@x" ObjectIDND1="10328@x" ObjectIDZND0="10327@1" Pin0InfoVect0LinkObjId="SW-56431_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-56434_0" Pin1InfoVect1LinkObjId="SW-56432_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1313,-115 1313,-104 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2af94f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1277,-13 1313,-13 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="generator" ObjectIDND0="10331@0" ObjectIDZND0="10329@x" ObjectIDZND1="g_23133e0@0" ObjectIDZND2="43312@x" Pin0InfoVect0LinkObjId="SW-56433_0" Pin0InfoVect1LinkObjId="g_23133e0_0" Pin0InfoVect2LinkObjId="SM-CX_XRD.P2_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56435_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1277,-13 1313,-13 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2af9750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1492,-112 1528,-112 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="10343@0" ObjectIDZND0="10339@x" ObjectIDZND1="10338@x" Pin0InfoVect0LinkObjId="SW-56443_0" Pin0InfoVect1LinkObjId="SW-56442_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56447_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1492,-112 1528,-112 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_23c6be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1528,-125 1528,-112 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="10339@0" ObjectIDZND0="10343@x" ObjectIDZND1="10338@x" Pin0InfoVect0LinkObjId="SW-56447_0" Pin0InfoVect1LinkObjId="SW-56442_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56443_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1528,-125 1528,-112 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_23c6e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1528,-112 1528,-101 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="10343@x" ObjectIDND1="10339@x" ObjectIDZND0="10338@1" Pin0InfoVect0LinkObjId="SW-56442_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-56447_0" Pin1InfoVect1LinkObjId="SW-56443_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1528,-112 1528,-101 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_23c70a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1492,-13 1529,-13 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="10344@0" ObjectIDZND0="10340@x" ObjectIDZND1="g_3680800@0" ObjectIDZND2="10341@x" Pin0InfoVect0LinkObjId="SW-56444_0" Pin0InfoVect1LinkObjId="g_3680800_0" Pin0InfoVect2LinkObjId="SW-56445_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56448_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1492,-13 1529,-13 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_344fa10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="555,-65 555,-47 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="10315@0" ObjectIDZND0="10317@1" Pin0InfoVect0LinkObjId="SW-56421_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56419_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="555,-65 555,-47 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_344fc00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="555,-11 555,5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="10317@0" ObjectIDZND0="g_2a48680@0" ObjectIDZND1="10319@x" Pin0InfoVect0LinkObjId="g_2a48680_0" Pin0InfoVect1LinkObjId="SW-56423_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56421_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="555,-11 555,5 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e5d860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="588,5 555,5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2a48680@0" ObjectIDZND0="10317@x" ObjectIDZND1="10319@x" Pin0InfoVect0LinkObjId="SW-56421_0" Pin0InfoVect1LinkObjId="SW-56423_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a48680_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="588,5 555,5 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e5da50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="555,5 519,5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="10317@x" ObjectIDND1="g_2a48680@0" ObjectIDZND0="10319@0" Pin0InfoVect0LinkObjId="SW-56423_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-56421_0" Pin1InfoVect1LinkObjId="g_2a48680_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="555,5 519,5 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e5dcb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="555,76 555,5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDZND0="10317@x" ObjectIDZND1="g_2a48680@0" ObjectIDZND2="10319@x" Pin0InfoVect0LinkObjId="SW-56421_0" Pin0InfoVect1LinkObjId="g_2a48680_0" Pin0InfoVect2LinkObjId="SW-56423_0" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="555,76 555,5 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a35cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="576,90 601,90 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="39269@1" Pin0InfoVect0LinkObjId="SW-235623_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="576,90 601,90 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a35f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="637,90 660,90 660,110 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="39269@0" ObjectIDZND0="g_26695e0@0" Pin0InfoVect0LinkObjId="g_26695e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235623_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="637,90 660,90 660,110 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32ed550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="600,112 600,90 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_3fbb8c0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3fbb8c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="600,112 600,90 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3523760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1068,88 1068,105 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="10323@0" ObjectIDZND0="10326@x" ObjectIDZND1="g_3f487a0@0" Pin0InfoVect0LinkObjId="SW-56430_0" Pin0InfoVect1LinkObjId="g_3f487a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56427_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1068,88 1068,105 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a48f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1044,105 1068,105 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="10326@0" ObjectIDZND0="10323@x" ObjectIDZND1="g_3f487a0@0" Pin0InfoVect0LinkObjId="SW-56427_0" Pin0InfoVect1LinkObjId="g_3f487a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56430_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1044,105 1068,105 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a49180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="995,105 1008,105 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2a493e0@0" ObjectIDZND0="10326@1" Pin0InfoVect0LinkObjId="SW-56430_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a493e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="995,105 1008,105 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_351cce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1032,-11 1068,-11 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="10325@0" ObjectIDZND0="10322@x" ObjectIDZND1="g_3effa50@0" ObjectIDZND2="10323@x" Pin0InfoVect0LinkObjId="SW-56426_0" Pin0InfoVect1LinkObjId="g_3effa50_0" Pin0InfoVect2LinkObjId="SW-56427_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56429_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1032,-11 1068,-11 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_351ced0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="983,-11 996,-11 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_351d0c0@0" ObjectIDZND0="10325@1" Pin0InfoVect0LinkObjId="SW-56429_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_351d0c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="983,-11 996,-11 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3eff5b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1101,-11 1068,-11 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_3effa50@0" ObjectIDZND0="10322@x" ObjectIDZND1="10325@x" ObjectIDZND2="10323@x" Pin0InfoVect0LinkObjId="SW-56426_0" Pin0InfoVect1LinkObjId="SW-56429_0" Pin0InfoVect2LinkObjId="SW-56427_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3effa50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1101,-11 1068,-11 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3eff7f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1068,-23 1068,-11 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="10322@0" ObjectIDZND0="g_3effa50@0" ObjectIDZND1="10325@x" ObjectIDZND2="10323@x" Pin0InfoVect0LinkObjId="g_3effa50_0" Pin0InfoVect1LinkObjId="SW-56429_0" Pin0InfoVect2LinkObjId="SW-56427_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56426_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1068,-23 1068,-11 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3db25e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1529,99 1505,99 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="10341@x" ObjectIDZND0="10345@0" Pin0InfoVect0LinkObjId="SW-56449_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56445_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1529,99 1505,99 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3db27d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1529,82 1529,99 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="10341@0" ObjectIDZND0="10345@x" Pin0InfoVect0LinkObjId="SW-56449_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56445_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1529,82 1529,99 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d07210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1529,109 1529,99 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" EndDevType1="switch" ObjectIDZND0="10341@x" ObjectIDZND1="10345@x" Pin0InfoVect0LinkObjId="SW-56445_0" Pin0InfoVect1LinkObjId="SW-56449_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1529,109 1529,99 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d07420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1456,99 1469,99 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3d07650@0" ObjectIDZND0="10345@1" Pin0InfoVect0LinkObjId="SW-56449_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3d07650_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1456,99 1469,99 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35aca00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1529,-13 1529,46 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="10340@x" ObjectIDND1="g_3680800@0" ObjectIDND2="10344@x" ObjectIDZND0="10341@1" Pin0InfoVect0LinkObjId="SW-56445_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-56444_0" Pin1InfoVect1LinkObjId="g_3680800_0" Pin1InfoVect2LinkObjId="SW-56448_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1529,-13 1529,46 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2af5b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="712,-487 712,-506 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="10350@0" ObjectIDZND0="10297@x" ObjectIDZND1="10294@x" Pin0InfoVect0LinkObjId="SW-56401_0" Pin0InfoVect1LinkObjId="SW-56398_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_250da60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="712,-487 712,-506 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2af5d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="712,-517 712,-506 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="transformer2" ObjectIDND0="10294@0" ObjectIDZND0="10297@x" ObjectIDZND1="10350@x" Pin0InfoVect0LinkObjId="SW-56401_0" Pin0InfoVect1LinkObjId="g_250da60_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56398_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="712,-517 712,-506 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2af5fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="667,-728 712,-728 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="10299@0" ObjectIDZND0="10295@x" ObjectIDZND1="g_3559da0@0" ObjectIDZND2="10299@x" Pin0InfoVect0LinkObjId="SW-56399_0" Pin0InfoVect1LinkObjId="g_3559da0_0" Pin0InfoVect2LinkObjId="SW-56403_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56403_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="667,-728 712,-728 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2ad2b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="712,-728 712,-715 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="10299@x" ObjectIDND1="g_3559da0@0" ObjectIDND2="10299@x" ObjectIDZND0="10295@1" Pin0InfoVect0LinkObjId="SW-56399_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-56403_0" Pin1InfoVect1LinkObjId="g_3559da0_0" Pin1InfoVect2LinkObjId="SW-56403_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="712,-728 712,-715 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2ad2db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="712,-766 712,-728 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_3559da0@0" ObjectIDND1="10299@x" ObjectIDND2="10295@x" ObjectIDZND0="10299@x" ObjectIDZND1="10295@x" ObjectIDZND2="10299@x" Pin0InfoVect0LinkObjId="SW-56403_0" Pin0InfoVect1LinkObjId="SW-56399_0" Pin0InfoVect2LinkObjId="SW-56403_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3559da0_0" Pin1InfoVect1LinkObjId="SW-56403_0" Pin1InfoVect2LinkObjId="SW-56399_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="712,-766 712,-728 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ad34c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1068,-11 1068,52 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="10322@x" ObjectIDND1="g_3effa50@0" ObjectIDND2="10325@x" ObjectIDZND0="10323@1" Pin0InfoVect0LinkObjId="SW-56427_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-56426_0" Pin1InfoVect1LinkObjId="g_3effa50_0" Pin1InfoVect2LinkObjId="SW-56429_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1068,-11 1068,52 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3425580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="712,-766 712,-728 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="10299@x" ObjectIDND1="10295@x" ObjectIDND2="g_3559da0@0" ObjectIDZND0="10299@x" ObjectIDZND1="10295@x" ObjectIDZND2="g_3559da0@0" Pin0InfoVect0LinkObjId="SW-56403_0" Pin0InfoVect1LinkObjId="SW-56399_0" Pin0InfoVect2LinkObjId="g_3559da0_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-56403_0" Pin1InfoVect1LinkObjId="SW-56399_0" Pin1InfoVect2LinkObjId="g_3559da0_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="712,-766 712,-728 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33d07a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="761,-256 713,-256 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="10351@0" ObjectIDZND0="10301@x" ObjectIDZND1="10300@x" Pin0InfoVect0LinkObjId="SW-56405_0" Pin0InfoVect1LinkObjId="SW-56404_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56453_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="761,-256 713,-256 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33d1110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="713,-234 713,-256 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="10301@1" ObjectIDZND0="10351@x" ObjectIDZND1="10300@x" Pin0InfoVect0LinkObjId="SW-56453_0" Pin0InfoVect1LinkObjId="SW-56404_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56405_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="713,-234 713,-256 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33d1300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="713,-256 713,-277 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="10351@x" ObjectIDND1="10301@x" ObjectIDZND0="10300@0" Pin0InfoVect0LinkObjId="SW-56404_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-56453_0" Pin1InfoVect1LinkObjId="SW-56405_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="713,-256 713,-277 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_33d1510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="712,-779 712,-815 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="10299@x" ObjectIDND1="10295@x" ObjectIDND2="10299@x" ObjectIDZND0="17996@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-56403_0" Pin1InfoVect1LinkObjId="SW-56399_0" Pin1InfoVect2LinkObjId="SW-56403_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="712,-779 712,-815 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_33d1fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="712,-766 712,-779 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="10299@x" ObjectIDND1="10295@x" ObjectIDND2="10299@x" ObjectIDZND0="g_2b68130@0" ObjectIDZND1="17996@1" Pin0InfoVect0LinkObjId="g_2b68130_0" Pin0InfoVect1LinkObjId="g_33d1510_1" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-56403_0" Pin1InfoVect1LinkObjId="SW-56399_0" Pin1InfoVect2LinkObjId="SW-56403_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="712,-766 712,-779 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_33d2210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="712,-779 761,-779 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="10299@x" ObjectIDND1="10295@x" ObjectIDND2="10299@x" ObjectIDZND0="g_2b68130@0" Pin0InfoVect0LinkObjId="g_2b68130_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-56403_0" Pin1InfoVect1LinkObjId="SW-56399_0" Pin1InfoVect2LinkObjId="SW-56403_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="712,-779 761,-779 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_34019c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="712,-553 714,-576 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="10294@1" ObjectIDZND0="48135@0" Pin0InfoVect0LinkObjId="g_250d800_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56398_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="712,-553 714,-576 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_地调直调_风电.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="155" x="-227" y="-940"/></g>
   <g href="cx_索引_接线图_地调直调_风电.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-276" y="-957"/></g>
   <g href="AVC仙人洞.svg" style="fill-opacity:0"><rect height="48" qtmmishow="hidden" stroke="rgb(255,0,0)" width="143" x="-91" y="-796"/></g>
  </g><g id="MultiLine_Layer">
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="593,-430 621,-430 " stroke="rgb(170,85,127)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="550,95 548,93 " stroke="rgb(255,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="555,84 558,84 " stroke="rgb(255,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="555,114 530,114 530,143 " stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-56475" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 353.000000 171.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="56475" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10304"/>
     <cge:Term_Ref ObjectID="14313"/>
    <cge:TPSR_Ref TObjectID="10304"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-56476" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 353.000000 171.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="56476" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10304"/>
     <cge:Term_Ref ObjectID="14313"/>
    <cge:TPSR_Ref TObjectID="10304"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-56489" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 353.000000 171.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="56489" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10304"/>
     <cge:Term_Ref ObjectID="14313"/>
    <cge:TPSR_Ref TObjectID="10304"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-56481" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 710.000000 171.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="56481" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10315"/>
     <cge:Term_Ref ObjectID="14335"/>
    <cge:TPSR_Ref TObjectID="10315"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-56482" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 710.000000 171.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="56482" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10315"/>
     <cge:Term_Ref ObjectID="14335"/>
    <cge:TPSR_Ref TObjectID="10315"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-56480" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 710.000000 171.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="56480" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10315"/>
     <cge:Term_Ref ObjectID="14335"/>
    <cge:TPSR_Ref TObjectID="10315"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-56478" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1352.000000 172.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="56478" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10327"/>
     <cge:Term_Ref ObjectID="14359"/>
    <cge:TPSR_Ref TObjectID="10327"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-56479" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1352.000000 172.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="56479" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10327"/>
     <cge:Term_Ref ObjectID="14359"/>
    <cge:TPSR_Ref TObjectID="10327"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-56477" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1352.000000 172.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="56477" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10327"/>
     <cge:Term_Ref ObjectID="14359"/>
    <cge:TPSR_Ref TObjectID="10327"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-56491" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1651.000000 172.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="56491" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10338"/>
     <cge:Term_Ref ObjectID="14381"/>
    <cge:TPSR_Ref TObjectID="10338"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-56485" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1651.000000 172.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="56485" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10338"/>
     <cge:Term_Ref ObjectID="14381"/>
    <cge:TPSR_Ref TObjectID="10338"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-56490" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1651.000000 172.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="56490" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10338"/>
     <cge:Term_Ref ObjectID="14381"/>
    <cge:TPSR_Ref TObjectID="10338"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-56456" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 867.000000 -637.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="56456" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10293"/>
     <cge:Term_Ref ObjectID="14291"/>
    <cge:TPSR_Ref TObjectID="10293"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-56457" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 867.000000 -637.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="56457" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10293"/>
     <cge:Term_Ref ObjectID="14291"/>
    <cge:TPSR_Ref TObjectID="10293"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-56455" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 867.000000 -637.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="56455" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10293"/>
     <cge:Term_Ref ObjectID="14291"/>
    <cge:TPSR_Ref TObjectID="10293"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-56461" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 867.000000 -637.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="56461" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10293"/>
     <cge:Term_Ref ObjectID="14291"/>
    <cge:TPSR_Ref TObjectID="10293"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-56465" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 867.000000 -637.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="56465" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10293"/>
     <cge:Term_Ref ObjectID="14291"/>
    <cge:TPSR_Ref TObjectID="10293"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-56467" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 877.000000 -345.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="56467" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10300"/>
     <cge:Term_Ref ObjectID="14305"/>
    <cge:TPSR_Ref TObjectID="10300"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-56468" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 877.000000 -345.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="56468" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10300"/>
     <cge:Term_Ref ObjectID="14305"/>
    <cge:TPSR_Ref TObjectID="10300"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-56466" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 877.000000 -345.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="56466" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10300"/>
     <cge:Term_Ref ObjectID="14305"/>
    <cge:TPSR_Ref TObjectID="10300"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="2" id="ME-56488" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1094.000000 -465.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="56488" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10350"/>
     <cge:Term_Ref ObjectID="14407"/>
    <cge:TPSR_Ref TObjectID="10350"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-56469" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 237.000000 -289.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="56469" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10357"/>
     <cge:Term_Ref ObjectID="14415"/>
    <cge:TPSR_Ref TObjectID="10357"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-56470" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 237.000000 -289.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="56470" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10357"/>
     <cge:Term_Ref ObjectID="14415"/>
    <cge:TPSR_Ref TObjectID="10357"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-56471" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 237.000000 -289.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="56471" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10357"/>
     <cge:Term_Ref ObjectID="14415"/>
    <cge:TPSR_Ref TObjectID="10357"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-56473" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 237.000000 -289.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="56473" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10357"/>
     <cge:Term_Ref ObjectID="14415"/>
    <cge:TPSR_Ref TObjectID="10357"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-56472" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 237.000000 -289.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="56472" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10357"/>
     <cge:Term_Ref ObjectID="14415"/>
    <cge:TPSR_Ref TObjectID="10357"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-56474" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 237.000000 -289.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="56474" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10357"/>
     <cge:Term_Ref ObjectID="14415"/>
    <cge:TPSR_Ref TObjectID="10357"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-56484" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1089.000000 225.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="56484" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10320"/>
     <cge:Term_Ref ObjectID="14345"/>
    <cge:TPSR_Ref TObjectID="10320"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-56493" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1089.000000 225.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="56493" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10320"/>
     <cge:Term_Ref ObjectID="14345"/>
    <cge:TPSR_Ref TObjectID="10320"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-56483" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1089.000000 225.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="56483" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10320"/>
     <cge:Term_Ref ObjectID="14345"/>
    <cge:TPSR_Ref TObjectID="10320"/></metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_XRD" endPointId="0" endStationName="CX_GY" flowDrawDirect="1" flowShape="0" id="AC-110kV.xianguo_line" runFlow="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="712,-812 712,-865 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="17996" ObjectName="AC-110kV.xianguo_line"/>
    <cge:TPSR_Ref TObjectID="17996_SS-104"/></metadata>
   <polyline fill="none" opacity="0" points="712,-812 712,-865 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_XRD"/>
</svg>