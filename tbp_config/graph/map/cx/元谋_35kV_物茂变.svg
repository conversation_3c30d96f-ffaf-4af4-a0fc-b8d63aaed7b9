<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-237" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="-736 -1332 2093 1258">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="capacitor:shape20">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="37" x2="37" y1="13" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.504561" x1="37" x2="37" y1="102" y2="115"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.550926" x1="57" x2="57" y1="116" y2="110"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.431962" x1="2" x2="2" y1="63" y2="97"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.550926" x1="20" x2="20" y1="116" y2="110"/>
    <rect height="26" stroke-width="0.398039" width="12" x="31" y="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.603704" x1="13" x2="37" y1="105" y2="105"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.393258" x1="21" x2="56" y1="116" y2="116"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.603704" x1="13" x2="37" y1="53" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.715488" x1="13" x2="13" y1="53" y2="62"/>
    <polyline arcFlag="1" points="13,73 12,73 12,73 11,73 10,73 10,72 9,72 8,71 8,71 8,70 7,69 7,69 7,68 7,67 7,66 7,66 8,65 8,64 8,64 9,63 10,63 10,62 11,62 12,62 12,62 13,62 " stroke-width="0.0428972"/>
    <polyline arcFlag="1" points="13,84 12,84 12,84 11,84 10,84 10,83 9,83 8,82 8,82 8,81 7,80 7,80 7,79 7,78 7,77 7,77 8,76 8,75 8,75 9,74 10,74 10,73 11,73 12,73 12,73 13,73 " stroke-width="0.0428972"/>
    <polyline arcFlag="1" points="13,95 12,95 12,95 11,95 10,95 10,94 9,94 8,93 8,93 8,92 7,91 7,91 7,90 7,89 7,88 7,88 8,87 8,86 8,86 9,85 10,85 10,84 11,84 12,84 12,84 13,84 " stroke-width="0.0428972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.715488" x1="13" x2="13" y1="95" y2="104"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="27" x2="47" y1="101" y2="101"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="27" x2="47" y1="93" y2="93"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.774005" x1="37" x2="37" y1="26" y2="93"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="24" x2="36" y1="26" y2="26"/>
    <polyline arcFlag="1" points="37,13 39,13 41,14 42,14 44,15 45,16 47,17 48,19 49,21 49,22 50,24 50,26 50,28 49,30 49,31 48,33 47,34 45,36 44,37 42,38 41,38 39,39 37,39 35,39 33,38 32,38 30,37 29,36 27,34 26,33 25,31 25,30 24,28 24,26 " stroke-width="0.0972"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="hydroGenerator:shape3">
    <polyline arcFlag="1" points="25,25 25,26 25,27 25,28 24,29 24,30 23,31 23,31 22,32 21,32 20,33 19,33 18,33 17,34 16,33 15,33 14,33 13,32 13,32 12,31 11,31 11,30 10,29 10,28 10,27 9,26 10,25 " stroke-width="1.14"/>
    <circle cx="24" cy="24" fillStyle="0" r="24" stroke-width="0.5"/>
    <polyline points="40,25 41,24 40,24 40,23 40,22 39,21 39,20 38,19 37,19 37,18 36,18 35,17 34,17 33,17 32,17 31,17 30,18 29,18 28,19 27,19 27,20 26,21 26,22 25,23 25,24 25,24 25,25 " stroke-width="1.14"/>
   </symbol>
   <symbol id="lightningRod:shape21">
    <rect height="26" stroke-width="1.99997" width="11" x="2" y="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="7" x2="7" y1="50" y2="5"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="8" x2="8" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape157">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,36 0,46 10,46 5,36 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,24 0,14 10,14 5,24 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="59" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape55">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="27,39 5,17 5,5 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="42"/>
    <rect height="4" stroke-width="1" width="19" x="7" y="26"/>
   </symbol>
   <symbol id="lightningRod:shape18">
    <rect height="29" stroke-width="2" width="15" x="1" y="14"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,49 8,55 15,49 " stroke-width="2"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,55 8,61 15,55 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="8" x2="8" y1="55" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.00003" x1="8" x2="11" y1="23" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.00003" x1="9" x2="6" y1="23" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="8" x2="8" y1="8" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.99999" x1="6" x2="11" y1="1" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.99999" x1="1" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.99999" x1="4" x2="13" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="8" x2="8" y1="51" y2="23"/>
   </symbol>
   <symbol id="lightningRod:shape113">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="9" y1="5" y2="36"/>
    <rect height="31" stroke-width="1" width="15" x="1" y="5"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="63" x2="63" y1="6" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="61" x2="61" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="58" x2="58" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="58" x2="49" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape7">
    <polyline DF8003:Layer="PUBLIC" points="1,5 10,17 19,5 1,5 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="20" y2="23"/>
    <polyline DF8003:Layer="PUBLIC" points="19,44 10,32 1,44 19,44 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="29" y2="26"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape30_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="31" x2="14" y1="13" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape30_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="12" x2="34" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape30-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="31" x2="14" y1="13" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape30-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="12" x2="34" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape12_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="6" x2="12" y1="2" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="6" x2="0" y1="8" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="6" x2="12" y1="8" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="6" x2="6" y1="8" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="6" x2="6" y1="2" y2="-1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="6" x2="0" y1="2" y2="8"/>
   </symbol>
   <symbol id="switch2:shape12_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="6" x2="0" y1="6" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="6" x2="12" y1="6" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="6" x2="6" y1="2" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="6" x2="6" y1="2" y2="-2"/>
   </symbol>
   <symbol id="switch2:shape12-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="6" x2="12" y1="2" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="6" x2="0" y1="8" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="6" x2="12" y1="8" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="6" x2="6" y1="8" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="6" x2="6" y1="2" y2="-1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="6" x2="0" y1="2" y2="8"/>
   </symbol>
   <symbol id="switch2:shape12-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="6" x2="0" y1="7" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="6" x2="12" y1="6" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="6" x2="6" y1="2" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="6" x2="6" y1="2" y2="-2"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="transformer2:shape25_0">
    <circle cx="16" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,57 40,57 40,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="42" y2="0"/>
    <polyline DF8003:Layer="PUBLIC" points="15,14 21,27 9,27 15,14 15,15 15,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="43" x2="40" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="15" y1="52" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="57" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="57" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="58" y2="62"/>
   </symbol>
   <symbol id="transformer2:shape25_1">
    <circle cx="16" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="12,76 19,76 16,83 12,76 "/>
   </symbol>
   <symbol id="transformer2:shape22_0">
    <circle cx="37" cy="66" fillStyle="0" r="26.5" stroke-width="0.63865"/>
    <polyline points="64,100 1,37 " stroke-width="1.15789"/>
    <polyline points="58,100 64,100 " stroke-width="1.15789"/>
    <polyline points="64,100 64,93 " stroke-width="1.15789"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="30" y1="71" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="30" y1="71" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="45" x2="38" y1="79" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="45" x2="38" y1="79" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="38" y1="62" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="38" y1="62" y2="71"/>
   </symbol>
   <symbol id="transformer2:shape22_1">
    <ellipse cx="37" cy="29" fillStyle="0" rx="26.5" ry="25.5" stroke-width="0.62032"/>
    <polyline DF8003:Layer="PUBLIC" points="38,34 31,19 46,19 38,34 38,34 38,34 "/>
   </symbol>
   <symbol id="voltageTransformer:shape65">
    <ellipse cx="19" cy="19" rx="7.5" ry="6.5" stroke-width="0.66594"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="46" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="41" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.10043" x1="20" x2="20" y1="9" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="23" x2="20" y1="7" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="17" x2="20" y1="7" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.10043" x1="8" x2="8" y1="20" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="11" x2="8" y1="18" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="5" x2="8" y1="18" y2="20"/>
    <ellipse cx="19" cy="9" rx="7.5" ry="7" stroke-width="0.66594"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.246311" x1="5" x2="9" y1="7" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.245503" x1="5" x2="9" y1="8" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.238574" x1="9" x2="9" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="41" y1="18" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="35" y1="30" y2="38"/>
    <rect height="13" stroke-width="1" width="7" x="32" y="17"/>
    <ellipse cx="8" cy="19" rx="7.5" ry="6.5" stroke-width="0.66594"/>
    <ellipse cx="8" cy="9" rx="7.5" ry="7" stroke-width="0.66594"/>
    <polyline points="27,8 35,8 35,18 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="38" y1="40" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="37" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="40" y1="38" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.10043" x1="20" x2="20" y1="20" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="23" x2="20" y1="18" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="17" x2="20" y1="18" y2="20"/>
   </symbol>
   <symbol id="voltageTransformer:shape56">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="1" y1="58" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="20" y1="58" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="20" y1="38" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="1" x2="13" y1="51" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="22" y1="59" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="22" y1="37" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="25" y1="61" y2="61"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="22" y1="70" y2="61"/>
    <ellipse cx="21" cy="8" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <ellipse cx="21" cy="16" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="25" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="25" y1="18" y2="18"/>
   </symbol>
   <symbol id="voltageTransformer:shape33">
    <ellipse cx="8" cy="16" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <ellipse cx="8" cy="8" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="67" y2="23"/>
    <rect height="24" stroke-width="0.379884" width="14" x="1" y="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="11" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="11" y1="6" y2="6"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2569530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_25906e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2555000" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2ccb870" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2de6a20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2e07b50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2cdabc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2eacf90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2d35330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2d35330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2e2b4a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2e2b4a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2d709f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2d709f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_2e076b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_26ce910" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2e3f2f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2dcdca0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2dd3f10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2d18690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2daaa80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2b7a030" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2e188a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2ec70f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2e77050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2b7a8f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2e52720" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2ea7120" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2e60d60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2d18140" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2cc3790" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2ea30a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2ca9d50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2545540" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2e5a930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1268" width="2103" x="-741" y="-1337"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,0)" stroke-width="1" x1="1348" x2="1357" y1="-589" y2="-589"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-171110">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 230.333333 -990.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27249" ObjectName="SW-YM_WM.YM_WM_3711SW"/>
     <cge:Meas_Ref ObjectId="171110"/>
    <cge:TPSR_Ref TObjectID="27249"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171111">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 230.333333 -1117.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27250" ObjectName="SW-YM_WM.YM_WM_3716SW"/>
     <cge:Meas_Ref ObjectId="171111"/>
    <cge:TPSR_Ref TObjectID="27250"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171113">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 261.819337 -1172.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27252" ObjectName="SW-YM_WM.YM_WM_37167SW"/>
     <cge:Meas_Ref ObjectId="171113"/>
    <cge:TPSR_Ref TObjectID="27252"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171161">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -25.666667 -1027.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27260" ObjectName="SW-YM_WM.YM_WM_3901SW"/>
     <cge:Meas_Ref ObjectId="171161"/>
    <cge:TPSR_Ref TObjectID="27260"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171163">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 7.819337 -1078.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27262" ObjectName="SW-YM_WM.YM_WM_39017SW"/>
     <cge:Meas_Ref ObjectId="171163"/>
    <cge:TPSR_Ref TObjectID="27262"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171162">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.819337 -1002.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27261" ObjectName="SW-YM_WM.YM_WM_39010SW"/>
     <cge:Meas_Ref ObjectId="171162"/>
    <cge:TPSR_Ref TObjectID="27261"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171112">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 267.819337 -1045.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27251" ObjectName="SW-YM_WM.YM_WM_37117SW"/>
     <cge:Meas_Ref ObjectId="171112"/>
    <cge:TPSR_Ref TObjectID="27251"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171114">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 264.819337 -1104.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27253" ObjectName="SW-YM_WM.YM_WM_37160SW"/>
     <cge:Meas_Ref ObjectId="171114"/>
    <cge:TPSR_Ref TObjectID="27253"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171131">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 582.333333 -989.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27255" ObjectName="SW-YM_WM.YM_WM_3721SW"/>
     <cge:Meas_Ref ObjectId="171131"/>
    <cge:TPSR_Ref TObjectID="27255"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171132">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 582.333333 -1116.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27256" ObjectName="SW-YM_WM.YM_WM_3726SW"/>
     <cge:Meas_Ref ObjectId="171132"/>
    <cge:TPSR_Ref TObjectID="27256"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171135">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 611.819337 -1173.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27259" ObjectName="SW-YM_WM.YM_WM_37267SW"/>
     <cge:Meas_Ref ObjectId="171135"/>
    <cge:TPSR_Ref TObjectID="27259"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171133">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 617.819337 -1044.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27257" ObjectName="SW-YM_WM.YM_WM_37217SW"/>
     <cge:Meas_Ref ObjectId="171133"/>
    <cge:TPSR_Ref TObjectID="27257"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171134">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 614.819337 -1103.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27258" ObjectName="SW-YM_WM.YM_WM_37260SW"/>
     <cge:Meas_Ref ObjectId="171134"/>
    <cge:TPSR_Ref TObjectID="27258"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171174">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 86.819337 -895.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27269" ObjectName="SW-YM_WM.YM_WM_30117SW"/>
     <cge:Meas_Ref ObjectId="171174"/>
    <cge:TPSR_Ref TObjectID="27269"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171240">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 838.000000 -911.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27274" ObjectName="SW-YM_WM.YM_WM_3021SW"/>
     <cge:Meas_Ref ObjectId="171240"/>
    <cge:TPSR_Ref TObjectID="27274"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171241">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 888.819337 -902.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27275" ObjectName="SW-YM_WM.YM_WM_30217SW"/>
     <cge:Meas_Ref ObjectId="171241"/>
    <cge:TPSR_Ref TObjectID="27275"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171173">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 36.000000 -913.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27268" ObjectName="SW-YM_WM.YM_WM_3011SW"/>
     <cge:Meas_Ref ObjectId="171173"/>
    <cge:TPSR_Ref TObjectID="27268"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171413">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -238.000000 -223.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27309" ObjectName="SW-YM_WM.YM_WM_0766SW"/>
     <cge:Meas_Ref ObjectId="171413"/>
    <cge:TPSR_Ref TObjectID="27309"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171415">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -212.180663 -362.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27312" ObjectName="SW-YM_WM.YM_WM_07637SW"/>
     <cge:Meas_Ref ObjectId="171415"/>
    <cge:TPSR_Ref TObjectID="27312"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171306">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 -263.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27280" ObjectName="SW-YM_WM.YM_WM_0726SW"/>
     <cge:Meas_Ref ObjectId="171306"/>
    <cge:TPSR_Ref TObjectID="27280"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171308">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 31.819337 -379.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27283" ObjectName="SW-YM_WM.YM_WM_07237SW"/>
     <cge:Meas_Ref ObjectId="171308"/>
    <cge:TPSR_Ref TObjectID="27283"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171324">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 162.000000 -232.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27285" ObjectName="SW-YM_WM.YM_WM_0736SW"/>
     <cge:Meas_Ref ObjectId="171324"/>
    <cge:TPSR_Ref TObjectID="27285"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171341">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 358.000000 -236.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27289" ObjectName="SW-YM_WM.YM_WM_0746SW"/>
     <cge:Meas_Ref ObjectId="171341"/>
    <cge:TPSR_Ref TObjectID="27289"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171343">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 383.819337 -360.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27292" ObjectName="SW-YM_WM.YM_WM_07437SW"/>
     <cge:Meas_Ref ObjectId="171343"/>
    <cge:TPSR_Ref TObjectID="27292"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171359">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 632.000000 -224.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27294" ObjectName="SW-YM_WM.YM_WM_0816SW"/>
     <cge:Meas_Ref ObjectId="171359"/>
    <cge:TPSR_Ref TObjectID="27294"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171361">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 657.819337 -363.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27297" ObjectName="SW-YM_WM.YM_WM_08137SW"/>
     <cge:Meas_Ref ObjectId="171361"/>
    <cge:TPSR_Ref TObjectID="27297"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171377">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 821.000000 -229.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27299" ObjectName="SW-YM_WM.YM_WM_0826SW"/>
     <cge:Meas_Ref ObjectId="171377"/>
    <cge:TPSR_Ref TObjectID="27299"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171379">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 846.819337 -356.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27302" ObjectName="SW-YM_WM.YM_WM_08237SW"/>
     <cge:Meas_Ref ObjectId="171379"/>
    <cge:TPSR_Ref TObjectID="27302"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171395">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1004.000000 -228.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27304" ObjectName="SW-YM_WM.YM_WM_0836SW"/>
     <cge:Meas_Ref ObjectId="171395"/>
    <cge:TPSR_Ref TObjectID="27304"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171397">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1029.819337 -352.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27307" ObjectName="SW-YM_WM.YM_WM_08337SW"/>
     <cge:Meas_Ref ObjectId="171397"/>
    <cge:TPSR_Ref TObjectID="27307"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171430">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1191.000000 -224.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27314" ObjectName="SW-YM_WM.YM_WM_0846SW"/>
     <cge:Meas_Ref ObjectId="171430"/>
    <cge:TPSR_Ref TObjectID="27314"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171432">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1216.819337 -351.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27317" ObjectName="SW-YM_WM.YM_WM_08437SW"/>
     <cge:Meas_Ref ObjectId="171432"/>
    <cge:TPSR_Ref TObjectID="27317"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171447">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -141.000000 -725.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27319" ObjectName="SW-YM_WM.YM_WM_0756SW"/>
     <cge:Meas_Ref ObjectId="171447"/>
    <cge:TPSR_Ref TObjectID="27319"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171450">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -115.180663 -791.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27323" ObjectName="SW-YM_WM.YM_WM_07567SW"/>
     <cge:Meas_Ref ObjectId="171450"/>
    <cge:TPSR_Ref TObjectID="27323"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171451">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -112.180663 -635.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27324" ObjectName="SW-YM_WM.YM_WM_07537SW"/>
     <cge:Meas_Ref ObjectId="171451"/>
    <cge:TPSR_Ref TObjectID="27324"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171449">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -112.180663 -713.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27322" ObjectName="SW-YM_WM.YM_WM_07560SW"/>
     <cge:Meas_Ref ObjectId="171449"/>
    <cge:TPSR_Ref TObjectID="27322"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171470">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1083.000000 -726.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27328" ObjectName="SW-YM_WM.YM_WM_0856SW"/>
     <cge:Meas_Ref ObjectId="171470"/>
    <cge:TPSR_Ref TObjectID="27328"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171471">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1108.819337 -792.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27329" ObjectName="SW-YM_WM.YM_WM_08567SW"/>
     <cge:Meas_Ref ObjectId="171471"/>
    <cge:TPSR_Ref TObjectID="27329"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171472">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1111.819337 -636.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27330" ObjectName="SW-YM_WM.YM_WM_08537SW"/>
     <cge:Meas_Ref ObjectId="171472"/>
    <cge:TPSR_Ref TObjectID="27330"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171166">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 286.000000 -590.000000)" xlink:href="#switch2:shape12_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27263" ObjectName="SW-YM_WM.YM_WM_0901XC"/>
     <cge:Meas_Ref ObjectId="171166"/>
    <cge:TPSR_Ref TObjectID="27263"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171169">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 700.000000 -578.000000)" xlink:href="#switch2:shape12_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27265" ObjectName="SW-YM_WM.YM_WM_0902XC"/>
     <cge:Meas_Ref ObjectId="171169"/>
    <cge:TPSR_Ref TObjectID="27265"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171596">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 187.819337 -363.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27341" ObjectName="SW-YM_WM.YM_WM_07337SW"/>
     <cge:Meas_Ref ObjectId="171596"/>
    <cge:TPSR_Ref TObjectID="27341"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196757">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 888.333333 -980.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30132" ObjectName="SW-YM_WM.YM_WM_3731SW"/>
     <cge:Meas_Ref ObjectId="196757"/>
    <cge:TPSR_Ref TObjectID="30132"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196759">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 888.333333 -1152.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30134" ObjectName="SW-YM_WM.YM_WM_3736SW"/>
     <cge:Meas_Ref ObjectId="196759"/>
    <cge:TPSR_Ref TObjectID="30134"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196762">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 917.819337 -1198.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30137" ObjectName="SW-YM_WM.YM_WM_37367SW"/>
     <cge:Meas_Ref ObjectId="196762"/>
    <cge:TPSR_Ref TObjectID="30137"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196760">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 924.819337 -1031.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30135" ObjectName="SW-YM_WM.YM_WM_37317SW"/>
     <cge:Meas_Ref ObjectId="196760"/>
    <cge:TPSR_Ref TObjectID="30135"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196761">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 927.819337 -1090.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30136" ObjectName="SW-YM_WM.YM_WM_37327SW"/>
     <cge:Meas_Ref ObjectId="196761"/>
    <cge:TPSR_Ref TObjectID="30136"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196758">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 888.333333 -1041.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30133" ObjectName="SW-YM_WM.YM_WM_3732SW"/>
     <cge:Meas_Ref ObjectId="196758"/>
    <cge:TPSR_Ref TObjectID="30133"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171414">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -239.000000 -447.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27310" ObjectName="SW-YM_WM.YM_WM_076XC"/>
     <cge:Meas_Ref ObjectId="171414"/>
    <cge:TPSR_Ref TObjectID="27310"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171414">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -239.000000 -372.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27311" ObjectName="SW-YM_WM.YM_WM_076XC1"/>
     <cge:Meas_Ref ObjectId="171414"/>
    <cge:TPSR_Ref TObjectID="27311"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171307">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 -460.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27281" ObjectName="SW-YM_WM.YM_WM_072XC"/>
     <cge:Meas_Ref ObjectId="171307"/>
    <cge:TPSR_Ref TObjectID="27281"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171307">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 -385.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27282" ObjectName="SW-YM_WM.YM_WM_072XC1"/>
     <cge:Meas_Ref ObjectId="171307"/>
    <cge:TPSR_Ref TObjectID="27282"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171325">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 161.000000 -454.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27286" ObjectName="SW-YM_WM.YM_WM_073XC"/>
     <cge:Meas_Ref ObjectId="171325"/>
    <cge:TPSR_Ref TObjectID="27286"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171325">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 161.000000 -379.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27287" ObjectName="SW-YM_WM.YM_WM_073XC1"/>
     <cge:Meas_Ref ObjectId="171325"/>
    <cge:TPSR_Ref TObjectID="27287"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171342">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 357.000000 -457.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27290" ObjectName="SW-YM_WM.YM_WM_074XC"/>
     <cge:Meas_Ref ObjectId="171342"/>
    <cge:TPSR_Ref TObjectID="27290"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171342">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 357.000000 -380.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27291" ObjectName="SW-YM_WM.YM_WM_074XC1"/>
     <cge:Meas_Ref ObjectId="171342"/>
    <cge:TPSR_Ref TObjectID="27291"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171360">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 632.000000 -447.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27295" ObjectName="SW-YM_WM.YM_WM_081XC"/>
     <cge:Meas_Ref ObjectId="171360"/>
    <cge:TPSR_Ref TObjectID="27295"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171360">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 631.000000 -373.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27296" ObjectName="SW-YM_WM.YM_WM_081XC1"/>
     <cge:Meas_Ref ObjectId="171360"/>
    <cge:TPSR_Ref TObjectID="27296"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171378">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 820.000000 -450.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27300" ObjectName="SW-YM_WM.YM_WM_082XC"/>
     <cge:Meas_Ref ObjectId="171378"/>
    <cge:TPSR_Ref TObjectID="27300"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171378">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 820.000000 -375.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27301" ObjectName="SW-YM_WM.YM_WM_082XC1"/>
     <cge:Meas_Ref ObjectId="171378"/>
    <cge:TPSR_Ref TObjectID="27301"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171396">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1003.000000 -448.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27305" ObjectName="SW-YM_WM.YM_WM_083XC"/>
     <cge:Meas_Ref ObjectId="171396"/>
    <cge:TPSR_Ref TObjectID="27305"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171396">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1003.000000 -373.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27306" ObjectName="SW-YM_WM.YM_WM_083XC1"/>
     <cge:Meas_Ref ObjectId="171396"/>
    <cge:TPSR_Ref TObjectID="27306"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171431">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1190.000000 -450.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27315" ObjectName="SW-YM_WM.YM_WM_084XC"/>
     <cge:Meas_Ref ObjectId="171431"/>
    <cge:TPSR_Ref TObjectID="27315"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171431">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1190.000000 -373.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27316" ObjectName="SW-YM_WM.YM_WM_084XC1"/>
     <cge:Meas_Ref ObjectId="171431"/>
    <cge:TPSR_Ref TObjectID="27316"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171448">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -142.000000 -591.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27321" ObjectName="SW-YM_WM.YM_WM_075XC1"/>
     <cge:Meas_Ref ObjectId="171448"/>
    <cge:TPSR_Ref TObjectID="27321"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171448">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -142.000000 -516.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27320" ObjectName="SW-YM_WM.YM_WM_075XC"/>
     <cge:Meas_Ref ObjectId="171448"/>
    <cge:TPSR_Ref TObjectID="27320"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171224">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 35.000000 -578.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27272" ObjectName="SW-YM_WM.YM_WM_001XC1"/>
     <cge:Meas_Ref ObjectId="171224"/>
    <cge:TPSR_Ref TObjectID="27272"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171224">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 35.000000 -503.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27271" ObjectName="SW-YM_WM.YM_WM_001XC"/>
     <cge:Meas_Ref ObjectId="171224"/>
    <cge:TPSR_Ref TObjectID="27271"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171490">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 421.000000 -594.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27333" ObjectName="SW-YM_WM.YM_WM_012XC1"/>
     <cge:Meas_Ref ObjectId="171490"/>
    <cge:TPSR_Ref TObjectID="27333"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171490">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 421.000000 -522.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27332" ObjectName="SW-YM_WM.YM_WM_012XC"/>
     <cge:Meas_Ref ObjectId="171490"/>
    <cge:TPSR_Ref TObjectID="27332"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171492">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 566.000000 -579.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27335" ObjectName="SW-YM_WM.YM_WM_0121XC1"/>
     <cge:Meas_Ref ObjectId="171492"/>
    <cge:TPSR_Ref TObjectID="27335"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171492">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 566.000000 -522.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27334" ObjectName="SW-YM_WM.YM_WM_0121XC"/>
     <cge:Meas_Ref ObjectId="171492"/>
    <cge:TPSR_Ref TObjectID="27334"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171249">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 837.000000 -578.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27278" ObjectName="SW-YM_WM.YM_WM_002XC1"/>
     <cge:Meas_Ref ObjectId="171249"/>
    <cge:TPSR_Ref TObjectID="27278"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171249">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 837.000000 -504.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27277" ObjectName="SW-YM_WM.YM_WM_002XC"/>
     <cge:Meas_Ref ObjectId="171249"/>
    <cge:TPSR_Ref TObjectID="27277"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171469">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1082.000000 -583.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27327" ObjectName="SW-YM_WM.YM_WM_085XC1"/>
     <cge:Meas_Ref ObjectId="171469"/>
    <cge:TPSR_Ref TObjectID="27327"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171469">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1082.000000 -508.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27326" ObjectName="SW-YM_WM.YM_WM_085XC"/>
     <cge:Meas_Ref ObjectId="171469"/>
    <cge:TPSR_Ref TObjectID="27326"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-YM_WM.YM_WM_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-170,-969 1152,-969 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="27244" ObjectName="BS-YM_WM.YM_WM_3IM"/>
    <cge:TPSR_Ref TObjectID="27244"/></metadata>
   <polyline fill="none" opacity="0" points="-170,-969 1152,-969 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-YM_WM.YM_WM_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-288,-497 448,-497 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="27245" ObjectName="BS-YM_WM.YM_WM_9IM"/>
    <cge:TPSR_Ref TObjectID="27245"/></metadata>
   <polyline fill="none" opacity="0" points="-288,-497 448,-497 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-YM_WM.YM_WM_9IIM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="544,-494 1276,-494 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="27246" ObjectName="BS-YM_WM.YM_WM_9IIM"/>
    <cge:TPSR_Ref TObjectID="27246"/></metadata>
   <polyline fill="none" opacity="0" points="544,-494 1276,-494 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-YM_WM.YM_WM_Cb1">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -169.000000 -805.000000)" xlink:href="#capacitor:shape20"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41293" ObjectName="CB-YM_WM.YM_WM_Cb1"/>
    <cge:TPSR_Ref TObjectID="41293"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-YM_WM.YM_WM_Cb2">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1055.000000 -806.000000)" xlink:href="#capacitor:shape20"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41294" ObjectName="CB-YM_WM.YM_WM_Cb2"/>
    <cge:TPSR_Ref TObjectID="41294"/></metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 473.000000 -738.000000)" xlink:href="#transformer2:shape25_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 473.000000 -738.000000)" xlink:href="#transformer2:shape25_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.717391 -0.000000 0.000000 -0.492857 -68.870588 -171.509091)" xlink:href="#transformer2:shape25_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(0.717391 -0.000000 0.000000 -0.492857 -68.870588 -171.509091)" xlink:href="#transformer2:shape25_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-YM_WM.YM_WM_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="38661"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(0.863636 -0.000000 0.000000 -0.882353 12.000000 -680.000000)" xlink:href="#transformer2:shape22_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(0.863636 -0.000000 0.000000 -0.882353 12.000000 -680.000000)" xlink:href="#transformer2:shape22_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="27336" ObjectName="TF-YM_WM.YM_WM_1T"/>
    <cge:TPSR_Ref TObjectID="27336"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-YM_WM.YM_WM_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="38665"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(0.863636 -0.000000 0.000000 -0.882353 814.500000 -690.000000)" xlink:href="#transformer2:shape22_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(0.863636 -0.000000 0.000000 -0.882353 814.500000 -690.000000)" xlink:href="#transformer2:shape22_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="27337" ObjectName="TF-YM_WM.YM_WM_2T"/>
    <cge:TPSR_Ref TObjectID="27337"/></metadata>
   </g>
  </g><g id="HydroGenerator_Layer">
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-10KV" transform="matrix(0.591837 -0.000000 0.000000 -0.666667 52.000000 -200.000000)" xlink:href="#hydroGenerator:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-10KV" transform="matrix(0.571429 -0.000000 0.000000 -0.574074 395.000000 -189.000000)" xlink:href="#hydroGenerator:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-10KV" transform="matrix(0.571429 -0.000000 0.000000 -0.574074 864.000000 -178.000000)" xlink:href="#hydroGenerator:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-10KV" transform="matrix(0.571429 -0.000000 0.000000 -0.574074 1046.000000 -175.000000)" xlink:href="#hydroGenerator:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_31c6390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="303,-1177 317,-1177 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27252@0" ObjectIDZND0="g_3594550@0" Pin0InfoVect0LinkObjId="g_3594550_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171113_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="303,-1177 317,-1177 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_39f32a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="239,-1218 270,-1218 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_36ee2b0@0" ObjectIDND1="27252@x" ObjectIDND2="27250@x" ObjectIDZND0="g_4701a90@0" Pin0InfoVect0LinkObjId="g_4701a90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_36ee2b0_0" Pin1InfoVect1LinkObjId="SW-171113_0" Pin1InfoVect2LinkObjId="SW-171111_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="239,-1218 270,-1218 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_355e000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="239,-1218 239,-1246 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_36ee2b0@0" ObjectIDND1="27252@x" ObjectIDND2="27250@x" ObjectIDZND0="37838@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_36ee2b0_0" Pin1InfoVect1LinkObjId="SW-171113_0" Pin1InfoVect2LinkObjId="SW-171111_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="239,-1218 239,-1246 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3378fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="161,-1158 161,-1218 239,-1218 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_36ee2b0@0" ObjectIDZND0="27252@x" ObjectIDZND1="27250@x" ObjectIDZND2="g_4701a90@0" Pin0InfoVect0LinkObjId="SW-171113_0" Pin0InfoVect1LinkObjId="SW-171111_0" Pin0InfoVect2LinkObjId="g_4701a90_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36ee2b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="161,-1158 161,-1218 239,-1218 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ddea50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-17,-1168 -17,-1193 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_3ae2500@1" ObjectIDZND0="g_3a1e7f0@0" Pin0InfoVect0LinkObjId="g_3a1e7f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3ae2500_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-17,-1168 -17,-1193 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26ee0f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-56,-1064 -56,-1083 -16,-1083 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_3ae3db0@0" ObjectIDZND0="27262@x" ObjectIDZND1="27260@x" ObjectIDZND2="g_3ae2500@0" Pin0InfoVect0LinkObjId="SW-171163_0" Pin0InfoVect1LinkObjId="SW-171161_0" Pin0InfoVect2LinkObjId="g_3ae2500_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3ae3db0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-56,-1064 -56,-1083 -16,-1083 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_367a500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="49,-1083 63,-1083 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27262@0" ObjectIDZND0="g_2df8480@0" Pin0InfoVect0LinkObjId="g_2df8480_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171163_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="49,-1083 63,-1083 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3138960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="13,-1083 -17,-1083 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="27262@1" ObjectIDZND0="27260@x" ObjectIDZND1="g_3ae3db0@0" ObjectIDZND2="g_3ae2500@0" Pin0InfoVect0LinkObjId="SW-171161_0" Pin0InfoVect1LinkObjId="g_3ae3db0_0" Pin0InfoVect2LinkObjId="g_3ae2500_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171163_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="13,-1083 -17,-1083 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_366fae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-17,-1068 -17,-1083 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="27260@1" ObjectIDZND0="27262@x" ObjectIDZND1="g_3ae3db0@0" ObjectIDZND2="g_3ae2500@0" Pin0InfoVect0LinkObjId="SW-171163_0" Pin0InfoVect1LinkObjId="g_3ae3db0_0" Pin0InfoVect2LinkObjId="g_3ae2500_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171161_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-17,-1068 -17,-1083 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d39c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="48,-1007 62,-1007 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27261@0" ObjectIDZND0="g_3a99d30@0" Pin0InfoVect0LinkObjId="g_3a99d30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171162_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="48,-1007 62,-1007 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_31d6420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="309,-1050 323,-1050 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27251@0" ObjectIDZND0="g_35d1320@0" Pin0InfoVect0LinkObjId="g_35d1320_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171112_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="309,-1050 323,-1050 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_39f5770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="273,-1050 239,-1050 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="27251@1" ObjectIDZND0="27249@x" ObjectIDZND1="27248@x" Pin0InfoVect0LinkObjId="SW-171110_0" Pin0InfoVect1LinkObjId="SW-171109_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171112_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="273,-1050 239,-1050 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36d97d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="306,-1109 320,-1109 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27253@0" ObjectIDZND0="g_31d2280@0" Pin0InfoVect0LinkObjId="g_31d2280_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171114_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="306,-1109 320,-1109 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_372b760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="239,-1031 239,-1050 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="27249@1" ObjectIDZND0="27251@x" ObjectIDZND1="27248@x" Pin0InfoVect0LinkObjId="SW-171112_0" Pin0InfoVect1LinkObjId="SW-171109_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171110_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="239,-1031 239,-1050 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2dd33b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="239,-1050 239,-1064 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="27251@x" ObjectIDND1="27249@x" ObjectIDZND0="27248@0" Pin0InfoVect0LinkObjId="SW-171109_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-171112_0" Pin1InfoVect1LinkObjId="SW-171110_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="239,-1050 239,-1064 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_361ba60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="267,-1177 239,-1177 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="27252@1" ObjectIDZND0="27250@x" ObjectIDZND1="g_36ee2b0@0" ObjectIDZND2="g_4701a90@0" Pin0InfoVect0LinkObjId="SW-171111_0" Pin0InfoVect1LinkObjId="g_36ee2b0_0" Pin0InfoVect2LinkObjId="g_4701a90_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171113_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="267,-1177 239,-1177 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_362d420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="270,-1109 239,-1109 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="27253@1" ObjectIDZND0="27248@x" ObjectIDZND1="27250@x" Pin0InfoVect0LinkObjId="SW-171109_0" Pin0InfoVect1LinkObjId="SW-171111_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171114_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="270,-1109 239,-1109 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30eab00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="239,-1177 239,-1158 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="27252@x" ObjectIDND1="g_36ee2b0@0" ObjectIDND2="g_4701a90@0" ObjectIDZND0="27250@1" Pin0InfoVect0LinkObjId="SW-171111_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-171113_0" Pin1InfoVect1LinkObjId="g_36ee2b0_0" Pin1InfoVect2LinkObjId="g_4701a90_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="239,-1177 239,-1158 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26f1010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="239,-1091 239,-1109 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="27248@1" ObjectIDZND0="27253@x" ObjectIDZND1="27250@x" Pin0InfoVect0LinkObjId="SW-171114_0" Pin0InfoVect1LinkObjId="SW-171111_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171109_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="239,-1091 239,-1109 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30fa970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="239,-1109 239,-1122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="27253@x" ObjectIDND1="27248@x" ObjectIDZND0="27250@0" Pin0InfoVect0LinkObjId="SW-171111_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-171114_0" Pin1InfoVect1LinkObjId="SW-171109_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="239,-1109 239,-1122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c172a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="653,-1178 667,-1178 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27259@0" ObjectIDZND0="g_33743a0@0" Pin0InfoVect0LinkObjId="g_33743a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171135_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="653,-1178 667,-1178 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36a68d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="591,-1217 624,-1217 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_30dd610@0" ObjectIDND1="27259@x" ObjectIDND2="27256@x" ObjectIDZND0="g_4702780@0" Pin0InfoVect0LinkObjId="g_4702780_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_30dd610_0" Pin1InfoVect1LinkObjId="SW-171135_0" Pin1InfoVect2LinkObjId="SW-171132_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="591,-1217 624,-1217 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36cbf40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="591,-1217 591,-1241 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_30dd610@0" ObjectIDND1="27259@x" ObjectIDND2="27256@x" ObjectIDZND0="37847@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_30dd610_0" Pin1InfoVect1LinkObjId="SW-171135_0" Pin1InfoVect2LinkObjId="SW-171132_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="591,-1217 591,-1241 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30ed610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="511,-1157 511,-1217 591,-1217 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_30dd610@0" ObjectIDZND0="27259@x" ObjectIDZND1="27256@x" ObjectIDZND2="g_4702780@0" Pin0InfoVect0LinkObjId="SW-171135_0" Pin0InfoVect1LinkObjId="SW-171132_0" Pin0InfoVect2LinkObjId="g_4702780_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_30dd610_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="511,-1157 511,-1217 591,-1217 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3adef40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="659,-1049 673,-1049 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27257@0" ObjectIDZND0="g_2cd8da0@0" Pin0InfoVect0LinkObjId="g_2cd8da0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171133_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="659,-1049 673,-1049 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3a727e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="623,-1049 591,-1049 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="27257@1" ObjectIDZND0="27255@x" ObjectIDZND1="27254@x" Pin0InfoVect0LinkObjId="SW-171131_0" Pin0InfoVect1LinkObjId="SW-171130_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171133_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="623,-1049 591,-1049 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d39340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="656,-1108 670,-1108 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27258@0" ObjectIDZND0="g_2cddd10@0" Pin0InfoVect0LinkObjId="g_2cddd10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171134_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="656,-1108 670,-1108 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cca170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="591,-1030 591,-1049 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="27255@1" ObjectIDZND0="27257@x" ObjectIDZND1="27254@x" Pin0InfoVect0LinkObjId="SW-171133_0" Pin0InfoVect1LinkObjId="SW-171130_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171131_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="591,-1030 591,-1049 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_39fadf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="591,-1049 591,-1063 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="27257@x" ObjectIDND1="27255@x" ObjectIDZND0="27254@0" Pin0InfoVect0LinkObjId="SW-171130_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-171133_0" Pin1InfoVect1LinkObjId="SW-171131_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="591,-1049 591,-1063 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33777a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="617,-1178 591,-1178 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="27259@1" ObjectIDZND0="27256@x" ObjectIDZND1="g_30dd610@0" ObjectIDZND2="g_4702780@0" Pin0InfoVect0LinkObjId="SW-171132_0" Pin0InfoVect1LinkObjId="g_30dd610_0" Pin0InfoVect2LinkObjId="g_4702780_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171135_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="617,-1178 591,-1178 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3a55260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="620,-1108 591,-1108 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="27258@1" ObjectIDZND0="27254@x" ObjectIDZND1="27256@x" Pin0InfoVect0LinkObjId="SW-171130_0" Pin0InfoVect1LinkObjId="SW-171132_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171134_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="620,-1108 591,-1108 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cf94c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="591,-1178 591,-1157 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="27259@x" ObjectIDND1="g_30dd610@0" ObjectIDND2="g_4702780@0" ObjectIDZND0="27256@1" Pin0InfoVect0LinkObjId="SW-171132_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-171135_0" Pin1InfoVect1LinkObjId="g_30dd610_0" Pin1InfoVect2LinkObjId="g_4702780_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="591,-1178 591,-1157 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2546340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="591,-1090 591,-1108 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="27254@1" ObjectIDZND0="27258@x" ObjectIDZND1="27256@x" Pin0InfoVect0LinkObjId="SW-171134_0" Pin0InfoVect1LinkObjId="SW-171132_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171130_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="591,-1090 591,-1108 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3137a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="591,-1108 591,-1121 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="27258@x" ObjectIDND1="27254@x" ObjectIDZND0="27256@0" Pin0InfoVect0LinkObjId="SW-171132_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-171134_0" Pin1InfoVect1LinkObjId="SW-171130_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="591,-1108 591,-1121 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_31d9120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="45,-856 45,-844 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" ObjectIDND0="27267@0" ObjectIDZND0="g_2da5f90@1" Pin0InfoVect0LinkObjId="g_2da5f90_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171172_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="45,-856 45,-844 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_39e2c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="110,-778 46,-778 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="transformer2" ObjectIDND0="g_3a97be0@0" ObjectIDZND0="g_2da5f90@0" ObjectIDZND1="27336@x" Pin0InfoVect0LinkObjId="g_2da5f90_0" Pin0InfoVect1LinkObjId="g_37297b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3a97be0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="110,-778 46,-778 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c93950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="45,-791 45,-778 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="transformer2" ObjectIDND0="g_2da5f90@0" ObjectIDZND0="g_3a97be0@0" ObjectIDZND1="27336@x" Pin0InfoVect0LinkObjId="g_3a97be0_0" Pin0InfoVect1LinkObjId="g_37297b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2da5f90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="45,-791 45,-778 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_37297b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="45,-778 45,-764 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_3a97be0@0" ObjectIDND1="g_2da5f90@0" ObjectIDZND0="27336@0" Pin0InfoVect0LinkObjId="g_4705db0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3a97be0_0" Pin1InfoVect1LinkObjId="g_2da5f90_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="45,-778 45,-764 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3a530a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="489,-918 489,-969 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="busSection" ObjectIDND0="g_31ca1f0@1" ObjectIDZND0="27244@0" Pin0InfoVect0LinkObjId="g_39ed8e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_31ca1f0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="489,-918 489,-969 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e08160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="488,-831 488,-877 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_31ca1f0@0" Pin0InfoVect0LinkObjId="g_31ca1f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="TF-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="488,-831 488,-877 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d73020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="128,-900 142,-900 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27269@0" ObjectIDZND0="g_313a860@0" Pin0InfoVect0LinkObjId="g_313a860_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171174_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="128,-900 142,-900 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3589780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="92,-900 45,-900 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" EndDevType2="breaker" ObjectIDND0="27269@1" ObjectIDZND0="27267@x" ObjectIDZND1="27269@x" ObjectIDZND2="27267@x" Pin0InfoVect0LinkObjId="SW-171172_0" Pin0InfoVect1LinkObjId="SW-171174_0" Pin0InfoVect2LinkObjId="SW-171172_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171174_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="92,-900 45,-900 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_31d4980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="847,-863 847,-851 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" ObjectIDND0="27273@0" ObjectIDZND0="g_31bdce0@1" Pin0InfoVect0LinkObjId="g_31bdce0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171239_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="847,-863 847,-851 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36d0be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="847,-691 847,-671 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="27337@1" ObjectIDZND0="g_2cdccc0@1" Pin0InfoVect0LinkObjId="g_2cdccc0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d89dc0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="847,-691 847,-671 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3743b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="912,-785 848,-785 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="transformer2" ObjectIDND0="g_2530800@0" ObjectIDZND0="g_31bdce0@0" ObjectIDZND1="27337@x" Pin0InfoVect0LinkObjId="g_31bdce0_0" Pin0InfoVect1LinkObjId="g_2d89dc0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2530800_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="912,-785 848,-785 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d89dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="847,-798 847,-785 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" EndDevType1="lightningRod" ObjectIDND0="g_31bdce0@0" ObjectIDZND0="27337@x" ObjectIDZND1="g_2530800@0" Pin0InfoVect0LinkObjId="g_35acdc0_0" Pin0InfoVect1LinkObjId="g_2530800_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_31bdce0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="847,-798 847,-785 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35acdc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="847,-785 847,-771 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_31bdce0@0" ObjectIDND1="g_2530800@0" ObjectIDZND0="27337@0" Pin0InfoVect0LinkObjId="g_2d89dc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_31bdce0_0" Pin1InfoVect1LinkObjId="g_2530800_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="847,-785 847,-771 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cf33f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="930,-907 944,-907 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27275@0" ObjectIDZND0="g_30d8ba0@0" Pin0InfoVect0LinkObjId="g_30d8ba0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171241_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="930,-907 944,-907 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30f6670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="894,-907 847,-907 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="27275@1" ObjectIDZND0="27274@x" ObjectIDZND1="27273@x" Pin0InfoVect0LinkObjId="SW-171240_0" Pin0InfoVect1LinkObjId="SW-171239_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171241_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="894,-907 847,-907 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_31f50e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="847,-916 847,-907 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="27274@0" ObjectIDZND0="27275@x" ObjectIDZND1="27273@x" Pin0InfoVect0LinkObjId="SW-171241_0" Pin0InfoVect1LinkObjId="SW-171239_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171240_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="847,-916 847,-907 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3194030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="847,-907 847,-890 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="27275@x" ObjectIDND1="27274@x" ObjectIDZND0="27273@1" Pin0InfoVect0LinkObjId="SW-171239_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-171241_0" Pin1InfoVect1LinkObjId="SW-171240_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="847,-907 847,-890 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cc2c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="45,-909 45,-900 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" BeginDevType2="switch" EndDevType0="switch" EndDevType1="breaker" EndDevType2="switch" ObjectIDND0="27269@x" ObjectIDND1="27267@x" ObjectIDND2="27268@x" ObjectIDZND0="27269@x" ObjectIDZND1="27267@x" ObjectIDZND2="27269@x" Pin0InfoVect0LinkObjId="SW-171174_0" Pin0InfoVect1LinkObjId="SW-171172_0" Pin0InfoVect2LinkObjId="SW-171174_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-171174_0" Pin1InfoVect1LinkObjId="SW-171172_0" Pin1InfoVect2LinkObjId="SW-171173_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="45,-909 45,-900 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cd20d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="45,-900 45,-883 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="breaker" EndDevType0="breaker" ObjectIDND0="27269@x" ObjectIDND1="27269@x" ObjectIDND2="27267@x" ObjectIDZND0="27267@1" Pin0InfoVect0LinkObjId="SW-171172_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-171174_0" Pin1InfoVect1LinkObjId="SW-171174_0" Pin1InfoVect2LinkObjId="SW-171172_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="45,-900 45,-883 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_39ed8e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="45,-954 45,-969 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27268@1" ObjectIDZND0="27244@0" Pin0InfoVect0LinkObjId="g_3a530a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171173_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="45,-954 45,-969 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30bf340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="45,-900 45,-909 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" BeginDevType2="switch" EndDevType0="switch" EndDevType1="breaker" EndDevType2="switch" ObjectIDND0="27269@x" ObjectIDND1="27267@x" ObjectIDND2="27269@x" ObjectIDZND0="27269@x" ObjectIDZND1="27267@x" ObjectIDZND2="27268@x" Pin0InfoVect0LinkObjId="SW-171174_0" Pin0InfoVect1LinkObjId="SW-171172_0" Pin0InfoVect2LinkObjId="SW-171173_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-171174_0" Pin1InfoVect1LinkObjId="SW-171172_0" Pin1InfoVect2LinkObjId="SW-171174_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="45,-900 45,-909 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35a1d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="45,-909 45,-918 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="27269@x" ObjectIDND1="27267@x" ObjectIDND2="27269@x" ObjectIDZND0="27268@0" Pin0InfoVect0LinkObjId="SW-171173_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-171174_0" Pin1InfoVect1LinkObjId="SW-171172_0" Pin1InfoVect2LinkObjId="SW-171174_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="45,-909 45,-918 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30bef70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="847,-952 847,-969 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27274@1" ObjectIDZND0="27244@0" Pin0InfoVect0LinkObjId="g_3a530a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171240_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="847,-952 847,-969 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31c4f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-229,-281 -229,-264 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2d94d40@0" ObjectIDZND0="27309@1" Pin0InfoVect0LinkObjId="SW-171413_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d94d40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-229,-281 -229,-264 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2abe6e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-263,-367 -230,-367 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_3378de0@0" ObjectIDZND0="27312@x" ObjectIDZND1="27311@x" ObjectIDZND2="g_2d94d40@0" Pin0InfoVect0LinkObjId="SW-171415_0" Pin0InfoVect1LinkObjId="SW-171414_0" Pin0InfoVect2LinkObjId="g_2d94d40_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3378de0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-263,-367 -230,-367 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31d0c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-171,-367 -157,-367 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27312@0" ObjectIDZND0="g_2b3db50@0" Pin0InfoVect0LinkObjId="g_2b3db50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171415_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-171,-367 -157,-367 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31c7340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-207,-367 -229,-367 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="27312@1" ObjectIDZND0="27311@x" ObjectIDZND1="g_2d94d40@0" ObjectIDZND2="g_3378de0@0" Pin0InfoVect0LinkObjId="SW-171414_0" Pin0InfoVect1LinkObjId="g_2d94d40_0" Pin0InfoVect2LinkObjId="g_3378de0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171415_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-207,-367 -229,-367 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3553350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-229,-379 -229,-367 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="27311@0" ObjectIDZND0="27312@x" ObjectIDZND1="g_2d94d40@0" ObjectIDZND2="g_3378de0@0" Pin0InfoVect0LinkObjId="SW-171415_0" Pin0InfoVect1LinkObjId="g_2d94d40_0" Pin0InfoVect2LinkObjId="g_3378de0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171414_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-229,-379 -229,-367 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31cc820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-263,-217 -229,-217 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_31cfac0@0" ObjectIDZND0="27309@x" ObjectIDZND1="34205@x" Pin0InfoVect0LinkObjId="SW-171413_0" Pin0InfoVect1LinkObjId="EC-YM_WM.076Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_31cfac0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-263,-217 -229,-217 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_361bfe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-229,-228 -229,-217 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="27309@0" ObjectIDZND0="g_31cfac0@0" ObjectIDZND1="34205@x" Pin0InfoVect0LinkObjId="g_31cfac0_0" Pin0InfoVect1LinkObjId="EC-YM_WM.076Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171413_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-229,-228 -229,-217 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31dfa20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-229,-217 -229,-200 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_31cfac0@0" ObjectIDND1="27309@x" ObjectIDZND0="34205@0" Pin0InfoVect0LinkObjId="EC-YM_WM.076Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_31cfac0_0" Pin1InfoVect1LinkObjId="SW-171413_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-229,-217 -229,-200 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3744ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="15,-314 15,-304 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2c7a7b0@0" ObjectIDZND0="27280@1" Pin0InfoVect0LinkObjId="SW-171306_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c7a7b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="15,-314 15,-304 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3728690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="73,-384 87,-384 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27283@0" ObjectIDZND0="g_3733090@0" Pin0InfoVect0LinkObjId="g_3733090_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171308_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="73,-384 87,-384 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31ce0e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="171,-290 171,-273 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_35b7810@0" ObjectIDZND0="27285@1" Pin0InfoVect0LinkObjId="SW-171324_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_35b7810_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="171,-290 171,-273 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c7c730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="229,-368 243,-368 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27341@0" ObjectIDZND0="g_3a054b0@0" Pin0InfoVect0LinkObjId="g_3a054b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171596_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="229,-368 243,-368 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a332f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="193,-368 171,-368 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="27341@1" ObjectIDZND0="g_31d05c0@0" ObjectIDZND1="g_35b7810@0" ObjectIDZND2="27287@x" Pin0InfoVect0LinkObjId="g_31d05c0_0" Pin0InfoVect1LinkObjId="g_35b7810_0" Pin0InfoVect2LinkObjId="SW-171325_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171596_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="193,-368 171,-368 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a1f790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="136,-226 171,-226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_31d55e0@0" ObjectIDZND0="27285@x" ObjectIDZND1="34207@x" Pin0InfoVect0LinkObjId="SW-171324_0" Pin0InfoVect1LinkObjId="EC-YM_WM.073Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_31d55e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="136,-226 171,-226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2dfc240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="171,-237 171,-226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="27285@0" ObjectIDZND0="g_31d55e0@0" ObjectIDZND1="34207@x" Pin0InfoVect0LinkObjId="g_31d55e0_0" Pin0InfoVect1LinkObjId="EC-YM_WM.073Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171324_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="171,-237 171,-226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e25a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="171,-226 171,-209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_31d55e0@0" ObjectIDND1="27285@x" ObjectIDZND0="34207@0" Pin0InfoVect0LinkObjId="EC-YM_WM.073Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_31d55e0_0" Pin1InfoVect1LinkObjId="SW-171324_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="171,-226 171,-209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3ab14a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="367,-289 367,-277 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_39fe8d0@0" ObjectIDZND0="27289@1" Pin0InfoVect0LinkObjId="SW-171341_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_39fe8d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="367,-289 367,-277 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2eae560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="425,-365 439,-365 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27292@0" ObjectIDZND0="g_31be780@0" Pin0InfoVect0LinkObjId="g_31be780_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171343_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="425,-365 439,-365 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cdef40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="389,-365 367,-365 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="27292@1" ObjectIDZND0="g_39fe8d0@0" ObjectIDZND1="27291@x" ObjectIDZND2="g_4710870@0" Pin0InfoVect0LinkObjId="g_39fe8d0_0" Pin0InfoVect1LinkObjId="SW-171342_0" Pin0InfoVect2LinkObjId="g_4710870_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171343_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="389,-365 367,-365 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_30d82e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="333,-225 367,-225 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="hydroGenerator" EndDevType2="load" ObjectIDND0="g_4712da0@0" ObjectIDZND0="27289@x" ObjectIDZND1="0@x" ObjectIDZND2="34208@x" Pin0InfoVect0LinkObjId="SW-171341_0" Pin0InfoVect1LinkObjId="TF-0_0" Pin0InfoVect2LinkObjId="EC-YM_WM.074Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4712da0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="333,-225 367,-225 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31c6e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="367,-241 367,-225 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="hydroGenerator" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="27289@0" ObjectIDZND0="0@x" ObjectIDZND1="g_4712da0@0" ObjectIDZND2="34208@x" Pin0InfoVect0LinkObjId="TF-0_0" Pin0InfoVect1LinkObjId="g_4712da0_0" Pin0InfoVect2LinkObjId="EC-YM_WM.074Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171341_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="367,-241 367,-225 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31e2450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-58,-216 -58,-231 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_3a1e2d0@0" Pin0InfoVect0LinkObjId="g_3a1e2d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="TF-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-58,-216 -58,-231 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2dd6300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-58,-258 15,-258 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="hydroGenerator" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="g_3a1e2d0@1" ObjectIDZND0="0@x" ObjectIDZND1="g_470d4a0@0" ObjectIDZND2="34206@x" Pin0InfoVect0LinkObjId="TF-0_0" Pin0InfoVect1LinkObjId="g_470d4a0_0" Pin0InfoVect2LinkObjId="EC-YM_WM.072Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3a1e2d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-58,-258 15,-258 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31d1800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="15,-268 15,-258 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="hydroGenerator" EndDevType2="lightningRod" ObjectIDND0="27280@0" ObjectIDZND0="g_3a1e2d0@0" ObjectIDZND1="0@x" ObjectIDZND2="g_470d4a0@0" Pin0InfoVect0LinkObjId="g_3a1e2d0_0" Pin0InfoVect1LinkObjId="TF-0_0" Pin0InfoVect2LinkObjId="g_470d4a0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171306_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="15,-268 15,-258 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d11ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="15,-258 15,-246 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="hydroGenerator" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="g_3a1e2d0@0" ObjectIDND1="27280@x" ObjectIDZND0="0@x" ObjectIDZND1="g_470d4a0@0" ObjectIDZND2="34206@x" Pin0InfoVect0LinkObjId="TF-0_0" Pin0InfoVect1LinkObjId="g_470d4a0_0" Pin0InfoVect2LinkObjId="EC-YM_WM.072Ld_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3a1e2d0_0" Pin1InfoVect1LinkObjId="SW-171306_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="15,-258 15,-246 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c87b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="171,-478 171,-497 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27286@0" ObjectIDZND0="27245@0" Pin0InfoVect0LinkObjId="g_3133cd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171325_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="171,-478 171,-497 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3cca770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="367,-225 408,-225 408,-217 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="load" EndDevType0="hydroGenerator" ObjectIDND0="27289@x" ObjectIDND1="g_4712da0@0" ObjectIDND2="34208@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="TF-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-171341_0" Pin1InfoVect1LinkObjId="g_4712da0_0" Pin1InfoVect2LinkObjId="EC-YM_WM.074Ld_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="367,-225 408,-225 408,-217 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3133cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="15,-497 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="busSection" ObjectIDND0="27245@0" ObjectIDZND0="27245@0" Pin0InfoVect0LinkObjId="g_2c87b20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c87b20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="15,-497 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25722c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="367,-479 367,-497 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27290@0" ObjectIDZND0="27245@0" Pin0InfoVect0LinkObjId="g_2c87b20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171342_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="367,-479 367,-497 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b0c9b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-229,-471 -229,-497 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27310@0" ObjectIDZND0="27245@0" Pin0InfoVect0LinkObjId="g_2c87b20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171414_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-229,-471 -229,-497 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2eb9fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="37,-384 15,-384 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="27283@1" ObjectIDZND0="27282@x" ObjectIDZND1="g_2c7a7b0@0" ObjectIDZND2="g_373de00@0" Pin0InfoVect0LinkObjId="SW-171307_0" Pin0InfoVect1LinkObjId="g_2c7a7b0_0" Pin0InfoVect2LinkObjId="g_373de00_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171308_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="37,-384 15,-384 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2dd9800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="15,-384 15,-391 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="27283@x" ObjectIDND1="g_2c7a7b0@0" ObjectIDND2="g_373de00@0" ObjectIDZND0="27282@0" Pin0InfoVect0LinkObjId="SW-171307_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-171308_0" Pin1InfoVect1LinkObjId="g_2c7a7b0_0" Pin1InfoVect2LinkObjId="g_373de00_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="15,-384 15,-391 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_271b940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="641,-282 641,-265 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_31f28d0@0" ObjectIDZND0="27294@1" Pin0InfoVect0LinkObjId="SW-171359_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_31f28d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="641,-282 641,-265 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d5a260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="608,-356 641,-356 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_20ca6d0@0" ObjectIDZND0="27297@x" ObjectIDZND1="27296@x" ObjectIDZND2="g_31f28d0@0" Pin0InfoVect0LinkObjId="SW-171361_0" Pin0InfoVect1LinkObjId="SW-171360_0" Pin0InfoVect2LinkObjId="g_31f28d0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20ca6d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="608,-356 641,-356 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d70ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="699,-368 713,-368 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27297@0" ObjectIDZND0="g_31e9ea0@0" Pin0InfoVect0LinkObjId="g_31e9ea0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171361_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="699,-368 713,-368 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e1b9d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="663,-368 641,-368 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="27297@1" ObjectIDZND0="g_20ca6d0@0" ObjectIDZND1="g_31f28d0@0" ObjectIDZND2="27296@x" Pin0InfoVect0LinkObjId="g_20ca6d0_0" Pin0InfoVect1LinkObjId="g_31f28d0_0" Pin0InfoVect2LinkObjId="SW-171360_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171361_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="663,-368 641,-368 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31a56a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="641,-380 641,-368 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="27296@0" ObjectIDZND0="27297@x" ObjectIDZND1="g_20ca6d0@0" ObjectIDZND2="g_31f28d0@0" Pin0InfoVect0LinkObjId="SW-171361_0" Pin0InfoVect1LinkObjId="g_20ca6d0_0" Pin0InfoVect2LinkObjId="g_31f28d0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171360_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="641,-380 641,-368 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e0ff20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="641,-368 641,-356 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="27297@x" ObjectIDND1="27296@x" ObjectIDZND0="g_20ca6d0@0" ObjectIDZND1="g_31f28d0@0" Pin0InfoVect0LinkObjId="g_20ca6d0_0" Pin0InfoVect1LinkObjId="g_31f28d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-171361_0" Pin1InfoVect1LinkObjId="SW-171360_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="641,-368 641,-356 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ddd5d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="607,-218 641,-218 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_2e3dab0@0" ObjectIDZND0="27294@x" ObjectIDZND1="34209@x" Pin0InfoVect0LinkObjId="SW-171359_0" Pin0InfoVect1LinkObjId="EC-YM_WM.081Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e3dab0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="607,-218 641,-218 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_30d6130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="641,-229 641,-218 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="27294@0" ObjectIDZND0="g_2e3dab0@0" ObjectIDZND1="34209@x" Pin0InfoVect0LinkObjId="g_2e3dab0_0" Pin0InfoVect1LinkObjId="EC-YM_WM.081Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171359_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="641,-229 641,-218 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39fbb30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="641,-218 641,-201 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="27294@x" ObjectIDND1="g_2e3dab0@0" ObjectIDZND0="34209@0" Pin0InfoVect0LinkObjId="EC-YM_WM.081Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-171359_0" Pin1InfoVect1LinkObjId="g_2e3dab0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="641,-218 641,-201 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e91430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="641,-472 641,-494 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27295@0" ObjectIDZND0="27246@0" Pin0InfoVect0LinkObjId="g_2b00be0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171360_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="641,-472 641,-494 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_30bd2f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="830,-282 830,-270 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2affc90@0" ObjectIDZND0="27299@1" Pin0InfoVect0LinkObjId="SW-171377_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2affc90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="830,-282 830,-270 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35d9db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="888,-361 902,-361 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27302@0" ObjectIDZND0="g_2d28970@0" Pin0InfoVect0LinkObjId="g_2d28970_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171379_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="888,-361 902,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36638e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="852,-361 830,-361 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="27302@1" ObjectIDZND0="g_2affc90@0" ObjectIDZND1="27301@x" ObjectIDZND2="g_4714fa0@0" Pin0InfoVect0LinkObjId="g_2affc90_0" Pin0InfoVect1LinkObjId="SW-171378_0" Pin0InfoVect2LinkObjId="g_4714fa0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171379_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="852,-361 830,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a207e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1013,-281 1013,-269 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_39fefa0@0" ObjectIDZND0="27304@1" Pin0InfoVect0LinkObjId="SW-171395_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_39fefa0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1013,-281 1013,-269 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e5df40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1200,-282 1200,-265 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2b4ab40@0" ObjectIDZND0="27314@1" Pin0InfoVect0LinkObjId="SW-171430_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b4ab40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1200,-282 1200,-265 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_30de310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1258,-356 1272,-356 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27317@0" ObjectIDZND0="g_3a07be0@0" Pin0InfoVect0LinkObjId="g_3a07be0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171432_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1258,-356 1272,-356 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d97960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1222,-356 1200,-356 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="27317@1" ObjectIDZND0="g_471b5f0@0" ObjectIDZND1="g_2b4ab40@0" ObjectIDZND2="27316@x" Pin0InfoVect0LinkObjId="g_471b5f0_0" Pin0InfoVect1LinkObjId="g_2b4ab40_0" Pin0InfoVect2LinkObjId="SW-171431_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171432_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1222,-356 1200,-356 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36c9520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1166,-218 1200,-218 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_2d06c00@0" ObjectIDZND0="27314@x" ObjectIDZND1="34215@x" Pin0InfoVect0LinkObjId="SW-171430_0" Pin0InfoVect1LinkObjId="EC-YM_WM.084Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d06c00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1166,-218 1200,-218 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cb1400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1200,-229 1200,-218 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="27314@0" ObjectIDZND0="g_2d06c00@0" ObjectIDZND1="34215@x" Pin0InfoVect0LinkObjId="g_2d06c00_0" Pin0InfoVect1LinkObjId="EC-YM_WM.084Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171430_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1200,-229 1200,-218 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3aa4550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1200,-218 1200,-201 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="27314@x" ObjectIDND1="g_2d06c00@0" ObjectIDZND0="34215@0" Pin0InfoVect0LinkObjId="EC-YM_WM.084Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-171430_0" Pin1InfoVect1LinkObjId="g_2d06c00_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1200,-218 1200,-201 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b00be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="830,-472 830,-492 831,-494 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27300@0" ObjectIDZND0="27246@0" Pin0InfoVect0LinkObjId="g_2e91430_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171378_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="830,-472 830,-492 831,-494 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_355bdd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1013,-471 1013,-494 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27305@0" ObjectIDZND0="27246@0" Pin0InfoVect0LinkObjId="g_2e91430_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171396_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1013,-471 1013,-494 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31edb70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1200,-472 1200,-494 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27315@0" ObjectIDZND0="27246@0" Pin0InfoVect0LinkObjId="g_2e91430_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171431_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1200,-472 1200,-494 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3732140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-74,-796 -60,-796 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27323@0" ObjectIDZND0="g_31a7470@0" Pin0InfoVect0LinkObjId="g_31a7470_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171450_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-74,-796 -60,-796 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35941b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-110,-796 -132,-796 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="27323@1" ObjectIDZND0="41293@x" ObjectIDZND1="g_3731570@0" ObjectIDZND2="27319@x" Pin0InfoVect0LinkObjId="CB-YM_WM.YM_WM_Cb1_0" Pin0InfoVect1LinkObjId="g_3731570_0" Pin0InfoVect2LinkObjId="SW-171447_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171450_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-110,-796 -132,-796 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35afb50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-132,-810 -132,-796 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="41293@1" ObjectIDZND0="27323@x" ObjectIDZND1="g_3731570@0" ObjectIDZND2="27319@x" Pin0InfoVect0LinkObjId="SW-171450_0" Pin0InfoVect1LinkObjId="g_3731570_0" Pin0InfoVect2LinkObjId="SW-171447_0" Pin0Num="1" Pin1InfoVect0LinkObjId="CB-YM_WM.YM_WM_Cb1_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-132,-810 -132,-796 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_37304f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-175,-796 -132,-796 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="capacitor" EndDevType2="switch" ObjectIDND0="g_3731570@0" ObjectIDZND0="27323@x" ObjectIDZND1="41293@x" ObjectIDZND2="27319@x" Pin0InfoVect0LinkObjId="SW-171450_0" Pin0InfoVect1LinkObjId="CB-YM_WM.YM_WM_Cb1_0" Pin0InfoVect2LinkObjId="SW-171447_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3731570_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-175,-796 -132,-796 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a34b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-71,-640 -57,-640 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27324@0" ObjectIDZND0="g_2ebe500@0" Pin0InfoVect0LinkObjId="g_2ebe500_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171451_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-71,-640 -57,-640 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31928e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-107,-640 -132,-640 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="27324@1" ObjectIDZND0="g_3a87930@0" ObjectIDZND1="27321@x" ObjectIDZND2="g_2cb7870@0" Pin0InfoVect0LinkObjId="g_3a87930_0" Pin0InfoVect1LinkObjId="SW-171448_0" Pin0InfoVect2LinkObjId="g_2cb7870_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171451_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-107,-640 -132,-640 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35b1f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-132,-640 -132,-653 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="27324@x" ObjectIDND1="27321@x" ObjectIDND2="g_2cb7870@0" ObjectIDZND0="g_3a87930@0" Pin0InfoVect0LinkObjId="g_3a87930_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-171451_0" Pin1InfoVect1LinkObjId="SW-171448_0" Pin1InfoVect2LinkObjId="g_2cb7870_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-132,-640 -132,-653 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d656d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-175,-640 -132,-640 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_2cb7870@0" ObjectIDZND0="27324@x" ObjectIDZND1="g_3a87930@0" ObjectIDZND2="27321@x" Pin0InfoVect0LinkObjId="SW-171451_0" Pin0InfoVect1LinkObjId="g_3a87930_0" Pin0InfoVect2LinkObjId="SW-171448_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2cb7870_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-175,-640 -132,-640 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b00140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-132,-613 -132,-640 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="27321@0" ObjectIDZND0="27324@x" ObjectIDZND1="g_3a87930@0" ObjectIDZND2="g_2cb7870@0" Pin0InfoVect0LinkObjId="SW-171451_0" Pin0InfoVect1LinkObjId="g_3a87930_0" Pin0InfoVect2LinkObjId="g_2cb7870_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171448_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-132,-613 -132,-640 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3aa8220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-71,-718 -57,-718 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27322@0" ObjectIDZND0="g_31e86a0@0" Pin0InfoVect0LinkObjId="g_31e86a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171449_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-71,-718 -57,-718 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3aa9c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-107,-718 -132,-718 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="27322@1" ObjectIDZND0="g_3a87930@0" ObjectIDZND1="27319@x" Pin0InfoVect0LinkObjId="g_3a87930_0" Pin0InfoVect1LinkObjId="SW-171447_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171449_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-107,-718 -132,-718 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31def50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-132,-706 -132,-718 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_3a87930@1" ObjectIDZND0="27322@x" ObjectIDZND1="27319@x" Pin0InfoVect0LinkObjId="SW-171449_0" Pin0InfoVect1LinkObjId="SW-171447_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3a87930_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-132,-706 -132,-718 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b491f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-132,-718 -132,-730 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="27322@x" ObjectIDND1="g_3a87930@0" ObjectIDZND0="27319@0" Pin0InfoVect0LinkObjId="SW-171447_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-171449_0" Pin1InfoVect1LinkObjId="g_3a87930_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-132,-718 -132,-730 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_367a780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-132,-527 -132,-497 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27320@0" ObjectIDZND0="27245@0" Pin0InfoVect0LinkObjId="g_2c87b20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171448_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-132,-527 -132,-497 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_30c0740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="45,-497 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="busSection" ObjectIDND0="27245@0" ObjectIDZND0="27245@0" Pin0InfoVect0LinkObjId="g_2c87b20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c87b20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="45,-497 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_30ff6f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="847,-511 847,-494 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27277@0" ObjectIDZND0="27246@0" Pin0InfoVect0LinkObjId="g_2e91430_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171249_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="847,-511 847,-494 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3376840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1150,-797 1164,-797 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27329@0" ObjectIDZND0="g_2b48d90@0" Pin0InfoVect0LinkObjId="g_2b48d90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171471_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1150,-797 1164,-797 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36c0030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1114,-797 1093,-797 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" EndDevType1="switch" ObjectIDND0="27329@1" ObjectIDZND0="41294@x" ObjectIDZND1="27328@x" Pin0InfoVect0LinkObjId="CB-YM_WM.YM_WM_Cb2_0" Pin0InfoVect1LinkObjId="SW-171470_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171471_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1114,-797 1093,-797 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36a6b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1092,-811 1092,-797 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="41294@1" ObjectIDZND0="27329@x" ObjectIDZND1="27328@x" Pin0InfoVect0LinkObjId="SW-171471_0" Pin0InfoVect1LinkObjId="SW-171470_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="CB-YM_WM.YM_WM_Cb2_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1092,-811 1092,-797 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3666960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1048,-852 1091,-852 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_30fb620@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_30fb620_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1048,-852 1091,-852 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31df410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1153,-641 1167,-641 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27330@0" ObjectIDZND0="g_40aaa00@0" Pin0InfoVect0LinkObjId="g_40aaa00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171472_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1153,-641 1167,-641 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31df640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1117,-641 1093,-641 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="27330@1" ObjectIDZND0="g_31ddf30@0" ObjectIDZND1="27327@x" ObjectIDZND2="g_4050e80@0" Pin0InfoVect0LinkObjId="g_31ddf30_0" Pin0InfoVect1LinkObjId="SW-171469_0" Pin0InfoVect2LinkObjId="g_4050e80_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171472_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1117,-641 1093,-641 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_372fce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1092,-641 1092,-654 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="27330@x" ObjectIDND1="27327@x" ObjectIDND2="g_4050e80@0" ObjectIDZND0="g_31ddf30@0" Pin0InfoVect0LinkObjId="g_31ddf30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-171472_0" Pin1InfoVect1LinkObjId="SW-171469_0" Pin1InfoVect2LinkObjId="g_4050e80_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1092,-641 1092,-654 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d60700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="292,-651 292,-671 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_35a0f20@1" ObjectIDZND0="g_3a52d40@0" Pin0InfoVect0LinkObjId="g_3a52d40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_35a0f20_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="292,-651 292,-671 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36632b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="292,-620 292,-612 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_35a0f20@0" ObjectIDZND0="27263@1" Pin0InfoVect0LinkObjId="SW-171166_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_35a0f20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="292,-620 292,-612 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d95a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="248,-568 292,-568 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="g_2d60960@0" ObjectIDZND0="27263@x" ObjectIDZND1="27245@0" Pin0InfoVect0LinkObjId="SW-171166_0" Pin0InfoVect1LinkObjId="g_2c87b20_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d60960_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="248,-568 292,-568 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a22000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="292,-589 292,-568 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="busSection" ObjectIDND0="27263@0" ObjectIDZND0="g_2d60960@0" ObjectIDZND1="27245@0" Pin0InfoVect0LinkObjId="g_2d60960_0" Pin0InfoVect1LinkObjId="g_2c87b20_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171166_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="292,-589 292,-568 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_30f7f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="292,-568 292,-497 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="g_2d60960@0" ObjectIDND1="27263@x" ObjectIDZND0="27245@0" Pin0InfoVect0LinkObjId="g_2c87b20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2d60960_0" Pin1InfoVect1LinkObjId="SW-171166_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="292,-568 292,-497 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cdfcf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="662,-566 706,-566 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="busSection" EndDevType1="switch" ObjectIDND0="g_39ef410@0" ObjectIDZND0="27246@0" ObjectIDZND1="27265@x" Pin0InfoVect0LinkObjId="g_2e91430_0" Pin0InfoVect1LinkObjId="SW-171169_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_39ef410_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="662,-566 706,-566 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31ee660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="706,-578 706,-566 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="busSection" ObjectIDND0="27265@0" ObjectIDZND0="g_39ef410@0" ObjectIDZND1="27246@0" Pin0InfoVect0LinkObjId="g_39ef410_0" Pin0InfoVect1LinkObjId="g_2e91430_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171169_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="706,-578 706,-566 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31ee890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="706,-566 706,-494 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="g_39ef410@0" ObjectIDND1="27265@x" ObjectIDZND0="27246@0" Pin0InfoVect0LinkObjId="g_2e91430_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_39ef410_0" Pin1InfoVect1LinkObjId="SW-171169_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="706,-566 706,-494 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cfc380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="431,-529 431,-497 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27332@0" ObjectIDZND0="27245@0" Pin0InfoVect0LinkObjId="g_2c87b20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171490_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="431,-529 431,-497 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_30ff180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="431,-621 431,-649 575,-649 575,-600 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="27333@0" ObjectIDZND0="27335@0" Pin0InfoVect0LinkObjId="SW-171492_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171490_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="431,-621 431,-649 575,-649 575,-600 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_30ff3d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="575,-531 575,-494 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27334@0" ObjectIDZND0="27246@0" Pin0InfoVect0LinkObjId="g_2e91430_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171492_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="575,-531 575,-494 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_313ed10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1092,-707 1092,-731 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_31ddf30@1" ObjectIDZND0="27328@0" Pin0InfoVect0LinkObjId="SW-171470_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_31ddf30_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1092,-707 1092,-731 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3ab0de0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="796,-214 831,-214 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="hydroGenerator" EndDevType1="load" EndDevType2="switch" ObjectIDND0="g_2dd1730@0" ObjectIDZND0="0@x" ObjectIDZND1="34210@x" ObjectIDZND2="27299@x" Pin0InfoVect0LinkObjId="TF-0_0" Pin0InfoVect1LinkObjId="EC-YM_WM.082Ld_0" Pin0InfoVect2LinkObjId="SW-171377_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2dd1730_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="796,-214 831,-214 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2dde5e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="830,-214 877,-214 877,-206 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="hydroGenerator" ObjectIDND0="34210@x" ObjectIDND1="27299@x" ObjectIDND2="g_2dd1730@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="TF-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="EC-YM_WM.082Ld_0" Pin1InfoVect1LinkObjId="SW-171377_0" Pin1InfoVect2LinkObjId="g_2dd1730_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="830,-214 877,-214 877,-206 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35aae90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="830,-201 830,-214 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="hydroGenerator" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="34210@0" ObjectIDZND0="0@x" ObjectIDZND1="27299@x" ObjectIDZND2="g_2dd1730@0" Pin0InfoVect0LinkObjId="TF-0_0" Pin0InfoVect1LinkObjId="SW-171377_0" Pin0InfoVect2LinkObjId="g_2dd1730_0" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-YM_WM.082Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="830,-201 830,-214 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31bbc40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="978,-214 1013,-214 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="hydroGenerator" EndDevType1="load" EndDevType2="switch" ObjectIDND0="g_3aafed0@0" ObjectIDZND0="0@x" ObjectIDZND1="34211@x" ObjectIDZND2="27304@x" Pin0InfoVect0LinkObjId="TF-0_0" Pin0InfoVect1LinkObjId="EC-YM_WM.083Ld_0" Pin0InfoVect2LinkObjId="SW-171395_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3aafed0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="978,-214 1013,-214 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a689a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1013,-214 1059,-214 1059,-203 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="hydroGenerator" ObjectIDND0="34211@x" ObjectIDND1="27304@x" ObjectIDND2="g_3aafed0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="TF-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="EC-YM_WM.083Ld_0" Pin1InfoVect1LinkObjId="SW-171395_0" Pin1InfoVect2LinkObjId="g_3aafed0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1013,-214 1059,-214 1059,-203 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3144b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1013,-200 1013,-214 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="hydroGenerator" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="34211@0" ObjectIDZND0="0@x" ObjectIDZND1="27304@x" ObjectIDZND2="g_3aafed0@0" Pin0InfoVect0LinkObjId="TF-0_0" Pin0InfoVect1LinkObjId="SW-171395_0" Pin0InfoVect2LinkObjId="g_3aafed0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-YM_WM.083Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1013,-200 1013,-214 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d5d6c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="706,-655 706,-638 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_30f8180@0" ObjectIDZND0="g_36bf490@1" Pin0InfoVect0LinkObjId="g_36bf490_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_30f8180_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="706,-655 706,-638 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d5d8b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="706,-607 706,-600 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_36bf490@0" ObjectIDZND0="27265@1" Pin0InfoVect0LinkObjId="SW-171169_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36bf490_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="706,-607 706,-600 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d5daa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-17,-1007 -17,-969 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="27261@x" ObjectIDND1="27260@x" ObjectIDZND0="27244@0" Pin0InfoVect0LinkObjId="g_3a530a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-171162_0" Pin1InfoVect1LinkObjId="SW-171161_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-17,-1007 -17,-969 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ab4e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="12,-1007 -17,-1007 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="27261@1" ObjectIDZND0="27260@x" ObjectIDZND1="27244@0" Pin0InfoVect0LinkObjId="SW-171161_0" Pin0InfoVect1LinkObjId="g_3a530a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171162_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="12,-1007 -17,-1007 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3add0d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-17,-1007 -17,-1032 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="busSection" EndDevType0="switch" ObjectIDND0="27261@x" ObjectIDND1="27244@0" ObjectIDZND0="27260@0" Pin0InfoVect0LinkObjId="SW-171161_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-171162_0" Pin1InfoVect1LinkObjId="g_3a530a0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-17,-1007 -17,-1032 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_39f1650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="239,-995 239,-969 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27249@0" ObjectIDZND0="27244@0" Pin0InfoVect0LinkObjId="g_3a530a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171110_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="239,-995 239,-969 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3578a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="959,-1203 973,-1203 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="30137@0" ObjectIDZND0="g_3577ff0@0" Pin0InfoVect0LinkObjId="g_3577ff0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196762_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="959,-1203 973,-1203 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ddf610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="966,-1036 980,-1036 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="30135@0" ObjectIDZND0="g_3190140@0" Pin0InfoVect0LinkObjId="g_3190140_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196760_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="966,-1036 980,-1036 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_39f7980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="969,-1095 983,-1095 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="30136@0" ObjectIDZND0="g_3d955e0@0" Pin0InfoVect0LinkObjId="g_3d955e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196761_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="969,-1095 983,-1095 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3adae30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="591,-994 591,-969 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27255@0" ObjectIDZND0="27244@0" Pin0InfoVect0LinkObjId="g_3a530a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171131_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="591,-994 591,-969 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_313e450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="897,-969 897,-985 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="27244@0" ObjectIDZND0="30132@0" Pin0InfoVect0LinkObjId="SW-196757_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3a530a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="897,-969 897,-985 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_313e640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="897,-1138 897,-1157 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="30131@1" ObjectIDZND0="30134@0" Pin0InfoVect0LinkObjId="SW-196759_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196756_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="897,-1138 897,-1157 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_313e830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="897,-1233 924,-1233 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_3abc950@0" ObjectIDND1="30137@x" ObjectIDND2="30134@x" ObjectIDZND0="g_47041c0@0" Pin0InfoVect0LinkObjId="g_47041c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3abc950_0" Pin1InfoVect1LinkObjId="SW-196762_0" Pin1InfoVect2LinkObjId="SW-196759_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="897,-1233 924,-1233 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_313ea40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="923,-1203 897,-1203 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="30137@1" ObjectIDZND0="30134@x" ObjectIDZND1="g_3abc950@0" ObjectIDZND2="g_47041c0@0" Pin0InfoVect0LinkObjId="SW-196759_0" Pin0InfoVect1LinkObjId="g_3abc950_0" Pin0InfoVect2LinkObjId="g_47041c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196762_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="923,-1203 897,-1203 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d96520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="834,-1233 897,-1233 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_3abc950@0" ObjectIDZND0="30137@x" ObjectIDZND1="30134@x" ObjectIDZND2="g_47041c0@0" Pin0InfoVect0LinkObjId="SW-196762_0" Pin0InfoVect1LinkObjId="SW-196759_0" Pin0InfoVect2LinkObjId="g_47041c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3abc950_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="834,-1233 897,-1233 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d971b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="897,-1233 897,-1247 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_3abc950@0" ObjectIDND1="30137@x" ObjectIDND2="30134@x" ObjectIDZND0="37445@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3abc950_0" Pin1InfoVect1LinkObjId="SW-196762_0" Pin1InfoVect2LinkObjId="SW-196759_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="897,-1233 897,-1247 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d97410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="933,-1095 897,-1095 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="30136@1" ObjectIDZND0="30133@x" ObjectIDZND1="30131@x" Pin0InfoVect0LinkObjId="SW-196758_0" Pin0InfoVect1LinkObjId="SW-196756_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196761_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="933,-1095 897,-1095 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d97670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="930,-1036 897,-1036 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="30135@1" ObjectIDZND0="30132@x" ObjectIDZND1="30133@x" Pin0InfoVect0LinkObjId="SW-196757_0" Pin0InfoVect1LinkObjId="SW-196758_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196760_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="930,-1036 897,-1036 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_39dfab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="897,-1082 897,-1095 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="30133@1" ObjectIDZND0="30136@x" ObjectIDZND1="30131@x" Pin0InfoVect0LinkObjId="SW-196761_0" Pin0InfoVect1LinkObjId="SW-196756_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196758_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="897,-1082 897,-1095 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_39dfd00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="897,-1095 897,-1111 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="30136@x" ObjectIDND1="30133@x" ObjectIDZND0="30131@0" Pin0InfoVect0LinkObjId="SW-196756_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-196761_0" Pin1InfoVect1LinkObjId="SW-196758_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="897,-1095 897,-1111 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_48bff20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="897,-1021 897,-1036 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="30132@1" ObjectIDZND0="30135@x" ObjectIDZND1="30133@x" Pin0InfoVect0LinkObjId="SW-196760_0" Pin0InfoVect1LinkObjId="SW-196758_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196757_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="897,-1021 897,-1036 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_48c0180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="897,-1036 897,-1046 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="30135@x" ObjectIDND1="30132@x" ObjectIDZND0="30133@0" Pin0InfoVect0LinkObjId="SW-196758_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-196760_0" Pin1InfoVect1LinkObjId="SW-196757_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="897,-1036 897,-1046 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_46d67b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="45,-508 45,-497 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27271@0" ObjectIDZND0="27245@0" Pin0InfoVect0LinkObjId="g_2c87b20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171224_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="45,-508 45,-497 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_46d6a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="15,-497 15,-483 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="27245@0" ObjectIDZND0="27281@0" Pin0InfoVect0LinkObjId="SW-171307_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c87b20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="15,-497 15,-483 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35bd040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-229,-454 -229,-439 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27310@1" ObjectIDZND0="27308@1" Pin0InfoVect0LinkObjId="SW-171412_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171414_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-229,-454 -229,-439 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35bd2a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-229,-396 -229,-412 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27311@1" ObjectIDZND0="27308@0" Pin0InfoVect0LinkObjId="SW-171412_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171414_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-229,-396 -229,-412 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_46d2a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="15,-467 15,-452 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27281@1" ObjectIDZND0="27279@1" Pin0InfoVect0LinkObjId="SW-171305_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171307_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="15,-467 15,-452 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_46d2cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="15,-409 15,-425 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27282@1" ObjectIDZND0="27279@0" Pin0InfoVect0LinkObjId="SW-171305_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171307_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="15,-409 15,-425 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a741c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="171,-461 171,-446 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27286@1" ObjectIDZND0="27284@1" Pin0InfoVect0LinkObjId="SW-171323_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171325_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="171,-461 171,-446 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a74420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="171,-403 171,-419 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27287@1" ObjectIDZND0="27284@0" Pin0InfoVect0LinkObjId="SW-171323_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171325_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="171,-403 171,-419 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a74c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="137,-368 171,-368 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_31d05c0@0" ObjectIDZND0="g_35b7810@0" ObjectIDZND1="27287@x" ObjectIDZND2="27341@x" Pin0InfoVect0LinkObjId="g_35b7810_0" Pin0InfoVect1LinkObjId="SW-171325_0" Pin0InfoVect2LinkObjId="SW-171596_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_31d05c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="137,-368 171,-368 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36b8a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="367,-464 367,-449 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27290@1" ObjectIDZND0="27288@1" Pin0InfoVect0LinkObjId="SW-171340_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171342_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="367,-464 367,-449 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36b8cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="367,-406 367,-422 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27291@1" ObjectIDZND0="27288@0" Pin0InfoVect0LinkObjId="SW-171340_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171342_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="367,-406 367,-422 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a8d070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="642,-454 642,-439 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27295@1" ObjectIDZND0="27293@1" Pin0InfoVect0LinkObjId="SW-171358_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171360_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="642,-454 642,-439 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a8d2d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="642,-396 642,-412 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27296@1" ObjectIDZND0="27293@0" Pin0InfoVect0LinkObjId="SW-171358_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171360_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="642,-396 642,-412 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a8db00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="641,-356 641,-335 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_20ca6d0@0" ObjectIDND1="27297@x" ObjectIDND2="27296@x" ObjectIDZND0="g_31f28d0@1" Pin0InfoVect0LinkObjId="g_31f28d0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_20ca6d0_0" Pin1InfoVect1LinkObjId="SW-171361_0" Pin1InfoVect2LinkObjId="SW-171360_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="641,-356 641,-335 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a95d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="830,-457 830,-442 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27300@1" ObjectIDZND0="27298@1" Pin0InfoVect0LinkObjId="SW-171376_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171378_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="830,-457 830,-442 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a95f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="830,-399 830,-415 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27301@1" ObjectIDZND0="27298@0" Pin0InfoVect0LinkObjId="SW-171376_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171378_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="830,-399 830,-415 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_317b770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1013,-455 1013,-440 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27305@1" ObjectIDZND0="27303@1" Pin0InfoVect0LinkObjId="SW-171394_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171396_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1013,-455 1013,-440 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_317b9d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1013,-397 1013,-413 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27306@1" ObjectIDZND0="27303@0" Pin0InfoVect0LinkObjId="SW-171394_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171396_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1013,-397 1013,-413 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31841d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1200,-457 1200,-442 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27315@1" ObjectIDZND0="27313@1" Pin0InfoVect0LinkObjId="SW-171429_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171431_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1200,-457 1200,-442 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3184430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1200,-399 1200,-415 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27316@1" ObjectIDZND0="27313@0" Pin0InfoVect0LinkObjId="SW-171429_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171431_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1200,-399 1200,-415 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_318cc30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-132,-598 -132,-583 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27321@1" ObjectIDZND0="27318@1" Pin0InfoVect0LinkObjId="SW-171446_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171448_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-132,-598 -132,-583 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_318ce90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-132,-540 -132,-556 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27320@1" ObjectIDZND0="27318@0" Pin0InfoVect0LinkObjId="SW-171446_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171448_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-132,-540 -132,-556 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3af6c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="45,-527 45,-543 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27271@1" ObjectIDZND0="27270@0" Pin0InfoVect0LinkObjId="SW-171223_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171224_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="45,-527 45,-543 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3aff430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="431,-601 431,-586 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27333@1" ObjectIDZND0="27331@1" Pin0InfoVect0LinkObjId="SW-171489_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171490_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="431,-601 431,-586 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3aff690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="431,-545 431,-559 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27332@1" ObjectIDZND0="27331@0" Pin0InfoVect0LinkObjId="SW-171489_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171490_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="431,-545 431,-559 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b05dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="576,-546 576,-586 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="27334@1" ObjectIDZND0="27335@1" Pin0InfoVect0LinkObjId="SW-171492_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171492_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="576,-546 576,-586 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b0e5c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="847,-585 847,-570 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27278@1" ObjectIDZND0="27276@1" Pin0InfoVect0LinkObjId="SW-171248_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171249_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="847,-585 847,-570 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b0e820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="847,-530 847,-543 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27277@1" ObjectIDZND0="27276@0" Pin0InfoVect0LinkObjId="SW-171248_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171249_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="847,-530 847,-543 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_46f69f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1051,-641 1094,-641 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_4050e80@0" ObjectIDZND0="g_31ddf30@0" ObjectIDZND1="27330@x" ObjectIDZND2="27327@x" Pin0InfoVect0LinkObjId="g_31ddf30_0" Pin0InfoVect1LinkObjId="SW-171472_0" Pin0InfoVect2LinkObjId="SW-171469_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4050e80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1051,-641 1094,-641 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_46f6c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1092,-575 1092,-590 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="27325@1" ObjectIDZND0="27327@1" Pin0InfoVect0LinkObjId="SW-171469_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171468_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1092,-575 1092,-590 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_46f6eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1092,-532 1092,-548 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27326@1" ObjectIDZND0="27325@0" Pin0InfoVect0LinkObjId="SW-171468_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171469_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1092,-532 1092,-548 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_46f7110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1092,-515 1092,-494 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27326@0" ObjectIDZND0="27246@0" Pin0InfoVect0LinkObjId="g_2e91430_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171469_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1092,-515 1092,-494 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_47016b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-17,-1124 -17,-1083 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_3ae2500@0" ObjectIDZND0="27262@x" ObjectIDZND1="27260@x" ObjectIDZND2="g_3ae3db0@0" Pin0InfoVect0LinkObjId="SW-171163_0" Pin0InfoVect1LinkObjId="SW-171161_0" Pin0InfoVect2LinkObjId="g_3ae3db0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3ae2500_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-17,-1124 -17,-1083 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_47018a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="239,-1218 239,-1177 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_36ee2b0@0" ObjectIDND1="g_4701a90@0" ObjectIDND2="37838@1" ObjectIDZND0="27252@x" ObjectIDZND1="27250@x" Pin0InfoVect0LinkObjId="SW-171113_0" Pin0InfoVect1LinkObjId="SW-171111_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_36ee2b0_0" Pin1InfoVect1LinkObjId="g_4701a90_0" Pin1InfoVect2LinkObjId="g_355e000_1" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="239,-1218 239,-1177 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4702520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="591,-1217 591,-1178 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_30dd610@0" ObjectIDND1="g_4702780@0" ObjectIDND2="37847@1" ObjectIDZND0="27259@x" ObjectIDZND1="27256@x" Pin0InfoVect0LinkObjId="SW-171135_0" Pin0InfoVect1LinkObjId="SW-171132_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_30dd610_0" Pin1InfoVect1LinkObjId="g_4702780_0" Pin1InfoVect2LinkObjId="g_36cbf40_1" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="591,-1217 591,-1178 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4703d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="897,-1193 897,-1203 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="30134@1" ObjectIDZND0="30137@x" ObjectIDZND1="g_3abc950@0" ObjectIDZND2="g_47041c0@0" Pin0InfoVect0LinkObjId="SW-196762_0" Pin0InfoVect1LinkObjId="g_3abc950_0" Pin0InfoVect2LinkObjId="g_47041c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196759_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="897,-1193 897,-1203 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4703f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="897,-1233 897,-1203 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_3abc950@0" ObjectIDND1="g_47041c0@0" ObjectIDND2="37445@1" ObjectIDZND0="30137@x" ObjectIDZND1="30134@x" Pin0InfoVect0LinkObjId="SW-196762_0" Pin0InfoVect1LinkObjId="SW-196759_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3abc950_0" Pin1InfoVect1LinkObjId="g_47041c0_0" Pin1InfoVect2LinkObjId="g_2d971b0_1" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="897,-1233 897,-1203 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4705b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="45,-571 45,-585 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="27270@1" ObjectIDZND0="27272@1" Pin0InfoVect0LinkObjId="SW-171224_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171223_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="45,-571 45,-585 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4705db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="45,-670 45,-684 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_4704f30@1" ObjectIDZND0="27336@1" Pin0InfoVect0LinkObjId="g_37297b0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4704f30_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="45,-670 45,-684 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4706010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2,-618 45,-618 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_4706270@0" ObjectIDZND0="27272@x" ObjectIDZND1="g_4704f30@0" Pin0InfoVect0LinkObjId="SW-171224_0" Pin0InfoVect1LinkObjId="g_4704f30_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4706270_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2,-618 45,-618 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4707830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="45,-602 45,-618 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="27272@0" ObjectIDZND0="g_4706270@0" ObjectIDZND1="g_4704f30@0" Pin0InfoVect0LinkObjId="g_4706270_0" Pin0InfoVect1LinkObjId="g_4704f30_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171224_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="45,-602 45,-618 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4707a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="45,-618 45,-631 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_4706270@0" ObjectIDND1="27272@x" ObjectIDZND0="g_4704f30@0" Pin0InfoVect0LinkObjId="g_4704f30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_4706270_0" Pin1InfoVect1LinkObjId="SW-171224_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="45,-618 45,-631 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4707cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-132,-796 -132,-766 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="27323@x" ObjectIDND1="41293@x" ObjectIDND2="g_3731570@0" ObjectIDZND0="27319@1" Pin0InfoVect0LinkObjId="SW-171447_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-171450_0" Pin1InfoVect1LinkObjId="CB-YM_WM.YM_WM_Cb1_0" Pin1InfoVect2LinkObjId="g_3731570_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-132,-796 -132,-766 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4707f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="804,-612 847,-612 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_47081b0@0" ObjectIDZND0="g_2cdccc0@0" ObjectIDZND1="27278@x" Pin0InfoVect0LinkObjId="g_2cdccc0_0" Pin0InfoVect1LinkObjId="SW-171249_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_47081b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="804,-612 847,-612 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4709770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="847,-618 847,-612 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2cdccc0@0" ObjectIDZND0="g_47081b0@0" ObjectIDZND1="27278@x" Pin0InfoVect0LinkObjId="g_47081b0_0" Pin0InfoVect1LinkObjId="SW-171249_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2cdccc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="847,-618 847,-612 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_47099d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="847,-612 847,-602 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_47081b0@0" ObjectIDND1="g_2cdccc0@0" ObjectIDZND0="27278@0" Pin0InfoVect0LinkObjId="SW-171249_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_47081b0_0" Pin1InfoVect1LinkObjId="g_2cdccc0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="847,-612 847,-602 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4709c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="846,-749 795,-749 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_4709e90@0" Pin0InfoVect0LinkObjId="g_4709e90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="846,-749 795,-749 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_470abc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1092,-797 1092,-766 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="41294@x" ObjectIDND1="27329@x" ObjectIDZND0="27328@1" Pin0InfoVect0LinkObjId="SW-171470_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="CB-YM_WM.YM_WM_Cb2_0" Pin1InfoVect1LinkObjId="SW-171471_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1092,-797 1092,-766 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_470ae20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1092,-607 1092,-641 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="27327@0" ObjectIDZND0="g_31ddf30@0" ObjectIDZND1="27330@x" ObjectIDZND2="g_4050e80@0" Pin0InfoVect0LinkObjId="g_31ddf30_0" Pin0InfoVect1LinkObjId="SW-171472_0" Pin0InfoVect2LinkObjId="g_4050e80_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171469_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1092,-607 1092,-641 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_470b080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-229,-334 -229,-367 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2d94d40@1" ObjectIDZND0="27312@x" ObjectIDZND1="27311@x" ObjectIDZND2="g_3378de0@0" Pin0InfoVect0LinkObjId="SW-171415_0" Pin0InfoVect1LinkObjId="SW-171414_0" Pin0InfoVect2LinkObjId="g_3378de0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d94d40_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-229,-334 -229,-367 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_470b2e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="15,-367 15,-384 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2c7a7b0@1" ObjectIDZND0="27283@x" ObjectIDZND1="27282@x" ObjectIDZND2="g_373de00@0" Pin0InfoVect0LinkObjId="SW-171308_0" Pin0InfoVect1LinkObjId="SW-171307_0" Pin0InfoVect2LinkObjId="g_373de00_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c7a7b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="15,-367 15,-384 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_470b540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-9,-384 -9,-375 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="27283@x" ObjectIDND1="27282@x" ObjectIDND2="g_2c7a7b0@0" ObjectIDZND0="g_373de00@0" Pin0InfoVect0LinkObjId="g_373de00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-171308_0" Pin1InfoVect1LinkObjId="SW-171307_0" Pin1InfoVect2LinkObjId="g_2c7a7b0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-9,-384 -9,-375 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_470c030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-9,-384 15,-384 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_373de00@0" ObjectIDND1="g_2ce0c00@0" ObjectIDZND0="27283@x" ObjectIDZND1="27282@x" ObjectIDZND2="g_2c7a7b0@0" Pin0InfoVect0LinkObjId="SW-171308_0" Pin0InfoVect1LinkObjId="SW-171307_0" Pin0InfoVect2LinkObjId="g_2c7a7b0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_373de00_0" Pin1InfoVect1LinkObjId="g_2ce0c00_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-9,-384 15,-384 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_470c290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-42,-373 -42,-384 -9,-384 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2ce0c00@0" ObjectIDZND0="g_373de00@0" ObjectIDZND1="27283@x" ObjectIDZND2="27282@x" Pin0InfoVect0LinkObjId="g_373de00_0" Pin0InfoVect1LinkObjId="SW-171308_0" Pin0InfoVect2LinkObjId="SW-171307_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ce0c00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-42,-373 -42,-384 -9,-384 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_470c4f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="37,-246 37,-235 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="load" EndDevType0="lightningRod" ObjectIDND0="g_3a1e2d0@0" ObjectIDND1="27280@x" ObjectIDND2="34206@x" ObjectIDZND0="g_470d4a0@0" Pin0InfoVect0LinkObjId="g_470d4a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3a1e2d0_0" Pin1InfoVect1LinkObjId="SW-171306_0" Pin1InfoVect2LinkObjId="EC-YM_WM.072Ld_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="37,-246 37,-235 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_470cfe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="15,-246 37,-246 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="load" EndDevType0="hydroGenerator" EndDevType1="lightningRod" ObjectIDND0="g_3a1e2d0@0" ObjectIDND1="27280@x" ObjectIDND2="34206@x" ObjectIDZND0="0@x" ObjectIDZND1="g_470d4a0@0" Pin0InfoVect0LinkObjId="TF-0_0" Pin0InfoVect1LinkObjId="g_470d4a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3a1e2d0_0" Pin1InfoVect1LinkObjId="SW-171306_0" Pin1InfoVect2LinkObjId="EC-YM_WM.072Ld_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="15,-246 37,-246 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_470d240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="37,-246 66,-246 66,-232 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="load" EndDevType0="hydroGenerator" ObjectIDND0="g_3a1e2d0@0" ObjectIDND1="27280@x" ObjectIDND2="34206@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="TF-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3a1e2d0_0" Pin1InfoVect1LinkObjId="SW-171306_0" Pin1InfoVect2LinkObjId="EC-YM_WM.072Ld_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="37,-246 66,-246 66,-232 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_470e1d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="15,-246 15,-213 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="hydroGenerator" EndDevType0="load" ObjectIDND0="g_3a1e2d0@0" ObjectIDND1="27280@x" ObjectIDND2="0@x" ObjectIDZND0="34206@0" Pin0InfoVect0LinkObjId="EC-YM_WM.072Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3a1e2d0_0" Pin1InfoVect1LinkObjId="SW-171306_0" Pin1InfoVect2LinkObjId="TF-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="15,-246 15,-213 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_470eee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="171,-343 171,-368 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_35b7810@1" ObjectIDZND0="g_31d05c0@0" ObjectIDZND1="27287@x" ObjectIDZND2="27341@x" Pin0InfoVect0LinkObjId="g_31d05c0_0" Pin0InfoVect1LinkObjId="SW-171325_0" Pin0InfoVect2LinkObjId="SW-171596_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_35b7810_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="171,-343 171,-368 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_470f140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="171,-386 171,-368 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="27287@0" ObjectIDZND0="g_31d05c0@0" ObjectIDZND1="g_35b7810@0" ObjectIDZND2="27341@x" Pin0InfoVect0LinkObjId="g_31d05c0_0" Pin0InfoVect1LinkObjId="g_35b7810_0" Pin0InfoVect2LinkObjId="SW-171596_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171325_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="171,-386 171,-368 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_470f3a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="343,-365 367,-365 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_4710870@0" ObjectIDND1="g_470f600@0" ObjectIDZND0="27292@x" ObjectIDZND1="g_39fe8d0@0" ObjectIDZND2="27291@x" Pin0InfoVect0LinkObjId="SW-171343_0" Pin0InfoVect1LinkObjId="g_39fe8d0_0" Pin0InfoVect2LinkObjId="SW-171342_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_4710870_0" Pin1InfoVect1LinkObjId="g_470f600_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="343,-365 367,-365 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_47103b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="343,-365 343,-356 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_470f600@0" ObjectIDND1="27292@x" ObjectIDND2="g_39fe8d0@0" ObjectIDZND0="g_4710870@0" Pin0InfoVect0LinkObjId="g_4710870_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_470f600_0" Pin1InfoVect1LinkObjId="SW-171343_0" Pin1InfoVect2LinkObjId="g_39fe8d0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="343,-365 343,-356 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4710610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="310,-354 310,-365 343,-365 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_470f600@0" ObjectIDZND0="g_4710870@0" ObjectIDZND1="27292@x" ObjectIDZND2="g_39fe8d0@0" Pin0InfoVect0LinkObjId="g_4710870_0" Pin0InfoVect1LinkObjId="SW-171343_0" Pin0InfoVect2LinkObjId="g_39fe8d0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_470f600_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="310,-354 310,-365 343,-365 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_47128e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="367,-342 367,-365 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_39fe8d0@1" ObjectIDZND0="27292@x" ObjectIDZND1="27291@x" ObjectIDZND2="g_4710870@0" Pin0InfoVect0LinkObjId="SW-171343_0" Pin0InfoVect1LinkObjId="SW-171342_0" Pin0InfoVect2LinkObjId="g_4710870_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_39fe8d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="367,-342 367,-365 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4712b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="367,-387 367,-365 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="27291@0" ObjectIDZND0="27292@x" ObjectIDZND1="g_39fe8d0@0" ObjectIDZND2="g_4710870@0" Pin0InfoVect0LinkObjId="SW-171343_0" Pin0InfoVect1LinkObjId="g_39fe8d0_0" Pin0InfoVect2LinkObjId="g_4710870_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171342_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="367,-387 367,-365 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4713ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="367,-225 367,-201 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="hydroGenerator" BeginDevType2="lightningRod" EndDevType0="load" ObjectIDND0="27289@x" ObjectIDND1="0@x" ObjectIDND2="g_4712da0@0" ObjectIDZND0="34208@0" Pin0InfoVect0LinkObjId="EC-YM_WM.074Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-171341_0" Pin1InfoVect1LinkObjId="TF-0_0" Pin1InfoVect2LinkObjId="g_4712da0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="367,-225 367,-201 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4714ae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="806,-361 806,-352 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_4713d30@0" ObjectIDND1="27302@x" ObjectIDND2="g_2affc90@0" ObjectIDZND0="g_4714fa0@0" Pin0InfoVect0LinkObjId="g_4714fa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_4713d30_0" Pin1InfoVect1LinkObjId="SW-171379_0" Pin1InfoVect2LinkObjId="g_2affc90_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="806,-361 806,-352 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4714d40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="773,-350 773,-361 806,-361 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_4713d30@0" ObjectIDZND0="g_4714fa0@0" ObjectIDZND1="27302@x" ObjectIDZND2="g_2affc90@0" Pin0InfoVect0LinkObjId="g_4714fa0_0" Pin0InfoVect1LinkObjId="SW-171379_0" Pin0InfoVect2LinkObjId="g_2affc90_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4713d30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="773,-350 773,-361 806,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4715cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="806,-361 830,-361 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_4714fa0@0" ObjectIDND1="g_4713d30@0" ObjectIDZND0="27302@x" ObjectIDZND1="g_2affc90@0" ObjectIDZND2="27301@x" Pin0InfoVect0LinkObjId="SW-171379_0" Pin0InfoVect1LinkObjId="g_2affc90_0" Pin0InfoVect2LinkObjId="SW-171378_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_4714fa0_0" Pin1InfoVect1LinkObjId="g_4713d30_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="806,-361 830,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4717270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="830,-361 830,-335 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="27302@x" ObjectIDND1="27301@x" ObjectIDND2="g_4714fa0@0" ObjectIDZND0="g_2affc90@1" Pin0InfoVect0LinkObjId="g_2affc90_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-171379_0" Pin1InfoVect1LinkObjId="SW-171378_0" Pin1InfoVect2LinkObjId="g_4714fa0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="830,-361 830,-335 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_47174d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="830,-380 830,-361 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="27301@0" ObjectIDZND0="27302@x" ObjectIDZND1="g_2affc90@0" ObjectIDZND2="g_4714fa0@0" Pin0InfoVect0LinkObjId="SW-171379_0" Pin0InfoVect1LinkObjId="g_2affc90_0" Pin0InfoVect2LinkObjId="g_4714fa0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171378_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="830,-380 830,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4717730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="989,-357 989,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_4718b80@0" ObjectIDND1="g_39fefa0@0" ObjectIDND2="27306@x" ObjectIDZND0="g_4717bf0@0" Pin0InfoVect0LinkObjId="g_4717bf0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_4718b80_0" Pin1InfoVect1LinkObjId="g_39fefa0_0" Pin1InfoVect2LinkObjId="SW-171396_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="989,-357 989,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4717990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="956,-346 956,-357 989,-357 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_4718b80@0" ObjectIDZND0="g_39fefa0@0" ObjectIDZND1="27306@x" ObjectIDZND2="27307@x" Pin0InfoVect0LinkObjId="g_39fefa0_0" Pin0InfoVect1LinkObjId="SW-171396_0" Pin0InfoVect2LinkObjId="SW-171397_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4718b80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="956,-346 956,-357 989,-357 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4718920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="989,-357 1013,-357 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_4718b80@0" ObjectIDND1="g_4717bf0@0" ObjectIDZND0="g_39fefa0@0" ObjectIDZND1="27306@x" ObjectIDZND2="27307@x" Pin0InfoVect0LinkObjId="g_39fefa0_0" Pin0InfoVect1LinkObjId="SW-171396_0" Pin0InfoVect2LinkObjId="SW-171397_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_4718b80_0" Pin1InfoVect1LinkObjId="g_4717bf0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="989,-357 1013,-357 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_471ac70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1013,-357 1013,-334 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="27306@x" ObjectIDND1="g_4718b80@0" ObjectIDND2="g_4717bf0@0" ObjectIDZND0="g_39fefa0@1" Pin0InfoVect0LinkObjId="g_39fefa0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-171396_0" Pin1InfoVect1LinkObjId="g_4718b80_0" Pin1InfoVect2LinkObjId="g_4717bf0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1013,-357 1013,-334 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_471aed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1013,-379 1013,-357 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="27306@0" ObjectIDZND0="g_39fefa0@0" ObjectIDZND1="g_4718b80@0" ObjectIDZND2="g_4717bf0@0" Pin0InfoVect0LinkObjId="g_39fefa0_0" Pin0InfoVect1LinkObjId="g_4718b80_0" Pin0InfoVect2LinkObjId="g_4717bf0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171396_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1013,-379 1013,-357 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_471b130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1083,-357 1071,-357 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3a1d060@0" ObjectIDZND0="27307@0" Pin0InfoVect0LinkObjId="SW-171397_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3a1d060_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1083,-357 1071,-357 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_471b390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1035,-357 1013,-357 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="27307@1" ObjectIDZND0="g_39fefa0@0" ObjectIDZND1="27306@x" ObjectIDZND2="g_4718b80@0" Pin0InfoVect0LinkObjId="g_39fefa0_0" Pin0InfoVect1LinkObjId="SW-171396_0" Pin0InfoVect2LinkObjId="g_4718b80_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171397_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1035,-357 1013,-357 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_471c320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1167,-356 1200,-356 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_471b5f0@0" ObjectIDZND0="g_2b4ab40@0" ObjectIDZND1="27317@x" ObjectIDZND2="27316@x" Pin0InfoVect0LinkObjId="g_2b4ab40_0" Pin0InfoVect1LinkObjId="SW-171432_0" Pin0InfoVect2LinkObjId="SW-171431_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_471b5f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1167,-356 1200,-356 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_471d030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1200,-335 1200,-356 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2b4ab40@1" ObjectIDZND0="g_471b5f0@0" ObjectIDZND1="27317@x" ObjectIDZND2="27316@x" Pin0InfoVect0LinkObjId="g_471b5f0_0" Pin0InfoVect1LinkObjId="SW-171432_0" Pin0InfoVect2LinkObjId="SW-171431_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b4ab40_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1200,-335 1200,-356 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_471d290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1200,-380 1200,-356 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="27316@0" ObjectIDZND0="g_471b5f0@0" ObjectIDZND1="g_2b4ab40@0" ObjectIDZND2="27317@x" Pin0InfoVect0LinkObjId="g_471b5f0_0" Pin0InfoVect1LinkObjId="g_2b4ab40_0" Pin0InfoVect2LinkObjId="SW-171432_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171431_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1200,-380 1200,-356 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_471d4f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="830,-234 830,-214 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="hydroGenerator" EndDevType1="load" EndDevType2="lightningRod" ObjectIDND0="27299@0" ObjectIDZND0="0@x" ObjectIDZND1="34210@x" ObjectIDZND2="g_2dd1730@0" Pin0InfoVect0LinkObjId="TF-0_0" Pin0InfoVect1LinkObjId="EC-YM_WM.082Ld_0" Pin0InfoVect2LinkObjId="g_2dd1730_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171377_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="830,-234 830,-214 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_471d750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1013,-233 1013,-214 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="hydroGenerator" EndDevType1="load" EndDevType2="lightningRod" ObjectIDND0="27304@0" ObjectIDZND0="0@x" ObjectIDZND1="34211@x" ObjectIDZND2="g_3aafed0@0" Pin0InfoVect0LinkObjId="TF-0_0" Pin0InfoVect1LinkObjId="EC-YM_WM.083Ld_0" Pin0InfoVect2LinkObjId="g_3aafed0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171395_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1013,-233 1013,-214 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="27244" cx="489" cy="-969" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27244" cx="45" cy="-969" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27244" cx="847" cy="-969" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27246" cx="706" cy="-494" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27245" cx="15" cy="-497" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27245" cx="-229" cy="-497" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27245" cx="45" cy="-497" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27245" cx="45" cy="-497" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27245" cx="292" cy="-497" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27244" cx="-17" cy="-969" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27244" cx="239" cy="-969" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27244" cx="591" cy="-969" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27244" cx="897" cy="-969" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27245" cx="15" cy="-497" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27245" cx="171" cy="-497" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27245" cx="367" cy="-497" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27246" cx="641" cy="-494" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27246" cx="831" cy="-494" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27246" cx="1013" cy="-494" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27246" cx="1200" cy="-494" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27245" cx="-132" cy="-497" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27245" cx="45" cy="-497" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27245" cx="431" cy="-497" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27246" cx="575" cy="-494" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27246" cx="847" cy="-494" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27246" cx="1092" cy="-494" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-154234" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -421.000000 -1166.000000)" xlink:href="#dynamicPoint:shape33"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26168" ObjectName="DYN-YM_WM"/>
     <cge:Meas_Ref ObjectId="154234"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(64,64,64)" font-family="SimHei" font-size="20" graphid="g_31c7580" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -567.000000 -1239.500000) translate(0,16)">物茂变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_36cf320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,17)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_36cf320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_36cf320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,59)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_36cf320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_36cf320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,101)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_36cf320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_36cf320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,143)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_36cf320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_36cf320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,185)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c4c740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c4c740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c4c740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c4c740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c4c740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c4c740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c4c740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c4c740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c4c740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c4c740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c4c740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c4c740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c4c740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c4c740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c4c740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c4c740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c4c740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c4c740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a05c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -58.000000 -156.000000) translate(0,12)">10kV凹鲊线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ae8140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 118.000000 -156.000000) translate(0,12)">10kV物茂线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30f60c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 324.000000 -156.000000) translate(0,12)">10kV土林线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e0ed50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 588.000000 -156.000000) translate(0,12)">10kV虎溪线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ceae90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 790.000000 -156.000000) translate(0,12)">10kV龙潭线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e54b60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 996.000000 -156.000000) translate(0,12)">10kV芝麻线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31bc250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -272.000000 -156.000000) translate(0,12)">10kV备用一</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a6b220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1156.000000 -156.000000) translate(0,12)">10kV德大线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a6b440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -52.000000 -1263.000000) translate(0,12)">35kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36a7390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 257.000000 -1257.000000) translate(0,12)">35kV永物T线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39ff570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 607.000000 -1256.000000) translate(0,12)">35kV黄物T线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cfc5e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1047.000000 -947.000000) translate(0,12)">10kVⅡ段电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30f8ec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 249.000000 -737.000000) translate(0,12)">10kVⅠ段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36e1500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 606.000000 -701.000000) translate(0,12)">10kVⅡ段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36e1700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 529.000000 -815.000000) translate(0,12)">35kV1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a006e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -171.000000 -207.000000) translate(0,12)">10kV2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35a15c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44.000000 -265.000000) translate(0,12)">竹棚电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31d9890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 435.000000 -208.000000) translate(0,12)">虎跳滩电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ae2100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 866.000000 -233.000000) translate(0,12)">那化电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3144dc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1040.000000 -239.000000) translate(0,12)">骂额电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3192390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -222.000000 -253.000000) translate(0,12)">0766</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30f7350" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -209.000000 -393.000000) translate(0,12)">07637</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30f75d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 34.000000 -410.000000) translate(0,12)">07237</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_313f3a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 22.000000 -293.000000) translate(0,12)">0726</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_313f5a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1219.000000 -379.000000) translate(0,12)">08437</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30d6cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1207.000000 -254.000000) translate(0,12)">0846</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30d6f40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 587.000000 -573.000000) translate(0,12)">0121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3138d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1115.000000 -667.000000) translate(0,12)">08537</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3138f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1112.000000 -823.000000) translate(0,12)">08567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31391a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1099.000000 -756.000000) translate(0,12)">0856</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36c7dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -356.000000 -506.000000) translate(0,12)">Ⅰ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36c8010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 661.000000 -394.000000) translate(0,12)">08137</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30f2fb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 648.000000 -254.000000) translate(0,12)">0816</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30f31f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 854.000000 -382.000000) translate(0,12)">08237</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30f3430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 837.000000 -259.000000) translate(0,12)">0826</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a541c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 386.000000 -386.000000) translate(0,12)">07437</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a543d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 374.000000 -266.000000) translate(0,12)">0746</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a54610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 178.000000 -262.000000) translate(0,12)">0736</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39f8a90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1034.000000 -381.000000) translate(0,12)">08337</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39f8ca0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1020.000000 -258.000000) translate(0,12)">0836</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39f8ee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 90.000000 -926.000000) translate(0,12)">30117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ade620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 54.000000 -877.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ade830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 52.000000 -943.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3adea70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 854.000000 -941.000000) translate(0,12)">3021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ae54c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 856.000000 -884.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ae56d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -170.000000 -989.000000) translate(0,12)">IM段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ae5910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 635.000000 -1076.000000) translate(0,12)">37217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39ecee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 626.000000 -1130.000000) translate(0,12)">37260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39ed120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 614.000000 -1204.000000) translate(0,12)">37267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39ed360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 598.000000 -1019.000000) translate(0,12)">3721</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39fde20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 598.000000 -1146.000000) translate(0,12)">3726</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39fe030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 601.000000 -1084.000000) translate(0,12)">372</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39fe270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 10.000000 -1033.000000) translate(0,12)">39010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a03a40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 11.000000 -1109.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3aa31a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -10.000000 -1057.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3aa33e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 276.000000 -1069.000000) translate(0,12)">37117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3aa3620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 246.000000 -1020.000000) translate(0,12)">3711</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35a03d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 276.000000 -1133.000000) translate(0,12)">37160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35a05e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 265.000000 -1203.000000) translate(0,12)">37167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35a0820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 246.000000 -1147.000000) translate(0,12)">3716</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35ab940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 249.000000 -1085.000000) translate(0,12)">371</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35abb40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -109.000000 -666.000000) translate(0,12)">07537</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35abd80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -97.000000 -741.000000) translate(0,12)">07560</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a04b10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -112.000000 -822.000000) translate(0,12)">07567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a04d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -125.000000 -755.000000) translate(0,12)">0756</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_253eba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 201.000000 -406.000000) translate(0,12)">07337</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_253ede0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 301.000000 -608.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d5d480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 715.000000 -596.000000) translate(0,12)">0902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3abc1c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 910.000000 -1262.000000) translate(0,12)">35kV坡物线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39f7be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 940.000000 -1062.000000) translate(0,12)">37317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30bf930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 939.000000 -1117.000000) translate(0,12)">37327</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30bfb70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 920.000000 -1225.000000) translate(0,12)">37367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30bfdb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 905.000000 -1010.000000) translate(0,12)">3731</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30bfff0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 905.000000 -1182.000000) translate(0,12)">3736</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30c0230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 908.000000 -1132.000000) translate(0,12)">373</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_313cfc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 905.000000 -1071.000000) translate(0,12)">3732</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_48c03e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 686.000000 -763.000000) translate(0,12)">2号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_48c03e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 686.000000 -763.000000) translate(0,27)">SZ11-10000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a78080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 139.000000 -773.000000) translate(0,12)">1号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a78080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 139.000000 -773.000000) translate(0,27)">SZ11-5000/35CY</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_46d7af0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -441.000000 -1226.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_3a6a000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -441.000000 -1263.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_46f7940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1279.000000 -499.000000) translate(0,12)">Ⅱ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_46f8a90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -219.000000 -435.000000) translate(0,12)">076</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_46f8cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 24.000000 -446.000000) translate(0,12)">072</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_46f8f10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 180.000000 -440.000000) translate(0,12)">073</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_46f9150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 379.000000 -443.000000) translate(0,12)">074</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_46f9390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 654.000000 -433.000000) translate(0,12)">081</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_46f95d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 842.000000 -436.000000) translate(0,12)">082</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_46f9810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1025.000000 -434.000000) translate(0,12)">083</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_46f9a50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1212.000000 -436.000000) translate(0,12)">084</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_46f9dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -124.000000 -577.000000) translate(0,12)">075</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_46fa0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 53.000000 -564.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_46fa320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 440.000000 -580.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_46fa560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 856.000000 -564.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_46fa7a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1101.000000 -569.000000) translate(0,12)">085</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_46fada0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -701.000000 -879.000000) translate(0,16)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4700c50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 895.000000 -931.000000) translate(0,12)">30217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_4723790" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -736.000000 -266.500000) translate(0,17)">元谋巡维中心：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_4724f50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -590.000000 -276.000000) translate(0,16)">18787879021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_4724f50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -590.000000 -276.000000) translate(0,36)">13908784331</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_47265e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -590.000000 -311.000000) translate(0,16)">8352785</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4729630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 73.000000 -730.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4729da0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 749.000000 -699.000000) translate(0,12)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_472a1e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -200.000000 -943.000000) translate(0,12)">10kVⅠ段电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_472a630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -294.000000 -1243.000000) translate(0,16)">AVC</text>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-171109">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 230.333333 -1056.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27248" ObjectName="SW-YM_WM.YM_WM_371BK"/>
     <cge:Meas_Ref ObjectId="171109"/>
    <cge:TPSR_Ref TObjectID="27248"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171130">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 582.333333 -1055.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27254" ObjectName="SW-YM_WM.YM_WM_372BK"/>
     <cge:Meas_Ref ObjectId="171130"/>
    <cge:TPSR_Ref TObjectID="27254"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171172">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 36.000000 -848.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27267" ObjectName="SW-YM_WM.YM_WM_301BK"/>
     <cge:Meas_Ref ObjectId="171172"/>
    <cge:TPSR_Ref TObjectID="27267"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171239">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 838.000000 -855.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27273" ObjectName="SW-YM_WM.YM_WM_302BK"/>
     <cge:Meas_Ref ObjectId="171239"/>
    <cge:TPSR_Ref TObjectID="27273"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196756">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 888.333333 -1103.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30131" ObjectName="SW-YM_WM.YM_WM_373BK"/>
     <cge:Meas_Ref ObjectId="196756"/>
    <cge:TPSR_Ref TObjectID="30131"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171412">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -239.000000 -404.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27308" ObjectName="SW-YM_WM.YM_WM_076BK"/>
     <cge:Meas_Ref ObjectId="171412"/>
    <cge:TPSR_Ref TObjectID="27308"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171305">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 -417.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27279" ObjectName="SW-YM_WM.YM_WM_072BK"/>
     <cge:Meas_Ref ObjectId="171305"/>
    <cge:TPSR_Ref TObjectID="27279"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171323">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 162.000000 -411.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27284" ObjectName="SW-YM_WM.YM_WM_073BK"/>
     <cge:Meas_Ref ObjectId="171323"/>
    <cge:TPSR_Ref TObjectID="27284"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171340">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 358.000000 -414.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27288" ObjectName="SW-YM_WM.YM_WM_074BK"/>
     <cge:Meas_Ref ObjectId="171340"/>
    <cge:TPSR_Ref TObjectID="27288"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171358">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 633.000000 -404.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27293" ObjectName="SW-YM_WM.YM_WM_081BK"/>
     <cge:Meas_Ref ObjectId="171358"/>
    <cge:TPSR_Ref TObjectID="27293"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171376">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 821.000000 -407.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27298" ObjectName="SW-YM_WM.YM_WM_082BK"/>
     <cge:Meas_Ref ObjectId="171376"/>
    <cge:TPSR_Ref TObjectID="27298"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171394">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1004.000000 -405.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27303" ObjectName="SW-YM_WM.YM_WM_083BK"/>
     <cge:Meas_Ref ObjectId="171394"/>
    <cge:TPSR_Ref TObjectID="27303"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171429">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1191.000000 -407.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27313" ObjectName="SW-YM_WM.YM_WM_084BK"/>
     <cge:Meas_Ref ObjectId="171429"/>
    <cge:TPSR_Ref TObjectID="27313"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171446">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -142.000000 -548.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27318" ObjectName="SW-YM_WM.YM_WM_075BK"/>
     <cge:Meas_Ref ObjectId="171446"/>
    <cge:TPSR_Ref TObjectID="27318"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171223">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 35.000000 -535.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27270" ObjectName="SW-YM_WM.YM_WM_001BK"/>
     <cge:Meas_Ref ObjectId="171223"/>
    <cge:TPSR_Ref TObjectID="27270"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171489">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 422.000000 -551.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27331" ObjectName="SW-YM_WM.YM_WM_012BK"/>
     <cge:Meas_Ref ObjectId="171489"/>
    <cge:TPSR_Ref TObjectID="27331"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171248">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 838.000000 -535.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27276" ObjectName="SW-YM_WM.YM_WM_002BK"/>
     <cge:Meas_Ref ObjectId="171248"/>
    <cge:TPSR_Ref TObjectID="27276"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-171468">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1083.000000 -540.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27325" ObjectName="SW-YM_WM.YM_WM_085BK"/>
     <cge:Meas_Ref ObjectId="171468"/>
    <cge:TPSR_Ref TObjectID="27325"/></metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_YR" endPointId="0" endStationName="YM_WM" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_yongwuT" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="239,-1245 239,-1279 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="37838" ObjectName="AC-35kV.LN_yongwuT"/>
    <cge:TPSR_Ref TObjectID="37838_SS-237"/></metadata>
   <polyline fill="none" opacity="0" points="239,-1245 239,-1279 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="YM_WM" endPointId="0" endStationName="CX_PTS" flowDrawDirect="1" flowShape="0" id="AC-35kV.LINE_powu" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="897,-1247 897,-1279 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="37445" ObjectName="AC-35kV.LINE_powu"/>
    <cge:TPSR_Ref TObjectID="37445_SS-237"/></metadata>
   <polyline fill="none" opacity="0" points="897,-1247 897,-1279 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_HGY" endPointId="0" endStationName="YM_WM" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_huanwuxian" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="591,-1238 591,-1276 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="37847" ObjectName="AC-35kV.LN_huanwuxian"/>
    <cge:TPSR_Ref TObjectID="37847_SS-237"/></metadata>
   <polyline fill="none" opacity="0" points="591,-1238 591,-1276 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_3594550" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 312.819337 -1171.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2df8480" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 58.819337 -1077.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3a99d30" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 57.819337 -1001.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35d1320" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 318.819337 -1044.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_31d2280" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 315.819337 -1103.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33743a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 662.819337 -1172.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2cd8da0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 668.819337 -1043.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2cddd10" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 665.819337 -1102.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_313a860" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 137.819337 -894.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_30d8ba0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 939.819337 -901.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b3db50" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -161.180663 -361.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3733090" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 82.819337 -378.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3a054b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 238.819337 -362.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_31be780" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 434.819337 -359.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_31e9ea0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 708.819337 -362.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d28970" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 897.819337 -355.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3a1d060" refnum="0">
    <use class="BV-0KV" transform="matrix(1.122896 -0.000000 0.000000 -1.000000 1078.068178 -351.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3a07be0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1267.819337 -350.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_31a7470" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -64.180663 -790.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ebe500" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -61.180663 -634.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_31e86a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -61.180663 -712.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b48d90" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1159.819337 -791.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_40aaa00" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1162.819337 -635.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3577ff0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 968.819337 -1197.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3190140" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 975.819337 -1030.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3d955e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 978.819337 -1089.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -618.000000 -1193.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-217879" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -589.000000 -1019.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217879" ObjectName="YM_WM:YM_WM_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-219736" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -589.000000 -977.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="219736" ObjectName="YM_WM:YM_WM_sumQ"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-217879" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -590.000000 -1100.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217879" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-217879" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -590.000000 -1060.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217879" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="-608" y="-1250"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="-608" y="-1250"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-655" y="-1269"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-655" y="-1269"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="249" y="-1085"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="249" y="-1085"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="601" y="-1084"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="601" y="-1084"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="908" y="-1132"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="908" y="-1132"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="-220" y="-436"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="-220" y="-436"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="179" y="-441"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="179" y="-441"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="376" y="-444"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="376" y="-444"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="652" y="-434"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="652" y="-434"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="839" y="-437"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="839" y="-437"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1022" y="-436"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1022" y="-436"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1209" y="-437"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1209" y="-437"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="-452" y="-1233"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="-452" y="-1233"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="-452" y="-1272"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="-452" y="-1272"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="24" y="-446"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="24" y="-446"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="-124" y="-577"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="-124" y="-577"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="440" y="-580"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="440" y="-580"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1101" y="-569"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1101" y="-569"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="30" qtmmishow="hidden" width="93" x="-706" y="-883"/>
    </a>
   <metadata/><rect fill="white" height="30" opacity="0" stroke="white" transform="" width="93" x="-706" y="-883"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="52" x="73" y="-730"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="52" x="73" y="-730"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="53" x="749" y="-699"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="53" x="749" y="-699"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <polygon fill="rgb(255,255,255)" points="-300,-1260 -303,-1263 -303,-1206 -300,-1209 -300,-1260" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(255,255,255)" points="-300,-1260 -303,-1263 -240,-1263 -243,-1260 -300,-1260" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(127,127,127)" points="-300,-1209 -303,-1206 -240,-1206 -243,-1209 -300,-1209" stroke="rgb(127,127,127)"/>
     <polygon fill="rgb(127,127,127)" points="-243,-1260 -240,-1263 -240,-1206 -243,-1209 -243,-1260" stroke="rgb(127,127,127)"/>
     <rect fill="rgb(255,255,255)" height="51" stroke="rgb(255,255,255)" width="57" x="-300" y="-1260"/>
     <rect fill="none" height="51" qtmmishow="hidden" stroke="rgb(0,0,0)" width="57" x="-300" y="-1260"/>
    </a>
   <metadata/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e19d90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -290.000000 122.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_337a3c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -301.000000 107.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30bf1b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -276.000000 92.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3374b80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 78.000000 684.000000) translate(0,12)">油温(℃):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cb8e50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 78.000000 699.000000) translate(0,12)">档位(档):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3375890" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 875.000000 689.000000) translate(0,12)">油温(℃):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ab6530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 875.000000 704.000000) translate(0,12)">档位(档):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3132260" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 191.000000 1328.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3132520" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 180.000000 1313.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ad9560" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 205.000000 1298.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ad9970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 539.000000 1332.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ad9c00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 528.000000 1317.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b01500" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 553.000000 1302.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b01910" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 106.000000 886.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b01bd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 95.000000 871.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39fd2e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 120.000000 856.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39fd710" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 900.000000 888.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39fd9a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 889.000000 873.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36c8970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 914.000000 858.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36c8d90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 903.000000 581.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36c9050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 892.000000 566.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ab6bd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 917.000000 551.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ab6ff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 106.000000 572.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a536b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 95.000000 557.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a538f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 120.000000 542.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a53c20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -201.000000 1032.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3106220" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -185.000000 1016.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35b9130" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -193.000000 1047.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39fc720" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -193.000000 1062.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39fc9a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -193.000000 1076.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31a97a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -360.000000 549.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31a9a10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -344.000000 533.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31a9c50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -352.000000 564.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31a9e90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -352.000000 579.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a07e80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -352.000000 593.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a08190" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1240.000000 537.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a08400" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1256.000000 521.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a08640" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1248.000000 552.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2de04e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1248.000000 567.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2de06f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1248.000000 581.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3373fd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 450.000000 700.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_253e720" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 439.000000 685.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_253e960" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 464.000000 670.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ada200" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 848.000000 1330.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ada4c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 837.000000 1315.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ada700" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 862.000000 1300.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_471f600" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -40.000000 122.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_471fc50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -51.000000 107.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_471fe90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -26.000000 92.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_47202b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 123.000000 122.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4720570" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 112.000000 107.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_47207b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 137.000000 92.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4720bd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 318.000000 120.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4720e90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 307.000000 105.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_47210d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 332.000000 90.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_47214f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 589.000000 123.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_47217b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 578.000000 108.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_47219f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 603.000000 93.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4721e10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 778.000000 123.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_47220d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 767.000000 108.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4722310" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 792.000000 93.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4722730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 961.000000 122.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_47229f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 950.000000 107.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4722c30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 975.000000 92.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4723050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1148.000000 125.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4723310" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1137.000000 110.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4723550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1162.000000 95.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_472b940" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -114.000000 548.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_472bba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -89.000000 533.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_472c4a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1120.000000 614.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_472c6d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1145.000000 599.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-YM_WM.076Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -238.000000 -173.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34205" ObjectName="EC-YM_WM.076Ld"/>
    <cge:TPSR_Ref TObjectID="34205"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YM_WM.072Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 -186.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34206" ObjectName="EC-YM_WM.072Ld"/>
    <cge:TPSR_Ref TObjectID="34206"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YM_WM.073Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 162.000000 -182.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34207" ObjectName="EC-YM_WM.073Ld"/>
    <cge:TPSR_Ref TObjectID="34207"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YM_WM.074Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 358.000000 -174.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34208" ObjectName="EC-YM_WM.074Ld"/>
    <cge:TPSR_Ref TObjectID="34208"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YM_WM.081Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 632.000000 -174.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34209" ObjectName="EC-YM_WM.081Ld"/>
    <cge:TPSR_Ref TObjectID="34209"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YM_WM.082Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 821.000000 -174.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34210" ObjectName="EC-YM_WM.082Ld"/>
    <cge:TPSR_Ref TObjectID="34210"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YM_WM.083Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1004.000000 -173.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34211" ObjectName="EC-YM_WM.083Ld"/>
    <cge:TPSR_Ref TObjectID="34211"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YM_WM.084Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1191.000000 -174.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34215" ObjectName="EC-YM_WM.084Ld"/>
    <cge:TPSR_Ref TObjectID="34215"/></metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-171059" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -237.000000 -120.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="171059" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27308"/>
     <cge:Term_Ref ObjectID="38603"/>
    <cge:TPSR_Ref TObjectID="27308"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-171060" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -237.000000 -120.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="171060" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27308"/>
     <cge:Term_Ref ObjectID="38603"/>
    <cge:TPSR_Ref TObjectID="27308"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-171056" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -237.000000 -120.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="171056" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27308"/>
     <cge:Term_Ref ObjectID="38603"/>
    <cge:TPSR_Ref TObjectID="27308"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-171028" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 15.000000 -120.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="171028" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27279"/>
     <cge:Term_Ref ObjectID="38545"/>
    <cge:TPSR_Ref TObjectID="27279"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-171029" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 15.000000 -120.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="171029" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27279"/>
     <cge:Term_Ref ObjectID="38545"/>
    <cge:TPSR_Ref TObjectID="27279"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-171026" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 15.000000 -120.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="171026" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27279"/>
     <cge:Term_Ref ObjectID="38545"/>
    <cge:TPSR_Ref TObjectID="27279"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-171033" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 176.000000 -120.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="171033" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27284"/>
     <cge:Term_Ref ObjectID="38555"/>
    <cge:TPSR_Ref TObjectID="27284"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-171034" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 176.000000 -120.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="171034" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27284"/>
     <cge:Term_Ref ObjectID="38555"/>
    <cge:TPSR_Ref TObjectID="27284"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-171031" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 176.000000 -120.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="171031" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27284"/>
     <cge:Term_Ref ObjectID="38555"/>
    <cge:TPSR_Ref TObjectID="27284"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-171038" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 369.000000 -119.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="171038" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27288"/>
     <cge:Term_Ref ObjectID="38563"/>
    <cge:TPSR_Ref TObjectID="27288"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-171039" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 369.000000 -119.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="171039" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27288"/>
     <cge:Term_Ref ObjectID="38563"/>
    <cge:TPSR_Ref TObjectID="27288"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-171036" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 369.000000 -119.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="171036" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27288"/>
     <cge:Term_Ref ObjectID="38563"/>
    <cge:TPSR_Ref TObjectID="27288"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-171043" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 641.000000 -122.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="171043" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27293"/>
     <cge:Term_Ref ObjectID="38573"/>
    <cge:TPSR_Ref TObjectID="27293"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-171044" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 641.000000 -122.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="171044" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27293"/>
     <cge:Term_Ref ObjectID="38573"/>
    <cge:TPSR_Ref TObjectID="27293"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-171041" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 641.000000 -122.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="171041" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27293"/>
     <cge:Term_Ref ObjectID="38573"/>
    <cge:TPSR_Ref TObjectID="27293"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-171048" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 831.000000 -122.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="171048" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27298"/>
     <cge:Term_Ref ObjectID="38583"/>
    <cge:TPSR_Ref TObjectID="27298"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-171049" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 831.000000 -122.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="171049" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27298"/>
     <cge:Term_Ref ObjectID="38583"/>
    <cge:TPSR_Ref TObjectID="27298"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-171046" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 831.000000 -122.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="171046" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27298"/>
     <cge:Term_Ref ObjectID="38583"/>
    <cge:TPSR_Ref TObjectID="27298"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-171053" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1012.000000 -121.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="171053" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27303"/>
     <cge:Term_Ref ObjectID="38593"/>
    <cge:TPSR_Ref TObjectID="27303"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-171054" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1012.000000 -121.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="171054" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27303"/>
     <cge:Term_Ref ObjectID="38593"/>
    <cge:TPSR_Ref TObjectID="27303"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-171051" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1012.000000 -121.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="171051" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27303"/>
     <cge:Term_Ref ObjectID="38593"/>
    <cge:TPSR_Ref TObjectID="27303"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-171065" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1201.000000 -123.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="171065" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27313"/>
     <cge:Term_Ref ObjectID="38613"/>
    <cge:TPSR_Ref TObjectID="27313"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-171066" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1201.000000 -123.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="171066" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27313"/>
     <cge:Term_Ref ObjectID="38613"/>
    <cge:TPSR_Ref TObjectID="27313"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-171062" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1201.000000 -123.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="171062" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27313"/>
     <cge:Term_Ref ObjectID="38613"/>
    <cge:TPSR_Ref TObjectID="27313"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-170977" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 161.000000 -887.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="170977" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27267"/>
     <cge:Term_Ref ObjectID="38521"/>
    <cge:TPSR_Ref TObjectID="27267"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-170978" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 161.000000 -887.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="170978" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27267"/>
     <cge:Term_Ref ObjectID="38521"/>
    <cge:TPSR_Ref TObjectID="27267"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-170974" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 161.000000 -887.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="170974" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27267"/>
     <cge:Term_Ref ObjectID="38521"/>
    <cge:TPSR_Ref TObjectID="27267"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-170991" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 956.000000 -890.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="170991" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27273"/>
     <cge:Term_Ref ObjectID="38533"/>
    <cge:TPSR_Ref TObjectID="27273"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-170992" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 956.000000 -890.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="170992" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27273"/>
     <cge:Term_Ref ObjectID="38533"/>
    <cge:TPSR_Ref TObjectID="27273"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-170988" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 956.000000 -890.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="170988" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27273"/>
     <cge:Term_Ref ObjectID="38533"/>
    <cge:TPSR_Ref TObjectID="27273"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-171080" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 508.000000 -698.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="171080" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27331"/>
     <cge:Term_Ref ObjectID="38649"/>
    <cge:TPSR_Ref TObjectID="27331"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-171081" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 508.000000 -698.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="171081" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27331"/>
     <cge:Term_Ref ObjectID="38649"/>
    <cge:TPSR_Ref TObjectID="27331"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-171077" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 508.000000 -698.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="171077" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27331"/>
     <cge:Term_Ref ObjectID="38649"/>
    <cge:TPSR_Ref TObjectID="27331"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-170965" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 243.000000 -1328.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="170965" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27248"/>
     <cge:Term_Ref ObjectID="38483"/>
    <cge:TPSR_Ref TObjectID="27248"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-170966" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 243.000000 -1328.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="170966" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27248"/>
     <cge:Term_Ref ObjectID="38483"/>
    <cge:TPSR_Ref TObjectID="27248"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-170963" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 243.000000 -1328.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="170963" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27248"/>
     <cge:Term_Ref ObjectID="38483"/>
    <cge:TPSR_Ref TObjectID="27248"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-170971" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 600.000000 -1332.000000) translate(0,12)">170971.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="170971" ObjectName="YM_WM.YM_WM_372BK:F"/>
     <cge:PSR_Ref ObjectID="27254"/>
     <cge:Term_Ref ObjectID="38495"/>
    <cge:TPSR_Ref TObjectID="27254"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-170972" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 600.000000 -1332.000000) translate(0,27)">170972.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="170972" ObjectName="YM_WM.YM_WM_372BK:F"/>
     <cge:PSR_Ref ObjectID="27254"/>
     <cge:Term_Ref ObjectID="38495"/>
    <cge:TPSR_Ref TObjectID="27254"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-170968" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 600.000000 -1332.000000) translate(0,42)">170968.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="170968" ObjectName="YM_WM.YM_WM_372BK:F"/>
     <cge:PSR_Ref ObjectID="27254"/>
     <cge:Term_Ref ObjectID="38495"/>
    <cge:TPSR_Ref TObjectID="27254"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-171002" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -132.000000 -1072.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="171002" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27244"/>
     <cge:Term_Ref ObjectID="38478"/>
    <cge:TPSR_Ref TObjectID="27244"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-171003" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -132.000000 -1072.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="171003" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27244"/>
     <cge:Term_Ref ObjectID="38478"/>
    <cge:TPSR_Ref TObjectID="27244"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-171004" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -132.000000 -1072.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="171004" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27244"/>
     <cge:Term_Ref ObjectID="38478"/>
    <cge:TPSR_Ref TObjectID="27244"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-171005" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -132.000000 -1072.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="171005" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27244"/>
     <cge:Term_Ref ObjectID="38478"/>
    <cge:TPSR_Ref TObjectID="27244"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-171009" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -132.000000 -1072.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="171009" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27244"/>
     <cge:Term_Ref ObjectID="38478"/>
    <cge:TPSR_Ref TObjectID="27244"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-171010" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -291.000000 -590.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="171010" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27245"/>
     <cge:Term_Ref ObjectID="38479"/>
    <cge:TPSR_Ref TObjectID="27245"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-171011" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -291.000000 -590.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="171011" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27245"/>
     <cge:Term_Ref ObjectID="38479"/>
    <cge:TPSR_Ref TObjectID="27245"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-171012" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -291.000000 -590.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="171012" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27245"/>
     <cge:Term_Ref ObjectID="38479"/>
    <cge:TPSR_Ref TObjectID="27245"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-171013" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -291.000000 -590.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="171013" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27245"/>
     <cge:Term_Ref ObjectID="38479"/>
    <cge:TPSR_Ref TObjectID="27245"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-171017" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -291.000000 -590.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="171017" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27245"/>
     <cge:Term_Ref ObjectID="38479"/>
    <cge:TPSR_Ref TObjectID="27245"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-171018" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1304.000000 -579.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="171018" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27246"/>
     <cge:Term_Ref ObjectID="38480"/>
    <cge:TPSR_Ref TObjectID="27246"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-171019" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1304.000000 -579.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="171019" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27246"/>
     <cge:Term_Ref ObjectID="38480"/>
    <cge:TPSR_Ref TObjectID="27246"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-171020" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1304.000000 -579.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="171020" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27246"/>
     <cge:Term_Ref ObjectID="38480"/>
    <cge:TPSR_Ref TObjectID="27246"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-171021" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1304.000000 -579.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="171021" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27246"/>
     <cge:Term_Ref ObjectID="38480"/>
    <cge:TPSR_Ref TObjectID="27246"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-171025" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1304.000000 -579.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="171025" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27246"/>
     <cge:Term_Ref ObjectID="38480"/>
    <cge:TPSR_Ref TObjectID="27246"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="2" id="ME-170987" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 156.000000 -698.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="170987" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27336"/>
     <cge:Term_Ref ObjectID="38662"/>
    <cge:TPSR_Ref TObjectID="27336"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-170986" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 156.000000 -698.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="170986" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27336"/>
     <cge:Term_Ref ObjectID="38662"/>
    <cge:TPSR_Ref TObjectID="27336"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="2" id="ME-171594" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 955.000000 -702.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="171594" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27337"/>
     <cge:Term_Ref ObjectID="38666"/>
    <cge:TPSR_Ref TObjectID="27337"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-171000" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 955.000000 -702.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="171000" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27337"/>
     <cge:Term_Ref ObjectID="38666"/>
    <cge:TPSR_Ref TObjectID="27337"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-196753" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 902.000000 -1329.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196753" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30131"/>
     <cge:Term_Ref ObjectID="42815"/>
    <cge:TPSR_Ref TObjectID="30131"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-196754" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 902.000000 -1329.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196754" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30131"/>
     <cge:Term_Ref ObjectID="42815"/>
    <cge:TPSR_Ref TObjectID="30131"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-196750" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 902.000000 -1329.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196750" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30131"/>
     <cge:Term_Ref ObjectID="42815"/>
    <cge:TPSR_Ref TObjectID="30131"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-170983" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 164.000000 -572.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="170983" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27270"/>
     <cge:Term_Ref ObjectID="38527"/>
    <cge:TPSR_Ref TObjectID="27270"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-170984" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 164.000000 -572.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="170984" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27270"/>
     <cge:Term_Ref ObjectID="38527"/>
    <cge:TPSR_Ref TObjectID="27270"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-170980" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 164.000000 -572.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="170980" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27270"/>
     <cge:Term_Ref ObjectID="38527"/>
    <cge:TPSR_Ref TObjectID="27270"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-170997" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 959.000000 -581.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="170997" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27276"/>
     <cge:Term_Ref ObjectID="38539"/>
    <cge:TPSR_Ref TObjectID="27276"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-170998" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 959.000000 -581.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="170998" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27276"/>
     <cge:Term_Ref ObjectID="38539"/>
    <cge:TPSR_Ref TObjectID="27276"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-170994" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 959.000000 -581.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="170994" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27276"/>
     <cge:Term_Ref ObjectID="38539"/>
    <cge:TPSR_Ref TObjectID="27276"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-171070" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -53.000000 -547.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="171070" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27318"/>
     <cge:Term_Ref ObjectID="38623"/>
    <cge:TPSR_Ref TObjectID="27318"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-171068" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -53.000000 -547.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="171068" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27318"/>
     <cge:Term_Ref ObjectID="38623"/>
    <cge:TPSR_Ref TObjectID="27318"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-171075" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1191.000000 -613.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="171075" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27325"/>
     <cge:Term_Ref ObjectID="38637"/>
    <cge:TPSR_Ref TObjectID="27325"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-171072" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1191.000000 -613.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="171072" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27325"/>
     <cge:Term_Ref ObjectID="38637"/>
    <cge:TPSR_Ref TObjectID="27325"/></metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_3a1e7f0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -37.000000 -1191.000000)" xlink:href="#voltageTransformer:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36ee2b0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 139.000000 -1088.000000)" xlink:href="#voltageTransformer:shape56"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_30dd610">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 489.000000 -1087.000000)" xlink:href="#voltageTransformer:shape56"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ce0c00">
    <use class="BV-10KV" transform="matrix(0.882353 -0.000000 0.000000 -0.647887 -49.000000 -330.000000)" xlink:href="#voltageTransformer:shape33"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3a52d40">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 272.000000 -669.000000)" xlink:href="#voltageTransformer:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_30f8180">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 686.000000 -653.000000)" xlink:href="#voltageTransformer:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3abc950">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 812.000000 -1163.000000)" xlink:href="#voltageTransformer:shape56"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_470f600">
    <use class="BV-10KV" transform="matrix(0.882353 -0.000000 0.000000 -0.647887 303.000000 -311.000000)" xlink:href="#voltageTransformer:shape33"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4713d30">
    <use class="BV-10KV" transform="matrix(0.882353 -0.000000 0.000000 -0.647887 766.000000 -307.000000)" xlink:href="#voltageTransformer:shape33"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4718b80">
    <use class="BV-10KV" transform="matrix(0.882353 -0.000000 0.000000 -0.647887 949.000000 -303.000000)" xlink:href="#voltageTransformer:shape33"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="-608" y="-1250"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-655" y="-1269"/></g>
   <g href="35kV物茂变35kV永物T线371间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="249" y="-1085"/></g>
   <g href="35kV物茂变35kV黄物T线372间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="601" y="-1084"/></g>
   <g href="35kV物茂变35kV坡物线373间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="908" y="-1132"/></g>
   <g href="35kV物茂变10kV备用一076间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="-220" y="-436"/></g>
   <g href="35kV物茂变10kV物茂线073间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="179" y="-441"/></g>
   <g href="35kV物茂变10kV土林线074间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="376" y="-444"/></g>
   <g href="35kV物茂变10kV虎溪线081间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="652" y="-434"/></g>
   <g href="35kV物茂变10kV龙潭线082间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="839" y="-437"/></g>
   <g href="35kV物茂变10kV芝麻线083间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1022" y="-436"/></g>
   <g href="35kV物茂变10kV德大线084间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1209" y="-437"/></g>
   <g href="cx_配调_配网接线图35_元谋.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="-452" y="-1233"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="-452" y="-1272"/></g>
   <g href="35kV物茂变10kV凹鲊线072间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="24" y="-446"/></g>
   <g href="35kV物茂变10kVⅠ段电容器075间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="-124" y="-577"/></g>
   <g href="35kV物茂变10kV分段012间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="440" y="-580"/></g>
   <g href="35kV物茂变10kVⅡ段电容器085间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1101" y="-569"/></g>
   <g href="35kV物茂变GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="30" qtmmishow="hidden" width="93" x="-706" y="-883"/></g>
   <g href="35kV物茂变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="52" x="73" y="-730"/></g>
   <g href="35kV物茂变2号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="53" x="749" y="-699"/></g>
   <g href="AVC物茂站.svg" style="fill-opacity:0"><rect height="51" qtmmishow="hidden" stroke="rgb(0,0,0)" width="57" x="-300" y="-1260"/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_3ae2500">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -24.000000 -1119.000000)" xlink:href="#lightningRod:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3ae3db0">
    <use class="BV-35KV" transform="matrix(0.933333 0.000000 0.000000 -0.796610 -62.936700 -1021.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2da5f90">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 40.000000 -786.125000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_31e4ee0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -33.000000 -700.125000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3a97be0">
    <use class="BV-35KV" transform="matrix(0.933333 0.000000 0.000000 -0.796610 104.063300 -735.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_31ca1f0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 484.000000 -868.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_31bdce0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 842.000000 -793.125000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2cdccc0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 842.000000 -613.125000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2530800">
    <use class="BV-35KV" transform="matrix(0.933333 0.000000 0.000000 -0.796610 906.063300 -742.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d94d40">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -234.000000 -276.125000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3378de0">
    <use class="BV-10KV" transform="matrix(0.933333 0.000000 0.000000 -0.796610 -269.936700 -324.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_31cfac0">
    <use class="BV-10KV" transform="matrix(0.933333 0.000000 0.000000 -0.796610 -269.936700 -174.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c7a7b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 10.000000 -309.125000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_373de00">
    <use class="BV-10KV" transform="matrix(0.933333 0.000000 0.000000 -0.796610 -15.936700 -332.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35b7810">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 166.000000 -285.125000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_31d05c0">
    <use class="BV-10KV" transform="matrix(0.933333 0.000000 0.000000 -0.796610 131.063300 -325.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_31d55e0">
    <use class="BV-10KV" transform="matrix(0.933333 0.000000 0.000000 -0.796610 130.063300 -183.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_39fe8d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 362.000000 -284.125000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3a1e2d0">
    <use class="BV-10KV" transform="matrix(0.666667 -0.000000 0.000000 -0.601914 -62.000000 -228.258097)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_31f28d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 636.000000 -277.125000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20ca6d0">
    <use class="BV-10KV" transform="matrix(0.933333 0.000000 0.000000 -0.796610 602.063300 -313.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e3dab0">
    <use class="BV-10KV" transform="matrix(0.933333 0.000000 0.000000 -0.796610 601.063300 -175.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2affc90">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 825.000000 -277.125000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2dd1730">
    <use class="BV-10KV" transform="matrix(0.933333 0.000000 0.000000 -0.796610 790.063300 -171.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_39fefa0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1008.000000 -276.125000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3aafed0">
    <use class="BV-10KV" transform="matrix(0.933333 0.000000 0.000000 -0.796610 972.063300 -171.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b4ab40">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1195.000000 -277.125000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d06c00">
    <use class="BV-10KV" transform="matrix(0.933333 0.000000 0.000000 -0.796610 1160.063300 -175.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3a87930">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -137.000000 -648.125000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3731570">
    <use class="BV-10KV" transform="matrix(0.933333 0.000000 0.000000 -0.796610 -181.936700 -753.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2cb7870">
    <use class="BV-10KV" transform="matrix(0.933333 0.000000 0.000000 -0.796610 -181.936700 -597.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_31ddf30">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1087.000000 -649.125000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_30fb620">
    <use class="BV-10KV" transform="matrix(0.933333 0.000000 0.000000 -0.796610 1042.063300 -809.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4050e80">
    <use class="BV-10KV" transform="matrix(0.933333 0.000000 0.000000 -0.796610 1045.063300 -598.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d60960">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 240.000000 -507.000000)" xlink:href="#lightningRod:shape18"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35a0f20">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 284.000000 -616.000000)" xlink:href="#lightningRod:shape113"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_39ef410">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 654.000000 -505.000000)" xlink:href="#lightningRod:shape18"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36bf490">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 698.000000 -603.000000)" xlink:href="#lightningRod:shape113"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4701a90">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 266.000000 -1210.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4702780">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 620.000000 -1209.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_47041c0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 920.000000 -1225.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4704f30">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 35.000000 -626.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4706270">
    <use class="BV-10KV" transform="matrix(0.933333 0.000000 0.000000 -0.796610 -3.936700 -575.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_47081b0">
    <use class="BV-10KV" transform="matrix(0.933333 0.000000 0.000000 -0.796610 798.063300 -569.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4709e90">
    <use class="BV-35KV" transform="matrix(0.933333 0.000000 0.000000 -0.796610 789.063300 -706.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_470d4a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 30.000000 -181.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4710870">
    <use class="BV-10KV" transform="matrix(0.933333 0.000000 0.000000 -0.796610 337.063300 -313.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4712da0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 326.000000 -171.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4714fa0">
    <use class="BV-10KV" transform="matrix(0.933333 0.000000 0.000000 -0.796610 800.063300 -309.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4717bf0">
    <use class="BV-10KV" transform="matrix(0.933333 0.000000 0.000000 -0.796610 983.063300 -305.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_471b5f0">
    <use class="BV-10KV" transform="matrix(0.933333 0.000000 0.000000 -0.796610 1161.063300 -313.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="YM_WM"/>
</svg>