<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-182" aopId="786686" id="thSvg" product="E8000V2" version="1.0" viewBox="3116 -1199 1739 1204">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="capacitor:shape4">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.484375" x1="31" x2="0" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.484375" x1="31" x2="0" y1="19" y2="19"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="5" x2="5" y1="13" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="2" x2="2" y1="11" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="26" x2="9" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="9" x2="9" y1="18" y2="1"/>
   </symbol>
   <symbol id="earth:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="7" x2="11" y1="30" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="9" x2="9" y1="5" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="0" x2="18" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="6" x2="13" y1="26" y2="26"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="hydroGenerator:shape2">
    <polyline points="12,27 11,28 12,28 12,29 12,30 13,31 13,32 14,33 15,33 15,34 16,34 17,35 18,35 19,35 20,35 21,35 22,34 23,34 24,33 25,33 25,32 26,31 26,30 27,29 27,28 27,28 27,27 " stroke-width="0.06"/>
    <circle cx="27" cy="27" fillStyle="0" r="26.5" stroke-width="0.55102"/>
    <polyline arcFlag="1" points="28,27 28,26 28,25 28,24 29,23 29,22 30,21 30,21 31,20 32,20 33,19 34,19 35,19 36,18 37,19 38,19 39,19 40,20 40,20 41,21 42,21 42,22 43,23 43,24 43,25 44,26 43,27 " stroke-width="0.06"/>
   </symbol>
   <symbol id="lightningRod:shape50">
    <polyline DF8003:Layer="PUBLIC" points="5,39 0,50 11,50 5,39 5,40 5,39 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.222222" x1="5" x2="5" y1="29" y2="33"/>
    <polyline DF8003:Layer="PUBLIC" points="5,25 0,14 11,14 5,25 5,24 5,25 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="59" y2="51"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="7" x2="7" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
   </symbol>
   <symbol id="lightningRod:shape47">
    <circle cx="14" cy="14" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="20" cy="11" fillStyle="0" r="6" stroke-width="0.431185"/>
    <ellipse cx="13" cy="6" fillStyle="0" rx="6.5" ry="6" stroke-width="0.431185"/>
    <circle cx="7" cy="10" fillStyle="0" r="6" stroke-width="0.431185"/>
   </symbol>
   <symbol id="lightningRod:shape55">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="50" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="9" y1="41" y2="41"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="27,39 5,17 5,5 " stroke-width="1"/>
    <rect height="4" stroke-width="1" width="19" x="7" y="26"/>
   </symbol>
   <symbol id="lightningRod:shape39">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.278409" x1="49" x2="49" y1="6" y2="9"/>
    <rect height="8" stroke-width="0.75" width="18" x="11" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="24" x2="22" y1="7" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="22" x2="24" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="4" x2="24" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.351519" x1="29" x2="43" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.351519" x1="43" x2="43" y1="0" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.351519" x1="46" x2="46" y1="4" y2="10"/>
   </symbol>
   <symbol id="lightningRod:shape85">
    <circle cx="8" cy="17" fillStyle="0" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="11" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="11" y1="20" y2="20"/>
    <rect height="27" stroke-width="0.416667" width="14" x="1" y="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="73" y2="25"/>
    <circle cx="8" cy="8" fillStyle="0" r="7.5" stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape44">
    <ellipse cx="11" cy="16" fillStyle="0" rx="10.5" ry="11.5" stroke-width="0.64567"/>
    <ellipse cx="11" cy="29" fillStyle="0" rx="10.5" ry="11.5" stroke-width="0.64567"/>
   </symbol>
   <symbol id="lightningRod:shape125">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="1" y2="1"/>
    <ellipse cx="14" cy="17" fillStyle="0" rx="9" ry="8" stroke-width="0.161079"/>
    <ellipse cx="19" cy="8" fillStyle="0" rx="8.5" ry="8" stroke-width="0.161079"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.161079" x1="14" x2="14" y1="16" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.161079" x1="16" x2="14" y1="21" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.161079" x1="14" x2="11" y1="18" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.161079" x1="8" x2="8" y1="4" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.161079" x1="10" x2="8" y1="9" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.161079" x1="8" x2="5" y1="7" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.161079" x1="20" x2="20" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.161079" x1="25" x2="20" y1="8" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.161079" x1="25" x2="20" y1="8" y2="10"/>
    <ellipse cx="8" cy="8" fillStyle="0" rx="8.5" ry="8" stroke-width="0.161079"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="7" x2="15" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="15" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="7" x2="15" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="15" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="transformer2:shape0_0">
    <circle cx="25" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="17" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="34" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="17" x2="34" y1="18" y2="18"/>
   </symbol>
   <symbol id="transformer2:shape0_1">
    <ellipse cx="25" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="24" y1="58" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="32" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="16" x2="24" y1="74" y2="66"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_28aff50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_28b1060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_28b1b40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_28b26f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_28b3970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_28b4490" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_28b4cf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_28b57b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_21bc630" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_21bc630" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_28b8aa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_28b8aa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_28ba840" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_28ba840" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_28bb820" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_28bd510" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_28be140" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_28befb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_28bf700" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_28c0f80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_28c1be0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_28c24a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_28c2c60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_28c3d40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_28c46c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_28c51b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_28c5b70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_28c7190" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_28c7bb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_28c8d50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_28c99e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_28d7df0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_28cff10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_28d16c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_28cbbd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1214" width="1749" x="3111" y="-1204"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3117" y="-1198"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3117" y="-598"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3125" y="-595"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3117" y="-1078"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4285.000000 -653.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129221">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4351.000000 -500.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23684" ObjectName="SW-CX_NNH.CX_NNH_6131SW"/>
     <cge:Meas_Ref ObjectId="129221"/>
    <cge:TPSR_Ref TObjectID="23684"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-126245">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3880.000000 -495.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23113" ObjectName="SW-CX_NNH.CX_NNH_6011SW"/>
     <cge:Meas_Ref ObjectId="126245"/>
    <cge:TPSR_Ref TObjectID="23113"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-126211">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3894.000000 -944.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23097" ObjectName="SW-CX_NNH.CX_NNH_30117SW"/>
     <cge:Meas_Ref ObjectId="126211"/>
    <cge:TPSR_Ref TObjectID="23097"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-126210">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3881.000000 -971.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23096" ObjectName="SW-CX_NNH.CX_NNH_3011SW"/>
     <cge:Meas_Ref ObjectId="126210"/>
    <cge:TPSR_Ref TObjectID="23096"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-126213">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4077.000000 -951.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23099" ObjectName="SW-CX_NNH.CX_NNH_39017SW"/>
     <cge:Meas_Ref ObjectId="126213"/>
    <cge:TPSR_Ref TObjectID="23099"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-126212">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4114.000000 -960.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23098" ObjectName="SW-CX_NNH.CX_NNH_3901SW"/>
     <cge:Meas_Ref ObjectId="126212"/>
    <cge:TPSR_Ref TObjectID="23098"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129223">
    <use class="BV-400KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4351.000000 -904.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23686" ObjectName="SW-CX_NNH.CX_NNH_K001SW"/>
     <cge:Meas_Ref ObjectId="129223"/>
    <cge:TPSR_Ref TObjectID="23686"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129224">
    <use class="BV-400KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4473.000000 -903.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23687" ObjectName="SW-CX_NNH.CX_NNH_K002SW"/>
     <cge:Meas_Ref ObjectId="129224"/>
    <cge:TPSR_Ref TObjectID="23687"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-126243">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4268.000000 -401.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23111" ObjectName="SW-CX_NNH.CX_NNH_6121SW"/>
     <cge:Meas_Ref ObjectId="126243"/>
    <cge:TPSR_Ref TObjectID="23111"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4412.000000 -278.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129218">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4412.000000 -188.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23681" ObjectName="SW-CX_NNH.CX_NNH_6200SW"/>
     <cge:Meas_Ref ObjectId="129218"/>
    <cge:TPSR_Ref TObjectID="23681"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-126241">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3803.000000 -397.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23109" ObjectName="SW-CX_NNH.CX_NNH_6111SW"/>
     <cge:Meas_Ref ObjectId="126241"/>
    <cge:TPSR_Ref TObjectID="23109"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3947.000000 -274.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129215">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3947.000000 -184.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23678" ObjectName="SW-CX_NNH.CX_NNH_6100SW"/>
     <cge:Meas_Ref ObjectId="129215"/>
    <cge:TPSR_Ref TObjectID="23678"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129216">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3706.000000 -191.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23679" ObjectName="SW-CX_NNH.CX_NNH_6110SW"/>
     <cge:Meas_Ref ObjectId="129216"/>
    <cge:TPSR_Ref TObjectID="23679"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-126246">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4599.000000 -405.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23114" ObjectName="SW-CX_NNH.CX_NNH_6901SW"/>
     <cge:Meas_Ref ObjectId="126246"/>
    <cge:TPSR_Ref TObjectID="23114"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129219">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4171.000000 -195.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23683" ObjectName="SW-CX_NNH.CX_NNH_6120SW"/>
     <cge:Meas_Ref ObjectId="129219"/>
    <cge:TPSR_Ref TObjectID="23683"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-126214">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4144.000000 -1030.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23100" ObjectName="SW-CX_NNH.CX_NNH_39010SW"/>
     <cge:Meas_Ref ObjectId="126214"/>
    <cge:TPSR_Ref TObjectID="23100"/></metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_NNH" endPointId="0" endStationName="PAS_T1" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_nayueTnnh" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3896,-1086 3896,-1116 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="37770" ObjectName="AC-35kV.LN_nayueTnnh"/>
    <cge:TPSR_Ref TObjectID="37770_SS-182"/></metadata>
   <polyline fill="none" opacity="0" points="3896,-1086 3896,-1116 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4341.000000 -733.000000)" xlink:href="#transformer2:shape0_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-400KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4341.000000 -733.000000)" xlink:href="#transformer2:shape0_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_NNH.CX_NNH_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="33444"/>
     </metadata>
     <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3871.000000 -746.000000)" xlink:href="#transformer2:shape0_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3871.000000 -746.000000)" xlink:href="#transformer2:shape0_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="23689" ObjectName="TF-CX_NNH.CX_NNH_1T"/>
    <cge:TPSR_Ref TObjectID="23689"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-400KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4616.000000 -885.000000)" xlink:href="#transformer2:shape0_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4616.000000 -885.000000)" xlink:href="#transformer2:shape0_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="HydroGenerator_Layer">
   <g DF8003:Layer="PUBLIC" id="SM-CX_NNH.P2">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4255.000000 -117.000000)" xlink:href="#hydroGenerator:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43424" ObjectName="SM-CX_NNH.P2"/>
    <cge:TPSR_Ref TObjectID="43424"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_NNH.P1">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3790.000000 -113.000000)" xlink:href="#hydroGenerator:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43423" ObjectName="SM-CX_NNH.P1"/>
    <cge:TPSR_Ref TObjectID="43423"/></metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-6KV" id="g_21c0a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4366,-738 4366,-714 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_232bcb0@0" Pin0InfoVect0LinkObjId="g_232bcb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4366,-738 4366,-714 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_235abc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3896,-831 3896,-881 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="23689@1" ObjectIDZND0="23095@0" Pin0InfoVect0LinkObjId="SW-126209_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2287180_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3896,-831 3896,-881 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_235f3b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3895,-486 3895,-517 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="23107@0" ObjectIDZND0="23113@0" Pin0InfoVect0LinkObjId="SW-126245_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_235f790_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3895,-486 3895,-517 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_235f5a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3895,-553 3895,-582 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="23113@1" ObjectIDZND0="23112@0" Pin0InfoVect0LinkObjId="SW-126244_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-126245_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3895,-553 3895,-582 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_235f790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4366,-522 4366,-486 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="23684@0" ObjectIDZND0="23107@0" Pin0InfoVect0LinkObjId="g_22e7410_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129221_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4366,-522 4366,-486 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_235feb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4366,-580 4366,-558 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_235f980@0" ObjectIDZND0="23684@1" Pin0InfoVect0LinkObjId="SW-129221_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_235f980_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4366,-580 4366,-558 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2360ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4366,-661 4366,-638 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_232bcb0@1" ObjectIDZND0="g_235f980@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_235f980_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_232bcb0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4366,-661 4366,-638 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2360e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4366,-638 4366,-611 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_232bcb0@0" ObjectIDND1="0@x" ObjectIDZND0="g_235f980@1" Pin0InfoVect0LinkObjId="g_235f980_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_232bcb0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4366,-638 4366,-611 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2361080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4366,-638 4343,-638 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_232bcb0@0" ObjectIDND1="g_235f980@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_232bcb0_0" Pin1InfoVect1LinkObjId="g_235f980_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4366,-638 4343,-638 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2361270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4307,-638 4291,-638 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_23600a0@0" Pin0InfoVect0LinkObjId="g_23600a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4307,-638 4291,-638 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22f58a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3896,-959 3872,-959 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="23096@x" ObjectIDND1="23095@0" ObjectIDZND0="23097@0" Pin0InfoVect0LinkObjId="SW-126211_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-126210_0" Pin1InfoVect1LinkObjId="SW-126209_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3896,-959 3872,-959 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22f5a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3836,-959 3820,-959 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23097@1" ObjectIDZND0="g_22f5270@0" Pin0InfoVect0LinkObjId="g_22f5270_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-126211_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3836,-959 3820,-959 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22f6250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3896,-908 3896,-959 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="23095@1" ObjectIDZND0="23097@x" ObjectIDZND1="23096@x" Pin0InfoVect0LinkObjId="SW-126211_0" Pin0InfoVect1LinkObjId="SW-126210_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-126209_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3896,-908 3896,-959 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2230280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3896,-959 3896,-993 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="23097@x" ObjectIDND1="23095@0" ObjectIDZND0="23096@0" Pin0InfoVect0LinkObjId="SW-126210_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-126211_0" Pin1InfoVect1LinkObjId="SW-126209_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3896,-959 3896,-993 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2232440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4162,-883 4162,-897 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2231810@0" ObjectIDZND0="g_2231e50@0" Pin0InfoVect0LinkObjId="g_2231e50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2231810_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4162,-883 4162,-897 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2232630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4162,-928 4162,-952 4129,-952 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2231e50@1" ObjectIDZND0="g_2230e40@0" ObjectIDZND1="23098@x" ObjectIDZND2="23099@x" Pin0InfoVect0LinkObjId="g_2230e40_0" Pin0InfoVect1LinkObjId="SW-126212_0" Pin0InfoVect2LinkObjId="SW-126213_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2231e50_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4162,-928 4162,-952 4129,-952 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2308d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4129,-952 4093,-952 4093,-927 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2231e50@0" ObjectIDND1="23098@x" ObjectIDND2="23099@x" ObjectIDZND0="g_2230e40@0" Pin0InfoVect0LinkObjId="g_2230e40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2231e50_0" Pin1InfoVect1LinkObjId="SW-126212_0" Pin1InfoVect2LinkObjId="SW-126213_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4129,-952 4093,-952 4093,-927 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2308f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4129,-952 4129,-966 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2231e50@0" ObjectIDND1="g_2230e40@0" ObjectIDZND0="23098@x" ObjectIDZND1="23099@x" Pin0InfoVect0LinkObjId="SW-126212_0" Pin0InfoVect1LinkObjId="SW-126213_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2231e50_0" Pin1InfoVect1LinkObjId="g_2230e40_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4129,-952 4129,-966 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2323bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4129,-966 4129,-982 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2231e50@0" ObjectIDND1="g_2230e40@0" ObjectIDND2="23099@x" ObjectIDZND0="23098@0" Pin0InfoVect0LinkObjId="SW-126212_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2231e50_0" Pin1InfoVect1LinkObjId="g_2230e40_0" Pin1InfoVect2LinkObjId="SW-126213_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4129,-966 4129,-982 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2323db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4129,-966 4092,-966 4092,-973 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2231e50@0" ObjectIDND1="g_2230e40@0" ObjectIDND2="23098@x" ObjectIDZND0="23099@0" Pin0InfoVect0LinkObjId="SW-126213_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2231e50_0" Pin1InfoVect1LinkObjId="g_2230e40_0" Pin1InfoVect2LinkObjId="SW-126212_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4129,-966 4092,-966 4092,-973 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2323fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4092,-1009 4092,-1023 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23099@1" ObjectIDZND0="g_2309160@0" Pin0InfoVect0LinkObjId="g_2309160_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-126213_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4092,-1009 4092,-1023 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-400KV" id="g_229bfb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4366,-997 4366,-962 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="23686@1" Pin0InfoVect0LinkObjId="SW-129223_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4366,-997 4366,-962 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-400KV" id="g_229cb90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4366,-926 4366,-896 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="23686@0" ObjectIDZND0="g_229c1a0@0" Pin0InfoVect0LinkObjId="g_229c1a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129223_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4366,-926 4366,-896 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-400KV" id="g_229cd80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4366,-843 4366,-818 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_229c1a0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_229c1a0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4366,-843 4366,-818 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-400KV" id="g_229e610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4585,-801 4641,-801 4641,-890 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_229dc20@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_229dc20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4585,-801 4641,-801 4641,-890 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-400KV" id="g_22ebe20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4488,-997 4488,-961 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="23687@1" Pin0InfoVect0LinkObjId="SW-129224_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4488,-997 4488,-961 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-400KV" id="g_22ec010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4488,-925 4488,-801 4532,-801 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="23687@0" ObjectIDZND0="g_229dc20@1" Pin0InfoVect0LinkObjId="g_229dc20_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129224_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4488,-925 4488,-801 4532,-801 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_226a920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4641,-970 4641,-1001 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_22ec200@1" Pin0InfoVect0LinkObjId="g_22ec200_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4641,-970 4641,-1001 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_226b4e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4641,-1075 4696,-1075 4696,-1047 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_22ec200@0" ObjectIDZND0="g_226ab10@0" Pin0InfoVect0LinkObjId="g_226ab10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22ec200_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4641,-1075 4696,-1075 4696,-1047 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_226bca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4641,-1046 4641,-1075 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_22ec200@0" ObjectIDZND0="g_226ab10@0" Pin0InfoVect0LinkObjId="g_226ab10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22ec200_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4641,-1046 4641,-1075 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_226be90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4641,-1075 4641,-1109 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" ObjectIDND0="g_226ab10@0" ObjectIDND1="g_22ec200@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_226ab10_0" Pin1InfoVect1LinkObjId="g_22ec200_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4641,-1075 4641,-1109 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2199600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3896,-1072 3896,-1086 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="23096@x" ObjectIDND1="23098@x" ObjectIDND2="23100@x" ObjectIDZND0="37770@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-126210_0" Pin1InfoVect1LinkObjId="SW-126212_0" Pin1InfoVect2LinkObjId="SW-126214_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3896,-1072 3896,-1086 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2199dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3896,-1072 3896,-1029 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="powerLine" EndDevType0="switch" ObjectIDND0="23098@x" ObjectIDND1="23100@x" ObjectIDND2="37770@1" ObjectIDZND0="23096@1" Pin0InfoVect0LinkObjId="SW-126210_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-126212_0" Pin1InfoVect1LinkObjId="SW-126214_0" Pin1InfoVect2LinkObjId="g_2199600_1" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3896,-1072 3896,-1029 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2362fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4283,-486 4283,-459 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="23107@0" ObjectIDZND0="23111@1" Pin0InfoVect0LinkObjId="SW-126243_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_235f790_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4283,-486 4283,-459 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_23647a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4283,-423 4283,-395 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="23111@0" ObjectIDZND0="23110@1" Pin0InfoVect0LinkObjId="SW-126242_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-126243_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4283,-423 4283,-395 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_23649c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4283,-345 4329,-345 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="23110@x" ObjectIDND1="0@x" ObjectIDND2="23681@x" ObjectIDZND0="g_2364e00@0" Pin0InfoVect0LinkObjId="g_2364e00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-126242_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-129218_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4283,-345 4329,-345 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2364be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4283,-368 4283,-345 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="23110@0" ObjectIDZND0="0@x" ObjectIDZND1="23681@x" ObjectIDZND2="23683@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-129218_0" Pin0InfoVect2LinkObjId="SW-129219_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-126242_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4283,-368 4283,-345 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_21f2510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4283,-275 4427,-275 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="23110@x" ObjectIDND1="g_2364e00@0" ObjectIDND2="23683@x" ObjectIDZND0="0@x" ObjectIDZND1="23681@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-129218_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-126242_0" Pin1InfoVect1LinkObjId="g_2364e00_0" Pin1InfoVect2LinkObjId="SW-129219_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4283,-275 4427,-275 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_21f2730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4283,-345 4283,-275 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="23110@x" ObjectIDND1="g_2364e00@0" ObjectIDZND0="0@x" ObjectIDZND1="23681@x" ObjectIDZND2="23683@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-129218_0" Pin0InfoVect2LinkObjId="SW-129219_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-126242_0" Pin1InfoVect1LinkObjId="g_2364e00_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4283,-345 4283,-275 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_21f2950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4283,-275 4283,-170 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="hydroGenerator" ObjectIDND0="23110@x" ObjectIDND1="g_2364e00@0" ObjectIDND2="0@x" ObjectIDZND0="43424@0" Pin0InfoVect0LinkObjId="SM-CX_NNH.P2_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-126242_0" Pin1InfoVect1LinkObjId="g_2364e00_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4283,-275 4283,-170 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_21f4df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4427,-372 4427,-340 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2333b30@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2333b30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4427,-372 4427,-340 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_22ced20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4427,-300 4427,-275 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="23110@x" ObjectIDZND1="g_2364e00@0" ObjectIDZND2="23683@x" Pin0InfoVect0LinkObjId="SW-126242_0" Pin0InfoVect1LinkObjId="g_2364e00_0" Pin0InfoVect2LinkObjId="SW-129219_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4427,-300 4427,-275 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_22d1180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4427,-275 4427,-246 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="23110@x" ObjectIDND2="g_2364e00@0" ObjectIDZND0="23681@1" Pin0InfoVect0LinkObjId="SW-129218_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-126242_0" Pin1InfoVect2LinkObjId="g_2364e00_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4427,-275 4427,-246 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_22d13a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4427,-210 4427,-174 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="23681@0" ObjectIDZND0="g_22d15c0@0" Pin0InfoVect0LinkObjId="g_22d15c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129218_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4427,-210 4427,-174 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2335270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4186,-384 4186,-358 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="capacitor" ObjectIDND0="g_2335490@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2335490_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4186,-384 4186,-358 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_229ac70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3818,-419 3818,-391 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="23109@0" ObjectIDZND0="23108@1" Pin0InfoVect0LinkObjId="SW-126240_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-126241_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3818,-419 3818,-391 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_22fd340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3962,-368 3962,-336 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_227ffb0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_227ffb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3962,-368 3962,-336 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_22ff830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3962,-206 3962,-170 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="23678@0" ObjectIDZND0="g_22ffa50@0" Pin0InfoVect0LinkObjId="g_22ffa50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129215_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3962,-206 3962,-170 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2281670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3721,-166 3721,-139 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2280ed0@0" ObjectIDZND0="g_2281890@0" Pin0InfoVect0LinkObjId="g_2281890_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2280ed0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3721,-166 3721,-139 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_22d56c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3721,-380 3721,-354 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="capacitor" ObjectIDND0="g_22d58e0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22d58e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3721,-380 3721,-354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_22d8520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3721,-213 3721,-197 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="23679@0" ObjectIDZND0="g_2280ed0@1" Pin0InfoVect0LinkObjId="g_2280ed0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129216_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3721,-213 3721,-197 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_22ad510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4614,-486 4614,-463 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="23107@0" ObjectIDZND0="23114@1" Pin0InfoVect0LinkObjId="SW-126246_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_235f790_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4614,-486 4614,-463 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_23176a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4186,-170 4186,-143 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_22adce0@0" ObjectIDZND0="g_2317890@0" Pin0InfoVect0LinkObjId="g_2317890_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22adce0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4186,-170 4186,-143 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2242440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4186,-217 4186,-201 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="23683@0" ObjectIDZND0="g_22adce0@1" Pin0InfoVect0LinkObjId="g_22adce0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129219_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4186,-217 4186,-201 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2242660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4614,-398 4660,-398 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="23114@x" ObjectIDND1="g_22faaf0@0" ObjectIDZND0="g_2242880@0" Pin0InfoVect0LinkObjId="g_2242880_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-126246_0" Pin1InfoVect1LinkObjId="g_22faaf0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4614,-398 4660,-398 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2243f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4614,-428 4614,-398 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="23114@0" ObjectIDZND0="g_2242880@0" ObjectIDZND1="g_22faaf0@0" Pin0InfoVect0LinkObjId="g_2242880_0" Pin0InfoVect1LinkObjId="g_22faaf0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-126246_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4614,-428 4614,-398 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21b6a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4129,-1018 4129,-1035 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="powerLine" EndDevType2="switch" ObjectIDND0="23098@1" ObjectIDZND0="23096@x" ObjectIDZND1="37770@1" ObjectIDZND2="23100@x" Pin0InfoVect0LinkObjId="SW-126210_0" Pin0InfoVect1LinkObjId="g_2199600_1" Pin0InfoVect2LinkObjId="SW-126214_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-126212_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4129,-1018 4129,-1035 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21b6ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4129,-1035 4129,-1072 3896,-1072 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="powerLine" ObjectIDND0="23098@x" ObjectIDND1="23100@x" ObjectIDZND0="23096@x" ObjectIDZND1="37770@1" Pin0InfoVect0LinkObjId="SW-126210_0" Pin0InfoVect1LinkObjId="g_2199600_1" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-126212_0" Pin1InfoVect1LinkObjId="SW-126214_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4129,-1035 4129,-1072 3896,-1072 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21b6f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4209,-1035 4185,-1035 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_21b5920@0" ObjectIDZND0="23100@1" Pin0InfoVect0LinkObjId="SW-126214_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21b5920_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4209,-1035 4185,-1035 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21b7160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4149,-1035 4129,-1035 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="powerLine" ObjectIDND0="23100@0" ObjectIDZND0="23098@x" ObjectIDZND1="23096@x" ObjectIDZND2="37770@1" Pin0InfoVect0LinkObjId="SW-126212_0" Pin0InfoVect1LinkObjId="SW-126210_0" Pin0InfoVect2LinkObjId="g_2199600_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-126214_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4149,-1035 4129,-1035 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_22e5e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3962,-296 3962,-274 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="breaker" ObjectIDND0="0@0" ObjectIDZND0="23678@x" ObjectIDZND1="g_229ae90@0" ObjectIDZND2="23108@x" Pin0InfoVect0LinkObjId="SW-129215_0" Pin0InfoVect1LinkObjId="g_229ae90_0" Pin0InfoVect2LinkObjId="SW-126240_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3962,-296 3962,-274 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_22e6080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3962,-274 3962,-242 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="breaker" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_229ae90@0" ObjectIDND2="23108@x" ObjectIDZND0="23678@1" Pin0InfoVect0LinkObjId="SW-129215_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_229ae90_0" Pin1InfoVect2LinkObjId="SW-126240_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3962,-274 3962,-242 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_22e6ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3818,-274 3962,-274 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_229ae90@0" ObjectIDND1="23108@x" ObjectIDND2="23679@x" ObjectIDZND0="0@x" ObjectIDZND1="23678@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-129215_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_229ae90_0" Pin1InfoVect1LinkObjId="SW-126240_0" Pin1InfoVect2LinkObjId="SW-129216_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3818,-274 3962,-274 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_22e7410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3818,-455 3818,-486 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="23109@1" ObjectIDZND0="23107@0" Pin0InfoVect0LinkObjId="g_235f790_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-126241_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3818,-455 3818,-486 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_22e7af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3818,-166 3818,-272 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="hydroGenerator" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="43423@0" ObjectIDZND0="0@x" ObjectIDZND1="23678@x" ObjectIDZND2="g_229ae90@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-129215_0" Pin0InfoVect2LinkObjId="g_229ae90_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-CX_NNH.P1_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3818,-166 3818,-272 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_22e7d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3864,-341 3818,-341 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_229ae90@0" ObjectIDZND0="23108@x" ObjectIDZND1="0@x" ObjectIDZND2="23678@x" Pin0InfoVect0LinkObjId="SW-126240_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-129215_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_229ae90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3864,-341 3818,-341 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_227acc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3818,-364 3818,-341 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="23108@0" ObjectIDZND0="g_229ae90@0" ObjectIDZND1="0@x" ObjectIDZND2="23678@x" Pin0InfoVect0LinkObjId="g_229ae90_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-129215_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-126240_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3818,-364 3818,-341 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_227af20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3818,-341 3818,-272 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_229ae90@0" ObjectIDND1="23108@x" ObjectIDZND0="0@x" ObjectIDZND1="23678@x" ObjectIDZND2="23679@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-129215_0" Pin0InfoVect2LinkObjId="SW-129216_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_229ae90_0" Pin1InfoVect1LinkObjId="SW-126240_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3818,-341 3818,-272 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_227b9f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3721,-249 3721,-274 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="23679@1" ObjectIDZND0="0@x" ObjectIDZND1="23678@x" ObjectIDZND2="g_229ae90@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-129215_0" Pin0InfoVect2LinkObjId="g_229ae90_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129216_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3721,-249 3721,-274 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_227bc50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3721,-274 3818,-274 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="23679@x" ObjectIDND1="g_2280730@0" ObjectIDZND0="0@x" ObjectIDZND1="23678@x" ObjectIDZND2="g_229ae90@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-129215_0" Pin0InfoVect2LinkObjId="g_229ae90_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-129216_0" Pin1InfoVect1LinkObjId="g_2280730_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3721,-274 3818,-274 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_227beb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3721,-339 3721,-322 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_2280730@1" Pin0InfoVect0LinkObjId="g_2280730_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3721,-339 3721,-322 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_227c110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3721,-291 3721,-274 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2280730@0" ObjectIDZND0="23679@x" ObjectIDZND1="0@x" ObjectIDZND2="23678@x" Pin0InfoVect0LinkObjId="SW-129216_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-129215_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2280730_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3721,-291 3721,-274 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_227cac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4283,-275 4186,-275 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="23110@x" ObjectIDND1="g_2364e00@0" ObjectIDND2="0@x" ObjectIDZND0="23683@x" ObjectIDZND1="g_23342e0@0" Pin0InfoVect0LinkObjId="SW-129219_0" Pin0InfoVect1LinkObjId="g_23342e0_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-126242_0" Pin1InfoVect1LinkObjId="g_2364e00_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4283,-275 4186,-275 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_227cd20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4186,-275 4186,-253 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="23110@x" ObjectIDND1="g_2364e00@0" ObjectIDND2="0@x" ObjectIDZND0="23683@1" Pin0InfoVect0LinkObjId="SW-129219_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-126242_0" Pin1InfoVect1LinkObjId="g_2364e00_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4186,-275 4186,-253 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_227cf80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4186,-275 4186,-295 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="23110@x" ObjectIDND1="g_2364e00@0" ObjectIDND2="0@x" ObjectIDZND0="g_23342e0@0" Pin0InfoVect0LinkObjId="g_23342e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-126242_0" Pin1InfoVect1LinkObjId="g_2364e00_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4186,-275 4186,-295 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_227d1e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4186,-326 4186,-343 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="capacitor" ObjectIDND0="g_23342e0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_23342e0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4186,-326 4186,-343 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_227d440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4614,-398 4614,-378 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2242880@0" ObjectIDND1="23114@x" ObjectIDZND0="g_22faaf0@1" Pin0InfoVect0LinkObjId="g_22faaf0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2242880_0" Pin1InfoVect1LinkObjId="SW-126246_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4614,-398 4614,-378 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_227d6a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4614,-347 4614,-322 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_22faaf0@0" ObjectIDZND0="g_22fb290@0" Pin0InfoVect0LinkObjId="g_22fb290_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22faaf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4614,-347 4614,-322 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_227d900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3895,-609 3895,-635 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="23112@1" ObjectIDZND0="g_2230470@0" ObjectIDZND1="g_1fda640@0" Pin0InfoVect0LinkObjId="g_2230470_0" Pin0InfoVect1LinkObjId="g_1fda640_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-126244_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3895,-609 3895,-635 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2286cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3932,-635 3895,-635 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="lightningRod" ObjectIDND0="g_2230470@0" ObjectIDZND0="23112@x" ObjectIDZND1="g_1fda640@0" Pin0InfoVect0LinkObjId="SW-126244_0" Pin0InfoVect1LinkObjId="g_1fda640_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2230470_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3932,-635 3895,-635 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2286f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3895,-635 3895,-672 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="23112@x" ObjectIDND1="g_2230470@0" ObjectIDZND0="g_1fda640@1" Pin0InfoVect0LinkObjId="g_1fda640_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-126244_0" Pin1InfoVect1LinkObjId="g_2230470_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3895,-635 3895,-672 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2287180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3895,-725 3895,-751 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_1fda640@0" ObjectIDZND0="23689@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fda640_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3895,-725 3895,-751 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="23107" cx="3895" cy="-486" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23107" cx="4366" cy="-486" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4366" cy="-997" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4488" cy="-997" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23107" cx="4283" cy="-486" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23107" cx="4614" cy="-486" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23107" cx="3818" cy="-486" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-126194" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3401.000000 -1061.000000)" xlink:href="#dynamicPoint:shape33"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23092" ObjectName="DYN-CX_NNH"/>
     <cge:Meas_Ref ObjectId="126194"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2340220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -577.000000) translate(0,17)">危险点说明:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2340220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -577.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2340220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -577.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2340220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -577.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2340220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -577.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2340220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -577.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2340220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -577.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2340220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -577.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2340220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -577.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2340220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -577.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2340220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -577.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2340220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -577.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2340220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -577.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2340220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -577.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2340220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -577.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2340220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -577.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2340220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -577.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2340220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -577.000000) translate(0,374)">联系方式:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1fe1e40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3130.000000 -1056.000000) translate(0,17)">频率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1fe1e40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3130.000000 -1056.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1fe1e40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3130.000000 -1056.000000) translate(0,59)">全站有功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1fe1e40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3130.000000 -1056.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1fe1e40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3130.000000 -1056.000000) translate(0,101)">全站无功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1fe1e40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3130.000000 -1056.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1fe1e40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3130.000000 -1056.000000) translate(0,143)">并网联络点的电压和交换功率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_208fe50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3280.000000 -1168.500000) translate(0,16)">纳嫩河电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2232820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3829.000000 -1141.000000) translate(0,15)">至鱼庄河电站35kV纳鱼鄂线372断路器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22930d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3862.000000 -1091.000000) translate(0,15)">35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22930d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3862.000000 -1091.000000) translate(0,33)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22930d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3862.000000 -1091.000000) translate(0,51)">纳</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22930d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3862.000000 -1091.000000) translate(0,69)">鱼</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22930d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3862.000000 -1091.000000) translate(0,87)">鄂</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22930d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3862.000000 -1091.000000) translate(0,105)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_23129c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3909.000000 -1015.000000) translate(0,15)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_23310f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3823.000000 -940.000000) translate(0,15)">30117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2331350" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3850.000000 -898.000000) translate(0,15)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22f18e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3909.000000 -604.000000) translate(0,15)">601</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22f1b20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3907.000000 -540.000000) translate(0,15)">6011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22f1da0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4381.000000 -544.000000) translate(0,15)">6131</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22f1fe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4308.000000 -668.000000) translate(0,15)">61317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22f2220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3828.000000 -447.000000) translate(0,15)">6111</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_232def0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3837.000000 -386.000000) translate(0,15)">611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_232e100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3977.000000 -322.000000) translate(0,15)">6210</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_232e340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3977.000000 -227.000000) translate(0,15)">6100</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_232e580" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3732.000000 -238.000000) translate(0,15)">6110</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_232e7c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4298.000000 -445.000000) translate(0,15)">6121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_232ea00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4307.000000 -391.000000) translate(0,15)">612</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_232ec40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4201.000000 -241.000000) translate(0,15)">6120</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_232ee80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4436.000000 -322.000000) translate(0,15)">6220</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_232f0c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4442.000000 -236.000000) translate(0,15)">6200</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_232f300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3854.000000 -72.000000) translate(0,15)">1号发电机参数:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_232f300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3854.000000 -72.000000) translate(0,33)">SFWE-K2500-10/1730</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_232f300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3854.000000 -72.000000) translate(0,51)">Ie=286.4A,cos=0.8</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_232f6a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4247.000000 -103.000000) translate(0,15)">2号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_232f8e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4578.000000 -282.000000) translate(0,15)">6kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_232fc40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4625.000000 -450.000000) translate(0,15)">6901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_232fe60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4021.000000 -1004.000000) translate(0,15)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2330080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4135.000000 -1010.000000) translate(0,15)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_23302c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4155.000000 -1064.000000) translate(0,15)">39010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2330500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4385.000000 -949.000000) translate(0,15)">K001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2330740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4501.000000 -946.000000) translate(0,15)">K002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2330980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4383.000000 -1024.000000) translate(0,15)">400V厂用电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21b4ee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4596.000000 -1131.000000) translate(0,15)">至10kV施工线路</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" graphid="g_22e5640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3251.000000 -226.000000) translate(0,16)">7818768</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22e6f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3570.000000 -473.000000) translate(0,12)">IM段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22873e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3942.000000 -801.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2287a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4022.000000 -809.000000) translate(0,12)">1号主变参数:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2287a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4022.000000 -809.000000) translate(0,27)">S9-6300/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2287a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4022.000000 -809.000000) translate(0,42)">YN,d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2288710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4344.000000 -69.000000) translate(0,15)">1号发电机参数:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2288710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4344.000000 -69.000000) translate(0,33)">SFWE-K2500-10/1730</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2288710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4344.000000 -69.000000) translate(0,51)">Ie=286.4A,cos=0.8</text>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-126244">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3886.000000 -574.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23112" ObjectName="SW-CX_NNH.CX_NNH_601BK"/>
     <cge:Meas_Ref ObjectId="126244"/>
    <cge:TPSR_Ref TObjectID="23112"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-126209">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3887.000000 -873.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23095" ObjectName="SW-CX_NNH.CX_NNH_301BK"/>
     <cge:Meas_Ref ObjectId="126209"/>
    <cge:TPSR_Ref TObjectID="23095"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-126242">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4274.000000 -360.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23110" ObjectName="SW-CX_NNH.CX_NNH_612BK"/>
     <cge:Meas_Ref ObjectId="126242"/>
    <cge:TPSR_Ref TObjectID="23110"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-126240">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3809.000000 -356.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23108" ObjectName="SW-CX_NNH.CX_NNH_611BK"/>
     <cge:Meas_Ref ObjectId="126240"/>
    <cge:TPSR_Ref TObjectID="23108"/></metadata>
   </g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4171.000000 -339.000000)" xlink:href="#capacitor:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3706.000000 -335.000000)" xlink:href="#capacitor:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1fda640">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3890.000000 -667.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_232bcb0">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4361.000000 -656.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_235f980">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4357.000000 -575.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2230470">
    <use class="BV-6KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3986.500000 -628.500000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2230e40">
    <use class="BV-35KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4086.000000 -873.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2231810">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4148.000000 -863.000000)" xlink:href="#lightningRod:shape47"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2231e50">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4153.000000 -892.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_229c1a0">
    <use class="BV-400KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4361.000000 -838.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_229dc20">
    <use class="BV-400KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4527.000000 -806.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22ec200">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4635.000000 -996.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_226ab10">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4689.000000 -993.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2364e00">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4324.000000 -339.000000)" xlink:href="#lightningRod:shape39"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22d15c0">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4419.000000 -101.000000)" xlink:href="#lightningRod:shape85"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2333b30">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4415.000000 -367.000000)" xlink:href="#lightningRod:shape44"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23342e0">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4177.000000 -290.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_229ae90">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3859.000000 -335.000000)" xlink:href="#lightningRod:shape39"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22ffa50">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3954.000000 -97.000000)" xlink:href="#lightningRod:shape85"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_227ffb0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3950.000000 -363.000000)" xlink:href="#lightningRod:shape44"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2280730">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3712.000000 -286.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2280ed0">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3712.000000 -161.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2281890">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3708.000000 -115.000000)" xlink:href="#lightningRod:shape125"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22faaf0">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4605.000000 -342.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22fb290">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4601.000000 -299.000000)" xlink:href="#lightningRod:shape125"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22adce0">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4177.000000 -165.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2317890">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4173.000000 -119.000000)" xlink:href="#lightningRod:shape125"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2242880">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4655.000000 -392.000000)" xlink:href="#lightningRod:shape39"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-126207" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3993.000000 -904.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="126207" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23095"/>
     <cge:Term_Ref ObjectID="32545"/>
    <cge:TPSR_Ref TObjectID="23095"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-126208" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3993.000000 -904.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="126208" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23095"/>
     <cge:Term_Ref ObjectID="32545"/>
    <cge:TPSR_Ref TObjectID="23095"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-126204" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3993.000000 -904.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="126204" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23095"/>
     <cge:Term_Ref ObjectID="32545"/>
    <cge:TPSR_Ref TObjectID="23095"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-126226" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3784.000000 -69.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="126226" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23108"/>
     <cge:Term_Ref ObjectID="32558"/>
    <cge:TPSR_Ref TObjectID="23108"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-126227" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3784.000000 -69.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="126227" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23108"/>
     <cge:Term_Ref ObjectID="32558"/>
    <cge:TPSR_Ref TObjectID="23108"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-126223" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3784.000000 -69.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="126223" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23108"/>
     <cge:Term_Ref ObjectID="32558"/>
    <cge:TPSR_Ref TObjectID="23108"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-126233" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4255.000000 -73.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="126233" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23110"/>
     <cge:Term_Ref ObjectID="32562"/>
    <cge:TPSR_Ref TObjectID="23110"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-126234" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4255.000000 -73.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="126234" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23110"/>
     <cge:Term_Ref ObjectID="32562"/>
    <cge:TPSR_Ref TObjectID="23110"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-126230" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4255.000000 -73.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="126230" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23110"/>
     <cge:Term_Ref ObjectID="32562"/>
    <cge:TPSR_Ref TObjectID="23110"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-126236" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3605.000000 -589.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="126236" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23107"/>
     <cge:Term_Ref ObjectID="32557"/>
    <cge:TPSR_Ref TObjectID="23107"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-126237" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3605.000000 -589.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="126237" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23107"/>
     <cge:Term_Ref ObjectID="32557"/>
    <cge:TPSR_Ref TObjectID="23107"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-126238" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3605.000000 -589.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="126238" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23107"/>
     <cge:Term_Ref ObjectID="32557"/>
    <cge:TPSR_Ref TObjectID="23107"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-126235" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3605.000000 -589.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="126235" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23107"/>
     <cge:Term_Ref ObjectID="32557"/>
    <cge:TPSR_Ref TObjectID="23107"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-126239" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3605.000000 -589.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="126239" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23107"/>
     <cge:Term_Ref ObjectID="32557"/>
    <cge:TPSR_Ref TObjectID="23107"/></metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_地调直调.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="3247" y="-1179"/></g>
   <g href="cx_索引_接线图_地调直调.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3198" y="-1196"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21b74b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3935.000000 905.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21b79c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3924.000000 890.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21b7bb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3949.000000 875.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21b7da0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3724.000000 70.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21b7f50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3713.000000 55.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21b8100" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3738.000000 40.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21b83a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4196.000000 74.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22e4600" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4185.000000 59.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22e4810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4210.000000 44.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2266d60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3542.000000 576.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22678f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3542.000000 562.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2267eb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3533.000000 543.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2268130" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3542.000000 591.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2268370" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3549.000000 527.000000) translate(0,12)">F(Hz):</text>
   <metadata/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_23600a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4266.000000 -630.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22f5270" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3795.000000 -951.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2309160" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4083.000000 -1018.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2335490" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4177.000000 -379.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22d58e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3712.000000 -375.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21b5920" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4205.000000 -1029.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="3247" y="-1179"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="3247" y="-1179"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3198" y="-1196"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3198" y="-1196"/></g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_NNH.CX_NNH_6IM">
    <g class="BV-6KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3589,-486 4855,-486 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="23107" ObjectName="BS-CX_NNH.CX_NNH_6IM"/>
    <cge:TPSR_Ref TObjectID="23107"/></metadata>
   <polyline fill="none" opacity="0" points="3589,-486 4855,-486 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4295,-997 4543,-997 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4295,-997 4543,-997 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3213.000000 -1099.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-223321" ratioFlag="0">
    <text fill="rgb(0,238,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3254.000000 -1007.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="223321" ObjectName="CX_NNH:CX_NNH_FDJS_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-223322" ratioFlag="0">
    <text fill="rgb(0,205,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3254.000000 -972.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="223322" ObjectName="CX_NNH:CX_NNH_FDJS_sumQ"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_NNH"/>
</svg>