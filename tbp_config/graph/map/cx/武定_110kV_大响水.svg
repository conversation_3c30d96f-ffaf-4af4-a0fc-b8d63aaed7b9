<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-153" aopId="803070" id="thSvg" product="E8000V2" version="1.0" viewBox="3115 -1279 2083 1396">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="capacitor:shape11">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.974359" x1="16" x2="92" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="92" x2="92" y1="22" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="52" x2="52" y1="22" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="22" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.484375" x1="107" x2="76" y1="37" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.484375" x1="107" x2="76" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.484375" x1="31" x2="0" y1="37" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.484375" x1="31" x2="0" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.484375" x1="68" x2="36" y1="37" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.484375" x1="68" x2="37" y1="22" y2="22"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="0" x2="12" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="hydroGenerator:shape3">
    <polyline arcFlag="1" points="25,25 25,26 25,27 25,28 24,29 24,30 23,31 23,31 22,32 21,32 20,33 19,33 18,33 17,34 16,33 15,33 14,33 13,32 13,32 12,31 11,31 11,30 10,29 10,28 10,27 9,26 10,25 " stroke-width="1.14"/>
    <circle cx="24" cy="24" fillStyle="0" r="24" stroke-width="0.5"/>
    <polyline points="40,25 41,24 40,24 40,23 40,22 39,21 39,20 38,19 37,19 37,18 36,18 35,17 34,17 33,17 32,17 31,17 30,18 29,18 28,19 27,19 27,20 26,21 26,22 25,23 25,24 25,24 25,25 " stroke-width="1.14"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="55" x2="55" y1="12" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="54" x2="46" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="59" x2="59" y1="3" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="62" x2="62" y1="5" y2="8"/>
    <rect height="12" stroke-width="1" width="26" x="19" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="39" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape54">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="6" x2="6" y1="58" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="6" x2="6" y1="8" y2="37"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="8" x2="5" y1="1" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="10" x2="3" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="8" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="1" x2="12" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape190">
    <ellipse cx="24" cy="20" rx="7.5" ry="7" stroke-width="0.726474"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="28" x2="25" y1="21" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="22" x2="25" y1="21" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="25" x2="25" y1="19" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="33" x2="39" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="35" x2="33" y1="29" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="36" x2="39" y1="29" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="40" x2="37" y1="22" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="34" x2="37" y1="22" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="37" x2="37" y1="20" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="28" x2="25" y1="34" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="25" x2="25" y1="32" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="22" x2="25" y1="34" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.05263" x1="13" x2="5" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.125" x1="10" x2="8" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.05263" x1="12" x2="6" y1="4" y2="4"/>
    <ellipse cx="35" cy="30" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <ellipse cx="24" cy="30" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <ellipse cx="35" cy="20" rx="7.5" ry="7" stroke-width="0.726474"/>
    <rect height="9" stroke-width="1" width="5" x="7" y="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="0" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="15" y1="12" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="19" y2="19"/>
    <polyline points="9,7 9,10 " stroke-width="0.676923"/>
    <polyline points="10,19 10,31 25,31 " stroke-width="0.676923"/>
   </symbol>
   <symbol id="lightningRod:shape186">
    <circle cx="15" cy="80" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="41" x2="39" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="43" x2="37" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="34" x2="46" y1="30" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="59" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="19" y1="57" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="57" y2="53"/>
    <circle cx="15" cy="58" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="43" y2="1"/>
    <polyline DF8003:Layer="PUBLIC" points="15,16 21,28 9,28 15,16 15,16 15,16 "/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,59 40,59 40,30 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="15" y1="53" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="10" y1="87" y2="81"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="18" y1="81" y2="81"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="14" y1="81" y2="87"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="60" x2="21" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="15" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="6" x2="6" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="4" x2="4" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="1" x2="1" y1="6" y2="9"/>
    <rect height="13" stroke-width="0.424575" width="29" x="15" y="1"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="13" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="9" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="transformer:shape3_0">
    <circle cx="57" cy="31" fillStyle="0" r="25" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="63" x2="56" y1="29" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="71" x2="63" y1="36" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="63" x2="63" y1="20" y2="29"/>
   </symbol>
   <symbol id="transformer:shape3_1">
    <circle cx="26" cy="30" fillStyle="0" r="25" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="11" x2="26" y1="28" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="11" x2="26" y1="28" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="26" x2="26" y1="19" y2="36"/>
   </symbol>
   <symbol id="transformer:shape3-2">
    <circle cx="41" cy="60" fillStyle="0" r="25" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="40" x2="33" y1="67" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="48" x2="40" y1="74" y2="67"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="40" x2="40" y1="58" y2="67"/>
   </symbol>
   <symbol id="transformer2:shape2_0">
    <ellipse cx="13" cy="34" fillStyle="0" rx="13" ry="12.5" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="32" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="36" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="40" y2="36"/>
   </symbol>
   <symbol id="transformer2:shape2_1">
    <circle cx="13" cy="18" fillStyle="0" r="13" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="18" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="9" y1="20" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="18" y1="20" y2="12"/>
   </symbol>
   <symbol id="voltageTransformer:shape21">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="28" x2="28" y1="11" y2="5"/>
    <circle cx="15" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="26" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="13" y1="11" y2="5"/>
   </symbol>
   <symbol id="voltageTransformer:shape10">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="18" x2="15" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="16" x2="16" y1="4" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="3" x2="9" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="6" x2="3" y1="14" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="6" x2="9" y1="14" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="18" x2="15" y1="17" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="16" x2="16" y1="15" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="13" x2="15" y1="17" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="27" x2="24" y1="13" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="25" x2="25" y1="11" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="22" x2="24" y1="13" y2="11"/>
    <circle cx="7" cy="11" fillStyle="0" r="6" stroke-width="0.431185"/>
    <ellipse cx="15" cy="6" fillStyle="0" rx="6.5" ry="6" stroke-width="0.431185"/>
    <circle cx="24" cy="11" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="15" cy="14" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="13" x2="15" y1="6" y2="4"/>
   </symbol>
   <symbol id="voltageTransformer:shape79">
    <circle cx="18" cy="24" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="34" y1="13" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="13" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="19" y1="11" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="19" y1="11" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="25" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="7" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="7" y1="23" y2="25"/>
    <circle cx="18" cy="12" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="8" y1="11" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="8" y1="13" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="15" y2="9"/>
    <polyline points="40,23 28,32 28,36 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="34" y1="37" y2="46"/>
    <rect height="14" stroke-width="1" width="8" x="30" y="23"/>
    <circle cx="7" cy="24" r="7.5" stroke-width="1"/>
    <circle cx="7" cy="12" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="37" y1="48" y2="48"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="36" y1="50" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="39" y1="46" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="25" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="19" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="19" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="34" y1="23" y2="13"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23d3690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23d40c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_23d4a30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_23d56d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_23d6930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_23d7550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23d7c90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23d8530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23d8c70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23d95b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注1</text>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23da0b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注2</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23da690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23dac70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23db680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23dbf70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_23dc850" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">禁止操作</text>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23ddf60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23de9f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23df170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_23dfb60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23e0d40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23e16c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23e21b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="78" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_23e7430" transform="matrix(1.246377 -0.000000 0.000000 -1.035714 2.739130 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23e8030" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">测试</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_23e3f70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_23e5350" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    
   </symbol>
   <symbol id="Tag:shape33">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_23e64f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape34">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_23ea0c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape36">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
   </symbol>
   <symbol id="Tag:shape37">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1406" width="2093" x="3110" y="-1284"/>
  </g><g id="PolygonFilled_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4116,-586 4111,-597 4122,-597 4116,-586 4116,-587 4116,-586 " stroke="rgb(50,205,50)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4116,-498 4111,-487 4122,-487 4116,-498 4116,-497 4116,-498 " stroke="rgb(50,205,50)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4955,-153 4950,-164 4961,-164 4955,-153 4955,-154 4955,-153 " stroke="rgb(50,205,50)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4955,-96 4950,-85 4961,-85 4955,-96 4955,-95 4955,-96 " stroke="rgb(50,205,50)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4852,-153 4847,-164 4858,-164 4852,-153 4852,-154 4852,-153 " stroke="rgb(50,205,50)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4852,-96 4847,-85 4858,-85 4852,-96 4852,-95 4852,-96 " stroke="rgb(50,205,50)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4747,-153 4742,-164 4753,-164 4747,-153 4747,-154 4747,-153 " stroke="rgb(50,205,50)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4747,-96 4742,-85 4753,-85 4747,-96 4747,-95 4747,-96 " stroke="rgb(50,205,50)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4638,-153 4633,-164 4644,-164 4638,-153 4638,-154 4638,-153 " stroke="rgb(50,205,50)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4638,-96 4633,-85 4644,-85 4638,-96 4638,-95 4638,-96 " stroke="rgb(50,205,50)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4535,-154 4530,-165 4541,-165 4535,-154 4535,-155 4535,-154 " stroke="rgb(50,205,50)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4535,-130 4530,-119 4541,-119 4535,-130 4535,-129 4535,-130 " stroke="rgb(50,205,50)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4398,-153 4393,-164 4404,-164 4398,-153 4398,-154 4398,-153 " stroke="rgb(50,205,50)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4535,-42 4530,-53 4541,-53 4535,-42 4535,-43 4535,-42 " stroke="rgb(50,205,50)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4095,-140 4090,-151 4101,-151 4095,-140 4095,-141 4095,-140 " stroke="rgb(50,205,50)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4095,-60 4090,-49 4101,-49 4095,-60 4095,-59 4095,-60 " stroke="rgb(50,205,50)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3858,-140 3853,-151 3864,-151 3858,-140 3858,-141 3858,-140 " stroke="rgb(50,205,50)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3858,-60 3853,-49 3864,-49 3858,-60 3858,-59 3858,-60 " stroke="rgb(50,205,50)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3617,-140 3612,-151 3623,-151 3617,-140 3617,-141 3617,-140 " stroke="rgb(50,205,50)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3617,-60 3612,-49 3623,-49 3617,-60 3617,-59 3617,-60 " stroke="rgb(50,205,50)"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-193259">
    <use class="BV-3KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4526.000000 -230.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29336" ObjectName="SW-WD_DXS.WD_DXS_001BK"/>
     <cge:Meas_Ref ObjectId="193259"/>
    <cge:TPSR_Ref TObjectID="29336"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193256">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4734.000000 -528.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29333" ObjectName="SW-WD_DXS.WD_DXS_311BK"/>
     <cge:Meas_Ref ObjectId="193256"/>
    <cge:TPSR_Ref TObjectID="29333"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-94045">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4354.578000 -552.874055)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19822" ObjectName="SW-WD_DXS.WD_DXS_301BK"/>
     <cge:Meas_Ref ObjectId="94045"/>
    <cge:TPSR_Ref TObjectID="19822"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193249">
    <use class="BV-3KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4107.191445 -436.874055)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23691" ObjectName="SW-WD_DXS.WD_DXS_601BK"/>
     <cge:Meas_Ref ObjectId="193249"/>
    <cge:TPSR_Ref TObjectID="23691"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193257">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4736.000000 -739.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29334" ObjectName="SW-WD_DXS.WD_DXS_313BK"/>
     <cge:Meas_Ref ObjectId="193257"/>
    <cge:TPSR_Ref TObjectID="29334"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193255">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4738.000000 -974.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29332" ObjectName="SW-WD_DXS.WD_DXS_314BK"/>
     <cge:Meas_Ref ObjectId="193255"/>
    <cge:TPSR_Ref TObjectID="29332"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193250">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4738.000000 -1087.627204)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29331" ObjectName="SW-WD_DXS.WD_DXS_315BK"/>
     <cge:Meas_Ref ObjectId="193250"/>
    <cge:TPSR_Ref TObjectID="29331"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-94042">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4738.000000 -1199.055416)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19818" ObjectName="SW-WD_DXS.WD_DXS_316BK"/>
     <cge:Meas_Ref ObjectId="94042"/>
    <cge:TPSR_Ref TObjectID="19818"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193254">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4946.000000 -230.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29398" ObjectName="SW-WD_DXS.WD_DXS_014BK"/>
     <cge:Meas_Ref ObjectId="193254"/>
    <cge:TPSR_Ref TObjectID="29398"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193252">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4843.000000 -230.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29397" ObjectName="SW-WD_DXS.WD_DXS_013BK"/>
     <cge:Meas_Ref ObjectId="193252"/>
    <cge:TPSR_Ref TObjectID="29397"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193253">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4738.000000 -230.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29396" ObjectName="SW-WD_DXS.WD_DXS_012BK"/>
     <cge:Meas_Ref ObjectId="193253"/>
    <cge:TPSR_Ref TObjectID="29396"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193251">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4629.000000 -230.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29395" ObjectName="SW-WD_DXS.WD_DXS_011BK"/>
     <cge:Meas_Ref ObjectId="193251"/>
    <cge:TPSR_Ref TObjectID="29395"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193260">
    <use class="BV-3KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4389.000000 -230.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29347" ObjectName="SW-WD_DXS.WD_DXS_602BK"/>
     <cge:Meas_Ref ObjectId="193260"/>
    <cge:TPSR_Ref TObjectID="29347"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-93967">
    <use class="BV-3KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4086.000000 -200.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19776" ObjectName="SW-WD_DXS.WD_DXS_613BK"/>
     <cge:Meas_Ref ObjectId="93967"/>
    <cge:TPSR_Ref TObjectID="19776"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-93964">
    <use class="BV-3KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3849.000000 -200.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19773" ObjectName="SW-WD_DXS.WD_DXS_612BK"/>
     <cge:Meas_Ref ObjectId="93964"/>
    <cge:TPSR_Ref TObjectID="19773"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-93961">
    <use class="BV-3KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3608.000000 -200.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19770" ObjectName="SW-WD_DXS.WD_DXS_611BK"/>
     <cge:Meas_Ref ObjectId="93961"/>
    <cge:TPSR_Ref TObjectID="19770"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193258">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4785.000000 -827.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29335" ObjectName="SW-WD_DXS.WD_DXS_312BK"/>
     <cge:Meas_Ref ObjectId="193258"/>
    <cge:TPSR_Ref TObjectID="29335"/></metadata>
   </g>
  </g><g id="Transformer_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-WD_DXS.WD_DXS_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="43412"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4092.578000 -602.000000)" xlink:href="#transformer:shape3_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="43414"/>
     </metadata>
     <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4092.578000 -602.000000)" xlink:href="#transformer:shape3_1"/>
    </g>
    <g id="WD-2">
     <metadata>
      <cge:PSR_Ref ObjectId="43416"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4092.578000 -602.000000)" xlink:href="#transformer:shape3-2"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="30500" ObjectName="TF-WD_DXS.WD_DXS_1T"/>
    </metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-WD_DXS.WD_DXS_6ⅠM">
    <g class="BV-3KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3512,-349 4439,-349 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="19827" ObjectName="BS-WD_DXS.WD_DXS_6ⅠM"/>
    <cge:TPSR_Ref TObjectID="19827"/></metadata>
   <polyline fill="none" opacity="0" points="3512,-349 4439,-349 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-WD_DXS.WD_DXS_9ⅠM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4505,-349 5124,-349 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="19826" ObjectName="BS-WD_DXS.WD_DXS_9ⅠM"/>
    <cge:TPSR_Ref TObjectID="19826"/></metadata>
   <polyline fill="none" opacity="0" points="4505,-349 5124,-349 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-WD_DXS.WD_DXS_3ⅠM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4633,-1279 4633,-878 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="19825" ObjectName="BS-WD_DXS.WD_DXS_3ⅠM"/>
    <cge:TPSR_Ref TObjectID="19825"/></metadata>
   <polyline fill="none" opacity="0" points="4633,-1279 4633,-878 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-WD_DXS.WD_DXS_3ⅡM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4633,-811 4633,-444 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="29321" ObjectName="BS-WD_DXS.WD_DXS_3ⅡM"/>
    <cge:TPSR_Ref TObjectID="29321"/></metadata>
   <polyline fill="none" opacity="0" points="4633,-811 4633,-444 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4946.000000 21.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4843.000000 21.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4738.000000 21.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4629.000000 21.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1bac7f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4033.000000 -593.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bee200" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4841.000000 -447.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c5d9c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4843.000000 -658.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bc29d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4845.000000 -893.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bd01f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4845.000000 -1005.627204)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bf8990" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4845.000000 -1118.055416)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fbd920" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5119.000000 -258.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fc7850" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5014.000000 -275.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fd7730" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4911.000000 -275.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fe77c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4806.000000 -275.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ff97c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4697.000000 -275.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2003090" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4594.000000 -275.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2010850" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4457.000000 -274.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_201f380" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4341.000000 -260.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20378e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4017.000000 -267.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_204a610" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3780.000000 -267.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_205fae0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3539.000000 -267.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20756c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3841.000000 -409.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_207b740" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3626.000000 -409.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1beff00">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5022.000000 -188.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a27030">
    <use class="BV-10KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 5079.000000 -241.400000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c63cd0">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4861.500000 -520.500000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c644a0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4956.500000 -565.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c59860">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4061.000000 -599.000000)" xlink:href="#lightningRod:shape54"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c43460">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4003.000000 -601.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bf1ee0">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4863.500000 -731.500000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bf2a90">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4955.500000 -789.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bd5df0">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4865.500000 -966.500000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bc08a0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4957.500000 -1024.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c17c30">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4865.500000 -1079.127204)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c189a0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4960.500000 -1137.127204)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bbf920">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4865.500000 -1191.555416)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bf6850">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4957.500000 -1248.555416)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bb1920">
    <use class="BV-35KV" transform="matrix(-0.000000 -1.477273 1.564103 -0.000000 4387.935897 -695.500000)" xlink:href="#lightningRod:shape190"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bb46c0">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4464.500000 -740.444584)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bb51a0">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4507.000000 -805.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d4b350">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4962.500000 -616.500000)" xlink:href="#lightningRod:shape186"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d4d100">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4815.500000 -622.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fb8aa0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5007.000000 -135.000000)" xlink:href="#lightningRod:shape190"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fc6900">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4917.000000 -166.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fd67e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4814.000000 -166.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fe67f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4709.000000 -166.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ff87f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4600.000000 -166.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2009830">
    <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4497.000000 -167.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2016990">
    <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4360.000000 -166.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2019ac0">
    <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4274.000000 -36.000000)" xlink:href="#lightningRod:shape186"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_201e8a0">
    <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4280.000000 -196.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_202bb80">
    <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4021.000000 -170.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2030820">
    <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4123.000000 -92.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2031300">
    <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4163.000000 -78.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2040290">
    <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3784.000000 -170.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2044480">
    <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3886.000000 -92.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2044f60">
    <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3926.000000 -78.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2055760">
    <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3543.000000 -170.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2059950">
    <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3645.000000 -92.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_205a430">
    <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3685.000000 -78.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2072180">
    <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3943.000000 -474.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2085a30">
    <use class="BV-35KV" transform="matrix(-0.000000 -1.477273 1.564103 -0.000000 4387.935897 -883.500000)" xlink:href="#lightningRod:shape190"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2088330">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4464.500000 -928.444584)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2088e10">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4507.000000 -993.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3215.000000 -1116.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-0" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3292.538462 -987.966362) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-0" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3292.538462 -945.966362) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-93948" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3599.000000 57.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="93948" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19770"/>
     <cge:Term_Ref ObjectID="27554"/>
    <cge:TPSR_Ref TObjectID="19770"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-93949" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3599.000000 57.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="93949" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19770"/>
     <cge:Term_Ref ObjectID="27554"/>
    <cge:TPSR_Ref TObjectID="19770"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-93947" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3599.000000 57.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="93947" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19770"/>
     <cge:Term_Ref ObjectID="27554"/>
    <cge:TPSR_Ref TObjectID="19770"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-93946" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3599.000000 57.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="93946" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19770"/>
     <cge:Term_Ref ObjectID="27554"/>
    <cge:TPSR_Ref TObjectID="19770"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-93953" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3834.000000 57.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="93953" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19773"/>
     <cge:Term_Ref ObjectID="27560"/>
    <cge:TPSR_Ref TObjectID="19773"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-93954" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3834.000000 57.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="93954" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19773"/>
     <cge:Term_Ref ObjectID="27560"/>
    <cge:TPSR_Ref TObjectID="19773"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-93952" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3834.000000 57.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="93952" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19773"/>
     <cge:Term_Ref ObjectID="27560"/>
    <cge:TPSR_Ref TObjectID="19773"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-93951" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3834.000000 57.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="93951" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19773"/>
     <cge:Term_Ref ObjectID="27560"/>
    <cge:TPSR_Ref TObjectID="19773"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-93958" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4071.000000 57.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="93958" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19776"/>
     <cge:Term_Ref ObjectID="27566"/>
    <cge:TPSR_Ref TObjectID="19776"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-93959" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4071.000000 57.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="93959" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19776"/>
     <cge:Term_Ref ObjectID="27566"/>
    <cge:TPSR_Ref TObjectID="19776"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-93957" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4071.000000 57.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="93957" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19776"/>
     <cge:Term_Ref ObjectID="27566"/>
    <cge:TPSR_Ref TObjectID="19776"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-93956" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4071.000000 57.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="93956" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19776"/>
     <cge:Term_Ref ObjectID="27566"/>
    <cge:TPSR_Ref TObjectID="19776"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-200472" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5167.000000 -1231.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="200472" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19818"/>
     <cge:Term_Ref ObjectID="27574"/>
    <cge:TPSR_Ref TObjectID="19818"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-200473" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5167.000000 -1231.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="200473" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19818"/>
     <cge:Term_Ref ObjectID="27574"/>
    <cge:TPSR_Ref TObjectID="19818"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-200470" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5167.000000 -1231.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="200470" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19818"/>
     <cge:Term_Ref ObjectID="27574"/>
    <cge:TPSR_Ref TObjectID="19818"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-200465" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5167.000000 -1115.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="200465" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29331"/>
     <cge:Term_Ref ObjectID="27521"/>
    <cge:TPSR_Ref TObjectID="29331"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-200466" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5167.000000 -1115.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="200466" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29331"/>
     <cge:Term_Ref ObjectID="27521"/>
    <cge:TPSR_Ref TObjectID="29331"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-200463" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5167.000000 -1115.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="200463" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29331"/>
     <cge:Term_Ref ObjectID="27521"/>
    <cge:TPSR_Ref TObjectID="29331"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-200458" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5167.000000 -1005.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="200458" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29332"/>
     <cge:Term_Ref ObjectID="27535"/>
    <cge:TPSR_Ref TObjectID="29332"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-200459" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5167.000000 -1005.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="200459" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29332"/>
     <cge:Term_Ref ObjectID="27535"/>
    <cge:TPSR_Ref TObjectID="29332"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-200456" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5167.000000 -1005.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="200456" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29332"/>
     <cge:Term_Ref ObjectID="27535"/>
    <cge:TPSR_Ref TObjectID="29332"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-200444" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5167.000000 -767.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="200444" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29334"/>
     <cge:Term_Ref ObjectID="41328"/>
    <cge:TPSR_Ref TObjectID="29334"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-200445" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5167.000000 -767.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="200445" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29334"/>
     <cge:Term_Ref ObjectID="41328"/>
    <cge:TPSR_Ref TObjectID="29334"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-200442" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5167.000000 -767.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="200442" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29334"/>
     <cge:Term_Ref ObjectID="41328"/>
    <cge:TPSR_Ref TObjectID="29334"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-200437" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5167.000000 -558.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="200437" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29333"/>
     <cge:Term_Ref ObjectID="41326"/>
    <cge:TPSR_Ref TObjectID="29333"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-200438" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5167.000000 -558.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="200438" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29333"/>
     <cge:Term_Ref ObjectID="41326"/>
    <cge:TPSR_Ref TObjectID="29333"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-200435" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5167.000000 -558.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="200435" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29333"/>
     <cge:Term_Ref ObjectID="41326"/>
    <cge:TPSR_Ref TObjectID="29333"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-200402" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4620.500000 64.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="200402" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29395"/>
     <cge:Term_Ref ObjectID="41854"/>
    <cge:TPSR_Ref TObjectID="29395"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-200403" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4620.500000 64.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="200403" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29395"/>
     <cge:Term_Ref ObjectID="41854"/>
    <cge:TPSR_Ref TObjectID="29395"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-200400" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4620.500000 64.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="200400" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29395"/>
     <cge:Term_Ref ObjectID="41854"/>
    <cge:TPSR_Ref TObjectID="29395"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-200409" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4728.000000 64.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="200409" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29396"/>
     <cge:Term_Ref ObjectID="41856"/>
    <cge:TPSR_Ref TObjectID="29396"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-200410" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4728.000000 64.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="200410" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29396"/>
     <cge:Term_Ref ObjectID="41856"/>
    <cge:TPSR_Ref TObjectID="29396"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-200407" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4728.000000 64.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="200407" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29396"/>
     <cge:Term_Ref ObjectID="41856"/>
    <cge:TPSR_Ref TObjectID="29396"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-200430" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4835.500000 64.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="200430" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29397"/>
     <cge:Term_Ref ObjectID="41858"/>
    <cge:TPSR_Ref TObjectID="29397"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-200431" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4835.500000 64.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="200431" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29397"/>
     <cge:Term_Ref ObjectID="41858"/>
    <cge:TPSR_Ref TObjectID="29397"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-200428" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4835.500000 64.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="200428" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29397"/>
     <cge:Term_Ref ObjectID="41858"/>
    <cge:TPSR_Ref TObjectID="29397"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-200423" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4943.000000 64.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="200423" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29398"/>
     <cge:Term_Ref ObjectID="41860"/>
    <cge:TPSR_Ref TObjectID="29398"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-200424" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4943.000000 64.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="200424" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29398"/>
     <cge:Term_Ref ObjectID="41860"/>
    <cge:TPSR_Ref TObjectID="29398"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-200421" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4943.000000 64.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="200421" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29398"/>
     <cge:Term_Ref ObjectID="41860"/>
    <cge:TPSR_Ref TObjectID="29398"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-200451" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4940.000000 -879.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="200451" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29335"/>
     <cge:Term_Ref ObjectID="41762"/>
    <cge:TPSR_Ref TObjectID="29335"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-200452" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4940.000000 -879.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="200452" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29335"/>
     <cge:Term_Ref ObjectID="41762"/>
    <cge:TPSR_Ref TObjectID="29335"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-200449" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4940.000000 -879.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="200449" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29335"/>
     <cge:Term_Ref ObjectID="41762"/>
    <cge:TPSR_Ref TObjectID="29335"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="3235" y="-1178"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="3235" y="-1178"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3186" y="-1195"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3186" y="-1195"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="43" qtmmishow="hidden" width="73" x="3395" y="-1180"/>
    </a>
   <metadata/><rect fill="white" height="43" opacity="0" stroke="white" transform="" width="73" x="3395" y="-1180"/></g>
  </g><g id="MotifButton_Layer">
   <g href="wd_索引_发电站接线图.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="3235" y="-1178"/></g>
   <g href="wd_索引_发电站接线图.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3186" y="-1195"/></g>
   <g href="AVC田心站.svg" style="fill-opacity:0"><rect height="43" qtmmishow="hidden" width="73" x="3395" y="-1180"/></g>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3116" y="-1198"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-1079"/>
   <rect DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" fillStyle="1" height="42" stroke="rgb(255,255,255)" stroke-width="1" width="72" x="3395" y="-1180"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="27" stroke="rgb(60,120,255)" stroke-width="0.416609" width="14" x="3874" y="-480"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="27" stroke="rgb(60,120,255)" stroke-width="0.416609" width="14" x="3697" y="-459"/>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1c649d0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4969.000000 -566.500000)" xlink:href="#voltageTransformer:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c5c200">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4968.000000 -790.500000)" xlink:href="#voltageTransformer:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bc10f0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4970.000000 -1025.500000)" xlink:href="#voltageTransformer:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c19220">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4973.000000 -1138.127204)" xlink:href="#voltageTransformer:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bf70b0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4970.000000 -1249.555416)" xlink:href="#voltageTransformer:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_202ea90">
    <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4117.000000 -59.000000)" xlink:href="#voltageTransformer:shape10"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20426f0">
    <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3880.000000 -59.000000)" xlink:href="#voltageTransformer:shape10"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2057bc0">
    <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3639.000000 -59.000000)" xlink:href="#voltageTransformer:shape10"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_206f5f0">
    <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3862.000000 -486.000000)" xlink:href="#voltageTransformer:shape79"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4522.000000 -59.000000)" xlink:href="#transformer2:shape2_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4522.000000 -59.000000)" xlink:href="#transformer2:shape2_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4159.000000 -20.000000)" xlink:href="#transformer2:shape2_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4159.000000 -20.000000)" xlink:href="#transformer2:shape2_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3922.000000 -20.000000)" xlink:href="#transformer2:shape2_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3922.000000 -20.000000)" xlink:href="#transformer2:shape2_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3681.000000 -20.000000)" xlink:href="#transformer2:shape2_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3681.000000 -20.000000)" xlink:href="#transformer2:shape2_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-10KV" id="g_1c2c710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4535,-331 4535,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29393@1" ObjectIDZND0="19826@0" Pin0InfoVect0LinkObjId="g_1fb8380_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193301_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4535,-331 4535,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c7fc60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4688,-538 4632,-538 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29376@0" ObjectIDZND0="29321@0" Pin0InfoVect0LinkObjId="g_1c751c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193284_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4688,-538 4632,-538 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c7fe50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4724,-538 4743,-538 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="29376@1" ObjectIDZND0="29333@1" Pin0InfoVect0LinkObjId="SW-193256_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193284_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4724,-538 4743,-538 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c80040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4770,-538 4788,-538 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29333@0" ObjectIDZND0="29377@0" Pin0InfoVect0LinkObjId="SW-193283_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193256_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4770,-538 4788,-538 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c80230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4847,-480 4847,-465 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="29378@0" ObjectIDZND0="g_1bee200@0" Pin0InfoVect0LinkObjId="g_1bee200_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193282_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4847,-480 4847,-465 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c80420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4951,-574 4974,-574 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_1c644a0@0" ObjectIDZND0="g_1c649d0@0" Pin0InfoVect0LinkObjId="g_1c649d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c644a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4951,-574 4974,-574 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c751c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4689,-792 4632,-792 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29383@0" ObjectIDZND0="29321@0" Pin0InfoVect0LinkObjId="g_1c7fc60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193289_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4689,-792 4632,-792 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_1c43080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4117,-472 4117,-607 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer" ObjectIDND0="23691@1" ObjectIDZND0="30500@1" Pin0InfoVect0LinkObjId="g_2092750_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193249_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4117,-472 4117,-607 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_1c43270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4117,-417 4117,-445 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="29330@1" ObjectIDZND0="23691@0" Pin0InfoVect0LinkObjId="SW-193249_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-200313_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4117,-417 4117,-445 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1bedb20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4039,-611 4039,-621 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1bac7f0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="EC-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bac7f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4039,-611 4039,-621 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c13290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4391,-562 4414,-562 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="19822@0" ObjectIDZND0="19824@0" Pin0InfoVect0LinkObjId="SW-94047_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-94045_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4391,-562 4414,-562 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c13480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4341,-562 4364,-562 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="19823@1" ObjectIDZND0="19822@1" Pin0InfoVect0LinkObjId="SW-94045_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-94046_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4341,-562 4364,-562 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c13df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5085,-237 5060,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1a27030@0" ObjectIDZND0="g_1beff00@0" ObjectIDZND1="29367@x" ObjectIDZND2="29401@x" Pin0InfoVect0LinkObjId="g_1beff00_0" Pin0InfoVect1LinkObjId="SW-193273_0" Pin0InfoVect2LinkObjId="SW-193304_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a27030_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5085,-237 5060,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c14a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4847,-516 4847,-538 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="29378@1" ObjectIDZND0="29377@x" ObjectIDZND1="g_1c63cd0@0" ObjectIDZND2="g_1c644a0@0" Pin0InfoVect0LinkObjId="SW-193283_0" Pin0InfoVect1LinkObjId="g_1c63cd0_0" Pin0InfoVect2LinkObjId="g_1c644a0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193282_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4847,-516 4847,-538 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c153c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4824,-538 4847,-538 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="29377@1" ObjectIDZND0="29378@x" ObjectIDZND1="g_1c63cd0@0" ObjectIDZND2="g_1c644a0@0" Pin0InfoVect0LinkObjId="SW-193282_0" Pin0InfoVect1LinkObjId="g_1c63cd0_0" Pin0InfoVect2LinkObjId="g_1c644a0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193283_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4824,-538 4847,-538 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c155b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4870,-516 4870,-538 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_1c63cd0@0" ObjectIDZND0="29378@x" ObjectIDZND1="29377@x" ObjectIDZND2="g_1c644a0@0" Pin0InfoVect0LinkObjId="SW-193282_0" Pin0InfoVect1LinkObjId="SW-193283_0" Pin0InfoVect2LinkObjId="g_1c644a0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c63cd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4870,-516 4870,-538 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bdd4a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4847,-538 4870,-538 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="29378@x" ObjectIDND1="29377@x" ObjectIDZND0="g_1c63cd0@0" ObjectIDZND1="g_1c644a0@0" Pin0InfoVect0LinkObjId="g_1c63cd0_0" Pin0InfoVect1LinkObjId="g_1c644a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-193282_0" Pin1InfoVect1LinkObjId="SW-193283_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4847,-538 4870,-538 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bdd6c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4888,-538 5071,-538 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" ObjectIDND0="29378@x" ObjectIDND1="29377@x" ObjectIDND2="g_1c63cd0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-193282_0" Pin1InfoVect1LinkObjId="SW-193283_0" Pin1InfoVect2LinkObjId="g_1c63cd0_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4888,-538 5071,-538 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bde150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4920,-575 4888,-575 4888,-538 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_1c644a0@1" ObjectIDZND0="29378@x" ObjectIDZND1="29377@x" ObjectIDZND2="g_1c63cd0@0" Pin0InfoVect0LinkObjId="SW-193282_0" Pin0InfoVect1LinkObjId="SW-193283_0" Pin0InfoVect2LinkObjId="g_1c63cd0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c644a0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4920,-575 4888,-575 4888,-538 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bde370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4870,-538 4888,-538 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="29378@x" ObjectIDND1="29377@x" ObjectIDND2="g_1c63cd0@0" ObjectIDZND0="g_1c644a0@0" Pin0InfoVect0LinkObjId="g_1c644a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-193282_0" Pin1InfoVect1LinkObjId="SW-193283_0" Pin1InfoVect2LinkObjId="g_1c63cd0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4870,-538 4888,-538 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c5cb50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4690,-749 4632,-749 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29381@0" ObjectIDZND0="29321@0" Pin0InfoVect0LinkObjId="g_1c7fc60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193287_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4690,-749 4632,-749 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c5cd70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4726,-749 4745,-749 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="29381@1" ObjectIDZND0="29334@1" Pin0InfoVect0LinkObjId="SW-193257_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193287_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4726,-749 4745,-749 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c5cf90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4772,-749 4790,-749 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29334@0" ObjectIDZND0="29380@0" Pin0InfoVect0LinkObjId="SW-193286_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193257_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4772,-749 4790,-749 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c5d1b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4849,-691 4849,-676 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="29379@0" ObjectIDZND0="g_1c5d9c0@0" Pin0InfoVect0LinkObjId="g_1c5d9c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193285_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4849,-691 4849,-676 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c5d3d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4950,-798 4973,-798 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_1bf2a90@0" ObjectIDZND0="g_1c5c200@0" Pin0InfoVect0LinkObjId="g_1c5c200_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bf2a90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4950,-798 4973,-798 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c549c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4849,-727 4849,-749 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="29379@1" ObjectIDZND0="29380@x" ObjectIDZND1="g_1bf1ee0@0" ObjectIDZND2="g_1bf2a90@0" Pin0InfoVect0LinkObjId="SW-193286_0" Pin0InfoVect1LinkObjId="g_1bf1ee0_0" Pin0InfoVect2LinkObjId="g_1bf2a90_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193285_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4849,-727 4849,-749 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c54be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4826,-749 4849,-749 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="29380@1" ObjectIDZND0="29379@x" ObjectIDZND1="g_1bf1ee0@0" ObjectIDZND2="g_1bf2a90@0" Pin0InfoVect0LinkObjId="SW-193285_0" Pin0InfoVect1LinkObjId="g_1bf1ee0_0" Pin0InfoVect2LinkObjId="g_1bf2a90_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193286_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4826,-749 4849,-749 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c54e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4872,-727 4872,-749 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_1bf1ee0@0" ObjectIDZND0="29380@x" ObjectIDZND1="29379@x" ObjectIDZND2="g_1bf2a90@0" Pin0InfoVect0LinkObjId="SW-193286_0" Pin0InfoVect1LinkObjId="SW-193285_0" Pin0InfoVect2LinkObjId="g_1bf2a90_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bf1ee0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4872,-727 4872,-749 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c55020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4849,-749 4872,-749 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="29380@x" ObjectIDND1="29379@x" ObjectIDZND0="g_1bf1ee0@0" ObjectIDZND1="g_1bf2a90@0" Pin0InfoVect0LinkObjId="g_1bf1ee0_0" Pin0InfoVect1LinkObjId="g_1bf2a90_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-193286_0" Pin1InfoVect1LinkObjId="SW-193285_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4849,-749 4872,-749 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c55240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4891,-749 5072,-749 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" ObjectIDND0="g_1bf2a90@0" ObjectIDND1="29380@x" ObjectIDND2="29379@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1bf2a90_0" Pin1InfoVect1LinkObjId="SW-193286_0" Pin1InfoVect2LinkObjId="SW-193285_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4891,-749 5072,-749 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c55460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4919,-798 4890,-798 4890,-749 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_1bf2a90@1" ObjectIDZND0="29380@x" ObjectIDZND1="29379@x" ObjectIDZND2="g_1bf1ee0@0" Pin0InfoVect0LinkObjId="SW-193286_0" Pin0InfoVect1LinkObjId="SW-193285_0" Pin0InfoVect2LinkObjId="g_1bf1ee0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bf2a90_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4919,-798 4890,-798 4890,-749 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c55680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4872,-749 4890,-749 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="29380@x" ObjectIDND1="29379@x" ObjectIDND2="g_1bf1ee0@0" ObjectIDZND0="g_1bf2a90@0" Pin0InfoVect0LinkObjId="g_1bf2a90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-193286_0" Pin1InfoVect1LinkObjId="SW-193285_0" Pin1InfoVect2LinkObjId="g_1bf1ee0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4872,-749 4890,-749 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bc1b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4728,-984 4747,-984 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="29375@1" ObjectIDZND0="29332@1" Pin0InfoVect0LinkObjId="SW-193255_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193281_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4728,-984 4747,-984 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bc1dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4774,-984 4792,-984 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29332@0" ObjectIDZND0="29374@0" Pin0InfoVect0LinkObjId="SW-193280_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193255_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4774,-984 4792,-984 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bc2020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4851,-926 4851,-911 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="29373@0" ObjectIDZND0="g_1bc29d0@0" Pin0InfoVect0LinkObjId="g_1bc29d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193279_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4851,-926 4851,-911 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bc2280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4952,-1033 4975,-1033 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_1bc08a0@0" ObjectIDZND0="g_1bc10f0@0" Pin0InfoVect0LinkObjId="g_1bc10f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bc08a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4952,-1033 4975,-1033 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c102b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4851,-962 4851,-984 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="29373@1" ObjectIDZND0="29374@x" ObjectIDZND1="g_1bd5df0@0" ObjectIDZND2="g_1bc08a0@0" Pin0InfoVect0LinkObjId="SW-193280_0" Pin0InfoVect1LinkObjId="g_1bd5df0_0" Pin0InfoVect2LinkObjId="g_1bc08a0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193279_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4851,-962 4851,-984 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c10510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4828,-984 4851,-984 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="29374@1" ObjectIDZND0="29373@x" ObjectIDZND1="g_1bd5df0@0" ObjectIDZND2="g_1bc08a0@0" Pin0InfoVect0LinkObjId="SW-193279_0" Pin0InfoVect1LinkObjId="g_1bd5df0_0" Pin0InfoVect2LinkObjId="g_1bc08a0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193280_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4828,-984 4851,-984 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c10770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4874,-962 4874,-984 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_1bd5df0@0" ObjectIDZND0="29373@x" ObjectIDZND1="29374@x" ObjectIDZND2="g_1bc08a0@0" Pin0InfoVect0LinkObjId="SW-193279_0" Pin0InfoVect1LinkObjId="SW-193280_0" Pin0InfoVect2LinkObjId="g_1bc08a0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bd5df0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4874,-962 4874,-984 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c109d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4851,-984 4874,-984 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="29373@x" ObjectIDND1="29374@x" ObjectIDZND0="g_1bd5df0@0" ObjectIDZND1="g_1bc08a0@0" Pin0InfoVect0LinkObjId="g_1bd5df0_0" Pin0InfoVect1LinkObjId="g_1bc08a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-193279_0" Pin1InfoVect1LinkObjId="SW-193280_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4851,-984 4874,-984 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c10c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4892,-984 5073,-984 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" ObjectIDND0="g_1bd5df0@0" ObjectIDND1="29373@x" ObjectIDND2="29374@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1bd5df0_0" Pin1InfoVect1LinkObjId="SW-193279_0" Pin1InfoVect2LinkObjId="SW-193280_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4892,-984 5073,-984 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c10e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4921,-1033 4892,-1033 4892,-984 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1bc08a0@1" ObjectIDZND0="g_1bd5df0@0" ObjectIDZND1="29373@x" ObjectIDZND2="29374@x" Pin0InfoVect0LinkObjId="g_1bd5df0_0" Pin0InfoVect1LinkObjId="SW-193279_0" Pin0InfoVect2LinkObjId="SW-193280_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bc08a0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4921,-1033 4892,-1033 4892,-984 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c110f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4874,-984 4892,-984 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_1bd5df0@0" ObjectIDND1="29373@x" ObjectIDND2="29374@x" ObjectIDZND0="g_1bc08a0@0" Pin0InfoVect0LinkObjId="g_1bc08a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1bd5df0_0" Pin1InfoVect1LinkObjId="SW-193279_0" Pin1InfoVect2LinkObjId="SW-193280_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4874,-984 4892,-984 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bcf380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4728,-1097 4747,-1097 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="29370@1" ObjectIDZND0="29331@1" Pin0InfoVect0LinkObjId="SW-193250_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193276_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4728,-1097 4747,-1097 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bcf5e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4774,-1097 4792,-1097 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29331@0" ObjectIDZND0="29371@0" Pin0InfoVect0LinkObjId="SW-193278_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193250_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4774,-1097 4792,-1097 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bcf840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4851,-1039 4851,-1024 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="29372@0" ObjectIDZND0="g_1bd01f0@0" Pin0InfoVect0LinkObjId="g_1bd01f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193277_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4851,-1039 4851,-1024 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bcfaa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4955,-1146 4978,-1146 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_1c189a0@0" ObjectIDZND0="g_1c19220@0" Pin0InfoVect0LinkObjId="g_1c19220_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c189a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4955,-1146 4978,-1146 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cda110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4851,-1075 4851,-1097 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="29372@1" ObjectIDZND0="29371@x" ObjectIDZND1="g_1c17c30@0" ObjectIDZND2="g_1c189a0@0" Pin0InfoVect0LinkObjId="SW-193278_0" Pin0InfoVect1LinkObjId="g_1c17c30_0" Pin0InfoVect2LinkObjId="g_1c189a0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193277_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4851,-1075 4851,-1097 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cda370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4828,-1097 4851,-1097 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="29371@1" ObjectIDZND0="29372@x" ObjectIDZND1="g_1c17c30@0" ObjectIDZND2="g_1c189a0@0" Pin0InfoVect0LinkObjId="SW-193277_0" Pin0InfoVect1LinkObjId="g_1c17c30_0" Pin0InfoVect2LinkObjId="g_1c189a0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193278_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4828,-1097 4851,-1097 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cda5d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4874,-1075 4874,-1097 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_1c17c30@0" ObjectIDZND0="29372@x" ObjectIDZND1="29371@x" ObjectIDZND2="g_1c189a0@0" Pin0InfoVect0LinkObjId="SW-193277_0" Pin0InfoVect1LinkObjId="SW-193278_0" Pin0InfoVect2LinkObjId="g_1c189a0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c17c30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4874,-1075 4874,-1097 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cda830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4851,-1097 4874,-1097 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="29372@x" ObjectIDND1="29371@x" ObjectIDZND0="g_1c17c30@0" ObjectIDZND1="g_1c189a0@0" Pin0InfoVect0LinkObjId="g_1c17c30_0" Pin0InfoVect1LinkObjId="g_1c189a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-193277_0" Pin1InfoVect1LinkObjId="SW-193278_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4851,-1097 4874,-1097 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cdaa90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4892,-1097 5073,-1097 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" ObjectIDND0="g_1c17c30@0" ObjectIDND1="29372@x" ObjectIDND2="29371@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1c17c30_0" Pin1InfoVect1LinkObjId="SW-193277_0" Pin1InfoVect2LinkObjId="SW-193278_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4892,-1097 5073,-1097 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cdacf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4924,-1146 4892,-1146 4892,-1097 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1c189a0@1" ObjectIDZND0="g_1c17c30@0" ObjectIDZND1="29372@x" ObjectIDZND2="29371@x" Pin0InfoVect0LinkObjId="g_1c17c30_0" Pin0InfoVect1LinkObjId="SW-193277_0" Pin0InfoVect2LinkObjId="SW-193278_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c189a0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4924,-1146 4892,-1146 4892,-1097 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cdaf50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4874,-1097 4892,-1097 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_1c17c30@0" ObjectIDND1="29372@x" ObjectIDND2="29371@x" ObjectIDZND0="g_1c189a0@0" Pin0InfoVect0LinkObjId="g_1c189a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1c17c30_0" Pin1InfoVect1LinkObjId="SW-193277_0" Pin1InfoVect2LinkObjId="SW-193278_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4874,-1097 4892,-1097 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bf7b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4728,-1209 4747,-1209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="19820@1" ObjectIDZND0="19818@1" Pin0InfoVect0LinkObjId="SW-94042_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-94044_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4728,-1209 4747,-1209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bf7d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4774,-1209 4792,-1209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="19818@0" ObjectIDZND0="19819@0" Pin0InfoVect0LinkObjId="SW-94043_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-94042_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4774,-1209 4792,-1209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bf7fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4851,-1151 4851,-1136 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="29328@0" ObjectIDZND0="g_1bf8990@0" Pin0InfoVect0LinkObjId="g_1bf8990_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-200315_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4851,-1151 4851,-1136 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bf8240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4952,-1258 4975,-1258 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_1bf6850@0" ObjectIDZND0="g_1bf70b0@0" Pin0InfoVect0LinkObjId="g_1bf70b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bf6850_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4952,-1258 4975,-1258 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bb0880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4851,-1187 4851,-1209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="29328@1" ObjectIDZND0="19819@x" ObjectIDZND1="g_1bbf920@0" ObjectIDZND2="g_1bf6850@0" Pin0InfoVect0LinkObjId="SW-94043_0" Pin0InfoVect1LinkObjId="g_1bbf920_0" Pin0InfoVect2LinkObjId="g_1bf6850_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-200315_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4851,-1187 4851,-1209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bb0ae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4828,-1209 4851,-1209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="19819@1" ObjectIDZND0="29328@x" ObjectIDZND1="g_1bbf920@0" ObjectIDZND2="g_1bf6850@0" Pin0InfoVect0LinkObjId="SW-200315_0" Pin0InfoVect1LinkObjId="g_1bbf920_0" Pin0InfoVect2LinkObjId="g_1bf6850_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-94043_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4828,-1209 4851,-1209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bb0d40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4874,-1187 4874,-1209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_1bbf920@0" ObjectIDZND0="29328@x" ObjectIDZND1="19819@x" ObjectIDZND2="g_1bf6850@0" Pin0InfoVect0LinkObjId="SW-200315_0" Pin0InfoVect1LinkObjId="SW-94043_0" Pin0InfoVect2LinkObjId="g_1bf6850_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bbf920_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4874,-1187 4874,-1209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bb0fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4851,-1209 4874,-1209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="29328@x" ObjectIDND1="19819@x" ObjectIDZND0="g_1bbf920@0" ObjectIDZND1="g_1bf6850@0" Pin0InfoVect0LinkObjId="g_1bbf920_0" Pin0InfoVect1LinkObjId="g_1bf6850_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-200315_0" Pin1InfoVect1LinkObjId="SW-94043_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4851,-1209 4874,-1209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bb1200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4892,-1209 5073,-1209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" ObjectIDND0="g_1bbf920@0" ObjectIDND1="29328@x" ObjectIDND2="19819@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1bbf920_0" Pin1InfoVect1LinkObjId="SW-200315_0" Pin1InfoVect2LinkObjId="SW-94043_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4892,-1209 5073,-1209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bb1460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4921,-1258 4892,-1258 4892,-1209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1bf6850@1" ObjectIDZND0="g_1bbf920@0" ObjectIDZND1="29328@x" ObjectIDZND2="19819@x" Pin0InfoVect0LinkObjId="g_1bbf920_0" Pin0InfoVect1LinkObjId="SW-200315_0" Pin0InfoVect2LinkObjId="SW-94043_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bf6850_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4921,-1258 4892,-1258 4892,-1209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bb16c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4874,-1209 4892,-1209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_1bbf920@0" ObjectIDND1="29328@x" ObjectIDND2="19819@x" ObjectIDZND0="g_1bf6850@0" Pin0InfoVect0LinkObjId="g_1bf6850_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1bbf920_0" Pin1InfoVect1LinkObjId="SW-200315_0" Pin1InfoVect2LinkObjId="SW-94043_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4874,-1209 4892,-1209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bb4f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4469,-731 4446,-731 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1bb46c0@0" ObjectIDZND0="g_1bb1920@0" Pin0InfoVect0LinkObjId="g_1bb1920_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bb46c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4469,-731 4446,-731 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bb5f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4632,-757 4594,-757 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="29321@0" ObjectIDZND0="29369@1" Pin0InfoVect0LinkObjId="SW-193275_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c7fc60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4632,-757 4594,-757 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bb86a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4537,-757 4558,-757 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_1bb46c0@0" ObjectIDND1="g_1bb51a0@0" ObjectIDZND0="29369@0" Pin0InfoVect0LinkObjId="SW-193275_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1bb46c0_0" Pin1InfoVect1LinkObjId="g_1bb51a0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4537,-757 4558,-757 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_1bbb100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4118,-348 4118,-380 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="19827@0" ObjectIDZND0="29330@0" Pin0InfoVect0LinkObjId="SW-200313_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_200be70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4118,-348 4118,-380 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d4b0f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4690,-631 4632,-631 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29384@0" ObjectIDZND0="29321@0" Pin0InfoVect0LinkObjId="g_1c7fc60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193290_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4690,-631 4632,-631 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d4d980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4868,-631 4810,-631 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1d4b350@1" ObjectIDZND0="g_1d4d100@0" Pin0InfoVect0LinkObjId="g_1d4d100_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d4b350_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4868,-631 4810,-631 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d4dbe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4779,-631 4726,-631 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1d4d100@1" ObjectIDZND0="29384@1" Pin0InfoVect0LinkObjId="SW-193290_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d4d100_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4779,-631 4726,-631 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fb8380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5060,-325 5060,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29367@1" ObjectIDZND0="19826@0" Pin0InfoVect0LinkObjId="g_1c2c710_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193273_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5060,-325 5060,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fb85e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5031,-172 5031,-193 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1fb8aa0@0" ObjectIDZND0="g_1beff00@0" Pin0InfoVect0LinkObjId="g_1beff00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fb8aa0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5031,-172 5031,-193 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fb8840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5031,-224 5031,-237 5060,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1beff00@1" ObjectIDZND0="g_1a27030@0" ObjectIDZND1="29367@x" ObjectIDZND2="29401@x" Pin0InfoVect0LinkObjId="g_1a27030_0" Pin0InfoVect1LinkObjId="SW-193273_0" Pin0InfoVect2LinkObjId="SW-193304_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1beff00_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5031,-224 5031,-237 5060,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fbebc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5060,-237 5060,-264 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1a27030@0" ObjectIDND1="g_1beff00@0" ObjectIDZND0="29367@x" ObjectIDZND1="29401@x" Pin0InfoVect0LinkObjId="SW-193273_0" Pin0InfoVect1LinkObjId="SW-193304_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1a27030_0" Pin1InfoVect1LinkObjId="g_1beff00_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5060,-237 5060,-264 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fbee20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5060,-264 5060,-289 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_1a27030@0" ObjectIDND1="g_1beff00@0" ObjectIDND2="29401@x" ObjectIDZND0="29367@0" Pin0InfoVect0LinkObjId="SW-193273_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1a27030_0" Pin1InfoVect1LinkObjId="g_1beff00_0" Pin1InfoVect2LinkObjId="SW-193304_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5060,-264 5060,-289 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fbf080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5123,-264 5114,-264 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1fbd920@0" ObjectIDZND0="29401@1" Pin0InfoVect0LinkObjId="SW-193304_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fbd920_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5123,-264 5114,-264 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fbf2e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5078,-264 5060,-264 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="29401@0" ObjectIDZND0="g_1a27030@0" ObjectIDZND1="g_1beff00@0" ObjectIDZND2="29367@x" Pin0InfoVect0LinkObjId="g_1a27030_0" Pin0InfoVect1LinkObjId="g_1beff00_0" Pin0InfoVect2LinkObjId="SW-193273_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193304_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5078,-264 5060,-264 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fbfaf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4955,-333 4955,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29364@1" ObjectIDZND0="19826@0" Pin0InfoVect0LinkObjId="g_1c2c710_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193269_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4955,-333 4955,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fc75f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4924,-224 4955,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="g_1fc6900@0" ObjectIDZND0="29365@x" ObjectIDZND1="29398@x" Pin0InfoVect0LinkObjId="SW-193270_0" Pin0InfoVect1LinkObjId="SW-193254_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fc6900_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4924,-224 4955,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fc8240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5018,-281 5009,-281 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1fc7850@0" ObjectIDZND0="29363@1" Pin0InfoVect0LinkObjId="SW-193268_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fc7850_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5018,-281 5009,-281 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fc84a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4973,-281 4955,-281 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="29363@0" ObjectIDZND0="29398@x" ObjectIDZND1="29364@x" Pin0InfoVect0LinkObjId="SW-193254_0" Pin0InfoVect1LinkObjId="SW-193269_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193268_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4973,-281 4955,-281 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fcb2c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4955,-265 4955,-281 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="29398@1" ObjectIDZND0="29363@x" ObjectIDZND1="29364@x" Pin0InfoVect0LinkObjId="SW-193268_0" Pin0InfoVect1LinkObjId="SW-193269_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193254_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4955,-265 4955,-281 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fcb520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4955,-281 4955,-297 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="29363@x" ObjectIDND1="29398@x" ObjectIDZND0="29364@0" Pin0InfoVect0LinkObjId="SW-193269_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-193268_0" Pin1InfoVect1LinkObjId="SW-193254_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4955,-281 4955,-297 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fcbff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4955,-210 4955,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="29365@1" ObjectIDZND0="g_1fc6900@0" ObjectIDZND1="29398@x" Pin0InfoVect0LinkObjId="g_1fc6900_0" Pin0InfoVect1LinkObjId="SW-193254_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193270_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4955,-210 4955,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fcc230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4955,-224 4955,-238 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="g_1fc6900@0" ObjectIDND1="29365@x" ObjectIDZND0="29398@0" Pin0InfoVect0LinkObjId="SW-193254_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1fc6900_0" Pin1InfoVect1LinkObjId="SW-193270_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4955,-224 4955,-238 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fcf3d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4955,-6 4955,-39 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="29366@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4955,-6 4955,-39 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fcf630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4955,-75 4955,-174 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="29366@1" ObjectIDZND0="29365@0" Pin0InfoVect0LinkObjId="SW-193270_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4955,-75 4955,-174 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fd01a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4852,-333 4852,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29356@1" ObjectIDZND0="19826@0" Pin0InfoVect0LinkObjId="g_1c2c710_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193265_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4852,-333 4852,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fd74d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4821,-224 4852,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="g_1fd67e0@0" ObjectIDZND0="29357@x" ObjectIDZND1="29397@x" Pin0InfoVect0LinkObjId="SW-193266_0" Pin0InfoVect1LinkObjId="SW-193252_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fd67e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4821,-224 4852,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fd8120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4915,-281 4906,-281 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1fd7730@0" ObjectIDZND0="29355@1" Pin0InfoVect0LinkObjId="SW-193264_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fd7730_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4915,-281 4906,-281 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fd8380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4870,-281 4852,-281 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="29355@0" ObjectIDZND0="29397@x" ObjectIDZND1="29356@x" Pin0InfoVect0LinkObjId="SW-193252_0" Pin0InfoVect1LinkObjId="SW-193265_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193264_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4870,-281 4852,-281 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fda930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4852,-265 4852,-281 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="29397@1" ObjectIDZND0="29355@x" ObjectIDZND1="29356@x" Pin0InfoVect0LinkObjId="SW-193264_0" Pin0InfoVect1LinkObjId="SW-193265_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193252_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4852,-265 4852,-281 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fdab90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4852,-281 4852,-297 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="29397@x" ObjectIDND1="29355@x" ObjectIDZND0="29356@0" Pin0InfoVect0LinkObjId="SW-193265_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-193252_0" Pin1InfoVect1LinkObjId="SW-193264_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4852,-281 4852,-297 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fdadf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4852,-210 4852,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="29357@1" ObjectIDZND0="g_1fd67e0@0" ObjectIDZND1="29397@x" Pin0InfoVect0LinkObjId="g_1fd67e0_0" Pin0InfoVect1LinkObjId="SW-193252_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193266_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4852,-210 4852,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fdb050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4852,-224 4852,-238 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="breaker" ObjectIDND0="29357@x" ObjectIDND1="g_1fd67e0@0" ObjectIDZND0="29397@0" Pin0InfoVect0LinkObjId="SW-193252_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-193266_0" Pin1InfoVect1LinkObjId="g_1fd67e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4852,-224 4852,-238 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fde1f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4852,-6 4852,-39 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="29358@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4852,-6 4852,-39 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fde450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4852,-75 4852,-174 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="29358@1" ObjectIDZND0="29357@0" Pin0InfoVect0LinkObjId="SW-193266_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4852,-75 4852,-174 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fdff30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4747,-333 4747,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29360@1" ObjectIDZND0="19826@0" Pin0InfoVect0LinkObjId="g_1c2c710_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193271_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4747,-333 4747,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fe7560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4716,-224 4747,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="g_1fe67f0@0" ObjectIDZND0="29361@x" ObjectIDZND1="29396@x" Pin0InfoVect0LinkObjId="SW-193272_0" Pin0InfoVect1LinkObjId="SW-193253_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fe67f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4716,-224 4747,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fe8250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4810,-281 4801,-281 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1fe77c0@0" ObjectIDZND0="29359@1" Pin0InfoVect0LinkObjId="SW-193267_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fe77c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4810,-281 4801,-281 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fe84b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4765,-281 4747,-281 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="29359@0" ObjectIDZND0="29396@x" ObjectIDZND1="29360@x" Pin0InfoVect0LinkObjId="SW-193253_0" Pin0InfoVect1LinkObjId="SW-193271_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193267_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4765,-281 4747,-281 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1feac40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4747,-265 4747,-281 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="29396@1" ObjectIDZND0="29359@x" ObjectIDZND1="29360@x" Pin0InfoVect0LinkObjId="SW-193267_0" Pin0InfoVect1LinkObjId="SW-193271_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193253_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4747,-265 4747,-281 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1feaea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4747,-281 4747,-297 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="29396@x" ObjectIDND1="29359@x" ObjectIDZND0="29360@0" Pin0InfoVect0LinkObjId="SW-193271_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-193253_0" Pin1InfoVect1LinkObjId="SW-193267_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4747,-281 4747,-297 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1feb100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4747,-210 4747,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="29361@1" ObjectIDZND0="g_1fe67f0@0" ObjectIDZND1="29396@x" Pin0InfoVect0LinkObjId="g_1fe67f0_0" Pin0InfoVect1LinkObjId="SW-193253_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193272_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4747,-210 4747,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1feb360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4747,-224 4747,-238 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="breaker" ObjectIDND0="29361@x" ObjectIDND1="g_1fe67f0@0" ObjectIDZND0="29396@0" Pin0InfoVect0LinkObjId="SW-193253_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-193272_0" Pin1InfoVect1LinkObjId="g_1fe67f0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4747,-224 4747,-238 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fee800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4747,-6 4747,-39 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="29362@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4747,-6 4747,-39 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1feea60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4747,-75 4747,-174 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="29362@1" ObjectIDZND0="29361@0" Pin0InfoVect0LinkObjId="SW-193272_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4747,-75 4747,-174 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ff1720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4638,-333 4638,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29353@1" ObjectIDZND0="19826@0" Pin0InfoVect0LinkObjId="g_1c2c710_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193262_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4638,-333 4638,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ff9560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4607,-224 4638,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="g_1ff87f0@0" ObjectIDZND0="29354@x" ObjectIDZND1="29395@x" Pin0InfoVect0LinkObjId="SW-193263_0" Pin0InfoVect1LinkObjId="SW-193251_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1ff87f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4607,-224 4638,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ffa250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4701,-281 4692,-281 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1ff97c0@0" ObjectIDZND0="29348@1" Pin0InfoVect0LinkObjId="SW-193261_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1ff97c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4701,-281 4692,-281 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ffa4b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4656,-281 4638,-281 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="29348@0" ObjectIDZND0="29395@x" ObjectIDZND1="29353@x" Pin0InfoVect0LinkObjId="SW-193251_0" Pin0InfoVect1LinkObjId="SW-193262_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193261_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4656,-281 4638,-281 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ffcc40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4638,-265 4638,-281 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="29395@1" ObjectIDZND0="29348@x" ObjectIDZND1="29353@x" Pin0InfoVect0LinkObjId="SW-193261_0" Pin0InfoVect1LinkObjId="SW-193262_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193251_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4638,-265 4638,-281 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ffcea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4638,-281 4638,-297 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="29395@x" ObjectIDND1="29348@x" ObjectIDZND0="29353@0" Pin0InfoVect0LinkObjId="SW-193262_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-193251_0" Pin1InfoVect1LinkObjId="SW-193261_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4638,-281 4638,-297 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ffd100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4638,-210 4638,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="29354@1" ObjectIDZND0="g_1ff87f0@0" ObjectIDZND1="29395@x" Pin0InfoVect0LinkObjId="g_1ff87f0_0" Pin0InfoVect1LinkObjId="SW-193251_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193263_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4638,-210 4638,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ffd360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4638,-224 4638,-238 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="breaker" ObjectIDND0="29354@x" ObjectIDND1="g_1ff87f0@0" ObjectIDZND0="29395@0" Pin0InfoVect0LinkObjId="SW-193251_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-193263_0" Pin1InfoVect1LinkObjId="g_1ff87f0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4638,-224 4638,-238 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2000f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4638,-6 4638,-39 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="EC-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4638,-6 4638,-39 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2001130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4638,-75 4638,-174 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="29354@0" Pin0InfoVect0LinkObjId="SW-193263_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4638,-75 4638,-174 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2003a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4598,-281 4589,-281 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2003090@0" ObjectIDZND0="29394@1" Pin0InfoVect0LinkObjId="SW-193300_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2003090_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4598,-281 4589,-281 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2006080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4553,-281 4535,-281 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="29394@0" ObjectIDZND0="29336@x" ObjectIDZND1="29393@x" Pin0InfoVect0LinkObjId="SW-193259_0" Pin0InfoVect1LinkObjId="SW-193301_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193300_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4553,-281 4535,-281 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2006b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4535,-265 4535,-281 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="29336@1" ObjectIDZND0="29394@x" ObjectIDZND1="29393@x" Pin0InfoVect0LinkObjId="SW-193300_0" Pin0InfoVect1LinkObjId="SW-193301_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193259_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4535,-265 4535,-281 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2006dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4535,-281 4535,-295 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="29394@x" ObjectIDND1="29336@x" ObjectIDZND0="29393@0" Pin0InfoVect0LinkObjId="SW-193301_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-193300_0" Pin1InfoVect1LinkObjId="SW-193259_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4535,-281 4535,-295 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_200a8e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4535,-106 4535,-175 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="29392@0" Pin0InfoVect0LinkObjId="SW-193302_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4535,-106 4535,-175 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_200be70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4398,-330 4398,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29391@1" ObjectIDZND0="19827@0" Pin0InfoVect0LinkObjId="g_201e070_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193305_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4398,-330 4398,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_20112e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4461,-280 4452,-280 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2010850@0" ObjectIDZND0="29390@1" Pin0InfoVect0LinkObjId="SW-193303_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2010850_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4461,-280 4452,-280 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_2013a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4416,-280 4398,-280 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="29390@0" ObjectIDZND0="29347@x" ObjectIDZND1="29391@x" Pin0InfoVect0LinkObjId="SW-193260_0" Pin0InfoVect1LinkObjId="SW-193305_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193303_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4416,-280 4398,-280 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_2013cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4398,-264 4398,-280 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="29347@1" ObjectIDZND0="29390@x" ObjectIDZND1="29391@x" Pin0InfoVect0LinkObjId="SW-193303_0" Pin0InfoVect1LinkObjId="SW-193305_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193260_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4398,-264 4398,-280 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_2013f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4398,-280 4398,-294 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="29347@x" ObjectIDND1="29390@x" ObjectIDZND0="29391@0" Pin0InfoVect0LinkObjId="SW-193305_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-193260_0" Pin1InfoVect1LinkObjId="SW-193303_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4398,-280 4398,-294 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_2017700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4367,-224 4398,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="g_2016990@0" ObjectIDZND0="29389@x" ObjectIDZND1="29347@x" Pin0InfoVect0LinkObjId="SW-200314_0" Pin0InfoVect1LinkObjId="SW-193260_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2016990_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4367,-224 4398,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_2017960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4398,-210 4398,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="29389@1" ObjectIDZND0="g_2016990@0" ObjectIDZND1="29347@x" Pin0InfoVect0LinkObjId="g_2016990_0" Pin0InfoVect1LinkObjId="SW-193260_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-200314_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4398,-210 4398,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_2017d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4398,-224 4398,-238 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="breaker" ObjectIDND0="29389@x" ObjectIDND1="g_2016990@0" ObjectIDZND0="29347@0" Pin0InfoVect0LinkObjId="SW-193260_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-200314_0" Pin1InfoVect1LinkObjId="g_2016990_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4398,-224 4398,-238 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_20196b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4398,-174 4398,-34 4535,-34 4535,-64 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="29389@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="EC-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-200314_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4398,-174 4398,-34 4535,-34 4535,-64 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_201e070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4289,-330 4289,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29388@1" ObjectIDZND0="19827@0" Pin0InfoVect0LinkObjId="g_200be70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193299_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4289,-330 4289,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_201f120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4289,-131 4289,-202 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2019ac0@1" ObjectIDZND0="g_201e8a0@0" Pin0InfoVect0LinkObjId="g_201e8a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2019ac0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4289,-131 4289,-202 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_201fe10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4345,-266 4336,-266 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_201f380@0" ObjectIDZND0="29399@1" Pin0InfoVect0LinkObjId="SW-193294_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_201f380_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4345,-266 4336,-266 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_20225a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4300,-266 4289,-266 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="29399@0" ObjectIDZND0="g_201e8a0@0" ObjectIDZND1="29388@x" Pin0InfoVect0LinkObjId="g_201e8a0_0" Pin0InfoVect1LinkObjId="SW-193299_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193294_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4300,-266 4289,-266 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_2023090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4289,-230 4289,-266 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_201e8a0@1" ObjectIDZND0="29399@x" ObjectIDZND1="29388@x" Pin0InfoVect0LinkObjId="SW-193294_0" Pin0InfoVect1LinkObjId="SW-193299_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_201e8a0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4289,-230 4289,-266 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_20232f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4289,-266 4289,-294 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="29399@x" ObjectIDND1="g_201e8a0@0" ObjectIDZND0="29388@0" Pin0InfoVect0LinkObjId="SW-193299_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-193294_0" Pin1InfoVect1LinkObjId="g_201e8a0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4289,-266 4289,-294 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_2023b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4095,-330 4095,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="19777@1" ObjectIDZND0="19827@0" Pin0InfoVect0LinkObjId="g_200be70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-93968_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4095,-330 4095,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_202abd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4095,-234 4095,-273 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="19776@1" ObjectIDZND0="19777@x" ObjectIDZND1="29320@x" Pin0InfoVect0LinkObjId="SW-93968_0" Pin0InfoVect1LinkObjId="SW-193293_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-93967_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4095,-234 4095,-273 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_202ae30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4095,-272 4095,-294 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="19776@x" ObjectIDND1="29320@x" ObjectIDZND0="19777@0" Pin0InfoVect0LinkObjId="SW-93968_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-93967_0" Pin1InfoVect1LinkObjId="SW-193293_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4095,-272 4095,-294 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_202b090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4080,-178 4095,-178 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="hydroGenerator" EndDevType2="lightningRod" ObjectIDND0="g_202bb80@0" ObjectIDZND0="19776@x" ObjectIDZND1="0@x" ObjectIDZND2="g_2030820@0" Pin0InfoVect0LinkObjId="SW-93967_0" Pin0InfoVect1LinkObjId="EC-0_0" Pin0InfoVect2LinkObjId="g_2030820_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_202bb80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4080,-178 4095,-178 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_202e5d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4095,-178 4095,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="hydroGenerator" BeginDevType2="lightningRod" EndDevType0="breaker" ObjectIDND0="g_202bb80@0" ObjectIDND1="0@x" ObjectIDND2="g_2030820@0" ObjectIDZND0="19776@0" Pin0InfoVect0LinkObjId="SW-93967_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_202bb80_0" Pin1InfoVect1LinkObjId="EC-0_0" Pin1InfoVect2LinkObjId="g_2030820_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4095,-178 4095,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_202e830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4095,-28 4095,-178 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="hydroGenerator" EndDevType0="lightningRod" EndDevType1="breaker" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_202bb80@0" ObjectIDZND1="19776@x" ObjectIDZND2="g_2030820@0" Pin0InfoVect0LinkObjId="g_202bb80_0" Pin0InfoVect1LinkObjId="SW-93967_0" Pin0InfoVect2LinkObjId="g_2030820_0" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4095,-28 4095,-178 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_20310a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4132,-81 4132,-97 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_202ea90@0" ObjectIDZND0="g_2030820@0" Pin0InfoVect0LinkObjId="g_2030820_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_202ea90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4132,-81 4132,-97 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_2031b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4172,-67 4172,-83 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_2031300@0" Pin0InfoVect0LinkObjId="g_2031300_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4172,-67 4172,-83 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_20357c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4172,-125 4172,-114 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="19778@0" ObjectIDZND0="g_2031300@1" Pin0InfoVect0LinkObjId="g_2031300_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-93969_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4172,-125 4172,-114 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_2035a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4132,-128 4132,-178 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="breaker" EndDevType2="hydroGenerator" ObjectIDND0="g_2030820@1" ObjectIDZND0="g_202bb80@0" ObjectIDZND1="19776@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_202bb80_0" Pin0InfoVect1LinkObjId="SW-93967_0" Pin0InfoVect2LinkObjId="EC-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2030820_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4132,-128 4132,-178 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_2036a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4095,-178 4132,-178 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="hydroGenerator" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_202bb80@0" ObjectIDND1="19776@x" ObjectIDND2="0@x" ObjectIDZND0="g_2030820@0" ObjectIDZND1="19778@x" Pin0InfoVect0LinkObjId="g_2030820_0" Pin0InfoVect1LinkObjId="SW-93969_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_202bb80_0" Pin1InfoVect1LinkObjId="SW-93967_0" Pin1InfoVect2LinkObjId="EC-0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4095,-178 4132,-178 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_2036c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4132,-178 4172,-178 4172,-161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="breaker" EndDevType0="switch" ObjectIDND0="g_2030820@0" ObjectIDND1="g_202bb80@0" ObjectIDND2="19776@x" ObjectIDZND0="19778@1" Pin0InfoVect0LinkObjId="SW-93969_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2030820_0" Pin1InfoVect1LinkObjId="g_202bb80_0" Pin1InfoVect2LinkObjId="SW-93967_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4132,-178 4172,-178 4172,-161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_20380e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4035,-273 4044,-273 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_20378e0@0" ObjectIDZND0="29320@0" Pin0InfoVect0LinkObjId="SW-193293_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20378e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4035,-273 4044,-273 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_2038340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4080,-273 4095,-273 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="29320@1" ObjectIDZND0="19776@x" ObjectIDZND1="19777@x" Pin0InfoVect0LinkObjId="SW-93967_0" Pin0InfoVect1LinkObjId="SW-93968_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193293_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4080,-273 4095,-273 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_2038750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3858,-330 3858,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="19774@1" ObjectIDZND0="19827@0" Pin0InfoVect0LinkObjId="g_200be70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-93965_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3858,-330 3858,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_203fb70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3858,-234 3858,-273 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="19773@1" ObjectIDZND0="19774@x" ObjectIDZND1="29319@x" Pin0InfoVect0LinkObjId="SW-93965_0" Pin0InfoVect1LinkObjId="SW-193292_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-93964_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3858,-234 3858,-273 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_203fdd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3858,-272 3858,-294 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="19773@x" ObjectIDND1="29319@x" ObjectIDZND0="19774@0" Pin0InfoVect0LinkObjId="SW-93965_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-93964_0" Pin1InfoVect1LinkObjId="SW-193292_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3858,-272 3858,-294 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_2040030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3843,-178 3858,-178 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="hydroGenerator" EndDevType2="lightningRod" ObjectIDND0="g_2040290@0" ObjectIDZND0="19773@x" ObjectIDZND1="0@x" ObjectIDZND2="g_2044480@0" Pin0InfoVect0LinkObjId="SW-93964_0" Pin0InfoVect1LinkObjId="EC-0_0" Pin0InfoVect2LinkObjId="g_2044480_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2040290_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3843,-178 3858,-178 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_2042230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3858,-178 3858,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="hydroGenerator" BeginDevType2="lightningRod" EndDevType0="breaker" ObjectIDND0="g_2040290@0" ObjectIDND1="0@x" ObjectIDND2="g_2044480@0" ObjectIDZND0="19773@0" Pin0InfoVect0LinkObjId="SW-93964_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2040290_0" Pin1InfoVect1LinkObjId="EC-0_0" Pin1InfoVect2LinkObjId="g_2044480_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3858,-178 3858,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_2042490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3858,-28 3858,-178 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="hydroGenerator" EndDevType0="lightningRod" EndDevType1="breaker" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_2040290@0" ObjectIDZND1="19773@x" ObjectIDZND2="g_2044480@0" Pin0InfoVect0LinkObjId="g_2040290_0" Pin0InfoVect1LinkObjId="SW-93964_0" Pin0InfoVect2LinkObjId="g_2044480_0" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3858,-28 3858,-178 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_2044d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3895,-81 3895,-97 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_20426f0@0" ObjectIDZND0="g_2044480@0" Pin0InfoVect0LinkObjId="g_2044480_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20426f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3895,-81 3895,-97 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_20457e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3935,-67 3935,-83 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_2044f60@0" Pin0InfoVect0LinkObjId="g_2044f60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3935,-67 3935,-83 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_2049420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3935,-125 3935,-114 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="19775@0" ObjectIDZND0="g_2044f60@1" Pin0InfoVect0LinkObjId="g_2044f60_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-93966_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3935,-125 3935,-114 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_2049680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3895,-128 3895,-178 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="breaker" EndDevType2="hydroGenerator" ObjectIDND0="g_2044480@1" ObjectIDZND0="g_2040290@0" ObjectIDZND1="19773@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_2040290_0" Pin0InfoVect1LinkObjId="SW-93964_0" Pin0InfoVect2LinkObjId="EC-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2044480_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3895,-128 3895,-178 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_2049f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3858,-178 3895,-178 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="hydroGenerator" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2040290@0" ObjectIDND1="19773@x" ObjectIDND2="0@x" ObjectIDZND0="g_2044480@0" ObjectIDZND1="19775@x" Pin0InfoVect0LinkObjId="g_2044480_0" Pin0InfoVect1LinkObjId="SW-93966_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2040290_0" Pin1InfoVect1LinkObjId="SW-93964_0" Pin1InfoVect2LinkObjId="EC-0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3858,-178 3895,-178 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_204a100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3895,-178 3935,-178 3935,-161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="breaker" EndDevType0="switch" ObjectIDND0="g_2044480@0" ObjectIDND1="g_2040290@0" ObjectIDND2="19773@x" ObjectIDZND0="19775@1" Pin0InfoVect0LinkObjId="SW-93966_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2044480_0" Pin1InfoVect1LinkObjId="g_2040290_0" Pin1InfoVect2LinkObjId="SW-93964_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3895,-178 3935,-178 3935,-161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_204adf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3798,-273 3807,-273 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_204a610@0" ObjectIDZND0="29319@0" Pin0InfoVect0LinkObjId="SW-193292_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_204a610_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3798,-273 3807,-273 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_204b000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3843,-273 3858,-273 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="29319@1" ObjectIDZND0="19773@x" ObjectIDZND1="19774@x" Pin0InfoVect0LinkObjId="SW-93964_0" Pin0InfoVect1LinkObjId="SW-93965_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193292_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3843,-273 3858,-273 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_204dc20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3617,-330 3617,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="19771@1" ObjectIDZND0="19827@0" Pin0InfoVect0LinkObjId="g_200be70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-93962_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3617,-330 3617,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_2055040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3617,-234 3617,-273 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="19770@1" ObjectIDZND0="19771@x" ObjectIDZND1="29318@x" Pin0InfoVect0LinkObjId="SW-93962_0" Pin0InfoVect1LinkObjId="SW-193291_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-93961_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3617,-234 3617,-273 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_20552a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3617,-272 3617,-294 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="19770@x" ObjectIDND1="29318@x" ObjectIDZND0="19771@0" Pin0InfoVect0LinkObjId="SW-93962_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-93961_0" Pin1InfoVect1LinkObjId="SW-193291_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3617,-272 3617,-294 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_2055500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3602,-178 3617,-178 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_2055760@0" ObjectIDZND0="19770@x" ObjectIDZND1="g_2059950@0" ObjectIDZND2="19772@x" Pin0InfoVect0LinkObjId="SW-93961_0" Pin0InfoVect1LinkObjId="g_2059950_0" Pin0InfoVect2LinkObjId="SW-93963_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2055760_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3602,-178 3617,-178 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_2057700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3617,-178 3617,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="breaker" ObjectIDND0="g_2055760@0" ObjectIDND1="g_2059950@0" ObjectIDND2="19772@x" ObjectIDZND0="19770@0" Pin0InfoVect0LinkObjId="SW-93961_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2055760_0" Pin1InfoVect1LinkObjId="g_2059950_0" Pin1InfoVect2LinkObjId="SW-93963_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3617,-178 3617,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_2057960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3617,-28 3617,-178 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="hydroGenerator" EndDevType0="breaker" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="19770@x" ObjectIDZND1="g_2055760@0" ObjectIDZND2="g_2059950@0" Pin0InfoVect0LinkObjId="SW-93961_0" Pin0InfoVect1LinkObjId="g_2055760_0" Pin0InfoVect2LinkObjId="g_2059950_0" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3617,-28 3617,-178 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_205a1d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3654,-81 3654,-97 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_2057bc0@0" ObjectIDZND0="g_2059950@0" Pin0InfoVect0LinkObjId="g_2059950_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2057bc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3654,-81 3654,-97 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_205acb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3694,-67 3694,-83 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_205a430@0" Pin0InfoVect0LinkObjId="g_205a430_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3694,-67 3694,-83 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_205e8f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3694,-125 3694,-114 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="19772@0" ObjectIDZND0="g_205a430@1" Pin0InfoVect0LinkObjId="g_205a430_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-93963_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3694,-125 3694,-114 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_205eb50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3654,-128 3654,-178 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="lightningRod" EndDevType2="hydroGenerator" ObjectIDND0="g_2059950@1" ObjectIDZND0="19770@x" ObjectIDZND1="g_2055760@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-93961_0" Pin0InfoVect1LinkObjId="g_2055760_0" Pin0InfoVect2LinkObjId="EC-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2059950_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3654,-128 3654,-178 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_205f3e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3617,-178 3654,-178 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" BeginDevType2="hydroGenerator" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="19770@x" ObjectIDND1="g_2055760@0" ObjectIDND2="0@x" ObjectIDZND0="g_2059950@0" ObjectIDZND1="19772@x" Pin0InfoVect0LinkObjId="g_2059950_0" Pin0InfoVect1LinkObjId="SW-93963_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-93961_0" Pin1InfoVect1LinkObjId="g_2055760_0" Pin1InfoVect2LinkObjId="EC-0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3617,-178 3654,-178 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_205f5d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3654,-178 3694,-178 3694,-161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" BeginDevType2="hydroGenerator" EndDevType0="switch" ObjectIDND0="19770@x" ObjectIDND1="g_2055760@0" ObjectIDND2="0@x" ObjectIDZND0="19772@1" Pin0InfoVect0LinkObjId="SW-93963_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-93961_0" Pin1InfoVect1LinkObjId="g_2055760_0" Pin1InfoVect2LinkObjId="EC-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3654,-178 3694,-178 3694,-161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_20602c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3557,-273 3566,-273 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_205fae0@0" ObjectIDZND0="29318@0" Pin0InfoVect0LinkObjId="SW-193291_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_205fae0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3557,-273 3566,-273 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_20604d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3602,-273 3617,-273 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="29318@1" ObjectIDZND0="19771@x" ObjectIDZND1="19770@x" Pin0InfoVect0LinkObjId="SW-93962_0" Pin0InfoVect1LinkObjId="SW-93961_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193291_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3602,-273 3617,-273 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_206edc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3704,-348 3704,-366 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="19827@0" ObjectIDZND0="29387@0" Pin0InfoVect0LinkObjId="SW-193298_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_200be70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3704,-348 3704,-366 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_2072f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3881,-491 3881,-445 3919,-445 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_206f5f0@0" ObjectIDZND0="g_2072180@0" ObjectIDZND1="29385@x" ObjectIDZND2="29386@x" Pin0InfoVect0LinkObjId="g_2072180_0" Pin0InfoVect1LinkObjId="SW-193296_0" Pin0InfoVect2LinkObjId="SW-193297_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_206f5f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3881,-491 3881,-445 3919,-445 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_2076150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3859,-415 3868,-415 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_20756c0@0" ObjectIDZND0="29385@0" Pin0InfoVect0LinkObjId="SW-193296_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20756c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3859,-415 3868,-415 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_2076c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3950,-479 3950,-445 3919,-445 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2072180@0" ObjectIDZND0="g_206f5f0@0" ObjectIDZND1="29385@x" ObjectIDZND2="29386@x" Pin0InfoVect0LinkObjId="g_206f5f0_0" Pin0InfoVect1LinkObjId="SW-193296_0" Pin0InfoVect2LinkObjId="SW-193297_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2072180_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3950,-479 3950,-445 3919,-445 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_2076ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3919,-445 3919,-415 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_206f5f0@0" ObjectIDND1="g_2072180@0" ObjectIDZND0="29385@x" ObjectIDZND1="29386@x" Pin0InfoVect0LinkObjId="SW-193296_0" Pin0InfoVect1LinkObjId="SW-193297_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_206f5f0_0" Pin1InfoVect1LinkObjId="g_2072180_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3919,-445 3919,-415 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_2077990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3904,-415 3919,-415 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="29385@1" ObjectIDZND0="g_206f5f0@0" ObjectIDZND1="g_2072180@0" ObjectIDZND2="29386@x" Pin0InfoVect0LinkObjId="g_206f5f0_0" Pin0InfoVect1LinkObjId="g_2072180_0" Pin0InfoVect2LinkObjId="SW-193297_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193296_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3904,-415 3919,-415 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_2077bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3919,-415 3919,-400 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_206f5f0@0" ObjectIDND1="g_2072180@0" ObjectIDND2="29385@x" ObjectIDZND0="29386@1" Pin0InfoVect0LinkObjId="SW-193297_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_206f5f0_0" Pin1InfoVect1LinkObjId="g_2072180_0" Pin1InfoVect2LinkObjId="SW-193296_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3919,-415 3919,-400 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_207c1d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3644,-415 3653,-415 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_207b740@0" ObjectIDZND0="29400@0" Pin0InfoVect0LinkObjId="SW-193295_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_207b740_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3644,-415 3653,-415 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_207c430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3689,-415 3704,-415 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="capacitor" ObjectIDND0="29400@1" ObjectIDZND0="29387@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-193298_0" Pin0InfoVect1LinkObjId="EC-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193295_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3689,-415 3704,-415 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_207cf20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3704,-402 3704,-415 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="capacitor" ObjectIDND0="29387@1" ObjectIDZND0="29400@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-193295_0" Pin0InfoVect1LinkObjId="EC-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193298_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3704,-402 3704,-415 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_207d180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3704,-415 3704,-474 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="capacitor" ObjectIDND0="29400@x" ObjectIDND1="29387@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="EC-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-193295_0" Pin1InfoVect1LinkObjId="SW-193298_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3704,-415 3704,-474 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_207fae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4500,-731 4537,-731 4537,-757 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_1bb46c0@1" ObjectIDZND0="29369@x" ObjectIDZND1="g_1bb51a0@0" Pin0InfoVect0LinkObjId="SW-193275_0" Pin0InfoVect1LinkObjId="g_1bb51a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bb46c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4500,-731 4537,-731 4537,-757 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_207fd00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4537,-757 4537,-796 4502,-796 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="29369@x" ObjectIDND1="g_1bb46c0@0" ObjectIDZND0="g_1bb51a0@0" Pin0InfoVect0LinkObjId="g_1bb51a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-193275_0" Pin1InfoVect1LinkObjId="g_1bb46c0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4537,-757 4537,-796 4502,-796 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2088bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4469,-919 4446,-919 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2088330@0" ObjectIDZND0="g_2085a30@0" Pin0InfoVect0LinkObjId="g_2085a30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2088330_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4469,-919 4446,-919 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_208c0b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4537,-945 4558,-945 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_2088330@0" ObjectIDND1="g_2088e10@0" ObjectIDZND0="29368@0" Pin0InfoVect0LinkObjId="SW-193274_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2088330_0" Pin1InfoVect1LinkObjId="g_2088e10_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4537,-945 4558,-945 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_208c310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4500,-919 4537,-919 4537,-945 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_2088330@1" ObjectIDZND0="29368@x" ObjectIDZND1="g_2088e10@0" Pin0InfoVect0LinkObjId="SW-193274_0" Pin0InfoVect1LinkObjId="g_2088e10_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2088330_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4500,-919 4537,-919 4537,-945 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_208c570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4537,-945 4537,-984 4502,-984 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="29368@x" ObjectIDND1="g_2088330@0" ObjectIDZND0="g_2088e10@0" Pin0InfoVect0LinkObjId="g_2088e10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-193274_0" Pin1InfoVect1LinkObjId="g_2088330_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4537,-945 4537,-984 4502,-984 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_208d900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4692,-1209 4632,-1209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="19820@0" ObjectIDZND0="19825@0" Pin0InfoVect0LinkObjId="g_208daf0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-94044_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4692,-1209 4632,-1209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_208daf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4692,-1097 4632,-1097 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29370@0" ObjectIDZND0="19825@0" Pin0InfoVect0LinkObjId="g_208d900_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193276_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4692,-1097 4632,-1097 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_208dce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4692,-984 4632,-984 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29375@0" ObjectIDZND0="19825@0" Pin0InfoVect0LinkObjId="g_208d900_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193281_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4692,-984 4632,-984 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_208df10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4689,-900 4632,-900 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29382@0" ObjectIDZND0="19825@0" Pin0InfoVect0LinkObjId="g_208d900_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193288_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4689,-900 4632,-900 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_208e140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4594,-945 4632,-945 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29368@1" ObjectIDZND0="19825@0" Pin0InfoVect0LinkObjId="g_208d900_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193274_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4594,-945 4632,-945 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2091a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4039,-657 4039,-669 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="transformer" ObjectIDND0="0@1" ObjectIDZND0="g_1c43460@0" ObjectIDZND1="g_1c59860@0" ObjectIDZND2="30500@x" Pin0InfoVect0LinkObjId="g_1c43460_0" Pin0InfoVect1LinkObjId="g_1c59860_0" Pin0InfoVect2LinkObjId="g_1c43080_0" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4039,-657 4039,-669 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_20924f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4039,-669 4010,-669 4010,-659 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="transformer" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_1c59860@0" ObjectIDND2="30500@x" ObjectIDZND0="g_1c43460@0" Pin0InfoVect0LinkObjId="g_1c43460_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="EC-0_0" Pin1InfoVect1LinkObjId="g_1c59860_0" Pin1InfoVect2LinkObjId="g_1c43080_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4039,-669 4010,-669 4010,-659 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2092750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4067,-656 4067,-669 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_1c59860@0" ObjectIDZND0="30500@x" ObjectIDZND1="0@x" ObjectIDZND2="g_1c43460@0" Pin0InfoVect0LinkObjId="g_1c43080_0" Pin0InfoVect1LinkObjId="EC-0_0" Pin0InfoVect2LinkObjId="g_1c43460_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c59860_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4067,-656 4067,-669 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2093240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4133,-669 4067,-669 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="30500@x" ObjectIDZND0="g_1c59860@0" ObjectIDZND1="0@x" ObjectIDZND2="g_1c43460@0" Pin0InfoVect0LinkObjId="g_1c59860_0" Pin0InfoVect1LinkObjId="EC-0_0" Pin0InfoVect2LinkObjId="g_1c43460_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c43080_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4133,-669 4067,-669 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_20934a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4067,-669 4039,-669 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_1c59860@0" ObjectIDND1="30500@x" ObjectIDZND0="0@x" ObjectIDZND1="g_1c43460@0" Pin0InfoVect0LinkObjId="EC-0_0" Pin0InfoVect1LinkObjId="g_1c43460_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1c59860_0" Pin1InfoVect1LinkObjId="g_1c43080_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4067,-669 4039,-669 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2093700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4133,-686 4133,-784 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" ObjectIDND0="30500@2" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c43080_2" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4133,-686 4133,-784 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2093960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4305,-562 4152,-562 4152,-608 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer" ObjectIDND0="19823@0" ObjectIDZND0="30500@0" Pin0InfoVect0LinkObjId="g_1c43080_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-94046_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4305,-562 4152,-562 4152,-608 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2093bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4450,-562 4632,-562 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="19824@1" ObjectIDZND0="29321@0" Pin0InfoVect0LinkObjId="g_1c7fc60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-94047_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4450,-562 4632,-562 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_2093e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3919,-364 3919,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29386@0" ObjectIDZND0="19827@0" Pin0InfoVect0LinkObjId="g_200be70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193297_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3919,-364 3919,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_20a0a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4504,-225 4535,-225 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="g_2009830@0" ObjectIDZND0="29336@x" ObjectIDZND1="29392@x" Pin0InfoVect0LinkObjId="SW-193259_0" Pin0InfoVect1LinkObjId="SW-193302_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2009830_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4504,-225 4535,-225 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_20a13d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4535,-238 4535,-225 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="29336@0" ObjectIDZND0="g_2009830@0" ObjectIDZND1="29392@x" Pin0InfoVect0LinkObjId="g_2009830_0" Pin0InfoVect1LinkObjId="SW-193302_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193259_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4535,-238 4535,-225 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_20a15c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4535,-225 4535,-211 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="g_2009830@0" ObjectIDND1="29336@x" ObjectIDZND0="29392@1" Pin0InfoVect0LinkObjId="SW-193302_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2009830_0" Pin1InfoVect1LinkObjId="SW-193259_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4535,-225 4535,-211 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20aada0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4794,-835 4794,-792 4725,-792 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29335@0" ObjectIDZND0="29383@1" Pin0InfoVect0LinkObjId="SW-193289_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193258_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4794,-835 4794,-792 4725,-792 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20ab000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4725,-900 4794,-900 4794,-862 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="29382@1" ObjectIDZND0="29335@1" Pin0InfoVect0LinkObjId="SW-193258_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193288_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4725,-900 4794,-900 4794,-862 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-93921" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3416.500000 -1084.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19754" ObjectName="DYN-WD_DXS"/>
     <cge:Meas_Ref ObjectId="93921"/>
    </metadata>
   </g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bd7bd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4198.000000 691.000000) translate(0,12)">油温(℃)：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c589c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4168.000000 676.000000) translate(0,12)">绕组温度(℃)：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c59270" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4198.000000 706.000000) translate(0,12)">档位(档)：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b9b070" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4492.000000 519.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c28d80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4492.000000 503.000000) translate(0,12)">Uc（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c29190" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4484.000000 487.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c295d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4492.000000 535.000000) translate(0,12)">Ua（kV）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c298e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4487.000000 1222.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c29af0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4487.000000 1206.000000) translate(0,12)">Uc（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c29cd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4479.000000 1190.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c29eb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4487.000000 1238.000000) translate(0,12)">Ua（kV）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c2a180" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5042.000000 403.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c2a390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5042.000000 387.000000) translate(0,12)">Uc（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c2a570" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5034.000000 371.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c2a750" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5042.000000 419.000000) translate(0,12)">Ua（kV）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c2aa20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3480.000000 405.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c2ac30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3480.000000 389.000000) translate(0,12)">Uc（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c12ed0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3472.000000 373.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c130b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3480.000000 421.000000) translate(0,12)">Ua（kV）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2080830" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3545.000000 -57.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2081490" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3534.000000 -72.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2081d40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3559.000000 -87.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20825d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3546.000000 -101.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20a1a10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5103.000000 1230.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20a1eb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5092.000000 1215.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20a20f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5117.000000 1200.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20a2510" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5103.000000 1115.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20a27d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5092.000000 1100.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20a2a10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5117.000000 1085.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20a2e30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5103.000000 1006.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20a30f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5092.000000 991.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20a3330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5117.000000 976.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20a3750" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5103.000000 767.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20a3a10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5092.000000 752.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20a3c50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5117.000000 737.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20a4070" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5103.000000 562.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20a4330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5092.000000 547.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20a4570" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5117.000000 532.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20a6410" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4524.000000 -64.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20a66d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4513.000000 -79.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20a6910" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4538.000000 -94.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20a8980" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4879.000000 877.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20a8c10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4868.000000 862.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20a8e50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4893.000000 847.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3652.000000 -511.000000)" xlink:href="#capacitor:shape11"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="19826" cx="4535" cy="-348" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29321" cx="4632" cy="-538" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29321" cx="4632" cy="-792" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29321" cx="4632" cy="-749" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29321" cx="4632" cy="-757" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29321" cx="4632" cy="-631" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19826" cx="5060" cy="-348" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19826" cx="4955" cy="-348" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19826" cx="4852" cy="-348" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19826" cx="4747" cy="-348" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19826" cx="4638" cy="-348" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19827" cx="4398" cy="-348" fill="rgb(154,205,50)" r="4" stroke="rgb(154,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19827" cx="4289" cy="-348" fill="rgb(154,205,50)" r="4" stroke="rgb(154,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19827" cx="4095" cy="-348" fill="rgb(154,205,50)" r="4" stroke="rgb(154,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19827" cx="3858" cy="-348" fill="rgb(154,205,50)" r="4" stroke="rgb(154,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19827" cx="3617" cy="-348" fill="rgb(154,205,50)" r="4" stroke="rgb(154,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19827" cx="3704" cy="-348" fill="rgb(154,205,50)" r="4" stroke="rgb(154,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19827" cx="4118" cy="-348" fill="rgb(154,205,50)" r="4" stroke="rgb(154,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19825" cx="4632" cy="-1209" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19825" cx="4632" cy="-1097" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19825" cx="4632" cy="-984" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19825" cx="4632" cy="-900" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19825" cx="4632" cy="-945" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19827" cx="3919" cy="-348" fill="rgb(154,205,50)" r="4" stroke="rgb(154,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29321" cx="4632" cy="-562" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-193301">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4526.000000 -290.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29393" ObjectName="SW-WD_DXS.WD_DXS_0011SW"/>
     <cge:Meas_Ref ObjectId="193301"/>
    <cge:TPSR_Ref TObjectID="29393"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193284">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4683.000000 -533.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29376" ObjectName="SW-WD_DXS.WD_DXS_3112SW"/>
     <cge:Meas_Ref ObjectId="193284"/>
    <cge:TPSR_Ref TObjectID="29376"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193283">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4783.000000 -533.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29377" ObjectName="SW-WD_DXS.WD_DXS_3116SW"/>
     <cge:Meas_Ref ObjectId="193283"/>
    <cge:TPSR_Ref TObjectID="29377"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193289">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4684.000000 -787.105793)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29383" ObjectName="SW-WD_DXS.WD_DXS_3122SW"/>
     <cge:Meas_Ref ObjectId="193289"/>
    <cge:TPSR_Ref TObjectID="29383"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193288">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4684.000000 -895.105793)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29382" ObjectName="SW-WD_DXS.WD_DXS_3121SW"/>
     <cge:Meas_Ref ObjectId="193288"/>
    <cge:TPSR_Ref TObjectID="29382"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-94046">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4299.578000 -556.874055)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19823" ObjectName="SW-WD_DXS.WD_DXS_3016SW"/>
     <cge:Meas_Ref ObjectId="94046"/>
    <cge:TPSR_Ref TObjectID="19823"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4030.000000 -616.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193282">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4838.000000 -475.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29378" ObjectName="SW-WD_DXS.WD_DXS_31167SW"/>
     <cge:Meas_Ref ObjectId="193282"/>
    <cge:TPSR_Ref TObjectID="29378"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193287">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4685.000000 -744.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29381" ObjectName="SW-WD_DXS.WD_DXS_3132SW"/>
     <cge:Meas_Ref ObjectId="193287"/>
    <cge:TPSR_Ref TObjectID="29381"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193286">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4785.000000 -744.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29380" ObjectName="SW-WD_DXS.WD_DXS_3136SW"/>
     <cge:Meas_Ref ObjectId="193286"/>
    <cge:TPSR_Ref TObjectID="29380"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193285">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4840.000000 -686.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29379" ObjectName="SW-WD_DXS.WD_DXS_31367SW"/>
     <cge:Meas_Ref ObjectId="193285"/>
    <cge:TPSR_Ref TObjectID="29379"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193281">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4687.000000 -979.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29375" ObjectName="SW-WD_DXS.WD_DXS_3141SW"/>
     <cge:Meas_Ref ObjectId="193281"/>
    <cge:TPSR_Ref TObjectID="29375"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193280">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4787.000000 -979.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29374" ObjectName="SW-WD_DXS.WD_DXS_3146SW"/>
     <cge:Meas_Ref ObjectId="193280"/>
    <cge:TPSR_Ref TObjectID="29374"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193279">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4842.000000 -921.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29373" ObjectName="SW-WD_DXS.WD_DXS_31467SW"/>
     <cge:Meas_Ref ObjectId="193279"/>
    <cge:TPSR_Ref TObjectID="29373"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193276">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4687.000000 -1091.627204)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29370" ObjectName="SW-WD_DXS.WD_DXS_3151SW"/>
     <cge:Meas_Ref ObjectId="193276"/>
    <cge:TPSR_Ref TObjectID="29370"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193278">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4787.000000 -1091.627204)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29371" ObjectName="SW-WD_DXS.WD_DXS_3156SW"/>
     <cge:Meas_Ref ObjectId="193278"/>
    <cge:TPSR_Ref TObjectID="29371"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193277">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4842.000000 -1033.627204)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29372" ObjectName="SW-WD_DXS.WD_DXS_31567SW"/>
     <cge:Meas_Ref ObjectId="193277"/>
    <cge:TPSR_Ref TObjectID="29372"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-94044">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4687.000000 -1204.055416)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19820" ObjectName="SW-WD_DXS.WD_DXS_3161SW"/>
     <cge:Meas_Ref ObjectId="94044"/>
    <cge:TPSR_Ref TObjectID="19820"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-94043">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4787.000000 -1204.055416)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19819" ObjectName="SW-WD_DXS.WD_DXS_3166SW"/>
     <cge:Meas_Ref ObjectId="94043"/>
    <cge:TPSR_Ref TObjectID="19819"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-200315">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4842.000000 -1146.055416)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29328" ObjectName="SW-WD_DXS.WD_DXS_31667SW"/>
     <cge:Meas_Ref ObjectId="200315"/>
    <cge:TPSR_Ref TObjectID="29328"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193275">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4553.000000 -752.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29369" ObjectName="SW-WD_DXS.WD_DXS_3902SW"/>
     <cge:Meas_Ref ObjectId="193275"/>
    <cge:TPSR_Ref TObjectID="29369"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-200313">
    <use class="BV-3KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4108.578000 -375.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29330" ObjectName="SW-WD_DXS.WD_DXS_6011SW"/>
     <cge:Meas_Ref ObjectId="200313"/>
    <cge:TPSR_Ref TObjectID="29330"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193290">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4685.000000 -626.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29384" ObjectName="SW-WD_DXS.WD_DXS_3171SW"/>
     <cge:Meas_Ref ObjectId="193290"/>
    <cge:TPSR_Ref TObjectID="29384"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193273">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5051.000000 -284.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29367" ObjectName="SW-WD_DXS.WD_DXS_0901SW"/>
     <cge:Meas_Ref ObjectId="193273"/>
    <cge:TPSR_Ref TObjectID="29367"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193304">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5073.000000 -259.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29401" ObjectName="SW-WD_DXS.WD_DXS_09017SW"/>
     <cge:Meas_Ref ObjectId="193304"/>
    <cge:TPSR_Ref TObjectID="29401"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193269">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4946.000000 -292.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29364" ObjectName="SW-WD_DXS.WD_DXS_0141SW"/>
     <cge:Meas_Ref ObjectId="193269"/>
    <cge:TPSR_Ref TObjectID="29364"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193270">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4946.000000 -169.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29365" ObjectName="SW-WD_DXS.WD_DXS_0142SW"/>
     <cge:Meas_Ref ObjectId="193270"/>
    <cge:TPSR_Ref TObjectID="29365"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193268">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4968.000000 -276.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29363" ObjectName="SW-WD_DXS.WD_DXS_01417SW"/>
     <cge:Meas_Ref ObjectId="193268"/>
    <cge:TPSR_Ref TObjectID="29363"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4946.000000 -34.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29366" ObjectName="SW-WD_DXS.WD_DXS_0146SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="29366"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193265">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4843.000000 -292.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29356" ObjectName="SW-WD_DXS.WD_DXS_0131SW"/>
     <cge:Meas_Ref ObjectId="193265"/>
    <cge:TPSR_Ref TObjectID="29356"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193266">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4843.000000 -169.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29357" ObjectName="SW-WD_DXS.WD_DXS_0132SW"/>
     <cge:Meas_Ref ObjectId="193266"/>
    <cge:TPSR_Ref TObjectID="29357"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193264">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4865.000000 -276.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29355" ObjectName="SW-WD_DXS.WD_DXS_01317SW"/>
     <cge:Meas_Ref ObjectId="193264"/>
    <cge:TPSR_Ref TObjectID="29355"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4843.000000 -34.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29358" ObjectName="SW-WD_DXS.WD_DXS_0136SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="29358"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193271">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4738.000000 -292.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29360" ObjectName="SW-WD_DXS.WD_DXS_0121SW"/>
     <cge:Meas_Ref ObjectId="193271"/>
    <cge:TPSR_Ref TObjectID="29360"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193272">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4738.000000 -169.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29361" ObjectName="SW-WD_DXS.WD_DXS_0122SW"/>
     <cge:Meas_Ref ObjectId="193272"/>
    <cge:TPSR_Ref TObjectID="29361"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193267">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4760.000000 -276.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29359" ObjectName="SW-WD_DXS.WD_DXS_01217SW"/>
     <cge:Meas_Ref ObjectId="193267"/>
    <cge:TPSR_Ref TObjectID="29359"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4738.000000 -34.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29362" ObjectName="SW-WD_DXS.WD_DXS_0126SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="29362"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193262">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4629.000000 -292.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29353" ObjectName="SW-WD_DXS.WD_DXS_0111SW"/>
     <cge:Meas_Ref ObjectId="193262"/>
    <cge:TPSR_Ref TObjectID="29353"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193263">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4629.000000 -169.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29354" ObjectName="SW-WD_DXS.WD_DXS_0112SW"/>
     <cge:Meas_Ref ObjectId="193263"/>
    <cge:TPSR_Ref TObjectID="29354"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193261">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4651.000000 -276.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29348" ObjectName="SW-WD_DXS.WD_DXS_01117SW"/>
     <cge:Meas_Ref ObjectId="193261"/>
    <cge:TPSR_Ref TObjectID="29348"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4629.000000 -34.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193300">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4548.000000 -276.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29394" ObjectName="SW-WD_DXS.WD_DXS_00117SW"/>
     <cge:Meas_Ref ObjectId="193300"/>
    <cge:TPSR_Ref TObjectID="29394"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193302">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4526.000000 -170.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29392" ObjectName="SW-WD_DXS.WD_DXS_0016SW"/>
     <cge:Meas_Ref ObjectId="193302"/>
    <cge:TPSR_Ref TObjectID="29392"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193305">
    <use class="BV-3KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4389.000000 -289.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29391" ObjectName="SW-WD_DXS.WD_DXS_6021SW"/>
     <cge:Meas_Ref ObjectId="193305"/>
    <cge:TPSR_Ref TObjectID="29391"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193303">
    <use class="BV-3KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4411.000000 -275.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29390" ObjectName="SW-WD_DXS.WD_DXS_60217SW"/>
     <cge:Meas_Ref ObjectId="193303"/>
    <cge:TPSR_Ref TObjectID="29390"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-200314">
    <use class="BV-3KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4389.000000 -169.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29389" ObjectName="SW-WD_DXS.WD_DXS_6026SW"/>
     <cge:Meas_Ref ObjectId="200314"/>
    <cge:TPSR_Ref TObjectID="29389"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193299">
    <use class="BV-3KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4280.000000 -289.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29388" ObjectName="SW-WD_DXS.WD_DXS_6141SW"/>
     <cge:Meas_Ref ObjectId="193299"/>
    <cge:TPSR_Ref TObjectID="29388"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193294">
    <use class="BV-3KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4295.000000 -261.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29399" ObjectName="SW-WD_DXS.WD_DXS_61417SW"/>
     <cge:Meas_Ref ObjectId="193294"/>
    <cge:TPSR_Ref TObjectID="29399"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-93968">
    <use class="BV-3KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4086.000000 -289.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19777" ObjectName="SW-WD_DXS.WD_DXS_6131SW"/>
     <cge:Meas_Ref ObjectId="93968"/>
    <cge:TPSR_Ref TObjectID="19777"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193293">
    <use class="BV-3KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4039.000000 -268.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29320" ObjectName="SW-WD_DXS.WD_DXS_61317SW"/>
     <cge:Meas_Ref ObjectId="193293"/>
    <cge:TPSR_Ref TObjectID="29320"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-93969">
    <use class="BV-3KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4163.000000 -120.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19778" ObjectName="SW-WD_DXS.WD_DXS_6132SW"/>
     <cge:Meas_Ref ObjectId="93969"/>
    <cge:TPSR_Ref TObjectID="19778"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-93965">
    <use class="BV-3KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3849.000000 -289.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19774" ObjectName="SW-WD_DXS.WD_DXS_6121SW"/>
     <cge:Meas_Ref ObjectId="93965"/>
    <cge:TPSR_Ref TObjectID="19774"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193292">
    <use class="BV-3KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3802.000000 -268.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29319" ObjectName="SW-WD_DXS.WD_DXS_61217SW"/>
     <cge:Meas_Ref ObjectId="193292"/>
    <cge:TPSR_Ref TObjectID="29319"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-93966">
    <use class="BV-3KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3926.000000 -120.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19775" ObjectName="SW-WD_DXS.WD_DXS_6122SW"/>
     <cge:Meas_Ref ObjectId="93966"/>
    <cge:TPSR_Ref TObjectID="19775"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-93962">
    <use class="BV-3KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3608.000000 -289.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19771" ObjectName="SW-WD_DXS.WD_DXS_6111SW"/>
     <cge:Meas_Ref ObjectId="93962"/>
    <cge:TPSR_Ref TObjectID="19771"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193291">
    <use class="BV-3KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3561.000000 -268.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29318" ObjectName="SW-WD_DXS.WD_DXS_61117SW"/>
     <cge:Meas_Ref ObjectId="193291"/>
    <cge:TPSR_Ref TObjectID="29318"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-93963">
    <use class="BV-3KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3685.000000 -120.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19772" ObjectName="SW-WD_DXS.WD_DXS_6112SW"/>
     <cge:Meas_Ref ObjectId="93963"/>
    <cge:TPSR_Ref TObjectID="19772"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193297">
    <use class="BV-3KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3910.000000 -359.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29386" ObjectName="SW-WD_DXS.WD_DXS_6901SW"/>
     <cge:Meas_Ref ObjectId="193297"/>
    <cge:TPSR_Ref TObjectID="29386"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193298">
    <use class="BV-3KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3695.000000 -361.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29387" ObjectName="SW-WD_DXS.WD_DXS_6151SW"/>
     <cge:Meas_Ref ObjectId="193298"/>
    <cge:TPSR_Ref TObjectID="29387"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193296">
    <use class="BV-3KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3863.000000 -410.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29385" ObjectName="SW-WD_DXS.WD_DXS_69017SW"/>
     <cge:Meas_Ref ObjectId="193296"/>
    <cge:TPSR_Ref TObjectID="29385"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193295">
    <use class="BV-3KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3648.000000 -410.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29400" ObjectName="SW-WD_DXS.WD_DXS_61517SW"/>
     <cge:Meas_Ref ObjectId="193295"/>
    <cge:TPSR_Ref TObjectID="29400"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193274">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4553.000000 -940.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29368" ObjectName="SW-WD_DXS.WD_DXS_3901SW"/>
     <cge:Meas_Ref ObjectId="193274"/>
    <cge:TPSR_Ref TObjectID="29368"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-94047">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4409.000000 -557.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19824" ObjectName="SW-WD_DXS.WD_DXS_3011SW"/>
     <cge:Meas_Ref ObjectId="94047"/>
    <cge:TPSR_Ref TObjectID="19824"/></metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c9a4a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3162.000000 -1028.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c9a4a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3162.000000 -1028.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c9a4a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3162.000000 -1028.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c9a4a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3162.000000 -1028.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c9a4a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3162.000000 -1028.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c9a4a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3162.000000 -1028.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c9a4a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3162.000000 -1028.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ae9690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ae9690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ae9690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ae9690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ae9690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ae9690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ae9690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ae9690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ae9690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ae9690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ae9690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ae9690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ae9690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ae9690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ae9690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ae9690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ae9690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,353)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_19ee290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3263.000000 -1167.500000) translate(0,16)">大响水电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1c45970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5078.000000 -346.000000) translate(0,15)">10kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_1a06bb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3411.500000 -1166.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c80610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4959.000000 -564.000000) translate(0,12)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c76a60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4750.000000 -856.000000) translate(0,12)">分段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1bd7030" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4966.142857 -743.800000) translate(0,15)">近大线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1bd7480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3924.000000 -753.000000) translate(0,15)">1号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1bd7480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3924.000000 -753.000000) translate(0,33)">SFSLb-16000/121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1bd7480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3924.000000 -753.000000) translate(0,51)">16000kW  121/38.5/6.3kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1bd7480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3924.000000 -753.000000) translate(0,69)">Ud=6.59%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bac450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4021.000000 -588.000000) translate(0,12)">1200</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c5d5f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4961.000000 -787.000000) translate(0,12)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bc24e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4963.000000 -1022.000000) translate(0,12)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bcfd00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4969.000000 -1135.000000) translate(0,12)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bf84a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4965.000000 -1247.000000) translate(0,12)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bbb6a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5009.000000 -1204.000000) translate(0,12)">田大线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d42dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5011.000000 -1097.000000) translate(0,12)">大白线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d43330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5009.000000 -980.000000) translate(0,12)">大插线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d43890" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4966.142857 -535.800000) translate(0,15)">大猫高线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d4fb50" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4944.142857 -653.800000) translate(0,15)">35kV1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1fcf890" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4919.542857 27.200000) translate(0,15)">小电站线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1fde6b0" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4816.542857 27.200000) translate(0,15)">二级站线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1feecc0" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4711.542857 27.200000) translate(0,15)">乐茂河线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20007a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4644.000000 -65.000000) translate(0,12)">0116</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2001320" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4613.542857 27.200000) translate(0,15)">大沙线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2023550" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4249.142857 -34.800000) translate(0,15)">6kV2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2028220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4097.000000 -319.000000) translate(0,12)">6131</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2028850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4106.000000 -229.000000) translate(0,12)">613</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2035c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4177.000000 -152.000000) translate(0,12)">6132</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2036e10" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4185.142857 -81.800000) translate(0,15)">励磁变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_203d1c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3860.000000 -319.000000) translate(0,12)">6121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_203d7f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3869.000000 -229.000000) translate(0,12)">612</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20498e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3940.000000 -152.000000) translate(0,12)">6122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_204a2f0" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 3948.142857 -81.800000) translate(0,15)">励磁变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2052690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3619.000000 -319.000000) translate(0,12)">6111</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2052cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3628.000000 -229.000000) translate(0,12)">611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_205edb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3699.000000 -152.000000) translate(0,12)">6112</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_205f7c0" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 3707.142857 -81.800000) translate(0,15)">励磁变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_2062b20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3497.000000 -27.000000) translate(0,10)">1号发电机参数</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_2062b20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3497.000000 -27.000000) translate(0,22)">TWS-K148/98-4</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_2062b20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3497.000000 -27.000000) translate(0,34)">4000kW  6.3kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_2062b20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3497.000000 -27.000000) translate(0,46)">COSΦ=0.8  458A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2068b60" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 3582.142857 31.200000) translate(0,15)">1号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2069450" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 3817.142857 31.200000) translate(0,15)">2号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20698b0" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4056.142857 31.200000) translate(0,15)">3号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_2069af0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3737.000000 -31.000000) translate(0,10)">2号发电机参数</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_2069af0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3737.000000 -31.000000) translate(0,22)">TWS-K148/98-4</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_2069af0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3737.000000 -31.000000) translate(0,34)">4000kW  6.3kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_2069af0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3737.000000 -31.000000) translate(0,46)">COSΦ=0.8  458A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_206a030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3976.000000 -29.000000) translate(0,10)">3号发电机参数</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_206a030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3976.000000 -29.000000) translate(0,22)">TWS-K148/98-4</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_206a030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3976.000000 -29.000000) translate(0,34)">4000kW  6.3kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_206a030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3976.000000 -29.000000) translate(0,46)">COSΦ=0.8  458A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_207d580" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3668.000000 -531.000000) translate(0,15)">6kV电容器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_207e050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3876.000000 -577.000000) translate(0,15)">6kV母线电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_207e050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3876.000000 -577.000000) translate(0,33)">压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2083330" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4549.542857 -90.800000) translate(0,15)">近区变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_2083870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4454.000000 -108.000000) translate(0,10)">近区变参数</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_2083870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4454.000000 -108.000000) translate(0,22)">S7-1250/6.3</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_2083870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4454.000000 -108.000000) translate(0,34)">1250kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_2083870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4454.000000 -108.000000) translate(0,46)">6.3/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_2083870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4454.000000 -108.000000) translate(0,58)">Ud=5.43%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2084f40" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4430.142857 -701.800000) translate(0,15)">35kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2084f40" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4430.142857 -701.800000) translate(0,33)"> 电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_208c7d0" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4430.142857 -889.800000) translate(0,15)">35kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_208c7d0" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4430.142857 -889.800000) translate(0,33)"> 电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2094650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4748.000000 -1234.000000) translate(0,12)">316</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2094c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4805.000000 -1235.000000) translate(0,12)">3166</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2094ec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4694.000000 -1235.000000) translate(0,12)">3161</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2095100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4799.000000 -1175.000000) translate(0,12)">31667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2095540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4749.000000 -1122.000000) translate(0,12)">315</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2095a30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4695.000000 -1123.000000) translate(0,12)">3151</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2095f20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4794.000000 -1123.000000) translate(0,12)">3156</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2096160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4800.000000 -1062.000000) translate(0,12)">31567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20963a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4749.000000 -1008.000000) translate(0,12)">314</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2096650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4794.000000 -1010.000000) translate(0,12)">3146</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2096b40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4695.000000 -1010.000000) translate(0,12)">3141</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2096d80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4797.000000 -949.000000) translate(0,12)">31467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2096fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4560.000000 -971.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2097500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4638.000000 -1279.000000) translate(0,12)">35kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2097a30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4691.000000 -926.000000) translate(0,12)">3121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2097c60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4691.000000 -818.000000) translate(0,12)">3122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2097ea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4646.000000 -464.000000) translate(0,12)">35kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2098440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4746.000000 -773.000000) translate(0,12)">313</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2098650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4692.000000 -775.000000) translate(0,12)">3132</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2098890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4792.000000 -775.000000) translate(0,12)">3136</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2098ad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4795.000000 -715.000000) translate(0,12)">31367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2098d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4692.000000 -657.000000) translate(0,12)">3171</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2098f50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4745.000000 -562.000000) translate(0,12)">311</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2099190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4790.000000 -564.000000) translate(0,12)">3116</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20993d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4690.000000 -564.000000) translate(0,12)">3112</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2099610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4794.000000 -503.000000) translate(0,12)">31167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2099850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4365.000000 -587.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2099a90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4307.000000 -588.000000) translate(0,12)">3016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2099cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4416.000000 -588.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2099f10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4126.000000 -466.000000) translate(0,12)">601</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_209a150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4125.000000 -405.000000) translate(0,12)">6011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_209a390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4180.000000 -642.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_209ade0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3489.000000 -340.000000) translate(0,15)">6kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_209b2a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3711.000000 -391.000000) translate(0,12)">6151</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_209b4e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3652.000000 -441.000000) translate(0,12)">61517</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_209b720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3926.000000 -389.000000) translate(0,12)">6901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_209b960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3866.000000 -441.000000) translate(0,12)">69017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_209bba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3565.000000 -299.000000) translate(0,12)">61117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_209bde0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3805.000000 -299.000000) translate(0,12)">61217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_209c020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4042.000000 -299.000000) translate(0,12)">61317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_209c260" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4296.000000 -319.000000) translate(0,12)">6141</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_209c4a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4299.000000 -292.000000) translate(0,12)">61417</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_209c6e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4407.000000 -259.000000) translate(0,12)">602</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_209c920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4405.000000 -199.000000) translate(0,12)">6026</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_209cb60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4414.000000 -306.000000) translate(0,12)">60217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_209cda0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4405.000000 -319.000000) translate(0,12)">6021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_209cfe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4544.000000 -259.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_209d220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4542.000000 -200.000000) translate(0,12)">0016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_209d460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4542.000000 -320.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_209d6a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4551.000000 -307.000000) translate(0,12)">00117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_209d8e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4647.000000 -259.000000) translate(0,12)">011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_209db20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4645.000000 -199.000000) translate(0,12)">0112</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_209dd60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4645.000000 -322.000000) translate(0,12)">0111</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_209dfa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4655.000000 -307.000000) translate(0,12)">01117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_209e1e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4756.000000 -259.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_209e420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4754.000000 -64.000000) translate(0,12)">0126</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_209e660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4754.000000 -199.000000) translate(0,12)">0122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_209e8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4754.000000 -322.000000) translate(0,12)">0121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_209eae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4763.000000 -307.000000) translate(0,12)">01217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_209ed20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4861.000000 -259.000000) translate(0,12)">013</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_209ef60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4859.000000 -64.000000) translate(0,12)">0136</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_209f1a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4859.000000 -199.000000) translate(0,12)">0132</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_209f3e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4868.000000 -307.000000) translate(0,12)">01317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_209f620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4859.000000 -322.000000) translate(0,12)">0131</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_209f860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4964.000000 -259.000000) translate(0,12)">014</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_209faa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4962.000000 -64.000000) translate(0,12)">0146</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_209fce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4962.000000 -199.000000) translate(0,12)">0142</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_209ff20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4972.000000 -307.000000) translate(0,12)">01417</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20a0160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4962.000000 -322.000000) translate(0,12)">0141</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20a03a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5067.000000 -314.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20a05e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5076.000000 -290.000000) translate(0,12)">09017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20a0820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4559.000000 -783.000000) translate(0,12)">3902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20ab260" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4805.000000 -856.000000) translate(0,12)">312</text>
  </g><g id="HydroGenerator_Layer">
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4071.000000 21.000000)" xlink:href="#hydroGenerator:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3834.000000 21.000000)" xlink:href="#hydroGenerator:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3593.000000 21.000000)" xlink:href="#hydroGenerator:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="WD_DXS"/>
</svg>