<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-172" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="3115 -1199 2453 1266">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="capacitor:shape11">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.974359" x1="16" x2="92" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.484375" x1="68" x2="37" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.484375" x1="68" x2="36" y1="37" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.484375" x1="31" x2="0" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.484375" x1="31" x2="0" y1="37" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.484375" x1="107" x2="76" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.484375" x1="107" x2="76" y1="37" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="22" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="52" x2="52" y1="22" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="92" x2="92" y1="22" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="7" x2="7" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape28">
    <circle cx="14" cy="13" fillStyle="0" r="13.5" stroke-width="1"/>
    <circle cx="14" cy="34" fillStyle="0" r="14" stroke-width="1"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape19_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="14" y1="30" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="26" y1="38" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="14" y1="26" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="7" y1="38" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="27" y1="17" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="5" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="5" y1="46" y2="10"/>
    <rect height="26" stroke-width="0.416609" width="14" x="-2" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="34" y1="10" y2="40"/>
    <rect height="29" stroke-width="0.416609" width="9" x="12" y="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="5" y1="46" y2="10"/>
    <rect height="26" stroke-width="0.416609" width="14" x="-2" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="transformer2:shape51_0">
    <circle cx="39" cy="70" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="64,100 1,37 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.643357" x1="97" x2="39" y1="75" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.643357" x1="97" x2="97" y1="54" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="100" x2="94" y1="50" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="98" x2="96" y1="47" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="103" x2="91" y1="54" y2="54"/>
    <polyline points="64,93 64,100 " stroke-width="1"/>
    <polyline points="58,100 64,100 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
   </symbol>
   <symbol id="transformer2:shape51_1">
    <circle cx="39" cy="32" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline DF8003:Layer="PUBLIC" points="39,19 32,34 47,34 39,19 39,19 39,19 "/>
   </symbol>
   <symbol id="transformer2:shape57_0">
    <circle cx="16" cy="80" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="16" y1="50" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="43" x2="40" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="45" x2="37" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="35" x2="47" y1="26" y2="26"/>
    <polyline DF8003:Layer="PUBLIC" points="16,12 22,25 10,25 16,12 16,13 16,12 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="43" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="21" y1="79" y2="84"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="11" y1="79" y2="84"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="79" y2="74"/>
   </symbol>
   <symbol id="transformer2:shape57_1">
    <circle cx="16" cy="58" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="16,55 41,55 41,26 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="55" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="21" y1="55" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="11" y1="55" y2="60"/>
   </symbol>
   <symbol id="voltageTransformer:shape94">
    <rect height="24" stroke-width="0.379884" width="14" x="2" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="21" y1="14" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="25" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="9" x2="9" y1="23" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="9" x2="6" y1="23" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="9" x2="9" y1="8" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="12" x2="9" y1="11" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="9" x2="6" y1="8" y2="11"/>
    <ellipse cx="21" cy="16" fillStyle="0" rx="8.5" ry="8" stroke-width="1"/>
    <circle cx="9" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="9" cy="22" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="12" x2="9" y1="26" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="23" x2="25" y1="18" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="75" y2="31"/>
   </symbol>
   <symbol id="voltageTransformer:shape93">
    <rect height="24" stroke-width="0.379884" width="14" x="14" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="9" y1="14" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="5" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="21" x2="21" y1="23" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="21" x2="24" y1="23" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="21" x2="21" y1="8" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="18" x2="21" y1="11" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="21" x2="24" y1="8" y2="11"/>
    <ellipse cx="8" cy="16" fillStyle="0" rx="8.5" ry="8" stroke-width="1"/>
    <circle cx="20" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="20" cy="22" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="18" x2="21" y1="26" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="5" y1="18" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="21" x2="21" y1="75" y2="31"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_278b2a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_278c400" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_278cdf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_278de10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_278f070" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_278fc90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_27906f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_27911b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2403810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2403810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_27941f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_27941f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2795f80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2795f80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_2796fa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2798ba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2799790" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_279a550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_279ae90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_279c540" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_279d130" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_279d9b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_279e170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_279f250" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_279fbd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_27a06c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_27a1080" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_27a24e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_27a3000" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_27a3fe0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_27a4ca0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_27b34a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_27a62a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_27a74d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_27a8ab0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1276" width="2463" x="3110" y="-1204"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,0)" stroke-width="1" x1="5553" x2="5562" y1="-418" y2="-418"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3116" y="-1198"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="24" stroke="rgb(60,120,255)" stroke-width="0.379884" width="14" x="5495" y="-157"/>
   <rect DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" fillStyle="1" height="42" stroke="rgb(255,255,255)" stroke-width="1" width="72" x="3545" y="-1149"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-120398">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4120.000000 -759.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22424" ObjectName="SW-WD_BY.WD_BY_3011SW"/>
     <cge:Meas_Ref ObjectId="120398"/>
    <cge:TPSR_Ref TObjectID="22424"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120400">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4120.000000 -387.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22426" ObjectName="SW-WD_BY.WD_BY_0011SW"/>
     <cge:Meas_Ref ObjectId="120400"/>
    <cge:TPSR_Ref TObjectID="22426"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120401">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4839.000000 -760.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22427" ObjectName="SW-WD_BY.WD_BY_3021SW"/>
     <cge:Meas_Ref ObjectId="120401"/>
    <cge:TPSR_Ref TObjectID="22427"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120402">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4839.000000 -499.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22428" ObjectName="SW-WD_BY.WD_BY_0026SW"/>
     <cge:Meas_Ref ObjectId="120402"/>
    <cge:TPSR_Ref TObjectID="22428"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120403">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4839.000000 -388.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22429" ObjectName="SW-WD_BY.WD_BY_0021SW"/>
     <cge:Meas_Ref ObjectId="120403"/>
    <cge:TPSR_Ref TObjectID="22429"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120447">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4417.000000 -754.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22461" ObjectName="SW-WD_BY.WD_BY_3901SW"/>
     <cge:Meas_Ref ObjectId="120447"/>
    <cge:TPSR_Ref TObjectID="22461"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4637.000000 -729.000000)" xlink:href="#switch2:shape19_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120397">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4487.000000 -836.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22423" ObjectName="SW-WD_BY.WD_BY_3331SW"/>
     <cge:Meas_Ref ObjectId="120397"/>
    <cge:TPSR_Ref TObjectID="22423"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120396">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4487.000000 -935.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22422" ObjectName="SW-WD_BY.WD_BY_3336SW"/>
     <cge:Meas_Ref ObjectId="120396"/>
    <cge:TPSR_Ref TObjectID="22422"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4429.000000 -951.000000)" xlink:href="#switch2:shape19_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120450">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4514.000000 -988.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22464" ObjectName="SW-WD_BY.WD_BY_33367SW"/>
     <cge:Meas_Ref ObjectId="120450"/>
    <cge:TPSR_Ref TObjectID="22464"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120395">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4839.000000 -835.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22421" ObjectName="SW-WD_BY.WD_BY_3321SW"/>
     <cge:Meas_Ref ObjectId="120395"/>
    <cge:TPSR_Ref TObjectID="22421"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120394">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4839.000000 -934.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22420" ObjectName="SW-WD_BY.WD_BY_3326SW"/>
     <cge:Meas_Ref ObjectId="120394"/>
    <cge:TPSR_Ref TObjectID="22420"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4775.000000 -944.000000)" xlink:href="#switch2:shape19_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120449">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4866.000000 -987.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22463" ObjectName="SW-WD_BY.WD_BY_33267SW"/>
     <cge:Meas_Ref ObjectId="120449"/>
    <cge:TPSR_Ref TObjectID="22463"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120393">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4120.000000 -835.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22419" ObjectName="SW-WD_BY.WD_BY_3311SW"/>
     <cge:Meas_Ref ObjectId="120393"/>
    <cge:TPSR_Ref TObjectID="22419"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120392">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4120.000000 -934.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22418" ObjectName="SW-WD_BY.WD_BY_3316SW"/>
     <cge:Meas_Ref ObjectId="120392"/>
    <cge:TPSR_Ref TObjectID="22418"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120448">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4147.000000 -987.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22462" ObjectName="SW-WD_BY.WD_BY_33167SW"/>
     <cge:Meas_Ref ObjectId="120448"/>
    <cge:TPSR_Ref TObjectID="22462"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120405">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3687.000000 -222.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22431" ObjectName="SW-WD_BY.WD_BY_0312SW"/>
     <cge:Meas_Ref ObjectId="120405"/>
    <cge:TPSR_Ref TObjectID="22431"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120453">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3687.000000 -117.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22467" ObjectName="SW-WD_BY.WD_BY_0316SW"/>
     <cge:Meas_Ref ObjectId="120453"/>
    <cge:TPSR_Ref TObjectID="22467"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120404">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3687.000000 -316.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22430" ObjectName="SW-WD_BY.WD_BY_0311SW"/>
     <cge:Meas_Ref ObjectId="120404"/>
    <cge:TPSR_Ref TObjectID="22430"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120407">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3880.000000 -222.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22433" ObjectName="SW-WD_BY.WD_BY_0322SW"/>
     <cge:Meas_Ref ObjectId="120407"/>
    <cge:TPSR_Ref TObjectID="22433"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120454">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3880.000000 -117.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22468" ObjectName="SW-WD_BY.WD_BY_0326SW"/>
     <cge:Meas_Ref ObjectId="120454"/>
    <cge:TPSR_Ref TObjectID="22468"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120406">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3880.000000 -316.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22432" ObjectName="SW-WD_BY.WD_BY_0321SW"/>
     <cge:Meas_Ref ObjectId="120406"/>
    <cge:TPSR_Ref TObjectID="22432"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120457">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4071.000000 -119.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22471" ObjectName="SW-WD_BY.WD_BY_0336SW"/>
     <cge:Meas_Ref ObjectId="120457"/>
    <cge:TPSR_Ref TObjectID="22471"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120408">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4071.000000 -318.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22434" ObjectName="SW-WD_BY.WD_BY_0331SW"/>
     <cge:Meas_Ref ObjectId="120408"/>
    <cge:TPSR_Ref TObjectID="22434"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120409">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4071.000000 -224.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22435" ObjectName="SW-WD_BY.WD_BY_0332SW"/>
     <cge:Meas_Ref ObjectId="120409"/>
    <cge:TPSR_Ref TObjectID="22435"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120455">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4263.000000 -116.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22469" ObjectName="SW-WD_BY.WD_BY_0346SW"/>
     <cge:Meas_Ref ObjectId="120455"/>
    <cge:TPSR_Ref TObjectID="22469"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120410">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4263.000000 -315.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22436" ObjectName="SW-WD_BY.WD_BY_0341SW"/>
     <cge:Meas_Ref ObjectId="120410"/>
    <cge:TPSR_Ref TObjectID="22436"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120411">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4263.000000 -221.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22437" ObjectName="SW-WD_BY.WD_BY_0342SW"/>
     <cge:Meas_Ref ObjectId="120411"/>
    <cge:TPSR_Ref TObjectID="22437"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120456">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4455.000000 -117.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22470" ObjectName="SW-WD_BY.WD_BY_0356SW"/>
     <cge:Meas_Ref ObjectId="120456"/>
    <cge:TPSR_Ref TObjectID="22470"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120412">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4455.000000 -320.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22438" ObjectName="SW-WD_BY.WD_BY_0351SW"/>
     <cge:Meas_Ref ObjectId="120412"/>
    <cge:TPSR_Ref TObjectID="22438"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120413">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4455.000000 -222.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22439" ObjectName="SW-WD_BY.WD_BY_0352SW"/>
     <cge:Meas_Ref ObjectId="120413"/>
    <cge:TPSR_Ref TObjectID="22439"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120486">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4648.000000 -117.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22474" ObjectName="SW-WD_BY.WD_BY_0366SW"/>
     <cge:Meas_Ref ObjectId="120486"/>
    <cge:TPSR_Ref TObjectID="22474"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120420">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4648.000000 -316.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22446" ObjectName="SW-WD_BY.WD_BY_0361SW"/>
     <cge:Meas_Ref ObjectId="120420"/>
    <cge:TPSR_Ref TObjectID="22446"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120421">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4648.000000 -222.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22447" ObjectName="SW-WD_BY.WD_BY_0362SW"/>
     <cge:Meas_Ref ObjectId="120421"/>
    <cge:TPSR_Ref TObjectID="22447"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120484">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4839.000000 -116.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22472" ObjectName="SW-WD_BY.WD_BY_0376SW"/>
     <cge:Meas_Ref ObjectId="120484"/>
    <cge:TPSR_Ref TObjectID="22472"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120416">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4839.000000 -315.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22442" ObjectName="SW-WD_BY.WD_BY_0371SW"/>
     <cge:Meas_Ref ObjectId="120416"/>
    <cge:TPSR_Ref TObjectID="22442"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120417">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4839.000000 -221.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22443" ObjectName="SW-WD_BY.WD_BY_0372SW"/>
     <cge:Meas_Ref ObjectId="120417"/>
    <cge:TPSR_Ref TObjectID="22443"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120485">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5031.400000 -119.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22473" ObjectName="SW-WD_BY.WD_BY_0386SW"/>
     <cge:Meas_Ref ObjectId="120485"/>
    <cge:TPSR_Ref TObjectID="22473"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120418">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5031.400000 -318.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22444" ObjectName="SW-WD_BY.WD_BY_0381SW"/>
     <cge:Meas_Ref ObjectId="120418"/>
    <cge:TPSR_Ref TObjectID="22444"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120419">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5031.400000 -224.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22445" ObjectName="SW-WD_BY.WD_BY_0382SW"/>
     <cge:Meas_Ref ObjectId="120419"/>
    <cge:TPSR_Ref TObjectID="22445"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120414">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5223.400000 -320.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22440" ObjectName="SW-WD_BY.WD_BY_0391SW"/>
     <cge:Meas_Ref ObjectId="120414"/>
    <cge:TPSR_Ref TObjectID="22440"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120422">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5415.000000 -316.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22448" ObjectName="SW-WD_BY.WD_BY_0901SW"/>
     <cge:Meas_Ref ObjectId="120422"/>
    <cge:TPSR_Ref TObjectID="22448"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-190830">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5494.000000 -232.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41935" ObjectName="SW-WD_BY.WD_BY_09011SW"/>
     <cge:Meas_Ref ObjectId="190830"/>
    <cge:TPSR_Ref TObjectID="41935"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120399">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4120.000000 -499.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22425" ObjectName="SW-WD_BY.WD_BY_0016SW"/>
     <cge:Meas_Ref ObjectId="120399"/>
    <cge:TPSR_Ref TObjectID="22425"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120451">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5159.400000 -110.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22465" ObjectName="SW-WD_BY.WD_BY_03961SW"/>
     <cge:Meas_Ref ObjectId="120451"/>
    <cge:TPSR_Ref TObjectID="22465"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120452">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5287.400000 -111.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22466" ObjectName="SW-WD_BY.WD_BY_03962SW"/>
     <cge:Meas_Ref ObjectId="120452"/>
    <cge:TPSR_Ref TObjectID="22466"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120415">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5223.000000 -224.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22441" ObjectName="SW-WD_BY.WD_BY_0396SW"/>
     <cge:Meas_Ref ObjectId="120415"/>
    <cge:TPSR_Ref TObjectID="22441"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-WD_BY.WD_BY_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3958,-823 5070,-823 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="22416" ObjectName="BS-WD_BY.WD_BY_3IM"/>
    <cge:TPSR_Ref TObjectID="22416"/></metadata>
   <polyline fill="none" opacity="0" points="3958,-823 5070,-823 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-WD_BY.WD_BY_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3649,-373 5539,-373 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="22417" ObjectName="BS-WD_BY.WD_BY_9IM"/>
    <cge:TPSR_Ref TObjectID="22417"/></metadata>
   <polyline fill="none" opacity="0" points="3649,-373 5539,-373 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-WD_BY.WD_BY_1Cb">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5116.000000 -59.000000)" xlink:href="#capacitor:shape11"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41934" ObjectName="CB-WD_BY.WD_BY_1Cb"/>
    <cge:TPSR_Ref TObjectID="41934"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5244.000000 -59.000000)" xlink:href="#capacitor:shape11"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-WD_BY.WD_BY_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="31568"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4809.000000 -600.000000)" xlink:href="#transformer2:shape51_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4809.000000 -600.000000)" xlink:href="#transformer2:shape51_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="22480" ObjectName="TF-WD_BY.WD_BY_2T"/>
    <cge:TPSR_Ref TObjectID="22480"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4627.000000 -584.000000)" xlink:href="#transformer2:shape57_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4627.000000 -584.000000)" xlink:href="#transformer2:shape57_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5488.000000 -20.000000)" xlink:href="#transformer2:shape57_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5488.000000 -20.000000)" xlink:href="#transformer2:shape57_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-WD_BY.WD_BY_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="31564"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4090.000000 -599.000000)" xlink:href="#transformer2:shape51_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4090.000000 -599.000000)" xlink:href="#transformer2:shape51_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="22479" ObjectName="TF-WD_BY.WD_BY_1T"/>
    <cge:TPSR_Ref TObjectID="22479"/></metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_280f410">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4378.000000 -666.136364)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34dc560">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4362.000000 -946.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_39dbcf0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4420.000000 -872.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c2cee0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4713.000000 -939.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ca2190">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4766.000000 -865.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3688600">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3995.000000 -951.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3467060">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4053.000000 -929.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1efc090">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3657.000000 -45.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f2ff20">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3850.000000 -45.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3649380">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4041.000000 -47.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_39eacc0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4233.000000 -44.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36895f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4425.000000 -45.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_39e0360">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4618.000000 -45.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36e58d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4809.000000 -44.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1eb4520">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5001.000000 -47.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f2f4d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5367.000000 -179.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3362.000000 -1054.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-145372" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3351.538462 -898.966362) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="145372" ObjectName="WD_BY:WD_BY_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-145373" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3352.538462 -856.966362) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="145373" ObjectName="WD_BY:WD_BY_sumQ"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-145372" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3349.538462 -972.966362) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="145372" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-145372" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3350.538462 -935.966362) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="145372" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-120286" prefix="P  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4205.000000 -931.000000) translate(0,16)">P   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120286" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22449"/>
     <cge:Term_Ref ObjectID="31502"/>
    <cge:TPSR_Ref TObjectID="22449"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-120287" prefix="Q " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4205.000000 -931.000000) translate(0,36)">Q  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120287" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22449"/>
     <cge:Term_Ref ObjectID="31502"/>
    <cge:TPSR_Ref TObjectID="22449"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-120283" prefix="Ia  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4205.000000 -931.000000) translate(0,56)">Ia   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120283" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22449"/>
     <cge:Term_Ref ObjectID="31502"/>
    <cge:TPSR_Ref TObjectID="22449"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-120296" prefix="P  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4568.000000 -933.000000) translate(0,16)">P   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120296" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22451"/>
     <cge:Term_Ref ObjectID="31506"/>
    <cge:TPSR_Ref TObjectID="22451"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-120297" prefix="Q " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4568.000000 -933.000000) translate(0,36)">Q  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120297" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22451"/>
     <cge:Term_Ref ObjectID="31506"/>
    <cge:TPSR_Ref TObjectID="22451"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-120293" prefix="Ia  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4568.000000 -933.000000) translate(0,56)">Ia   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120293" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22451"/>
     <cge:Term_Ref ObjectID="31506"/>
    <cge:TPSR_Ref TObjectID="22451"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-120291" prefix="P  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4919.000000 -934.000000) translate(0,16)">P   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120291" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22450"/>
     <cge:Term_Ref ObjectID="31504"/>
    <cge:TPSR_Ref TObjectID="22450"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-120292" prefix="Q " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4919.000000 -934.000000) translate(0,36)">Q  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120292" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22450"/>
     <cge:Term_Ref ObjectID="31504"/>
    <cge:TPSR_Ref TObjectID="22450"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-120288" prefix="Ia  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4919.000000 -934.000000) translate(0,56)">Ia   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120288" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22450"/>
     <cge:Term_Ref ObjectID="31504"/>
    <cge:TPSR_Ref TObjectID="22450"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-120268" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4260.000000 -490.000000) translate(0,16)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120268" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22475"/>
     <cge:Term_Ref ObjectID="31554"/>
    <cge:TPSR_Ref TObjectID="22475"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-120269" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4260.000000 -490.000000) translate(0,36)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120269" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22475"/>
     <cge:Term_Ref ObjectID="31554"/>
    <cge:TPSR_Ref TObjectID="22475"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-120259" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4260.000000 -490.000000) translate(0,56)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120259" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22475"/>
     <cge:Term_Ref ObjectID="31554"/>
    <cge:TPSR_Ref TObjectID="22475"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-120280" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4992.000000 -489.000000) translate(0,16)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120280" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22477"/>
     <cge:Term_Ref ObjectID="31558"/>
    <cge:TPSR_Ref TObjectID="22477"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-120281" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4992.000000 -489.000000) translate(0,36)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120281" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22477"/>
     <cge:Term_Ref ObjectID="31558"/>
    <cge:TPSR_Ref TObjectID="22477"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-120271" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4992.000000 -489.000000) translate(0,56)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120271" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22477"/>
     <cge:Term_Ref ObjectID="31558"/>
    <cge:TPSR_Ref TObjectID="22477"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-120308" prefix="P  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3682.000000 4.000000) translate(0,16)">P   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120308" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22452"/>
     <cge:Term_Ref ObjectID="31508"/>
    <cge:TPSR_Ref TObjectID="22452"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-120309" prefix="Q " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3682.000000 4.000000) translate(0,36)">Q  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120309" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22452"/>
     <cge:Term_Ref ObjectID="31508"/>
    <cge:TPSR_Ref TObjectID="22452"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-120306" prefix="Ia  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3682.000000 4.000000) translate(0,56)">Ia   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120306" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22452"/>
     <cge:Term_Ref ObjectID="31508"/>
    <cge:TPSR_Ref TObjectID="22452"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-120324" prefix="P  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3875.000000 4.000000) translate(0,16)">P   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120324" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22453"/>
     <cge:Term_Ref ObjectID="31510"/>
    <cge:TPSR_Ref TObjectID="22453"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-120325" prefix="Q " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3875.000000 4.000000) translate(0,36)">Q  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120325" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22453"/>
     <cge:Term_Ref ObjectID="31510"/>
    <cge:TPSR_Ref TObjectID="22453"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-120322" prefix="Ia  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3875.000000 4.000000) translate(0,56)">Ia   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120322" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22453"/>
     <cge:Term_Ref ObjectID="31510"/>
    <cge:TPSR_Ref TObjectID="22453"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-120316" prefix="P  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4066.000000 2.000000) translate(0,16)">P   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120316" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22454"/>
     <cge:Term_Ref ObjectID="31512"/>
    <cge:TPSR_Ref TObjectID="22454"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-120317" prefix="Q " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4066.000000 2.000000) translate(0,36)">Q  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120317" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22454"/>
     <cge:Term_Ref ObjectID="31512"/>
    <cge:TPSR_Ref TObjectID="22454"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-120314" prefix="Ia  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4066.000000 2.000000) translate(0,56)">Ia   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120314" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22454"/>
     <cge:Term_Ref ObjectID="31512"/>
    <cge:TPSR_Ref TObjectID="22454"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-120332" prefix="P  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4259.000000 3.000000) translate(0,16)">P   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120332" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22455"/>
     <cge:Term_Ref ObjectID="31514"/>
    <cge:TPSR_Ref TObjectID="22455"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-120333" prefix="Q " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4259.000000 3.000000) translate(0,36)">Q  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120333" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22455"/>
     <cge:Term_Ref ObjectID="31514"/>
    <cge:TPSR_Ref TObjectID="22455"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-120330" prefix="Ia  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4259.000000 3.000000) translate(0,56)">Ia   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120330" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22455"/>
     <cge:Term_Ref ObjectID="31514"/>
    <cge:TPSR_Ref TObjectID="22455"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-120340" prefix="P  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4450.000000 4.000000) translate(0,16)">P   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120340" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22456"/>
     <cge:Term_Ref ObjectID="31516"/>
    <cge:TPSR_Ref TObjectID="22456"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-120341" prefix="Q " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4450.000000 4.000000) translate(0,36)">Q  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120341" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22456"/>
     <cge:Term_Ref ObjectID="31516"/>
    <cge:TPSR_Ref TObjectID="22456"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-120338" prefix="Ia  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4450.000000 4.000000) translate(0,56)">Ia   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120338" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22456"/>
     <cge:Term_Ref ObjectID="31516"/>
    <cge:TPSR_Ref TObjectID="22456"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-120356" prefix="P  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4643.000000 6.000000) translate(0,16)">P   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120356" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22460"/>
     <cge:Term_Ref ObjectID="31524"/>
    <cge:TPSR_Ref TObjectID="22460"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-120357" prefix="Q " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4643.000000 6.000000) translate(0,36)">Q  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120357" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22460"/>
     <cge:Term_Ref ObjectID="31524"/>
    <cge:TPSR_Ref TObjectID="22460"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-120354" prefix="Ia  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4643.000000 6.000000) translate(0,56)">Ia   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120354" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22460"/>
     <cge:Term_Ref ObjectID="31524"/>
    <cge:TPSR_Ref TObjectID="22460"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-120363" prefix="P  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4818.000000 7.000000) translate(0,16)">P   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120363" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22458"/>
     <cge:Term_Ref ObjectID="31520"/>
    <cge:TPSR_Ref TObjectID="22458"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-120364" prefix="Q " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4818.000000 7.000000) translate(0,36)">Q  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120364" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22458"/>
     <cge:Term_Ref ObjectID="31520"/>
    <cge:TPSR_Ref TObjectID="22458"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-120361" prefix="Ia  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4818.000000 7.000000) translate(0,56)">Ia   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120361" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22458"/>
     <cge:Term_Ref ObjectID="31520"/>
    <cge:TPSR_Ref TObjectID="22458"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-120348" prefix="P  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5016.000000 2.000000) translate(0,16)">P   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120348" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22459"/>
     <cge:Term_Ref ObjectID="31522"/>
    <cge:TPSR_Ref TObjectID="22459"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-120349" prefix="Q " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5016.000000 2.000000) translate(0,36)">Q  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120349" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22459"/>
     <cge:Term_Ref ObjectID="31522"/>
    <cge:TPSR_Ref TObjectID="22459"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-120346" prefix="Ia  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5016.000000 2.000000) translate(0,56)">Ia   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120346" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22459"/>
     <cge:Term_Ref ObjectID="31522"/>
    <cge:TPSR_Ref TObjectID="22459"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-120301" prefix="Q " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5163.000000 -0.000000) translate(0,16)">Q  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120301" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22457"/>
     <cge:Term_Ref ObjectID="31518"/>
    <cge:TPSR_Ref TObjectID="22457"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-120298" prefix="Ia  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5163.000000 -0.000000) translate(0,36)">Ia   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120298" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22457"/>
     <cge:Term_Ref ObjectID="31518"/>
    <cge:TPSR_Ref TObjectID="22457"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-120389" prefix="Ua  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3659.000000 -521.000000) translate(0,16)">Ua   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120389" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22417"/>
     <cge:Term_Ref ObjectID="31439"/>
    <cge:TPSR_Ref TObjectID="22417"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-120390" prefix="Ub " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3659.000000 -521.000000) translate(0,36)">Ub  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120390" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22417"/>
     <cge:Term_Ref ObjectID="31439"/>
    <cge:TPSR_Ref TObjectID="22417"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-120391" prefix="Uc " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3659.000000 -521.000000) translate(0,56)">Uc  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120391" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22417"/>
     <cge:Term_Ref ObjectID="31439"/>
    <cge:TPSR_Ref TObjectID="22417"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-191843" prefix="3Uo " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3659.000000 -521.000000) translate(0,76)">3Uo  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="191843" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22417"/>
     <cge:Term_Ref ObjectID="31439"/>
    <cge:TPSR_Ref TObjectID="22417"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-120375" prefix="Uab " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3659.000000 -521.000000) translate(0,96)">Uab  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120375" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22417"/>
     <cge:Term_Ref ObjectID="31439"/>
    <cge:TPSR_Ref TObjectID="22417"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ubc" PreSymbol="0" appendix="" decimal="2" id="ME-120376" prefix="Ubc " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3659.000000 -521.000000) translate(0,116)">Ubc  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120376" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22417"/>
     <cge:Term_Ref ObjectID="31439"/>
    <cge:TPSR_Ref TObjectID="22417"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uca" PreSymbol="0" appendix="" decimal="2" id="ME-120377" prefix="Uca " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3659.000000 -521.000000) translate(0,136)">Uca  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120377" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22417"/>
     <cge:Term_Ref ObjectID="31439"/>
    <cge:TPSR_Ref TObjectID="22417"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-120302" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5161.000000 -960.000000) translate(0,16)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120302" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22416"/>
     <cge:Term_Ref ObjectID="21162"/>
    <cge:TPSR_Ref TObjectID="22416"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-120303" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5161.000000 -960.000000) translate(0,36)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120303" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22416"/>
     <cge:Term_Ref ObjectID="21162"/>
    <cge:TPSR_Ref TObjectID="22416"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-120304" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5161.000000 -960.000000) translate(0,56)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120304" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22416"/>
     <cge:Term_Ref ObjectID="21162"/>
    <cge:TPSR_Ref TObjectID="22416"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-120372" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5161.000000 -960.000000) translate(0,76)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120372" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22416"/>
     <cge:Term_Ref ObjectID="21162"/>
    <cge:TPSR_Ref TObjectID="22416"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-191679" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.230989 -0.000000 -0.000000 1.203849 4010.030864 -531.038492) translate(0,16)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="191679" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22479"/>
     <cge:Term_Ref ObjectID="31565"/>
    <cge:TPSR_Ref TObjectID="22479"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-120262" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4262.000000 -765.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120262" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22476"/>
     <cge:Term_Ref ObjectID="31556"/>
    <cge:TPSR_Ref TObjectID="22476"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-120263" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4262.000000 -765.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120263" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22476"/>
     <cge:Term_Ref ObjectID="31556"/>
    <cge:TPSR_Ref TObjectID="22476"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-120265" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4262.000000 -765.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120265" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22476"/>
     <cge:Term_Ref ObjectID="31556"/>
    <cge:TPSR_Ref TObjectID="22476"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-120264" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4262.000000 -765.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120264" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22476"/>
     <cge:Term_Ref ObjectID="31556"/>
    <cge:TPSR_Ref TObjectID="22476"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-120274" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4792.000000 -766.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120274" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22478"/>
     <cge:Term_Ref ObjectID="31560"/>
    <cge:TPSR_Ref TObjectID="22478"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-120275" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4792.000000 -766.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120275" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22478"/>
     <cge:Term_Ref ObjectID="31560"/>
    <cge:TPSR_Ref TObjectID="22478"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-120277" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4792.000000 -766.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120277" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22478"/>
     <cge:Term_Ref ObjectID="31560"/>
    <cge:TPSR_Ref TObjectID="22478"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-120276" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4792.000000 -766.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120276" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22478"/>
     <cge:Term_Ref ObjectID="31560"/>
    <cge:TPSR_Ref TObjectID="22478"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-120371" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.343750 -0.000000 -0.000000 1.300813 4011.000000 -593.256098) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120371" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22479"/>
     <cge:Term_Ref ObjectID="31565"/>
    <cge:TPSR_Ref TObjectID="22479"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-191681" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.655344 -0.000000 -0.000000 1.655689 5115.000000 -533.917664) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="191681" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22480"/>
     <cge:Term_Ref ObjectID="31566"/>
    <cge:TPSR_Ref TObjectID="22480"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-120370" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.316952 -0.000000 -0.000000 1.333333 5116.000000 -593.500000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120370" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22480"/>
     <cge:Term_Ref ObjectID="31566"/>
    <cge:TPSR_Ref TObjectID="22480"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="67" qtmmishow="hidden" width="202" x="3313" y="-1160"/>
    </a>
   <metadata/><rect fill="white" height="67" opacity="0" stroke="white" transform="" width="202" x="3313" y="-1160"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="100" qtmmishow="hidden" width="116" x="3228" y="-1181"/>
    </a>
   <metadata/><rect fill="white" height="100" opacity="0" stroke="white" transform="" width="116" x="3228" y="-1181"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="34" x="4477" y="-295"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="34" x="4477" y="-295"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="34" x="4859" y="-294"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="34" x="4859" y="-294"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="35" x="5053" y="-297"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="35" x="5053" y="-297"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="35" x="4670" y="-293"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="35" x="4670" y="-293"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="35" x="4284" y="-296"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="35" x="4284" y="-296"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="35" x="4093" y="-297"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="35" x="4093" y="-297"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="34" x="3902" y="-295"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="34" x="3902" y="-295"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="34" x="3710" y="-297"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="34" x="3710" y="-297"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="35" x="5245" y="-299"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="35" x="5245" y="-299"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="130" x="4215" y="-668"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="130" x="4215" y="-668"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="129" x="4937" y="-667"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="129" x="4937" y="-667"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="36" x="4146" y="-917"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="36" x="4146" y="-917"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="36" x="4516" y="-918"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="36" x="4516" y="-918"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="36" x="4869" y="-914"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="36" x="4869" y="-914"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="63" x="3230" y="-679"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="63" x="3230" y="-679"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3650" y="-1128"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3650" y="-1128"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3650" y="-1163"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3650" y="-1163"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="44" qtmmishow="hidden" width="73" x="3545" y="-1150"/>
    </a>
   <metadata/><rect fill="white" height="44" opacity="0" stroke="white" transform="" width="73" x="3545" y="-1150"/></g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="67" qtmmishow="hidden" width="202" x="3313" y="-1160"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="100" qtmmishow="hidden" width="116" x="3228" y="-1181"/></g>
   <g href="35kV白邑变WD_BY_035间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="34" x="4477" y="-295"/></g>
   <g href="35kV白邑变WD_BY_037间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="34" x="4859" y="-294"/></g>
   <g href="35kV白邑变WD_BY_038间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="35" x="5053" y="-297"/></g>
   <g href="35kV白邑变WD_BY_036间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="35" x="4670" y="-293"/></g>
   <g href="35kV白邑变WD_BY_034间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="35" x="4284" y="-296"/></g>
   <g href="35kV白邑变WD_BY_033间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="35" x="4093" y="-297"/></g>
   <g href="35kV白邑变WD_BY_032间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="34" x="3902" y="-295"/></g>
   <g href="35kV白邑变WD_BY_031间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="34" x="3710" y="-297"/></g>
   <g href="35kV白邑变WD_BY_039间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="35" x="5245" y="-299"/></g>
   <g href="35kV白邑变白邑变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="130" x="4215" y="-668"/></g>
   <g href="35kV白邑变白邑变2号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="129" x="4937" y="-667"/></g>
   <g href="35kV白邑变WD_BY_331间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="36" x="4146" y="-917"/></g>
   <g href="35kV白邑变WD_BY_333间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="36" x="4516" y="-918"/></g>
   <g href="35kV白邑变WD_BY_332间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="36" x="4869" y="-914"/></g>
   <g href="35kV白邑变GG间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="63" x="3230" y="-679"/></g>
   <g href="cx_配调_配网接线图35_武定.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3650" y="-1128"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3650" y="-1163"/></g>
   <g href="AVC白邑站.svg" style="fill-opacity:0"><rect height="44" qtmmishow="hidden" width="73" x="3545" y="-1150"/></g>
  </g><g id="PolygonFilled_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3696,-183 3689,-168 3704,-168 3696,-183 3696,-183 3696,-183 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3696,-195 3689,-210 3704,-210 3696,-195 3696,-195 3696,-195 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3889,-183 3882,-168 3897,-168 3889,-183 3889,-183 3889,-183 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3889,-195 3882,-210 3897,-210 3889,-195 3889,-195 3889,-195 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4080,-185 4073,-170 4088,-170 4080,-185 4080,-185 4080,-185 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4080,-197 4073,-212 4088,-212 4080,-197 4080,-197 4080,-197 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4272,-182 4265,-167 4280,-167 4272,-182 4272,-182 4272,-182 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4272,-194 4265,-209 4280,-209 4272,-194 4272,-194 4272,-194 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4464,-183 4457,-168 4472,-168 4464,-183 4464,-183 4464,-183 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4464,-195 4457,-210 4472,-210 4464,-195 4464,-195 4464,-195 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4657,-183 4650,-168 4665,-168 4657,-183 4657,-183 4657,-183 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4657,-195 4650,-210 4665,-210 4657,-195 4657,-195 4657,-195 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4848,-182 4841,-167 4856,-167 4848,-182 4848,-182 4848,-182 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4848,-194 4841,-209 4856,-209 4848,-194 4848,-194 4848,-194 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="5040,-185 5033,-170 5048,-170 5040,-185 5040,-185 5040,-185 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="5040,-197 5033,-212 5048,-212 5040,-197 5040,-197 5040,-197 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="5232,-187 5225,-172 5240,-172 5232,-187 5232,-187 5232,-187 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="5232,-199 5225,-214 5240,-214 5232,-199 5232,-199 5232,-199 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="5503,-203 5496,-218 5511,-218 5503,-203 5503,-203 5503,-203 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="5503,-191 5496,-176 5511,-176 5503,-191 5503,-191 5503,-191 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4129,-579 4122,-594 4137,-594 4129,-579 4129,-579 4129,-579 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4129,-567 4122,-552 4137,-552 4129,-567 4129,-567 4129,-567 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4848,-579 4841,-594 4856,-594 4848,-579 4848,-579 4848,-579 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4848,-567 4841,-552 4856,-552 4848,-567 4848,-567 4848,-567 " stroke="rgb(0,255,0)"/>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_228d1d0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4417.000000 -636.863636)" xlink:href="#voltageTransformer:shape94"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3685b50">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5403.000000 -162.000000)" xlink:href="#voltageTransformer:shape93"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-WD_BY.031Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3687.000000 -6.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37630" ObjectName="EC-WD_BY.031Ld"/>
    <cge:TPSR_Ref TObjectID="37630"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-WD_BY.032Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3880.000000 -6.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37631" ObjectName="EC-WD_BY.032Ld"/>
    <cge:TPSR_Ref TObjectID="37631"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-WD_BY.033Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4071.000000 -8.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37632" ObjectName="EC-WD_BY.033Ld"/>
    <cge:TPSR_Ref TObjectID="37632"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-WD_BY.034Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4263.000000 -5.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37633" ObjectName="EC-WD_BY.034Ld"/>
    <cge:TPSR_Ref TObjectID="37633"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-WD_BY.035Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4455.000000 -6.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37634" ObjectName="EC-WD_BY.035Ld"/>
    <cge:TPSR_Ref TObjectID="37634"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-WD_BY.036Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4648.000000 -6.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37635" ObjectName="EC-WD_BY.036Ld"/>
    <cge:TPSR_Ref TObjectID="37635"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-WD_BY.037Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4839.000000 -5.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37636" ObjectName="EC-WD_BY.037Ld"/>
    <cge:TPSR_Ref TObjectID="37636"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-WD_BY.038Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5031.400000 -8.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37637" ObjectName="EC-WD_BY.038Ld"/>
    <cge:TPSR_Ref TObjectID="37637"/></metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_230a120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4129,-800 4129,-823 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22424@1" ObjectIDZND0="22416@0" Pin0InfoVect0LinkObjId="g_1f2e120_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120398_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4129,-800 4129,-823 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c2ab10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4129,-748 4129,-764 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22476@1" ObjectIDZND0="22424@0" Pin0InfoVect0LinkObjId="SW-120398_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120468_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4129,-748 4129,-764 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ef59d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4129,-721 4129,-695 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="22476@0" ObjectIDZND0="22479@0" Pin0InfoVect0LinkObjId="g_22f5700_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120468_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4129,-721 4129,-695 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c288d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4848,-749 4848,-765 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22478@1" ObjectIDZND0="22427@0" Pin0InfoVect0LinkObjId="SW-120401_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120476_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4848,-749 4848,-765 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39dc890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4848,-504 4848,-478 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22428@0" ObjectIDZND0="22477@1" Pin0InfoVect0LinkObjId="SW-120480_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120402_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4848,-504 4848,-478 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d30db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4848,-451 4848,-429 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22477@0" ObjectIDZND0="22429@1" Pin0InfoVect0LinkObjId="SW-120403_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120480_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4848,-451 4848,-429 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f04030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4848,-722 4848,-697 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="22478@0" ObjectIDZND0="22480@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120476_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4848,-722 4848,-697 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c4c4e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4848,-605 4848,-540 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="22480@1" ObjectIDZND0="22428@1" Pin0InfoVect0LinkObjId="SW-120402_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f04030_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4848,-605 4848,-540 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f2e120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4426,-795 4426,-823 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22461@1" ObjectIDZND0="22416@0" Pin0InfoVect0LinkObjId="g_230a120_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120447_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4426,-795 4426,-823 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_39bfd10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4426,-736 4385,-736 4385,-720 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_228d1d0@0" ObjectIDND1="22461@x" ObjectIDZND0="g_280f410@0" Pin0InfoVect0LinkObjId="g_280f410_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_228d1d0_0" Pin1InfoVect1LinkObjId="SW-120447_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4426,-736 4385,-736 4385,-720 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c4ca60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4426,-711 4426,-736 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_228d1d0@0" ObjectIDZND0="g_280f410@0" ObjectIDZND1="22461@x" Pin0InfoVect0LinkObjId="g_280f410_0" Pin0InfoVect1LinkObjId="SW-120447_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_228d1d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4426,-711 4426,-736 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2306e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4426,-736 4426,-759 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" EndDevType0="switch" ObjectIDND0="g_280f410@0" ObjectIDND1="g_228d1d0@0" ObjectIDZND0="22461@0" Pin0InfoVect0LinkObjId="SW-120447_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_280f410_0" Pin1InfoVect1LinkObjId="g_228d1d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4426,-736 4426,-759 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23d3630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4129,-428 4129,-451 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22426@1" ObjectIDZND0="22475@0" Pin0InfoVect0LinkObjId="SW-120472_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120400_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4129,-428 4129,-451 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22b7dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4642,-823 4642,-782 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="22416@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_230a120_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4642,-823 4642,-782 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_371c520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4642,-730 4642,-678 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4642,-730 4642,-678 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_39e3fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4496,-841 4496,-823 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22423@0" ObjectIDZND0="22416@0" Pin0InfoVect0LinkObjId="g_230a120_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120397_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4496,-841 4496,-823 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2700380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4496,-894 4496,-877 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22451@0" ObjectIDZND0="22423@1" Pin0InfoVect0LinkObjId="SW-120397_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120427_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4496,-894 4496,-877 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c77170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4496,-940 4496,-921 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22422@0" ObjectIDZND0="22451@1" Pin0InfoVect0LinkObjId="SW-120427_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120396_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4496,-940 4496,-921 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_285ab90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4434,-1022 4369,-1022 4369,-1000 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="22464@x" ObjectIDND2="22422@x" ObjectIDZND0="g_34dc560@0" Pin0InfoVect0LinkObjId="g_34dc560_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-120450_0" Pin1InfoVect2LinkObjId="SW-120396_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4434,-1022 4369,-1022 4369,-1000 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_23092d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4434,-1022 4434,-1004 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_34dc560@0" ObjectIDND1="22464@x" ObjectIDND2="22422@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_34dc560_0" Pin1InfoVect1LinkObjId="SW-120450_0" Pin1InfoVect2LinkObjId="SW-120396_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4434,-1022 4434,-1004 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_22b2f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4434,-952 4434,-920 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_39dbcf0@0" Pin0InfoVect0LinkObjId="g_39dbcf0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4434,-952 4434,-920 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1db78f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4555,-993 4577,-993 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="22464@1" ObjectIDZND0="g_1f03c40@0" Pin0InfoVect0LinkObjId="g_1f03c40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120450_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4555,-993 4577,-993 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1db5520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4496,-993 4519,-993 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="22422@x" ObjectIDND1="g_34dc560@0" ObjectIDND2="0@x" ObjectIDZND0="22464@0" Pin0InfoVect0LinkObjId="SW-120450_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-120396_0" Pin1InfoVect1LinkObjId="g_34dc560_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4496,-993 4519,-993 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_39e4650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4848,-893 4848,-876 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22450@0" ObjectIDZND0="22421@1" Pin0InfoVect0LinkObjId="SW-120395_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120425_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4848,-893 4848,-876 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24251a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4848,-939 4848,-920 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22420@0" ObjectIDZND0="22450@1" Pin0InfoVect0LinkObjId="SW-120425_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120394_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4848,-939 4848,-920 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2428560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4780,-1015 4780,-997 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_1c2cee0@0" ObjectIDND1="22463@x" ObjectIDND2="22420@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1c2cee0_0" Pin1InfoVect1LinkObjId="SW-120449_0" Pin1InfoVect2LinkObjId="SW-120394_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4780,-1015 4780,-997 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24280e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4780,-945 4780,-913 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_1ca2190@0" Pin0InfoVect0LinkObjId="g_1ca2190_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4780,-945 4780,-913 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_39dbf00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4907,-992 4929,-992 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="22463@1" ObjectIDZND0="g_364ac40@0" Pin0InfoVect0LinkObjId="g_364ac40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120449_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4907,-992 4929,-992 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3706ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4848,-992 4871,-992 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_1c2cee0@0" ObjectIDND2="37808@1" ObjectIDZND0="22463@0" Pin0InfoVect0LinkObjId="SW-120449_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_1c2cee0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4848,-992 4871,-992 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bd1920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4848,-801 4848,-823 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22427@1" ObjectIDZND0="22416@0" Pin0InfoVect0LinkObjId="g_230a120_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120401_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4848,-801 4848,-823 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34d0980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4848,-840 4848,-823 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22421@0" ObjectIDZND0="22416@0" Pin0InfoVect0LinkObjId="g_230a120_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120395_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4848,-840 4848,-823 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b8d2a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4129,-893 4129,-876 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22449@0" ObjectIDZND0="22419@1" Pin0InfoVect0LinkObjId="SW-120393_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120423_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4129,-893 4129,-876 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3687750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4129,-939 4129,-920 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22418@0" ObjectIDZND0="22449@1" Pin0InfoVect0LinkObjId="SW-120423_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120392_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4129,-939 4129,-920 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_351ab50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4129,-1027 4067,-1027 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="powerLine" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="22462@x" ObjectIDND1="22418@x" ObjectIDND2="34565@1" ObjectIDZND0="g_3688600@0" ObjectIDZND1="g_3467060@0" Pin0InfoVect0LinkObjId="g_3688600_0" Pin0InfoVect1LinkObjId="g_3467060_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-120448_0" Pin1InfoVect1LinkObjId="SW-120392_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4129,-1027 4067,-1027 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d34fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4067,-1027 4002,-1027 4002,-1005 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="powerLine" EndDevType0="lightningRod" ObjectIDND0="22462@x" ObjectIDND1="22418@x" ObjectIDND2="34565@1" ObjectIDZND0="g_3688600@0" Pin0InfoVect0LinkObjId="g_3688600_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-120448_0" Pin1InfoVect1LinkObjId="SW-120392_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4067,-1027 4002,-1027 4002,-1005 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1db8630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4188,-992 4210,-992 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="22462@1" ObjectIDZND0="g_1ca59f0@0" Pin0InfoVect0LinkObjId="g_1ca59f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120448_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4188,-992 4210,-992 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ca2f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4129,-992 4152,-992 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" ObjectIDND0="g_3688600@0" ObjectIDND1="g_3467060@0" ObjectIDND2="34565@1" ObjectIDZND0="22462@0" Pin0InfoVect0LinkObjId="SW-120448_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3688600_0" Pin1InfoVect1LinkObjId="g_3467060_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4129,-992 4152,-992 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ca35f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4129,-840 4129,-823 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22419@0" ObjectIDZND0="22416@0" Pin0InfoVect0LinkObjId="g_230a120_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120393_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4129,-840 4129,-823 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39e8510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3696,-357 3696,-373 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22430@1" ObjectIDZND0="22417@0" Pin0InfoVect0LinkObjId="g_39e90b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120404_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3696,-357 3696,-373 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1db9cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3664,-99 3664,-111 3696,-111 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_1efc090@0" ObjectIDZND0="37630@x" ObjectIDZND1="22467@x" Pin0InfoVect0LinkObjId="EC-WD_BY.031Ld_0" Pin0InfoVect1LinkObjId="SW-120453_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1efc090_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3664,-99 3664,-111 3696,-111 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ca13d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3696,-263 3696,-274 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22431@1" ObjectIDZND0="22452@0" Pin0InfoVect0LinkObjId="SW-120429_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120405_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3696,-263 3696,-274 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39f5a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3696,-33 3696,-111 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="37630@0" ObjectIDZND0="g_1efc090@0" ObjectIDZND1="22467@x" Pin0InfoVect0LinkObjId="g_1efc090_0" Pin0InfoVect1LinkObjId="SW-120453_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-WD_BY.031Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3696,-33 3696,-111 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36c1b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3696,-111 3696,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_1efc090@0" ObjectIDND1="37630@x" ObjectIDZND0="22467@0" Pin0InfoVect0LinkObjId="SW-120453_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1efc090_0" Pin1InfoVect1LinkObjId="EC-WD_BY.031Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3696,-111 3696,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39e90b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4129,-392 4129,-373 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22426@0" ObjectIDZND0="22417@0" Pin0InfoVect0LinkObjId="g_39e8510_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120400_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4129,-392 4129,-373 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39f4320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4848,-393 4848,-373 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22429@0" ObjectIDZND0="22417@0" Pin0InfoVect0LinkObjId="g_39e8510_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120403_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4848,-393 4848,-373 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39f4af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3696,-321 3696,-301 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22430@0" ObjectIDZND0="22452@1" Pin0InfoVect0LinkObjId="SW-120429_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120404_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3696,-321 3696,-301 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39a9530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3696,-158 3696,-227 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="22467@1" ObjectIDZND0="22431@0" Pin0InfoVect0LinkObjId="SW-120405_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120453_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3696,-158 3696,-227 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36b52f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3857,-99 3857,-111 3889,-111 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_1f2ff20@0" ObjectIDZND0="37631@x" ObjectIDZND1="22468@x" Pin0InfoVect0LinkObjId="EC-WD_BY.032Ld_0" Pin0InfoVect1LinkObjId="SW-120454_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f2ff20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3857,-99 3857,-111 3889,-111 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_368ff50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3889,-263 3889,-274 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22433@1" ObjectIDZND0="22453@0" Pin0InfoVect0LinkObjId="SW-120431_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120407_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3889,-263 3889,-274 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1efd1b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3889,-33 3889,-111 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="37631@0" ObjectIDZND0="g_1f2ff20@0" ObjectIDZND1="22468@x" Pin0InfoVect0LinkObjId="g_1f2ff20_0" Pin0InfoVect1LinkObjId="SW-120454_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-WD_BY.032Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3889,-33 3889,-111 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39daec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3889,-111 3889,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="37631@x" ObjectIDND1="g_1f2ff20@0" ObjectIDZND0="22468@0" Pin0InfoVect0LinkObjId="SW-120454_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-WD_BY.032Ld_0" Pin1InfoVect1LinkObjId="g_1f2ff20_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3889,-111 3889,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c75ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3889,-321 3889,-301 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22432@0" ObjectIDZND0="22453@1" Pin0InfoVect0LinkObjId="SW-120431_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120406_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3889,-321 3889,-301 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1db4fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3889,-158 3889,-227 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="22468@1" ObjectIDZND0="22433@0" Pin0InfoVect0LinkObjId="SW-120407_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120454_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3889,-158 3889,-227 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3bd15d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3889,-357 3889,-373 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22432@1" ObjectIDZND0="22417@0" Pin0InfoVect0LinkObjId="g_39e8510_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120406_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3889,-357 3889,-373 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23ec5e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4048,-101 4048,-113 4080,-113 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_3649380@0" ObjectIDZND0="37632@x" ObjectIDZND1="22471@x" Pin0InfoVect0LinkObjId="EC-WD_BY.033Ld_0" Pin0InfoVect1LinkObjId="SW-120457_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3649380_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4048,-101 4048,-113 4080,-113 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3495d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4080,-35 4080,-113 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="37632@0" ObjectIDZND0="g_3649380@0" ObjectIDZND1="22471@x" Pin0InfoVect0LinkObjId="g_3649380_0" Pin0InfoVect1LinkObjId="SW-120457_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-WD_BY.033Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4080,-35 4080,-113 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39dda00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4080,-113 4080,-124 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="37632@x" ObjectIDND1="g_3649380@0" ObjectIDZND0="22471@0" Pin0InfoVect0LinkObjId="SW-120457_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-WD_BY.033Ld_0" Pin1InfoVect1LinkObjId="g_3649380_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4080,-113 4080,-124 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f00610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4080,-323 4080,-303 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22434@0" ObjectIDZND0="22454@1" Pin0InfoVect0LinkObjId="SW-120433_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120408_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4080,-323 4080,-303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f12910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4080,-160 4080,-229 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="22471@1" ObjectIDZND0="22435@0" Pin0InfoVect0LinkObjId="SW-120409_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120457_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4080,-160 4080,-229 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22a1740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4080,-359 4080,-373 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22434@1" ObjectIDZND0="22417@0" Pin0InfoVect0LinkObjId="g_39e8510_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120408_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4080,-359 4080,-373 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c2c8d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4080,-276 4080,-265 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22454@0" ObjectIDZND0="22435@1" Pin0InfoVect0LinkObjId="SW-120409_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120433_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4080,-276 4080,-265 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39ee8f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4240,-98 4240,-110 4272,-110 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_39eacc0@0" ObjectIDZND0="37633@x" ObjectIDZND1="22469@x" Pin0InfoVect0LinkObjId="EC-WD_BY.034Ld_0" Pin0InfoVect1LinkObjId="SW-120455_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_39eacc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4240,-98 4240,-110 4272,-110 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39eeae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4272,-262 4272,-275 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22437@1" ObjectIDZND0="22455@0" Pin0InfoVect0LinkObjId="SW-120435_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120411_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4272,-262 4272,-275 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39ee120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4272,-32 4272,-110 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="37633@0" ObjectIDZND0="g_39eacc0@0" ObjectIDZND1="22469@x" Pin0InfoVect0LinkObjId="g_39eacc0_0" Pin0InfoVect1LinkObjId="SW-120455_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-WD_BY.034Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4272,-32 4272,-110 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39ee310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4272,-110 4272,-121 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="37633@x" ObjectIDND1="g_39eacc0@0" ObjectIDZND0="22469@0" Pin0InfoVect0LinkObjId="SW-120455_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-WD_BY.034Ld_0" Pin1InfoVect1LinkObjId="g_39eacc0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4272,-110 4272,-121 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39efac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4272,-320 4272,-302 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22436@0" ObjectIDZND0="22455@1" Pin0InfoVect0LinkObjId="SW-120435_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120410_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4272,-320 4272,-302 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39f1830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4272,-157 4272,-226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="22469@1" ObjectIDZND0="22437@0" Pin0InfoVect0LinkObjId="SW-120411_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120455_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4272,-157 4272,-226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_241e620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4272,-356 4272,-373 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22436@1" ObjectIDZND0="22417@0" Pin0InfoVect0LinkObjId="g_39e8510_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120410_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4272,-356 4272,-373 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39874c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4432,-99 4432,-111 4464,-111 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_36895f0@0" ObjectIDZND0="37634@x" ObjectIDZND1="22470@x" Pin0InfoVect0LinkObjId="EC-WD_BY.035Ld_0" Pin0InfoVect1LinkObjId="SW-120456_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36895f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4432,-99 4432,-111 4464,-111 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_344ee30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4464,-263 4464,-274 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22439@1" ObjectIDZND0="22456@0" Pin0InfoVect0LinkObjId="SW-120437_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120413_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4464,-263 4464,-274 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_344f020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4464,-33 4464,-111 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="37634@0" ObjectIDZND0="g_36895f0@0" ObjectIDZND1="22470@x" Pin0InfoVect0LinkObjId="g_36895f0_0" Pin0InfoVect1LinkObjId="SW-120456_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-WD_BY.035Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4464,-33 4464,-111 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3548960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4464,-111 4464,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_36895f0@0" ObjectIDND1="37634@x" ObjectIDZND0="22470@0" Pin0InfoVect0LinkObjId="SW-120456_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_36895f0_0" Pin1InfoVect1LinkObjId="EC-WD_BY.035Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4464,-111 4464,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3640ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4464,-321 4464,-301 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22438@0" ObjectIDZND0="22456@1" Pin0InfoVect0LinkObjId="SW-120437_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120412_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4464,-321 4464,-301 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3640f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4464,-158 4464,-227 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="22470@1" ObjectIDZND0="22439@0" Pin0InfoVect0LinkObjId="SW-120413_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120456_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4464,-158 4464,-227 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35060e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4464,-361 4464,-373 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22438@1" ObjectIDZND0="22417@0" Pin0InfoVect0LinkObjId="g_39e8510_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120412_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4464,-361 4464,-373 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c91610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4625,-99 4625,-111 4657,-111 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_39e0360@0" ObjectIDZND0="37635@x" ObjectIDZND1="22474@x" Pin0InfoVect0LinkObjId="EC-WD_BY.036Ld_0" Pin0InfoVect1LinkObjId="SW-120486_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_39e0360_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4625,-99 4625,-111 4657,-111 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c91800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4657,-263 4657,-274 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22447@1" ObjectIDZND0="22460@0" Pin0InfoVect0LinkObjId="SW-120445_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120421_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4657,-263 4657,-274 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1eb2e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4657,-33 4657,-111 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="37635@0" ObjectIDZND0="g_39e0360@0" ObjectIDZND1="22474@x" Pin0InfoVect0LinkObjId="g_39e0360_0" Pin0InfoVect1LinkObjId="SW-120486_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-WD_BY.036Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4657,-33 4657,-111 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1eb3020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4657,-111 4657,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_39e0360@0" ObjectIDND1="37635@x" ObjectIDZND0="22474@0" Pin0InfoVect0LinkObjId="SW-120486_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_39e0360_0" Pin1InfoVect1LinkObjId="EC-WD_BY.036Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4657,-111 4657,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36afbc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4657,-321 4657,-299 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22446@0" ObjectIDZND0="22460@1" Pin0InfoVect0LinkObjId="SW-120445_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120420_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4657,-321 4657,-299 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34cd110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4657,-158 4657,-227 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="22474@1" ObjectIDZND0="22447@0" Pin0InfoVect0LinkObjId="SW-120421_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120486_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4657,-158 4657,-227 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35499b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4657,-357 4657,-373 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22446@1" ObjectIDZND0="22417@0" Pin0InfoVect0LinkObjId="g_39e8510_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120420_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4657,-357 4657,-373 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c930f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4816,-98 4816,-110 4848,-110 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_36e58d0@0" ObjectIDZND0="37636@x" ObjectIDZND1="22472@x" Pin0InfoVect0LinkObjId="EC-WD_BY.037Ld_0" Pin0InfoVect1LinkObjId="SW-120484_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36e58d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4816,-98 4816,-110 4848,-110 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c932e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4848,-262 4848,-273 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22443@1" ObjectIDZND0="22458@0" Pin0InfoVect0LinkObjId="SW-120441_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120417_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4848,-262 4848,-273 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ee8d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4848,-32 4848,-110 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="37636@0" ObjectIDZND0="g_36e58d0@0" ObjectIDZND1="22472@x" Pin0InfoVect0LinkObjId="g_36e58d0_0" Pin0InfoVect1LinkObjId="SW-120484_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-WD_BY.037Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4848,-32 4848,-110 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ee8f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4848,-110 4848,-121 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="37636@x" ObjectIDND1="g_36e58d0@0" ObjectIDZND0="22472@0" Pin0InfoVect0LinkObjId="SW-120484_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-WD_BY.037Ld_0" Pin1InfoVect1LinkObjId="g_36e58d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4848,-110 4848,-121 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22a65c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4848,-317 4848,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22442@0" ObjectIDZND0="22458@1" Pin0InfoVect0LinkObjId="SW-120441_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120416_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4848,-317 4848,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22a67d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4848,-157 4848,-226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="22472@1" ObjectIDZND0="22443@0" Pin0InfoVect0LinkObjId="SW-120417_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120484_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4848,-157 4848,-226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1eae7f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4848,-356 4848,-373 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22442@1" ObjectIDZND0="22417@0" Pin0InfoVect0LinkObjId="g_39e8510_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120416_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4848,-356 4848,-373 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f355e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5008,-101 5008,-113 5040,-113 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_1eb4520@0" ObjectIDZND0="37637@x" ObjectIDZND1="22473@x" Pin0InfoVect0LinkObjId="EC-WD_BY.038Ld_0" Pin0InfoVect1LinkObjId="SW-120485_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1eb4520_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5008,-101 5008,-113 5040,-113 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f357d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5040,-265 5040,-276 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22445@1" ObjectIDZND0="22459@0" Pin0InfoVect0LinkObjId="SW-120443_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120419_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5040,-265 5040,-276 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2473ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5040,-35 5040,-113 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="37637@0" ObjectIDZND0="g_1eb4520@0" ObjectIDZND1="22473@x" Pin0InfoVect0LinkObjId="g_1eb4520_0" Pin0InfoVect1LinkObjId="SW-120485_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-WD_BY.038Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5040,-35 5040,-113 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2473ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5040,-113 5040,-124 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="37637@x" ObjectIDND1="g_1eb4520@0" ObjectIDZND0="22473@0" Pin0InfoVect0LinkObjId="SW-120485_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-WD_BY.038Ld_0" Pin1InfoVect1LinkObjId="g_1eb4520_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5040,-113 5040,-124 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36aec40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5040,-323 5040,-303 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22444@0" ObjectIDZND0="22459@1" Pin0InfoVect0LinkObjId="SW-120443_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120418_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5040,-323 5040,-303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3527ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5040,-160 5040,-229 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="22473@1" ObjectIDZND0="22445@0" Pin0InfoVect0LinkObjId="SW-120419_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120485_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5040,-160 5040,-229 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_225de20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5041,-373 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" ObjectIDND0="22417@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_39e8510_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5041,-373 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_352a980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5041,-373 5040,-359 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="22417@0" ObjectIDZND0="22444@1" Pin0InfoVect0LinkObjId="SW-120418_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_39e8510_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5041,-373 5040,-359 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2413d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5232,-325 5232,-305 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22440@0" ObjectIDZND0="22457@1" Pin0InfoVect0LinkObjId="SW-120439_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120414_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5232,-325 5232,-305 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ba6980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5232,-373 5232,-361 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="22417@0" ObjectIDZND0="22440@1" Pin0InfoVect0LinkObjId="SW-120414_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_39e8510_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5232,-373 5232,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22f4c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5168,-96 5168,-105 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" ObjectIDND0="41934@0" ObjectIDZND0="22465@x" Pin0InfoVect0LinkObjId="SW-120451_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="CB-WD_BY.WD_BY_1Cb_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5168,-96 5168,-105 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22f4ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5296,-106 5296,-96 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" ObjectIDND0="22466@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120452_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5296,-106 5296,-96 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c91f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5424,-373 5424,-357 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="22417@0" ObjectIDZND0="22448@1" Pin0InfoVect0LinkObjId="SW-120422_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_39e8510_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5424,-373 5424,-357 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2410aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5424,-321 5424,-287 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="voltageTransformer" ObjectIDND0="22448@0" ObjectIDZND0="41935@x" ObjectIDZND1="g_1f2f4d0@0" ObjectIDZND2="g_3685b50@0" Pin0InfoVect0LinkObjId="SW-190830_0" Pin0InfoVect1LinkObjId="g_1f2f4d0_0" Pin0InfoVect2LinkObjId="g_3685b50_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120422_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5424,-321 5424,-287 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2410d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5424,-252 5374,-252 5374,-233 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_3685b50@0" ObjectIDND1="22448@x" ObjectIDND2="41935@x" ObjectIDZND0="g_1f2f4d0@0" Pin0InfoVect0LinkObjId="g_1f2f4d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3685b50_0" Pin1InfoVect1LinkObjId="SW-120422_0" Pin1InfoVect2LinkObjId="SW-190830_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5424,-252 5374,-252 5374,-233 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ce9a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5424,-287 5424,-250 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" ObjectIDND0="22448@x" ObjectIDND1="41935@x" ObjectIDZND0="g_1f2f4d0@0" ObjectIDZND1="g_3685b50@0" Pin0InfoVect0LinkObjId="g_1f2f4d0_0" Pin0InfoVect1LinkObjId="g_3685b50_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-120422_0" Pin1InfoVect1LinkObjId="SW-190830_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5424,-287 5424,-250 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ce9c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5424,-252 5424,-236 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="voltageTransformer" ObjectIDND0="g_1f2f4d0@0" ObjectIDND1="22448@x" ObjectIDND2="41935@x" ObjectIDZND0="g_3685b50@0" Pin0InfoVect0LinkObjId="g_3685b50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1f2f4d0_0" Pin1InfoVect1LinkObjId="SW-120422_0" Pin1InfoVect2LinkObjId="SW-190830_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5424,-252 5424,-236 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2284a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5424,-287 5503,-287 5503,-273 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="voltageTransformer" EndDevType0="switch" ObjectIDND0="22448@x" ObjectIDND1="g_1f2f4d0@0" ObjectIDND2="g_3685b50@0" ObjectIDZND0="41935@1" Pin0InfoVect0LinkObjId="SW-190830_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-120422_0" Pin1InfoVect1LinkObjId="g_1f2f4d0_0" Pin1InfoVect2LinkObjId="g_3685b50_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5424,-287 5503,-287 5503,-273 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_351ec10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5503,-237 5503,-114 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="41935@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-190830_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5503,-237 5503,-114 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b9ea40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4129,-478 4129,-504 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22475@1" ObjectIDZND0="22425@0" Pin0InfoVect0LinkObjId="SW-120399_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120472_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4129,-478 4129,-504 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22f5700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4129,-539 4129,-604 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="22425@1" ObjectIDZND0="22479@1" Pin0InfoVect0LinkObjId="g_1ef59d0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120399_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4129,-539 4129,-604 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d00650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4847,-1015 4780,-1015 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="powerLine" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="22463@x" ObjectIDND1="22420@x" ObjectIDND2="37808@1" ObjectIDZND0="0@x" ObjectIDZND1="g_1c2cee0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_1c2cee0_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-120449_0" Pin1InfoVect1LinkObjId="SW-120394_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4847,-1015 4780,-1015 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d00840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4780,-1015 4720,-1015 4720,-993 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="22463@x" ObjectIDND2="22420@x" ObjectIDZND0="g_1c2cee0@0" Pin0InfoVect0LinkObjId="g_1c2cee0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-120449_0" Pin1InfoVect2LinkObjId="SW-120394_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4780,-1015 4720,-1015 4720,-993 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1edbfc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5168,-95 5168,-105 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="capacitor" EndDevType1="switch" ObjectIDZND0="41934@x" ObjectIDZND1="22465@x" Pin0InfoVect0LinkObjId="CB-WD_BY.WD_BY_1Cb_0" Pin0InfoVect1LinkObjId="SW-120451_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5168,-95 5168,-105 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ba8590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5296,-106 5296,-95 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="22466@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-120452_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5296,-106 5296,-95 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_229eb00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5168,-105 5168,-115 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" ObjectIDND0="41934@x" ObjectIDZND0="22465@0" Pin0InfoVect0LinkObjId="SW-120451_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="CB-WD_BY.WD_BY_1Cb_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5168,-105 5168,-115 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_229ed60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5168,-151 5168,-159 5232,-159 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="22465@1" ObjectIDZND0="22466@x" ObjectIDZND1="22441@x" Pin0InfoVect0LinkObjId="SW-120452_0" Pin0InfoVect1LinkObjId="SW-120415_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120451_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5168,-151 5168,-159 5232,-159 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2276040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5232,-159 5296,-159 5296,-152 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="22465@x" ObjectIDND1="22441@x" ObjectIDZND0="22466@1" Pin0InfoVect0LinkObjId="SW-120452_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-120451_0" Pin1InfoVect1LinkObjId="SW-120415_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5232,-159 5296,-159 5296,-152 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22762a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5296,-116 5296,-106 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" ObjectIDND0="22466@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120452_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5296,-116 5296,-106 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1edf790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5232,-159 5232,-229 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="22465@x" ObjectIDND1="22466@x" ObjectIDZND0="22441@0" Pin0InfoVect0LinkObjId="SW-120415_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-120451_0" Pin1InfoVect1LinkObjId="SW-120452_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5232,-159 5232,-229 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1edf9f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5232,-265 5232,-278 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22441@1" ObjectIDZND0="22457@0" Pin0InfoVect0LinkObjId="SW-120439_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120415_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5232,-265 5232,-278 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c0ace0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4129,-1105 4129,-1027 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="34565@0" ObjectIDZND0="g_3688600@0" ObjectIDZND1="g_3467060@0" ObjectIDZND2="22462@x" Pin0InfoVect0LinkObjId="g_3688600_0" Pin0InfoVect1LinkObjId="g_3467060_0" Pin0InfoVect2LinkObjId="SW-120448_0" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4129,-1105 4129,-1027 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c0aed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4129,-1027 4129,-992 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_3688600@0" ObjectIDND1="g_3467060@0" ObjectIDND2="34565@1" ObjectIDZND0="22462@x" ObjectIDZND1="22418@x" Pin0InfoVect0LinkObjId="SW-120448_0" Pin0InfoVect1LinkObjId="SW-120392_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3688600_0" Pin1InfoVect1LinkObjId="g_3467060_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4129,-1027 4129,-992 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3494bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4129,-975 4129,-992 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="22418@1" ObjectIDZND0="22462@x" ObjectIDZND1="g_3688600@0" ObjectIDZND2="g_3467060@0" Pin0InfoVect0LinkObjId="SW-120448_0" Pin0InfoVect1LinkObjId="g_3688600_0" Pin0InfoVect2LinkObjId="g_3467060_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120392_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4129,-975 4129,-992 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3494dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4496,-1022 4496,-993 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="powerLine" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_34dc560@0" ObjectIDND1="0@x" ObjectIDND2="37809@1" ObjectIDZND0="22464@x" ObjectIDZND1="22422@x" Pin0InfoVect0LinkObjId="SW-120450_0" Pin0InfoVect1LinkObjId="SW-120396_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_34dc560_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_1eba5b0_1" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4496,-1022 4496,-993 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3494ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4496,-976 4496,-993 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="22422@1" ObjectIDZND0="22464@x" ObjectIDZND1="g_34dc560@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-120450_0" Pin0InfoVect1LinkObjId="g_34dc560_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120396_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4496,-976 4496,-993 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3495250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4067,-977 4067,-1027 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_3467060@0" ObjectIDZND0="g_3688600@0" ObjectIDZND1="22462@x" ObjectIDZND2="22418@x" Pin0InfoVect0LinkObjId="g_3688600_0" Pin0InfoVect1LinkObjId="SW-120448_0" Pin0InfoVect2LinkObjId="SW-120392_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3467060_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4067,-977 4067,-1027 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2246fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4848,-1094 4848,-1015 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="37808@1" ObjectIDZND0="0@x" ObjectIDZND1="g_1c2cee0@0" ObjectIDZND2="22463@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_1c2cee0_0" Pin0InfoVect2LinkObjId="SW-120449_0" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4848,-1094 4848,-1015 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22471f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4848,-1015 4848,-992 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="g_1c2cee0@0" ObjectIDND2="37808@1" ObjectIDZND0="22463@x" ObjectIDZND1="22420@x" Pin0InfoVect0LinkObjId="SW-120449_0" Pin0InfoVect1LinkObjId="SW-120394_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_1c2cee0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4848,-1015 4848,-992 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2247450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4848,-975 4848,-992 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="22420@1" ObjectIDZND0="22463@x" ObjectIDZND1="0@x" ObjectIDZND2="g_1c2cee0@0" Pin0InfoVect0LinkObjId="SW-120449_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_1c2cee0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120394_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4848,-975 4848,-992 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34907c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4434,-1022 4495,-1022 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="powerLine" ObjectIDND0="g_34dc560@0" ObjectIDND1="0@x" ObjectIDZND0="22464@x" ObjectIDZND1="22422@x" ObjectIDZND2="37809@1" Pin0InfoVect0LinkObjId="SW-120450_0" Pin0InfoVect1LinkObjId="SW-120396_0" Pin0InfoVect2LinkObjId="g_1eba5b0_1" Pin0Num="2" Pin1InfoVect0LinkObjId="g_34dc560_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4434,-1022 4495,-1022 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1eba5b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4495,-1022 4496,-1022 4496,-1100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="powerLine" ObjectIDND0="22464@x" ObjectIDND1="22422@x" ObjectIDND2="g_34dc560@0" ObjectIDZND0="37809@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-120450_0" Pin1InfoVect1LinkObjId="SW-120396_0" Pin1InfoVect2LinkObjId="g_34dc560_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4495,-1022 4496,-1022 4496,-1100 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-119708" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3551.500000 -1030.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22305" ObjectName="DYN-WD_BY"/>
     <cge:Meas_Ref ObjectId="119708"/>
    </metadata>
   </g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1.631579 -0.000000 0.000000 -1.440678 -1158.421053 298.593220)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c6bd30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3807.000000 831.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_399a210" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3815.000000 846.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39bf1a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3815.000000 861.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39bf420" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3815.000000 875.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2428d10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4194.000000 750.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23e6920" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4219.000000 735.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23e7160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4224.000000 719.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f33920" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4205.000000 765.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d08810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4724.000000 751.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d08ac0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4749.000000 736.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d08d00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4754.000000 720.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34d1630" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4735.000000 766.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -0.897059 -59.000000 -50.647059)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ee5fc0" transform="matrix(1.000000 -0.000000 0.000000 -1.475410 4254.000000 468.868852) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ee6260" transform="matrix(1.000000 -0.000000 0.000000 -1.475410 4279.000000 446.737705) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22b3bc0" transform="matrix(1.000000 -0.000000 0.000000 -1.475410 4265.000000 491.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -0.897059 671.000000 -50.647059)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22b3ef0" transform="matrix(1.000000 -0.000000 0.000000 -1.475410 4254.000000 468.868852) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22b4190" transform="matrix(1.000000 -0.000000 0.000000 -1.475410 4279.000000 446.737705) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22b43d0" transform="matrix(1.000000 -0.000000 0.000000 -1.475410 4265.000000 491.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="WD_BY" endPointId="0" endStationName="PAS_T1" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_JinYiChaTby" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4129,-1099 4129,-1126 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="34565" ObjectName="AC-35kV.LN_JinYiChaTby"/>
    <cge:TPSR_Ref TObjectID="34565_SS-172"/></metadata>
   <polyline fill="none" opacity="0" points="4129,-1099 4129,-1126 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_WD" endPointId="0" endStationName="WD_BY" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_WuYiYongSheng" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4496,-1100 4496,-1120 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="37809" ObjectName="AC-35kV.LN_WuYiYongSheng"/>
    <cge:TPSR_Ref TObjectID="37809_SS-172"/></metadata>
   <polyline fill="none" opacity="0" points="4496,-1100 4496,-1120 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_WD" endPointId="0" endStationName="WD_BY" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_WuYi" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4848,-1095 4848,-1117 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="37808" ObjectName="AC-35kV.LN_WuYi"/>
    <cge:TPSR_Ref TObjectID="37808_SS-172"/></metadata>
   <polyline fill="none" opacity="0" points="4848,-1095 4848,-1117 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="22417" cx="3696" cy="-373" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22417" cx="4129" cy="-373" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22417" cx="4848" cy="-373" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22417" cx="3889" cy="-373" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22417" cx="4080" cy="-373" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22417" cx="4272" cy="-373" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22417" cx="4464" cy="-373" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22417" cx="4657" cy="-373" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22417" cx="4848" cy="-373" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22417" cx="5041" cy="-373" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22417" cx="5041" cy="-373" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22417" cx="5232" cy="-373" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22417" cx="5424" cy="-373" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22416" cx="4129" cy="-823" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22416" cx="4426" cy="-823" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22416" cx="4642" cy="-823" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22416" cx="4496" cy="-823" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22416" cx="4848" cy="-823" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22416" cx="4848" cy="-823" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22416" cx="4129" cy="-823" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-120468">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4120.000000 -713.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22476" ObjectName="SW-WD_BY.WD_BY_301BK"/>
     <cge:Meas_Ref ObjectId="120468"/>
    <cge:TPSR_Ref TObjectID="22476"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120472">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4120.000000 -443.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22475" ObjectName="SW-WD_BY.WD_BY_001BK"/>
     <cge:Meas_Ref ObjectId="120472"/>
    <cge:TPSR_Ref TObjectID="22475"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120476">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4839.000000 -714.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22478" ObjectName="SW-WD_BY.WD_BY_302BK"/>
     <cge:Meas_Ref ObjectId="120476"/>
    <cge:TPSR_Ref TObjectID="22478"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120480">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4839.000000 -443.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22477" ObjectName="SW-WD_BY.WD_BY_002BK"/>
     <cge:Meas_Ref ObjectId="120480"/>
    <cge:TPSR_Ref TObjectID="22477"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120427">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4487.000000 -886.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22451" ObjectName="SW-WD_BY.WD_BY_333BK"/>
     <cge:Meas_Ref ObjectId="120427"/>
    <cge:TPSR_Ref TObjectID="22451"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120425">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4839.000000 -885.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22450" ObjectName="SW-WD_BY.WD_BY_332BK"/>
     <cge:Meas_Ref ObjectId="120425"/>
    <cge:TPSR_Ref TObjectID="22450"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120423">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4120.000000 -885.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22449" ObjectName="SW-WD_BY.WD_BY_331BK"/>
     <cge:Meas_Ref ObjectId="120423"/>
    <cge:TPSR_Ref TObjectID="22449"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120429">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3687.000000 -266.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22452" ObjectName="SW-WD_BY.WD_BY_031BK"/>
     <cge:Meas_Ref ObjectId="120429"/>
    <cge:TPSR_Ref TObjectID="22452"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120431">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3880.000000 -266.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22453" ObjectName="SW-WD_BY.WD_BY_032BK"/>
     <cge:Meas_Ref ObjectId="120431"/>
    <cge:TPSR_Ref TObjectID="22453"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120433">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4071.000000 -268.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22454" ObjectName="SW-WD_BY.WD_BY_033BK"/>
     <cge:Meas_Ref ObjectId="120433"/>
    <cge:TPSR_Ref TObjectID="22454"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120435">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4263.000000 -267.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22455" ObjectName="SW-WD_BY.WD_BY_034BK"/>
     <cge:Meas_Ref ObjectId="120435"/>
    <cge:TPSR_Ref TObjectID="22455"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120437">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4455.000000 -266.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22456" ObjectName="SW-WD_BY.WD_BY_035BK"/>
     <cge:Meas_Ref ObjectId="120437"/>
    <cge:TPSR_Ref TObjectID="22456"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120445">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4648.000000 -264.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22460" ObjectName="SW-WD_BY.WD_BY_036BK"/>
     <cge:Meas_Ref ObjectId="120445"/>
    <cge:TPSR_Ref TObjectID="22460"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120441">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4839.000000 -265.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22458" ObjectName="SW-WD_BY.WD_BY_037BK"/>
     <cge:Meas_Ref ObjectId="120441"/>
    <cge:TPSR_Ref TObjectID="22458"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120443">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5030.600000 -268.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22459" ObjectName="SW-WD_BY.WD_BY_038BK"/>
     <cge:Meas_Ref ObjectId="120443"/>
    <cge:TPSR_Ref TObjectID="22459"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120439">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5222.600000 -270.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22457" ObjectName="SW-WD_BY.WD_BY_039BK"/>
     <cge:Meas_Ref ObjectId="120439"/>
    <cge:TPSR_Ref TObjectID="22457"/></metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_23081b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3237.000000 -969.000000) translate(0,16)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_23081b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3237.000000 -969.000000) translate(0,36)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_23081b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3237.000000 -969.000000) translate(0,56)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_23081b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3237.000000 -969.000000) translate(0,76)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_23081b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3237.000000 -969.000000) translate(0,96)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_23081b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3237.000000 -969.000000) translate(0,116)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_23081b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3237.000000 -969.000000) translate(0,136)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_23081b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3237.000000 -969.000000) translate(0,156)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_23081b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3237.000000 -969.000000) translate(0,176)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_23081b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3237.000000 -969.000000) translate(0,196)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_23081b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3237.000000 -969.000000) translate(0,216)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_23081b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3237.000000 -969.000000) translate(0,236)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_23081b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3237.000000 -969.000000) translate(0,256)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_261ce30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3220.000000 -585.000000) translate(0,16)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_261ce30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3220.000000 -585.000000) translate(0,36)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_261ce30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3220.000000 -585.000000) translate(0,56)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_261ce30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3220.000000 -585.000000) translate(0,76)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_261ce30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3220.000000 -585.000000) translate(0,96)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_261ce30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3220.000000 -585.000000) translate(0,116)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_261ce30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3220.000000 -585.000000) translate(0,136)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_261ce30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3220.000000 -585.000000) translate(0,156)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_261ce30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3220.000000 -585.000000) translate(0,176)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_261ce30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3220.000000 -585.000000) translate(0,196)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_261ce30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3220.000000 -585.000000) translate(0,216)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_261ce30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3220.000000 -585.000000) translate(0,236)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_261ce30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3220.000000 -585.000000) translate(0,256)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_261ce30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3220.000000 -585.000000) translate(0,276)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_261ce30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3220.000000 -585.000000) translate(0,296)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_261ce30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3220.000000 -585.000000) translate(0,316)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_261ce30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3220.000000 -585.000000) translate(0,336)">联系方式：8723108</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimSun" font-size="40" graphid="g_1b640c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3354.000000 -1147.500000) translate(0,32)">白邑变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1b861e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3921.000000 -759.000000) translate(0,16)">1号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1b861e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3921.000000 -759.000000) translate(0,36)">SFZ9-10000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1b861e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3921.000000 -759.000000) translate(0,56)">35±3×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1b861e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3921.000000 -759.000000) translate(0,76)">Y/△-11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1b861e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3921.000000 -759.000000) translate(0,96)">Ud=7.54%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2720ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5040.000000 -773.000000) translate(0,16)">2号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2720ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5040.000000 -773.000000) translate(0,36)">SFZ9-10000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2720ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5040.000000 -773.000000) translate(0,56)">35±3×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2720ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5040.000000 -773.000000) translate(0,76)">Y/△-11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2720ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5040.000000 -773.000000) translate(0,96)">Ud=7.38%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="20" graphid="g_3528180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4372.000000 -627.000000) translate(0,16)">35kV母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="20" graphid="g_1f01780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4937.000000 -667.000000) translate(0,16)">白邑变2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_365f120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3876.000000 -526.500000) translate(0,16)">1号主变档位：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1f348a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4979.500000 -530.500000) translate(0,16)">2号主变档位：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1cbab10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4582.000000 -570.000000) translate(0,16)">35kV 2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1cbab10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4582.000000 -570.000000) translate(0,36)">     50kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1efc920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4472.000000 -1138.000000) translate(0,16)">武</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1efc920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4472.000000 -1138.000000) translate(0,36)">邑</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1efc920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4472.000000 -1138.000000) translate(0,56)">永</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1efc920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4472.000000 -1138.000000) translate(0,76)">盛</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1efc920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4472.000000 -1138.000000) translate(0,96)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_36b7c10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4817.000000 -1109.000000) translate(0,16)">武</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_36b7c10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4817.000000 -1109.000000) translate(0,36)">邑</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_36b7c10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4817.000000 -1109.000000) translate(0,56)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3d9b620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4105.000000 -1115.000000) translate(0,16)">近</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3d9b620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4105.000000 -1115.000000) translate(0,36)">邑</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3d9b620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4105.000000 -1115.000000) translate(0,56)">插</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3d9b620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4105.000000 -1115.000000) translate(0,76)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1ca1a90" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 3707.142857 -113.800000) translate(0,16)">城</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1ca1a90" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 3707.142857 -113.800000) translate(0,36)">区</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1ca1a90" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 3707.142857 -113.800000) translate(0,56)">Ⅲ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1ca1a90" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 3707.142857 -113.800000) translate(0,76)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1ca1a90" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 3707.142857 -113.800000) translate(0,96)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_34d0c90" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 3900.142857 -105.300000) translate(0,16)">钛</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_34d0c90" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 3900.142857 -105.300000) translate(0,36)">白</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_34d0c90" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 3900.142857 -105.300000) translate(0,56)">粉</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_34d0c90" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 3900.142857 -105.300000) translate(0,76)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_23d2b10" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4091.142857 -96.800000) translate(0,16)">永</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_23d2b10" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4091.142857 -96.800000) translate(0,36)">丰</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_23d2b10" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4091.142857 -96.800000) translate(0,56)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_39ebda0" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4283.142857 -96.800000) translate(0,16)">盛</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_39ebda0" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4283.142857 -96.800000) translate(0,36)">源</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_39ebda0" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4283.142857 -96.800000) translate(0,56)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3d353d0" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4475.142857 -113.800000) translate(0,16)">铺</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3d353d0" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4475.142857 -113.800000) translate(0,36)">西</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3d353d0" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4475.142857 -113.800000) translate(0,56)">矿</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3d353d0" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4475.142857 -113.800000) translate(0,76)">山</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3d353d0" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4475.142857 -113.800000) translate(0,96)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3642f20" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4668.142857 -105.300000) translate(0,16)">二</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3642f20" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4668.142857 -105.300000) translate(0,36)">龙</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3642f20" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4668.142857 -105.300000) translate(0,56)">山</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3642f20" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4668.142857 -105.300000) translate(0,76)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_399c2c0" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4860.142857 -96.800000) translate(0,16)">新</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_399c2c0" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4860.142857 -96.800000) translate(0,36)">立</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_399c2c0" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4860.142857 -96.800000) translate(0,56)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_39b9d50" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 5053.142857 -106.300000) translate(0,16)">水</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_39b9d50" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 5053.142857 -106.300000) translate(0,36)">泥</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_39b9d50" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 5053.142857 -106.300000) translate(0,56)">厂</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_39b9d50" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 5053.142857 -106.300000) translate(0,76)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1cae290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5384.000000 -157.000000) translate(0,16)">10kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1cae290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5384.000000 -157.000000) translate(0,36)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1eb5f50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5464.000000 -9.000000) translate(0,16)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_39c1ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5367.000000 -420.000000) translate(0,24)">10kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_240f470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5010.000000 -862.000000) translate(0,24)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1c14b40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3705.000000 -149.000000) translate(0,16)">0316</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_35642f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3705.000000 -348.000000) translate(0,16)">0311</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3564740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3705.000000 -254.000000) translate(0,16)">0312</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3564980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3710.000000 -297.000000) translate(0,16)">031</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_229fe50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4279.500000 -146.000000) translate(0,16)">0346</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_22a0090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4284.000000 -296.000000) translate(0,16)">034</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_22a02d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4280.000000 -345.000000) translate(0,16)">0341</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_22a0510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4280.000000 -251.000000) translate(0,16)">0342</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_22a0d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4666.000000 -346.000000) translate(0,16)">0361</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_22a0f30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4666.000000 -252.000000) translate(0,16)">0362</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_22a1170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4665.500000 -147.000000) translate(0,16)">0366</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_22a13b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4670.000000 -293.000000) translate(0,16)">036</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1dca7c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5433.000000 -346.000000) translate(0,16)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1dca9c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4473.000000 -350.000000) translate(0,16)">0351</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1dcac00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4473.000000 -252.000000) translate(0,16)">0352</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1dcae40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4472.500000 -147.000000) translate(0,16)">0356</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2423060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4477.000000 -295.000000) translate(0,16)">035</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_24232a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5048.500000 -149.000000) translate(0,16)">0386</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_24234e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5053.000000 -297.000000) translate(0,16)">038</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2423720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5049.000000 -348.000000) translate(0,16)">0381</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_34d8fa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5049.000000 -254.000000) translate(0,16)">0382</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_34d91e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3897.500000 -147.000000) translate(0,16)">0326</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_34d9420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3902.000000 -295.000000) translate(0,16)">032</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_34d9660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3898.000000 -346.000000) translate(0,16)">0321</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_39e2690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3898.000000 -252.000000) translate(0,16)">0322</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_39e28d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4854.500000 -146.000000) translate(0,16)">0376</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_39e2b10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4859.000000 -294.000000) translate(0,16)">037</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_39e2d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4855.000000 -345.000000) translate(0,16)">0371</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_36bc360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4855.000000 -251.000000) translate(0,16)">0372</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_36bc5a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4089.000000 -348.000000) translate(0,16)">0331</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_36bc7e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4089.000000 -254.000000) translate(0,16)">0332</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_36bca20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4088.500000 -149.000000) translate(0,16)">0336</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_348ec90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4093.000000 -297.000000) translate(0,16)">033</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_348eed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5241.000000 -350.000000) translate(0,16)">0391</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_348f110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5245.000000 -299.000000) translate(0,16)">039</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_348f350" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4149.500000 -473.000000) translate(0,16)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_34da110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4143.000000 -529.000000) translate(0,16)">0016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_34da350" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4144.500000 -416.000000) translate(0,16)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_34da590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4144.000000 -791.000000) translate(0,16)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_34da7d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4150.000000 -741.000000) translate(0,16)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2429f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4868.500000 -473.000000) translate(0,16)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_242a1a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4863.000000 -529.000000) translate(0,16)">0026</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_242a3e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4863.500000 -418.000000) translate(0,16)">0021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2281790" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4870.000000 -743.000000) translate(0,16)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_22ac260" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4863.000000 -791.000000) translate(0,16)">3021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_22ac4a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4149.000000 -1027.000000) translate(0,16)">33167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_22ac6e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4146.500000 -917.000000) translate(0,16)">331</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_22ac920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4141.500000 -864.000000) translate(0,16)">3311</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_22acb60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4141.000000 -961.000000) translate(0,16)">3316</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2274c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4435.000000 -784.000000) translate(0,16)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2274ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4869.000000 -1023.000000) translate(0,16)">33267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2275110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4869.500000 -914.000000) translate(0,16)">332</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2275350" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4863.500000 -865.000000) translate(0,16)">3321</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2275590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4862.000000 -966.000000) translate(0,16)">3326</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_227f390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4511.000000 -1025.000000) translate(0,16)">33367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_227f5a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4516.000000 -918.000000) translate(0,16)">333</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_227f7e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4513.000000 -863.000000) translate(0,16)">3331</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_227fa20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4514.500000 -965.000000) translate(0,16)">3336</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_227fc60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4215.000000 -668.000000) translate(0,16)">白邑变1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1c23c10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3874.000000 -592.500000) translate(0,16)">1号主变温度：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1ee5c90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4978.000000 -592.500000) translate(0,16)">2号主变温度：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2276500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5175.500000 -147.000000) translate(0,16)">03961</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2276a40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5300.500000 -147.000000) translate(0,16)">03962</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1edfc50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5240.000000 -256.000000) translate(0,16)">0396</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ee01e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3230.000000 -679.000000) translate(0,12)">公用间隔</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_1cb6660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3661.000000 -1120.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_1c0a290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3661.000000 -1155.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1ebb040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3180.000000 -215.500000) translate(0,16)">武定巡维中心：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1ebc8c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3317.000000 -235.500000) translate(0,16)">18787878990</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1ebc8c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3317.000000 -235.500000) translate(0,36)">18787842893</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1ebc8c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3317.000000 -235.500000) translate(0,56)">13987880311</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1ebcc50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5172.000000 -45.000000) translate(0,16)">1号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_1c0b760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3561.500000 -1138.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1c0bf70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5512.000000 -266.000000) translate(0,16)">09011</text>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1f03c40" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4573.000000 -987.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_364ac40" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4925.000000 -986.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ca59f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4206.000000 -986.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="WD_BY"/>
</svg>