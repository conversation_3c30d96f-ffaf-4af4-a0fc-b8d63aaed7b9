<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-299" aopId="3940870" id="thSvg" product="E8000V2" version="1.0" viewBox="16 -1221 2147 1770">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="5" y2="36"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="14" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="5" x2="8" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="3" x2="10" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="12" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
   </symbol>
   <symbol id="lightningRod:shape157">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,36 0,46 10,46 5,36 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,24 0,14 10,14 5,24 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="59" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape214">
    <polyline points="14,13 12,13 10,14 9,14 7,15 6,16 4,17 3,19 2,21 2,22 1,24 1,26 1,28 2,30 2,31 3,33 4,34 6,36 7,37 9,38 10,38 12,39 14,39 16,39 18,38 19,38 21,37 22,36 24,34 25,33 26,31 26,30 27,28 27,26 " stroke-width="0.3888"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.664615" x1="14" x2="14" y1="13" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.88" x1="14" x2="14" y1="47" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.96" x1="14" x2="27" y1="26" y2="26"/>
   </symbol>
   <symbol id="lightningRod:shape98">
    <polyline DF8003:Layer="PUBLIC" points="0,17 4,32 8,17 0,17 " stroke-width="0.4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="4" y1="5" y2="17"/>
    <polyline DF8003:Layer="PUBLIC" points="0,51 4,36 8,51 0,51 " stroke-width="0.4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="4" y1="63" y2="51"/>
   </symbol>
   <symbol id="lightningRod:shape146">
    <rect height="19" stroke-width="1" width="35" x="0" y="0"/>
    <polyline points="17,19 17,30 " stroke-width="1"/>
    <text font-family="SimSun" font-size="15" graphid="g_1e349d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 17.000000) translate(0,12)">SVG</text>
   </symbol>
   <symbol id="load:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="transformer2:shape48_0">
    <ellipse cx="25" cy="29" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="17" x2="25" y1="32" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="33" y1="24" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="25" y1="16" y2="24"/>
   </symbol>
   <symbol id="transformer2:shape48_1">
    <circle cx="25" cy="61" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="16" x2="33" y1="59" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="33" y1="75" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="16" y1="75" y2="59"/>
   </symbol>
   <symbol id="transformer2:shape13_0">
    <ellipse cx="38" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="66" x2="71" y1="83" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="71" x2="69" y1="83" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="71" x2="71" y1="80" y2="80"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.911765" x1="1" x2="69" y1="44" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="30" x2="38" y1="74" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="46" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="38" y1="58" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape13_1">
    <circle cx="38" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="29" x2="46" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="46" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="29" y1="34" y2="18"/>
   </symbol>
   <symbol id="voltageTransformer:shape147">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="29" x2="34" y1="46" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="29" x2="33" y1="35" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45294" x1="33" x2="40" y1="40" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="29" x2="33" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45294" x1="33" x2="40" y1="16" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="29" x2="34" y1="22" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="9" x2="14" y1="46" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="9" x2="13" y1="34" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45294" x1="13" x2="20" y1="40" y2="39"/>
    <ellipse cx="34" cy="40" fillStyle="0" rx="12" ry="15" stroke-width="0.862369"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="25" y1="27" y2="27"/>
    <ellipse cx="33" cy="15" fillStyle="0" rx="12" ry="14.5" stroke-width="0.862369"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="25" y1="3" y2="3"/>
    <ellipse cx="50" cy="27" fillStyle="0" rx="12" ry="15" stroke-width="0.862369"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="9" x2="13" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45294" x1="13" x2="20" y1="16" y2="15"/>
    <ellipse cx="14" cy="39" fillStyle="0" rx="12" ry="15" stroke-width="0.862369"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="9" x2="14" y1="22" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="27" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.77579" x1="56" x2="56" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.39762" x1="49" x2="56" y1="30" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.36031" x1="49" x2="56" y1="25" y2="22"/>
    <ellipse cx="13" cy="16" fillStyle="0" rx="12" ry="14.5" stroke-width="0.862369"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="3" y2="3"/>
   </symbol>
   <symbol id="voltageTransformer:shape126">
    <circle cx="32" cy="16" fillStyle="0" r="8" stroke-width="0.570276"/>
    <circle cx="20" cy="23" fillStyle="0" r="8" stroke-width="0.570276"/>
    <circle cx="20" cy="9" fillStyle="0" r="8" stroke-width="0.570276"/>
    <circle cx="8" cy="16" fillStyle="0" r="7.5" stroke-width="0.536731"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="20" x2="20" y1="23" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="20" x2="23" y1="23" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="17" x2="20" y1="20" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="8" x2="8" y1="17" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="5" x2="8" y1="14" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="8" x2="11" y1="17" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="20" x2="20" y1="9" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="17" x2="20" y1="6" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="20" x2="23" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="35" y1="19" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="19" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="31" y1="15" y2="13"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1f48b50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1f49cb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1f4a6a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1f4b340" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1f4c570" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1f4d210" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1f4ddb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1f4e6c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_156fec0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_156fec0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1f514c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1f514c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1f52f30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1f52f30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_1f53c30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1f55830" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1f56420" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1f571e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1f57b20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1f591e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1f59dc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1f5a680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1f5ae40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1f5bf20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1f5c8a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1f5d390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1f5dd50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1f5f1b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1f5fd40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1f60d70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1f619b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1f701c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1f684f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1f69600" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1f63db0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1780" width="2157" x="11" y="-1226"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,0)" stroke-width="1" x1="2161" x2="2161" y1="-724" y2="-280"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,0)" stroke-width="1" x1="841" x2="1489" y1="547" y2="547"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1493" x2="1493" y1="73" y2="109"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1744" x2="1744" y1="69" y2="105"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-270750">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 782.000000 -221.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43653" ObjectName="SW-CX_DGZJ.CX_DGZJ_351BK"/>
     <cge:Meas_Ref ObjectId="270750"/>
    <cge:TPSR_Ref TObjectID="43653"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-270755">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1020.000000 -220.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43657" ObjectName="SW-CX_DGZJ.CX_DGZJ_352BK"/>
     <cge:Meas_Ref ObjectId="270755"/>
    <cge:TPSR_Ref TObjectID="43657"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-270770">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1260.000000 -220.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43669" ObjectName="SW-CX_DGZJ.CX_DGZJ_353BK"/>
     <cge:Meas_Ref ObjectId="270770"/>
    <cge:TPSR_Ref TObjectID="43669"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-270760">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1483.000000 -218.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43661" ObjectName="SW-CX_DGZJ.CX_DGZJ_354BK"/>
     <cge:Meas_Ref ObjectId="270760"/>
    <cge:TPSR_Ref TObjectID="43661"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-270765">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1733.000000 -222.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43665" ObjectName="SW-CX_DGZJ.CX_DGZJ_355BK"/>
     <cge:Meas_Ref ObjectId="270765"/>
    <cge:TPSR_Ref TObjectID="43665"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-270740">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1144.000000 -363.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43647" ObjectName="SW-CX_DGZJ.CX_DGZJ_301BK"/>
     <cge:Meas_Ref ObjectId="270740"/>
    <cge:TPSR_Ref TObjectID="43647"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-270730">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1144.000000 -858.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43637" ObjectName="SW-CX_DGZJ.CX_DGZJ_101BK"/>
     <cge:Meas_Ref ObjectId="270730"/>
    <cge:TPSR_Ref TObjectID="43637"/></metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2c32190">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 559.000000 -89.000000)" xlink:href="#voltageTransformer:shape147"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1dff3e0">
    <use class="BV-110KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1366.000000 -1011.000000)" xlink:href="#voltageTransformer:shape126"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_YZ" endPointId="0" endStationName="CX_DGZJ" flowDrawDirect="1" flowShape="0" id="AC-110kV.yaodong_line" runFlow="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1153,-1136 1153,-1179 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="43825" ObjectName="AC-110kV.yaodong_line"/>
    <cge:TPSR_Ref TObjectID="43825_SS-299"/></metadata>
   <polyline fill="none" opacity="0" points="1153,-1136 1153,-1179 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1467.000000 77.000000)" xlink:href="#transformer2:shape48_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1467.000000 77.000000)" xlink:href="#transformer2:shape48_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_DGZJ.CX_DGZJ_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="19908"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1115.000000 -640.000000)" xlink:href="#transformer2:shape13_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1115.000000 -640.000000)" xlink:href="#transformer2:shape13_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="43675" ObjectName="TF-CX_DGZJ.CX_DGZJ_1T"/>
    <cge:TPSR_Ref TObjectID="43675"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1718.000000 73.000000)" xlink:href="#transformer2:shape48_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1718.000000 73.000000)" xlink:href="#transformer2:shape48_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2c31980">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 583.000000 -158.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c34be0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 535.000000 -93.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f77010">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 860.000000 -125.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f84d40">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1098.000000 -124.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f92c40">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1338.000000 -124.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f9fcb0">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1561.000000 -122.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fad990">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1811.000000 -122.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fb8ff0">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 662.000000 -181.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fbb270">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1264.000000 -38.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fc1a00">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1283.000000 57.000000)" xlink:href="#lightningRod:shape214"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fc28f0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1487.000000 -37.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fc4970">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1737.000000 -36.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fcfcf0">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1187.000000 -482.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1defaa0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 968.000000 -625.000000)" xlink:href="#lightningRod:shape98"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1df06c0">
    <use class="BV-110KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1015.000000 -684.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e01170">
    <use class="BV-110KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1189.000000 -1097.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e34470">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1252.000000 152.000000)" xlink:href="#lightningRod:shape146"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 107.000000 -953.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-270701" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 500.000000 -402.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="270701" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43635"/>
     <cge:Term_Ref ObjectID="19827"/>
    <cge:TPSR_Ref TObjectID="43635"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-270702" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 500.000000 -402.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="270702" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43635"/>
     <cge:Term_Ref ObjectID="19827"/>
    <cge:TPSR_Ref TObjectID="43635"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-270703" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 500.000000 -402.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="270703" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43635"/>
     <cge:Term_Ref ObjectID="19827"/>
    <cge:TPSR_Ref TObjectID="43635"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-270704" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 500.000000 -402.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="270704" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43635"/>
     <cge:Term_Ref ObjectID="19827"/>
    <cge:TPSR_Ref TObjectID="43635"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-270705" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 500.000000 -402.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="270705" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43635"/>
     <cge:Term_Ref ObjectID="19827"/>
    <cge:TPSR_Ref TObjectID="43635"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-270708" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 500.000000 -402.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="270708" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43635"/>
     <cge:Term_Ref ObjectID="19827"/>
    <cge:TPSR_Ref TObjectID="43635"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-270683" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1332.000000 -896.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="270683" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43637"/>
     <cge:Term_Ref ObjectID="19830"/>
    <cge:TPSR_Ref TObjectID="43637"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-270684" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1332.000000 -896.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="270684" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43637"/>
     <cge:Term_Ref ObjectID="19830"/>
    <cge:TPSR_Ref TObjectID="43637"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-270675" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1332.000000 -896.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="270675" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43637"/>
     <cge:Term_Ref ObjectID="19830"/>
    <cge:TPSR_Ref TObjectID="43637"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-270710" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 815.000000 107.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="270710" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43653"/>
     <cge:Term_Ref ObjectID="19862"/>
    <cge:TPSR_Ref TObjectID="43653"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-270711" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 815.000000 107.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="270711" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43653"/>
     <cge:Term_Ref ObjectID="19862"/>
    <cge:TPSR_Ref TObjectID="43653"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-270709" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 815.000000 107.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="270709" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43653"/>
     <cge:Term_Ref ObjectID="19862"/>
    <cge:TPSR_Ref TObjectID="43653"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-270714" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1048.000000 109.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="270714" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43657"/>
     <cge:Term_Ref ObjectID="19870"/>
    <cge:TPSR_Ref TObjectID="43657"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-270715" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1048.000000 109.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="270715" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43657"/>
     <cge:Term_Ref ObjectID="19870"/>
    <cge:TPSR_Ref TObjectID="43657"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-270713" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1048.000000 109.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="270713" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43657"/>
     <cge:Term_Ref ObjectID="19870"/>
    <cge:TPSR_Ref TObjectID="43657"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-270718" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1506.000000 203.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="270718" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43661"/>
     <cge:Term_Ref ObjectID="19878"/>
    <cge:TPSR_Ref TObjectID="43661"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-270719" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1506.000000 203.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="270719" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43661"/>
     <cge:Term_Ref ObjectID="19878"/>
    <cge:TPSR_Ref TObjectID="43661"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-270717" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1506.000000 203.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="270717" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43661"/>
     <cge:Term_Ref ObjectID="19878"/>
    <cge:TPSR_Ref TObjectID="43661"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-270722" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1768.000000 202.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="270722" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43665"/>
     <cge:Term_Ref ObjectID="19886"/>
    <cge:TPSR_Ref TObjectID="43665"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-270723" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1768.000000 202.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="270723" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43665"/>
     <cge:Term_Ref ObjectID="19886"/>
    <cge:TPSR_Ref TObjectID="43665"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-270721" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1768.000000 202.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="270721" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43665"/>
     <cge:Term_Ref ObjectID="19886"/>
    <cge:TPSR_Ref TObjectID="43665"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-270700" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1320.000000 -698.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="270700" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43675"/>
     <cge:Term_Ref ObjectID="19906"/>
    <cge:TPSR_Ref TObjectID="43675"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="1" id="ME-270697" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1320.000000 -698.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="270697" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43675"/>
     <cge:Term_Ref ObjectID="19906"/>
    <cge:TPSR_Ref TObjectID="43675"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-270694" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1322.000000 -406.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="270694" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43647"/>
     <cge:Term_Ref ObjectID="19850"/>
    <cge:TPSR_Ref TObjectID="43647"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-270695" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1322.000000 -406.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="270695" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43647"/>
     <cge:Term_Ref ObjectID="19850"/>
    <cge:TPSR_Ref TObjectID="43647"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-270686" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1322.000000 -406.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="270686" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43647"/>
     <cge:Term_Ref ObjectID="19850"/>
    <cge:TPSR_Ref TObjectID="43647"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-270726" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1284.000000 196.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="270726" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43669"/>
     <cge:Term_Ref ObjectID="19894"/>
    <cge:TPSR_Ref TObjectID="43669"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-270727" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1284.000000 196.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="270727" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43669"/>
     <cge:Term_Ref ObjectID="19894"/>
    <cge:TPSR_Ref TObjectID="43669"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-270725" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1284.000000 196.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="270725" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43669"/>
     <cge:Term_Ref ObjectID="19894"/>
    <cge:TPSR_Ref TObjectID="43669"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="119" y="-1012"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="119" y="-1012"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="70" y="-1029"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="70" y="-1029"/></g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_客户变110.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="119" y="-1012"/></g>
   <g href="cx_索引_接线图_客户变110.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="70" y="-1029"/></g>
  </g><g id="MultiLine_Layer">
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="15" arrowPos="0" arrowType="0" fill="none" points="785,-41 796,-41 791,-32 785,-41 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="15" arrowPos="0" arrowType="0" fill="none" points="1023,-46 1034,-46 1029,-37 1023,-46 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="15" arrowPos="0" arrowType="0" fill="none" points="1486,72 1497,72 1492,81 1486,72 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="15" arrowPos="0" arrowType="0" fill="none" points="1487,87 1498,87 1493,96 1487,87 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="15" arrowPos="0" arrowType="0" fill="none" points="1737,68 1748,68 1743,77 1737,68 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="15" arrowPos="0" arrowType="0" fill="none" points="1738,83 1749,83 1744,92 1738,83 " stroke="rgb(60,120,255)" stroke-width="1"/>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_DGZJ.CX_DGZJ_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="514,-306 1778,-306 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="43635" ObjectName="BS-CX_DGZJ.CX_DGZJ_3IM"/>
    <cge:TPSR_Ref TObjectID="43635"/></metadata>
   <polyline fill="none" opacity="0" points="514,-306 1778,-306 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_DGZJ.CX_DGZJ_1XM">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1150,-836 1158,-836 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="49420" ObjectName="BS-CX_DGZJ.CX_DGZJ_1XM"/>
    <cge:TPSR_Ref TObjectID="49420"/></metadata>
   <polyline fill="none" opacity="0" points="1150,-836 1158,-836 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1f7ab10" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 721.000000 -71.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f88a10" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 959.000000 -70.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f966b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1199.000000 -70.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fa3720" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1422.000000 -68.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fb1400" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1672.000000 -68.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fc0f70" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 1363.000000 47.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fd3500" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1222.000000 -542.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1df88a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1222.000000 -762.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1dfe950" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 1085.000000 -967.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e21520" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1240.000000 -902.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e247b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1254.000000 -948.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e28120" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1238.000000 -845.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="43635" cx="791" cy="-306" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="43635" cx="1029" cy="-306" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="43635" cx="1269" cy="-306" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="43635" cx="1492" cy="-306" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="43635" cx="592" cy="-306" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49420" cx="1153" cy="-836" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49420" cx="1153" cy="-836" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="43635" cx="1153" cy="-306" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="43635" cx="1742" cy="-306" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c230b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c230b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c230b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c230b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c230b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c230b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c230b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c230b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c230b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c230b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c230b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c230b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c230b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c230b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c230b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c230b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c230b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c230b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,374)">联系方式：15716525387</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c26d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c26d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c26d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c26d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c26d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c26d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c26d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_2c2a240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 159.000000 -1001.500000) translate(0,16)">东锆变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2c30450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 642.000000 -335.000000) translate(0,16)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1fb67b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 501.000000 -9.000000) translate(0,15)">35kV母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1fb9a80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 725.000000 43.000000) translate(0,15)">35kV1号电炉变线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1fbad00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 955.000000 45.000000) translate(0,15)">35kV2号电炉变线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1fc59b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1429.000000 154.000000) translate(0,15)">35kV1号动力变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1fc6670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1688.000000 157.000000) translate(0,15)">35kV2号动力变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1fc68f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1176.000000 166.000000) translate(0,15)">35kV1号动态无功补偿装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e02ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1162.000000 -392.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e03500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1179.000000 -540.000000) translate(0,12)">30117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e038b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1160.000000 -605.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e03d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1060.000000 -668.000000) translate(0,12)">1010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e03fa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1172.000000 -758.000000) translate(0,12)">10117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e041e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1162.000000 -887.000000) translate(0,12)">101</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e04420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1089.000000 -963.000000) translate(0,12)">10167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e0b830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 548.000000 -264.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e0c220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 800.000000 -250.000000) translate(0,12)">351</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e0c4a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 672.000000 -117.000000) translate(0,12)">35167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e0c6e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1038.000000 -249.000000) translate(0,12)">352</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e0c9b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 906.000000 -127.000000) translate(0,12)">35267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e0cea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1278.000000 -249.000000) translate(0,12)">353</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e0d0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1149.000000 -126.000000) translate(0,12)">35360</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e0d320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1276.000000 -20.000000) translate(0,12)">3536</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e0d560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1292.000000 52.000000) translate(0,12)">35367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e0d7a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1501.000000 -247.000000) translate(0,12)">354</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e0dae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1375.000000 -122.000000) translate(0,12)">35467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e0df40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1751.000000 -251.000000) translate(0,12)">355</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e0e180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1613.000000 -124.000000) translate(0,12)">35567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1e15440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1218.000000 -734.000000) translate(0,16)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e165a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1137.000000 -1221.000000) translate(0,12)">至220kV腰站变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e2b3b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1189.000000 -1024.000000) translate(0,12)">1026</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e2b9e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1267.000000 -1007.000000) translate(0,12)">10260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e2bc20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1113.000000 -934.000000) translate(0,12)">1016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e2be60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1192.000000 -934.000000) translate(0,12)">10160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e2c0a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1112.000000 -805.000000) translate(0,12)">1011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e2c2e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1191.000000 -877.000000) translate(0,12)">10110</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_1e2c520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1137.000000 -1201.000000) translate(0,13)">110kV腰东线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1e2dc20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 903.000000 -589.000000) translate(0,16)">1号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1e2dc20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 903.000000 -589.000000) translate(0,36)">SZ18-63000/110GYW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1e2dc20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 903.000000 -589.000000) translate(0,56)">110±8×1.25%/35kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1e2dc20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 903.000000 -589.000000) translate(0,76)">YNd11</text>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c2afc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 421.000000 347.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c2c950" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 436.000000 331.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c2d5a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 429.000000 407.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c2d820" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 429.000000 392.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c2da60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 427.000000 363.000000) translate(0,12)">3U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c2dff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 429.000000 378.000000) translate(0,12)">Uc(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c2e600" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1264.000000 406.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c2f1c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1253.000000 391.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c2fa40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1278.000000 374.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e0edc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1273.000000 898.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e0f030" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1262.000000 883.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e0f270" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1287.000000 866.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e0fae0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 751.000000 -108.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e0fd40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 740.000000 -123.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e0ff80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 765.000000 -140.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e102b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 984.000000 -107.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e10510" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 973.000000 -122.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e10750" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 998.000000 -139.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e10a80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1227.000000 -195.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e10ce0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1216.000000 -210.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e10f20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1241.000000 -227.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e11250" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1449.000000 -206.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e114b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1438.000000 -221.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e116f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1463.000000 -238.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e11a20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1709.000000 -207.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e11c80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1698.000000 -222.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e11ec0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1723.000000 -239.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e326d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1217.000000 683.000000) translate(0,12)">油温（℃）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e339c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1217.000000 698.000000) translate(0,12)">档位（档）：</text>
   <metadata/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-CX_DGZJ.351LD">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 786.000000 13.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49413" ObjectName="EC-CX_DGZJ.351LD"/>
    <cge:TPSR_Ref TObjectID="49413"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_DGZJ.352LD">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1024.000000 15.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49414" ObjectName="EC-CX_DGZJ.352LD"/>
    <cge:TPSR_Ref TObjectID="49414"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_DGZJ.354LD">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1487.000000 -3.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49415" ObjectName="EC-CX_DGZJ.354LD"/>
    <cge:TPSR_Ref TObjectID="49415"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_DGZJ.355LD">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1737.000000 -9.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49416" ObjectName="EC-CX_DGZJ.355LD"/>
    <cge:TPSR_Ref TObjectID="49416"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_DGZJ.353LD">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1264.000000 34.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49444" ObjectName="EC-CX_DGZJ.353LD"/>
    <cge:TPSR_Ref TObjectID="49444"/></metadata>
   </g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-260588" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 317.500000 -917.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42707" ObjectName="DYN-CX_DGZJ"/>
     <cge:Meas_Ref ObjectId="260588"/>
    </metadata>
   </g>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-270751">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 781.000000 -264.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43654" ObjectName="SW-CX_DGZJ.CX_DGZJ_351XC"/>
     <cge:Meas_Ref ObjectId="270751"/>
    <cge:TPSR_Ref TObjectID="43654"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-270751">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 781.000000 -194.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43655" ObjectName="SW-CX_DGZJ.CX_DGZJ_351XC1"/>
     <cge:Meas_Ref ObjectId="270751"/>
    <cge:TPSR_Ref TObjectID="43655"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-270752">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 718.000000 -95.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43656" ObjectName="SW-CX_DGZJ.CX_DGZJ_35167SW"/>
     <cge:Meas_Ref ObjectId="270752"/>
    <cge:TPSR_Ref TObjectID="43656"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-270756">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1019.000000 -263.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43658" ObjectName="SW-CX_DGZJ.CX_DGZJ_352XC"/>
     <cge:Meas_Ref ObjectId="270756"/>
    <cge:TPSR_Ref TObjectID="43658"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-270756">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1019.000000 -193.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43659" ObjectName="SW-CX_DGZJ.CX_DGZJ_352XC1"/>
     <cge:Meas_Ref ObjectId="270756"/>
    <cge:TPSR_Ref TObjectID="43659"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-270757">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 956.000000 -94.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43660" ObjectName="SW-CX_DGZJ.CX_DGZJ_35267SW"/>
     <cge:Meas_Ref ObjectId="270757"/>
    <cge:TPSR_Ref TObjectID="43660"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-270771">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1259.000000 -263.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43670" ObjectName="SW-CX_DGZJ.CX_DGZJ_353XC"/>
     <cge:Meas_Ref ObjectId="270771"/>
    <cge:TPSR_Ref TObjectID="43670"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-270771">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1259.000000 -193.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43671" ObjectName="SW-CX_DGZJ.CX_DGZJ_353XC1"/>
     <cge:Meas_Ref ObjectId="270771"/>
    <cge:TPSR_Ref TObjectID="43671"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-270772">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1196.000000 -94.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43672" ObjectName="SW-CX_DGZJ.CX_DGZJ_35360SW"/>
     <cge:Meas_Ref ObjectId="270772"/>
    <cge:TPSR_Ref TObjectID="43672"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-270761">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1482.000000 -261.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43662" ObjectName="SW-CX_DGZJ.CX_DGZJ_354XC"/>
     <cge:Meas_Ref ObjectId="270761"/>
    <cge:TPSR_Ref TObjectID="43662"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-270761">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1482.000000 -191.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43663" ObjectName="SW-CX_DGZJ.CX_DGZJ_354XC1"/>
     <cge:Meas_Ref ObjectId="270761"/>
    <cge:TPSR_Ref TObjectID="43663"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-270762">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1419.000000 -92.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43664" ObjectName="SW-CX_DGZJ.CX_DGZJ_35467SW"/>
     <cge:Meas_Ref ObjectId="270762"/>
    <cge:TPSR_Ref TObjectID="43664"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-270766">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1732.000000 -264.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43666" ObjectName="SW-CX_DGZJ.CX_DGZJ_355XC"/>
     <cge:Meas_Ref ObjectId="270766"/>
    <cge:TPSR_Ref TObjectID="43666"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-270766">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1732.000000 -191.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43667" ObjectName="SW-CX_DGZJ.CX_DGZJ_355XC1"/>
     <cge:Meas_Ref ObjectId="270766"/>
    <cge:TPSR_Ref TObjectID="43667"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-270767">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1669.000000 -92.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43668" ObjectName="SW-CX_DGZJ.CX_DGZJ_35567SW"/>
     <cge:Meas_Ref ObjectId="270767"/>
    <cge:TPSR_Ref TObjectID="43668"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-270773">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1260.000000 12.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43673" ObjectName="SW-CX_DGZJ.CX_DGZJ_3536SW"/>
     <cge:Meas_Ref ObjectId="270773"/>
    <cge:TPSR_Ref TObjectID="43673"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-270774">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 1.000000 -1.000000 0.000000 1332.000000 32.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43674" ObjectName="SW-CX_DGZJ.CX_DGZJ_35367SW"/>
     <cge:Meas_Ref ObjectId="270774"/>
    <cge:TPSR_Ref TObjectID="43674"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-270741">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1143.000000 -333.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43648" ObjectName="SW-CX_DGZJ.CX_DGZJ_301XC"/>
     <cge:Meas_Ref ObjectId="270741"/>
    <cge:TPSR_Ref TObjectID="43648"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-270741">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1143.000000 -410.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43649" ObjectName="SW-CX_DGZJ.CX_DGZJ_301XC1"/>
     <cge:Meas_Ref ObjectId="270741"/>
    <cge:TPSR_Ref TObjectID="43649"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-270743">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1171.000000 -557.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43651" ObjectName="SW-CX_DGZJ.CX_DGZJ_30117SW"/>
     <cge:Meas_Ref ObjectId="270743"/>
    <cge:TPSR_Ref TObjectID="43651"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-270742">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1144.000000 -575.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43650" ObjectName="SW-CX_DGZJ.CX_DGZJ_3011SW"/>
     <cge:Meas_Ref ObjectId="270742"/>
    <cge:TPSR_Ref TObjectID="43650"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-270739">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1044.000000 -638.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43646" ObjectName="SW-CX_DGZJ.CX_DGZJ_1010SW"/>
     <cge:Meas_Ref ObjectId="270739"/>
    <cge:TPSR_Ref TObjectID="43646"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-270733">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1171.000000 -777.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43640" ObjectName="SW-CX_DGZJ.CX_DGZJ_10117SW"/>
     <cge:Meas_Ref ObjectId="270733"/>
    <cge:TPSR_Ref TObjectID="43640"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-270736">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(0.000000 1.000000 -1.000000 0.000000 1135.000000 -982.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43643" ObjectName="SW-CX_DGZJ.CX_DGZJ_10167SW"/>
     <cge:Meas_Ref ObjectId="270736"/>
    <cge:TPSR_Ref TObjectID="43643"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-270749">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 582.000000 -265.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43652" ObjectName="SW-CX_DGZJ.CX_DGZJ_3901XC"/>
     <cge:Meas_Ref ObjectId="270749"/>
    <cge:TPSR_Ref TObjectID="43652"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-270749">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 582.000000 -216.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43676" ObjectName="SW-CX_DGZJ.CX_DGZJ_3901XC1"/>
     <cge:Meas_Ref ObjectId="270749"/>
    <cge:TPSR_Ref TObjectID="43676"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-270737">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1180.000000 -1025.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43644" ObjectName="SW-CX_DGZJ.CX_DGZJ_1026SW"/>
     <cge:Meas_Ref ObjectId="270737"/>
    <cge:TPSR_Ref TObjectID="43644"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-270734">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1144.000000 -912.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43641" ObjectName="SW-CX_DGZJ.CX_DGZJ_1016SW"/>
     <cge:Meas_Ref ObjectId="270734"/>
    <cge:TPSR_Ref TObjectID="43641"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-270735">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1189.000000 -917.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43642" ObjectName="SW-CX_DGZJ.CX_DGZJ_10160SW"/>
     <cge:Meas_Ref ObjectId="270735"/>
    <cge:TPSR_Ref TObjectID="43642"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-270738">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1251.000000 -977.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43645" ObjectName="SW-CX_DGZJ.CX_DGZJ_10260SW"/>
     <cge:Meas_Ref ObjectId="270738"/>
    <cge:TPSR_Ref TObjectID="43645"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-270731">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1144.000000 -777.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43638" ObjectName="SW-CX_DGZJ.CX_DGZJ_1011SW"/>
     <cge:Meas_Ref ObjectId="270731"/>
    <cge:TPSR_Ref TObjectID="43638"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-270732">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1187.000000 -860.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43639" ObjectName="SW-CX_DGZJ.CX_DGZJ_10110SW"/>
     <cge:Meas_Ref ObjectId="270732"/>
    <cge:TPSR_Ref TObjectID="43639"/></metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_2c31f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="592,-163 592,-144 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_2c31980@1" ObjectIDZND0="g_2c32190@0" Pin0InfoVect0LinkObjId="g_2c32190_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c31980_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="592,-163 592,-144 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2c35990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="528,-88 528,-129 574,-129 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_2c34be0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c34be0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="528,-88 528,-129 574,-129 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c3baf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="791,-306 791,-288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="43635@0" ObjectIDZND0="43654@0" Pin0InfoVect0LinkObjId="SW-270751_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e3c410_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="791,-306 791,-288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f76b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="791,-271 791,-256 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="43654@1" ObjectIDZND0="43653@1" Pin0InfoVect0LinkObjId="SW-270750_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-270751_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="791,-271 791,-256 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f76db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="791,-229 791,-218 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="43653@0" ObjectIDZND0="43655@1" Pin0InfoVect0LinkObjId="SW-270751_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-270750_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="791,-229 791,-218 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f77d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="791,-147 853,-147 853,-120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="load" EndDevType0="lightningRod" ObjectIDND0="43655@x" ObjectIDND1="43656@x" ObjectIDND2="49413@x" ObjectIDZND0="g_1f77010@0" Pin0InfoVect0LinkObjId="g_1f77010_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-270751_0" Pin1InfoVect1LinkObjId="SW-270752_0" Pin1InfoVect2LinkObjId="EC-CX_DGZJ.351LD_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="791,-147 853,-147 853,-120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f77f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="791,-201 791,-147 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="load" ObjectIDND0="43655@0" ObjectIDZND0="g_1f77010@0" ObjectIDZND1="43656@x" ObjectIDZND2="49413@x" Pin0InfoVect0LinkObjId="g_1f77010_0" Pin0InfoVect1LinkObjId="SW-270752_0" Pin0InfoVect2LinkObjId="EC-CX_DGZJ.351LD_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-270751_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="791,-201 791,-147 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f781c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="791,-147 791,-8 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_1f77010@0" ObjectIDND1="43655@x" ObjectIDND2="43656@x" ObjectIDZND0="49413@0" Pin0InfoVect0LinkObjId="EC-CX_DGZJ.351LD_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1f77010_0" Pin1InfoVect1LinkObjId="SW-270751_0" Pin1InfoVect2LinkObjId="SW-270752_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="791,-147 791,-8 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f7b560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="791,-147 727,-147 727,-136 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="load" EndDevType0="switch" ObjectIDND0="g_1f77010@0" ObjectIDND1="43655@x" ObjectIDND2="49413@x" ObjectIDZND0="43656@1" Pin0InfoVect0LinkObjId="SW-270752_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1f77010_0" Pin1InfoVect1LinkObjId="SW-270751_0" Pin1InfoVect2LinkObjId="EC-CX_DGZJ.351LD_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="791,-147 727,-147 727,-136 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f7b7c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="727,-100 727,-89 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="43656@0" ObjectIDZND0="g_1f7ab10@0" Pin0InfoVect0LinkObjId="g_1f7ab10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-270752_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="727,-100 727,-89 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f82550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1029,-306 1029,-287 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="43635@0" ObjectIDZND0="43658@0" Pin0InfoVect0LinkObjId="SW-270756_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e3c410_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1029,-306 1029,-287 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f84880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1029,-270 1029,-255 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="43658@1" ObjectIDZND0="43657@1" Pin0InfoVect0LinkObjId="SW-270755_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-270756_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1029,-270 1029,-255 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f84ae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1029,-228 1029,-217 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="43657@0" ObjectIDZND0="43659@1" Pin0InfoVect0LinkObjId="SW-270756_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-270755_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1029,-228 1029,-217 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f85af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1029,-146 1091,-146 1091,-119 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="43659@x" ObjectIDND1="49414@x" ObjectIDND2="43660@x" ObjectIDZND0="g_1f84d40@0" Pin0InfoVect0LinkObjId="g_1f84d40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-270756_0" Pin1InfoVect1LinkObjId="EC-CX_DGZJ.352LD_0" Pin1InfoVect2LinkObjId="SW-270757_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1029,-146 1091,-146 1091,-119 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f85d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1029,-200 1029,-146 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" EndDevType2="switch" ObjectIDND0="43659@0" ObjectIDZND0="g_1f84d40@0" ObjectIDZND1="49414@x" ObjectIDZND2="43660@x" Pin0InfoVect0LinkObjId="g_1f84d40_0" Pin0InfoVect1LinkObjId="EC-CX_DGZJ.352LD_0" Pin0InfoVect2LinkObjId="SW-270757_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-270756_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1029,-200 1029,-146 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f85fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1029,-146 1029,-6 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_1f84d40@0" ObjectIDND1="43659@x" ObjectIDND2="43660@x" ObjectIDZND0="49414@0" Pin0InfoVect0LinkObjId="EC-CX_DGZJ.352LD_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1f84d40_0" Pin1InfoVect1LinkObjId="SW-270756_0" Pin1InfoVect2LinkObjId="SW-270757_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1029,-146 1029,-6 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f89460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1029,-146 965,-146 965,-135 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="load" EndDevType0="switch" ObjectIDND0="g_1f84d40@0" ObjectIDND1="43659@x" ObjectIDND2="49414@x" ObjectIDZND0="43660@1" Pin0InfoVect0LinkObjId="SW-270757_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1f84d40_0" Pin1InfoVect1LinkObjId="SW-270756_0" Pin1InfoVect2LinkObjId="EC-CX_DGZJ.352LD_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1029,-146 965,-146 965,-135 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f896c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="965,-99 965,-88 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="43660@0" ObjectIDZND0="g_1f88a10@0" Pin0InfoVect0LinkObjId="g_1f88a10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-270757_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="965,-99 965,-88 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f90450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1269,-306 1269,-287 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="43635@0" ObjectIDZND0="43670@0" Pin0InfoVect0LinkObjId="SW-270771_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e3c410_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1269,-306 1269,-287 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f92780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1269,-270 1269,-255 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="43670@1" ObjectIDZND0="43669@1" Pin0InfoVect0LinkObjId="SW-270770_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-270771_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1269,-270 1269,-255 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f929e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1269,-228 1269,-217 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="43669@0" ObjectIDZND0="43671@1" Pin0InfoVect0LinkObjId="SW-270771_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-270770_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1269,-228 1269,-217 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f939f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1269,-146 1331,-146 1331,-119 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="43671@x" ObjectIDND1="43672@x" ObjectIDND2="g_1fbb270@0" ObjectIDZND0="g_1f92c40@0" Pin0InfoVect0LinkObjId="g_1f92c40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-270771_0" Pin1InfoVect1LinkObjId="SW-270772_0" Pin1InfoVect2LinkObjId="g_1fbb270_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1269,-146 1331,-146 1331,-119 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f93c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1269,-200 1269,-146 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="43671@0" ObjectIDZND0="g_1f92c40@0" ObjectIDZND1="43672@x" ObjectIDZND2="g_1fbb270@0" Pin0InfoVect0LinkObjId="g_1f92c40_0" Pin0InfoVect1LinkObjId="SW-270772_0" Pin0InfoVect2LinkObjId="g_1fbb270_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-270771_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1269,-200 1269,-146 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f97100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1269,-146 1205,-146 1205,-135 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_1f92c40@0" ObjectIDND1="43671@x" ObjectIDND2="g_1fbb270@0" ObjectIDZND0="43672@1" Pin0InfoVect0LinkObjId="SW-270772_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1f92c40_0" Pin1InfoVect1LinkObjId="SW-270771_0" Pin1InfoVect2LinkObjId="g_1fbb270_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1269,-146 1205,-146 1205,-135 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f97360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1205,-99 1205,-88 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="43672@0" ObjectIDZND0="g_1f966b0@0" Pin0InfoVect0LinkObjId="g_1f966b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-270772_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1205,-99 1205,-88 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f9d4c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1492,-306 1492,-285 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="43635@0" ObjectIDZND0="43662@0" Pin0InfoVect0LinkObjId="SW-270761_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e3c410_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1492,-306 1492,-285 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f9f7f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1492,-268 1492,-253 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="43662@1" ObjectIDZND0="43661@1" Pin0InfoVect0LinkObjId="SW-270760_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-270761_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1492,-268 1492,-253 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f9fa50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1492,-226 1492,-215 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="43661@0" ObjectIDZND0="43663@1" Pin0InfoVect0LinkObjId="SW-270761_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-270760_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1492,-226 1492,-215 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fa0a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1492,-144 1554,-144 1554,-117 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="43663@x" ObjectIDND1="43664@x" ObjectIDND2="g_1fc28f0@0" ObjectIDZND0="g_1f9fcb0@0" Pin0InfoVect0LinkObjId="g_1f9fcb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-270761_0" Pin1InfoVect1LinkObjId="SW-270762_0" Pin1InfoVect2LinkObjId="g_1fc28f0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1492,-144 1554,-144 1554,-117 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fa0cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1492,-198 1492,-144 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="43663@0" ObjectIDZND0="g_1f9fcb0@0" ObjectIDZND1="43664@x" ObjectIDZND2="g_1fc28f0@0" Pin0InfoVect0LinkObjId="g_1f9fcb0_0" Pin0InfoVect1LinkObjId="SW-270762_0" Pin0InfoVect2LinkObjId="g_1fc28f0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-270761_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1492,-198 1492,-144 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fa4170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1492,-144 1428,-144 1428,-133 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_1f9fcb0@0" ObjectIDND1="43663@x" ObjectIDND2="g_1fc28f0@0" ObjectIDZND0="43664@1" Pin0InfoVect0LinkObjId="SW-270762_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1f9fcb0_0" Pin1InfoVect1LinkObjId="SW-270761_0" Pin1InfoVect2LinkObjId="g_1fc28f0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1492,-144 1428,-144 1428,-133 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fa43d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1428,-97 1428,-86 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="43664@0" ObjectIDZND0="g_1fa3720@0" Pin0InfoVect0LinkObjId="g_1fa3720_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-270762_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1428,-97 1428,-86 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fab1a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1742,-306 1742,-288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="43635@0" ObjectIDZND0="43666@0" Pin0InfoVect0LinkObjId="SW-270766_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e3c410_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1742,-306 1742,-288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fad4d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1742,-271 1742,-257 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="43666@1" ObjectIDZND0="43665@1" Pin0InfoVect0LinkObjId="SW-270765_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-270766_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1742,-271 1742,-257 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fad730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1742,-230 1742,-215 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="43665@0" ObjectIDZND0="43667@1" Pin0InfoVect0LinkObjId="SW-270766_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-270765_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1742,-230 1742,-215 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fae740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1742,-144 1804,-144 1804,-117 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="43667@x" ObjectIDND1="43668@x" ObjectIDND2="g_1fc4970@0" ObjectIDZND0="g_1fad990@0" Pin0InfoVect0LinkObjId="g_1fad990_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-270766_0" Pin1InfoVect1LinkObjId="SW-270767_0" Pin1InfoVect2LinkObjId="g_1fc4970_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1742,-144 1804,-144 1804,-117 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fae9a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1742,-198 1742,-144 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="43667@0" ObjectIDZND0="g_1fad990@0" ObjectIDZND1="43668@x" ObjectIDZND2="g_1fc4970@0" Pin0InfoVect0LinkObjId="g_1fad990_0" Pin0InfoVect1LinkObjId="SW-270767_0" Pin0InfoVect2LinkObjId="g_1fc4970_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-270766_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1742,-198 1742,-144 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fb1e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1742,-144 1678,-144 1678,-133 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_1fad990@0" ObjectIDND1="43667@x" ObjectIDND2="g_1fc4970@0" ObjectIDZND0="43668@1" Pin0InfoVect0LinkObjId="SW-270767_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1fad990_0" Pin1InfoVect1LinkObjId="SW-270766_0" Pin1InfoVect2LinkObjId="g_1fc4970_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1742,-144 1678,-144 1678,-133 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fb20b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1678,-97 1678,-86 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="43668@0" ObjectIDZND0="g_1fb1400@0" Pin0InfoVect0LinkObjId="g_1fb1400_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-270767_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1678,-97 1678,-86 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fbb9b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1269,-146 1269,-96 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_1f92c40@0" ObjectIDND1="43671@x" ObjectIDND2="43672@x" ObjectIDZND0="g_1fbb270@1" Pin0InfoVect0LinkObjId="g_1fbb270_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1f92c40_0" Pin1InfoVect1LinkObjId="SW-270771_0" Pin1InfoVect2LinkObjId="SW-270772_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1269,-146 1269,-96 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fbe2b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1269,-43 1269,-31 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1fbb270@0" ObjectIDZND0="43673@1" Pin0InfoVect0LinkObjId="SW-270773_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fbb270_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1269,-43 1269,-31 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fc0d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1345,41 1327,41 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1fc0f70@0" ObjectIDZND0="43674@0" Pin0InfoVect0LinkObjId="SW-270774_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fc0f70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1345,41 1327,41 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fc2690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1269,104 1269,122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1fc1a00@0" ObjectIDZND0="g_1e34470@0" Pin0InfoVect0LinkObjId="g_1e34470_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fc1a00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1269,104 1269,122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fc3310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1492,-144 1492,-95 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_1f9fcb0@0" ObjectIDND1="43663@x" ObjectIDND2="43664@x" ObjectIDZND0="g_1fc28f0@1" Pin0InfoVect0LinkObjId="g_1fc28f0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1f9fcb0_0" Pin1InfoVect1LinkObjId="SW-270761_0" Pin1InfoVect2LinkObjId="SW-270762_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1492,-144 1492,-95 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fc5390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1742,-144 1742,-94 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_1fad990@0" ObjectIDND1="43667@x" ObjectIDND2="43668@x" ObjectIDZND0="g_1fc4970@1" Pin0InfoVect0LinkObjId="g_1fc4970_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1fad990_0" Pin1InfoVect1LinkObjId="SW-270766_0" Pin1InfoVect2LinkObjId="SW-270767_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1742,-144 1742,-94 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fc55f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1742,-41 1742,-30 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_1fc4970@0" ObjectIDZND0="49416@0" Pin0InfoVect0LinkObjId="EC-CX_DGZJ.355LD_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fc4970_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1742,-41 1742,-30 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fd32a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1212,-548 1226,-548 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="43651@1" ObjectIDZND0="g_1fd3500@0" Pin0InfoVect0LinkObjId="g_1fd3500_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-270743_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1212,-548 1226,-548 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1df2a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1153,-705 1053,-705 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="43675@x" ObjectIDZND0="43646@x" ObjectIDZND1="g_1df06c0@0" Pin0InfoVect0LinkObjId="SW-270739_0" Pin0InfoVect1LinkObjId="g_1df06c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1153,-705 1053,-705 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1df5710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1053,-705 1053,-679 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" EndDevType0="switch" ObjectIDND0="g_1df06c0@0" ObjectIDND1="43675@x" ObjectIDZND0="43646@1" Pin0InfoVect0LinkObjId="SW-270739_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1df06c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1053,-705 1053,-679 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1df5970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1053,-643 1053,-614 972,-613 972,-628 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="43646@0" ObjectIDZND0="g_1defaa0@0" Pin0InfoVect0LinkObjId="g_1defaa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-270739_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1053,-643 1053,-614 972,-613 972,-628 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1df83e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1153,-768 1176,-768 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="43675@x" ObjectIDND1="43638@x" ObjectIDZND0="43640@0" Pin0InfoVect0LinkObjId="SW-270733_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="SW-270731_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1153,-768 1176,-768 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1df8640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1212,-768 1226,-768 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="43640@1" ObjectIDZND0="g_1df88a0@0" Pin0InfoVect0LinkObjId="g_1df88a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-270733_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1212,-768 1226,-768 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1df9bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1153,-725 1153,-768 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="43675@0" ObjectIDZND0="43640@x" ObjectIDZND1="43638@x" Pin0InfoVect0LinkObjId="SW-270733_0" Pin0InfoVect1LinkObjId="SW-270731_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1153,-725 1153,-768 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1dfe6f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1094,-973 1080,-973 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="43643@1" ObjectIDZND0="g_1dfe950@0" Pin0InfoVect0LinkObjId="g_1dfe950_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-270736_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1094,-973 1080,-973 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e01f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1269,41 1291,41 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1fc1a00@0" ObjectIDZND0="43674@1" Pin0InfoVect0LinkObjId="SW-270774_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fc1a00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1269,41 1291,41 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e02a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1269,34 1269,41 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" EndDevType1="lightningRod" ObjectIDZND0="43674@x" ObjectIDZND1="g_1fc1a00@0" Pin0InfoVect0LinkObjId="SW-270774_0" Pin0InfoVect1LinkObjId="g_1fc1a00_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1269,34 1269,41 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e02c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1269,62 1269,41 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1fc1a00@1" ObjectIDZND0="43674@x" Pin0InfoVect0LinkObjId="SW-270774_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fc1a00_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1269,62 1269,41 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e050c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="655,-176 655,-203 592,-203 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_1fb8ff0@0" ObjectIDZND0="g_2c31980@0" ObjectIDZND1="43676@x" Pin0InfoVect0LinkObjId="g_2c31980_0" Pin0InfoVect1LinkObjId="SW-270749_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fb8ff0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="655,-176 655,-203 592,-203 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e052b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="592,-203 592,-194 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_1fb8ff0@0" ObjectIDND1="43676@x" ObjectIDZND0="g_2c31980@0" Pin0InfoVect0LinkObjId="g_2c31980_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1fb8ff0_0" Pin1InfoVect1LinkObjId="SW-270749_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="592,-203 592,-194 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e0b110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="592,-306 592,-289 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="43635@0" ObjectIDZND0="43652@0" Pin0InfoVect0LinkObjId="SW-270749_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e3c410_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="592,-306 592,-289 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e0b370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="592,-272 592,-240 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="43652@1" ObjectIDZND0="43676@1" Pin0InfoVect0LinkObjId="SW-270749_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-270749_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="592,-272 592,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e0b5d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="592,-223 592,-203 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="43676@0" ObjectIDZND0="g_1fb8ff0@0" ObjectIDZND1="g_2c31980@0" Pin0InfoVect0LinkObjId="g_1fb8ff0_0" Pin0InfoVect1LinkObjId="g_2c31980_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-270749_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="592,-223 592,-203 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1e19610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1260,-1018 1260,-1030 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" ObjectIDND0="43645@1" ObjectIDZND0="43644@x" ObjectIDZND1="g_1dff3e0@0" Pin0InfoVect0LinkObjId="SW-270737_0" Pin0InfoVect1LinkObjId="g_1dff3e0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-270738_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1260,-1018 1260,-1030 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1e1a100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1221,-1030 1260,-1030 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" EndDevType1="switch" ObjectIDND0="43644@1" ObjectIDZND0="g_1dff3e0@0" ObjectIDZND1="43645@x" Pin0InfoVect0LinkObjId="g_1dff3e0_0" Pin0InfoVect1LinkObjId="SW-270738_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-270737_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1221,-1030 1260,-1030 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1e1a360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1260,-1030 1334,-1030 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" ObjectIDND0="43644@x" ObjectIDND1="43645@x" ObjectIDZND0="g_1dff3e0@0" Pin0InfoVect0LinkObjId="g_1dff3e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-270737_0" Pin1InfoVect1LinkObjId="SW-270738_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1260,-1030 1334,-1030 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1e1ae50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1130,-973 1153,-973 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="43643@0" ObjectIDZND0="43641@x" ObjectIDZND1="43644@x" ObjectIDZND2="g_1e01170@0" Pin0InfoVect0LinkObjId="SW-270734_0" Pin0InfoVect1LinkObjId="SW-270737_0" Pin0InfoVect2LinkObjId="g_1e01170_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-270736_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1130,-973 1153,-973 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1e1d8b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1153,-953 1153,-973 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="43641@1" ObjectIDZND0="43643@x" ObjectIDZND1="43644@x" ObjectIDZND2="g_1e01170@0" Pin0InfoVect0LinkObjId="SW-270736_0" Pin0InfoVect1LinkObjId="SW-270737_0" Pin0InfoVect2LinkObjId="g_1e01170_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-270734_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1153,-953 1153,-973 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1e1db10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1193,-908 1153,-908 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="43642@0" ObjectIDZND0="43637@x" ObjectIDZND1="43641@x" Pin0InfoVect0LinkObjId="SW-270730_0" Pin0InfoVect1LinkObjId="SW-270734_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-270735_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1193,-908 1153,-908 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1e1e600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1153,-894 1153,-908 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="43637@1" ObjectIDZND0="43641@x" ObjectIDZND1="43642@x" Pin0InfoVect0LinkObjId="SW-270734_0" Pin0InfoVect1LinkObjId="SW-270735_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-270730_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1153,-894 1153,-908 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1e1e860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1153,-908 1153,-917 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="43637@x" ObjectIDND1="43642@x" ObjectIDZND0="43641@0" Pin0InfoVect0LinkObjId="SW-270734_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-270730_0" Pin1InfoVect1LinkObjId="SW-270735_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1153,-908 1153,-917 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1e212c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1230,-908 1244,-908 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="43642@1" ObjectIDZND0="g_1e21520@0" Pin0InfoVect0LinkObjId="g_1e21520_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-270735_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1230,-908 1244,-908 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1e25200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1260,-966 1260,-982 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1e247b0@0" ObjectIDZND0="43645@0" Pin0InfoVect0LinkObjId="SW-270738_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e247b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1260,-966 1260,-982 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1e27c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1153,-768 1153,-782 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer2" EndDevType0="switch" ObjectIDND0="43640@x" ObjectIDND1="43675@x" ObjectIDZND0="43638@0" Pin0InfoVect0LinkObjId="SW-270731_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-270733_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1153,-768 1153,-782 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1e27ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1228,-851 1242,-851 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="43639@1" ObjectIDZND0="g_1e28120@0" Pin0InfoVect0LinkObjId="g_1e28120_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-270732_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1228,-851 1242,-851 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1e353e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1153,-818 1153,-836 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="43638@1" ObjectIDZND0="49420@0" Pin0InfoVect0LinkObjId="g_1e36b80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-270731_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1153,-818 1153,-836 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1e35b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1153,-836 1153,-851 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="49420@0" ObjectIDZND0="43639@x" ObjectIDZND1="43637@x" Pin0InfoVect0LinkObjId="SW-270732_0" Pin0InfoVect1LinkObjId="SW-270730_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e353e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1153,-836 1153,-851 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1e36b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1192,-851 1153,-851 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" EndDevType1="breaker" ObjectIDND0="43639@0" ObjectIDZND0="49420@0" ObjectIDZND1="43637@x" Pin0InfoVect0LinkObjId="g_1e353e0_0" Pin0InfoVect1LinkObjId="SW-270730_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-270732_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1192,-851 1153,-851 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1e36dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1153,-851 1153,-866 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="49420@0" ObjectIDND1="43639@x" ObjectIDZND0="43637@0" Pin0InfoVect0LinkObjId="SW-270730_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1e353e0_0" Pin1InfoVect1LinkObjId="SW-270732_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1153,-851 1153,-866 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1e37020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1185,-1030 1153,-1030 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="43644@0" ObjectIDZND0="43643@x" ObjectIDZND1="43641@x" ObjectIDZND2="g_1e01170@0" Pin0InfoVect0LinkObjId="SW-270736_0" Pin0InfoVect1LinkObjId="SW-270734_0" Pin0InfoVect2LinkObjId="g_1e01170_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-270737_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1185,-1030 1153,-1030 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1e37280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1153,-1090 1193,-1090 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="43644@x" ObjectIDND1="43643@x" ObjectIDND2="43641@x" ObjectIDZND0="g_1e01170@0" Pin0InfoVect0LinkObjId="g_1e01170_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-270737_0" Pin1InfoVect1LinkObjId="SW-270736_0" Pin1InfoVect2LinkObjId="SW-270734_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1153,-1090 1193,-1090 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1e37d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1153,-1090 1153,-1140 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_1e01170@0" ObjectIDND1="43644@x" ObjectIDND2="43643@x" ObjectIDZND0="43825@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1e01170_0" Pin1InfoVect1LinkObjId="SW-270737_0" Pin1InfoVect2LinkObjId="SW-270736_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1153,-1090 1153,-1140 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1e38820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1153,-973 1153,-1030 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="43643@x" ObjectIDND1="43641@x" ObjectIDZND0="43644@x" ObjectIDZND1="g_1e01170@0" ObjectIDZND2="43825@1" Pin0InfoVect0LinkObjId="SW-270737_0" Pin0InfoVect1LinkObjId="g_1e01170_0" Pin0InfoVect2LinkObjId="g_1e37d50_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-270736_0" Pin1InfoVect1LinkObjId="SW-270734_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1153,-973 1153,-1030 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1e38a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1153,-1030 1153,-1090 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="43644@x" ObjectIDND1="43643@x" ObjectIDND2="43641@x" ObjectIDZND0="g_1e01170@0" ObjectIDZND1="43825@1" Pin0InfoVect0LinkObjId="g_1e01170_0" Pin0InfoVect1LinkObjId="g_1e37d50_1" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-270737_0" Pin1InfoVect1LinkObjId="SW-270736_0" Pin1InfoVect2LinkObjId="SW-270734_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1153,-1030 1153,-1090 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e398d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1269,13 1269,7 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" ObjectIDND0="49444@0" ObjectIDZND0="43673@0" Pin0InfoVect0LinkObjId="SW-270773_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-CX_DGZJ.353LD_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1269,13 1269,7 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e39b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1492,-24 1492,-42 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" ObjectIDND0="49415@0" ObjectIDZND0="g_1fc28f0@0" Pin0InfoVect0LinkObjId="g_1fc28f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-CX_DGZJ.354LD_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1492,-24 1492,-42 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e3c410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1153,-340 1153,-306 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="43648@0" ObjectIDZND0="43635@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-270741_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1153,-340 1153,-306 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e3c670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1153,-371 1153,-357 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="43647@0" ObjectIDZND0="43648@1" Pin0InfoVect0LinkObjId="SW-270741_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-270740_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1153,-371 1153,-357 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e3c8d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1153,-417 1153,-398 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="43649@1" ObjectIDZND0="43647@1" Pin0InfoVect0LinkObjId="SW-270740_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-270741_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1153,-417 1153,-398 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e3cb30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1153,-645 1153,-616 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="43675@1" ObjectIDZND0="43650@1" Pin0InfoVect0LinkObjId="SW-270742_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1153,-645 1153,-616 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e3cd90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1176,-548 1153,-548 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="43651@0" ObjectIDZND0="43650@x" ObjectIDZND1="g_1fcfcf0@0" ObjectIDZND2="43649@x" Pin0InfoVect0LinkObjId="SW-270742_0" Pin0InfoVect1LinkObjId="g_1fcfcf0_0" Pin0InfoVect2LinkObjId="SW-270741_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-270743_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1176,-548 1153,-548 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e3d880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1153,-580 1153,-548 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="43650@0" ObjectIDZND0="43651@x" ObjectIDZND1="g_1fcfcf0@0" ObjectIDZND2="43649@x" Pin0InfoVect0LinkObjId="SW-270743_0" Pin0InfoVect1LinkObjId="g_1fcfcf0_0" Pin0InfoVect2LinkObjId="SW-270741_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-270742_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1153,-580 1153,-548 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e3dae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1191,-475 1153,-475 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1fcfcf0@0" ObjectIDZND0="43651@x" ObjectIDZND1="43650@x" ObjectIDZND2="43649@x" Pin0InfoVect0LinkObjId="SW-270743_0" Pin0InfoVect1LinkObjId="SW-270742_0" Pin0InfoVect2LinkObjId="SW-270741_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fcfcf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1191,-475 1153,-475 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e3e5d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1153,-548 1153,-475 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="43651@x" ObjectIDND1="43650@x" ObjectIDZND0="g_1fcfcf0@0" ObjectIDZND1="43649@x" Pin0InfoVect0LinkObjId="g_1fcfcf0_0" Pin0InfoVect1LinkObjId="SW-270741_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-270743_0" Pin1InfoVect1LinkObjId="SW-270742_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1153,-548 1153,-475 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e3e830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1153,-475 1153,-434 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_1fcfcf0@0" ObjectIDND1="43651@x" ObjectIDND2="43650@x" ObjectIDZND0="43649@0" Pin0InfoVect0LinkObjId="SW-270741_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1fcfcf0_0" Pin1InfoVect1LinkObjId="SW-270743_0" Pin1InfoVect2LinkObjId="SW-270742_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1153,-475 1153,-434 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_208d5d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="972,-689 972,-705 997,-705 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_1defaa0@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1defaa0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="972,-689 972,-705 997,-705 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1ff1320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1008,-679 1008,-705 1053,-705 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="transformer2" ObjectIDND0="g_1df06c0@0" ObjectIDZND0="43646@x" ObjectIDZND1="43675@x" Pin0InfoVect0LinkObjId="SW-270739_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1df06c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1008,-679 1008,-705 1053,-705 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_DGZJ"/>
</svg>