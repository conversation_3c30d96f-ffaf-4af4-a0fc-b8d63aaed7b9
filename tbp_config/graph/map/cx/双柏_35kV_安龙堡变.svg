<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-165" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="-259 -1173 2098 1297">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="capacitor:shape41">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.299051" x1="44" x2="44" y1="67" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="25" x2="25" y1="101" y2="109"/>
    <polyline arcFlag="1" points="44,21 45,21 46,21 46,21 47,22 47,22 48,23 48,23 49,24 49,24 49,25 49,26 50,27 50,28 50,28 49,29 49,30 49,31 49,31 48,32 48,32 47,33 47,33 46,34 46,34 45,34 44,34 " stroke-width="1"/>
    <polyline arcFlag="1" points="44,34 45,34 46,34 46,34 47,35 47,35 48,36 48,36 49,37 49,37 49,38 49,39 50,40 50,41 50,41 49,42 49,43 49,44 49,44 48,45 48,45 47,46 47,46 46,47 46,47 45,47 44,47 " stroke-width="1"/>
    <polyline arcFlag="1" points="44,47 45,47 46,47 46,47 47,48 47,48 48,49 48,49 49,50 49,50 49,51 49,52 50,53 50,54 50,54 49,55 49,56 49,57 49,57 48,58 48,58 47,59 47,59 46,60 46,60 45,60 44,60 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.368819" x1="7" x2="44" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.395152" x1="26" x2="44" y1="13" y2="13"/>
    <rect height="28" stroke-width="0.398039" width="12" x="1" y="32"/>
    <rect height="26" stroke-width="0.398039" width="12" x="20" y="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.275463" x1="7" x2="7" y1="6" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.275463" x1="44" x2="44" y1="6" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.682641" x1="26" x2="26" y1="21" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="16" x2="36" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="16" x2="36" y1="29" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="24" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="44" x2="44" y1="21" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="26" x2="26" y1="29" y2="88"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="12" x2="26" y1="88" y2="88"/>
    <polyline points="25,101 27,101 29,100 30,100 32,99 33,98 35,97 36,95 37,93 37,92 38,90 38,88 38,86 37,84 37,83 36,81 35,80 33,78 32,77 30,76 29,76 27,75 25,75 23,75 21,76 20,76 18,77 17,78 15,80 14,81 13,83 13,84 12,86 12,88 " stroke-width="0.0972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="0.708333" x1="7" x2="7" y1="67" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.468987" x1="7" x2="44" y1="68" y2="68"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="0" x2="12" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="earth:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="14" y2="14"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="13" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="9" y1="60" y2="60"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="8" x2="5" y1="1" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="10" x2="3" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="8" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="1" x2="12" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape4">
    <rect height="31" stroke-width="2" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="9" x2="9" y1="35" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <rect height="14" stroke-width="1" width="27" x="18" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="5" x2="5" y1="11" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="9" x2="18" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="8" x2="8" y1="2" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="58" x2="23" y1="7" y2="7"/>
   </symbol>
   <symbol id="load:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape19_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="14" y1="30" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="26" y1="38" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="14" y1="26" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="7" y1="38" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="27" y1="17" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="5" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="5" y1="46" y2="10"/>
    <rect height="26" stroke-width="0.416609" width="14" x="-2" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="34" y1="10" y2="40"/>
    <rect height="29" stroke-width="0.416609" width="9" x="12" y="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="5" y1="46" y2="10"/>
    <rect height="26" stroke-width="0.416609" width="14" x="-2" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape45_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="5" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="34" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="25" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape45_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="9" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="34" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="14" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape45-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="5" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="34" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="25" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape45-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="9" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="34" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="14" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="4" y2="14"/>
   </symbol>
   <symbol id="transformer2:shape39_0">
    <ellipse cx="37" cy="29" fillStyle="0" rx="26.5" ry="25.5" stroke-width="0.62032"/>
    <polyline points="64,96 64,89 " stroke-width="1"/>
    <polyline points="58,96 64,96 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="38,34 31,19 46,19 38,34 38,34 38,34 "/>
   </symbol>
   <symbol id="transformer2:shape39_1">
    <circle cx="37" cy="66" fillStyle="0" r="26.5" stroke-width="0.63865"/>
    <polyline points="64,96 1,33 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="38" y1="70" y2="79"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="38" y1="70" y2="79"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="38" y1="64" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="38" y1="64" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="28" y1="71" y2="64"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="28" y1="71" y2="64"/>
   </symbol>
   <symbol id="transformer2:shape55_0">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="31,44 6,44 6,73 " stroke-width="1"/>
    <circle cx="31" cy="42" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="56" y2="98"/>
    <polyline DF8003:Layer="PUBLIC" points="31,87 25,74 37,74 31,87 31,86 31,87 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="74" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="76" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="79" y2="79"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="31" y1="49" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="44" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="36" y1="44" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="26" y1="44" y2="39"/>
   </symbol>
   <symbol id="transformer2:shape55_1">
    <circle cx="31" cy="20" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="20" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="36" y1="20" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="26" y1="20" y2="15"/>
   </symbol>
   <symbol id="transformer2:shape41_0">
    <circle cx="16" cy="20" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="56" y2="98"/>
    <polyline DF8003:Layer="PUBLIC" points="15,84 21,71 8,71 15,84 15,83 15,84 "/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="32,42 41,42 41,70 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="35" x2="47" y1="73" y2="73"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="45" x2="37" y1="75" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="44" x2="41" y1="78" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="15" y1="45" y2="72"/>
    <polyline DF8003:Layer="PUBLIC" points="17,11 21,20 11,20 17,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="17" y2="17"/>
   </symbol>
   <symbol id="transformer2:shape41_1">
    <circle cx="17" cy="42" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="41" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="21" y1="41" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="12" y1="41" y2="45"/>
   </symbol>
   <symbol id="voltageTransformer:shape56">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="1" y1="58" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="20" y1="58" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="20" y1="38" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="1" x2="13" y1="51" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="22" y1="59" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="22" y1="37" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="25" y1="61" y2="61"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="22" y1="70" y2="61"/>
    <ellipse cx="21" cy="8" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <ellipse cx="21" cy="16" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="25" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="25" y1="18" y2="18"/>
   </symbol>
   <symbol id="voltageTransformer:shape21">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="28" x2="28" y1="11" y2="5"/>
    <circle cx="15" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="26" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="13" y1="11" y2="5"/>
   </symbol>
   <symbol id="voltageTransformer:shape63">
    <ellipse cx="19" cy="18" rx="7.5" ry="6.5" stroke-width="0.66594"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="25" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236902" x1="21" x2="21" y1="7" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236902" x1="18" x2="21" y1="9" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236902" x1="21" x2="23" y1="7" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236902" x1="20" x2="20" y1="19" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236902" x1="17" x2="20" y1="21" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236902" x1="20" x2="22" y1="19" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236902" x1="8" x2="8" y1="19" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236902" x1="5" x2="8" y1="21" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236902" x1="8" x2="10" y1="19" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="6" x2="9" y1="6" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.103806" x1="6" x2="9" y1="9" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="9" x2="9" y1="5" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="24" y1="50" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="21" y1="54" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="22" y1="52" y2="52"/>
    <ellipse cx="8" cy="8" rx="7.5" ry="7" stroke-width="0.66594"/>
    <ellipse cx="19" cy="8" rx="7.5" ry="7" stroke-width="0.66594"/>
    <ellipse cx="8" cy="17" rx="7.5" ry="6.5" stroke-width="0.66594"/>
    <rect height="13" stroke-width="1" width="7" x="16" y="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="42" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="25" y1="33" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="31" y1="39" y2="39"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22e3680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22e4030" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_22e49e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_22e5750" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_22e69a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_22e75c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22e7d00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_22e85e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1c62630" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1c62630" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22eb110" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22eb110" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22ec5c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22ec5c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_22ed2c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22eeec0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_22efab0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_22f0870" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_22f0f20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22f2550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22f2d50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22f3440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_22f3bd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22f4ba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22f5520" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22f6010" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_22f69d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_22f8050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_22f8a10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_22f99c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_22fa540" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2308b10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2300c70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2301930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_22fc3e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(0,0,0)" height="1307" width="2108" x="-264" y="-1178"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="1116" x2="1116" y1="-185" y2="-41"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="1116" x2="1182" y1="-41" y2="-41"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="1109" x2="1124" y1="-235" y2="-235"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="1112" x2="1120" y1="-238" y2="-238"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="1113" x2="1119" y1="-241" y2="-241"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="1137" x2="1178" y1="-192" y2="-192"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="1116" x2="1116" y1="-211" y2="-235"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="354" x2="364" y1="-564" y2="-564"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="364" x2="364" y1="-564" y2="-543"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="354" x2="354" y1="-564" y2="-543"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="354" x2="364" y1="-543" y2="-543"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-113674">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 768.000000 -665.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21581" ObjectName="SW-SB_ALB.SB_ALB_301BK"/>
     <cge:Meas_Ref ObjectId="113674"/>
    <cge:TPSR_Ref TObjectID="21581"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-113680">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 768.000000 -493.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21583" ObjectName="SW-SB_ALB.SB_ALB_001BK"/>
     <cge:Meas_Ref ObjectId="113680"/>
    <cge:TPSR_Ref TObjectID="21583"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-113562">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1513.000000 -249.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21559" ObjectName="SW-SB_ALB.SB_ALB_071BK"/>
     <cge:Meas_Ref ObjectId="113562"/>
    <cge:TPSR_Ref TObjectID="21559"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-113578">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1339.000000 -249.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21562" ObjectName="SW-SB_ALB.SB_ALB_072BK"/>
     <cge:Meas_Ref ObjectId="113578"/>
    <cge:TPSR_Ref TObjectID="21562"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-113594">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1172.000000 -290.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21565" ObjectName="SW-SB_ALB.SB_ALB_073BK"/>
     <cge:Meas_Ref ObjectId="113594"/>
    <cge:TPSR_Ref TObjectID="21565"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-113610">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 753.000000 -258.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21568" ObjectName="SW-SB_ALB.SB_ALB_074BK"/>
     <cge:Meas_Ref ObjectId="113610"/>
    <cge:TPSR_Ref TObjectID="21568"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-113626">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 575.000000 -252.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21571" ObjectName="SW-SB_ALB.SB_ALB_075BK"/>
     <cge:Meas_Ref ObjectId="113626"/>
    <cge:TPSR_Ref TObjectID="21571"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-113642">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 401.000000 -250.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21574" ObjectName="SW-SB_ALB.SB_ALB_076BK"/>
     <cge:Meas_Ref ObjectId="113642"/>
    <cge:TPSR_Ref TObjectID="21574"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-113659">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 768.000000 -858.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21577" ObjectName="SW-SB_ALB.SB_ALB_371BK"/>
     <cge:Meas_Ref ObjectId="113659"/>
    <cge:TPSR_Ref TObjectID="21577"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-187287">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 30.000000 -511.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21591" ObjectName="SW-SB_ALB.SB_ALB_ZYB1BK"/>
     <cge:Meas_Ref ObjectId="187287"/>
    <cge:TPSR_Ref TObjectID="21591"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-187288">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 119.000000 -516.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21592" ObjectName="SW-SB_ALB.SB_ALB_ZYB2BK"/>
     <cge:Meas_Ref ObjectId="187288"/>
    <cge:TPSR_Ref TObjectID="21592"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-251781">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1389.000000 -669.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41938" ObjectName="SW-SB_ALB.SB_ALB_302BK"/>
     <cge:Meas_Ref ObjectId="251781"/>
    <cge:TPSR_Ref TObjectID="41938"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-251795">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1389.000000 -497.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41941" ObjectName="SW-SB_ALB.SB_ALB_002BK"/>
     <cge:Meas_Ref ObjectId="251795"/>
    <cge:TPSR_Ref TObjectID="41941"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-251873">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1676.071429 -251.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41945" ObjectName="SW-SB_ALB.SB_ALB_077BK"/>
     <cge:Meas_Ref ObjectId="251873"/>
    <cge:TPSR_Ref TObjectID="41945"/></metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_29d1da0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 667.000000 -985.000000)" xlink:href="#voltageTransformer:shape56"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_16bee10">
    <use class="BV-10KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 620.000000 -71.000000)" xlink:href="#voltageTransformer:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2823770">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1241.000000 -914.000000)" xlink:href="#voltageTransformer:shape63"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15f4bf0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1642.000000 -586.000000)" xlink:href="#voltageTransformer:shape63"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ecb9a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 345.000000 -593.067797)" xlink:href="#voltageTransformer:shape63"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="SB_ALB" endPointId="0" endStationName="PAS_T1" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_damaidiTalb" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="777,-1088 777,-1132 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="37760" ObjectName="AC-35kV.LN_damaidiTalb"/>
    <cge:TPSR_Ref TObjectID="37760_SS-165"/></metadata>
   <polyline fill="none" opacity="0" points="777,-1088 777,-1132 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-SB_ALB.071Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1517.000000 -17.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34337" ObjectName="EC-SB_ALB.071Ld"/>
    <cge:TPSR_Ref TObjectID="34337"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-SB_ALB.072Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1343.000000 -17.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34338" ObjectName="EC-SB_ALB.072Ld"/>
    <cge:TPSR_Ref TObjectID="34338"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-SB_ALB.074Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 757.000000 -26.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34341" ObjectName="EC-SB_ALB.074Ld"/>
    <cge:TPSR_Ref TObjectID="34341"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-SB_ALB.075Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 579.000000 -19.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34340" ObjectName="EC-SB_ALB.075Ld"/>
    <cge:TPSR_Ref TObjectID="34340"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-SB_ALB.076Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 405.000000 -18.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34339" ObjectName="EC-SB_ALB.076Ld"/>
    <cge:TPSR_Ref TObjectID="34339"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1679.519231 -19.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2f08300" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1285.000000 -776.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bca8b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 849.000000 -977.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_16486f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1134.000000 -221.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2db7910" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 -1.000000 -1.000000 0.000000 1498.500000 -707.500000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_1cfbd10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="777,-754 777,-767 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="21582@1" ObjectIDZND0="21556@0" Pin0InfoVect0LinkObjId="g_15f93a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-113677_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="777,-754 777,-767 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16ecf30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="777,-700 777,-718 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="21581@1" ObjectIDZND0="21582@0" Pin0InfoVect0LinkObjId="SW-113677_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-113674_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="777,-700 777,-718 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f77890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="777,-649 777,-673 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="21588@1" ObjectIDZND0="21581@0" Pin0InfoVect0LinkObjId="SW-113674_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f77ad0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="777,-649 777,-673 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f77ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="777,-528 777,-562 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="21583@1" ObjectIDZND0="21588@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-113680_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="777,-528 777,-562 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c802a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="777,-417 777,-441 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="41950@0" ObjectIDZND0="21584@0" Pin0InfoVect0LinkObjId="SW-113683_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2bd5380_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="777,-417 777,-441 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c804d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="777,-477 777,-501 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="21584@1" ObjectIDZND0="21583@0" Pin0InfoVect0LinkObjId="SW-113680_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-113683_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="777,-477 777,-501 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3365320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1656,-416 1656,-439 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="21557@0" ObjectIDZND0="21587@0" Pin0InfoVect0LinkObjId="SW-113724_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a6eff0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1656,-416 1656,-439 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3365580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1656,-564 1656,-588 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="0@1" ObjectIDZND0="g_15f4bf0@0" Pin0InfoVect0LinkObjId="g_15f4bf0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29d1da0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1656,-564 1656,-588 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c0ebf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1522,-416 1522,-382 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="21557@0" ObjectIDZND0="21560@1" Pin0InfoVect0LinkObjId="SW-113564_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a6eff0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1522,-416 1522,-382 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c0ee50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1522,-346 1522,-284 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="21560@0" ObjectIDZND0="21559@1" Pin0InfoVect0LinkObjId="SW-113562_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-113564_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1522,-346 1522,-284 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c0f0b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1522,-257 1522,-220 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="21559@0" ObjectIDZND0="21561@1" Pin0InfoVect0LinkObjId="SW-113565_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-113562_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1522,-257 1522,-220 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f15b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1348,-416 1348,-382 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="21557@0" ObjectIDZND0="21563@1" Pin0InfoVect0LinkObjId="SW-113580_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a6eff0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1348,-416 1348,-382 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f15da0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1348,-346 1348,-284 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="21563@0" ObjectIDZND0="21562@1" Pin0InfoVect0LinkObjId="SW-113578_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-113580_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1348,-346 1348,-284 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f16000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1348,-257 1348,-220 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="21562@0" ObjectIDZND0="21564@1" Pin0InfoVect0LinkObjId="SW-113581_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-113578_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1348,-257 1348,-220 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f12120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="762,-417 762,-391 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="41950@0" ObjectIDZND0="21569@1" Pin0InfoVect0LinkObjId="SW-113612_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2bd5380_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="762,-417 762,-391 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f12380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="762,-355 762,-293 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="21569@0" ObjectIDZND0="21568@1" Pin0InfoVect0LinkObjId="SW-113610_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-113612_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="762,-355 762,-293 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f125e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="762,-266 762,-229 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="21568@0" ObjectIDZND0="21570@1" Pin0InfoVect0LinkObjId="SW-113613_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-113610_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="762,-266 762,-229 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2fbe7f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="584,-417 584,-384 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="41950@0" ObjectIDZND0="21572@1" Pin0InfoVect0LinkObjId="SW-113628_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2bd5380_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="584,-417 584,-384 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2fbea50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="584,-348 584,-286 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="21572@0" ObjectIDZND0="21571@1" Pin0InfoVect0LinkObjId="SW-113626_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-113628_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="584,-348 584,-286 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2fbecb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="584,-260 584,-222 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="21571@0" ObjectIDZND0="21573@1" Pin0InfoVect0LinkObjId="SW-113629_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-113626_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="584,-260 584,-222 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e8af20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="410,-417 410,-383 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="41950@0" ObjectIDZND0="21575@1" Pin0InfoVect0LinkObjId="SW-113644_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2bd5380_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="410,-417 410,-383 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e8b180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="410,-347 410,-285 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="21575@0" ObjectIDZND0="21574@1" Pin0InfoVect0LinkObjId="SW-113642_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-113644_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="410,-347 410,-285 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e886b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="410,-258 410,-221 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="21574@0" ObjectIDZND0="21576@1" Pin0InfoVect0LinkObjId="SW-113645_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-113642_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="410,-258 410,-221 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f070f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1255,-767 1255,-782 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="21556@0" ObjectIDZND0="21585@0" Pin0InfoVect0LinkObjId="SW-113708_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1cfbd10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1255,-767 1255,-782 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f07350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1255,-904 1255,-916 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_2f07be0@0" ObjectIDZND0="g_2823770@0" Pin0InfoVect0LinkObjId="g_2823770_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f07be0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1255,-904 1255,-916 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16b0890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1255,-846 1291,-846 1291,-839 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="21585@x" ObjectIDND1="g_2e2a8a0@0" ObjectIDND2="g_2f07be0@0" ObjectIDZND0="21586@1" Pin0InfoVect0LinkObjId="SW-113710_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-113708_0" Pin1InfoVect1LinkObjId="g_2e2a8a0_0" Pin1InfoVect2LinkObjId="g_2f07be0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1255,-846 1291,-846 1291,-839 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16b0af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1291,-803 1291,-794 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21586@0" ObjectIDZND0="g_2f08300@0" Pin0InfoVect0LinkObjId="g_2f08300_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-113710_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1291,-803 1291,-794 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16b0d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1255,-818 1255,-846 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="21585@1" ObjectIDZND0="21586@x" ObjectIDZND1="g_2e2a8a0@0" ObjectIDZND2="g_2f07be0@0" Pin0InfoVect0LinkObjId="SW-113710_0" Pin0InfoVect1LinkObjId="g_2e2a8a0_0" Pin0InfoVect2LinkObjId="g_2f07be0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-113708_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1255,-818 1255,-846 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c9b110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="777,-767 777,-802 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="21556@0" ObjectIDZND0="21578@0" Pin0InfoVect0LinkObjId="SW-113660_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1cfbd10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="777,-767 777,-802 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c9b370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="777,-838 777,-866 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="21578@1" ObjectIDZND0="21577@0" Pin0InfoVect0LinkObjId="SW-113659_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-113660_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="777,-838 777,-866 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29cf480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="777,-893 777,-919 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="21577@1" ObjectIDZND0="21579@0" Pin0InfoVect0LinkObjId="SW-113661_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-113659_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="777,-893 777,-919 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bca650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="855,-1011 855,-995 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21580@0" ObjectIDZND0="g_2bca8b0@0" Pin0InfoVect0LinkObjId="g_2bca8b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-113662_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="855,-1011 855,-995 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_167a890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="628,-66 628,-77 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" ObjectIDND0="g_16bee10@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_29d1da0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_16bee10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="628,-66 628,-77 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_167aa80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="628,-120 628,-133 583,-133 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="0@1" ObjectIDZND0="34340@x" ObjectIDZND1="g_2fbdb00@0" ObjectIDZND2="21573@x" Pin0InfoVect0LinkObjId="EC-SB_ALB.075Ld_0" Pin0InfoVect1LinkObjId="g_2fbdb00_0" Pin0InfoVect2LinkObjId="SW-113629_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29d1da0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="628,-120 628,-133 583,-133 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16bf470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="584,-133 584,-40 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="0@x" ObjectIDND1="g_2fbdb00@0" ObjectIDND2="21573@x" ObjectIDZND0="34340@0" Pin0InfoVect0LinkObjId="EC-SB_ALB.075Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_29d1da0_0" Pin1InfoVect1LinkObjId="g_2fbdb00_0" Pin1InfoVect2LinkObjId="SW-113629_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="584,-133 584,-40 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3bfb470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="39,-590 39,-546 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="breaker" ObjectIDZND0="21591@1" Pin0InfoVect0LinkObjId="SW-187287_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="39,-590 39,-546 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3bfb660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="39,-519 39,-503 40,-506 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" ObjectIDND0="21591@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-187287_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="39,-519 39,-503 40,-506 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3bfb850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="128,-598 128,-551 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="breaker" ObjectIDZND0="21592@1" Pin0InfoVect0LinkObjId="SW-187288_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="128,-598 128,-551 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3bfba40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="128,-524 128,-509 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" ObjectIDND0="21592@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-187288_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="128,-524 128,-509 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e7b2e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1181,-416 1181,-382 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="21557@0" ObjectIDZND0="21566@1" Pin0InfoVect0LinkObjId="SW-113596_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a6eff0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1181,-416 1181,-382 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e7b540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1181,-346 1181,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="21566@0" ObjectIDZND0="21565@1" Pin0InfoVect0LinkObjId="SW-113594_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-113596_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1181,-346 1181,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e7b7a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1181,-298 1181,-273 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="21565@0" ObjectIDZND0="30390@1" Pin0InfoVect0LinkObjId="SW-113746_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-113594_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1181,-298 1181,-273 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e7ba00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1181,-237 1181,-213 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="30390@0" ObjectIDZND0="21567@1" Pin0InfoVect0LinkObjId="SW-113597_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-113746_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1181,-237 1181,-213 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e2b650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1255,-846 1216,-846 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="21586@x" ObjectIDND1="21585@x" ObjectIDND2="g_2f07be0@0" ObjectIDZND0="g_2e2a8a0@0" Pin0InfoVect0LinkObjId="g_2e2a8a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-113710_0" Pin1InfoVect1LinkObjId="SW-113708_0" Pin1InfoVect2LinkObjId="g_2f07be0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1255,-846 1216,-846 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e2b8b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1255,-871 1255,-846 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2f07be0@1" ObjectIDZND0="21586@x" ObjectIDZND1="21585@x" ObjectIDZND2="g_2e2a8a0@0" Pin0InfoVect0LinkObjId="SW-113710_0" Pin0InfoVect1LinkObjId="SW-113708_0" Pin0InfoVect2LinkObjId="g_2e2a8a0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f07be0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1255,-871 1255,-846 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15f9140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="434,-901 434,-876 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_29d1da0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29d1da0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="434,-901 434,-876 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_15f93a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="434,-831 434,-769 436,-767 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="21556@0" Pin0InfoVect0LinkObjId="g_1cfbd10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29d1da0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="434,-831 434,-769 436,-767 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2bd5120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="475,-557 475,-533 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_29d1da0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29d1da0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="475,-557 475,-533 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bd5380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="475,-488 475,-417 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="41950@0" Pin0InfoVect0LinkObjId="g_27d6fe0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29d1da0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="475,-488 475,-417 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28aa530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1484,-152 1484,-160 1522,-160 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_33a9120@0" ObjectIDZND0="21561@x" ObjectIDZND1="34337@x" Pin0InfoVect0LinkObjId="SW-113565_0" Pin0InfoVect1LinkObjId="EC-SB_ALB.071Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33a9120_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1484,-152 1484,-160 1522,-160 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28aa790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1522,-184 1522,-160 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="21561@0" ObjectIDZND0="g_33a9120@0" ObjectIDZND1="34337@x" Pin0InfoVect0LinkObjId="g_33a9120_0" Pin0InfoVect1LinkObjId="EC-SB_ALB.071Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-113565_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1522,-184 1522,-160 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28aa9f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1522,-160 1522,-38 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_33a9120@0" ObjectIDND1="21561@x" ObjectIDZND0="34337@0" Pin0InfoVect0LinkObjId="EC-SB_ALB.071Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_33a9120_0" Pin1InfoVect1LinkObjId="SW-113565_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1522,-160 1522,-38 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28aac50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1310,-154 1310,-163 1348,-163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_2f14dd0@0" ObjectIDZND0="21564@x" ObjectIDZND1="34338@x" Pin0InfoVect0LinkObjId="SW-113581_0" Pin0InfoVect1LinkObjId="EC-SB_ALB.072Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f14dd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1310,-154 1310,-163 1348,-163 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28aaeb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1348,-184 1348,-163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="21564@0" ObjectIDZND0="g_2f14dd0@0" ObjectIDZND1="34338@x" Pin0InfoVect0LinkObjId="g_2f14dd0_0" Pin0InfoVect1LinkObjId="EC-SB_ALB.072Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-113581_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1348,-184 1348,-163 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28ab110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1348,-163 1348,-38 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_2f14dd0@0" ObjectIDND1="21564@x" ObjectIDZND0="34338@0" Pin0InfoVect0LinkObjId="EC-SB_ALB.072Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2f14dd0_0" Pin1InfoVect1LinkObjId="SW-113581_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1348,-163 1348,-38 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c517f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="724,-170 724,-179 762,-179 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_2f113b0@0" ObjectIDZND0="21570@x" ObjectIDZND1="34341@x" Pin0InfoVect0LinkObjId="SW-113613_0" Pin0InfoVect1LinkObjId="EC-SB_ALB.074Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f113b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="724,-170 724,-179 762,-179 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c51a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="762,-193 762,-179 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="21570@0" ObjectIDZND0="g_2f113b0@0" ObjectIDZND1="34341@x" Pin0InfoVect0LinkObjId="g_2f113b0_0" Pin0InfoVect1LinkObjId="EC-SB_ALB.074Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-113613_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="762,-193 762,-179 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c51cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="762,-179 762,-47 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_2f113b0@0" ObjectIDND1="21570@x" ObjectIDZND0="34341@0" Pin0InfoVect0LinkObjId="EC-SB_ALB.074Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2f113b0_0" Pin1InfoVect1LinkObjId="SW-113613_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="762,-179 762,-47 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c51f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="546,-156 546,-165 584,-165 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" EndDevType2="switch" ObjectIDND0="g_2fbdb00@0" ObjectIDZND0="21573@x" ObjectIDZND1="34340@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-113629_0" Pin0InfoVect1LinkObjId="EC-SB_ALB.075Ld_0" Pin0InfoVect2LinkObjId="g_29d1da0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2fbdb00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="546,-156 546,-165 584,-165 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c52170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="584,-186 584,-165 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" EndDevType2="switch" ObjectIDND0="21573@0" ObjectIDZND0="g_2fbdb00@0" ObjectIDZND1="34340@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_2fbdb00_0" Pin0InfoVect1LinkObjId="EC-SB_ALB.075Ld_0" Pin0InfoVect2LinkObjId="g_29d1da0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-113629_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="584,-186 584,-165 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c523d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="584,-165 584,-133 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_2fbdb00@0" ObjectIDND1="21573@x" ObjectIDZND0="34340@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="EC-SB_ALB.075Ld_0" Pin0InfoVect1LinkObjId="g_29d1da0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2fbdb00_0" Pin1InfoVect1LinkObjId="SW-113629_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="584,-165 584,-133 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c52630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="372,-155 372,-166 410,-166 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_1e8a1b0@0" ObjectIDZND0="21576@x" ObjectIDZND1="34339@x" Pin0InfoVect0LinkObjId="SW-113645_0" Pin0InfoVect1LinkObjId="EC-SB_ALB.076Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e8a1b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="372,-155 372,-166 410,-166 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c52890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="410,-185 410,-166 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="21576@0" ObjectIDZND0="g_1e8a1b0@0" ObjectIDZND1="34339@x" Pin0InfoVect0LinkObjId="g_1e8a1b0_0" Pin0InfoVect1LinkObjId="EC-SB_ALB.076Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-113645_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="410,-185 410,-166 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c52af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="410,-166 410,-39 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_1e8a1b0@0" ObjectIDND1="21576@x" ObjectIDZND0="34339@0" Pin0InfoVect0LinkObjId="EC-SB_ALB.076Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1e8a1b0_0" Pin1InfoVect1LinkObjId="SW-113645_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="410,-166 410,-39 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1649180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1140,-175 1140,-165 1181,-165 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="capacitor" ObjectIDND0="30391@0" ObjectIDZND0="21567@x" ObjectIDZND1="40974@x" Pin0InfoVect0LinkObjId="SW-113597_0" Pin0InfoVect1LinkObjId="CB-SB_ALB.SB_ALB_Cb1_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-113606_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1140,-175 1140,-165 1181,-165 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16493e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1181,-177 1181,-165 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="capacitor" ObjectIDND0="21567@0" ObjectIDZND0="30391@x" ObjectIDZND1="40974@x" Pin0InfoVect0LinkObjId="SW-113606_0" Pin0InfoVect1LinkObjId="CB-SB_ALB.SB_ALB_Cb1_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-113597_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1181,-177 1181,-165 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1649640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1181,-165 1181,-150 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="capacitor" ObjectIDND0="30391@x" ObjectIDND1="21567@x" ObjectIDZND0="40974@0" Pin0InfoVect0LinkObjId="CB-SB_ALB.SB_ALB_Cb1_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-113606_0" Pin1InfoVect1LinkObjId="SW-113597_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1181,-165 1181,-150 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16498a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1140,-211 1140,-226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="30391@1" ObjectIDZND0="g_16486f0@0" Pin0InfoVect0LinkObjId="g_16486f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-113606_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1140,-211 1140,-226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2dbc780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="689,-1055 777,-1055 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_29d1da0@0" ObjectIDZND0="21579@x" ObjectIDZND1="g_2e29d20@0" ObjectIDZND2="21580@x" Pin0InfoVect0LinkObjId="SW-113661_0" Pin0InfoVect1LinkObjId="g_2e29d20_0" Pin0InfoVect2LinkObjId="SW-113662_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29d1da0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="689,-1055 777,-1055 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2dbd0b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="777,-955 777,-1055 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="21579@1" ObjectIDZND0="g_29d1da0@0" ObjectIDZND1="g_2e29d20@0" ObjectIDZND2="21580@x" Pin0InfoVect0LinkObjId="g_29d1da0_0" Pin0InfoVect1LinkObjId="g_2e29d20_0" Pin0InfoVect2LinkObjId="SW-113662_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-113661_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="777,-955 777,-1055 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2dbd2e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="777,-1065 816,-1065 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" BeginDevType2="powerLine" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_29d1da0@0" ObjectIDND1="21579@x" ObjectIDND2="37760@1" ObjectIDZND0="g_2e29d20@0" ObjectIDZND1="21580@x" Pin0InfoVect0LinkObjId="g_2e29d20_0" Pin0InfoVect1LinkObjId="SW-113662_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_29d1da0_0" Pin1InfoVect1LinkObjId="SW-113661_0" Pin1InfoVect2LinkObjId="g_2dbf330_1" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="777,-1065 816,-1065 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2dbe380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="816,-1074 816,-1065 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="switch" ObjectIDND0="g_2e29d20@0" ObjectIDZND0="21580@x" ObjectIDZND1="g_29d1da0@0" ObjectIDZND2="21579@x" Pin0InfoVect0LinkObjId="SW-113662_0" Pin0InfoVect1LinkObjId="g_29d1da0_0" Pin0InfoVect2LinkObjId="SW-113661_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e29d20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="816,-1074 816,-1065 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2dbe5e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="816,-1065 855,-1065 855,-1047 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2e29d20@0" ObjectIDND1="g_29d1da0@0" ObjectIDND2="21579@x" ObjectIDZND0="21580@1" Pin0InfoVect0LinkObjId="SW-113662_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2e29d20_0" Pin1InfoVect1LinkObjId="g_29d1da0_0" Pin1InfoVect2LinkObjId="SW-113661_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="816,-1065 855,-1065 855,-1047 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2dbf0d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="777,-1055 777,-1065 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="powerLine" ObjectIDND0="g_29d1da0@0" ObjectIDND1="21579@x" ObjectIDZND0="g_2e29d20@0" ObjectIDZND1="21580@x" ObjectIDZND2="37760@1" Pin0InfoVect0LinkObjId="g_2e29d20_0" Pin0InfoVect1LinkObjId="SW-113662_0" Pin0InfoVect2LinkObjId="g_2dbf330_1" Pin0Num="2" Pin1InfoVect0LinkObjId="g_29d1da0_0" Pin1InfoVect1LinkObjId="SW-113661_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="777,-1055 777,-1065 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2dbf330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="777,-1065 777,-1088 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="voltageTransformer" EndDevType0="powerLine" ObjectIDND0="g_2e29d20@0" ObjectIDND1="21580@x" ObjectIDND2="g_29d1da0@0" ObjectIDZND0="37760@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2e29d20_0" Pin1InfoVect1LinkObjId="SW-113662_0" Pin1InfoVect2LinkObjId="g_29d1da0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="777,-1065 777,-1088 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26efaf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1618,-491 1656,-491 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_224a680@0" ObjectIDZND0="21587@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-113724_0" Pin0InfoVect1LinkObjId="g_29d1da0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_224a680_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1618,-491 1656,-491 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26efce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1656,-475 1656,-491 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="21587@1" ObjectIDZND0="g_224a680@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_224a680_0" Pin0InfoVect1LinkObjId="g_29d1da0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-113724_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1656,-475 1656,-491 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26efed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1656,-491 1656,-518 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_224a680@0" ObjectIDND1="21587@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_29d1da0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_224a680_0" Pin1InfoVect1LinkObjId="SW-113724_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1656,-491 1656,-518 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_171c9b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1398,-653 1398,-677 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="41948@1" ObjectIDZND0="41938@0" Pin0InfoVect0LinkObjId="SW-251781_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_171cc10_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1398,-653 1398,-677 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_171cc10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1398,-534 1398,-566 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="41941@1" ObjectIDZND0="41948@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-251795_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1398,-534 1398,-566 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_171ce70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1398,-481 1398,-505 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="41942@1" ObjectIDZND0="41941@0" Pin0InfoVect0LinkObjId="SW-251795_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-251796_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1398,-481 1398,-505 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_171f3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1398,-713 1433,-713 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="41938@x" ObjectIDND1="41939@x" ObjectIDZND0="41940@1" Pin0InfoVect0LinkObjId="SW-251783_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-251781_0" Pin1InfoVect1LinkObjId="SW-251782_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1398,-713 1433,-713 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2db4d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1398,-704 1398,-713 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="41938@1" ObjectIDZND0="41940@x" ObjectIDZND1="41939@x" Pin0InfoVect0LinkObjId="SW-251783_0" Pin0InfoVect1LinkObjId="SW-251782_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-251781_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1398,-704 1398,-713 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2db4f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1398,-713 1398,-717 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="41939@0" ObjectIDZND0="41938@x" ObjectIDZND1="41940@x" Pin0InfoVect0LinkObjId="SW-251781_0" Pin0InfoVect1LinkObjId="SW-251783_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-251782_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1398,-713 1398,-717 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2db76b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1469,-713 1480,-713 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="41940@0" ObjectIDZND0="g_2db7910@0" Pin0InfoVect0LinkObjId="g_2db7910_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-251783_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1469,-713 1480,-713 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a6eff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1074,-364 1139,-364 1139,-416 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="41944@0" ObjectIDZND0="21557@0" Pin0InfoVect0LinkObjId="g_27d61e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-251856_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1074,-364 1139,-364 1139,-416 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16d0100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1685,-416 1685,-384 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="21557@0" ObjectIDZND0="41946@1" Pin0InfoVect0LinkObjId="SW-251876_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a6eff0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1685,-416 1685,-384 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16d0360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1685,-348 1685,-286 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="41946@0" ObjectIDZND0="41945@1" Pin0InfoVect0LinkObjId="SW-251873_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-251876_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1685,-348 1685,-286 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16d05c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1685,-259 1685,-222 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="41945@0" ObjectIDZND0="41947@1" Pin0InfoVect0LinkObjId="SW-251877_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-251873_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1685,-259 1685,-222 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2eca030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1649,-163 1649,-172 1685,-172 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_16cf390@0" ObjectIDZND0="41947@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-251877_0" Pin0InfoVect1LinkObjId="g_29d1da0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_16cf390_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1649,-163 1649,-172 1685,-172 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2eca220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1685,-186 1685,-172 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="41947@0" ObjectIDZND0="g_16cf390@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_16cf390_0" Pin0InfoVect1LinkObjId="g_29d1da0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-251877_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1685,-186 1685,-172 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2eca410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1685,-172 1685,-40 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_16cf390@0" ObjectIDND1="41947@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_29d1da0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_16cf390_0" Pin1InfoVect1LinkObjId="SW-251877_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1685,-172 1685,-40 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ecb250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="359,-417 359,-445 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="41950@0" ObjectIDZND0="41943@0" Pin0InfoVect0LinkObjId="SW-251855_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2bd5380_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="359,-417 359,-445 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ec7800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="321,-498 359,-498 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="voltageTransformer" ObjectIDND0="g_2eca600@0" ObjectIDZND0="41943@x" ObjectIDZND1="g_2ecb9a0@0" Pin0InfoVect0LinkObjId="SW-251855_0" Pin0InfoVect1LinkObjId="g_2ecb9a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2eca600_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="321,-498 359,-498 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ec7a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="359,-482 359,-498 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" ObjectIDND0="41943@1" ObjectIDZND0="g_2eca600@0" ObjectIDZND1="g_2ecb9a0@0" Pin0InfoVect0LinkObjId="g_2eca600_0" Pin0InfoVect1LinkObjId="g_2ecb9a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-251855_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="359,-482 359,-498 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27d61e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1398,-445 1398,-416 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="41942@0" ObjectIDZND0="21557@0" Pin0InfoVect0LinkObjId="g_2a6eff0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-251796_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1398,-445 1398,-416 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27d6fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1038,-364 961,-364 961,-417 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="41944@1" ObjectIDZND0="41950@0" Pin0InfoVect0LinkObjId="g_2bd5380_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-251856_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1038,-364 961,-364 961,-417 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c39010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="359,-498 359,-595 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="voltageTransformer" ObjectIDND0="g_2eca600@0" ObjectIDND1="41943@x" ObjectIDZND0="g_2ecb9a0@0" Pin0InfoVect0LinkObjId="g_2ecb9a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2eca600_0" Pin1InfoVect1LinkObjId="SW-251855_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="359,-498 359,-595 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c3a0a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1398,-753 1398,-767 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="41939@1" ObjectIDZND0="21556@0" Pin0InfoVect0LinkObjId="g_1cfbd10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-251782_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1398,-753 1398,-767 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="21556" cx="1255" cy="-767" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21556" cx="779" cy="-767" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21556" cx="436" cy="-767" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21556" cx="777" cy="-767" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="41950" cx="777" cy="-417" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="41950" cx="762" cy="-417" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="41950" cx="584" cy="-417" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="41950" cx="410" cy="-417" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="41950" cx="475" cy="-417" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="41950" cx="359" cy="-417" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21557" cx="1522" cy="-416" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21557" cx="1348" cy="-416" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21557" cx="1181" cy="-416" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21557" cx="1139" cy="-416" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21557" cx="1685" cy="-416" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21557" cx="1398" cy="-416" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21557" cx="1656" cy="-416" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="41950" cx="961" cy="-417" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21556" cx="1398" cy="-767" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-111360" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 28.000000 -971.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21418" ObjectName="DYN-SB_ALB"/>
     <cge:Meas_Ref ObjectId="111360"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_1594390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1324.666667 17.000000) translate(0,18)">青香树线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_294fe90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1497.000000 17.000000) translate(0,18)">六街线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1cf16d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -908.000000) translate(0,17)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1cf16d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -908.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1cf16d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -908.000000) translate(0,59)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1cf16d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -908.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1cf16d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -908.000000) translate(0,101)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1cf16d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -908.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1cf16d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -908.000000) translate(0,143)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1cf16d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -908.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1cf16d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -908.000000) translate(0,185)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_254d0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_254d0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_254d0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_254d0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_254d0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_254d0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_254d0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_254d0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_254d0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_254d0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_254d0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_254d0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_254d0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_254d0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_254d0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_254d0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_254d0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_254d0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_1ef39c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -114.000000 -1049.500000) translate(0,16)">安龙堡变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2bf7d80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 277.000000 -755.000000) translate(0,15)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1cfbad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 257.000000 -406.000000) translate(0,15)">IM段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_33657e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 298.000000 -681.000000) translate(0,15)">10kVⅠ母TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_16c5220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1153.000000 17.000000) translate(0,18)">1号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_2f09250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 737.000000 10.000000) translate(0,18)">塔埔线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_1eab960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 559.000000 16.000000) translate(0,18)">炉房线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_2fbfac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 385.000000 14.000000) translate(0,18)">新街线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_2e894c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 611.000000 -30.000000) translate(0,18)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2f075b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1229.000000 -993.000000) translate(0,15)">35kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2bcb340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 666.000000 -981.000000) translate(0,15)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_2bcb830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 715.000000 -1172.000000) translate(0,18)">35kV大麦T安线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_167c120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -128.000000 -111.000000) translate(0,17)">7817268</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21db6f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 786.000000 -887.000000) translate(0,12)">371</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21db970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 784.000000 -827.000000) translate(0,12)">3711</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21dbbb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 784.000000 -944.000000) translate(0,12)">3716</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21dbdf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 862.000000 -1036.000000) translate(0,12)">37167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21dc030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1205.000000 -812.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21dc270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1298.000000 -828.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21dc4b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 786.000000 -694.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21dc6f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 784.000000 -743.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21dc930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 786.000000 -522.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21dcb70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 784.000000 -466.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21dcdb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1663.000000 -464.000000) translate(0,12)">0902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21dcff0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1531.000000 -278.000000) translate(0,12)">071</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21dd230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1529.000000 -371.000000) translate(0,12)">0712</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32c4cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1529.000000 -209.000000) translate(0,12)">0716</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32c4f10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1357.000000 -278.000000) translate(0,12)">072</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32c5150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1355.000000 -371.000000) translate(0,12)">0722</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32c5390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1355.000000 -209.000000) translate(0,12)">0726</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32c55d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1199.000000 -321.000000) translate(0,12)">073</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32c5810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1185.000000 -371.000000) translate(0,12)">0732</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32c5a50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1185.000000 -262.000000) translate(0,12)">0733</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32c5c90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 771.000000 -287.000000) translate(0,12)">074</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32c5ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 769.000000 -380.000000) translate(0,12)">0741</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32c6110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 769.000000 -218.000000) translate(0,12)">0746</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32c6350" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 591.000000 -373.000000) translate(0,12)">0751</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32c6590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 591.000000 -211.000000) translate(0,12)">0756</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32c67d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 419.000000 -279.000000) translate(0,12)">076</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32c6a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 417.000000 -372.000000) translate(0,12)">0761</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32c6c50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 417.000000 -210.000000) translate(0,12)">0766</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32c6e90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 824.000000 -616.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32c70d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 390.000000 -1040.000000) translate(0,12)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32c70d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 390.000000 -1040.000000) translate(0,27)">50kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32c7310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 427.000000 -679.000000) translate(0,12)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32c7310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 427.000000 -679.000000) translate(0,27)">50kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16bebd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 593.000000 -280.000000) translate(0,12)">075</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16c1470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 103.000000 -491.000000) translate(0,12)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16c1470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 103.000000 -491.000000) translate(0,27)">进线开关</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3bfb010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 7.000000 -492.000000) translate(0,12)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3bfb010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 7.000000 -492.000000) translate(0,27)">进线开关</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e7bc60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1203.000000 -201.000000) translate(0,12)">0736</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e7c150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 626.000000 -637.000000) translate(0,15)">SZ11-2000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e7c150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 626.000000 -637.000000) translate(0,33)">35/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e7c150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 626.000000 -637.000000) translate(0,51)">Yd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e7c150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 626.000000 -637.000000) translate(0,69)">Ud%=6.91%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_1e7c700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 838.000000 -852.000000) translate(0,14)">TA变比 600/5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_3442250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1469.000000 106.000000) translate(0,14)">TA变比 300/5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_34424d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1312.000000 106.000000) translate(0,14)">TA变比 300/5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_3442710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 713.000000 99.000000) translate(0,14)">TA变比 300/5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_3442950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 539.000000 105.000000) translate(0,14)">TA变比 300/5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_3442b90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 366.000000 103.000000) translate(0,14)">TA变比 300/5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_3442dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 667.000000 -518.000000) translate(0,14)">TA变比400/5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_3443340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 669.000000 -695.000000) translate(0,14)">TA变比 75/5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_3443780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 85.000000 -1061.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_3443c20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 84.000000 -1020.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" graphid="g_2e299a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -242.000000 -688.000000) translate(0,16)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_164a4b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1062.000000 -195.000000) translate(0,12)">07367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2e49c90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -259.000000 -71.000000) translate(0,17)">楚雄巡维中</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2e49c90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -259.000000 -71.000000) translate(0,38)">心变运三班：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2e4a240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -129.000000 -81.500000) translate(0,17)">18787878955</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2e4a240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -129.000000 -81.500000) translate(0,38)">18787878953</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2e4a240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -129.000000 -81.500000) translate(0,59)">18787878979</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_26f01f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 105.500000 -956.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_171d0d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1407.000000 -698.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_171d5c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1405.000000 -742.000000) translate(0,12)">3021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_171d800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1407.000000 -526.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_171da40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1405.000000 -470.000000) translate(0,12)">0022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_171dc80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1445.000000 -620.000000) translate(0,12)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25dc500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1440.000000 -734.000000) translate(0,12)">30217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_2a6f250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1661.000000 17.000000) translate(0,18)">备用线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ec96c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1695.000000 -280.000000) translate(0,12)">077</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ec9bb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1693.000000 -373.000000) translate(0,12)">0772</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ec9df0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1693.000000 -211.000000) translate(0,12)">0776</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ecb4b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1598.000000 -663.000000) translate(0,15)">10kVⅡ母TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ec7cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1039.000000 -393.000000) translate(0,12)">0012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ec81b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1723.000000 -412.000000) translate(0,12)">IIM段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2eccef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1246.000000 -630.000000) translate(0,15)">SZ11-5000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2eccef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1246.000000 -630.000000) translate(0,33)">35/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2eccef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1246.000000 -630.000000) translate(0,51)">Yd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2eccef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1246.000000 -630.000000) translate(0,69)">Ud%=7%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ebb120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 370.000000 -469.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_1c39640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1278.000000 -702.000000) translate(0,14)">TA变比 400/5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_1c399e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1281.000000 -526.000000) translate(0,14)">TA变比 600/5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_1c39c20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1134.000000 102.000000) translate(0,14)">TA变比 100/5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_1c39e60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1637.000000 104.000000) translate(0,14)">TA变比 300/5</text>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" fillStyle="1" height="42" stroke="rgb(255,255,255)" stroke-width="1" width="72" x="89" y="-967"/>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-SB_ALB.SB_ALB_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="287,-767 1637,-767 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="21556" ObjectName="BS-SB_ALB.SB_ALB_3IM"/>
    <cge:TPSR_Ref TObjectID="21556"/></metadata>
   <polyline fill="none" opacity="0" points="287,-767 1637,-767 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-SB_ALB.SB_ALB_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="288,-417 1000,-417 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="41950" ObjectName="BS-SB_ALB.SB_ALB_9IM"/>
    <cge:TPSR_Ref TObjectID="41950"/></metadata>
   <polyline fill="none" opacity="0" points="288,-417 1000,-417 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-SB_ALB.SB_ALB_9IIM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1102,-416 1742,-416 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="21557" ObjectName="BS-SB_ALB.SB_ALB_9IIM"/>
    <cge:TPSR_Ref TObjectID="21557"/></metadata>
   <polyline fill="none" opacity="0" points="1102,-416 1742,-416 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-SB_ALB.SB_ALB_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="30186"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 739.000000 -557.000000)" xlink:href="#transformer2:shape39_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 739.000000 -557.000000)" xlink:href="#transformer2:shape39_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="21588" ObjectName="TF-SB_ALB.SB_ALB_1T"/>
    <cge:TPSR_Ref TObjectID="21588"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 402.000000 -896.000000)" xlink:href="#transformer2:shape55_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 402.000000 -896.000000)" xlink:href="#transformer2:shape55_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 459.000000 -552.000000)" xlink:href="#transformer2:shape41_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 459.000000 -552.000000)" xlink:href="#transformer2:shape41_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-SB_ALB.SB_ALB_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="17135"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1360.000000 -561.000000)" xlink:href="#transformer2:shape39_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1360.000000 -561.000000)" xlink:href="#transformer2:shape39_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="41948" ObjectName="TF-SB_ALB.SB_ALB_2T"/>
    <cge:TPSR_Ref TObjectID="41948"/></metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -169.000000 -987.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-255149" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -138.000000 -823.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="255149" ObjectName="SB_ALB:SB_ALB_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-255150" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -138.000000 -780.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="255150" ObjectName="SB_ALB:SB_ALB_sumQ"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-255149" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -140.000000 -906.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="255149" ObjectName="SB_ALB:SB_ALB_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-255149" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -138.000000 -865.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="255149" ObjectName="SB_ALB:SB_ALB_sumP"/>
    </metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="141" x="-151" y="-1060"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="141" x="-151" y="-1060"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-198" y="-1077"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-198" y="-1077"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="74" y="-1069"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="74" y="-1069"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="76" y="-1027"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="76" y="-1027"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="786" y="-887"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="786" y="-887"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="52" x="824" y="-616"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="52" x="824" y="-616"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="1531" y="-278"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="1531" y="-278"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="1357" y="-278"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="1357" y="-278"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1199" y="-321"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1199" y="-321"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="771" y="-287"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="771" y="-287"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="593" y="-280"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="593" y="-280"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="419" y="-279"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="419" y="-279"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="19" qtmmishow="hidden" width="76" x="-242" y="-688"/>
    </a>
   <metadata/><rect fill="white" height="19" opacity="0" stroke="white" transform="" width="76" x="-242" y="-688"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="44" qtmmishow="hidden" width="73" x="88" y="-968"/>
    </a>
   <metadata/><rect fill="white" height="44" opacity="0" stroke="white" transform="" width="73" x="88" y="-968"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="53" x="1445" y="-620"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="53" x="1445" y="-620"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1695" y="-280"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1695" y="-280"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32c7640" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 829.000000 578.000000) translate(0,12)">档位(档):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d1b4b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 829.000000 560.000000) translate(0,12)">油温(℃):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d1b7e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 848.000000 904.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d1ba40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 837.000000 889.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d1bc80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 862.000000 874.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bf6780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 829.000000 699.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bf69f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 818.000000 684.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bf6c00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 843.000000 669.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bf6f30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 840.000000 528.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bf7190" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 829.000000 513.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bf73d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 854.000000 498.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bf7700" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1481.000000 -55.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bf7960" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1470.000000 -70.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16be990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1495.000000 -85.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_344dcf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 260.000000 859.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_344e1e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 260.000000 845.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_344e420" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 260.000000 831.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_344e660" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 252.000000 798.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_344e8a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 264.000000 813.000000) translate(0,12)">U0(V):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_344ebd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1756.000000 503.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_344ee40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1756.000000 489.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_344f080" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1756.000000 475.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_344f2c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1748.000000 442.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_344f500" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1760.000000 457.000000) translate(0,12)">U0(V):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e47e40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1300.000000 -54.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e480a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1289.000000 -69.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e482e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1314.000000 -84.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e48610" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 711.000000 -46.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e48870" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 700.000000 -61.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e48ab0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 725.000000 -76.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e48de0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 533.000000 -53.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e49040" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 522.000000 -68.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e49280" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 547.000000 -83.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e495b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 365.000000 -52.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e49810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 354.000000 -67.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e49a50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 379.000000 -82.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26ef630" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1123.000000 -68.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26ef8e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1148.000000 -83.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_171dfb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1450.000000 582.000000) translate(0,12)">档位(档):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_171e210" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1450.000000 564.000000) translate(0,12)">油温(℃):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_171e540" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1450.000000 703.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_171e7a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1439.000000 688.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_171e9e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1464.000000 673.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_171ed10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1461.000000 532.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_171ef70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1450.000000 517.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_171f1b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1475.000000 502.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ecc730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1637.000000 -52.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ecc930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1626.000000 -67.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eccb70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1651.000000 -82.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c37700" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 182.000000 495.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c37bb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 182.000000 481.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c37df0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 182.000000 467.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c38030" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 174.000000 434.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c38270" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 186.000000 449.000000) translate(0,12)">U0(V):</text>
   <metadata/></g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-SB_ALB.SB_ALB_Cb1">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1156.000000 -41.000000)" xlink:href="#capacitor:shape41"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40974" ObjectName="CB-SB_ALB.SB_ALB_Cb1"/>
    <cge:TPSR_Ref TObjectID="40974"/></metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-113518" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 904.000000 -904.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113518" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21577"/>
     <cge:Term_Ref ObjectID="30162"/>
    <cge:TPSR_Ref TObjectID="21577"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-113519" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 904.000000 -904.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113519" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21577"/>
     <cge:Term_Ref ObjectID="30162"/>
    <cge:TPSR_Ref TObjectID="21577"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-113516" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 904.000000 -904.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113516" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21577"/>
     <cge:Term_Ref ObjectID="30162"/>
    <cge:TPSR_Ref TObjectID="21577"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-113524" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 886.000000 -702.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113524" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21581"/>
     <cge:Term_Ref ObjectID="30170"/>
    <cge:TPSR_Ref TObjectID="21581"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-113525" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 886.000000 -702.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113525" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21581"/>
     <cge:Term_Ref ObjectID="30170"/>
    <cge:TPSR_Ref TObjectID="21581"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-113521" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 886.000000 -702.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113521" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21581"/>
     <cge:Term_Ref ObjectID="30170"/>
    <cge:TPSR_Ref TObjectID="21581"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-113530" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 899.000000 -529.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113530" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21583"/>
     <cge:Term_Ref ObjectID="30174"/>
    <cge:TPSR_Ref TObjectID="21583"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-113531" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 899.000000 -529.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113531" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21583"/>
     <cge:Term_Ref ObjectID="30174"/>
    <cge:TPSR_Ref TObjectID="21583"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-113527" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 899.000000 -529.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113527" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21583"/>
     <cge:Term_Ref ObjectID="30174"/>
    <cge:TPSR_Ref TObjectID="21583"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-113488" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1537.000000 55.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113488" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21559"/>
     <cge:Term_Ref ObjectID="28723"/>
    <cge:TPSR_Ref TObjectID="21559"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-113489" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1537.000000 55.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113489" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21559"/>
     <cge:Term_Ref ObjectID="28723"/>
    <cge:TPSR_Ref TObjectID="21559"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-113486" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1537.000000 55.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113486" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21559"/>
     <cge:Term_Ref ObjectID="28723"/>
    <cge:TPSR_Ref TObjectID="21559"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-113493" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 54.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113493" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21562"/>
     <cge:Term_Ref ObjectID="30132"/>
    <cge:TPSR_Ref TObjectID="21562"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-113494" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 54.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113494" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21562"/>
     <cge:Term_Ref ObjectID="30132"/>
    <cge:TPSR_Ref TObjectID="21562"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-113491" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 54.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113491" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21562"/>
     <cge:Term_Ref ObjectID="30132"/>
    <cge:TPSR_Ref TObjectID="21562"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-113499" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1192.000000 70.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113499" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21565"/>
     <cge:Term_Ref ObjectID="30138"/>
    <cge:TPSR_Ref TObjectID="21565"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-113496" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1192.000000 70.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113496" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21565"/>
     <cge:Term_Ref ObjectID="30138"/>
    <cge:TPSR_Ref TObjectID="21565"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-113503" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 771.000000 46.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113503" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21568"/>
     <cge:Term_Ref ObjectID="30144"/>
    <cge:TPSR_Ref TObjectID="21568"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-113504" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 771.000000 46.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113504" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21568"/>
     <cge:Term_Ref ObjectID="30144"/>
    <cge:TPSR_Ref TObjectID="21568"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-113501" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 771.000000 46.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113501" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21568"/>
     <cge:Term_Ref ObjectID="30144"/>
    <cge:TPSR_Ref TObjectID="21568"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-113508" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 593.000000 53.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113508" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21571"/>
     <cge:Term_Ref ObjectID="30150"/>
    <cge:TPSR_Ref TObjectID="21571"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-113509" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 593.000000 53.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113509" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21571"/>
     <cge:Term_Ref ObjectID="30150"/>
    <cge:TPSR_Ref TObjectID="21571"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-113506" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 593.000000 53.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113506" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21571"/>
     <cge:Term_Ref ObjectID="30150"/>
    <cge:TPSR_Ref TObjectID="21571"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-113513" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 424.000000 51.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113513" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21574"/>
     <cge:Term_Ref ObjectID="30156"/>
    <cge:TPSR_Ref TObjectID="21574"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-113514" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 424.000000 51.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113514" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21574"/>
     <cge:Term_Ref ObjectID="30156"/>
    <cge:TPSR_Ref TObjectID="21574"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-113511" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 424.000000 51.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113511" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21574"/>
     <cge:Term_Ref ObjectID="30156"/>
    <cge:TPSR_Ref TObjectID="21574"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-113551" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 318.000000 -858.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113551" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21556"/>
     <cge:Term_Ref ObjectID="28715"/>
    <cge:TPSR_Ref TObjectID="21556"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-113552" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 318.000000 -858.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113552" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21556"/>
     <cge:Term_Ref ObjectID="28715"/>
    <cge:TPSR_Ref TObjectID="21556"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-113553" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 318.000000 -858.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113553" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21556"/>
     <cge:Term_Ref ObjectID="28715"/>
    <cge:TPSR_Ref TObjectID="21556"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-186812" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 318.000000 -858.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186812" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21556"/>
     <cge:Term_Ref ObjectID="28715"/>
    <cge:TPSR_Ref TObjectID="21556"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-113557" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 318.000000 -858.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113557" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21556"/>
     <cge:Term_Ref ObjectID="28715"/>
    <cge:TPSR_Ref TObjectID="21556"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="2" id="ME-113560" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 895.000000 -575.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113560" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21588"/>
     <cge:Term_Ref ObjectID="30184"/>
    <cge:TPSR_Ref TObjectID="21588"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-113790" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 895.000000 -575.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113790" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21588"/>
     <cge:Term_Ref ObjectID="30184"/>
    <cge:TPSR_Ref TObjectID="21588"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-251909" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1501.000000 -703.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="251909" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41938"/>
     <cge:Term_Ref ObjectID="17113"/>
    <cge:TPSR_Ref TObjectID="41938"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-251910" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1501.000000 -703.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="251910" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41938"/>
     <cge:Term_Ref ObjectID="17113"/>
    <cge:TPSR_Ref TObjectID="41938"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-251911" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1501.000000 -703.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="251911" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41938"/>
     <cge:Term_Ref ObjectID="17113"/>
    <cge:TPSR_Ref TObjectID="41938"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-251918" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1514.000000 -531.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="251918" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41941"/>
     <cge:Term_Ref ObjectID="17119"/>
    <cge:TPSR_Ref TObjectID="41941"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-251919" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1514.000000 -531.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="251919" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41941"/>
     <cge:Term_Ref ObjectID="17119"/>
    <cge:TPSR_Ref TObjectID="41941"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-251920" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1514.000000 -531.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="251920" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41941"/>
     <cge:Term_Ref ObjectID="17119"/>
    <cge:TPSR_Ref TObjectID="41941"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="2" id="ME-251928" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1520.000000 -581.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="251928" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41948"/>
     <cge:Term_Ref ObjectID="17136"/>
    <cge:TPSR_Ref TObjectID="41948"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-251929" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1520.000000 -581.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="251929" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41948"/>
     <cge:Term_Ref ObjectID="17136"/>
    <cge:TPSR_Ref TObjectID="41948"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-251938" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1690.000000 54.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="251938" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41945"/>
     <cge:Term_Ref ObjectID="17127"/>
    <cge:TPSR_Ref TObjectID="41945"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-251939" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1690.000000 54.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="251939" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41945"/>
     <cge:Term_Ref ObjectID="17127"/>
    <cge:TPSR_Ref TObjectID="41945"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-251945" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1690.000000 54.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="251945" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41945"/>
     <cge:Term_Ref ObjectID="17127"/>
    <cge:TPSR_Ref TObjectID="41945"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-113554" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1808.000000 -501.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113554" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21557"/>
     <cge:Term_Ref ObjectID="28716"/>
    <cge:TPSR_Ref TObjectID="21557"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-113555" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1808.000000 -501.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113555" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21557"/>
     <cge:Term_Ref ObjectID="28716"/>
    <cge:TPSR_Ref TObjectID="21557"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-113556" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1808.000000 -501.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113556" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21557"/>
     <cge:Term_Ref ObjectID="28716"/>
    <cge:TPSR_Ref TObjectID="21557"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-186815" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1808.000000 -501.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186815" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21557"/>
     <cge:Term_Ref ObjectID="28716"/>
    <cge:TPSR_Ref TObjectID="21557"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-113558" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1808.000000 -501.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113558" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21557"/>
     <cge:Term_Ref ObjectID="28716"/>
    <cge:TPSR_Ref TObjectID="21557"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-251930" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 232.000000 -494.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="251930" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41950"/>
     <cge:Term_Ref ObjectID="17137"/>
    <cge:TPSR_Ref TObjectID="41950"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-251931" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 232.000000 -494.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="251931" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41950"/>
     <cge:Term_Ref ObjectID="17137"/>
    <cge:TPSR_Ref TObjectID="41950"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-251932" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 232.000000 -494.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="251932" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41950"/>
     <cge:Term_Ref ObjectID="17137"/>
    <cge:TPSR_Ref TObjectID="41950"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-251936" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 232.000000 -494.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="251936" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41950"/>
     <cge:Term_Ref ObjectID="17137"/>
    <cge:TPSR_Ref TObjectID="41950"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-251933" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 232.000000 -494.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="251933" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41950"/>
     <cge:Term_Ref ObjectID="17137"/>
    <cge:TPSR_Ref TObjectID="41950"/></metadata>
   </g>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-113677">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 768.000000 -713.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21582" ObjectName="SW-SB_ALB.SB_ALB_3011SW"/>
     <cge:Meas_Ref ObjectId="113677"/>
    <cge:TPSR_Ref TObjectID="21582"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-113683">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 768.000000 -436.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21584" ObjectName="SW-SB_ALB.SB_ALB_0011SW"/>
     <cge:Meas_Ref ObjectId="113683"/>
    <cge:TPSR_Ref TObjectID="21584"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-113724">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1647.000000 -434.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21587" ObjectName="SW-SB_ALB.SB_ALB_0902SW"/>
     <cge:Meas_Ref ObjectId="113724"/>
    <cge:TPSR_Ref TObjectID="21587"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-113564">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1513.000000 -341.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21560" ObjectName="SW-SB_ALB.SB_ALB_0712SW"/>
     <cge:Meas_Ref ObjectId="113564"/>
    <cge:TPSR_Ref TObjectID="21560"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-113565">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1513.000000 -179.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21561" ObjectName="SW-SB_ALB.SB_ALB_0716SW"/>
     <cge:Meas_Ref ObjectId="113565"/>
    <cge:TPSR_Ref TObjectID="21561"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-113580">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1339.000000 -341.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21563" ObjectName="SW-SB_ALB.SB_ALB_0722SW"/>
     <cge:Meas_Ref ObjectId="113580"/>
    <cge:TPSR_Ref TObjectID="21563"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-113581">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1339.000000 -179.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21564" ObjectName="SW-SB_ALB.SB_ALB_0726SW"/>
     <cge:Meas_Ref ObjectId="113581"/>
    <cge:TPSR_Ref TObjectID="21564"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-113596">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1172.000000 -341.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21566" ObjectName="SW-SB_ALB.SB_ALB_0732SW"/>
     <cge:Meas_Ref ObjectId="113596"/>
    <cge:TPSR_Ref TObjectID="21566"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-113746">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1172.000000 -232.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30390" ObjectName="SW-SB_ALB.SB_ALB_0733SW"/>
     <cge:Meas_Ref ObjectId="113746"/>
    <cge:TPSR_Ref TObjectID="30390"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-113612">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 753.000000 -349.847826)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21569" ObjectName="SW-SB_ALB.SB_ALB_0741SW"/>
     <cge:Meas_Ref ObjectId="113612"/>
    <cge:TPSR_Ref TObjectID="21569"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-113613">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 753.000000 -187.847826)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21570" ObjectName="SW-SB_ALB.SB_ALB_0746SW"/>
     <cge:Meas_Ref ObjectId="113613"/>
    <cge:TPSR_Ref TObjectID="21570"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-113628">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 575.000000 -343.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21572" ObjectName="SW-SB_ALB.SB_ALB_0751SW"/>
     <cge:Meas_Ref ObjectId="113628"/>
    <cge:TPSR_Ref TObjectID="21572"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-113629">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 575.000000 -181.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21573" ObjectName="SW-SB_ALB.SB_ALB_0756SW"/>
     <cge:Meas_Ref ObjectId="113629"/>
    <cge:TPSR_Ref TObjectID="21573"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-113644">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 401.000000 -342.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21575" ObjectName="SW-SB_ALB.SB_ALB_0761SW"/>
     <cge:Meas_Ref ObjectId="113644"/>
    <cge:TPSR_Ref TObjectID="21575"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-113645">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 401.000000 -180.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21576" ObjectName="SW-SB_ALB.SB_ALB_0766SW"/>
     <cge:Meas_Ref ObjectId="113645"/>
    <cge:TPSR_Ref TObjectID="21576"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-113708">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1246.000000 -777.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21585" ObjectName="SW-SB_ALB.SB_ALB_3901SW"/>
     <cge:Meas_Ref ObjectId="113708"/>
    <cge:TPSR_Ref TObjectID="21585"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-113710">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1282.000000 -798.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21586" ObjectName="SW-SB_ALB.SB_ALB_39017SW"/>
     <cge:Meas_Ref ObjectId="113710"/>
    <cge:TPSR_Ref TObjectID="21586"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-113660">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 768.000000 -797.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21578" ObjectName="SW-SB_ALB.SB_ALB_3711SW"/>
     <cge:Meas_Ref ObjectId="113660"/>
    <cge:TPSR_Ref TObjectID="21578"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-113661">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 768.000000 -914.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21579" ObjectName="SW-SB_ALB.SB_ALB_3716SW"/>
     <cge:Meas_Ref ObjectId="113661"/>
    <cge:TPSR_Ref TObjectID="21579"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-113662">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 846.000000 -1006.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21580" ObjectName="SW-SB_ALB.SB_ALB_37167SW"/>
     <cge:Meas_Ref ObjectId="113662"/>
    <cge:TPSR_Ref TObjectID="21580"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-113597">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1172.000000 -172.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21567" ObjectName="SW-SB_ALB.SB_ALB_0736SW"/>
     <cge:Meas_Ref ObjectId="113597"/>
    <cge:TPSR_Ref TObjectID="21567"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 429.000000 -826.000000)" xlink:href="#switch2:shape19_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 470.000000 -483.000000)" xlink:href="#switch2:shape19_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1651.000000 -513.000000)" xlink:href="#switch2:shape19_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 623.000000 -70.000000)" xlink:href="#switch2:shape19_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-113606">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1107.000000 -170.000000)" xlink:href="#switch2:shape45_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30391" ObjectName="SW-SB_ALB.SB_ALB_07367SW"/>
     <cge:Meas_Ref ObjectId="113606"/>
    <cge:TPSR_Ref TObjectID="30391"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-251782">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1389.000000 -712.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41939" ObjectName="SW-SB_ALB.SB_ALB_3021SW"/>
     <cge:Meas_Ref ObjectId="251782"/>
    <cge:TPSR_Ref TObjectID="41939"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-251796">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1389.000000 -440.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41942" ObjectName="SW-SB_ALB.SB_ALB_0022SW"/>
     <cge:Meas_Ref ObjectId="251796"/>
    <cge:TPSR_Ref TObjectID="41942"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-251783">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 1.000000 -1.000000 0.000000 1474.000000 -722.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41940" ObjectName="SW-SB_ALB.SB_ALB_30217SW"/>
     <cge:Meas_Ref ObjectId="251783"/>
    <cge:TPSR_Ref TObjectID="41940"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-251856">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.000000 1.000000 -1.000000 0.000000 1079.000000 -373.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41944" ObjectName="SW-SB_ALB.SB_ALB_0012SW"/>
     <cge:Meas_Ref ObjectId="251856"/>
    <cge:TPSR_Ref TObjectID="41944"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-251876">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1675.519231 -343.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41946" ObjectName="SW-SB_ALB.SB_ALB_0772SW"/>
     <cge:Meas_Ref ObjectId="251876"/>
    <cge:TPSR_Ref TObjectID="41946"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-251877">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1675.519231 -181.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41947" ObjectName="SW-SB_ALB.SB_ALB_0776SW"/>
     <cge:Meas_Ref ObjectId="251877"/>
    <cge:TPSR_Ref TObjectID="41947"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-251855">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 350.000000 -441.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41943" ObjectName="SW-SB_ALB.SB_ALB_0901SW"/>
     <cge:Meas_Ref ObjectId="251855"/>
    <cge:TPSR_Ref TObjectID="41943"/></metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="141" x="-151" y="-1060"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-198" y="-1077"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="74" y="-1069"/></g>
   <g href="cx_配调_配网接线图35_双柏.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="76" y="-1027"/></g>
   <g href="35kV安龙堡变SB_ALB_371间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="786" y="-887"/></g>
   <g href="35kV安龙堡变35kV1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="52" x="824" y="-616"/></g>
   <g href="35kV安龙堡变SB_ALB_071间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="1531" y="-278"/></g>
   <g href="35kV安龙堡变SB_ALB_072间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="1357" y="-278"/></g>
   <g href="35kV安龙堡变SB_ALB_073间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1199" y="-321"/></g>
   <g href="35kV安龙堡变SB_ALB_074间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="771" y="-287"/></g>
   <g href="35kV安龙堡变SB_ALB_075间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="593" y="-280"/></g>
   <g href="35kV安龙堡变SB_ALB_076间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="419" y="-279"/></g>
   <g href="35kV安龙堡变GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="19" qtmmishow="hidden" width="76" x="-242" y="-688"/></g>
   <g href="AVC安龙堡站.svg" style="fill-opacity:0"><rect height="44" qtmmishow="hidden" width="73" x="88" y="-968"/></g>
   <g href="35kV安龙堡变2号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="53" x="1445" y="-620"/></g>
   <g href="35kV安龙堡变SB_ALB_077间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1695" y="-280"/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_224a680">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1612.000000 -487.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33a9120">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1477.000000 -94.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f14dd0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1303.000000 -96.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f113b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 717.000000 -112.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2fbdb00">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 539.000000 -98.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e8a1b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 365.000000 -97.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f07be0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1246.000000 -866.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e29d20">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 809.000000 -1070.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e2a8a0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1159.000000 -839.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_16cf390">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1642.000000 -105.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2eca600">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 315.000000 -493.067797)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="SB_ALB"/>
</svg>