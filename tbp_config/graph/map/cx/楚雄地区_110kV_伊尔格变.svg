<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-154" aopId="786686" id="thSvg" product="E8000V2" version="1.0" viewBox="3115 -1198 1863 1201">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape8_0">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="91" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="82" y2="91"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="91" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="15" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="breaker2:shape8_1">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="7" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="99" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
   </symbol>
   <symbol id="breaker2:shape8-UnNor1">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="15" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="91" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="82" y2="91"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="91" y2="82"/>
   </symbol>
   <symbol id="breaker2:shape8-UnNor2">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="7" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="98" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="7" x2="11" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="9" x2="9" y1="27" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="0" x2="18" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="6" x2="13" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="29" x2="29" y1="7" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="4" x2="22" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="22" x2="22" y1="0" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="25" x2="25" y1="6" y2="13"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.742424" x1="2" x2="2" y1="11" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="26" x2="9" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="9" x2="9" y1="18" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="5" x2="5" y1="13" y2="5"/>
   </symbol>
   <symbol id="hydroGenerator:shape3">
    <polyline arcFlag="1" points="25,25 25,26 25,27 25,28 24,29 24,30 23,31 23,31 22,32 21,32 20,33 19,33 18,33 17,34 16,33 15,33 14,33 13,32 13,32 12,31 11,31 11,30 10,29 10,28 10,27 9,26 10,25 " stroke-width="1.14"/>
    <circle cx="24" cy="24" fillStyle="0" r="24" stroke-width="0.5"/>
    <polyline points="40,25 41,24 40,24 40,23 40,22 39,21 39,20 38,19 37,19 37,18 36,18 35,17 34,17 33,17 32,17 31,17 30,18 29,18 28,19 27,19 27,20 26,21 26,22 25,23 25,24 25,24 25,25 " stroke-width="1.14"/>
   </symbol>
   <symbol id="lightningRod:shape4">
    <rect height="31" stroke-width="2" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="9" x2="9" y1="35" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="8" x2="8" y1="12" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="17" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="5" x2="5" y1="3" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="1" x2="1" y1="5" y2="8"/>
    <rect height="12" stroke-width="1" width="26" x="18" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="59" x2="24" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape117">
    <ellipse cx="13" cy="27" fillStyle="0" rx="13" ry="12.5" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="42" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="46" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="50" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="22" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="26" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="30" y2="26"/>
    <circle cx="13" cy="46" fillStyle="0" r="13" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="56" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="56" x2="56" y1="28" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="54" x2="58" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="47" x2="65" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="53" x2="60" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape36">
    <rect height="19" stroke-width="0.75" width="8" x="4" y="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.278409" x1="6" x2="9" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.351519" x1="5" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.351519" x1="1" x2="14" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.351519" x1="8" x2="8" y1="21" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="7" x2="7" y1="46" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="6" x2="7" y1="29" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="8" x2="9" y1="27" y2="29"/>
   </symbol>
   <symbol id="lightningRod:shape25">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0182374" x1="13" x2="13" y1="32" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0326126" x1="8" x2="13" y1="29" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0326126" x1="17" x2="13" y1="29" y2="32"/>
    <ellipse cx="12" cy="32" rx="12.5" ry="12" stroke-width="0.120929"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0433242" x1="16" x2="9" y1="12" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0433242" x1="9" x2="9" y1="17" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0433242" x1="16" x2="9" y1="12" y2="7"/>
    <circle cx="12" cy="12" r="12.5" stroke-width="0.120929"/>
   </symbol>
   <symbol id="lightningRod:shape115">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0319731" x1="9" x2="13" y1="31" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0178798" x1="13" x2="13" y1="35" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0319731" x1="18" x2="13" y1="31" y2="35"/>
    <ellipse cx="13" cy="34" rx="12.5" ry="11.5" stroke-width="0.118558"/>
    <circle cx="13" cy="17" r="12" stroke-width="0.120929"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0326126" x1="18" x2="13" y1="14" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0326126" x1="9" x2="13" y1="14" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0182374" x1="13" x2="13" y1="17" y2="21"/>
   </symbol>
   <symbol id="lightningRod:shape39">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.278409" x1="49" x2="49" y1="6" y2="9"/>
    <rect height="8" stroke-width="0.75" width="18" x="11" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="24" x2="22" y1="7" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="22" x2="24" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="4" x2="24" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.351519" x1="29" x2="43" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.351519" x1="43" x2="43" y1="0" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.351519" x1="46" x2="46" y1="4" y2="10"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="8" x2="8" y1="9" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="1" y2="1"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="7" x2="15" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="15" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="7" x2="15" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="15" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="transformer:shape0_0">
    <circle cx="26" cy="29" fillStyle="0" r="24.5" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="20" x2="20" y1="32" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="35" x2="20" y1="23" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="35" x2="20" y1="23" y2="32"/>
   </symbol>
   <symbol id="transformer:shape0_1">
    <circle cx="26" cy="61" fillStyle="0" r="25" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="25" x2="25" y1="57" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="33" x2="25" y1="73" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="25" x2="18" y1="66" y2="73"/>
   </symbol>
   <symbol id="transformer:shape0-2">
    <circle cx="56" cy="45" fillStyle="0" r="25" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="62" x2="62" y1="37" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="70" x2="62" y1="53" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="62" x2="55" y1="46" y2="53"/>
   </symbol>
   <symbol id="transformer:shape21_0">
    <circle cx="41" cy="60" fillStyle="0" r="25" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="40" x2="40" y1="58" y2="67"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="48" x2="40" y1="74" y2="67"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="40" x2="33" y1="67" y2="74"/>
   </symbol>
   <symbol id="transformer:shape21_1">
    <circle cx="26" cy="30" fillStyle="0" r="25" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="26" x2="26" y1="19" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="11" x2="26" y1="28" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="11" x2="26" y1="28" y2="19"/>
   </symbol>
   <symbol id="transformer:shape21-2">
    <circle cx="57" cy="31" fillStyle="0" r="25" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="63" x2="63" y1="20" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="71" x2="63" y1="36" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="63" x2="56" y1="29" y2="36"/>
   </symbol>
   <symbol id="voltageTransformer:shape18">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="48" x2="48" y1="4" y2="4"/>
    <circle cx="41" cy="55" fillStyle="0" r="25" stroke-width="0.520408"/>
    <circle cx="57" cy="26" fillStyle="0" r="25" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="40" x2="40" y1="50" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="48" x2="40" y1="66" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="40" x2="32" y1="59" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="22" x2="22" y1="13" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="30" x2="22" y1="29" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="22" x2="14" y1="22" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="57" x2="57" y1="33" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="73" x2="57" y1="24" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="73" x2="57" y1="24" y2="33"/>
    <circle cx="25" cy="25" fillStyle="0" r="25" stroke-width="0.520408"/>
   </symbol>
   <symbol id="voltageTransformer:shape5">
    <circle cx="7" cy="9" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="6" cy="18" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="13" cy="14" fillStyle="0" r="6" stroke-width="0.431185"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_137a940" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_137b560" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_137bd50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_137c5d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_137d620" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_137e100" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_137e840" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_137f070" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_13800f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_13800f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1b9dd20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1b9dd20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1b9f690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1b9f690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_1ba0460" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1ba1c90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1ba2850" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1ba3530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1ba3c00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1ba5280" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1ba5d00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1ba6540" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1ba6d00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1ba7de0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1ba8760" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1ba9250" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1ba9c10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1bab280" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1babcd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1baced0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1badb40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1bbcb60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1bb5210" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1bb6900" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1bb08f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1211" width="1873" x="3110" y="-1203"/>
  </g><g id="MultiLine_Layer">
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4140,-302 4140,-294 4146,-298 4140,-302 " stroke="rgb(139,139,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4183,-302 4183,-294 4177,-298 4183,-302 " stroke="rgb(139,139,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3779,-308 3779,-300 3785,-304 3779,-308 " stroke="rgb(139,139,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3822,-308 3822,-300 3816,-304 3822,-308 " stroke="rgb(139,139,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3853,-308 3853,-300 3859,-304 3853,-308 " stroke="rgb(139,139,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3896,-308 3896,-300 3890,-304 3896,-308 " stroke="rgb(139,139,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4196,-302 4196,-294 4202,-298 4196,-302 " stroke="rgb(139,139,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4239,-302 4239,-294 4233,-298 4239,-302 " stroke="rgb(139,139,0)" stroke-width="1"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-94011">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3987.000000 -784.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19811" ObjectName="SW-CX_YErG.CX_YErG_101BK"/>
     <cge:Meas_Ref ObjectId="94011"/>
    <cge:TPSR_Ref TObjectID="19811"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-94029">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3834.000000 -366.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19807" ObjectName="SW-CX_YErG.CX_YErG_611BK"/>
     <cge:Meas_Ref ObjectId="94029"/>
    <cge:TPSR_Ref TObjectID="19807"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-94034">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4182.000000 -360.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19809" ObjectName="SW-CX_YErG.CX_YErG_612BK"/>
     <cge:Meas_Ref ObjectId="94034"/>
    <cge:TPSR_Ref TObjectID="19809"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-94019">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4593.000000 -769.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19797" ObjectName="SW-CX_YErG.CX_YErG_301BK"/>
     <cge:Meas_Ref ObjectId="94019"/>
    <cge:TPSR_Ref TObjectID="19797"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-94026">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3985.000000 -485.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19804" ObjectName="SW-CX_YErG.CX_YErG_601BK"/>
     <cge:Meas_Ref ObjectId="94026"/>
    <cge:TPSR_Ref TObjectID="19804"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-94027">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4455.000000 -326.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19805" ObjectName="SW-CX_YErG.CX_YErG_613BK"/>
     <cge:Meas_Ref ObjectId="94027"/>
    <cge:TPSR_Ref TObjectID="19805"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-94028">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4724.000000 -324.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19806" ObjectName="SW-CX_YErG.CX_YErG_614BK"/>
     <cge:Meas_Ref ObjectId="94028"/>
    <cge:TPSR_Ref TObjectID="19806"/></metadata>
   </g>
  </g><g id="Transformer_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_YErG.CX_YErG_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="27617"/>
     </metadata>
     <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3969.000000 -632.000000)" xlink:href="#transformer:shape0_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="27619"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3969.000000 -632.000000)" xlink:href="#transformer:shape0_1"/>
    </g>
    <g id="WD-2">
     <metadata>
      <cge:PSR_Ref ObjectId="27621"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3969.000000 -632.000000)" xlink:href="#transformer:shape0-2"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="19812" ObjectName="TF-CX_YErG.CX_YErG_1T"/>
    <cge:TPSR_Ref TObjectID="19812"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.488372 -0.000000 0.000000 -0.516854 4646.000000 -77.000000)" xlink:href="#transformer:shape21_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.488372 -0.000000 0.000000 -0.516854 4646.000000 -77.000000)" xlink:href="#transformer:shape21_1"/>
    </g>
    <g id="WD-2">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-6KV" transform="matrix(0.488372 -0.000000 0.000000 -0.516854 4646.000000 -77.000000)" xlink:href="#transformer:shape21-2"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_YErG.CX_YErG_6IM">
    <g class="BV-6KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3708,-450 4978,-450 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="19816" ObjectName="BS-CX_YErG.CX_YErG_6IM"/>
    <cge:TPSR_Ref TObjectID="19816"/></metadata>
   <polyline fill="none" opacity="0" points="3708,-450 4978,-450 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_YErG.CX_YErG_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4560,-919 4932,-919 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="19790" ObjectName="BS-CX_YErG.CX_YErG_3IM"/>
    <cge:TPSR_Ref TObjectID="19790"/></metadata>
   <polyline fill="none" opacity="0" points="4560,-919 4932,-919 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_YErG.XM">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3990,-783 4008,-783 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="48326" ObjectName="BS-CX_YErG.XM"/>
    <cge:TPSR_Ref TObjectID="48326"/></metadata>
   <polyline fill="none" opacity="0" points="3990,-783 4008,-783 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_135deb0" refnum="0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4708.500000 -875.500000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2908970" refnum="0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4888.500000 -989.500000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28344a0" refnum="0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4709.500000 -743.500000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25f73a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3841.000000 -583.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_258c9d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4076.000000 -884.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2853570" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4512.000000 -97.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25d2b60" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3797.000000 -903.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1eae630">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4881.000000 -562.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_209cc20">
    <use class="BV-6KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4796.500000 -493.500000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2195010">
    <use class="BV-6KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3929.500000 -329.500000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2853760">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3892.000000 -221.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1375e60">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3763.000000 -221.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1625040">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3717.000000 -219.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_257eac0">
    <use class="BV-6KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4277.500000 -323.500000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25dbd30">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4240.000000 -215.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1406600">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4118.000000 -215.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2836750">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4064.000000 -213.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2570810">
    <use class="BV-6KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4558.500000 -320.500000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2928de0">
    <use class="BV-6KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4827.500000 -317.500000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2838940">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4888.000000 -850.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28d5820">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4884.000000 -769.000000)" xlink:href="#lightningRod:shape117"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2915c80">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4344.500000 -638.500000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1269f60">
    <use class="BV-6KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4087.500000 -476.500000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2853f40">
    <use class="BV-6KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4089.500000 -613.500000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15b0520">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3906.000000 -602.000000)" xlink:href="#lightningRod:shape36"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1275d00">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3713.000000 -97.000000)" xlink:href="#lightningRod:shape25"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14c8810">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4061.000000 -91.000000)" xlink:href="#lightningRod:shape25"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20f96c0">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4452.000000 -156.000000)" xlink:href="#lightningRod:shape115"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2589b60">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4722.000000 -153.000000)" xlink:href="#lightningRod:shape115"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2910cc0">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4726.000000 -101.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15981c0">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4758.000000 -87.000000)" xlink:href="#lightningRod:shape39"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25b68a0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3867.000000 -794.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25d1880">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4686.500000 -1006.500000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25af600">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4772.000000 -1015.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25cfc50">
    <use class="BV-110KV" transform="matrix(0.777778 -0.000000 0.000000 -0.666667 3923.000000 -843.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3233.000000 -1118.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-116496" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3245.538462 -1014.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="116496" ObjectName="CX_YErG:CX_YErG_ZJ_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-116497" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3245.538462 -967.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="116497" ObjectName="CX_YErG:CX_YErG_ZJ_sumQ"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-93981" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4124.000000 -841.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="93981" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19811"/>
     <cge:Term_Ref ObjectID="27614"/>
    <cge:TPSR_Ref TObjectID="19811"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-93982" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4124.000000 -841.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="93982" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19811"/>
     <cge:Term_Ref ObjectID="27614"/>
    <cge:TPSR_Ref TObjectID="19811"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-93984" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4124.000000 -841.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="93984" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19811"/>
     <cge:Term_Ref ObjectID="27614"/>
    <cge:TPSR_Ref TObjectID="19811"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-93994" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4559.000000 -987.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="93994" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19790"/>
     <cge:Term_Ref ObjectID="27573"/>
    <cge:TPSR_Ref TObjectID="19790"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-93995" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4559.000000 -987.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="93995" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19790"/>
     <cge:Term_Ref ObjectID="27573"/>
    <cge:TPSR_Ref TObjectID="19790"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-93996" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4559.000000 -987.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="93996" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19790"/>
     <cge:Term_Ref ObjectID="27573"/>
    <cge:TPSR_Ref TObjectID="19790"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-93993" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4559.000000 -987.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="93993" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19790"/>
     <cge:Term_Ref ObjectID="27573"/>
    <cge:TPSR_Ref TObjectID="19790"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-93999" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3842.000000 -56.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="93999" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19807"/>
     <cge:Term_Ref ObjectID="27606"/>
    <cge:TPSR_Ref TObjectID="19807"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-94000" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3842.000000 -56.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="94000" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19807"/>
     <cge:Term_Ref ObjectID="27606"/>
    <cge:TPSR_Ref TObjectID="19807"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-94001" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3842.000000 -56.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="94001" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19807"/>
     <cge:Term_Ref ObjectID="27606"/>
    <cge:TPSR_Ref TObjectID="19807"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-94006" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4190.000000 -56.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="94006" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19809"/>
     <cge:Term_Ref ObjectID="27610"/>
    <cge:TPSR_Ref TObjectID="19809"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-94007" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4190.000000 -56.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="94007" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19809"/>
     <cge:Term_Ref ObjectID="27610"/>
    <cge:TPSR_Ref TObjectID="19809"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-94005" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4190.000000 -56.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="94005" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19809"/>
     <cge:Term_Ref ObjectID="27610"/>
    <cge:TPSR_Ref TObjectID="19809"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="3245" y="-1177"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="3245" y="-1177"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3196" y="-1194"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3196" y="-1194"/></g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_地调直调.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="3245" y="-1177"/></g>
   <g href="cx_索引_接线图_地调直调.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3196" y="-1194"/></g>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3116" y="-597"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3116" y="-1077"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3116" y="-1197"/>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1994330">
    <use class="BV-110KV" transform="matrix(0.536585 -0.000000 0.000000 -0.559524 3909.000000 -785.000000)" xlink:href="#voltageTransformer:shape18"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_258f5b0">
    <use class="BV-35KV" transform="matrix(0.536585 -0.000000 0.000000 0.559524 4760.000000 -1108.000000)" xlink:href="#voltageTransformer:shape18"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28d3e60">
    <use class="BV-6KV" transform="matrix(0.231707 -0.000000 0.000000 -0.279762 3763.000000 -188.000000)" xlink:href="#voltageTransformer:shape18"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25b3060">
    <use class="BV-6KV" transform="matrix(0.231707 -0.000000 0.000000 -0.279762 3892.000000 -187.000000)" xlink:href="#voltageTransformer:shape18"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24b9990">
    <use class="BV-6KV" transform="matrix(0.231707 -0.000000 0.000000 -0.279762 4118.000000 -182.000000)" xlink:href="#voltageTransformer:shape18"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_284f140">
    <use class="BV-6KV" transform="matrix(0.231707 -0.000000 0.000000 -0.279762 4240.000000 -181.000000)" xlink:href="#voltageTransformer:shape18"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2902480">
    <use class="BV-0KV" transform="matrix(1.650000 -0.000000 0.000000 -1.960000 4878.000000 -621.000000)" xlink:href="#voltageTransformer:shape5"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="HydroGenerator_Layer">
   <g DF8003:Layer="PUBLIC" id="SM-CX_YErG.P1">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3819.000000 -161.000000)" xlink:href="#hydroGenerator:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43427" ObjectName="SM-CX_YErG.P1"/>
    <cge:TPSR_Ref TObjectID="43427"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_YErG.P2">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4167.000000 -155.000000)" xlink:href="#hydroGenerator:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43428" ObjectName="SM-CX_YErG.P2"/>
    <cge:TPSR_Ref TObjectID="43428"/></metadata>
   </g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="19816" cx="3843" cy="-450" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19816" cx="4191" cy="-450" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19816" cx="4890" cy="-450" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19790" cx="4781" cy="-919" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19790" cx="4897" cy="-919" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19816" cx="4465" cy="-450" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19816" cx="4734" cy="-450" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19816" cx="3995" cy="-450" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19790" cx="4602" cy="-919" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48326" cx="3997" cy="-783" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48326" cx="3997" cy="-783" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19790" cx="4602" cy="-919" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24b4f80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3910.000000 -736.000000) translate(0,15)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_299baf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4854.000000 -688.000000) translate(0,15)">6kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1962cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3867.000000 -293.000000) translate(0,15)">1</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1962cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3867.000000 -293.000000) translate(0,33)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1962cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3867.000000 -293.000000) translate(0,51)">TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1962cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3867.000000 -293.000000) translate(0,69)">柜</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25decd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3743.000000 -294.000000) translate(0,15)">1</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25decd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3743.000000 -294.000000) translate(0,33)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25decd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3743.000000 -294.000000) translate(0,51)">励</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25decd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3743.000000 -294.000000) translate(0,69)">磁</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25decd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3743.000000 -294.000000) translate(0,87)">TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25decd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3743.000000 -294.000000) translate(0,105)">柜</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_141ceb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3692.000000 -293.000000) translate(0,15)">1</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_141ceb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3692.000000 -293.000000) translate(0,33)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_141ceb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3692.000000 -293.000000) translate(0,51)">励</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_141ceb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3692.000000 -293.000000) translate(0,69)">磁</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_141ceb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3692.000000 -293.000000) translate(0,87)">变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_141ceb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3692.000000 -293.000000) translate(0,105)">柜</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_291fc00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3812.000000 -148.000000) translate(0,15)">1号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19a4740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3685.000000 -90.000000) translate(0,15)">1号励磁变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_15bc860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4111.000000 -177.000000) translate(0,15)">YH2</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2941e40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4215.000000 -287.000000) translate(0,15)">2</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2941e40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4215.000000 -287.000000) translate(0,33)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2941e40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4215.000000 -287.000000) translate(0,51)">TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2941e40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4215.000000 -287.000000) translate(0,69)">柜</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2764f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4100.000000 -287.000000) translate(0,15)">2</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2764f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4100.000000 -287.000000) translate(0,33)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2764f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4100.000000 -287.000000) translate(0,51)">励</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2764f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4100.000000 -287.000000) translate(0,69)">磁</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2764f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4100.000000 -287.000000) translate(0,87)">TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2764f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4100.000000 -287.000000) translate(0,105)">柜</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25b04e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4040.000000 -287.000000) translate(0,15)">2</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25b04e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4040.000000 -287.000000) translate(0,33)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25b04e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4040.000000 -287.000000) translate(0,51)">励</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25b04e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4040.000000 -287.000000) translate(0,69)">磁</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25b04e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4040.000000 -287.000000) translate(0,87)">变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25b04e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4040.000000 -287.000000) translate(0,105)">柜</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25b0720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4160.000000 -142.000000) translate(0,15)">2号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25705f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4031.000000 -90.000000) translate(0,15)">2号励磁变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_217bce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4449.000000 -51.000000) translate(0,15)">至厂用I段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b4fe60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4753.000000 -191.000000) translate(0,15)">近区变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_258d980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4748.000000 -1157.000000) translate(0,15)">35kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_290b030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4520.000000 -1157.000000) translate(0,15)">至35kV勐果河三级电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_14647d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4577.000000 -1086.000000) translate(0,15)">联</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_14647d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4577.000000 -1086.000000) translate(0,33)">络</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_14647d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4577.000000 -1086.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e4d1d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4910.000000 -851.000000) translate(0,15)">直配变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_29265c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3957.000000 -1150.000000) translate(0,15)">110kV勐武线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_129b660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -576.000000) translate(0,17)">危险点说明:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_129b660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -576.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_129b660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -576.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_129b660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -576.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_129b660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -576.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_129b660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -576.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_129b660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -576.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_129b660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -576.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_129b660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -576.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_129b660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -576.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_129b660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -576.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_129b660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -576.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_129b660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -576.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_129b660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -576.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_129b660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -576.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_129b660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -576.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_129b660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -576.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_129b660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -576.000000) translate(0,374)">联系方式:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2587fe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3129.000000 -1055.000000) translate(0,17)">频率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2587fe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3129.000000 -1055.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2587fe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3129.000000 -1055.000000) translate(0,59)">全站有功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2587fe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3129.000000 -1055.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2587fe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3129.000000 -1055.000000) translate(0,101)">全站无功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2587fe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3129.000000 -1055.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2587fe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3129.000000 -1055.000000) translate(0,143)">并网联络点的电压和交换功率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_290ec60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3277.000000 -1166.500000) translate(0,16)">伊尔格电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28546a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3853.000000 -972.000000) translate(0,12)">1600</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2854b90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3847.000000 -936.000000) translate(0,12)">16007</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25b08f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4025.000000 -923.000000) translate(0,12)">10117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25b0b30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4006.000000 -811.000000) translate(0,12)">101</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25b0d70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4003.000000 -857.000000) translate(0,12)">1011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25b0fb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3798.000000 -666.000000) translate(0,12)">1701</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20fbeb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4009.000000 -544.000000) translate(0,12)">601</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20fc0f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3857.000000 -395.000000) translate(0,12)">611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20fc330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4819.000000 -1023.000000) translate(0,12)">36007</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20fc570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4796.000000 -967.000000) translate(0,12)">3600</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2921e50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4854.000000 -878.000000) translate(0,12)">42B</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2906100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4640.000000 -913.000000) translate(0,12)">30117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2906340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4641.000000 -777.000000) translate(0,12)">30167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2906580" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4613.000000 -798.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_290f1a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4611.000000 -848.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_290f3e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4614.000000 -726.000000) translate(0,12)">3016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_290f620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4204.000000 -389.000000) translate(0,12)">612</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_290f860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4477.000000 -385.000000) translate(0,12)">613</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_290faa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4747.000000 -384.000000) translate(0,12)">614</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_258ac80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4694.000000 -186.000000) translate(0,12)">43B</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_258b170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4344.000000 -476.000000) translate(0,15)">6kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2abdec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4674.000000 -1023.000000) translate(0,12)">4PB</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25b01e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4881.000000 -946.000000) translate(0,15)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_283b4f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4283.000000 -182.000000) translate(0,12)">2号发电机参数:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_283b4f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4283.000000 -182.000000) translate(0,27)">SFW6300-6300-/2150</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_283b4f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4283.000000 -182.000000) translate(0,42)">Pe=6.3MW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_283b4f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4283.000000 -182.000000) translate(0,57)">Ue=6.3kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_283b4f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4283.000000 -182.000000) translate(0,72)">Ie=722A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_283b4f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4283.000000 -182.000000) translate(0,87)">CoS∮=0.8</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_283b770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3901.000000 -152.000000) translate(0,12)">1号发电机参数:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_283b770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3901.000000 -152.000000) translate(0,27)">SFW6300-6300-/2150</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_283b770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3901.000000 -152.000000) translate(0,42)">Pe=6.3MW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_283b770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3901.000000 -152.000000) translate(0,57)">Ue=6.3kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_283b770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3901.000000 -152.000000) translate(0,72)">Ie=722A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_283b770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3901.000000 -152.000000) translate(0,87)">CoS∮=0.8</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_283b9e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3893.000000 -348.000000) translate(0,12)">1TPB</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2851dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4234.000000 -339.000000) translate(0,12)">2TPB</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25b1d30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4229.000000 -179.000000) translate(0,15)">1YH2</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25b1f70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3761.000000 -180.000000) translate(0,15)">YH1</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25b21b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3893.000000 -185.000000) translate(0,15)">1YH1</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2836f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4770.000000 -114.000000) translate(0,12)">5PB</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28373f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4640.000000 -136.000000) translate(0,12)">5YH</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2837630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4720.000000 -42.000000) translate(0,12)">近区线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2837bb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4785.000000 -338.000000) translate(0,12)">4TPB</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2837e30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4511.000000 -343.000000) translate(0,12)">3TPB</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2901dc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4049.000000 -497.000000) translate(0,12)">5TPB</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2902000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4052.000000 -633.000000) translate(0,12)">6TPB</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2902240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4795.000000 -523.000000) translate(0,12)">7TPB</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2902ab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4301.000000 -654.000000) translate(0,12)">1PB</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25cf7d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3923.000000 -642.000000) translate(0,12)">2PB</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25cfa10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3831.000000 -830.000000) translate(0,12)">3PB</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25d05d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3831.000000 -790.000000) translate(0,15)">110kV电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25b57c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4069.000000 -762.000000) translate(0,15)">1号主变参数:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25b57c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4069.000000 -762.000000) translate(0,33)">SFS9-2000-121/38.5/6.3</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25b57c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4069.000000 -762.000000) translate(0,51)">YN,yn0，d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25b57c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4069.000000 -762.000000) translate(0,69)">121/38.5/6.3kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2478860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3243.000000 -224.000000) translate(0,15)">13887891101</text>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24767a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4068.000000 820.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2476a20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4057.000000 805.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2476c60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4082.000000 790.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2476f90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3783.000000 55.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24771f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3772.000000 40.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2477430" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3797.000000 25.000000) translate(0,12)">Ib(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2477760" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4132.000000 55.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24779c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4121.000000 40.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2477c00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4146.000000 25.000000) translate(0,12)">Ib(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2477f30" transform="matrix(1.000000 -0.000000 0.000000 -0.966667 4499.000000 971.500000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24781a0" transform="matrix(1.000000 -0.000000 0.000000 -0.966667 4499.000000 956.033333) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24783e0" transform="matrix(1.000000 -0.000000 0.000000 -0.966667 4491.000000 941.033333) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2478620" transform="matrix(1.000000 -0.000000 0.000000 -0.966667 4499.000000 986.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_YErG" endPointId="0" endStationName="PAS_T1" flowDrawDirect="1" flowShape="0" id="AC-110kV.mengwuTyeg_line" runFlow="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3995,-1096 3995,-1124 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="38104" ObjectName="AC-110kV.mengwuTyeg_line"/>
    <cge:TPSR_Ref TObjectID="38104_SS-154"/></metadata>
   <polyline fill="none" opacity="0" points="3995,-1096 3995,-1124 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-93970" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3422.000000 -1086.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19779" ObjectName="DYN-CX_YErG"/>
     <cge:Meas_Ref ObjectId="93970"/>
    </metadata>
   </g>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-94012">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3980.000000 -815.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19792" ObjectName="SW-CX_YErG.CX_YErG_1011SW"/>
     <cge:Meas_Ref ObjectId="94012"/>
    <cge:TPSR_Ref TObjectID="19792"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-94030">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3833.000000 -409.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19808" ObjectName="SW-CX_YErG.CX_YErG_611XC"/>
     <cge:Meas_Ref ObjectId="94030"/>
    <cge:TPSR_Ref TObjectID="19808"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-94030">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3833.000000 -331.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19814" ObjectName="SW-CX_YErG.CX_YErG_611XC1"/>
     <cge:Meas_Ref ObjectId="94030"/>
    <cge:TPSR_Ref TObjectID="19814"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-94035">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4181.000000 -403.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19810" ObjectName="SW-CX_YErG.CX_YErG_612XC"/>
     <cge:Meas_Ref ObjectId="94035"/>
    <cge:TPSR_Ref TObjectID="19810"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-94035">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4181.000000 -325.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19815" ObjectName="SW-CX_YErG.CX_YErG_612XC1"/>
     <cge:Meas_Ref ObjectId="94035"/>
    <cge:TPSR_Ref TObjectID="19815"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4880.000000 -528.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-94020">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4587.000000 -678.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19798" ObjectName="SW-CX_YErG.CX_YErG_3016SW"/>
     <cge:Meas_Ref ObjectId="94020"/>
    <cge:TPSR_Ref TObjectID="19798"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-94022">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4587.000000 -803.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19800" ObjectName="SW-CX_YErG.CX_YErG_3011SW"/>
     <cge:Meas_Ref ObjectId="94022"/>
    <cge:TPSR_Ref TObjectID="19800"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-94023">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4613.000000 -900.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19801" ObjectName="SW-CX_YErG.CX_YErG_30117SW"/>
     <cge:Meas_Ref ObjectId="94023"/>
    <cge:TPSR_Ref TObjectID="19801"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-94024">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4766.000000 -923.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19802" ObjectName="SW-CX_YErG.CX_YErG_3600SW"/>
     <cge:Meas_Ref ObjectId="94024"/>
    <cge:TPSR_Ref TObjectID="19802"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-94025">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4793.000000 -1014.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19803" ObjectName="SW-CX_YErG.CX_YErG_36007SW"/>
     <cge:Meas_Ref ObjectId="94025"/>
    <cge:TPSR_Ref TObjectID="19803"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-94021">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4614.000000 -768.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19799" ObjectName="SW-CX_YErG.CX_YErG_30167SW"/>
     <cge:Meas_Ref ObjectId="94021"/>
    <cge:TPSR_Ref TObjectID="19799"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-94014">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3836.000000 -613.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19794" ObjectName="SW-CX_YErG.CX_YErG_1701SW"/>
     <cge:Meas_Ref ObjectId="94014"/>
    <cge:TPSR_Ref TObjectID="19794"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-94013">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4035.000000 -868.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19793" ObjectName="SW-CX_YErG.CX_YErG_10117SW"/>
     <cge:Meas_Ref ObjectId="94013"/>
    <cge:TPSR_Ref TObjectID="19793"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-94018">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3854.000000 -886.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19796" ObjectName="SW-CX_YErG.CX_YErG_16007"/>
     <cge:Meas_Ref ObjectId="94018"/>
    <cge:TPSR_Ref TObjectID="19796"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-94015">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3886.000000 -923.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19795" ObjectName="SW-CX_YErG.CX_YErG_1600SW"/>
     <cge:Meas_Ref ObjectId="94015"/>
    <cge:TPSR_Ref TObjectID="19795"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-311529">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3981.000000 -715.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48327" ObjectName="SW-CX_YErG.XB"/>
     <cge:Meas_Ref ObjectId="311529"/>
    <cge:TPSR_Ref TObjectID="48327"/></metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-6KV" id="g_14c77e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3843,-450 3843,-433 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="19816@0" ObjectIDZND0="19808@0" Pin0InfoVect0LinkObjId="SW-94030_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d695a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3843,-450 3843,-433 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2903140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3843,-416 3843,-401 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="19808@1" ObjectIDZND0="19807@1" Pin0InfoVect0LinkObjId="SW-94029_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-94030_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3843,-416 3843,-401 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2713950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3843,-374 3843,-355 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="19807@0" ObjectIDZND0="19814@0" Pin0InfoVect0LinkObjId="SW-94030_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-94029_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3843,-374 3843,-355 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_25cd160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3870,-323 3870,-322 3843,-322 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="hydroGenerator" EndDevType2="lightningRod" ObjectIDND0="g_2195010@0" ObjectIDZND0="19814@x" ObjectIDZND1="43427@x" ObjectIDZND2="g_1375e60@0" Pin0InfoVect0LinkObjId="SW-94030_0" Pin0InfoVect1LinkObjId="SM-CX_YErG.P1_0" Pin0InfoVect2LinkObjId="g_1375e60_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2195010_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3870,-323 3870,-322 3843,-322 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_25b3b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3843,-337 3843,-322 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="hydroGenerator" EndDevType2="lightningRod" ObjectIDND0="19814@1" ObjectIDZND0="g_2195010@0" ObjectIDZND1="43427@x" ObjectIDZND2="g_1375e60@0" Pin0InfoVect0LinkObjId="g_2195010_0" Pin0InfoVect1LinkObjId="SM-CX_YErG.P1_0" Pin0InfoVect2LinkObjId="g_1375e60_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-94030_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3843,-337 3843,-322 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_15cb200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3901,-209 3901,-226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_25b3060@0" ObjectIDZND0="g_2853760@0" Pin0InfoVect0LinkObjId="g_2853760_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25b3060_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3901,-209 3901,-226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1966760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3772,-257 3772,-304 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_1375e60@1" ObjectIDZND0="g_1625040@0" ObjectIDZND1="g_2195010@0" ObjectIDZND2="19814@x" Pin0InfoVect0LinkObjId="g_1625040_0" Pin0InfoVect1LinkObjId="g_2195010_0" Pin0InfoVect2LinkObjId="SW-94030_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1375e60_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3772,-257 3772,-304 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_28eeb20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3772,-226 3772,-210 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_1375e60@0" ObjectIDZND0="g_28d3e60@0" Pin0InfoVect0LinkObjId="g_28d3e60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1375e60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3772,-226 3772,-210 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2833dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4191,-410 4191,-395 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="19810@1" ObjectIDZND0="19809@1" Pin0InfoVect0LinkObjId="SW-94034_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-94035_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4191,-410 4191,-395 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_217b100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4191,-368 4191,-349 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="19809@0" ObjectIDZND0="19815@0" Pin0InfoVect0LinkObjId="SW-94035_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-94034_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4191,-368 4191,-349 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1998870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4249,-203 4249,-220 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_284f140@0" ObjectIDZND0="g_25dbd30@0" Pin0InfoVect0LinkObjId="g_25dbd30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_284f140_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4249,-203 4249,-220 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_25f6940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4127,-220 4127,-204 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_1406600@0" ObjectIDZND0="g_24b9990@0" Pin0InfoVect0LinkObjId="g_24b9990_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1406600_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4127,-220 4127,-204 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_28076a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4191,-450 4191,-427 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="19816@0" ObjectIDZND0="19810@0" Pin0InfoVect0LinkObjId="SW-94035_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d695a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4191,-450 4191,-427 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2807890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4465,-450 4465,-425 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="19816@0" ObjectIDZND0="19805@0" Pin0InfoVect0LinkObjId="SW-94027_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d695a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4465,-450 4465,-425 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_217ba80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4499,-314 4465,-314 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="lightningRod" ObjectIDND0="g_2570810@0" ObjectIDZND0="19805@x" ObjectIDZND1="g_20f96c0@0" Pin0InfoVect0LinkObjId="SW-94027_0" Pin0InfoVect1LinkObjId="g_20f96c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2570810_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4499,-314 4465,-314 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_19adaa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4734,-450 4734,-422 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="19816@0" ObjectIDZND0="19806@0" Pin0InfoVect0LinkObjId="SW-94028_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d695a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4734,-450 4734,-422 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1d693b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4734,-311 4768,-311 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="g_2589b60@0" ObjectIDND1="19806@x" ObjectIDZND0="g_2928de0@0" Pin0InfoVect0LinkObjId="g_2928de0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2589b60_0" Pin1InfoVect1LinkObjId="SW-94028_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4734,-311 4768,-311 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1d695a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4856,-500 4856,-499 4890,-499 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="busSection" EndDevType1="switch" ObjectIDND0="g_209cc20@0" ObjectIDZND0="19816@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_27a2a70_0" Pin0InfoVect1LinkObjId="TF-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_209cc20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4856,-500 4856,-499 4890,-499 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_27a2a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4890,-499 4890,-450 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="g_209cc20@0" ObjectIDND1="0@x" ObjectIDZND0="19816@0" Pin0InfoVect0LinkObjId="g_1d695a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_209cc20_0" Pin1InfoVect1LinkObjId="TF-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4890,-499 4890,-450 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_27a3b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4890,-534 4890,-499 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="busSection" ObjectIDND0="0@1" ObjectIDZND0="g_209cc20@0" ObjectIDZND1="19816@0" Pin0InfoVect0LinkObjId="g_209cc20_0" Pin0InfoVect1LinkObjId="g_1d695a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="TF-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4890,-534 4890,-499 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_27a3d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4890,-627 4890,-599 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_2902480@0" ObjectIDZND0="g_1eae630@0" Pin0InfoVect0LinkObjId="g_1eae630_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2902480_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4890,-627 4890,-599 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_27a32d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4890,-567 4890,-552 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1eae630@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="TF-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1eae630_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4890,-567 4890,-552 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_271df70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3995,-821 3995,-837 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="19811@1" ObjectIDZND0="19792@0" Pin0InfoVect0LinkObjId="SW-94012_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-94011_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3995,-821 3995,-837 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_129d880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4602,-804 4602,-825 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="19797@1" ObjectIDZND0="19800@0" Pin0InfoVect0LinkObjId="SW-94022_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-94019_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4602,-804 4602,-825 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1984030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4602,-861 4602,-885 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="19800@1" ObjectIDZND0="19801@x" ObjectIDZND1="19790@0" Pin0InfoVect0LinkObjId="SW-94023_0" Pin0InfoVect1LinkObjId="g_1984290_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-94022_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4602,-861 4602,-885 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1984290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4602,-885 4602,-919 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="19800@x" ObjectIDND1="19801@x" ObjectIDZND0="19790@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-94022_0" Pin1InfoVect1LinkObjId="SW-94023_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4602,-885 4602,-919 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2929fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4602,-885 4635,-885 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="busSection" EndDevType0="switch" ObjectIDND0="19800@x" ObjectIDND1="19790@0" ObjectIDZND0="19801@0" Pin0InfoVect0LinkObjId="SW-94023_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-94022_0" Pin1InfoVect1LinkObjId="g_1984290_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4602,-885 4635,-885 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_135dc50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4671,-885 4682,-885 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="19801@1" ObjectIDZND0="g_135deb0@0" Pin0InfoVect0LinkObjId="g_135deb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-94023_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4671,-885 4682,-885 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25cb290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4897,-919 4897,-887 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="lightningRod" ObjectIDND0="19790@0" ObjectIDZND0="g_2838940@0" Pin0InfoVect0LinkObjId="g_2838940_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1984290_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4897,-919 4897,-887 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25cb4f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4897,-855 4897,-828 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2838940@1" ObjectIDZND0="g_28d5820@1" Pin0InfoVect0LinkObjId="g_28d5820_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2838940_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4897,-855 4897,-828 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25d3db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4897,-784 4897,-745 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_28d5820@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28d5820_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4897,-784 4897,-745 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12e51f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4781,-919 4781,-945 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="19790@0" ObjectIDZND0="19802@0" Pin0InfoVect0LinkObjId="SW-94024_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1984290_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4781,-919 4781,-945 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_199dc10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4782,-999 4815,-999 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="19802@x" ObjectIDND1="g_25d1880@0" ObjectIDND2="g_25af600@0" ObjectIDZND0="19803@0" Pin0InfoVect0LinkObjId="SW-94025_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-94024_0" Pin1InfoVect1LinkObjId="g_25d1880_0" Pin1InfoVect2LinkObjId="g_25af600_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4782,-999 4815,-999 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_199de70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4851,-999 4862,-999 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="19803@1" ObjectIDZND0="g_2908970@0" Pin0InfoVect0LinkObjId="g_2908970_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-94025_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4851,-999 4862,-999 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2809510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4781,-981 4781,-999 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="19802@1" ObjectIDZND0="19803@x" ObjectIDZND1="g_25d1880@0" ObjectIDZND2="g_25af600@0" Pin0InfoVect0LinkObjId="SW-94025_0" Pin0InfoVect1LinkObjId="g_25d1880_0" Pin0InfoVect2LinkObjId="g_25af600_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-94024_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4781,-981 4781,-999 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_290add0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3843,-322 3843,-303 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="hydroGenerator" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="g_2195010@0" ObjectIDND1="19814@x" ObjectIDZND0="43427@x" ObjectIDZND1="g_1375e60@0" ObjectIDZND2="g_1625040@0" Pin0InfoVect0LinkObjId="SM-CX_YErG.P1_0" Pin0InfoVect1LinkObjId="g_1375e60_0" Pin0InfoVect2LinkObjId="g_1625040_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2195010_0" Pin1InfoVect1LinkObjId="SW-94030_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3843,-322 3843,-303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_258d720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3843,-303 3843,-210 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="hydroGenerator" ObjectIDND0="g_2195010@0" ObjectIDND1="19814@x" ObjectIDND2="g_1375e60@0" ObjectIDZND0="43427@0" Pin0InfoVect0LinkObjId="SM-CX_YErG.P1_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2195010_0" Pin1InfoVect1LinkObjId="SW-94030_0" Pin1InfoVect2LinkObjId="g_1375e60_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3843,-303 3843,-210 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_290d510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4285,-632 4285,-679 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer" EndDevType1="switch" ObjectIDND0="g_2915c80@0" ObjectIDZND0="19812@x" ObjectIDZND1="19798@x" Pin0InfoVect0LinkObjId="g_2bd0380_0" Pin0InfoVect1LinkObjId="SW-94020_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2915c80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4285,-632 4285,-679 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_290d770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4049,-678 4051,-679 4285,-679 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="19812@2" ObjectIDZND0="g_2915c80@0" ObjectIDZND1="19798@x" Pin0InfoVect0LinkObjId="g_2915c80_0" Pin0InfoVect1LinkObjId="SW-94020_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_290d510_2" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4049,-678 4051,-679 4285,-679 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_263ee60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4028,-470 3995,-470 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="busSection" ObjectIDND0="g_1269f60@0" ObjectIDZND0="19804@x" ObjectIDZND1="19816@0" Pin0InfoVect0LinkObjId="SW-94026_0" Pin0InfoVect1LinkObjId="g_1d695a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1269f60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4028,-470 3995,-470 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2853aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3995,-492 3995,-470 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="busSection" ObjectIDND0="19804@1" ObjectIDZND0="g_1269f60@0" ObjectIDZND1="19816@0" Pin0InfoVect0LinkObjId="g_1269f60_0" Pin0InfoVect1LinkObjId="g_1d695a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-94026_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3995,-492 3995,-470 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2853ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3995,-470 3995,-450 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="busSection" ObjectIDND0="g_1269f60@0" ObjectIDND1="19804@x" ObjectIDZND0="19816@0" Pin0InfoVect0LinkObjId="g_1d695a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1269f60_0" Pin1InfoVect1LinkObjId="SW-94026_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3995,-470 3995,-450 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2bd0380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4030,-607 3995,-607 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer" EndDevType1="breaker" ObjectIDND0="g_2853f40@0" ObjectIDZND0="19812@x" ObjectIDZND1="19804@x" Pin0InfoVect0LinkObjId="g_290d510_0" Pin0InfoVect1LinkObjId="SW-94026_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2853f40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4030,-607 3995,-607 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_25a5550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3994,-637 3995,-635 3995,-607 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="19812@0" ObjectIDZND0="g_2853f40@0" ObjectIDZND1="19804@x" Pin0InfoVect0LinkObjId="g_2853f40_0" Pin0InfoVect1LinkObjId="SW-94026_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_290d510_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3994,-637 3995,-635 3995,-607 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_25a57b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3995,-607 3995,-584 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer" EndDevType0="breaker" ObjectIDND0="g_2853f40@0" ObjectIDND1="19812@x" ObjectIDZND0="19804@0" Pin0InfoVect0LinkObjId="SW-94026_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2853f40_0" Pin1InfoVect1LinkObjId="g_290d510_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3995,-607 3995,-584 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_159b410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4285,-679 4602,-679 4602,-700 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer" EndDevType0="switch" ObjectIDND0="g_2915c80@0" ObjectIDND1="19812@x" ObjectIDZND0="19798@0" Pin0InfoVect0LinkObjId="SW-94020_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2915c80_0" Pin1InfoVect1LinkObjId="g_290d510_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4285,-679 4602,-679 4602,-700 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2834240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4672,-753 4683,-753 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="19799@1" ObjectIDZND0="g_28344a0@0" Pin0InfoVect0LinkObjId="g_28344a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-94021_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4672,-753 4683,-753 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13a7bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4602,-753 4636,-753 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="19798@x" ObjectIDND1="19797@x" ObjectIDZND0="19799@0" Pin0InfoVect0LinkObjId="SW-94021_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-94020_0" Pin1InfoVect1LinkObjId="SW-94019_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4602,-753 4636,-753 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13a7e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4602,-736 4602,-753 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="19798@1" ObjectIDZND0="19799@x" ObjectIDZND1="19797@x" Pin0InfoVect0LinkObjId="SW-94021_0" Pin0InfoVect1LinkObjId="SW-94019_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-94020_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4602,-736 4602,-753 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13a8070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4602,-753 4602,-777 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="19799@x" ObjectIDND1="19798@x" ObjectIDZND0="19797@0" Pin0InfoVect0LinkObjId="SW-94019_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-94021_0" Pin1InfoVect1LinkObjId="SW-94020_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4602,-753 4602,-777 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_14d2f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3913,-695 3913,-647 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="19812@x" ObjectIDND1="19794@x" ObjectIDZND0="g_15b0520@0" Pin0InfoVect0LinkObjId="g_15b0520_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_290d510_0" Pin1InfoVect1LinkObjId="SW-94014_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3913,-695 3913,-647 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_15b78e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3996,-695 3913,-695 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="19812@x" ObjectIDZND0="19794@x" ObjectIDZND1="g_15b0520@0" Pin0InfoVect0LinkObjId="SW-94014_0" Pin0InfoVect1LinkObjId="g_15b0520_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_290d510_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3996,-695 3913,-695 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_25f6ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3913,-695 3851,-695 3851,-671 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="19812@x" ObjectIDND1="g_15b0520@0" ObjectIDZND0="19794@1" Pin0InfoVect0LinkObjId="SW-94014_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_290d510_0" Pin1InfoVect1LinkObjId="g_15b0520_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3913,-695 3851,-695 3851,-671 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_25f7140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3851,-635 3851,-609 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="19794@0" ObjectIDZND0="g_25f73a0@0" Pin0InfoVect0LinkObjId="g_25f73a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-94014_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3851,-635 3851,-609 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_258c770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3995,-873 3995,-894 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="powerLine" EndDevType2="switch" ObjectIDND0="19792@1" ObjectIDZND0="19793@x" ObjectIDZND1="38104@1" ObjectIDZND2="19795@x" Pin0InfoVect0LinkObjId="SW-94013_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="SW-94015_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-94012_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3995,-873 3995,-894 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1275840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3995,-894 4026,-894 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="38104@1" ObjectIDND1="19795@x" ObjectIDND2="19792@x" ObjectIDZND0="19793@0" Pin0InfoVect0LinkObjId="SW-94013_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="SW-94015_0" Pin1InfoVect2LinkObjId="SW-94012_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3995,-894 4026,-894 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1275aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4062,-894 4081,-894 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="19793@1" ObjectIDZND0="g_258c9d0@0" Pin0InfoVect0LinkObjId="g_258c9d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-94013_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4062,-894 4081,-894 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_25b7670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4465,-125 4522,-158 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="earth" ObjectIDND0="g_20f96c0@0" ObjectIDZND0="g_2853570@0" Pin0InfoVect0LinkObjId="g_2853570_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20f96c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4465,-125 4522,-158 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_293c5c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4465,-159 4465,-125 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="earth" ObjectIDND0="g_20f96c0@1" ObjectIDZND0="g_2853570@0" Pin0InfoVect0LinkObjId="g_2853570_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20f96c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4465,-159 4465,-125 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_293c820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4465,-125 4465,-87 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" BeginDevType1="lightningRod" ObjectIDND0="g_2853570@0" ObjectIDND1="g_20f96c0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2853570_0" Pin1InfoVect1LinkObjId="g_20f96c0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4465,-125 4465,-87 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_28da7f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4466,-170 4522,-170 4522,-158 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" EndDevType1="earth" ObjectIDZND0="g_20f96c0@0" ObjectIDZND1="g_2853570@0" Pin0InfoVect0LinkObjId="g_20f96c0_0" Pin0InfoVect1LinkObjId="g_2853570_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4466,-170 4522,-170 4522,-158 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_28daa50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4522,-158 4522,-123 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="earth" ObjectIDND0="g_20f96c0@0" ObjectIDZND0="g_2853570@0" Pin0InfoVect0LinkObjId="g_2853570_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20f96c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4522,-158 4522,-123 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_28dacb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4735,-93 4763,-93 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer" EndDevType0="lightningRod" ObjectIDND0="g_2910cc0@0" ObjectIDND1="0@x" ObjectIDZND0="g_15981c0@0" Pin0InfoVect0LinkObjId="g_15981c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2910cc0_0" Pin1InfoVect1LinkObjId="TF-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4735,-93 4763,-93 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2590630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4735,-93 4735,-67 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="transformer" ObjectIDND0="g_2910cc0@0" ObjectIDND1="g_15981c0@0" ObjectIDND2="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2910cc0_0" Pin1InfoVect1LinkObjId="g_15981c0_0" Pin1InfoVect2LinkObjId="TF-0_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4735,-93 4735,-67 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1597d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4735,-156 4735,-137 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2589b60@1" ObjectIDZND0="g_2910cc0@1" Pin0InfoVect0LinkObjId="g_2910cc0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2589b60_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4735,-156 4735,-137 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1597f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4735,-106 4735,-93 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="transformer" ObjectIDND0="g_2910cc0@0" ObjectIDZND0="g_15981c0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_15981c0_0" Pin0InfoVect1LinkObjId="TF-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2910cc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4735,-106 4735,-93 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_25d2900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3845,-912 3822,-912 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="19796@0" ObjectIDZND0="g_25d2b60@0" Pin0InfoVect0LinkObjId="g_25d2b60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-94018_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3845,-912 3822,-912 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_25d0d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4218,-317 4191,-317 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="g_257eac0@0" ObjectIDZND0="19815@x" ObjectIDZND1="g_25dbd30@0" ObjectIDZND2="g_1406600@0" Pin0InfoVect0LinkObjId="SW-94035_0" Pin0InfoVect1LinkObjId="g_25dbd30_0" Pin0InfoVect2LinkObjId="g_1406600_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_257eac0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4218,-317 4191,-317 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_25d1690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4191,-331 4191,-317 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="19815@1" ObjectIDZND0="g_257eac0@0" ObjectIDZND1="g_25dbd30@0" ObjectIDZND2="g_1406600@0" Pin0InfoVect0LinkObjId="g_257eac0_0" Pin0InfoVect1LinkObjId="g_25dbd30_0" Pin0InfoVect2LinkObjId="g_1406600_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-94035_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4191,-331 4191,-317 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2abdc60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4781,-999 4744,-999 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="19803@x" ObjectIDND1="19802@x" ObjectIDND2="g_25af600@0" ObjectIDZND0="g_25d1880@0" Pin0InfoVect0LinkObjId="g_25d1880_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-94025_0" Pin1InfoVect1LinkObjId="SW-94024_0" Pin1InfoVect2LinkObjId="g_25af600_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4781,-999 4744,-999 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25afd20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4781,-999 4781,-1020 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="19803@x" ObjectIDND1="19802@x" ObjectIDND2="g_25d1880@0" ObjectIDZND0="g_25af600@1" Pin0InfoVect0LinkObjId="g_25af600_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-94025_0" Pin1InfoVect1LinkObjId="SW-94024_0" Pin1InfoVect2LinkObjId="g_25d1880_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4781,-999 4781,-1020 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25aff80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4781,-1052 4781,-1063 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_25af600@0" ObjectIDZND0="g_258f5b0@0" Pin0InfoVect0LinkObjId="g_258f5b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25af600_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4781,-1052 4781,-1063 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_283bc60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3726,-141 3726,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1275d00@0" ObjectIDZND0="g_1625040@0" Pin0InfoVect0LinkObjId="g_1625040_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1275d00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3726,-141 3726,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_28dbe80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3726,-255 3726,-304 3772,-304 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_1625040@1" ObjectIDZND0="g_1375e60@0" ObjectIDZND1="g_2195010@0" ObjectIDZND2="19814@x" Pin0InfoVect0LinkObjId="g_1375e60_0" Pin0InfoVect1LinkObjId="g_2195010_0" Pin0InfoVect2LinkObjId="SW-94030_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1625040_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3726,-255 3726,-304 3772,-304 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_28dc070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4073,-135 4073,-218 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_14c8810@0" ObjectIDZND0="g_2836750@0" Pin0InfoVect0LinkObjId="g_2836750_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_14c8810_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4073,-135 4073,-218 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_28dc7a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3843,-304 3772,-304 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="hydroGenerator" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="g_2195010@0" ObjectIDND1="19814@x" ObjectIDND2="43427@x" ObjectIDZND0="g_1375e60@0" ObjectIDZND1="g_1625040@0" Pin0InfoVect0LinkObjId="g_1375e60_0" Pin0InfoVect1LinkObjId="g_1625040_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2195010_0" Pin1InfoVect1LinkObjId="SW-94030_0" Pin1InfoVect2LinkObjId="SM-CX_YErG.P1_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3843,-304 3772,-304 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2851200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3843,-305 3843,-304 3901,-304 3901,-255 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="hydroGenerator" EndDevType0="lightningRod" ObjectIDND0="g_2195010@0" ObjectIDND1="19814@x" ObjectIDND2="43427@x" ObjectIDZND0="g_2853760@1" Pin0InfoVect0LinkObjId="g_2853760_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2195010_0" Pin1InfoVect1LinkObjId="SW-94030_0" Pin1InfoVect2LinkObjId="SM-CX_YErG.P1_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3843,-305 3843,-304 3901,-304 3901,-255 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2851910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4191,-298 4249,-298 4249,-251 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_257eac0@0" ObjectIDND1="19815@x" ObjectIDND2="g_1406600@0" ObjectIDZND0="g_25dbd30@1" Pin0InfoVect0LinkObjId="g_25dbd30_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_257eac0_0" Pin1InfoVect1LinkObjId="SW-94035_0" Pin1InfoVect2LinkObjId="g_1406600_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4191,-298 4249,-298 4249,-251 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2851b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4249,-283 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4249,-283 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2b4f6d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4735,-93 4686,-93 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="transformer" ObjectIDND0="g_2910cc0@0" ObjectIDND1="g_15981c0@0" ObjectIDZND0="0@2" Pin0InfoVect0LinkObjId="TF-0_2" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2910cc0_0" Pin1InfoVect1LinkObjId="g_15981c0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4735,-93 4686,-93 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_25d0370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3930,-846 3930,-829 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_25cfc50@1" ObjectIDZND0="g_1994330@0" Pin0InfoVect0LinkObjId="g_1994330_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25cfc50_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3930,-846 3930,-829 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_25b5560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3995,-1009 3901,-1009 3901,-981 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="38104@1" ObjectIDND1="19793@x" ObjectIDND2="19792@x" ObjectIDZND0="19795@1" Pin0InfoVect0LinkObjId="SW-94015_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="SW-94013_0" Pin1InfoVect2LinkObjId="SW-94012_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3995,-1009 3901,-1009 3901,-981 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_28d23c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3901,-912 3901,-886 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="19795@x" ObjectIDND1="19796@x" ObjectIDZND0="g_25cfc50@0" ObjectIDZND1="g_25b68a0@0" Pin0InfoVect0LinkObjId="g_25cfc50_0" Pin0InfoVect1LinkObjId="g_25b68a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-94015_0" Pin1InfoVect1LinkObjId="SW-94018_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3901,-912 3901,-886 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_28d25b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3881,-912 3901,-912 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="19796@1" ObjectIDZND0="19795@x" ObjectIDZND1="g_25cfc50@0" ObjectIDZND2="g_25b68a0@0" Pin0InfoVect0LinkObjId="SW-94015_0" Pin0InfoVect1LinkObjId="g_25cfc50_0" Pin0InfoVect2LinkObjId="g_25b68a0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-94018_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3881,-912 3901,-912 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_28d27c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3901,-912 3901,-946 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="19796@x" ObjectIDND1="g_25cfc50@0" ObjectIDND2="g_25b68a0@0" ObjectIDZND0="19795@0" Pin0InfoVect0LinkObjId="SW-94015_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-94018_0" Pin1InfoVect1LinkObjId="g_25cfc50_0" Pin1InfoVect2LinkObjId="g_25b68a0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3901,-912 3901,-946 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_28d29f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3874,-852 3874,-886 3901,-886 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_25b68a0@0" ObjectIDZND0="19795@x" ObjectIDZND1="19796@x" ObjectIDZND2="g_25cfc50@0" Pin0InfoVect0LinkObjId="SW-94015_0" Pin0InfoVect1LinkObjId="SW-94018_0" Pin0InfoVect2LinkObjId="g_25cfc50_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25b68a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3874,-852 3874,-886 3901,-886 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_28d2c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3901,-886 3930,-886 3930,-867 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="19795@x" ObjectIDND1="19796@x" ObjectIDND2="g_25b68a0@0" ObjectIDZND0="g_25cfc50@0" Pin0InfoVect0LinkObjId="g_25cfc50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-94015_0" Pin1InfoVect1LinkObjId="SW-94018_0" Pin1InfoVect2LinkObjId="g_25b68a0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3901,-886 3930,-886 3930,-867 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_28d2e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3995,-1096 3995,-1009 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="38104@1" ObjectIDZND0="19795@x" ObjectIDZND1="19793@x" ObjectIDZND2="19792@x" Pin0InfoVect0LinkObjId="SW-94015_0" Pin0InfoVect1LinkObjId="SW-94013_0" Pin0InfoVect2LinkObjId="SW-94012_0" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3995,-1096 3995,-1009 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_28d3080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3995,-1009 3995,-894 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="38104@1" ObjectIDND1="19795@x" ObjectIDZND0="19793@x" ObjectIDZND1="19792@x" Pin0InfoVect0LinkObjId="SW-94013_0" Pin0InfoVect1LinkObjId="SW-94012_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="SW-94015_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3995,-1009 3995,-894 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2471a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4734,-200 4734,-311 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="lightningRod" ObjectIDND0="g_2589b60@0" ObjectIDZND0="19806@x" ObjectIDZND1="g_2928de0@0" Pin0InfoVect0LinkObjId="SW-94028_0" Pin0InfoVect1LinkObjId="g_2928de0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2589b60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4734,-200 4734,-311 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2471ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4734,-311 4734,-332 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="breaker" ObjectIDND0="g_2589b60@0" ObjectIDND1="g_2928de0@0" ObjectIDZND0="19806@1" Pin0InfoVect0LinkObjId="SW-94028_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2589b60_0" Pin1InfoVect1LinkObjId="g_2928de0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4734,-311 4734,-332 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2472770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4465,-333 4465,-314 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="19805@1" ObjectIDZND0="g_2570810@0" ObjectIDZND1="g_20f96c0@0" Pin0InfoVect0LinkObjId="g_2570810_0" Pin0InfoVect1LinkObjId="g_20f96c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-94027_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4465,-333 4465,-314 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_24729d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4465,-314 4465,-202 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="g_2570810@0" ObjectIDND1="19805@x" ObjectIDZND0="g_20f96c0@0" Pin0InfoVect0LinkObjId="g_20f96c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2570810_0" Pin1InfoVect1LinkObjId="SW-94027_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4465,-314 4465,-202 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_24736c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4191,-204 4191,-298 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="hydroGenerator" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="43428@0" ObjectIDZND0="g_25dbd30@0" ObjectIDZND1="g_257eac0@0" ObjectIDZND2="19815@x" Pin0InfoVect0LinkObjId="g_25dbd30_0" Pin0InfoVect1LinkObjId="g_257eac0_0" Pin0InfoVect2LinkObjId="SW-94035_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-CX_YErG.P2_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4191,-204 4191,-298 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2473900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4191,-298 4191,-317 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_25dbd30@0" ObjectIDND1="g_1406600@0" ObjectIDND2="g_2836750@0" ObjectIDZND0="g_257eac0@0" ObjectIDZND1="19815@x" Pin0InfoVect0LinkObjId="g_257eac0_0" Pin0InfoVect1LinkObjId="SW-94035_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_25dbd30_0" Pin1InfoVect1LinkObjId="g_1406600_0" Pin1InfoVect2LinkObjId="g_2836750_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4191,-298 4191,-317 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2473b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4127,-298 4191,-298 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_1406600@0" ObjectIDND1="g_2836750@0" ObjectIDZND0="g_25dbd30@0" ObjectIDZND1="g_257eac0@0" ObjectIDZND2="19815@x" Pin0InfoVect0LinkObjId="g_25dbd30_0" Pin0InfoVect1LinkObjId="g_257eac0_0" Pin0InfoVect2LinkObjId="SW-94035_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1406600_0" Pin1InfoVect1LinkObjId="g_2836750_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4127,-298 4191,-298 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2474650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4127,-251 4127,-298 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_1406600@1" ObjectIDZND0="g_25dbd30@0" ObjectIDZND1="g_257eac0@0" ObjectIDZND2="19815@x" Pin0InfoVect0LinkObjId="g_25dbd30_0" Pin0InfoVect1LinkObjId="g_257eac0_0" Pin0InfoVect2LinkObjId="SW-94035_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1406600_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4127,-251 4127,-298 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_24748b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4127,-298 4073,-298 4073,-249 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_25dbd30@0" ObjectIDND1="g_257eac0@0" ObjectIDND2="19815@x" ObjectIDZND0="g_2836750@1" Pin0InfoVect0LinkObjId="g_2836750_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_25dbd30_0" Pin1InfoVect1LinkObjId="g_257eac0_0" Pin1InfoVect2LinkObjId="SW-94035_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4127,-298 4073,-298 4073,-249 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2479860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4602,-919 4602,-1111 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" ObjectIDND0="19790@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1984290_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4602,-919 4602,-1111 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2a25270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3995,-717 3996,-717 3996,-737 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="switch" ObjectIDND0="19812@1" ObjectIDZND0="48327@0" Pin0InfoVect0LinkObjId="SW-311529_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_290d510_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3995,-717 3996,-717 3996,-737 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2a254d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3997,-783 3996,-792 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="48326@0" ObjectIDZND0="19811@0" Pin0InfoVect0LinkObjId="SW-94011_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a25990_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3997,-783 3996,-792 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2a25990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3996,-773 3997,-783 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48327@1" ObjectIDZND0="48326@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-311529_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3996,-773 3997,-783 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_YErG"/>
</svg>