<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-212" aopId="524" id="thSvg" product="E8000V2" version="1.0" viewBox="-330 -957 2209 1059">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="capacitor:shape13">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="10" x2="10" y1="5" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="0" x2="20" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="0" x2="20" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="10" x2="10" y1="23" y2="33"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="14" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="5" x2="8" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="3" x2="10" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="12" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
   </symbol>
   <symbol id="lightningRod:shape99">
    <polyline DF8003:Layer="PUBLIC" points="25,29 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="8" y1="46" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="41" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="41" y1="46" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="42" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="46" y2="46"/>
    <circle cx="25" cy="29" fillStyle="0" r="24" stroke-width="0.5"/>
   </symbol>
   <symbol id="lightningRod:shape4">
    <rect height="31" stroke-width="2" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="9" x2="9" y1="35" y2="6"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape13_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="12" y2="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="-5" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="4" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="3" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="46" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="63" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="54" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="55" y2="46"/>
   </symbol>
   <symbol id="switch2:shape13_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="-5" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="4" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="62" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="63" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="54" y2="63"/>
   </symbol>
   <symbol id="switch2:shape13-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="12" y2="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="-5" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="4" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="3" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="46" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="63" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="54" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="55" y2="46"/>
   </symbol>
   <symbol id="switch2:shape13-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="-5" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="4" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="62" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="63" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="54" y2="63"/>
   </symbol>
   <symbol id="switch2:shape18_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="57" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="13" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="22" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="13" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="66" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="65" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="74" y2="65"/>
   </symbol>
   <symbol id="switch2:shape18_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="74" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="5" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="65" y2="74"/>
   </symbol>
   <symbol id="switch2:shape18-UnNor1">
    <circle cx="10" cy="69" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="14" y2="65"/>
    <circle cx="10" cy="11" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="13" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="22" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="66" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="65" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="74" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="57" y2="66"/>
   </symbol>
   <symbol id="switch2:shape18-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="57" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="66"/>
    <circle cx="10" cy="11" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="13" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="22" y2="13"/>
    <circle cx="10" cy="69" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="66" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="65" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="74" y2="65"/>
   </symbol>
   <symbol id="transformer2:shape3_0">
    <ellipse cx="13" cy="34" fillStyle="0" rx="13" ry="12.5" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="40" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="36" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="32" y2="36"/>
   </symbol>
   <symbol id="transformer2:shape3_1">
    <circle cx="13" cy="18" fillStyle="0" r="13" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="20" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="16" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="12" y2="16"/>
   </symbol>
   <symbol id="voltageTransformer:shape74">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="25" y1="32" y2="32"/>
    <circle cx="59" cy="64" fillStyle="0" r="25" stroke-width="0.520408"/>
    <circle cx="51" cy="32" fillStyle="0" r="25" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="58" x2="58" y1="59" y2="68"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="66" x2="58" y1="75" y2="68"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="58" x2="50" y1="68" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="54" x2="54" y1="18" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="62" x2="54" y1="34" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="54" x2="46" y1="27" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="28" x2="28" y1="65" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="12" x2="28" y1="48" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="12" x2="28" y1="55" y2="65"/>
    <circle cx="25" cy="58" fillStyle="0" r="25" stroke-width="0.520408"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2f88ac0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2f89aa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2f8a4c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2f8b470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2f8c5d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2f8cf80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2f8daa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2f8e460" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_284afb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_284afb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2f914b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2f914b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2f931b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2f931b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_2f94190" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2f95df0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2f96a80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2f978f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2f98040" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2f998c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2f9a520" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2f9ade0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2f9b5a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2f9c680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2f9d000" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2f9daf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2f9e4b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2f9fad0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2fa04f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2fa1690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2fa2320" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2fb0730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2fa8ad0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2fa34b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2fa4830" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1069" width="2219" x="-335" y="-962"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-142787">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 263.000000 -406.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25347" ObjectName="SW-CX_ZD.CX_ZD_082BK"/>
     <cge:Meas_Ref ObjectId="142787"/>
    <cge:TPSR_Ref TObjectID="25347"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-142796">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 176.000000 -241.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25348" ObjectName="SW-CX_ZD.CX_ZD_083BK"/>
     <cge:Meas_Ref ObjectId="142796"/>
    <cge:TPSR_Ref TObjectID="25348"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-142805">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 349.000000 -240.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25349" ObjectName="SW-CX_ZD.CX_ZD_084BK"/>
     <cge:Meas_Ref ObjectId="142805"/>
    <cge:TPSR_Ref TObjectID="25349"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-142814">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 511.000000 -241.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25350" ObjectName="SW-CX_ZD.CX_ZD_085BK"/>
     <cge:Meas_Ref ObjectId="142814"/>
    <cge:TPSR_Ref TObjectID="25350"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-142823">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 672.000000 -240.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25351" ObjectName="SW-CX_ZD.CX_ZD_086BK"/>
     <cge:Meas_Ref ObjectId="142823"/>
    <cge:TPSR_Ref TObjectID="25351"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-142832">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 986.000000 -241.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25352" ObjectName="SW-CX_ZD.CX_ZD_091BK"/>
     <cge:Meas_Ref ObjectId="142832"/>
    <cge:TPSR_Ref TObjectID="25352"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-142841">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1147.000000 -240.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25353" ObjectName="SW-CX_ZD.CX_ZD_092BK"/>
     <cge:Meas_Ref ObjectId="142841"/>
    <cge:TPSR_Ref TObjectID="25353"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-142850">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1304.000000 -240.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25354" ObjectName="SW-CX_ZD.CX_ZD_093BK"/>
     <cge:Meas_Ref ObjectId="142850"/>
    <cge:TPSR_Ref TObjectID="25354"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-142859">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1465.000000 -239.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25355" ObjectName="SW-CX_ZD.CX_ZD_094BK"/>
     <cge:Meas_Ref ObjectId="142859"/>
    <cge:TPSR_Ref TObjectID="25355"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-142868">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1619.000000 -238.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25356" ObjectName="SW-CX_ZD.CX_ZD_095BK"/>
     <cge:Meas_Ref ObjectId="142868"/>
    <cge:TPSR_Ref TObjectID="25356"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-142877">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1780.000000 -237.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25357" ObjectName="SW-CX_ZD.CX_ZD_096BK"/>
     <cge:Meas_Ref ObjectId="142877"/>
    <cge:TPSR_Ref TObjectID="25357"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-142886">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1495.000000 -407.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25358" ObjectName="SW-CX_ZD.CX_ZD_097BK"/>
     <cge:Meas_Ref ObjectId="142886"/>
    <cge:TPSR_Ref TObjectID="25358"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-142771">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 810.000000 -211.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25346" ObjectName="SW-CX_ZD.CX_ZD_012BK"/>
     <cge:Meas_Ref ObjectId="142771"/>
    <cge:TPSR_Ref TObjectID="25346"/></metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_28df9c0">
    <use class="BV-10KV" transform="matrix(0.541176 -0.000000 0.000000 -0.522222 445.000000 -561.000000)" xlink:href="#voltageTransformer:shape74"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28b9e50">
    <use class="BV-10KV" transform="matrix(0.541176 -0.000000 0.000000 -0.522222 1372.000000 -561.000000)" xlink:href="#voltageTransformer:shape74"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 223.000000 -633.000000)" xlink:href="#capacitor:shape13"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 414.000000 -382.000000)" xlink:href="#capacitor:shape13"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(-1.000000 0.000000 0.000000 1.000000 233.000000 -104.000000)" xlink:href="#capacitor:shape13"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(-1.000000 0.000000 0.000000 1.000000 406.000000 -103.000000)" xlink:href="#capacitor:shape13"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(-1.000000 0.000000 0.000000 1.000000 568.000000 -104.000000)" xlink:href="#capacitor:shape13"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(-1.000000 0.000000 0.000000 1.000000 729.000000 -103.000000)" xlink:href="#capacitor:shape13"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(-1.000000 0.000000 0.000000 1.000000 1043.000000 -104.000000)" xlink:href="#capacitor:shape13"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(-1.000000 0.000000 0.000000 1.000000 1204.000000 -103.000000)" xlink:href="#capacitor:shape13"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(-1.000000 0.000000 0.000000 1.000000 1361.000000 -103.000000)" xlink:href="#capacitor:shape13"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(-1.000000 0.000000 0.000000 1.000000 1522.000000 -102.000000)" xlink:href="#capacitor:shape13"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(-1.000000 0.000000 0.000000 1.000000 1676.000000 -101.000000)" xlink:href="#capacitor:shape13"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(-1.000000 0.000000 0.000000 1.000000 1837.000000 -100.000000)" xlink:href="#capacitor:shape13"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1340.000000 -382.000000)" xlink:href="#capacitor:shape13"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1455.000000 -634.000000)" xlink:href="#capacitor:shape13"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_29eca50" refnum="0">
    <use class="BV-0KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 239.500000 -752.993333)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28e1ca0" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 119.500000 -29.500000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29fb8b0" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 217.500000 15.993333)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a166f0" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 292.500000 -28.500000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a18a10" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 390.500000 16.993333)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2952920" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 461.500000 -29.500000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29fc860" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 552.500000 15.993333)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_295e6b0" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 621.500000 -28.500000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2960df0" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 713.500000 16.993333)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2962f60" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 929.500000 -29.500000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2901430" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 1027.500000 15.993333)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29125e0" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 1098.500000 -28.500000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29152d0" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 1188.500000 16.993333)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_294efe0" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 1256.500000 -28.500000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2951cf0" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 1345.500000 16.993333)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2837520" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 1416.500000 -27.500000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_283a0a0" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 1506.500000 17.993333)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28f4660" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 1660.500000 18.993333)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_290d8e0" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 1730.500000 -25.500000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2910560" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 1821.500000 19.993333)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28b6120" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 1571.500000 -26.500000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28fe920" refnum="0">
    <use class="BV-0KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 1471.500000 -753.993333)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-0KV" id="g_29e5a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="166,-485 166,-465 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_28c9f00@0" Pin0InfoVect0LinkObjId="g_28c9f00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28df9c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="166,-485 166,-465 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_28c2c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="165,-432 165,-384 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_28c9f00@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_28df9c0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28c9f00_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="165,-432 165,-384 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28b1f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="165,-366 165,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="25342@0" Pin0InfoVect0LinkObjId="g_29c5940_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28df9c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="165,-366 165,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2994fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="166,-528 166,-502 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_28df9c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28df9c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="166,-528 166,-502 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29c5560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="272,-461 272,-441 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25379@1" ObjectIDZND0="25347@1" Pin0InfoVect0LinkObjId="SW-142787_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-142788_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="272,-461 272,-441 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29c5750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="272,-414 272,-386 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25347@0" ObjectIDZND0="25378@1" Pin0InfoVect0LinkObjId="SW-142788_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-142787_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="272,-414 272,-386 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29c5940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="272,-367 272,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25378@0" ObjectIDZND0="25342@0" Pin0InfoVect0LinkObjId="g_28b1f70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-142788_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="272,-367 272,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29c6e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="233,-638 233,-614 272,-614 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="25379@x" ObjectIDZND1="g_29c5b30@0" Pin0InfoVect0LinkObjId="SW-142788_0" Pin0InfoVect1LinkObjId="g_29c5b30_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28df9c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="233,-638 233,-614 272,-614 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_29c7020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="233,-666 233,-682 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_29c7210@1" Pin0InfoVect0LinkObjId="g_29c7210_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28df9c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="233,-666 233,-682 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_29c7cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="233,-718 233,-735 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="earth" ObjectIDND0="g_29c7210@0" ObjectIDZND0="g_29eca50@0" Pin0InfoVect0LinkObjId="g_29eca50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29c7210_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="233,-718 233,-735 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29ed530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="439,-373 439,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="capacitor" BeginDevType2="switch" EndDevType0="busSection" ObjectIDND0="g_29ed720@0" ObjectIDND1="0@x" ObjectIDND2="25402@x" ObjectIDZND0="25342@0" Pin0InfoVect0LinkObjId="g_28b1f70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_29ed720_0" Pin1InfoVect1LinkObjId="g_28df9c0_0" Pin1InfoVect2LinkObjId="SW-142895_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="439,-373 439,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_29ee030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="485,-490 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="485,-490 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_29ee220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="424,-415 424,-437 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_29ee410@1" Pin0InfoVect0LinkObjId="g_29ee410_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28df9c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="424,-415 424,-437 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_28df7d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="424,-467 424,-495 390,-495 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_29ee410@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29ee410_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="424,-467 424,-495 390,-495 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_29f9d40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="125,-64 125,-47 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_28e1ca0@0" Pin0InfoVect0LinkObjId="g_28e1ca0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28df9c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="125,-64 125,-47 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29fa820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="223,-99 223,-123 185,-123 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="0@1" ObjectIDZND0="25381@x" ObjectIDZND1="g_28e1390@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-142797_0" Pin0InfoVect1LinkObjId="g_28e1390_0" Pin0InfoVect2LinkObjId="g_28df9c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28df9c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="223,-99 223,-123 185,-123 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_29faa10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="223,-71 223,-55 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_29fac00@1" Pin0InfoVect0LinkObjId="g_29fac00_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28df9c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="223,-71 223,-55 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_29fb6c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="223,-19 223,-2 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="earth" ObjectIDND0="g_29fac00@0" ObjectIDZND0="g_29fb8b0@0" Pin0InfoVect0LinkObjId="g_29fb8b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29fac00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="223,-19 223,-2 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2968150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="185,-296 185,-276 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25380@1" ObjectIDZND0="25348@1" Pin0InfoVect0LinkObjId="SW-142796_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-142797_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="185,-296 185,-276 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2968340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="185,-249 185,-221 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25348@0" ObjectIDZND0="25381@1" Pin0InfoVect0LinkObjId="SW-142797_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-142796_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="185,-249 185,-221 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2968530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="185,-310 185,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25380@0" ObjectIDZND0="25342@0" Pin0InfoVect0LinkObjId="g_28b1f70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-142797_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="185,-310 185,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a16ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="298,-63 298,-46 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_2a166f0@0" Pin0InfoVect0LinkObjId="g_2a166f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28df9c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="298,-63 298,-46 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a17980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="396,-98 396,-122 358,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="0@1" ObjectIDZND0="25383@x" ObjectIDZND1="g_2968a50@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-142806_0" Pin0InfoVect1LinkObjId="g_2968a50_0" Pin0InfoVect2LinkObjId="g_28df9c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28df9c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="396,-98 396,-122 358,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a17b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="396,-70 396,-54 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_2a17d60@1" Pin0InfoVect0LinkObjId="g_2a17d60_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28df9c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="396,-70 396,-54 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a18820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="396,-18 396,-1 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="earth" ObjectIDND0="g_2a17d60@0" ObjectIDZND0="g_2a18a10@0" Pin0InfoVect0LinkObjId="g_2a18a10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a17d60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="396,-18 396,-1 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29b4730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="358,-295 358,-275 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25382@1" ObjectIDZND0="25349@1" Pin0InfoVect0LinkObjId="SW-142805_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-142806_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="358,-295 358,-275 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29b4950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="358,-248 358,-220 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25349@0" ObjectIDZND0="25383@1" Pin0InfoVect0LinkObjId="SW-142806_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-142805_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="358,-248 358,-220 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29b4b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="358,-309 358,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25382@0" ObjectIDZND0="25342@0" Pin0InfoVect0LinkObjId="g_28b1f70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-142806_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="358,-309 358,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2953250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="467,-64 467,-47 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_2952920@0" Pin0InfoVect0LinkObjId="g_2952920_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28df9c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="467,-64 467,-47 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2953f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="558,-71 558,-55 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_2954130@1" Pin0InfoVect0LinkObjId="g_2954130_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28df9c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="558,-71 558,-55 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2954e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="558,-19 558,-2 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="earth" ObjectIDND0="g_2954130@0" ObjectIDZND0="g_29fc860@0" Pin0InfoVect0LinkObjId="g_29fc860_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2954130_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="558,-19 558,-2 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_295db40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="520,-296 520,-276 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25384@1" ObjectIDZND0="25350@1" Pin0InfoVect0LinkObjId="SW-142814_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-142815_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="520,-296 520,-276 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_295dd60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="520,-249 520,-221 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25350@0" ObjectIDZND0="25385@1" Pin0InfoVect0LinkObjId="SW-142815_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-142814_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="520,-249 520,-221 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_295df80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="520,-310 520,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25384@0" ObjectIDZND0="25342@0" Pin0InfoVect0LinkObjId="g_28b1f70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-142815_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="520,-310 520,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_295ee20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="627,-63 627,-46 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_295e6b0@0" Pin0InfoVect0LinkObjId="g_295e6b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28df9c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="627,-63 627,-46 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_295fab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="719,-98 719,-122 681,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="25387@x" ObjectIDZND1="0@x" ObjectIDZND2="g_29cdb80@0" Pin0InfoVect0LinkObjId="SW-142824_0" Pin0InfoVect1LinkObjId="g_28df9c0_0" Pin0InfoVect2LinkObjId="g_29cdb80_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28df9c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="719,-98 719,-122 681,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_295fcd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="719,-70 719,-54 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_295fef0@1" Pin0InfoVect0LinkObjId="g_295fef0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28df9c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="719,-70 719,-54 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2960bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="719,-18 719,-1 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="earth" ObjectIDND0="g_295fef0@0" ObjectIDZND0="g_2960df0@0" Pin0InfoVect0LinkObjId="g_2960df0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_295fef0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="719,-18 719,-1 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29ccf50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="681,-295 681,-275 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25386@1" ObjectIDZND0="25351@1" Pin0InfoVect0LinkObjId="SW-142823_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-142824_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="681,-295 681,-275 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29cd180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="681,-248 681,-220 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25351@0" ObjectIDZND0="25387@1" Pin0InfoVect0LinkObjId="SW-142824_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-142823_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="681,-248 681,-220 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29cd3e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="681,-309 681,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25386@0" ObjectIDZND0="25342@0" Pin0InfoVect0LinkObjId="g_28b1f70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-142824_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="681,-309 681,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2961a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="786,-221 756,-221 756,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25374@0" ObjectIDZND0="25342@0" Pin0InfoVect0LinkObjId="g_28b1f70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-142772_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="786,-221 756,-221 756,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2961cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="901,-240 901,-221 878,-221 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="25376@1" ObjectIDZND0="25375@0" Pin0InfoVect0LinkObjId="SW-142772_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-142773_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="901,-240 901,-221 878,-221 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2961f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="901,-309 901,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25376@0" ObjectIDZND0="25343@0" Pin0InfoVect0LinkObjId="g_29b8f90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-142773_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="901,-309 901,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_29639f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="935,-64 935,-47 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_2962f60@0" Pin0InfoVect0LinkObjId="g_2962f60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28df9c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="935,-64 935,-47 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2964870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1033,-99 1033,-123 995,-123 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="0@1" ObjectIDZND0="25389@x" ObjectIDZND1="g_29621b0@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-142833_0" Pin0InfoVect1LinkObjId="g_29621b0_0" Pin0InfoVect2LinkObjId="g_28df9c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28df9c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1033,-99 1033,-123 995,-123 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2964ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1033,-71 1033,-55 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_2964d30@1" Pin0InfoVect0LinkObjId="g_2964d30_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28df9c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1033,-71 1033,-55 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_29011d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1033,-19 1033,-2 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="earth" ObjectIDND0="g_2964d30@0" ObjectIDZND0="g_2901430@0" Pin0InfoVect0LinkObjId="g_2901430_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2964d30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1033,-19 1033,-2 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29b8ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="995,-296 995,-276 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25388@1" ObjectIDZND0="25352@1" Pin0InfoVect0LinkObjId="SW-142832_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-142833_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="995,-296 995,-276 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29b8d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="995,-249 995,-221 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25352@0" ObjectIDZND0="25389@1" Pin0InfoVect0LinkObjId="SW-142833_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-142832_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="995,-249 995,-221 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29b8f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="995,-310 995,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25388@0" ObjectIDZND0="25343@0" Pin0InfoVect0LinkObjId="g_2961f50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-142833_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="995,-310 995,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29123f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1157,-67 1152,-67 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="capacitor" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@x" ObjectIDND1="25391@x" ObjectIDND2="0@x" ObjectIDZND0="0@x" ObjectIDZND1="25391@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_28df9c0_0" Pin0InfoVect1LinkObjId="SW-142842_0" Pin0InfoVect2LinkObjId="g_28df9c0_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_28df9c0_0" Pin1InfoVect1LinkObjId="SW-142842_0" Pin1InfoVect2LinkObjId="g_28df9c0_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1157,-67 1152,-67 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2912e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1104,-63 1104,-46 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_29125e0@0" Pin0InfoVect0LinkObjId="g_29125e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28df9c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1104,-63 1104,-46 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2913cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1194,-98 1194,-122 1156,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" EndDevType1="capacitor" EndDevType2="switch" ObjectIDND0="0@1" ObjectIDZND0="25391@x" ObjectIDZND1="0@x" ObjectIDZND2="25391@x" Pin0InfoVect0LinkObjId="SW-142842_0" Pin0InfoVect1LinkObjId="g_28df9c0_0" Pin0InfoVect2LinkObjId="SW-142842_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28df9c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1194,-98 1194,-122 1156,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2913f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1194,-70 1194,-54 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_2914170@1" Pin0InfoVect0LinkObjId="g_2914170_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28df9c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1194,-70 1194,-54 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2915070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1194,-18 1194,-1 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="earth" ObjectIDND0="g_2914170@0" ObjectIDZND0="g_29152d0@0" Pin0InfoVect0LinkObjId="g_29152d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2914170_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1194,-18 1194,-1 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29c2c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1156,-295 1156,-275 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25390@1" ObjectIDZND0="25353@1" Pin0InfoVect0LinkObjId="SW-142841_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-142842_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1156,-295 1156,-275 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29c2ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1156,-248 1156,-220 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25353@0" ObjectIDZND0="25391@1" Pin0InfoVect0LinkObjId="SW-142842_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-142841_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1156,-248 1156,-220 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29c3100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1156,-309 1156,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25390@0" ObjectIDZND0="25343@0" Pin0InfoVect0LinkObjId="g_2961f50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-142842_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1156,-309 1156,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_294e670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1157,-67 1157,10 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="switch" BeginDevType2="switch" ObjectIDND0="0@x" ObjectIDND1="25391@x" ObjectIDND2="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_28df9c0_0" Pin1InfoVect1LinkObjId="SW-142842_0" Pin1InfoVect2LinkObjId="g_28df9c0_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1157,-67 1157,10 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_294f850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1262,-63 1262,-46 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_294efe0@0" Pin0InfoVect0LinkObjId="g_294efe0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28df9c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1262,-63 1262,-46 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29506d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1351,-98 1351,-122 1313,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="25393@x" ObjectIDZND1="0@x" ObjectIDZND2="g_28f1a00@0" Pin0InfoVect0LinkObjId="SW-142851_0" Pin0InfoVect1LinkObjId="g_28df9c0_0" Pin0InfoVect2LinkObjId="g_28f1a00_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28df9c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1351,-98 1351,-122 1313,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2950930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1351,-70 1351,-54 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_2950b90@1" Pin0InfoVect0LinkObjId="g_2950b90_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28df9c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1351,-70 1351,-54 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2951a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1351,-18 1351,-1 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="earth" ObjectIDND0="g_2950b90@0" ObjectIDZND0="g_2951cf0@0" Pin0InfoVect0LinkObjId="g_2951cf0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2950b90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1351,-18 1351,-1 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_298f530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1313,-295 1313,-275 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25392@1" ObjectIDZND0="25354@1" Pin0InfoVect0LinkObjId="SW-142850_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-142851_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1313,-295 1313,-275 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_298f790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1313,-248 1313,-220 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25354@0" ObjectIDZND0="25393@1" Pin0InfoVect0LinkObjId="SW-142851_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-142850_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1313,-248 1313,-220 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_298f9f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1313,-309 1313,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25392@0" ObjectIDZND0="25343@0" Pin0InfoVect0LinkObjId="g_2961f50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-142851_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1313,-309 1313,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2837d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1422,-62 1422,-45 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_2837520@0" Pin0InfoVect0LinkObjId="g_2837520_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28df9c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1422,-62 1422,-45 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2838a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1512,-97 1512,-121 1474,-121 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="25395@x" ObjectIDZND1="0@x" ObjectIDZND2="g_28f03d0@0" Pin0InfoVect0LinkObjId="SW-142860_0" Pin0InfoVect1LinkObjId="g_28df9c0_0" Pin0InfoVect2LinkObjId="g_28f03d0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28df9c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1512,-97 1512,-121 1474,-121 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2838ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1512,-69 1512,-53 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_2838f40@1" Pin0InfoVect0LinkObjId="g_2838f40_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28df9c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1512,-69 1512,-53 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2839e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1512,-17 1512,0 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="earth" ObjectIDND0="g_2838f40@0" ObjectIDZND0="g_283a0a0@0" Pin0InfoVect0LinkObjId="g_283a0a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2838f40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1512,-17 1512,0 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28ef680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1474,-294 1474,-274 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25394@1" ObjectIDZND0="25355@1" Pin0InfoVect0LinkObjId="SW-142859_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-142860_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1474,-294 1474,-274 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28ef8e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1474,-247 1474,-219 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25355@0" ObjectIDZND0="25395@1" Pin0InfoVect0LinkObjId="SW-142860_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-142859_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1474,-247 1474,-219 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28efb40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1474,-308 1474,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25394@0" ObjectIDZND0="25343@0" Pin0InfoVect0LinkObjId="g_2961f50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-142860_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1474,-308 1474,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28f3040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1666,-96 1666,-120 1628,-120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="25397@x" ObjectIDZND1="0@x" ObjectIDZND2="g_28b5620@0" Pin0InfoVect0LinkObjId="SW-142869_0" Pin0InfoVect1LinkObjId="g_28df9c0_0" Pin0InfoVect2LinkObjId="g_28b5620_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28df9c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1666,-96 1666,-120 1628,-120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_28f32a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1666,-68 1666,-52 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_28f3500@1" Pin0InfoVect0LinkObjId="g_28f3500_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28df9c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1666,-68 1666,-52 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_28f4400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1666,-16 1666,1 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="earth" ObjectIDND0="g_28f3500@0" ObjectIDZND0="g_28f4660@0" Pin0InfoVect0LinkObjId="g_28f4660_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28f3500_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1666,-16 1666,1 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_290cb90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1628,-293 1628,-273 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25396@1" ObjectIDZND0="25356@1" Pin0InfoVect0LinkObjId="SW-142868_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-142869_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1628,-293 1628,-273 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_290cdf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1628,-246 1628,-218 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25356@0" ObjectIDZND0="25397@1" Pin0InfoVect0LinkObjId="SW-142869_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-142868_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1628,-246 1628,-218 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_290d050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1628,-307 1628,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25396@0" ObjectIDZND0="25343@0" Pin0InfoVect0LinkObjId="g_2961f50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-142869_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1628,-307 1628,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_290e120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1736,-60 1736,-43 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_290d8e0@0" Pin0InfoVect0LinkObjId="g_290d8e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28df9c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1736,-60 1736,-43 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_290ef40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1827,-95 1827,-119 1789,-119 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="25399@x" ObjectIDZND1="0@x" ObjectIDZND2="g_28b4450@0" Pin0InfoVect0LinkObjId="SW-142878_0" Pin0InfoVect1LinkObjId="g_28df9c0_0" Pin0InfoVect2LinkObjId="g_28b4450_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28df9c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1827,-95 1827,-119 1789,-119 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_290f1a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1827,-67 1827,-51 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_290f400@1" Pin0InfoVect0LinkObjId="g_290f400_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28df9c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1827,-67 1827,-51 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2910300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1827,-15 1827,2 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="earth" ObjectIDND0="g_290f400@0" ObjectIDZND0="g_2910560@0" Pin0InfoVect0LinkObjId="g_2910560_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_290f400_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1827,-15 1827,2 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28b3700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1789,-292 1789,-272 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25398@1" ObjectIDZND0="25357@1" Pin0InfoVect0LinkObjId="SW-142877_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-142878_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1789,-292 1789,-272 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28b3960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1789,-245 1789,-217 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25357@0" ObjectIDZND0="25399@1" Pin0InfoVect0LinkObjId="SW-142878_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-142877_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1789,-245 1789,-217 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28b3bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1789,-306 1789,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25398@0" ObjectIDZND0="25343@0" Pin0InfoVect0LinkObjId="g_2961f50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-142878_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1789,-306 1789,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_28b6bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1577,-61 1577,-44 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_28b6120@0" Pin0InfoVect0LinkObjId="g_28b6120_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28df9c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1577,-61 1577,-44 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_28b7bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1411,-490 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1411,-490 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_28b7e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1350,-415 1350,-433 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_28b8080@1" Pin0InfoVect0LinkObjId="g_28b8080_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28df9c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1350,-415 1350,-433 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_28b9bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1350,-467 1350,-495 1316,-495 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_28b8080@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28b8080_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1350,-467 1350,-495 1316,-495 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28fae90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1504,-462 1504,-442 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25401@1" ObjectIDZND0="25358@1" Pin0InfoVect0LinkObjId="SW-142886_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-142887_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1504,-462 1504,-442 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28fb0f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1504,-415 1504,-387 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25358@0" ObjectIDZND0="25400@1" Pin0InfoVect0LinkObjId="SW-142887_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-142886_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1504,-415 1504,-387 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28fb980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1504,-368 1504,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25400@0" ObjectIDZND0="25343@0" Pin0InfoVect0LinkObjId="g_2961f50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-142887_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1504,-368 1504,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28fd300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1465,-639 1465,-615 1504,-615 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="25401@x" ObjectIDZND1="g_28fbb70@0" Pin0InfoVect0LinkObjId="SW-142887_0" Pin0InfoVect1LinkObjId="g_28fbb70_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28df9c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1465,-639 1465,-615 1504,-615 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_28fd560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1465,-667 1465,-683 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_28fd7c0@1" Pin0InfoVect0LinkObjId="g_28fd7c0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28df9c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1465,-667 1465,-683 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_28fe6c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1465,-719 1465,-736 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="earth" ObjectIDND0="g_28fd7c0@0" ObjectIDZND0="g_28fe920@0" Pin0InfoVect0LinkObjId="g_28fe920_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28fd7c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1465,-719 1465,-736 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_28d8a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1677,-485 1677,-465 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_28ca8d0@0" Pin0InfoVect0LinkObjId="g_28ca8d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28df9c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1677,-485 1677,-465 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_28d8cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1676,-432 1676,-384 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_28ca8d0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_28df9c0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28ca8d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1676,-432 1676,-384 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28d94e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1676,-366 1676,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="25343@0" Pin0InfoVect0LinkObjId="g_2961f50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28df9c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1676,-366 1676,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_28d96d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1677,-528 1677,-502 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_28df9c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28df9c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1677,-528 1677,-502 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28659f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="803,-221 819,-221 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25374@1" ObjectIDZND0="25346@1" Pin0InfoVect0LinkObjId="SW-142771_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-142772_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="803,-221 819,-221 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_286a7c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="846,-221 861,-221 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25346@0" ObjectIDZND0="25375@1" Pin0InfoVect0LinkObjId="SW-142772_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-142771_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="846,-221 861,-221 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29a2890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="185,-205 185,-123 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="25381@0" ObjectIDZND0="0@x" ObjectIDZND1="g_28e1390@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_28df9c0_0" Pin0InfoVect1LinkObjId="g_28e1390_0" Pin0InfoVect2LinkObjId="g_28df9c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-142797_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="185,-205 185,-123 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29a2ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="185,-123 185,8 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="switch" BeginDevType2="lightningRod" ObjectIDND0="0@x" ObjectIDND1="25381@x" ObjectIDND2="g_28e1390@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_28df9c0_0" Pin1InfoVect1LinkObjId="SW-142797_0" Pin1InfoVect2LinkObjId="g_28e1390_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="185,-123 185,8 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29a2cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="155,-99 155,-123 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="capacitor" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_28e1390@0" ObjectIDZND0="0@x" ObjectIDZND1="25381@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_28df9c0_0" Pin0InfoVect1LinkObjId="SW-142797_0" Pin0InfoVect2LinkObjId="g_28df9c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28e1390_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="155,-99 155,-123 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29a37c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="185,-123 155,-123 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="25381@x" ObjectIDZND0="g_28e1390@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_28e1390_0" Pin0InfoVect1LinkObjId="g_28df9c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_28df9c0_0" Pin1InfoVect1LinkObjId="SW-142797_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="185,-123 155,-123 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29a3a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="155,-123 125,-123 125,-99 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="capacitor" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_28e1390@0" ObjectIDND1="0@x" ObjectIDND2="25381@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_28df9c0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_28e1390_0" Pin1InfoVect1LinkObjId="g_28df9c0_0" Pin1InfoVect2LinkObjId="SW-142797_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="155,-123 125,-123 125,-99 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29a4730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="358,-203 358,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="25383@0" ObjectIDZND0="0@x" ObjectIDZND1="g_2968a50@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_28df9c0_0" Pin0InfoVect1LinkObjId="g_2968a50_0" Pin0InfoVect2LinkObjId="g_28df9c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-142806_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="358,-203 358,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29a4990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="358,-122 358,10 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" BeginDevType2="lightningRod" ObjectIDND0="25383@x" ObjectIDND1="0@x" ObjectIDND2="g_2968a50@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-142806_0" Pin1InfoVect1LinkObjId="g_28df9c0_0" Pin1InfoVect2LinkObjId="g_2968a50_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="358,-122 358,10 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29a4bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="328,-98 328,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="capacitor" EndDevType2="switch" ObjectIDND0="g_2968a50@0" ObjectIDZND0="25383@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-142806_0" Pin0InfoVect1LinkObjId="g_28df9c0_0" Pin0InfoVect2LinkObjId="g_28df9c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2968a50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="328,-98 328,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29a56e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="358,-122 328,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="25383@x" ObjectIDND1="0@x" ObjectIDZND0="g_2968a50@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_2968a50_0" Pin0InfoVect1LinkObjId="g_28df9c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-142806_0" Pin1InfoVect1LinkObjId="g_28df9c0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="358,-122 328,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29a5940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="328,-122 298,-122 298,-98 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="capacitor" EndDevType0="switch" ObjectIDND0="g_2968a50@0" ObjectIDND1="25383@x" ObjectIDND2="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_28df9c0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2968a50_0" Pin1InfoVect1LinkObjId="SW-142806_0" Pin1InfoVect2LinkObjId="g_28df9c0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="328,-122 298,-122 298,-98 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29a5ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="558,-99 558,-123 520,-123 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="25385@x" ObjectIDZND1="0@x" ObjectIDZND2="g_2951f20@0" Pin0InfoVect0LinkObjId="SW-142815_0" Pin0InfoVect1LinkObjId="g_28df9c0_0" Pin0InfoVect2LinkObjId="g_2951f20_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28df9c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="558,-99 558,-123 520,-123 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29a68b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="520,-204 520,-123 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="25385@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="g_2951f20@0" Pin0InfoVect0LinkObjId="g_28df9c0_0" Pin0InfoVect1LinkObjId="g_28df9c0_0" Pin0InfoVect2LinkObjId="g_2951f20_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-142815_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="520,-204 520,-123 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29a6b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="520,-123 520,9 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="switch" BeginDevType2="switch" ObjectIDND0="0@x" ObjectIDND1="25385@x" ObjectIDND2="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_28df9c0_0" Pin1InfoVect1LinkObjId="SW-142815_0" Pin1InfoVect2LinkObjId="g_28df9c0_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="520,-123 520,9 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29a6d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="497,-123 467,-123 467,-99 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="25385@x" ObjectIDND2="g_2951f20@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_28df9c0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_28df9c0_0" Pin1InfoVect1LinkObjId="SW-142815_0" Pin1InfoVect2LinkObjId="g_2951f20_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="497,-123 467,-123 467,-99 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29a7a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="681,10 681,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="capacitor" EndDevType1="switch" EndDevType2="switch" ObjectIDZND0="0@x" ObjectIDZND1="25387@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_28df9c0_0" Pin0InfoVect1LinkObjId="SW-142824_0" Pin0InfoVect2LinkObjId="g_28df9c0_0" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="681,10 681,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29a7ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="681,-122 681,-203 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="g_29cdb80@0" ObjectIDZND0="25387@0" Pin0InfoVect0LinkObjId="SW-142824_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_28df9c0_0" Pin1InfoVect1LinkObjId="g_28df9c0_0" Pin1InfoVect2LinkObjId="g_29cdb80_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="681,-122 681,-203 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29a7f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="657,-122 627,-122 627,-98 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="25387@x" ObjectIDND2="g_29cdb80@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_28df9c0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_28df9c0_0" Pin1InfoVect1LinkObjId="SW-142824_0" Pin1InfoVect2LinkObjId="g_29cdb80_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="657,-122 627,-122 627,-98 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29a8c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="995,-204 995,-123 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="25389@0" ObjectIDZND0="0@x" ObjectIDZND1="g_29621b0@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_28df9c0_0" Pin0InfoVect1LinkObjId="g_29621b0_0" Pin0InfoVect2LinkObjId="g_28df9c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-142833_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="995,-204 995,-123 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29a8eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="995,-123 995,9 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="switch" BeginDevType2="lightningRod" ObjectIDND0="0@x" ObjectIDND1="25389@x" ObjectIDND2="g_29621b0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_28df9c0_0" Pin1InfoVect1LinkObjId="SW-142833_0" Pin1InfoVect2LinkObjId="g_29621b0_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="995,-123 995,9 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29a9110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="965,-123 995,-123 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="capacitor" EndDevType1="switch" ObjectIDND0="g_29621b0@0" ObjectIDND1="0@x" ObjectIDZND0="0@x" ObjectIDZND1="25389@x" Pin0InfoVect0LinkObjId="g_28df9c0_0" Pin0InfoVect1LinkObjId="SW-142833_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_29621b0_0" Pin1InfoVect1LinkObjId="g_28df9c0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="965,-123 995,-123 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29a9c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="965,-99 965,-123 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="capacitor" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_29621b0@0" ObjectIDZND0="0@x" ObjectIDZND1="25389@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_28df9c0_0" Pin0InfoVect1LinkObjId="SW-142833_0" Pin0InfoVect2LinkObjId="g_28df9c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29621b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="965,-99 965,-123 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29a9e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="965,-123 935,-123 935,-99 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="25389@x" ObjectIDND2="g_29621b0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_28df9c0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_28df9c0_0" Pin1InfoVect1LinkObjId="SW-142833_0" Pin1InfoVect2LinkObjId="g_29621b0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="965,-123 935,-123 935,-99 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29aab70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1156,-203 1156,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" EndDevType1="capacitor" EndDevType2="switch" ObjectIDND0="25391@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="25391@x" Pin0InfoVect0LinkObjId="g_28df9c0_0" Pin0InfoVect1LinkObjId="g_28df9c0_0" Pin0InfoVect2LinkObjId="SW-142842_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-142842_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1156,-203 1156,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29aadd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1156,-122 1156,-67 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="capacitor" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@x" ObjectIDND1="25391@x" ObjectIDND2="0@x" ObjectIDZND0="0@x" ObjectIDZND1="25391@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_28df9c0_0" Pin0InfoVect1LinkObjId="SW-142842_0" Pin0InfoVect2LinkObjId="g_28df9c0_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_28df9c0_0" Pin1InfoVect1LinkObjId="SW-142842_0" Pin1InfoVect2LinkObjId="g_28df9c0_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1156,-122 1156,-67 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29abae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1313,-203 1313,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="25393@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="g_28f1a00@0" Pin0InfoVect0LinkObjId="g_28df9c0_0" Pin0InfoVect1LinkObjId="g_28df9c0_0" Pin0InfoVect2LinkObjId="g_28f1a00_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-142851_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1313,-203 1313,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29abd40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1313,-122 1313,10 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="switch" BeginDevType2="switch" ObjectIDND0="0@x" ObjectIDND1="25393@x" ObjectIDND2="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_28df9c0_0" Pin1InfoVect1LinkObjId="SW-142851_0" Pin1InfoVect2LinkObjId="g_28df9c0_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1313,-122 1313,10 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29aca50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1474,-202 1474,-121 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="25395@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="g_28f03d0@0" Pin0InfoVect0LinkObjId="g_28df9c0_0" Pin0InfoVect1LinkObjId="g_28df9c0_0" Pin0InfoVect2LinkObjId="g_28f03d0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-142860_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1474,-202 1474,-121 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_288d2a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1628,-201 1628,-120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="25397@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="g_28b5620@0" Pin0InfoVect0LinkObjId="g_28df9c0_0" Pin0InfoVect1LinkObjId="g_28df9c0_0" Pin0InfoVect2LinkObjId="g_28b5620_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-142869_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1628,-201 1628,-120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_288d500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1628,-120 1628,12 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="switch" BeginDevType2="switch" ObjectIDND0="0@x" ObjectIDND1="25397@x" ObjectIDND2="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_28df9c0_0" Pin1InfoVect1LinkObjId="SW-142869_0" Pin1InfoVect2LinkObjId="g_28df9c0_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1628,-120 1628,12 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_288e1c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1789,-200 1789,-119 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="25399@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="g_28b4450@0" Pin0InfoVect0LinkObjId="g_28df9c0_0" Pin0InfoVect1LinkObjId="g_28df9c0_0" Pin0InfoVect2LinkObjId="g_28b4450_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-142878_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1789,-200 1789,-119 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_288e420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1789,-119 1789,13 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="switch" BeginDevType2="switch" ObjectIDND0="0@x" ObjectIDND1="25399@x" ObjectIDND2="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_28df9c0_0" Pin1InfoVect1LinkObjId="SW-142878_0" Pin1InfoVect2LinkObjId="g_28df9c0_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1789,-119 1789,13 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_288e680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1474,11 1474,-121 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="capacitor" EndDevType1="switch" EndDevType2="switch" ObjectIDZND0="0@x" ObjectIDZND1="25395@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_28df9c0_0" Pin0InfoVect1LinkObjId="SW-142860_0" Pin0InfoVect2LinkObjId="g_28df9c0_0" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1474,11 1474,-121 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_288e8e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1134,-122 1104,-122 1104,-98 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="switch" BeginDevType2="capacitor" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="25391@x" ObjectIDND2="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_28df9c0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_28df9c0_0" Pin1InfoVect1LinkObjId="SW-142842_0" Pin1InfoVect2LinkObjId="g_28df9c0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1134,-122 1104,-122 1104,-98 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_288eb40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1292,-122 1262,-122 1262,-98 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="25393@x" ObjectIDND2="g_28f1a00@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_28df9c0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_28df9c0_0" Pin1InfoVect1LinkObjId="SW-142851_0" Pin1InfoVect2LinkObjId="g_28f1a00_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1292,-122 1262,-122 1262,-98 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_288eda0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1452,-121 1422,-121 1422,-97 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="25395@x" ObjectIDND2="g_28f03d0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_28df9c0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_28df9c0_0" Pin1InfoVect1LinkObjId="SW-142860_0" Pin1InfoVect2LinkObjId="g_28f03d0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1452,-121 1422,-121 1422,-97 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_288f000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1607,-120 1577,-120 1577,-96 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="25397@x" ObjectIDND2="g_28b5620@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_28df9c0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_28df9c0_0" Pin1InfoVect1LinkObjId="SW-142869_0" Pin1InfoVect2LinkObjId="g_28b5620_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1607,-120 1577,-120 1577,-96 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_288f260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1766,-119 1736,-119 1736,-95 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="25399@x" ObjectIDND2="g_28b4450@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_28df9c0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_28df9c0_0" Pin1InfoVect1LinkObjId="SW-142878_0" Pin1InfoVect2LinkObjId="g_28b4450_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1766,-119 1736,-119 1736,-95 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_288fd50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1366,-373 1366,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="capacitor" BeginDevType2="switch" EndDevType0="busSection" ObjectIDND0="g_28b6e10@0" ObjectIDND1="0@x" ObjectIDND2="25404@x" ObjectIDZND0="25343@0" Pin0InfoVect0LinkObjId="g_2961f50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_28b6e10_0" Pin1InfoVect1LinkObjId="g_28df9c0_0" Pin1InfoVect2LinkObjId="SW-142896_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1366,-373 1366,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2890580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1350,-373 1316,-373 1316,-449 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="busSection" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="25343@0" ObjectIDND2="25404@x" ObjectIDZND0="g_28b6e10@0" Pin0InfoVect0LinkObjId="g_28b6e10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_28df9c0_0" Pin1InfoVect1LinkObjId="g_2961f50_0" Pin1InfoVect2LinkObjId="SW-142896_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1350,-373 1316,-373 1316,-449 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2891070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1350,-387 1350,-373 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="lightningRod" EndDevType1="busSection" EndDevType2="switch" ObjectIDND0="0@1" ObjectIDZND0="g_28b6e10@0" ObjectIDZND1="25343@0" ObjectIDZND2="25404@x" Pin0InfoVect0LinkObjId="g_28b6e10_0" Pin0InfoVect1LinkObjId="g_2961f50_0" Pin0InfoVect2LinkObjId="SW-142896_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28df9c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1350,-387 1350,-373 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28912d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1350,-373 1366,-373 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="capacitor" EndDevType0="busSection" EndDevType1="switch" ObjectIDND0="g_28b6e10@0" ObjectIDND1="0@x" ObjectIDZND0="25343@0" ObjectIDZND1="25404@x" Pin0InfoVect0LinkObjId="g_2961f50_0" Pin0InfoVect1LinkObjId="SW-142896_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_28b6e10_0" Pin1InfoVect1LinkObjId="g_28df9c0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1350,-373 1366,-373 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2891fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1504,-778 1504,-615 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="capacitor" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDZND0="0@x" ObjectIDZND1="25401@x" ObjectIDZND2="g_28fbb70@0" Pin0InfoVect0LinkObjId="g_28df9c0_0" Pin0InfoVect1LinkObjId="SW-142887_0" Pin0InfoVect2LinkObjId="g_28fbb70_0" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1504,-778 1504,-615 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2892240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1504,-615 1504,-479 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_28fbb70@0" ObjectIDZND0="25401@0" Pin0InfoVect0LinkObjId="SW-142887_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_28df9c0_0" Pin1InfoVect1LinkObjId="g_28fbb70_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1504,-615 1504,-479 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2892a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="424,-373 390,-373 390,-449 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="busSection" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="25342@0" ObjectIDND2="25402@x" ObjectIDZND0="g_29ed720@0" Pin0InfoVect0LinkObjId="g_29ed720_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_28df9c0_0" Pin1InfoVect1LinkObjId="g_28b1f70_0" Pin1InfoVect2LinkObjId="SW-142895_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="424,-373 390,-373 390,-449 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2893560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="424,-387 424,-373 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="lightningRod" EndDevType1="busSection" EndDevType2="switch" ObjectIDND0="0@1" ObjectIDZND0="g_29ed720@0" ObjectIDZND1="25342@0" ObjectIDZND2="25402@x" Pin0InfoVect0LinkObjId="g_29ed720_0" Pin0InfoVect1LinkObjId="g_28b1f70_0" Pin0InfoVect2LinkObjId="SW-142895_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28df9c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="424,-387 424,-373 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28937c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="424,-373 439,-373 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="capacitor" EndDevType0="busSection" EndDevType1="switch" ObjectIDND0="g_29ed720@0" ObjectIDND1="0@x" ObjectIDZND0="25342@0" ObjectIDZND1="25402@x" Pin0InfoVect0LinkObjId="g_28b1f70_0" Pin0InfoVect1LinkObjId="SW-142895_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_29ed720_0" Pin1InfoVect1LinkObjId="g_28df9c0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="424,-373 439,-373 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28944d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="272,-777 272,-614 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="capacitor" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDZND0="0@0" ObjectIDZND1="25379@x" ObjectIDZND2="g_29c5b30@0" Pin0InfoVect0LinkObjId="g_28df9c0_0" Pin0InfoVect1LinkObjId="SW-142788_0" Pin0InfoVect2LinkObjId="g_29c5b30_0" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="272,-777 272,-614 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2894730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="272,-614 272,-478 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDND1="g_29c5b30@0" ObjectIDZND0="25379@0" Pin0InfoVect0LinkObjId="SW-142788_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_28df9c0_0" Pin1InfoVect1LinkObjId="g_29c5b30_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="272,-614 272,-478 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28e4db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="272,-614 301,-614 301,-638 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDND1="25379@x" ObjectIDZND0="g_29c5b30@0" Pin0InfoVect0LinkObjId="g_29c5b30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_28df9c0_0" Pin1InfoVect1LinkObjId="SW-142788_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="272,-614 301,-614 301,-638 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28e96f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="474,-564 474,-512 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" ObjectIDND0="g_28df9c0@0" ObjectIDZND0="25402@1" Pin0InfoVect0LinkObjId="SW-142895_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28df9c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="474,-564 474,-512 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28e9950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="474,-443 474,-373 439,-373 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" EndDevType1="lightningRod" EndDevType2="capacitor" ObjectIDND0="25402@0" ObjectIDZND0="25342@0" ObjectIDZND1="g_29ed720@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_28b1f70_0" Pin0InfoVect1LinkObjId="g_29ed720_0" Pin0InfoVect2LinkObjId="g_28df9c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-142895_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="474,-443 474,-373 439,-373 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28c3ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1366,-373 1400,-373 1400,-441 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="lightningRod" BeginDevType2="capacitor" EndDevType0="switch" ObjectIDND0="25343@0" ObjectIDND1="g_28b6e10@0" ObjectIDND2="0@x" ObjectIDZND0="25404@0" Pin0InfoVect0LinkObjId="SW-142896_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2961f50_0" Pin1InfoVect1LinkObjId="g_28b6e10_0" Pin1InfoVect2LinkObjId="g_28df9c0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1366,-373 1400,-373 1400,-441 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28c3d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1400,-510 1400,-564 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="25404@1" ObjectIDZND0="g_28b9e50@0" Pin0InfoVect0LinkObjId="g_28b9e50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-142896_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1400,-510 1400,-564 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28c3f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1504,-615 1533,-615 1533,-639 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="25401@x" ObjectIDZND0="g_28fbb70@0" Pin0InfoVect0LinkObjId="g_28fbb70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_28df9c0_0" Pin1InfoVect1LinkObjId="SW-142887_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1504,-615 1533,-615 1533,-639 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28c4a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="497,-123 520,-123 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="capacitor" EndDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="g_2951f20@0" ObjectIDZND0="0@x" ObjectIDZND1="25385@x" Pin0InfoVect0LinkObjId="g_28df9c0_0" Pin0InfoVect1LinkObjId="SW-142815_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_28df9c0_0" Pin1InfoVect1LinkObjId="g_2951f20_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="497,-123 520,-123 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28c5550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="681,-122 657,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="0@x" ObjectIDND1="25387@x" ObjectIDZND0="0@x" ObjectIDZND1="g_29cdb80@0" Pin0InfoVect0LinkObjId="g_28df9c0_0" Pin0InfoVect1LinkObjId="g_29cdb80_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_28df9c0_0" Pin1InfoVect1LinkObjId="SW-142824_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="681,-122 657,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28c57b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="490,-99 490,-123 497,-123 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="capacitor" EndDevType2="switch" ObjectIDND0="g_2951f20@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="25385@x" Pin0InfoVect0LinkObjId="g_28df9c0_0" Pin0InfoVect1LinkObjId="g_28df9c0_0" Pin0InfoVect2LinkObjId="SW-142815_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2951f20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="490,-99 490,-123 497,-123 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28c5a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="651,-98 651,-122 657,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="capacitor" EndDevType2="switch" ObjectIDND0="g_29cdb80@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="25387@x" Pin0InfoVect0LinkObjId="g_28df9c0_0" Pin0InfoVect1LinkObjId="g_28df9c0_0" Pin0InfoVect2LinkObjId="SW-142824_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29cdb80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="651,-98 651,-122 657,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28c6500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1134,-122 1156,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="capacitor" EndDevType1="switch" EndDevType2="capacitor" ObjectIDND0="0@x" ObjectIDND1="g_294c360@0" ObjectIDZND0="0@x" ObjectIDZND1="25391@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_28df9c0_0" Pin0InfoVect1LinkObjId="SW-142842_0" Pin0InfoVect2LinkObjId="g_28df9c0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_28df9c0_0" Pin1InfoVect1LinkObjId="g_294c360_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1134,-122 1156,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28c6760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1126,-98 1126,-122 1134,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="capacitor" EndDevType2="switch" ObjectIDND0="g_294c360@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="25391@x" Pin0InfoVect0LinkObjId="g_28df9c0_0" Pin0InfoVect1LinkObjId="g_28df9c0_0" Pin0InfoVect2LinkObjId="SW-142842_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_294c360_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1126,-98 1126,-122 1134,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28c7250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1292,-122 1313,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="capacitor" EndDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="g_28f1a00@0" ObjectIDZND0="0@x" ObjectIDZND1="25393@x" Pin0InfoVect0LinkObjId="g_28df9c0_0" Pin0InfoVect1LinkObjId="SW-142851_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_28df9c0_0" Pin1InfoVect1LinkObjId="g_28f1a00_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1292,-122 1313,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28c74b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1283,-98 1283,-122 1292,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="capacitor" EndDevType2="switch" ObjectIDND0="g_28f1a00@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="25393@x" Pin0InfoVect0LinkObjId="g_28df9c0_0" Pin0InfoVect1LinkObjId="g_28df9c0_0" Pin0InfoVect2LinkObjId="SW-142851_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28f1a00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1283,-98 1283,-122 1292,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28c7fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1452,-121 1474,-121 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="capacitor" EndDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="g_28f03d0@0" ObjectIDZND0="0@x" ObjectIDZND1="25395@x" Pin0InfoVect0LinkObjId="g_28df9c0_0" Pin0InfoVect1LinkObjId="SW-142860_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_28df9c0_0" Pin1InfoVect1LinkObjId="g_28f03d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1452,-121 1474,-121 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28c8200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1444,-97 1444,-121 1452,-121 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="capacitor" EndDevType2="switch" ObjectIDND0="g_28f03d0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="25395@x" Pin0InfoVect0LinkObjId="g_28df9c0_0" Pin0InfoVect1LinkObjId="g_28df9c0_0" Pin0InfoVect2LinkObjId="SW-142860_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28f03d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1444,-97 1444,-121 1452,-121 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28c8cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1607,-120 1628,-120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="capacitor" EndDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="g_28b5620@0" ObjectIDZND0="0@x" ObjectIDZND1="25397@x" Pin0InfoVect0LinkObjId="g_28df9c0_0" Pin0InfoVect1LinkObjId="SW-142869_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_28df9c0_0" Pin1InfoVect1LinkObjId="g_28b5620_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1607,-120 1628,-120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28c8f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1598,-96 1598,-120 1607,-120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="capacitor" EndDevType2="switch" ObjectIDND0="g_28b5620@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="25397@x" Pin0InfoVect0LinkObjId="g_28df9c0_0" Pin0InfoVect1LinkObjId="g_28df9c0_0" Pin0InfoVect2LinkObjId="SW-142869_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28b5620_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1598,-96 1598,-120 1607,-120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28c9a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1766,-119 1789,-119 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="capacitor" EndDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="g_28b4450@0" ObjectIDZND0="0@x" ObjectIDZND1="25399@x" Pin0InfoVect0LinkObjId="g_28df9c0_0" Pin0InfoVect1LinkObjId="SW-142878_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_28df9c0_0" Pin1InfoVect1LinkObjId="g_28b4450_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1766,-119 1789,-119 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28c9ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1759,-95 1759,-119 1766,-119 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="capacitor" EndDevType2="switch" ObjectIDND0="g_28b4450@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="25399@x" Pin0InfoVect0LinkObjId="g_28df9c0_0" Pin0InfoVect1LinkObjId="g_28df9c0_0" Pin0InfoVect2LinkObjId="SW-142878_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28b4450_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1759,-95 1759,-119 1766,-119 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="25342" cx="165" cy="-330" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25342" cx="272" cy="-330" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25342" cx="185" cy="-330" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25342" cx="358" cy="-330" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25342" cx="520" cy="-330" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25342" cx="681" cy="-330" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25343" cx="901" cy="-330" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25343" cx="995" cy="-330" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25343" cx="1156" cy="-330" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25343" cx="1313" cy="-330" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25343" cx="1474" cy="-330" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25343" cx="1628" cy="-330" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25343" cx="1789" cy="-330" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25343" cx="1504" cy="-330" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25343" cx="1676" cy="-330" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25342" cx="756" cy="-330" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25343" cx="1366" cy="-330" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25342" cx="439" cy="-330" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-142525" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -47.000000 -849.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25326" ObjectName="DYN-CX_ZD"/>
     <cge:Meas_Ref ObjectId="142525"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2763090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2763090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2763090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2763090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2763090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2763090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2763090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2763090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2763090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2763090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2763090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2763090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2763090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2763090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2763090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2763090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2763090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2763090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_29d9240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -788.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_29d9240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -788.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_29d9240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -788.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_29d9240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -788.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_29d9240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -788.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_29d9240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -788.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_29d9240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -788.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_26ad8e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -200.000000 -929.500000) translate(0,16)">庄甸开闭所</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_28c2e50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 112.000000 -455.000000) translate(0,15)">0811</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_29ed200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 210.000000 -802.000000) translate(0,15)">10kV庄甸Ⅲ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_28e0b00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 412.000000 -650.000000) translate(0,15)">10kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_28e0b00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 412.000000 -650.000000) translate(0,33)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_28e0e30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 128.000000 -614.000000) translate(0,15)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_28e10e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 116.000000 -595.000000) translate(0,15)">SC11-30kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2968720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 64.000000 -86.000000) translate(0,15)">08367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_29b4d90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 238.000000 -84.000000) translate(0,15)">08467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_295e1a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 406.000000 -84.000000) translate(0,15)">08567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_29cd640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 568.000000 -80.000000) translate(0,15)">08667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2957f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 114.000000 23.000000) translate(0,15)">10kV庄甸Ⅴ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2958550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 328.000000 22.000000) translate(0,15)">备用一</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2958760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 470.000000 17.000000) translate(0,15)">庄甸V回线T</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2958760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 470.000000 17.000000) translate(0,33)">药科院Ⅰ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2958bc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 612.000000 21.000000) translate(0,15)">10kV东升路Ⅱ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2911a90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 875.000000 -86.000000) translate(0,15)">09167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_29c3360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1044.000000 -81.000000) translate(0,15)">09267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_294cd00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 966.000000 19.000000) translate(0,15)">备用四</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_294e120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1083.000000 22.000000) translate(0,15)">10kV东升路Ⅲ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_298fc50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1207.000000 -80.000000) translate(0,15)">09367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_28efda0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1365.000000 -82.000000) translate(0,15)">09467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_28f0ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1284.000000 20.000000) translate(0,15)">备用六</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_28f15a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1429.000000 23.000000) translate(0,15)">东升路Ⅰ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_290d2b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1521.000000 -77.000000) translate(0,15)">09567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_28b3e20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1679.000000 -79.000000) translate(0,15)">09667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_28b4f50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1599.000000 22.000000) translate(0,15)">备用八</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_28fb350" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1413.000000 -486.000000) translate(0,15)">0902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_28ff3b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1442.000000 -803.000000) translate(0,15)">10kV庄甸Ⅳ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_28d8f50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1693.000000 -456.000000) translate(0,15)">0981</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_28dd5f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1001.000000 -362.000000) translate(0,15)">10kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_28ddb80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1328.000000 -648.000000) translate(0,15)">10kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_28ddb80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1328.000000 -648.000000) translate(0,33)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_28dddc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1626.000000 -595.000000) translate(0,15)">SC11-30kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_28de010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1638.000000 -614.000000) translate(0,15)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_28de240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 489.000000 -489.000000) translate(0,15)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_28de480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1723.000000 28.000000) translate(0,15)">10kV庄甸Ⅵ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2862460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 853.000000 -285.000000) translate(0,15)">0122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_286aa20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 557.000000 -361.000000) translate(0,15)">10kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_28ea630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 809.000000 -273.000000) translate(0,15)">分段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28cb2a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 293.000000 -438.000000) translate(0,12)">082</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28cb990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 205.000000 -270.000000) translate(0,12)">083</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28cbdb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 380.000000 -273.000000) translate(0,12)">084</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28cc0f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 539.000000 -275.000000) translate(0,12)">085</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28cc650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 703.000000 -271.000000) translate(0,12)">086</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28ccbb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 818.000000 -253.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28cd010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1019.000000 -272.000000) translate(0,12)">091</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28cd530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1174.000000 -270.000000) translate(0,12)">092</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28cd7b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1336.000000 -272.000000) translate(0,12)">093</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28cd9f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1494.000000 -272.000000) translate(0,12)">094</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28cdc30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1650.000000 -268.000000) translate(0,12)">095</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28cde70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1808.000000 -264.000000) translate(0,12)">096</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28ce0b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1531.000000 -437.000000) translate(0,12)">097</text>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 156.000000 -478.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 155.000000 -359.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-142788">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 262.000000 -454.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25379" ObjectName="SW-CX_ZD.CX_ZD_082XC1"/>
     <cge:Meas_Ref ObjectId="142788"/>
    <cge:TPSR_Ref TObjectID="25379"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-142788">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 262.000000 -360.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25378" ObjectName="SW-CX_ZD.CX_ZD_082XC"/>
     <cge:Meas_Ref ObjectId="142788"/>
    <cge:TPSR_Ref TObjectID="25378"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -0.978261 117.000000 -59.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-142797">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 175.000000 -286.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25380" ObjectName="SW-CX_ZD.CX_ZD_083XC"/>
     <cge:Meas_Ref ObjectId="142797"/>
    <cge:TPSR_Ref TObjectID="25380"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-142797">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 175.000000 -197.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25381" ObjectName="SW-CX_ZD.CX_ZD_083XC1"/>
     <cge:Meas_Ref ObjectId="142797"/>
    <cge:TPSR_Ref TObjectID="25381"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -0.978261 290.000000 -58.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-142806">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 348.000000 -285.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25382" ObjectName="SW-CX_ZD.CX_ZD_084XC"/>
     <cge:Meas_Ref ObjectId="142806"/>
    <cge:TPSR_Ref TObjectID="25382"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-142806">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 348.000000 -196.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25383" ObjectName="SW-CX_ZD.CX_ZD_084XC1"/>
     <cge:Meas_Ref ObjectId="142806"/>
    <cge:TPSR_Ref TObjectID="25383"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -0.978261 459.000000 -59.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-142815">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 510.000000 -286.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25384" ObjectName="SW-CX_ZD.CX_ZD_085XC"/>
     <cge:Meas_Ref ObjectId="142815"/>
    <cge:TPSR_Ref TObjectID="25384"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-142815">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 510.000000 -197.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25385" ObjectName="SW-CX_ZD.CX_ZD_085XC1"/>
     <cge:Meas_Ref ObjectId="142815"/>
    <cge:TPSR_Ref TObjectID="25385"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -0.978261 619.000000 -58.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-142824">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 671.000000 -285.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25386" ObjectName="SW-CX_ZD.CX_ZD_086XC"/>
     <cge:Meas_Ref ObjectId="142824"/>
    <cge:TPSR_Ref TObjectID="25386"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-142824">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 671.000000 -196.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25387" ObjectName="SW-CX_ZD.CX_ZD_086XC1"/>
     <cge:Meas_Ref ObjectId="142824"/>
    <cge:TPSR_Ref TObjectID="25387"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-142773">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 894.000000 -246.000000)" xlink:href="#switch2:shape13_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25376" ObjectName="SW-CX_ZD.CX_ZD_0122XC"/>
     <cge:Meas_Ref ObjectId="142773"/>
    <cge:TPSR_Ref TObjectID="25376"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -0.978261 927.000000 -59.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-142833">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 985.000000 -286.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25388" ObjectName="SW-CX_ZD.CX_ZD_091XC"/>
     <cge:Meas_Ref ObjectId="142833"/>
    <cge:TPSR_Ref TObjectID="25388"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-142833">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 985.000000 -197.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25389" ObjectName="SW-CX_ZD.CX_ZD_091XC1"/>
     <cge:Meas_Ref ObjectId="142833"/>
    <cge:TPSR_Ref TObjectID="25389"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -0.978261 1096.000000 -58.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-142842">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1146.000000 -285.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25390" ObjectName="SW-CX_ZD.CX_ZD_092XC"/>
     <cge:Meas_Ref ObjectId="142842"/>
    <cge:TPSR_Ref TObjectID="25390"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-142842">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1146.000000 -196.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25391" ObjectName="SW-CX_ZD.CX_ZD_092XC1"/>
     <cge:Meas_Ref ObjectId="142842"/>
    <cge:TPSR_Ref TObjectID="25391"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -0.978261 1254.000000 -58.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-142851">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1303.000000 -285.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25392" ObjectName="SW-CX_ZD.CX_ZD_093XC"/>
     <cge:Meas_Ref ObjectId="142851"/>
    <cge:TPSR_Ref TObjectID="25392"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-142851">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1303.000000 -196.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25393" ObjectName="SW-CX_ZD.CX_ZD_093XC1"/>
     <cge:Meas_Ref ObjectId="142851"/>
    <cge:TPSR_Ref TObjectID="25393"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -0.978261 1414.000000 -57.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-142860">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1464.000000 -284.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25394" ObjectName="SW-CX_ZD.CX_ZD_094XC"/>
     <cge:Meas_Ref ObjectId="142860"/>
    <cge:TPSR_Ref TObjectID="25394"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-142860">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1464.000000 -195.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25395" ObjectName="SW-CX_ZD.CX_ZD_094XC1"/>
     <cge:Meas_Ref ObjectId="142860"/>
    <cge:TPSR_Ref TObjectID="25395"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -0.978261 1569.000000 -56.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-142869">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1618.000000 -283.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25396" ObjectName="SW-CX_ZD.CX_ZD_095XC"/>
     <cge:Meas_Ref ObjectId="142869"/>
    <cge:TPSR_Ref TObjectID="25396"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-142869">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1618.000000 -194.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25397" ObjectName="SW-CX_ZD.CX_ZD_095XC1"/>
     <cge:Meas_Ref ObjectId="142869"/>
    <cge:TPSR_Ref TObjectID="25397"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -0.978261 1728.000000 -55.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-142878">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1779.000000 -282.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25398" ObjectName="SW-CX_ZD.CX_ZD_096XC"/>
     <cge:Meas_Ref ObjectId="142878"/>
    <cge:TPSR_Ref TObjectID="25398"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-142878">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1779.000000 -193.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25399" ObjectName="SW-CX_ZD.CX_ZD_096XC1"/>
     <cge:Meas_Ref ObjectId="142878"/>
    <cge:TPSR_Ref TObjectID="25399"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-142887">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1494.000000 -455.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25401" ObjectName="SW-CX_ZD.CX_ZD_097XC1"/>
     <cge:Meas_Ref ObjectId="142887"/>
    <cge:TPSR_Ref TObjectID="25401"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-142887">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1494.000000 -361.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25400" ObjectName="SW-CX_ZD.CX_ZD_097XC"/>
     <cge:Meas_Ref ObjectId="142887"/>
    <cge:TPSR_Ref TObjectID="25400"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1666.000000 -359.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1667.000000 -478.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-142772">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 810.500000 -211.500000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25374" ObjectName="SW-CX_ZD.CX_ZD_012XC"/>
     <cge:Meas_Ref ObjectId="142772"/>
    <cge:TPSR_Ref TObjectID="25374"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-142772">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 854.500000 -211.500000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25375" ObjectName="SW-CX_ZD.CX_ZD_012XC1"/>
     <cge:Meas_Ref ObjectId="142772"/>
    <cge:TPSR_Ref TObjectID="25375"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-142895">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 464.000000 -438.000000)" xlink:href="#switch2:shape18_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25402" ObjectName="SW-CX_ZD.CX_ZD_0901XC"/>
     <cge:Meas_Ref ObjectId="142895"/>
    <cge:TPSR_Ref TObjectID="25402"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-142896">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1390.000000 -436.000000)" xlink:href="#switch2:shape18_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25404" ObjectName="SW-CX_ZD.CX_ZD_0902XC"/>
     <cge:Meas_Ref ObjectId="142896"/>
    <cge:TPSR_Ref TObjectID="25404"/></metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 153.000000 -575.000000)" xlink:href="#transformer2:shape3_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 153.000000 -575.000000)" xlink:href="#transformer2:shape3_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 1664.000000 -575.000000)" xlink:href="#transformer2:shape3_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 1664.000000 -575.000000)" xlink:href="#transformer2:shape3_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -239.000000 -881.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="155" x="-227" y="-940"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="155" x="-227" y="-940"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-276" y="-957"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-276" y="-957"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="293" y="-438"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="293" y="-438"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="205" y="-270"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="205" y="-270"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="380" y="-273"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="380" y="-273"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="539" y="-275"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="539" y="-275"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="703" y="-271"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="703" y="-271"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="818" y="-253"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="818" y="-253"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="1019" y="-272"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="1019" y="-272"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="1174" y="-270"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="1174" y="-270"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1336" y="-272"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1336" y="-272"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1494" y="-272"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1494" y="-272"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1650" y="-268"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1650" y="-268"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1808" y="-264"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1808" y="-264"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1531" y="-437"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1531" y="-437"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29a1a10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 210.000000 519.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a2d2e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 199.000000 504.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26a45b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 224.000000 489.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2895420" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 19.000000 350.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2895fa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1.000000 366.000000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2896930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1771.000000 350.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2896cf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1753.000000 366.000000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28e46d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 121.000000 -49.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28e4930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 110.000000 -64.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28e4b70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 135.000000 -79.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28e9ca0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 763.000000 202.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28ea1b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 752.000000 187.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28ea3f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 777.000000 172.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_29c5b30">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 294.000000 -634.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29c7210">
    <use class="BV-0KV" transform="matrix(0.693878 -0.000000 0.000000 -0.706897 215.000000 -679.000000)" xlink:href="#lightningRod:shape99"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29ed720">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 383.000000 -445.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29ee410">
    <use class="BV-0KV" transform="matrix(0.693878 -0.000000 0.000000 -0.706897 407.000000 -430.000000)" xlink:href="#lightningRod:shape99"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28e1390">
    <use class="BV-10KV" transform="matrix(-1.000000 0.000000 0.000000 1.000000 162.000000 -104.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29fac00">
    <use class="BV-0KV" transform="matrix(-0.693878 0.000000 0.000000 0.706897 241.000000 -57.000000)" xlink:href="#lightningRod:shape99"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2968a50">
    <use class="BV-10KV" transform="matrix(-1.000000 0.000000 0.000000 1.000000 335.000000 -103.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a17d60">
    <use class="BV-0KV" transform="matrix(-0.693878 0.000000 0.000000 0.706897 414.000000 -56.000000)" xlink:href="#lightningRod:shape99"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2951f20">
    <use class="BV-10KV" transform="matrix(-1.000000 0.000000 0.000000 1.000000 497.000000 -104.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2954130">
    <use class="BV-0KV" transform="matrix(-0.693878 0.000000 0.000000 0.706897 576.000000 -57.000000)" xlink:href="#lightningRod:shape99"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_295fef0">
    <use class="BV-0KV" transform="matrix(-0.693878 0.000000 0.000000 0.706897 737.000000 -56.000000)" xlink:href="#lightningRod:shape99"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29cdb80">
    <use class="BV-10KV" transform="matrix(-1.000000 0.000000 0.000000 1.000000 658.000000 -103.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29621b0">
    <use class="BV-10KV" transform="matrix(-1.000000 0.000000 0.000000 1.000000 972.000000 -104.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2964d30">
    <use class="BV-0KV" transform="matrix(-0.693878 0.000000 0.000000 0.706897 1051.000000 -57.000000)" xlink:href="#lightningRod:shape99"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2914170">
    <use class="BV-0KV" transform="matrix(-0.693878 0.000000 0.000000 0.706897 1212.000000 -56.000000)" xlink:href="#lightningRod:shape99"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_294c360">
    <use class="BV-10KV" transform="matrix(-1.000000 0.000000 0.000000 1.000000 1133.000000 -103.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2950b90">
    <use class="BV-0KV" transform="matrix(-0.693878 0.000000 0.000000 0.706897 1369.000000 -56.000000)" xlink:href="#lightningRod:shape99"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2838f40">
    <use class="BV-0KV" transform="matrix(-0.693878 0.000000 0.000000 0.706897 1530.000000 -55.000000)" xlink:href="#lightningRod:shape99"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28f03d0">
    <use class="BV-10KV" transform="matrix(-1.000000 0.000000 0.000000 1.000000 1451.000000 -102.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28f1a00">
    <use class="BV-10KV" transform="matrix(-1.000000 0.000000 0.000000 1.000000 1290.000000 -103.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28f3500">
    <use class="BV-0KV" transform="matrix(-0.693878 0.000000 0.000000 0.706897 1684.000000 -54.000000)" xlink:href="#lightningRod:shape99"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_290f400">
    <use class="BV-0KV" transform="matrix(-0.693878 0.000000 0.000000 0.706897 1845.000000 -53.000000)" xlink:href="#lightningRod:shape99"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28b4450">
    <use class="BV-10KV" transform="matrix(-1.000000 0.000000 0.000000 1.000000 1766.000000 -100.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28b5620">
    <use class="BV-10KV" transform="matrix(-1.000000 0.000000 0.000000 1.000000 1605.000000 -101.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28b6e10">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1309.000000 -445.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28b8080">
    <use class="BV-0KV" transform="matrix(0.693878 -0.000000 0.000000 -0.706897 1333.000000 -430.000000)" xlink:href="#lightningRod:shape99"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28fbb70">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1526.000000 -635.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28fd7c0">
    <use class="BV-0KV" transform="matrix(0.693878 -0.000000 0.000000 -0.706897 1448.000000 -682.000000)" xlink:href="#lightningRod:shape99"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28c9f00">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 157.000000 -427.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28ca8d0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1668.000000 -428.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="cx_配调_配网接线图开闭所.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="155" x="-227" y="-940"/></g>
   <g href="cx_索引_接线图_省地共调.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-276" y="-957"/></g>
   <g href="10kV庄甸开闭所CX_ZD_082间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="293" y="-438"/></g>
   <g href="10kV庄甸开闭所CX_ZD_083间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="205" y="-270"/></g>
   <g href="10kV庄甸开闭所CX_ZD_084间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="380" y="-273"/></g>
   <g href="10kV庄甸开闭所CX_ZD_085间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="539" y="-275"/></g>
   <g href="10kV庄甸开闭所CX_ZD_086间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="703" y="-271"/></g>
   <g href="10kV庄甸开闭所CX_ZD_012间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="818" y="-253"/></g>
   <g href="10kV庄甸开闭所CX_ZD_091间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="1019" y="-272"/></g>
   <g href="10kV庄甸开闭所CX_ZD_092间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="1174" y="-270"/></g>
   <g href="10kV庄甸开闭所CX_ZD_093间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1336" y="-272"/></g>
   <g href="10kV庄甸开闭所CX_ZD_094间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1494" y="-272"/></g>
   <g href="10kV庄甸开闭所CX_ZD_095间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1650" y="-268"/></g>
   <g href="10kV庄甸开闭所CX_ZD_096间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1808" y="-264"/></g>
   <g href="10kV庄甸开闭所CX_ZD_097间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1531" y="-437"/></g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_ZD.CX_ZD_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="20,-330 787,-330 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="25342" ObjectName="BS-CX_ZD.CX_ZD_9IM"/>
    <cge:TPSR_Ref TObjectID="25342"/></metadata>
   <polyline fill="none" opacity="0" points="20,-330 787,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_ZD.CX_ZD_9IIM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="862,-330 1878,-330 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="25343" ObjectName="BS-CX_ZD.CX_ZD_9IIM"/>
    <cge:TPSR_Ref TObjectID="25343"/></metadata>
   <polyline fill="none" opacity="0" points="862,-330 1878,-330 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-142551" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 68.000000 -366.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="142551" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25342"/>
     <cge:Term_Ref ObjectID="35712"/>
    <cge:TPSR_Ref TObjectID="25342"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-142553" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 68.000000 -366.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="142553" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25342"/>
     <cge:Term_Ref ObjectID="35712"/>
    <cge:TPSR_Ref TObjectID="25342"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-142554" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1823.000000 -366.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="142554" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25343"/>
     <cge:Term_Ref ObjectID="35713"/>
    <cge:TPSR_Ref TObjectID="25343"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-142556" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1823.000000 -366.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="142556" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25343"/>
     <cge:Term_Ref ObjectID="35713"/>
    <cge:TPSR_Ref TObjectID="25343"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-142561" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 287.000000 -518.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="142561" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25347"/>
     <cge:Term_Ref ObjectID="35726"/>
    <cge:TPSR_Ref TObjectID="25347"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-142562" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 287.000000 -518.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="142562" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25347"/>
     <cge:Term_Ref ObjectID="35726"/>
    <cge:TPSR_Ref TObjectID="25347"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-142557" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 287.000000 -518.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="142557" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25347"/>
     <cge:Term_Ref ObjectID="35726"/>
    <cge:TPSR_Ref TObjectID="25347"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-142638" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1519.000000 -518.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="142638" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25358"/>
     <cge:Term_Ref ObjectID="35792"/>
    <cge:TPSR_Ref TObjectID="25358"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-142639" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1519.000000 -518.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="142639" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25358"/>
     <cge:Term_Ref ObjectID="35792"/>
    <cge:TPSR_Ref TObjectID="25358"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-142634" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1519.000000 -518.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="142634" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25358"/>
     <cge:Term_Ref ObjectID="35792"/>
    <cge:TPSR_Ref TObjectID="25358"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-142568" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 180.000000 50.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="142568" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25348"/>
     <cge:Term_Ref ObjectID="35732"/>
    <cge:TPSR_Ref TObjectID="25348"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-142569" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 180.000000 50.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="142569" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25348"/>
     <cge:Term_Ref ObjectID="35732"/>
    <cge:TPSR_Ref TObjectID="25348"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-142564" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 180.000000 50.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="142564" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25348"/>
     <cge:Term_Ref ObjectID="35732"/>
    <cge:TPSR_Ref TObjectID="25348"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-142575" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 353.000000 50.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="142575" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25349"/>
     <cge:Term_Ref ObjectID="35738"/>
    <cge:TPSR_Ref TObjectID="25349"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-142576" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 353.000000 50.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="142576" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25349"/>
     <cge:Term_Ref ObjectID="35738"/>
    <cge:TPSR_Ref TObjectID="25349"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-142571" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 353.000000 50.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="142571" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25349"/>
     <cge:Term_Ref ObjectID="35738"/>
    <cge:TPSR_Ref TObjectID="25349"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-142582" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 503.000000 57.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="142582" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25350"/>
     <cge:Term_Ref ObjectID="35744"/>
    <cge:TPSR_Ref TObjectID="25350"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-142583" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 503.000000 57.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="142583" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25350"/>
     <cge:Term_Ref ObjectID="35744"/>
    <cge:TPSR_Ref TObjectID="25350"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-142578" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 503.000000 57.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="142578" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25350"/>
     <cge:Term_Ref ObjectID="35744"/>
    <cge:TPSR_Ref TObjectID="25350"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-142589" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 676.000000 50.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="142589" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25351"/>
     <cge:Term_Ref ObjectID="35750"/>
    <cge:TPSR_Ref TObjectID="25351"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-142590" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 676.000000 50.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="142590" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25351"/>
     <cge:Term_Ref ObjectID="35750"/>
    <cge:TPSR_Ref TObjectID="25351"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-142585" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 676.000000 50.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="142585" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25351"/>
     <cge:Term_Ref ObjectID="35750"/>
    <cge:TPSR_Ref TObjectID="25351"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-142548" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 824.000000 -204.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="142548" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25346"/>
     <cge:Term_Ref ObjectID="35716"/>
    <cge:TPSR_Ref TObjectID="25346"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-142549" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 824.000000 -204.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="142549" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25346"/>
     <cge:Term_Ref ObjectID="35716"/>
    <cge:TPSR_Ref TObjectID="25346"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-142545" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 824.000000 -204.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="142545" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25346"/>
     <cge:Term_Ref ObjectID="35716"/>
    <cge:TPSR_Ref TObjectID="25346"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-142596" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 990.000000 50.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="142596" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25352"/>
     <cge:Term_Ref ObjectID="35756"/>
    <cge:TPSR_Ref TObjectID="25352"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-142597" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 990.000000 50.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="142597" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25352"/>
     <cge:Term_Ref ObjectID="35756"/>
    <cge:TPSR_Ref TObjectID="25352"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-142592" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 990.000000 50.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="142592" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25352"/>
     <cge:Term_Ref ObjectID="35756"/>
    <cge:TPSR_Ref TObjectID="25352"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-142603" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1151.000000 50.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="142603" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25353"/>
     <cge:Term_Ref ObjectID="35762"/>
    <cge:TPSR_Ref TObjectID="25353"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-142604" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1151.000000 50.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="142604" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25353"/>
     <cge:Term_Ref ObjectID="35762"/>
    <cge:TPSR_Ref TObjectID="25353"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-142599" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1151.000000 50.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="142599" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25353"/>
     <cge:Term_Ref ObjectID="35762"/>
    <cge:TPSR_Ref TObjectID="25353"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-142610" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1308.000000 50.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="142610" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25354"/>
     <cge:Term_Ref ObjectID="35768"/>
    <cge:TPSR_Ref TObjectID="25354"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-142611" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1308.000000 50.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="142611" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25354"/>
     <cge:Term_Ref ObjectID="35768"/>
    <cge:TPSR_Ref TObjectID="25354"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-142606" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1308.000000 50.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="142606" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25354"/>
     <cge:Term_Ref ObjectID="35768"/>
    <cge:TPSR_Ref TObjectID="25354"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-142617" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1469.000000 50.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="142617" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25355"/>
     <cge:Term_Ref ObjectID="35774"/>
    <cge:TPSR_Ref TObjectID="25355"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-142618" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1469.000000 50.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="142618" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25355"/>
     <cge:Term_Ref ObjectID="35774"/>
    <cge:TPSR_Ref TObjectID="25355"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-142613" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1469.000000 50.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="142613" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25355"/>
     <cge:Term_Ref ObjectID="35774"/>
    <cge:TPSR_Ref TObjectID="25355"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-142624" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1623.000000 50.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="142624" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25356"/>
     <cge:Term_Ref ObjectID="35780"/>
    <cge:TPSR_Ref TObjectID="25356"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-142625" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1623.000000 50.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="142625" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25356"/>
     <cge:Term_Ref ObjectID="35780"/>
    <cge:TPSR_Ref TObjectID="25356"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-142620" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1623.000000 50.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="142620" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25356"/>
     <cge:Term_Ref ObjectID="35780"/>
    <cge:TPSR_Ref TObjectID="25356"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-142631" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1784.000000 50.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="142631" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25357"/>
     <cge:Term_Ref ObjectID="35786"/>
    <cge:TPSR_Ref TObjectID="25357"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-142632" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1784.000000 50.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="142632" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25357"/>
     <cge:Term_Ref ObjectID="35786"/>
    <cge:TPSR_Ref TObjectID="25357"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-142627" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1784.000000 50.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="142627" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25357"/>
     <cge:Term_Ref ObjectID="35786"/>
    <cge:TPSR_Ref TObjectID="25357"/></metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_ZD"/>
</svg>