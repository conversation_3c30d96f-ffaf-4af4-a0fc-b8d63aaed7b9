<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" LineID="楚雄地区_500kV小楚甲乙线金楚甲乙线.svg" MapType="line" StationID="SS-118" aopId="786686" id="thSvg" product="E8000V2" version="1.0" viewBox="3517 -1766 2677 1150">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="5" x2="5" y1="13" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="2" x2="2" y1="11" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="26" x2="9" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="9" x2="9" y1="18" y2="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="29" x2="29" y1="7" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="4" x2="22" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="22" x2="22" y1="0" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="25" x2="25" y1="6" y2="13"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2c057e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2c06190" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2c06b40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2c07820" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2c08a20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2c09630" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2c0a1e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2c0ac10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2c0b480" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2c0be60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注1</text>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2c0ca50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注2</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_2c0d3c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2c0eff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2c0fc00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2c105b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2c10ec0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">禁止操作</text>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2c126b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_25f8380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_25eb720" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2c149d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2c15bd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2c16550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2c17040" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="78" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2c1c440" transform="matrix(1.246377 -0.000000 0.000000 -1.035714 2.739130 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2c1d190" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">测试</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2c18dd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2c1a2b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2c1ad70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2c29020" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_264ed00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(0,0,0)" height="1160" width="2687" x="3512" y="-1771"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-196135">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4708.000000 -1319.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29858" ObjectName="SW-CX_TASE2JS.YN_XCⅡ_5051BK"/>
     <cge:Meas_Ref ObjectId="196135"/>
    <cge:TPSR_Ref TObjectID="29858"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196136">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4708.000000 -1123.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29859" ObjectName="SW-CX_TASE2JS.YN_XCⅡ_5052BK"/>
     <cge:Meas_Ref ObjectId="196136"/>
    <cge:TPSR_Ref TObjectID="29859"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213893">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4708.000000 -932.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31823" ObjectName="SW-CX_TASE2JS.YN_XCⅡ_5053BK"/>
     <cge:Meas_Ref ObjectId="213893"/>
    <cge:TPSR_Ref TObjectID="31823"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213894">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4983.000000 -1321.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31808" ObjectName="SW-CX_TASE2JS.YN_XCⅠ_5041BK"/>
     <cge:Meas_Ref ObjectId="213894"/>
    <cge:TPSR_Ref TObjectID="31808"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196133">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4983.000000 -1125.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29856" ObjectName="SW-CX_TASE2JS.YN_XCⅠ_5042BK"/>
     <cge:Meas_Ref ObjectId="196133"/>
    <cge:TPSR_Ref TObjectID="29856"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196134">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4983.000000 -934.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29857" ObjectName="SW-CX_TASE2JS.YN_XCⅠ_5043BK"/>
     <cge:Meas_Ref ObjectId="196134"/>
    <cge:TPSR_Ref TObjectID="29857"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196139">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5233.000000 -1320.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29862" ObjectName="SW-CX_TASE2JS.YN_JCⅡ_5031BK"/>
     <cge:Meas_Ref ObjectId="196139"/>
    <cge:TPSR_Ref TObjectID="29862"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196140">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5233.000000 -1124.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29863" ObjectName="SW-CX_TASE2JS.YN_JCⅡ_5032BK"/>
     <cge:Meas_Ref ObjectId="196140"/>
    <cge:TPSR_Ref TObjectID="29863"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213921">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5233.000000 -933.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31874" ObjectName="SW-CX_TASE2JS.YN_JCⅡ_5033BK"/>
     <cge:Meas_Ref ObjectId="213921"/>
    <cge:TPSR_Ref TObjectID="31874"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196137">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5448.000000 -1318.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29860" ObjectName="SW-CX_TASE2JS.YN_JCⅠ_5021BK"/>
     <cge:Meas_Ref ObjectId="196137"/>
    <cge:TPSR_Ref TObjectID="29860"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196138">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5448.000000 -1122.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29861" ObjectName="SW-CX_TASE2JS.YN_JCⅠ_5022BK"/>
     <cge:Meas_Ref ObjectId="196138"/>
    <cge:TPSR_Ref TObjectID="29861"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213913">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5448.000000 -931.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31866" ObjectName="SW-CX_TASE2JS.YN_JCⅠ_5023BK"/>
     <cge:Meas_Ref ObjectId="213913"/>
    <cge:TPSR_Ref TObjectID="31866"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66427">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4251.000000 -1324.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20281" ObjectName="SW-CX_TASE2JS.YN_XCⅡ_5021BK"/>
     <cge:Meas_Ref ObjectId="66427"/>
    <cge:TPSR_Ref TObjectID="20281"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66428">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4251.000000 -1128.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20282" ObjectName="SW-CX_TASE2JS.YN_XCⅡ_5022BK"/>
     <cge:Meas_Ref ObjectId="66428"/>
    <cge:TPSR_Ref TObjectID="20282"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66429">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4251.000000 -937.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20283" ObjectName="SW-CX_TASE2JS.YN_XCⅡ_5023BK"/>
     <cge:Meas_Ref ObjectId="66429"/>
    <cge:TPSR_Ref TObjectID="20283"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66430">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4251.000000 -749.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20284" ObjectName="SW-CX_TASE2JS.YN_XCⅡ_5024BK"/>
     <cge:Meas_Ref ObjectId="66430"/>
    <cge:TPSR_Ref TObjectID="20284"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66415">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4012.000000 -1325.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20267" ObjectName="SW-CX_TASE2JS.YN_XCⅠ_5031BK"/>
     <cge:Meas_Ref ObjectId="66415"/>
    <cge:TPSR_Ref TObjectID="20267"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66416">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4012.000000 -1129.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20268" ObjectName="SW-CX_TASE2JS.YN_XCⅠ_5032BK"/>
     <cge:Meas_Ref ObjectId="66416"/>
    <cge:TPSR_Ref TObjectID="20268"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66417">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4012.000000 -938.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20269" ObjectName="SW-CX_TASE2JS.YN_XCⅠ_5033BK"/>
     <cge:Meas_Ref ObjectId="66417"/>
    <cge:TPSR_Ref TObjectID="20269"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66418">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4012.000000 -750.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20270" ObjectName="SW-CX_TASE2JS.YN_XCⅠ_5034BK"/>
     <cge:Meas_Ref ObjectId="66418"/>
    <cge:TPSR_Ref TObjectID="20270"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66439">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5779.000000 -1322.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20293" ObjectName="SW-CX_TASE2JS.YN_JCⅠ_5031BK"/>
     <cge:Meas_Ref ObjectId="66439"/>
    <cge:TPSR_Ref TObjectID="20293"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66440">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5779.000000 -1126.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20294" ObjectName="SW-CX_TASE2JS.YN_JCⅠ_5032BK"/>
     <cge:Meas_Ref ObjectId="66440"/>
    <cge:TPSR_Ref TObjectID="20294"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66441">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5779.000000 -935.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20295" ObjectName="SW-CX_TASE2JS.YN_JCⅠ_5033BK"/>
     <cge:Meas_Ref ObjectId="66441"/>
    <cge:TPSR_Ref TObjectID="20295"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66448">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5994.000000 -1320.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20302" ObjectName="SW-CX_TASE2JS.YN_JCⅡ_5021BK"/>
     <cge:Meas_Ref ObjectId="66448"/>
    <cge:TPSR_Ref TObjectID="20302"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66449">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5994.000000 -1124.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20303" ObjectName="SW-CX_TASE2JS.YN_JCⅡ_5022BK"/>
     <cge:Meas_Ref ObjectId="66449"/>
    <cge:TPSR_Ref TObjectID="20303"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66450">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5994.000000 -933.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20304" ObjectName="SW-CX_TASE2JS.YN_JCⅡ_5023BK"/>
     <cge:Meas_Ref ObjectId="66450"/>
    <cge:TPSR_Ref TObjectID="20304"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_TASE2JS.CX_TASE2JS_CH_5ⅠM">
    <g class="BV-500KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4652,-1437 5530,-1437 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="31786" ObjectName="BS-CX_TASE2JS.CX_TASE2JS_CH_5ⅠM"/>
    <cge:TPSR_Ref TObjectID="31786"/></metadata>
   <polyline fill="none" opacity="0" points="4652,-1437 5530,-1437 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_TASE2JS.CX_TASE2JS_CH_5ⅡM">
    <g class="BV-500KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4643,-842 5536,-842 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="31787" ObjectName="BS-CX_TASE2JS.CX_TASE2JS_CH_5ⅡM"/>
    <cge:TPSR_Ref TObjectID="31787"/></metadata>
   <polyline fill="none" opacity="0" points="4643,-842 5536,-842 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_TASE2JS.CX_TASE2JS_XW_5ⅠM">
    <g class="BV-500KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3952,-1441 4346,-1441 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="31788" ObjectName="BS-CX_TASE2JS.CX_TASE2JS_XW_5ⅠM"/>
    <cge:TPSR_Ref TObjectID="31788"/></metadata>
   <polyline fill="none" opacity="0" points="3952,-1441 4346,-1441 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_TASE2JS.CX_TASE2JS_XW_5ⅡM">
    <g class="BV-500KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3939,-659 4352,-659 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="31791" ObjectName="BS-CX_TASE2JS.CX_TASE2JS_XW_5ⅡM"/>
    <cge:TPSR_Ref TObjectID="31791"/></metadata>
   <polyline fill="none" opacity="0" points="3939,-659 4352,-659 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_TASE2JS.CX_TASE2JS_JAQ_5ⅠM">
    <g class="BV-500KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5664,-1439 6085,-1439 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="31789" ObjectName="BS-CX_TASE2JS.CX_TASE2JS_JAQ_5ⅠM"/>
    <cge:TPSR_Ref TObjectID="31789"/></metadata>
   <polyline fill="none" opacity="0" points="5664,-1439 6085,-1439 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_TASE2JS.CX_TASE2JS_JAQ_5ⅡM">
    <g class="BV-500KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5662,-844 6084,-844 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="31790" ObjectName="BS-CX_TASE2JS.CX_TASE2JS_JAQ_5ⅡM"/>
    <cge:TPSR_Ref TObjectID="31790"/></metadata>
   <polyline fill="none" opacity="0" points="5662,-844 6084,-844 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Link_Layer">
   <g class="BV-500KV" id="g_26f7de0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4717,-1437 4717,-1416 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="31786@0" ObjectIDZND0="31817@1" Pin0InfoVect0LinkObjId="SW-213903_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4717,-1437 4717,-1416 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_267b440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4717,-870 4717,-842 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="31819@0" ObjectIDZND0="31787@0" Pin0InfoVect0LinkObjId="g_2684280_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213908_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4717,-870 4717,-842 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_267b630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4717,-1061 4717,-1029 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="31821@0" ObjectIDZND0="31816@1" Pin0InfoVect0LinkObjId="SW-213902_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213905_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4717,-1061 4717,-1029 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2653740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4992,-1437 4992,-1418 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="31786@0" ObjectIDZND0="31809@1" Pin0InfoVect0LinkObjId="SW-213895_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4992,-1437 4992,-1418 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2695140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4992,-1259 4992,-1222 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="31810@0" ObjectIDZND0="31811@1" Pin0InfoVect0LinkObjId="SW-213896_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213901_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4992,-1259 4992,-1222 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2684280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4992,-872 4992,-842 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="31813@0" ObjectIDZND0="31787@0" Pin0InfoVect0LinkObjId="g_267b440_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213898_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4992,-872 4992,-842 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_25ff120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4854,-1046 4833,-1046 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="31814@0" ObjectIDZND0="g_25ff310@0" Pin0InfoVect0LinkObjId="g_25ff310_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213900_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4854,-1046 4833,-1046 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_26c1380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5242,-1437 5242,-1417 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="31786@0" ObjectIDZND0="31876@1" Pin0InfoVect0LinkObjId="SW-213925_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5242,-1437 5242,-1417 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_264a0d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5242,-871 5242,-842 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="31881@0" ObjectIDZND0="31787@0" Pin0InfoVect0LinkObjId="g_267b440_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213928_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5242,-871 5242,-842 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_264a2f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5242,-1062 5242,-1030 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="31878@0" ObjectIDZND0="31875@1" Pin0InfoVect0LinkObjId="SW-213922_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213924_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5242,-1062 5242,-1030 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_264a510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5457,-1437 5457,-1415 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="31786@0" ObjectIDZND0="31868@1" Pin0InfoVect0LinkObjId="SW-213915_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5457,-1437 5457,-1415 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2603d40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5593,-1245 5613,-1245 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="31872@1" ObjectIDZND0="g_2603f60@0" Pin0InfoVect0LinkObjId="g_2603f60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213919_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5593,-1245 5613,-1245 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_26433d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5457,-869 5457,-842 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="31873@0" ObjectIDZND0="31787@0" Pin0InfoVect0LinkObjId="g_267b440_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213920_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5457,-869 5457,-842 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2643630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5457,-1060 5457,-1028 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="31870@0" ObjectIDZND0="31867@1" Pin0InfoVect0LinkObjId="SW-213914_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213917_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5457,-1060 5457,-1028 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_260cec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4260,-1441 4260,-1421 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="31788@0" ObjectIDZND0="20285@1" Pin0InfoVect0LinkObjId="SW-66431_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4260,-1441 4260,-1421 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_26687f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4260,-875 4260,-846 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="20290@0" ObjectIDZND0="20291@1" Pin0InfoVect0LinkObjId="SW-66437_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66436_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4260,-875 4260,-846 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_266b710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4445,-1055 4467,-1055 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="31803@1" ObjectIDZND0="g_266ad00@0" Pin0InfoVect0LinkObjId="g_266ad00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213886_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4445,-1055 4467,-1055 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_266bdd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4260,-1385 4260,-1359 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="20285@0" ObjectIDZND0="20281@1" Pin0InfoVect0LinkObjId="SW-66427_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66431_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4260,-1385 4260,-1359 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_266c030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4260,-1332 4260,-1298 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="20281@0" ObjectIDZND0="20288@1" Pin0InfoVect0LinkObjId="SW-66432_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66427_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4260,-1332 4260,-1298 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_266c290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4260,-1189 4260,-1163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="20286@0" ObjectIDZND0="20282@1" Pin0InfoVect0LinkObjId="SW-66428_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66433_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4260,-1189 4260,-1163 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_266c4f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4260,-1136 4260,-1116 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="20282@0" ObjectIDZND0="20287@1" Pin0InfoVect0LinkObjId="SW-66434_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66428_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4260,-1136 4260,-1116 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_25cf550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4260,-972 4260,-988 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="20283@1" ObjectIDZND0="20289@0" Pin0InfoVect0LinkObjId="SW-66435_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66429_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4260,-972 4260,-988 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_25cf7b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4260,-911 4260,-945 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="20290@1" ObjectIDZND0="20283@0" Pin0InfoVect0LinkObjId="SW-66429_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66436_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4260,-911 4260,-945 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_25d6090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4260,-687 4260,-659 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="20292@0" ObjectIDZND0="31791@0" Pin0InfoVect0LinkObjId="g_25f5fe0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66438_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4260,-687 4260,-659 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_25d62f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4260,-784 4260,-810 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="20284@1" ObjectIDZND0="20291@0" Pin0InfoVect0LinkObjId="SW-66437_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66430_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4260,-784 4260,-810 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_25d6550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4260,-723 4260,-757 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="20292@1" ObjectIDZND0="20284@0" Pin0InfoVect0LinkObjId="SW-66430_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66438_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4260,-723 4260,-757 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_26601a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4260,-1262 4260,-1225 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="20288@0" ObjectIDZND0="20286@1" Pin0InfoVect0LinkObjId="SW-66433_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66432_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4260,-1262 4260,-1225 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_26628f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4021,-1441 4021,-1422 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="31788@0" ObjectIDZND0="20271@1" Pin0InfoVect0LinkObjId="SW-66419_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4021,-1441 4021,-1422 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_27442f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4021,-876 4021,-846 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="20276@0" ObjectIDZND0="20277@1" Pin0InfoVect0LinkObjId="SW-66425_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66424_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4021,-876 4021,-846 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_25eeac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4021,-1386 4021,-1360 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="20271@0" ObjectIDZND0="20267@1" Pin0InfoVect0LinkObjId="SW-66415_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66419_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4021,-1386 4021,-1360 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_25eed20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4021,-1333 4021,-1299 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="20267@0" ObjectIDZND0="20272@1" Pin0InfoVect0LinkObjId="SW-66420_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66415_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4021,-1333 4021,-1299 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_25eef80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4021,-1190 4021,-1164 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="20273@0" ObjectIDZND0="20268@1" Pin0InfoVect0LinkObjId="SW-66416_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66421_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4021,-1190 4021,-1164 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_25ef1e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4021,-1137 4021,-1103 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="20268@0" ObjectIDZND0="20274@1" Pin0InfoVect0LinkObjId="SW-66422_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66416_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4021,-1137 4021,-1103 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_25ef440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4021,-973 4021,-999 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="20269@1" ObjectIDZND0="20275@0" Pin0InfoVect0LinkObjId="SW-66423_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66417_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4021,-973 4021,-999 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_25ef6a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4021,-912 4021,-946 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="20276@1" ObjectIDZND0="20269@0" Pin0InfoVect0LinkObjId="SW-66417_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66424_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4021,-912 4021,-946 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_25f5fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4021,-688 4021,-659 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="20278@0" ObjectIDZND0="31791@0" Pin0InfoVect0LinkObjId="g_25d6090_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66426_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4021,-688 4021,-659 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_25f6240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4021,-785 4021,-811 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="20270@1" ObjectIDZND0="20277@0" Pin0InfoVect0LinkObjId="SW-66425_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66418_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4021,-785 4021,-811 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_25f64a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4021,-724 4021,-758 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="20278@1" ObjectIDZND0="20270@0" Pin0InfoVect0LinkObjId="SW-66418_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66426_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4021,-724 4021,-758 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_25f6700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4021,-1263 4021,-1226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="20272@0" ObjectIDZND0="20273@1" Pin0InfoVect0LinkObjId="SW-66421_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66420_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4021,-1263 4021,-1226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_25f7140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4643,-1506 4643,-1508 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="31803@x" ObjectIDND1="31800@x" ObjectIDND2="31822@x" ObjectIDZND0="31803@x" ObjectIDZND1="31800@x" ObjectIDZND2="31822@x" Pin0InfoVect0LinkObjId="SW-213886_0" Pin0InfoVect1LinkObjId="SW-213885_0" Pin0InfoVect2LinkObjId="SW-213907_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-213886_0" Pin1InfoVect1LinkObjId="SW-213885_0" Pin1InfoVect2LinkObjId="SW-213907_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4643,-1506 4643,-1508 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_25fade0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3860,-1056 3880,-1056 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_25fa3b0@0" ObjectIDZND0="31802@0" Pin0InfoVect0LinkObjId="SW-213888_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25fa3b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3860,-1056 3880,-1056 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_25fb820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4021,-1067 4021,-1056 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="20274@0" ObjectIDZND0="20275@x" ObjectIDZND1="31801@x" Pin0InfoVect0LinkObjId="SW-66423_0" Pin0InfoVect1LinkObjId="SW-213887_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66422_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4021,-1067 4021,-1056 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_25fba80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4021,-1056 4021,-1035 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="20274@x" ObjectIDND1="31801@x" ObjectIDZND0="20275@1" Pin0InfoVect0LinkObjId="SW-66423_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-66422_0" Pin1InfoVect1LinkObjId="SW-213887_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4021,-1056 4021,-1035 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_25fc4c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4260,-1080 4260,-1055 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="20287@0" ObjectIDZND0="31800@x" ObjectIDZND1="20289@x" Pin0InfoVect0LinkObjId="SW-213885_0" Pin0InfoVect1LinkObjId="SW-66435_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66434_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4260,-1080 4260,-1055 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_25fc700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4260,-1055 4260,-1024 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="31800@x" ObjectIDND1="20287@x" ObjectIDZND0="20289@1" Pin0InfoVect0LinkObjId="SW-66435_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-213885_0" Pin1InfoVect1LinkObjId="SW-66434_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4260,-1055 4260,-1024 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_25dfea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4308,-1055 4260,-1055 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="31800@0" ObjectIDZND0="20287@x" ObjectIDZND1="20289@x" Pin0InfoVect0LinkObjId="SW-66434_0" Pin0InfoVect1LinkObjId="SW-66435_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213885_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4308,-1055 4260,-1055 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_25e0100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4375,-1055 4409,-1055 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="31803@x" ObjectIDND1="31800@x" ObjectIDND2="31822@x" ObjectIDZND0="31803@0" Pin0InfoVect0LinkObjId="SW-213886_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-213886_0" Pin1InfoVect1LinkObjId="SW-213885_0" Pin1InfoVect2LinkObjId="SW-213907_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4375,-1055 4409,-1055 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_25e0b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4643,-1506 4375,-1506 4375,-1055 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="31803@x" ObjectIDND1="31800@x" ObjectIDND2="31822@x" ObjectIDZND0="31803@x" ObjectIDZND1="31800@x" Pin0InfoVect0LinkObjId="SW-213886_0" Pin0InfoVect1LinkObjId="SW-213885_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-213886_0" Pin1InfoVect1LinkObjId="SW-213885_0" Pin1InfoVect2LinkObjId="SW-213907_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4643,-1506 4375,-1506 4375,-1055 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_25e0da0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4375,-1055 4344,-1055 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="31803@x" ObjectIDND1="31803@x" ObjectIDND2="31800@x" ObjectIDZND0="31800@1" Pin0InfoVect0LinkObjId="SW-213885_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-213886_0" Pin1InfoVect1LinkObjId="SW-213886_0" Pin1InfoVect2LinkObjId="SW-213885_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4375,-1055 4344,-1055 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_25e1000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4717,-1354 4717,-1380 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29858@1" ObjectIDZND0="31817@0" Pin0InfoVect0LinkObjId="SW-213903_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196135_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4717,-1354 4717,-1380 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_25e1260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4717,-1327 4717,-1293 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29858@0" ObjectIDZND0="31818@1" Pin0InfoVect0LinkObjId="SW-213906_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196135_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4717,-1327 4717,-1293 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_25e1ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4717,-1257 4717,-1239 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="31818@0" ObjectIDZND0="31820@x" ObjectIDZND1="31822@x" ObjectIDZND2="31803@x" Pin0InfoVect0LinkObjId="SW-213904_0" Pin0InfoVect1LinkObjId="SW-213907_0" Pin0InfoVect2LinkObjId="SW-213886_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213906_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4717,-1257 4717,-1239 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_25e1ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4717,-1239 4717,-1220 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="31818@x" ObjectIDND1="31822@x" ObjectIDND2="31803@x" ObjectIDZND0="31820@1" Pin0InfoVect0LinkObjId="SW-213904_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-213906_0" Pin1InfoVect1LinkObjId="SW-213907_0" Pin1InfoVect2LinkObjId="SW-213886_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4717,-1239 4717,-1220 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_25e4e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4533,-1239 4569,-1239 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_25e4410@0" ObjectIDZND0="31822@0" Pin0InfoVect0LinkObjId="SW-213907_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25e4410_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4533,-1239 4569,-1239 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_25e50a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4605,-1239 4643,-1239 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="31822@1" ObjectIDZND0="31818@x" ObjectIDZND1="31820@x" ObjectIDZND2="31803@x" Pin0InfoVect0LinkObjId="SW-213906_0" Pin0InfoVect1LinkObjId="SW-213904_0" Pin0InfoVect2LinkObjId="SW-213886_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213907_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4605,-1239 4643,-1239 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_25e5b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4714,-1239 4643,-1239 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="31818@x" ObjectIDND1="31820@x" ObjectIDZND0="31822@x" ObjectIDZND1="31803@x" ObjectIDZND2="31800@x" Pin0InfoVect0LinkObjId="SW-213907_0" Pin0InfoVect1LinkObjId="SW-213886_0" Pin0InfoVect2LinkObjId="SW-213885_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-213906_0" Pin1InfoVect1LinkObjId="SW-213904_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4714,-1239 4643,-1239 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_25e5dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4643,-1239 4643,-1506 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="31822@x" ObjectIDND1="31818@x" ObjectIDND2="31820@x" ObjectIDZND0="31803@x" ObjectIDZND1="31800@x" ObjectIDZND2="31822@x" Pin0InfoVect0LinkObjId="SW-213886_0" Pin0InfoVect1LinkObjId="SW-213885_0" Pin0InfoVect2LinkObjId="SW-213907_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-213907_0" Pin1InfoVect1LinkObjId="SW-213906_0" Pin1InfoVect2LinkObjId="SW-213904_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4643,-1239 4643,-1506 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_25e6030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4717,-1158 4717,-1184 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29859@1" ObjectIDZND0="31820@0" Pin0InfoVect0LinkObjId="SW-213904_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196136_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4717,-1158 4717,-1184 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_25e6290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4717,-1131 4717,-1097 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29859@0" ObjectIDZND0="31821@1" Pin0InfoVect0LinkObjId="SW-213905_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196136_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4717,-1131 4717,-1097 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_25e64f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4717,-967 4717,-993 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="31823@1" ObjectIDZND0="31816@0" Pin0InfoVect0LinkObjId="SW-213902_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213893_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4717,-967 4717,-993 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_25e6750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4717,-940 4717,-906 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="31823@0" ObjectIDZND0="31819@1" Pin0InfoVect0LinkObjId="SW-213908_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213893_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4717,-940 4717,-906 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_25e69b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4992,-1329 4992,-1295 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="31808@0" ObjectIDZND0="31810@1" Pin0InfoVect0LinkObjId="SW-213901_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213894_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4992,-1329 4992,-1295 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_25e6c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4992,-1356 4992,-1382 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="31808@1" ObjectIDZND0="31809@0" Pin0InfoVect0LinkObjId="SW-213895_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213894_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4992,-1356 4992,-1382 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_25e6e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4992,-1160 4992,-1186 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29856@1" ObjectIDZND0="31811@0" Pin0InfoVect0LinkObjId="SW-213896_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196133_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4992,-1160 4992,-1186 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_25e70d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4992,-1133 4992,-1099 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29856@0" ObjectIDZND0="31815@1" Pin0InfoVect0LinkObjId="SW-213899_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196133_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4992,-1133 4992,-1099 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_25e7330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4992,-969 4992,-995 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29857@1" ObjectIDZND0="31812@0" Pin0InfoVect0LinkObjId="SW-213897_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196134_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4992,-969 4992,-995 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_25e7590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4992,-942 4992,-908 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29857@0" ObjectIDZND0="31813@1" Pin0InfoVect0LinkObjId="SW-213898_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196134_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4992,-942 4992,-908 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_25e8060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4992,-1063 4992,-1046 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="31815@0" ObjectIDZND0="31812@x" ObjectIDZND1="31814@x" ObjectIDZND2="31801@x" Pin0InfoVect0LinkObjId="SW-213897_0" Pin0InfoVect1LinkObjId="SW-213900_0" Pin0InfoVect2LinkObjId="SW-213887_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213899_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4992,-1063 4992,-1046 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_25e82a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4992,-1046 4992,-1031 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="31815@x" ObjectIDND1="31814@x" ObjectIDND2="31801@x" ObjectIDZND0="31812@1" Pin0InfoVect0LinkObjId="SW-213897_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-213899_0" Pin1InfoVect1LinkObjId="SW-213900_0" Pin1InfoVect2LinkObjId="SW-213887_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4992,-1046 4992,-1031 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_25e8500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4915,-1046 4915,-1583 3940,-1582 3940,-1056 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="31814@x" ObjectIDND1="31815@x" ObjectIDND2="31812@x" ObjectIDZND0="31801@x" ObjectIDZND1="31802@x" Pin0InfoVect0LinkObjId="SW-213887_0" Pin0InfoVect1LinkObjId="SW-213888_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-213900_0" Pin1InfoVect1LinkObjId="SW-213899_0" Pin1InfoVect2LinkObjId="SW-213897_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4915,-1046 4915,-1583 3940,-1582 3940,-1056 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_25e8fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4890,-1046 4915,-1046 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="31814@1" ObjectIDZND0="31815@x" ObjectIDZND1="31812@x" ObjectIDZND2="31801@x" Pin0InfoVect0LinkObjId="SW-213899_0" Pin0InfoVect1LinkObjId="SW-213897_0" Pin0InfoVect2LinkObjId="SW-213887_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213900_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4890,-1046 4915,-1046 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_25e9200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4993,-1046 4915,-1046 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="31815@x" ObjectIDND1="31812@x" ObjectIDZND0="31814@x" ObjectIDZND1="31801@x" ObjectIDZND2="31802@x" Pin0InfoVect0LinkObjId="SW-213900_0" Pin0InfoVect1LinkObjId="SW-213887_0" Pin0InfoVect2LinkObjId="SW-213888_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-213899_0" Pin1InfoVect1LinkObjId="SW-213897_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4993,-1046 4915,-1046 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_25e9460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5242,-1381 5242,-1355 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="31876@0" ObjectIDZND0="29862@1" Pin0InfoVect0LinkObjId="SW-196139_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213925_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5242,-1381 5242,-1355 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_25e96c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5242,-1328 5242,-1294 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29862@0" ObjectIDZND0="31880@1" Pin0InfoVect0LinkObjId="SW-213927_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196139_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5242,-1328 5242,-1294 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_25e9920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5242,-1185 5242,-1159 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="31877@0" ObjectIDZND0="29863@1" Pin0InfoVect0LinkObjId="SW-196140_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213923_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5242,-1185 5242,-1159 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_25e9b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5242,-1132 5242,-1098 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29863@0" ObjectIDZND0="31878@1" Pin0InfoVect0LinkObjId="SW-213924_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196140_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5242,-1132 5242,-1098 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_25e9de0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5242,-968 5242,-994 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="31874@1" ObjectIDZND0="31875@0" Pin0InfoVect0LinkObjId="SW-213922_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213921_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5242,-968 5242,-994 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_25ea040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5242,-907 5242,-941 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="31881@1" ObjectIDZND0="31874@0" Pin0InfoVect0LinkObjId="SW-213921_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213928_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5242,-907 5242,-941 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_25ea2a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5457,-1379 5457,-1353 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="31868@0" ObjectIDZND0="29860@1" Pin0InfoVect0LinkObjId="SW-196137_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213915_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5457,-1379 5457,-1353 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_25ea500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5457,-1183 5457,-1157 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="31871@0" ObjectIDZND0="29861@1" Pin0InfoVect0LinkObjId="SW-196138_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213916_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5457,-1183 5457,-1157 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_25ea760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5457,-1130 5457,-1096 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29861@0" ObjectIDZND0="31870@1" Pin0InfoVect0LinkObjId="SW-213917_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196138_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5457,-1130 5457,-1096 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_25ea9c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5457,-966 5457,-992 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="31866@1" ObjectIDZND0="31867@0" Pin0InfoVect0LinkObjId="SW-213914_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213913_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5457,-966 5457,-992 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_25eac20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5457,-905 5457,-939 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="31873@1" ObjectIDZND0="31866@0" Pin0InfoVect0LinkObjId="SW-213913_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213920_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5457,-905 5457,-939 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_260a4f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5788,-1439 5788,-1419 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="31789@0" ObjectIDZND0="20296@1" Pin0InfoVect0LinkObjId="SW-66442_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5788,-1439 5788,-1419 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2a1acc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5788,-873 5788,-844 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="20301@0" ObjectIDZND0="31790@0" Pin0InfoVect0LinkObjId="g_2a30590_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66447_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5788,-873 5788,-844 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2a1af20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5788,-1064 5788,-1031 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="20299@0" ObjectIDZND0="20300@1" Pin0InfoVect0LinkObjId="SW-66446_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66445_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5788,-1064 5788,-1031 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2a1b180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6003,-1439 6003,-1417 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="31789@0" ObjectIDZND0="20305@1" Pin0InfoVect0LinkObjId="SW-66451_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6003,-1439 6003,-1417 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2a21810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6148,-1040 6168,-1040 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="31805@1" ObjectIDZND0="g_2a21a70@0" Pin0InfoVect0LinkObjId="g_2a21a70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213892_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6148,-1040 6168,-1040 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2a292b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6003,-1258 6003,-1221 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="20306@0" ObjectIDZND0="20307@1" Pin0InfoVect0LinkObjId="SW-66453_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66452_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6003,-1258 6003,-1221 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2a30590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6003,-871 6003,-844 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="20310@0" ObjectIDZND0="31790@0" Pin0InfoVect0LinkObjId="g_2a1acc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66456_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6003,-871 6003,-844 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2a33c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5788,-1383 5788,-1357 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="20296@0" ObjectIDZND0="20293@1" Pin0InfoVect0LinkObjId="SW-66439_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66442_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5788,-1383 5788,-1357 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2a33e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5788,-1330 5788,-1296 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="20293@0" ObjectIDZND0="20297@1" Pin0InfoVect0LinkObjId="SW-66443_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66439_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5788,-1330 5788,-1296 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2a34030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5788,-1187 5788,-1161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="20298@0" ObjectIDZND0="20294@1" Pin0InfoVect0LinkObjId="SW-66440_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66444_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5788,-1187 5788,-1161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2a34220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5788,-1134 5788,-1100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="20294@0" ObjectIDZND0="20299@1" Pin0InfoVect0LinkObjId="SW-66445_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66440_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5788,-1134 5788,-1100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2a34450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5788,-970 5788,-996 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="20295@1" ObjectIDZND0="20300@0" Pin0InfoVect0LinkObjId="SW-66446_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66441_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5788,-970 5788,-996 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2a34680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5788,-909 5788,-943 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="20301@1" ObjectIDZND0="20295@0" Pin0InfoVect0LinkObjId="SW-66441_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66447_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5788,-909 5788,-943 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2a348b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6003,-1328 6003,-1294 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="20302@0" ObjectIDZND0="20306@1" Pin0InfoVect0LinkObjId="SW-66452_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66448_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6003,-1328 6003,-1294 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2a34ae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6003,-1381 6003,-1355 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="20305@0" ObjectIDZND0="20302@1" Pin0InfoVect0LinkObjId="SW-66448_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66451_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6003,-1381 6003,-1355 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2a34d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6003,-1185 6003,-1159 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="20307@0" ObjectIDZND0="20303@1" Pin0InfoVect0LinkObjId="SW-66449_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66453_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6003,-1185 6003,-1159 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2a34f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6003,-968 6003,-994 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="20304@1" ObjectIDZND0="20309@0" Pin0InfoVect0LinkObjId="SW-66455_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66450_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6003,-968 6003,-994 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2a351a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6003,-907 6003,-941 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="20310@1" ObjectIDZND0="20304@0" Pin0InfoVect0LinkObjId="SW-66450_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66456_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6003,-907 6003,-941 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2a36b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5242,-1258 5242,-1240 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="31880@0" ObjectIDZND0="31877@x" ObjectIDZND1="31880@x" ObjectIDZND2="31877@x" Pin0InfoVect0LinkObjId="SW-213923_0" Pin0InfoVect1LinkObjId="SW-213927_0" Pin0InfoVect2LinkObjId="SW-213923_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213927_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5242,-1258 5242,-1240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2a36d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5242,-1240 5242,-1221 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="31880@x" ObjectIDND1="31880@x" ObjectIDND2="31877@x" ObjectIDZND0="31877@1" Pin0InfoVect0LinkObjId="SW-213923_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-213927_0" Pin1InfoVect1LinkObjId="SW-213927_0" Pin1InfoVect2LinkObjId="SW-213923_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5242,-1240 5242,-1221 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2a37920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5168,-1240 5169,-1240 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="31880@x" ObjectIDND1="31877@x" ObjectIDND2="31879@x" ObjectIDZND0="31880@x" ObjectIDZND1="31877@x" ObjectIDZND2="31879@x" Pin0InfoVect0LinkObjId="SW-213927_0" Pin0InfoVect1LinkObjId="SW-213923_0" Pin0InfoVect2LinkObjId="SW-213926_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-213927_0" Pin1InfoVect1LinkObjId="SW-213923_0" Pin1InfoVect2LinkObjId="SW-213926_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5168,-1240 5169,-1240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2a37b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5168,-1240 5242,-1240 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="31880@x" ObjectIDND1="31877@x" ObjectIDND2="31879@x" ObjectIDZND0="31880@x" ObjectIDZND1="31877@x" Pin0InfoVect0LinkObjId="SW-213927_0" Pin0InfoVect1LinkObjId="SW-213923_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-213927_0" Pin1InfoVect1LinkObjId="SW-213923_0" Pin1InfoVect2LinkObjId="SW-213926_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5168,-1240 5242,-1240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2a3ab80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5077,-1240 5113,-1240 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2a3a0f0@0" ObjectIDZND0="31879@0" Pin0InfoVect0LinkObjId="SW-213926_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a3a0f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5077,-1240 5113,-1240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2a3ade0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5149,-1240 5168,-1240 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="31879@1" ObjectIDZND0="31880@x" ObjectIDZND1="31877@x" ObjectIDZND2="31880@x" Pin0InfoVect0LinkObjId="SW-213927_0" Pin0InfoVect1LinkObjId="SW-213923_0" Pin0InfoVect2LinkObjId="SW-213927_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213926_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5149,-1240 5168,-1240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2a3b8d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5457,-1220 5457,-1245 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="31871@1" ObjectIDZND0="31869@x" ObjectIDZND1="31872@x" ObjectIDZND2="31807@x" Pin0InfoVect0LinkObjId="SW-213918_0" Pin0InfoVect1LinkObjId="SW-213919_0" Pin0InfoVect2LinkObjId="SW-213890_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213916_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5457,-1220 5457,-1245 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2a3bb30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5457,-1245 5457,-1256 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="31871@x" ObjectIDND1="31872@x" ObjectIDND2="31807@x" ObjectIDZND0="31869@0" Pin0InfoVect0LinkObjId="SW-213918_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-213916_0" Pin1InfoVect1LinkObjId="SW-213919_0" Pin1InfoVect2LinkObjId="SW-213890_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5457,-1245 5457,-1256 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2a3bd90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5457,-1292 5457,-1326 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="31869@1" ObjectIDZND0="29860@0" Pin0InfoVect0LinkObjId="SW-196137_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213918_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5457,-1292 5457,-1326 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2a3c880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5457,-1245 5543,-1245 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="31871@x" ObjectIDND1="31869@x" ObjectIDZND0="31872@x" ObjectIDZND1="31807@x" ObjectIDZND2="31806@x" Pin0InfoVect0LinkObjId="SW-213919_0" Pin0InfoVect1LinkObjId="SW-213890_0" Pin0InfoVect2LinkObjId="SW-213889_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-213916_0" Pin1InfoVect1LinkObjId="SW-213918_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5457,-1245 5543,-1245 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2a3cae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5543,-1245 5556,-1245 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="31871@x" ObjectIDND1="31869@x" ObjectIDND2="31807@x" ObjectIDZND0="31872@0" Pin0InfoVect0LinkObjId="SW-213919_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-213916_0" Pin1InfoVect1LinkObjId="SW-213918_0" Pin1InfoVect2LinkObjId="SW-213890_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5543,-1245 5556,-1245 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2a3f280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5939,-1231 5959,-1231 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="31807@1" ObjectIDZND0="g_2a3f4e0@0" Pin0InfoVect0LinkObjId="g_2a3f4e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213890_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5939,-1231 5959,-1231 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2a40800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5907,-1231 5878,-1231 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="31807@0" ObjectIDZND0="31871@x" ObjectIDZND1="31869@x" ObjectIDZND2="31872@x" Pin0InfoVect0LinkObjId="SW-213916_0" Pin0InfoVect1LinkObjId="SW-213918_0" Pin0InfoVect2LinkObjId="SW-213919_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213890_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5907,-1231 5878,-1231 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2a40a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5878,-1231 5878,-1501 5543,-1501 5543,-1245 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="31807@x" ObjectIDND1="31806@x" ObjectIDZND0="31871@x" ObjectIDZND1="31869@x" ObjectIDZND2="31872@x" Pin0InfoVect0LinkObjId="SW-213916_0" Pin0InfoVect1LinkObjId="SW-213918_0" Pin0InfoVect2LinkObjId="SW-213919_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-213890_0" Pin1InfoVect1LinkObjId="SW-213889_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5878,-1231 5878,-1501 5543,-1501 5543,-1245 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2a41560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5788,-1260 5788,-1231 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="20297@0" ObjectIDZND0="20298@x" ObjectIDZND1="31806@x" Pin0InfoVect0LinkObjId="SW-66444_0" Pin0InfoVect1LinkObjId="SW-213889_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66443_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5788,-1260 5788,-1231 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2a417c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5788,-1231 5788,-1223 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="20297@x" ObjectIDND1="31806@x" ObjectIDZND0="20298@1" Pin0InfoVect0LinkObjId="SW-66444_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-66443_0" Pin1InfoVect1LinkObjId="SW-213889_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5788,-1231 5788,-1223 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2a43f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5878,-1231 5853,-1231 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="31807@x" ObjectIDND1="31871@x" ObjectIDND2="31869@x" ObjectIDZND0="31806@1" Pin0InfoVect0LinkObjId="SW-213889_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-213890_0" Pin1InfoVect1LinkObjId="SW-213916_0" Pin1InfoVect2LinkObjId="SW-213918_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5878,-1231 5853,-1231 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2a441c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5817,-1231 5788,-1231 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="31806@0" ObjectIDZND0="20297@x" ObjectIDZND1="20298@x" Pin0InfoVect0LinkObjId="SW-66443_0" Pin0InfoVect1LinkObjId="SW-66444_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213889_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5817,-1231 5788,-1231 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2a44cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6003,-1039 6003,-1030 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="31804@x" ObjectIDND1="20308@x" ObjectIDZND0="20309@1" Pin0InfoVect0LinkObjId="SW-66455_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-213891_0" Pin1InfoVect1LinkObjId="SW-66454_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6003,-1039 6003,-1030 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2a47450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6051,-1039 6003,-1039 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="31804@0" ObjectIDZND0="20309@x" ObjectIDZND1="20308@x" Pin0InfoVect0LinkObjId="SW-66455_0" Pin0InfoVect1LinkObjId="SW-66454_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213891_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="6051,-1039 6003,-1039 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2a476b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6003,-1039 6003,-1076 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="20309@x" ObjectIDND1="31804@x" ObjectIDZND0="20308@0" Pin0InfoVect0LinkObjId="SW-66454_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-66455_0" Pin1InfoVect1LinkObjId="SW-213891_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6003,-1039 6003,-1076 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2a47910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6003,-1112 6003,-1132 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="20308@1" ObjectIDZND0="20303@0" Pin0InfoVect0LinkObjId="SW-66449_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66454_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6003,-1112 6003,-1132 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2a4b560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5169,-1238 5169,-1582 6101,-1582 6101,-1039 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="31880@x" ObjectIDND1="31877@x" ObjectIDND2="31880@x" ObjectIDZND0="31804@x" ObjectIDZND1="31805@x" Pin0InfoVect0LinkObjId="SW-213891_0" Pin0InfoVect1LinkObjId="SW-213892_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-213927_0" Pin1InfoVect1LinkObjId="SW-213923_0" Pin1InfoVect2LinkObjId="SW-213927_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5169,-1238 5169,-1582 6101,-1582 6101,-1039 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2a4b750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6111,-1040 6101,-1040 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="31805@0" ObjectIDZND0="31880@x" ObjectIDZND1="31877@x" ObjectIDZND2="31880@x" Pin0InfoVect0LinkObjId="SW-213927_0" Pin0InfoVect1LinkObjId="SW-213923_0" Pin0InfoVect2LinkObjId="SW-213927_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213892_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="6111,-1040 6101,-1040 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2a4b940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6101,-1039 6087,-1039 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="31880@x" ObjectIDND1="31877@x" ObjectIDND2="31880@x" ObjectIDZND0="31804@1" Pin0InfoVect0LinkObjId="SW-213891_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-213927_0" Pin1InfoVect1LinkObjId="SW-213923_0" Pin1InfoVect2LinkObjId="SW-213927_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6101,-1039 6087,-1039 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2a57a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3995,-1056 4021,-1056 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="31801@1" ObjectIDZND0="20274@x" ObjectIDZND1="20275@x" Pin0InfoVect0LinkObjId="SW-66422_0" Pin0InfoVect1LinkObjId="SW-66423_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213887_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3995,-1056 4021,-1056 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2a5d3c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3940,-1056 3959,-1056 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="31814@x" ObjectIDND1="31815@x" ObjectIDND2="31812@x" ObjectIDZND0="31801@0" Pin0InfoVect0LinkObjId="SW-213887_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-213900_0" Pin1InfoVect1LinkObjId="SW-213899_0" Pin1InfoVect2LinkObjId="SW-213897_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3940,-1056 3959,-1056 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2a5d620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3915,-1056 3940,-1056 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="31802@1" ObjectIDZND0="31814@x" ObjectIDZND1="31815@x" ObjectIDZND2="31812@x" Pin0InfoVect0LinkObjId="SW-213900_0" Pin0InfoVect1LinkObjId="SW-213899_0" Pin0InfoVect2LinkObjId="SW-213897_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213888_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3915,-1056 3940,-1056 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="31788" cx="4260" cy="-1441" fill="rgb(213,0,0)" r="4" stroke="rgb(213,0,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31788" cx="4021" cy="-1441" fill="rgb(213,0,0)" r="4" stroke="rgb(213,0,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31791" cx="4260" cy="-659" fill="rgb(213,0,0)" r="4" stroke="rgb(213,0,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31791" cx="4021" cy="-659" fill="rgb(213,0,0)" r="4" stroke="rgb(213,0,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31786" cx="4717" cy="-1437" fill="rgb(213,0,0)" r="4" stroke="rgb(213,0,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31786" cx="4992" cy="-1437" fill="rgb(213,0,0)" r="4" stroke="rgb(213,0,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31786" cx="5242" cy="-1437" fill="rgb(213,0,0)" r="4" stroke="rgb(213,0,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31786" cx="5457" cy="-1437" fill="rgb(213,0,0)" r="4" stroke="rgb(213,0,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31787" cx="4717" cy="-842" fill="rgb(213,0,0)" r="4" stroke="rgb(213,0,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31787" cx="4992" cy="-842" fill="rgb(213,0,0)" r="4" stroke="rgb(213,0,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31787" cx="5242" cy="-842" fill="rgb(213,0,0)" r="4" stroke="rgb(213,0,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31787" cx="5457" cy="-842" fill="rgb(213,0,0)" r="4" stroke="rgb(213,0,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31789" cx="5788" cy="-1439" fill="rgb(213,0,0)" r="4" stroke="rgb(213,0,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31789" cx="6003" cy="-1439" fill="rgb(213,0,0)" r="4" stroke="rgb(213,0,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31790" cx="5788" cy="-844" fill="rgb(213,0,0)" r="4" stroke="rgb(213,0,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31790" cx="6003" cy="-844" fill="rgb(213,0,0)" r="4" stroke="rgb(213,0,0)" stroke-width="1"/>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2645e30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4794.000000 -1464.000000) translate(0,16)">500kVI母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_26466a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4475.000000 -1534.000000) translate(0,16)">小楚乙线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_25ce520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4340.000000 -1614.000000) translate(0,16)">小楚甲线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_25cebb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4613.000000 -873.000000) translate(0,16)">500kVII母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimSun" font-size="38" graphid="g_25cdda0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3691.500000 -1710.500000) translate(0,31)">小楚甲乙线、金楚甲乙线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_264cb50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4730.000000 -1350.000000) translate(0,16)">5051</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_264ce40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4731.000000 -1157.000000) translate(0,16)">5052</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_264d140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5004.000000 -1155.000000) translate(0,16)">5042</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_264d5a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5004.000000 -963.000000) translate(0,16)">5043</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_264d960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5252.000000 -1351.000000) translate(0,16)">5031</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_264dd80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5251.000000 -1153.000000) translate(0,16)">5032</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_264f6e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5470.000000 -1348.000000) translate(0,16)">5021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_264f920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5470.000000 -1152.000000) translate(0,16)">5022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_25f73a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4060.000000 -636.000000) translate(0,16)">500kV小湾电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_25ce350" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3896.000000 -690.000000) translate(0,16)">500kVII母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_25cef40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3964.000000 -1466.000000) translate(0,16)">500kVI母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_25eae80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5031.000000 -794.000000) translate(0,16)">±800kV楚雄换流站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a33000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5521.000000 -1611.000000) translate(0,16)">金楚乙线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a33a00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5620.000000 -1529.000000) translate(0,16)">金楚甲线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a35400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5839.000000 -807.000000) translate(0,16)">500kV金安桥电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a35f20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5622.000000 -1466.000000) translate(0,16)">500kVI母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a36150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5658.000000 -875.000000) translate(0,16)">500kVII母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a47b70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4028.000000 -1411.000000) translate(0,16)">50311</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a481a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4030.000000 -1354.000000) translate(0,16)">5031</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a483e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4028.000000 -1288.000000) translate(0,16)">50312</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a48620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4028.000000 -1215.000000) translate(0,16)">50321</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a48860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4030.000000 -1158.000000) translate(0,16)">5032</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a48aa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4028.000000 -1092.000000) translate(0,16)">50322</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a48ce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4028.000000 -1024.000000) translate(0,16)">50331</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a48f20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4028.000000 -901.000000) translate(0,16)">50332</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a49160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4030.000000 -967.000000) translate(0,16)">5033</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a493a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4030.000000 -779.000000) translate(0,16)">5034</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a495e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4028.000000 -836.000000) translate(0,16)">50341</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a49820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4028.000000 -713.000000) translate(0,16)">50342</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a49a60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4269.000000 -1353.000000) translate(0,16)">5021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a49ca0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4267.000000 -1287.000000) translate(0,16)">50212</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a49ee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4267.000000 -1410.000000) translate(0,16)">50211</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a4a120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4267.000000 -1214.000000) translate(0,16)">50221</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a4a360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4269.000000 -1157.000000) translate(0,16)">5022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a4a5a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4267.000000 -1105.000000) translate(0,16)">50222</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a4a7e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4267.000000 -1013.000000) translate(0,16)">50231</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a4aa20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4269.000000 -966.000000) translate(0,16)">5023</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a4ac60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4267.000000 -900.000000) translate(0,16)">50232</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a4aea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4267.000000 -835.000000) translate(0,16)">50241</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a4b0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4269.000000 -778.000000) translate(0,16)">5024</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a4b320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4267.000000 -712.000000) translate(0,16)">50242</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a4bb30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5795.000000 -1408.000000) translate(0,16)">50311</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a4bed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5797.000000 -1351.000000) translate(0,16)">5031</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a4c110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5795.000000 -1285.000000) translate(0,16)">50312</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a4c350" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5797.000000 -1155.000000) translate(0,16)">5032</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a4c590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5795.000000 -1212.000000) translate(0,16)">50321</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a4c7d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5795.000000 -1089.000000) translate(0,16)">50322</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a4ca10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5795.000000 -1021.000000) translate(0,16)">50331</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a4cc50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5797.000000 -964.000000) translate(0,16)">5033</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a4ce90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5795.000000 -898.000000) translate(0,16)">50332</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a4d0d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6010.000000 -1406.000000) translate(0,16)">50211</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a4d310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6012.000000 -1349.000000) translate(0,16)">5021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a4d550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6010.000000 -1283.000000) translate(0,16)">50212</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a4d790" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6010.000000 -1210.000000) translate(0,16)">50221</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a4d9d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6012.000000 -1153.000000) translate(0,16)">5022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a4dc10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6010.000000 -1101.000000) translate(0,16)">50222</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a4de50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6010.000000 -1019.000000) translate(0,16)">50231</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a4e090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6012.000000 -962.000000) translate(0,16)">5023</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a4e2d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6010.000000 -896.000000) translate(0,16)">50232</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a5d880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3946.000000 -1092.000000) translate(0,16)">50326</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a5e070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3848.000000 -1094.000000) translate(0,16)">5032617</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a5e5e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4304.000000 -1048.000000) translate(0,16)">50226</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a5ea90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4397.000000 -1089.000000) translate(0,16)">5022617</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a5ecd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4726.000000 -1406.000000) translate(0,16)">50511</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a5ef10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4726.000000 -1287.000000) translate(0,16)">50512</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a5f150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4556.000000 -1268.000000) translate(0,16)">505167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a5f390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4731.000000 -1210.000000) translate(0,16)">50521</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a5f5d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4728.000000 -1084.000000) translate(0,16)">50522</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a5f810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4732.000000 -1021.000000) translate(0,16)">50531</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a5fa50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4736.000000 -959.000000) translate(0,16)">5053</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a5fc90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4734.000000 -897.000000) translate(0,16)">50532</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a5fed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5006.000000 -1409.000000) translate(0,16)">50411</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a60110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5008.000000 -1352.000000) translate(0,16)">5041</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a60350" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5006.000000 -1286.000000) translate(0,16)">50412</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a60590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5006.000000 -1213.000000) translate(0,16)">50421</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a607d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5006.000000 -1090.000000) translate(0,16)">50422</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a60a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5006.000000 -1022.000000) translate(0,16)">50431</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a60c50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5006.000000 -899.000000) translate(0,16)">50432</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a60e90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4834.000000 -1084.000000) translate(0,16)">504367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a610d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5257.000000 -1406.000000) translate(0,16)">50311</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a61310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5259.000000 -1284.000000) translate(0,16)">50312</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a61550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5085.000000 -1273.000000) translate(0,16)">503167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a61790" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5258.000000 -1212.000000) translate(0,16)">50321</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a619d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5260.000000 -1088.000000) translate(0,16)">50322</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a61c10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5258.000000 -1022.000000) translate(0,16)">50331</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a61e50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5263.000000 -963.000000) translate(0,16)">5033</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a62090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5260.000000 -897.000000) translate(0,16)">50332</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a622d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5464.000000 -1404.000000) translate(0,16)">50211</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a62510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5470.000000 -1282.000000) translate(0,16)">50212</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a62750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5556.000000 -1281.000000) translate(0,16)">502167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a62990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5475.000000 -1210.000000) translate(0,16)">50221</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a62bd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5476.000000 -1088.000000) translate(0,16)">50222</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a62e10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5477.000000 -1017.000000) translate(0,16)">50231</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a63050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5480.000000 -964.000000) translate(0,16)">5023</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a63290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5476.000000 -896.000000) translate(0,16)">50232</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a634d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5811.000000 -1260.000000) translate(0,16)">50316</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a63710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5890.000000 -1260.000000) translate(0,16)">5031617</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a63950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6033.000000 -1072.000000) translate(0,16)">50236</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a63b90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6112.000000 -1075.000000) translate(0,16)">5023617</text>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-213906">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4708.000000 -1252.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31818" ObjectName="SW-CX_TASE2JS.YN_XCⅡ_50512SW"/>
     <cge:Meas_Ref ObjectId="213906"/>
    <cge:TPSR_Ref TObjectID="31818"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213904">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4708.000000 -1179.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31820" ObjectName="SW-CX_TASE2JS.YN_XCⅡ_50521SW"/>
     <cge:Meas_Ref ObjectId="213904"/>
    <cge:TPSR_Ref TObjectID="31820"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213905">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4708.000000 -1056.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31821" ObjectName="SW-CX_TASE2JS.YN_XCⅡ_50522SW"/>
     <cge:Meas_Ref ObjectId="213905"/>
    <cge:TPSR_Ref TObjectID="31821"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213908">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4708.000000 -865.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31819" ObjectName="SW-CX_TASE2JS.YN_XCⅡ_50532SW"/>
     <cge:Meas_Ref ObjectId="213908"/>
    <cge:TPSR_Ref TObjectID="31819"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213902">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4708.000000 -988.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31816" ObjectName="SW-CX_TASE2JS.YN_XCⅡ_50531SW"/>
     <cge:Meas_Ref ObjectId="213902"/>
    <cge:TPSR_Ref TObjectID="31816"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213903">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4708.000000 -1375.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31817" ObjectName="SW-CX_TASE2JS.YN_XCⅡ_50511SW"/>
     <cge:Meas_Ref ObjectId="213903"/>
    <cge:TPSR_Ref TObjectID="31817"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213901">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4983.000000 -1254.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31810" ObjectName="SW-CX_TASE2JS.YN_XCⅠ_50412SW"/>
     <cge:Meas_Ref ObjectId="213901"/>
    <cge:TPSR_Ref TObjectID="31810"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213896">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4983.000000 -1181.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31811" ObjectName="SW-CX_TASE2JS.YN_XCⅠ_50421SW"/>
     <cge:Meas_Ref ObjectId="213896"/>
    <cge:TPSR_Ref TObjectID="31811"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213899">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4983.000000 -1058.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31815" ObjectName="SW-CX_TASE2JS.YN_XCⅠ_50422SW"/>
     <cge:Meas_Ref ObjectId="213899"/>
    <cge:TPSR_Ref TObjectID="31815"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213898">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4983.000000 -867.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31813" ObjectName="SW-CX_TASE2JS.YN_XCⅠ_50432SW"/>
     <cge:Meas_Ref ObjectId="213898"/>
    <cge:TPSR_Ref TObjectID="31813"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213897">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4983.000000 -990.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31812" ObjectName="SW-CX_TASE2JS.YN_XCⅠ_50431SW"/>
     <cge:Meas_Ref ObjectId="213897"/>
    <cge:TPSR_Ref TObjectID="31812"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213895">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4983.000000 -1377.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31809" ObjectName="SW-CX_TASE2JS.YN_XCⅠ_50411SW"/>
     <cge:Meas_Ref ObjectId="213895"/>
    <cge:TPSR_Ref TObjectID="31809"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213900">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4849.000000 -1041.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31814" ObjectName="SW-CX_TASE2JS.YN_XCⅠ_504367SW"/>
     <cge:Meas_Ref ObjectId="213900"/>
    <cge:TPSR_Ref TObjectID="31814"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213925">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5233.000000 -1376.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31876" ObjectName="SW-CX_TASE2JS.YN_JCⅡ_50311SW"/>
     <cge:Meas_Ref ObjectId="213925"/>
    <cge:TPSR_Ref TObjectID="31876"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213927">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5233.000000 -1253.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31880" ObjectName="SW-CX_TASE2JS.YN_JCⅡ_50312SW"/>
     <cge:Meas_Ref ObjectId="213927"/>
    <cge:TPSR_Ref TObjectID="31880"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213923">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5233.000000 -1180.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31877" ObjectName="SW-CX_TASE2JS.YN_JCⅡ_50321SW"/>
     <cge:Meas_Ref ObjectId="213923"/>
    <cge:TPSR_Ref TObjectID="31877"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213924">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5233.000000 -1057.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31878" ObjectName="SW-CX_TASE2JS.YN_JCⅡ_50322SW"/>
     <cge:Meas_Ref ObjectId="213924"/>
    <cge:TPSR_Ref TObjectID="31878"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213928">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5233.000000 -866.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31881" ObjectName="SW-CX_TASE2JS.YN_JCⅡ_50332SW"/>
     <cge:Meas_Ref ObjectId="213928"/>
    <cge:TPSR_Ref TObjectID="31881"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213922">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5233.000000 -989.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31875" ObjectName="SW-CX_TASE2JS.YN_JCⅡ_50331SW"/>
     <cge:Meas_Ref ObjectId="213922"/>
    <cge:TPSR_Ref TObjectID="31875"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213918">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5448.000000 -1251.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31869" ObjectName="SW-CX_TASE2JS.YN_JCⅠ_50212SW"/>
     <cge:Meas_Ref ObjectId="213918"/>
    <cge:TPSR_Ref TObjectID="31869"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213919">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5552.000000 -1240.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31872" ObjectName="SW-CX_TASE2JS.YN_JCⅠ_502167SW"/>
     <cge:Meas_Ref ObjectId="213919"/>
    <cge:TPSR_Ref TObjectID="31872"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213916">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5448.000000 -1178.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31871" ObjectName="SW-CX_TASE2JS.YN_JCⅠ_50221SW"/>
     <cge:Meas_Ref ObjectId="213916"/>
    <cge:TPSR_Ref TObjectID="31871"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213917">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5448.000000 -1055.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31870" ObjectName="SW-CX_TASE2JS.YN_JCⅠ_50222SW"/>
     <cge:Meas_Ref ObjectId="213917"/>
    <cge:TPSR_Ref TObjectID="31870"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213920">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5448.000000 -864.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31873" ObjectName="SW-CX_TASE2JS.YN_JCⅠ_50232SW"/>
     <cge:Meas_Ref ObjectId="213920"/>
    <cge:TPSR_Ref TObjectID="31873"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213914">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5448.000000 -987.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31867" ObjectName="SW-CX_TASE2JS.YN_JCⅠ_50231SW"/>
     <cge:Meas_Ref ObjectId="213914"/>
    <cge:TPSR_Ref TObjectID="31867"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213915">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5448.000000 -1374.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31868" ObjectName="SW-CX_TASE2JS.YN_JCⅠ_50211SW"/>
     <cge:Meas_Ref ObjectId="213915"/>
    <cge:TPSR_Ref TObjectID="31868"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66431">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4251.000000 -1380.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20285" ObjectName="SW-CX_TASE2JS.YN_XCⅡ_50211SW"/>
     <cge:Meas_Ref ObjectId="66431"/>
    <cge:TPSR_Ref TObjectID="20285"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66432">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4251.000000 -1257.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20288" ObjectName="SW-CX_TASE2JS.YN_XCⅡ_50212SW"/>
     <cge:Meas_Ref ObjectId="66432"/>
    <cge:TPSR_Ref TObjectID="20288"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66433">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4251.000000 -1184.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20286" ObjectName="SW-CX_TASE2JS.YN_XCⅡ_50221SW"/>
     <cge:Meas_Ref ObjectId="66433"/>
    <cge:TPSR_Ref TObjectID="20286"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66434">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4251.000000 -1075.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20287" ObjectName="SW-CX_TASE2JS.YN_XCⅡ_50222SW"/>
     <cge:Meas_Ref ObjectId="66434"/>
    <cge:TPSR_Ref TObjectID="20287"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66436">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4251.000000 -870.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20290" ObjectName="SW-CX_TASE2JS.YN_XCⅡ_50232SW"/>
     <cge:Meas_Ref ObjectId="66436"/>
    <cge:TPSR_Ref TObjectID="20290"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66435">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4251.000000 -983.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20289" ObjectName="SW-CX_TASE2JS.YN_XCⅡ_50231SW"/>
     <cge:Meas_Ref ObjectId="66435"/>
    <cge:TPSR_Ref TObjectID="20289"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213886">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4404.000000 -1050.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31803" ObjectName="SW-CX_TASE2JS.YN_XCⅠ_5022617SW"/>
     <cge:Meas_Ref ObjectId="213886"/>
    <cge:TPSR_Ref TObjectID="31803"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66438">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4251.000000 -682.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20292" ObjectName="SW-CX_TASE2JS.YN_XCⅡ_50242SW"/>
     <cge:Meas_Ref ObjectId="66438"/>
    <cge:TPSR_Ref TObjectID="20292"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66437">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4251.000000 -805.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20291" ObjectName="SW-CX_TASE2JS.YN_XCⅡ_50241SW"/>
     <cge:Meas_Ref ObjectId="66437"/>
    <cge:TPSR_Ref TObjectID="20291"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66419">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4012.000000 -1381.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20271" ObjectName="SW-CX_TASE2JS.YN_XCⅠ_50311SW"/>
     <cge:Meas_Ref ObjectId="66419"/>
    <cge:TPSR_Ref TObjectID="20271"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66420">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4012.000000 -1258.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20272" ObjectName="SW-CX_TASE2JS.YN_XCⅠ_50312SW"/>
     <cge:Meas_Ref ObjectId="66420"/>
    <cge:TPSR_Ref TObjectID="20272"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66421">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4012.000000 -1185.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20273" ObjectName="SW-CX_TASE2JS.YN_XCⅠ_50321SW"/>
     <cge:Meas_Ref ObjectId="66421"/>
    <cge:TPSR_Ref TObjectID="20273"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66422">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4012.000000 -1062.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20274" ObjectName="SW-CX_TASE2JS.YN_XCⅠ_50322SW"/>
     <cge:Meas_Ref ObjectId="66422"/>
    <cge:TPSR_Ref TObjectID="20274"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66424">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4012.000000 -871.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20276" ObjectName="SW-CX_TASE2JS.YN_XCⅠ_50332SW"/>
     <cge:Meas_Ref ObjectId="66424"/>
    <cge:TPSR_Ref TObjectID="20276"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66423">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4012.000000 -994.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20275" ObjectName="SW-CX_TASE2JS.YN_XCⅠ_50331SW"/>
     <cge:Meas_Ref ObjectId="66423"/>
    <cge:TPSR_Ref TObjectID="20275"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213888">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3874.000000 -1051.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31802" ObjectName="SW-CX_TASE2JS.YN_XCⅠ_5032617SW"/>
     <cge:Meas_Ref ObjectId="213888"/>
    <cge:TPSR_Ref TObjectID="31802"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66426">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4012.000000 -683.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20278" ObjectName="SW-CX_TASE2JS.YN_XCⅠ_50342SW"/>
     <cge:Meas_Ref ObjectId="66426"/>
    <cge:TPSR_Ref TObjectID="20278"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66425">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4012.000000 -806.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20277" ObjectName="SW-CX_TASE2JS.YN_XCⅠ_50341SW"/>
     <cge:Meas_Ref ObjectId="66425"/>
    <cge:TPSR_Ref TObjectID="20277"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213887">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3954.000000 -1051.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31801" ObjectName="SW-CX_TASE2JS.YN_XCⅠ_50326SW"/>
     <cge:Meas_Ref ObjectId="213887"/>
    <cge:TPSR_Ref TObjectID="31801"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213885">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4303.000000 -1050.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31800" ObjectName="SW-CX_TASE2JS.YN_XCⅠ_50226SW"/>
     <cge:Meas_Ref ObjectId="213885"/>
    <cge:TPSR_Ref TObjectID="31800"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213907">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4564.000000 -1234.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31822" ObjectName="SW-CX_TASE2JS.YN_XCⅡ_505167SW"/>
     <cge:Meas_Ref ObjectId="213907"/>
    <cge:TPSR_Ref TObjectID="31822"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66442">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5779.000000 -1378.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20296" ObjectName="SW-CX_TASE2JS.YN_JCⅠ_50311SW"/>
     <cge:Meas_Ref ObjectId="66442"/>
    <cge:TPSR_Ref TObjectID="20296"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66443">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5779.000000 -1255.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20297" ObjectName="SW-CX_TASE2JS.YN_JCⅠ_50312SW"/>
     <cge:Meas_Ref ObjectId="66443"/>
    <cge:TPSR_Ref TObjectID="20297"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66444">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5779.000000 -1182.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20298" ObjectName="SW-CX_TASE2JS.YN_JCⅠ_50321SW"/>
     <cge:Meas_Ref ObjectId="66444"/>
    <cge:TPSR_Ref TObjectID="20298"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66445">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5779.000000 -1059.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20299" ObjectName="SW-CX_TASE2JS.YN_JCⅠ_50322SW"/>
     <cge:Meas_Ref ObjectId="66445"/>
    <cge:TPSR_Ref TObjectID="20299"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66447">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5779.000000 -868.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20301" ObjectName="SW-CX_TASE2JS.YN_JCⅠ_50332SW"/>
     <cge:Meas_Ref ObjectId="66447"/>
    <cge:TPSR_Ref TObjectID="20301"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66446">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5779.000000 -991.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20300" ObjectName="SW-CX_TASE2JS.YN_JCⅠ_50331SW"/>
     <cge:Meas_Ref ObjectId="66446"/>
    <cge:TPSR_Ref TObjectID="20300"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66452">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5994.000000 -1253.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20306" ObjectName="SW-CX_TASE2JS.YN_JCⅡ_50212SW"/>
     <cge:Meas_Ref ObjectId="66452"/>
    <cge:TPSR_Ref TObjectID="20306"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213892">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6107.000000 -1035.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31805" ObjectName="SW-CX_TASE2JS.YN_JCⅡ_5023617SW"/>
     <cge:Meas_Ref ObjectId="213892"/>
    <cge:TPSR_Ref TObjectID="31805"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66453">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5994.000000 -1180.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20307" ObjectName="SW-CX_TASE2JS.YN_JCⅡ_50221SW"/>
     <cge:Meas_Ref ObjectId="66453"/>
    <cge:TPSR_Ref TObjectID="20307"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66454">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5994.000000 -1071.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20308" ObjectName="SW-CX_TASE2JS.YN_JCⅡ_50222SW"/>
     <cge:Meas_Ref ObjectId="66454"/>
    <cge:TPSR_Ref TObjectID="20308"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66456">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5994.000000 -866.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20310" ObjectName="SW-CX_TASE2JS.YN_JCⅡ_50232SW"/>
     <cge:Meas_Ref ObjectId="66456"/>
    <cge:TPSR_Ref TObjectID="20310"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66455">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5994.000000 -989.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20309" ObjectName="SW-CX_TASE2JS.YN_JCⅡ_50231SW"/>
     <cge:Meas_Ref ObjectId="66455"/>
    <cge:TPSR_Ref TObjectID="20309"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66451">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5994.000000 -1376.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20305" ObjectName="SW-CX_TASE2JS.YN_JCⅡ_50211SW"/>
     <cge:Meas_Ref ObjectId="66451"/>
    <cge:TPSR_Ref TObjectID="20305"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213926">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5108.000000 -1235.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31879" ObjectName="SW-CX_TASE2JS.YN_JCⅡ_503167SW"/>
     <cge:Meas_Ref ObjectId="213926"/>
    <cge:TPSR_Ref TObjectID="31879"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213890">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5898.000000 -1226.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31807" ObjectName="SW-CX_TASE2JS.YN_JCⅠ_5031617SW"/>
     <cge:Meas_Ref ObjectId="213890"/>
    <cge:TPSR_Ref TObjectID="31807"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213889">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5812.000000 -1226.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31806" ObjectName="SW-CX_TASE2JS.YN_JCⅠ_50316SW"/>
     <cge:Meas_Ref ObjectId="213889"/>
    <cge:TPSR_Ref TObjectID="31806"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213891">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6046.000000 -1034.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31804" ObjectName="SW-CX_TASE2JS.YN_JCⅡ_50236SW"/>
     <cge:Meas_Ref ObjectId="213891"/>
    <cge:TPSR_Ref TObjectID="31804"/></metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3691.500000 -1624.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-66367" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5007.000000 -1524.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="66367" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-66368" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5007.000000 -1509.500000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="66368" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-66369" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5007.000000 -1494.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="66369" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-66370" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4743.000000 -1519.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="66370" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-66371" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4743.000000 -1504.500000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="66371" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-66372" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4743.000000 -1489.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="66372" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-66373" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5967.000000 -1517.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="66373" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-66374" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5967.000000 -1502.500000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="66374" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-66375" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5967.000000 -1487.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="66375" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-66376" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6055.000000 -1634.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="66376" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-66377" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6055.000000 -1619.500000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="66377" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-66378" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6055.000000 -1604.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="66378" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_局属变.svg" style="fill-opacity:0"><rect height="84" qtmmishow="hidden" width="503" x="3616" y="-1734"/></g>
   <g href="cx_索引_接线图_局属变.svg" style="fill-opacity:0"><rect height="131" qtmmishow="hidden" width="173" x="3517" y="-1765"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a52a90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4674.000000 1516.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a53ca0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4663.000000 1501.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a54cc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4688.000000 1486.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a556d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4943.000000 1523.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a55960" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4932.000000 1508.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a55ba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4957.000000 1493.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a55fc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5900.000000 1516.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a56280" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5889.000000 1501.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a564c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5914.000000 1486.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a568e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5997.000000 1633.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a56ba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5986.000000 1618.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a56de0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6011.000000 1603.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="84" qtmmishow="hidden" width="503" x="3616" y="-1734"/>
    </a>
   <metadata/><rect fill="white" height="84" opacity="0" stroke="white" transform="" width="503" x="3616" y="-1734"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="131" qtmmishow="hidden" width="173" x="3517" y="-1765"/>
    </a>
   <metadata/><rect fill="white" height="131" opacity="0" stroke="white" transform="" width="173" x="3517" y="-1765"/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_25ff310" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4808.000000 -1038.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    <cge:TPSR_Ref TObjectID="0"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2603f60" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5608.000000 -1235.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    <cge:TPSR_Ref TObjectID="0"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_266ad00" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4462.000000 -1045.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    <cge:TPSR_Ref TObjectID="0"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25fa3b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3842.000000 -1050.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    <cge:TPSR_Ref TObjectID="0"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25e4410" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4515.000000 -1233.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    <cge:TPSR_Ref TObjectID="0"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a21a70" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6163.000000 -1030.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    <cge:TPSR_Ref TObjectID="0"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a3a0f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5059.000000 -1234.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    <cge:TPSR_Ref TObjectID="0"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a3f4e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5954.000000 -1221.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    <cge:TPSR_Ref TObjectID="0"/></metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1"/>
</svg>