<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-73" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="3115 -1202 2154 1204">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="29" x2="29" y1="7" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="4" x2="22" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="22" x2="22" y1="0" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="25" x2="25" y1="6" y2="13"/>
   </symbol>
   <symbol id="hydroGenerator:shape2">
    <polyline points="12,27 11,28 12,28 12,29 12,30 13,31 13,32 14,33 15,33 15,34 16,34 17,35 18,35 19,35 20,35 21,35 22,34 23,34 24,33 25,33 25,32 26,31 26,30 27,29 27,28 27,28 27,27 " stroke-width="0.06"/>
    <circle cx="27" cy="27" fillStyle="0" r="26.5" stroke-width="0.55102"/>
    <polyline arcFlag="1" points="28,27 28,26 28,25 28,24 29,23 29,22 30,21 30,21 31,20 32,20 33,19 34,19 35,19 36,18 37,19 38,19 39,19 40,20 40,20 41,21 42,21 42,22 43,23 43,24 43,25 44,26 43,27 " stroke-width="0.06"/>
   </symbol>
   <symbol id="lightningRod:shape81">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="40" y2="27"/>
    <ellipse cx="9" cy="18" fillStyle="0" rx="8.5" ry="8" stroke-width="1"/>
    <circle cx="9" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="18" cy="12" fillStyle="0" r="8.5" stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape44">
    <ellipse cx="11" cy="16" fillStyle="0" rx="10.5" ry="11.5" stroke-width="0.64567"/>
    <ellipse cx="11" cy="29" fillStyle="0" rx="10.5" ry="11.5" stroke-width="0.64567"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="7" x2="15" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="15" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="7" x2="15" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="15" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="transformer2:shape0_0">
    <circle cx="25" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="17" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="34" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="17" x2="34" y1="18" y2="18"/>
   </symbol>
   <symbol id="transformer2:shape0_1">
    <ellipse cx="25" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="24" y1="58" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="32" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="16" x2="24" y1="74" y2="66"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_198e740" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_198f120" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_198fb00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_19902c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_19912d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1991ee0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1992a90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_19934c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1993d30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_19946d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_19946d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_19964d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_19964d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_19975e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_19991b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1999da0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_199ab60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_199b4a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_199c520" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_199cd20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_199d410" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_199de30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_199f010" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_199f990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_19a0480" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_19a0e40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_19a2330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_19a2e90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_19a4100" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_19a4d90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_19b3590" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_19b3dc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_19a6f80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_19a8560" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1214" width="2164" x="3110" y="-1207"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="1201" stroke="rgb(21,40,56)" stroke-width="1" width="2150" x="3118" y="-1202"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3116" y="-1198"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3116" y="-1078"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3116" y="-598"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-208612">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4247.000000 -804.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8067" ObjectName="SW-CX_KLH.CX_KLH_0011SW"/>
     <cge:Meas_Ref ObjectId="208612"/>
    <cge:TPSR_Ref TObjectID="8067"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-208609">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3952.000000 -803.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11275" ObjectName="SW-CX_KLH.CX_KLH_0901SW"/>
     <cge:Meas_Ref ObjectId="208609"/>
    <cge:TPSR_Ref TObjectID="11275"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-208610">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3998.000000 -773.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11276" ObjectName="SW-CX_KLH.CX_KLH_09017SW"/>
     <cge:Meas_Ref ObjectId="208610"/>
    <cge:TPSR_Ref TObjectID="11276"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-208613">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4294.000000 -772.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8068" ObjectName="SW-CX_KLH.CX_KLH_00117SW"/>
     <cge:Meas_Ref ObjectId="208613"/>
    <cge:TPSR_Ref TObjectID="8068"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-208615">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4295.000000 -520.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8065" ObjectName="SW-CX_KLH.CX_KLH_60117SW"/>
     <cge:Meas_Ref ObjectId="208615"/>
    <cge:TPSR_Ref TObjectID="8065"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-208620">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4009.000000 -390.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11281" ObjectName="SW-CX_KLH.CX_KLH_66117SW_S"/>
     <cge:Meas_Ref ObjectId="208620"/>
    <cge:TPSR_Ref TObjectID="11281"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-208617">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4448.000000 -390.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11284" ObjectName="SW-CX_KLH.CX_KLH_69017SW_S"/>
     <cge:Meas_Ref ObjectId="208617"/>
    <cge:TPSR_Ref TObjectID="11284"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-208622">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4538.000000 -708.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11278" ObjectName="SW-CX_KLH.CX_KLH_0021SW_S"/>
     <cge:Meas_Ref ObjectId="208622"/>
    <cge:TPSR_Ref TObjectID="11278"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-208623">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4585.000000 -678.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11279" ObjectName="SW-CX_KLH.CX_KLH_00217SW_S"/>
     <cge:Meas_Ref ObjectId="208623"/>
    <cge:TPSR_Ref TObjectID="11279"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-208619">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3962.000000 -420.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11280" ObjectName="SW-CX_KLH.CX_KLH_6611SW_S"/>
     <cge:Meas_Ref ObjectId="208619"/>
    <cge:TPSR_Ref TObjectID="11280"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-208616">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4402.000000 -418.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11283" ObjectName="SW-CX_KLH.CX_KLH_6901SW_S"/>
     <cge:Meas_Ref ObjectId="208616"/>
    <cge:TPSR_Ref TObjectID="11283"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-208614">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4247.000000 -563.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8064" ObjectName="SW-CX_KLH.CX_KLH_6011SW"/>
     <cge:Meas_Ref ObjectId="208614"/>
    <cge:TPSR_Ref TObjectID="8064"/></metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_KLH.CX_KLH_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="11562"/>
     </metadata>
     <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4238.000000 -636.000000)" xlink:href="#transformer2:shape0_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4238.000000 -636.000000)" xlink:href="#transformer2:shape0_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="8069" ObjectName="TF-CX_KLH.CX_KLH_1T"/>
    </metadata>
   </g>
  </g><g id="HydroGenerator_Layer">
   <g DF8003:Layer="PUBLIC" id="SM-CX_KLH.P1">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4226.000000 -218.000000)" xlink:href="#hydroGenerator:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43420" ObjectName="SM-CX_KLH.P1"/>
    <cge:TPSR_Ref TObjectID="43420"/></metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-10KV" id="g_2507ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4255,-882 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="busSection" ObjectIDND0="11272@0" ObjectIDZND0="11272@0" Pin0InfoVect0LinkObjId="g_19d67c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_19d67c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4255,-882 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1c59730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4417,-352 4465,-352 4465,-333 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_14ec300@0" ObjectIDND1="g_1fbacf0@0" ObjectIDZND0="g_1a880b0@0" Pin0InfoVect0LinkObjId="g_1a880b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_14ec300_0" Pin1InfoVect1LinkObjId="g_1fbacf0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4417,-352 4465,-352 4465,-333 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c221a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3967,-882 3967,-861 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="11272@0" ObjectIDZND0="11275@1" Pin0InfoVect0LinkObjId="SW-208609_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2507ab0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3967,-882 3967,-861 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_192c760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3967,-825 3967,-799 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="11275@0" ObjectIDZND0="11276@x" ObjectIDZND1="g_151c7d0@0" Pin0InfoVect0LinkObjId="SW-208610_0" Pin0InfoVect1LinkObjId="g_151c7d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-208609_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3967,-825 3967,-799 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a82c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3967,-799 3989,-799 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="11275@x" ObjectIDND1="g_151c7d0@0" ObjectIDZND0="11276@0" Pin0InfoVect0LinkObjId="SW-208610_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-208609_0" Pin1InfoVect1LinkObjId="g_151c7d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3967,-799 3989,-799 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fc18a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4025,-799 4047,-799 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="11276@1" ObjectIDZND0="g_244b770@0" Pin0InfoVect0LinkObjId="g_244b770_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-208610_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4025,-799 4047,-799 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19d67c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4255,-1067 4255,-882 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="busSection" ObjectIDZND0="11272@0" Pin0InfoVect0LinkObjId="g_2507ab0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4255,-1067 4255,-882 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fb7e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4262,-798 4285,-798 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="8067@x" ObjectIDND1="8066@x" ObjectIDZND0="8068@0" Pin0InfoVect0LinkObjId="SW-208613_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-208612_0" Pin1InfoVect1LinkObjId="SW-208611_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4262,-798 4285,-798 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ac47a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4321,-798 4360,-798 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="8068@1" ObjectIDZND0="g_1d0da40@0" Pin0InfoVect0LinkObjId="g_1d0da40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-208613_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4321,-798 4360,-798 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_200c300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4262,-798 4262,-826 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="8068@x" ObjectIDND1="8066@x" ObjectIDZND0="8067@0" Pin0InfoVect0LinkObjId="SW-208612_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-208613_0" Pin1InfoVect1LinkObjId="SW-208611_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4262,-798 4262,-826 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10411c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4262,-862 4262,-882 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="8067@1" ObjectIDZND0="11272@0" Pin0InfoVect0LinkObjId="g_2507ab0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-208612_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4262,-862 4262,-882 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10352a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4262,-765 4262,-798 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="8066@1" ObjectIDZND0="8068@x" ObjectIDZND1="8067@x" Pin0InfoVect0LinkObjId="SW-208613_0" Pin0InfoVect1LinkObjId="SW-208612_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-208611_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4262,-765 4262,-798 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_21385d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4254,-497 4254,-271 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="hydroGenerator" ObjectIDND0="11273@0" ObjectIDZND0="43420@0" Pin0InfoVect0LinkObjId="SM-CX_KLH.P1_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1cadb70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4254,-497 4254,-271 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_17465d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4262,-546 4286,-546 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="busSection" EndDevType0="switch" ObjectIDND0="8064@x" ObjectIDND1="11273@0" ObjectIDZND0="8065@0" Pin0InfoVect0LinkObjId="SW-208615_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-208614_0" Pin1InfoVect1LinkObjId="g_1cadb70_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4262,-546 4286,-546 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_18d6010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4322,-546 4361,-546 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="8065@1" ObjectIDZND0="g_222c260@0" Pin0InfoVect0LinkObjId="g_222c260_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-208615_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4322,-546 4361,-546 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_183c1e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4262,-585 4262,-546 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="8064@0" ObjectIDZND0="8065@x" ObjectIDZND1="11273@0" Pin0InfoVect0LinkObjId="SW-208615_0" Pin0InfoVect1LinkObjId="g_1cadb70_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-208614_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4262,-585 4262,-546 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1cadb70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4262,-546 4262,-497 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="8065@x" ObjectIDND1="8064@x" ObjectIDZND0="11273@0" Pin0InfoVect0LinkObjId="g_14fabc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-208615_0" Pin1InfoVect1LinkObjId="SW-208614_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4262,-546 4262,-497 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_f6bf30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3978,-416 4000,-416 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="11280@x" ObjectIDND1="11282@x" ObjectIDZND0="11281@0" Pin0InfoVect0LinkObjId="SW-208620_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-208619_0" Pin1InfoVect1LinkObjId="SW-208618_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3978,-416 4000,-416 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1ce5400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4036,-416 4075,-416 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="11281@1" ObjectIDZND0="g_1d028b0@0" Pin0InfoVect0LinkObjId="g_1d028b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-208620_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4036,-416 4075,-416 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_14fabc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3977,-478 3977,-497 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="11280@1" ObjectIDZND0="11273@0" Pin0InfoVect0LinkObjId="g_1cadb70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-208619_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3977,-478 3977,-497 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_228c1c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3977,-416 3977,-442 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="11281@x" ObjectIDND1="11282@x" ObjectIDZND0="11280@0" Pin0InfoVect0LinkObjId="SW-208619_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-208620_0" Pin1InfoVect1LinkObjId="SW-208618_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3977,-416 3977,-442 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1f90f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3977,-269 3977,-355 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_1a86110@1" ObjectIDZND0="11282@0" Pin0InfoVect0LinkObjId="SW-208618_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a86110_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3977,-269 3977,-355 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1d8ab60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3977,-382 3977,-416 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="11282@1" ObjectIDZND0="11281@x" ObjectIDZND1="11280@x" Pin0InfoVect0LinkObjId="SW-208620_0" Pin0InfoVect1LinkObjId="SW-208619_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-208618_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3977,-382 3977,-416 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_21abfd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3977,-234 3977,-164 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_1a86110@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a86110_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3977,-234 3977,-164 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1a7b2b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4336,-497 4336,-329 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="lightningRod" ObjectIDND0="11273@0" ObjectIDZND0="g_22319e0@1" Pin0InfoVect0LinkObjId="g_22319e0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1cadb70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4336,-497 4336,-329 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1cea280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4417,-497 4417,-476 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="11273@0" ObjectIDZND0="11283@1" Pin0InfoVect0LinkObjId="SW-208616_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1cadb70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4417,-497 4417,-476 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1a4af20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4417,-416 4439,-416 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="11283@x" ObjectIDND1="g_1fbacf0@0" ObjectIDZND0="11284@0" Pin0InfoVect0LinkObjId="SW-208617_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-208616_0" Pin1InfoVect1LinkObjId="g_1fbacf0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4417,-416 4439,-416 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1f9c540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4475,-416 4514,-416 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="11284@1" ObjectIDZND0="g_1ca7fd0@0" Pin0InfoVect0LinkObjId="g_1ca7fd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-208617_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4475,-416 4514,-416 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2477180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4417,-440 4417,-416 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="11283@0" ObjectIDZND0="11284@x" ObjectIDZND1="g_1fbacf0@0" Pin0InfoVect0LinkObjId="SW-208617_0" Pin0InfoVect1LinkObjId="g_1fbacf0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-208616_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4417,-440 4417,-416 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_ed3d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4417,-352 4417,-333 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1a880b0@0" ObjectIDND1="g_1fbacf0@0" ObjectIDZND0="g_14ec300@1" Pin0InfoVect0LinkObjId="g_14ec300_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1a880b0_0" Pin1InfoVect1LinkObjId="g_1fbacf0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4417,-352 4417,-333 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c89ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4553,-766 4553,-882 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="11278@1" ObjectIDZND0="11272@0" Pin0InfoVect0LinkObjId="g_2507ab0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-208622_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4553,-766 4553,-882 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_117d280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4554,-704 4576,-704 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="11278@x" ObjectIDND1="11277@x" ObjectIDZND0="11279@0" Pin0InfoVect0LinkObjId="SW-208623_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-208622_0" Pin1InfoVect1LinkObjId="SW-208621_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4554,-704 4576,-704 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_efbd10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4612,-704 4651,-704 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="11279@1" ObjectIDZND0="g_1c7ed10@0" Pin0InfoVect0LinkObjId="g_1c7ed10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-208623_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4612,-704 4651,-704 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14ddf00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4553,-704 4553,-730 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="11279@x" ObjectIDND1="11277@x" ObjectIDZND0="11278@0" Pin0InfoVect0LinkObjId="SW-208622_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-208623_0" Pin1InfoVect1LinkObjId="SW-208621_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4553,-704 4553,-730 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ff1510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4553,-575 4553,-641 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_2391820@1" ObjectIDZND0="11277@0" Pin0InfoVect0LinkObjId="SW-208621_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2391820_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4553,-575 4553,-641 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2118950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4553,-668 4553,-704 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="11277@1" ObjectIDZND0="11279@x" ObjectIDZND1="11278@x" Pin0InfoVect0LinkObjId="SW-208623_0" Pin0InfoVect1LinkObjId="SW-208622_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-208621_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4553,-668 4553,-704 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2719400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4553,-540 4553,-470 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_2391820@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2391820_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4553,-540 4553,-470 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c005f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3967,-697 3967,-731 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_f63c90@0" ObjectIDZND0="g_151c7d0@0" Pin0InfoVect0LinkObjId="g_151c7d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_f63c90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3967,-697 3967,-731 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c04f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3967,-762 3967,-799 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_151c7d0@1" ObjectIDZND0="11275@x" ObjectIDZND1="11276@x" Pin0InfoVect0LinkObjId="SW-208609_0" Pin0InfoVect1LinkObjId="SW-208610_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_151c7d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3967,-762 3967,-799 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2029c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4417,-416 4417,-400 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="11284@x" ObjectIDND1="11283@x" ObjectIDZND0="g_1fbacf0@1" Pin0InfoVect0LinkObjId="g_1fbacf0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-208617_0" Pin1InfoVect1LinkObjId="SW-208616_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4417,-416 4417,-400 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1750ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4417,-369 4417,-352 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="g_1fbacf0@0" ObjectIDZND0="g_14ec300@0" ObjectIDZND1="g_1a880b0@0" Pin0InfoVect0LinkObjId="g_14ec300_0" Pin0InfoVect1LinkObjId="g_1a880b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fbacf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4417,-369 4417,-352 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f563d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4262,-738 4262,-721 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="8066@0" ObjectIDZND0="8069@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-208611_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4262,-738 4262,-721 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1fb24e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4262,-641 4262,-621 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="8069@0" ObjectIDZND0="8064@1" Pin0InfoVect0LinkObjId="SW-208614_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f563d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4262,-641 4262,-621 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="11272" cx="4255" cy="-882" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11272" cx="4255" cy="-882" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11272" cx="4262" cy="-882" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11272" cx="3967" cy="-882" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11273" cx="4254" cy="-497" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11273" cx="4262" cy="-497" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11273" cx="3977" cy="-497" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11273" cx="4417" cy="-497" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11273" cx="4336" cy="-497" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11272" cx="4553" cy="-882" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11272" cx="4255" cy="-882" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-46222" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3420.000000 -1087.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8061" ObjectName="DYN-CX_KLH"/>
     <cge:Meas_Ref ObjectId="46222"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_103f890" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4532.000000 -452.000000) translate(0,12)">至厂用II段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f84610" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3940.000000 -158.000000) translate(0,12)">至厂用I段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_f4e310" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4220.000000 -215.000000) translate(0,12)">1号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_ec01e0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4307.000000 -287.000000) translate(0,12)">励磁变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b4c630" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4401.000000 -288.000000) translate(0,12)">1YH</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1599d50" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4456.000000 -290.000000) translate(0,12)">2YH</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1884100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4271.000000 -759.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18af520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4269.000000 -851.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1879420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4283.000000 -824.000000) translate(0,12)">00117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f56cf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4269.000000 -610.000000) translate(0,12)">6011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1977710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4284.000000 -572.000000) translate(0,12)">60117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_fc15a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4290.000000 -690.000000) translate(0,15)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_fb88d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3144.000000 -1026.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_fb88d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3144.000000 -1026.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_fb88d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3144.000000 -1026.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_fb88d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3144.000000 -1026.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_fb88d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3144.000000 -1026.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_fb88d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3144.000000 -1026.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_fb88d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3144.000000 -1026.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14f77d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3144.000000 -588.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14f77d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3144.000000 -588.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14f77d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3144.000000 -588.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14f77d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3144.000000 -588.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14f77d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3144.000000 -588.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14f77d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3144.000000 -588.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14f77d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3144.000000 -588.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14f77d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3144.000000 -588.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14f77d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3144.000000 -588.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14f77d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3144.000000 -588.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14f77d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3144.000000 -588.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14f77d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3144.000000 -588.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14f77d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3144.000000 -588.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14f77d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3144.000000 -588.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14f77d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3144.000000 -588.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14f77d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3144.000000 -588.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14f77d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3144.000000 -588.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14f77d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3144.000000 -588.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_1cab630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3274.000000 -1167.500000) translate(0,16)">空龙河零级</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20e00d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4032.000000 -677.000000) translate(0,15)">1号主变参数:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20e00d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4032.000000 -677.000000) translate(0,33)">S9-2500/10</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20e00d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4032.000000 -677.000000) translate(0,51)">YN,d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d71c20" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4330.000000 -182.000000) translate(0,12)">1号发电机参数:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d71c20" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4330.000000 -182.000000) translate(0,27)">SFW2000-6/1430</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d71c20" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4330.000000 -182.000000) translate(0,42)">Pe=2MW,Ue=6.3kV,Ie=263A,cos=0.8</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14f5a30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4562.000000 -662.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18b7610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4560.000000 -755.000000) translate(0,12)">0021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_270bfd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4589.000000 -730.000000) translate(0,12)">00217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ca5590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3986.000000 -376.000000) translate(0,12)">661</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2572cf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3984.000000 -467.000000) translate(0,12)">6611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18b7b40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4013.000000 -442.000000) translate(0,12)">66117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e3c630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4424.000000 -465.000000) translate(0,12)">6901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_185dcc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4452.000000 -442.000000) translate(0,12)">69017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cb4f90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3974.000000 -850.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14e2cf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3987.000000 -825.000000) translate(0,12)">09017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1017020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3853.000000 -901.000000) translate(0,12)">10kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_fe3440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3906.000000 -515.000000) translate(0,12)">6kV母线</text>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-208611">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4253.000000 -730.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8066" ObjectName="SW-CX_KLH.CX_KLH_001BK"/>
     <cge:Meas_Ref ObjectId="208611"/>
    <cge:TPSR_Ref TObjectID="8066"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-208618">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3968.000000 -347.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11282" ObjectName="SW-CX_KLH.CX_KLH_661BK_S"/>
     <cge:Meas_Ref ObjectId="208618"/>
    <cge:TPSR_Ref TObjectID="11282"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-208621">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4544.000000 -633.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11277" ObjectName="SW-CX_KLH.CX_KLH_002BK_S"/>
     <cge:Meas_Ref ObjectId="208621"/>
    <cge:TPSR_Ref TObjectID="11277"/></metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_244b770" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4042.000000 -789.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d0da40" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4355.000000 -788.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_222c260" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4356.000000 -536.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d028b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4070.000000 -406.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ca7fd0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4509.000000 -406.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c7ed10" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4646.000000 -694.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3235.000000 -1119.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-116504" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3273.538462 -982.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="116504" ObjectName="CX_KLH:CX_KLH_ZJ_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-208631" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4269.000000 -186.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="208631" ObjectName="CX_KLH:CX_KLH_1G_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-208632" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4269.000000 -170.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="208632" ObjectName="CX_KLH:CX_KLH_1G_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-208630" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4269.000000 -154.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="208630" ObjectName="CX_KLH:CX_KLH_1G_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-116505" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3272.538462 -944.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="116505" ObjectName="CX_KLH:CX_KLH_ZJ_sumQ"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-208634" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4269.000000 -138.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="208634" ObjectName="CX_KLH:CX_KLH_1G_F"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-208624" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3949.000000 -588.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="208624" ObjectName="CX_KLH:CX_KLH_6IM_Ua"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-208625" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3949.000000 -572.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="208625" ObjectName="CX_KLH:CX_KLH_6IM_Ub"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-208626" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3949.000000 -556.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="208626" ObjectName="CX_KLH:CX_KLH_6IM_Uc"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-208627" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3949.000000 -540.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="208627" ObjectName="CX_KLH:CX_KLH_6IM_Uab"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-208635" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3912.000000 -967.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="208635" ObjectName="CX_KLH:CX_KLH_9IM_Ua"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-208636" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3912.000000 -951.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="208636" ObjectName="CX_KLH:CX_KLH_9IM_Ub"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-208637" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3912.000000 -935.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="208637" ObjectName="CX_KLH:CX_KLH_9IM_Uc"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-208638" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3912.000000 -919.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="208638" ObjectName="CX_KLH:CX_KLH_9IM_Uab"/>
    </metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="3247" y="-1178"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="3247" y="-1178"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3198" y="-1195"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3198" y="-1195"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_155c4e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4096.000000 780.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c6b5f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4085.000000 765.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2175e80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4110.000000 750.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2507700" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4209.000000 185.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bcd090" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4198.000000 170.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_185f360" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4223.000000 155.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1209890" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4218.000000 139.000000) translate(0,12)">F(Hz):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1871cf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3891.000000 557.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11e1bd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3883.000000 541.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f77ed0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3891.000000 572.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a79e00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3891.000000 587.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24132f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3854.000000 936.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ce4960" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3846.000000 920.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b30620" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3854.000000 951.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_fd1bd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3854.000000 966.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_f63c90">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3958.000000 -658.000000)" xlink:href="#lightningRod:shape81"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a86110">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3965.000000 -229.000000)" xlink:href="#lightningRod:shape44"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22319e0">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4324.000000 -293.000000)" xlink:href="#lightningRod:shape44"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14ec300">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4406.000000 -292.000000)" xlink:href="#lightningRod:shape44"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a880b0">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4456.000000 -294.000000)" xlink:href="#lightningRod:shape81"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_151c7d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3958.000000 -726.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fbacf0">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4408.000000 -364.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2391820">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4541.000000 -535.000000)" xlink:href="#lightningRod:shape44"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_地调直调.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="3247" y="-1178"/></g>
   <g href="cx_索引_接线图_地调直调.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3198" y="-1195"/></g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_KLH.CX_KLH_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3852,-882 4797,-882 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11272" ObjectName="BS-CX_KLH.CX_KLH_9IM"/>
    <cge:TPSR_Ref TObjectID="11272"/></metadata>
   <polyline fill="none" opacity="0" points="3852,-882 4797,-882 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_KLH.CX_KLH_6IM">
    <g class="BV-6KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3943,-497 4477,-497 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11273" ObjectName="BS-CX_KLH.CX_KLH_6IM"/>
    <cge:TPSR_Ref TObjectID="11273"/></metadata>
   <polyline fill="none" opacity="0" points="3943,-497 4477,-497 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-208642" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4157.000000 -780.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="208642" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8066"/>
     <cge:Term_Ref ObjectID="11554"/>
    <cge:TPSR_Ref TObjectID="8066"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-208643" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4157.000000 -780.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="208643" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8066"/>
     <cge:Term_Ref ObjectID="11554"/>
    <cge:TPSR_Ref TObjectID="8066"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-208641" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4157.000000 -780.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="208641" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8066"/>
     <cge:Term_Ref ObjectID="11554"/>
    <cge:TPSR_Ref TObjectID="8066"/></metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_KLH"/>
</svg>