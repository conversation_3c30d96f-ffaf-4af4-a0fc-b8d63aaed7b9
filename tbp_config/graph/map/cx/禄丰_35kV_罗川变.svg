<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-36" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="3133 -1276 2075 1142">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="capacitor:shape32">
    <polyline arcFlag="1" points="36,25 37,25 38,25 38,25 39,26 39,26 40,26 40,27 41,27 41,28 41,29 41,29 42,30 42,31 42,31 41,32 41,33 41,33 41,34 40,34 40,35 39,35 39,35 38,36 38,36 37,36 36,36 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="19" x2="19" y1="2" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="19" x2="19" y1="21" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="36" y1="14" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="36" y1="55" y2="47"/>
    <polyline arcFlag="1" points="36,14 37,14 38,14 38,14 39,14 39,15 40,15 40,16 41,16 41,17 41,17 41,18 42,19 42,19 42,20 41,21 41,21 41,22 41,22 40,23 40,23 39,24 39,24 38,24 38,25 37,25 36,25 " stroke-width="1"/>
    <polyline arcFlag="1" points="36,36 37,36 38,36 38,37 39,37 39,37 40,38 40,38 41,39 41,39 41,40 41,40 42,41 42,42 42,42 41,43 41,44 41,44 41,45 40,45 40,46 39,46 39,47 38,47 38,47 37,47 36,47 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.368819" x1="2" x2="35" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.44164" x1="19" x2="36" y1="8" y2="8"/>
    <rect height="23" stroke-width="0.369608" width="12" x="13" y="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.356919" x1="19" x2="36" y1="55" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="2" x2="2" y1="2" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="35" x2="35" y1="2" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.271035" x1="11" x2="26" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="11" x2="26" y1="21" y2="21"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="14" y2="14"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="6" y2="41"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="63" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="55" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="56" y2="56"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="55" x2="55" y1="12" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="54" x2="46" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="59" x2="59" y1="3" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="62" x2="62" y1="5" y2="8"/>
    <rect height="12" stroke-width="1" width="26" x="19" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="39" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="9" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="1" y2="1"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
   </symbol>
   <symbol id="lightningRod:shape105">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="13" x2="13" y1="39" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.44" x1="13" x2="13" y1="5" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="0" x2="12" y1="26" y2="26"/>
    <polyline points="13,39 15,39 17,38 18,38 20,37 21,36 23,35 24,33 25,31 25,30 26,28 26,26 26,24 25,22 25,21 24,19 23,18 21,16 20,15 18,14 17,14 15,13 13,13 11,13 9,14 8,14 6,15 5,16 3,18 2,19 1,21 1,22 0,24 0,26 " stroke-width="0.0972"/>
   </symbol>
   <symbol id="lightningRod:shape190">
    <ellipse cx="24" cy="20" rx="7.5" ry="7" stroke-width="0.726474"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="28" x2="25" y1="21" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="22" x2="25" y1="21" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="25" x2="25" y1="19" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="33" x2="39" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="35" x2="33" y1="29" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="36" x2="39" y1="29" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="40" x2="37" y1="22" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="34" x2="37" y1="22" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="37" x2="37" y1="20" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="28" x2="25" y1="34" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="25" x2="25" y1="32" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="22" x2="25" y1="34" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.05263" x1="13" x2="5" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.125" x1="10" x2="8" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.05263" x1="12" x2="6" y1="4" y2="4"/>
    <ellipse cx="35" cy="30" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <ellipse cx="24" cy="30" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <ellipse cx="35" cy="20" rx="7.5" ry="7" stroke-width="0.726474"/>
    <rect height="9" stroke-width="1" width="5" x="7" y="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="0" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="15" y1="12" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="19" y2="19"/>
    <polyline points="9,7 9,10 " stroke-width="1"/>
    <polyline points="10,19 10,31 25,31 " stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape204">
    <rect height="31" stroke-width="0.5" width="16" x="12" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="36" y1="30" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="2" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="2" y1="30" y2="10"/>
   </symbol>
   <symbol id="lightningRod:shape123">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="4" y2="4"/>
    <ellipse cx="14" cy="18" fillStyle="0" rx="9" ry="7.5" stroke-width="0.155709"/>
    <ellipse cx="22" cy="8" fillStyle="0" rx="8.5" ry="7.5" stroke-width="0.155709"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="14" x2="14" y1="17" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="16" x2="14" y1="22" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="14" x2="11" y1="20" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="8" x2="8" y1="6" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="10" x2="8" y1="11" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="8" x2="5" y1="9" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="24" x2="24" y1="12" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="20" x2="24" y1="8" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="19" x2="24" y1="10" y2="12"/>
    <ellipse cx="8" cy="10" fillStyle="0" rx="8.5" ry="7.5" stroke-width="0.155709"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape36_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="5" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="27" y1="17" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="7" y1="38" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="14" y1="26" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="26" y1="38" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="14" y1="30" y2="18"/>
   </symbol>
   <symbol id="switch2:shape36_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="17" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-17" x2="5" y1="39" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-16" x2="-4" y1="31" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-4" x2="3" y1="18" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-9" x2="3" y1="38" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-9" x2="-16" y1="38" y2="31"/>
   </symbol>
   <symbol id="switch2:shape36-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-17" x2="-9" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-8" x2="-8" y1="25" y2="31"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="-6,49 16,27 28,27 " stroke-width="1"/>
    <rect height="19" stroke-width="1" width="4" x="3" y="29"/>
   </symbol>
   <symbol id="switch2:shape36-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-17" x2="-9" y1="27" y2="27"/>
    <rect height="19" stroke-width="1" width="4" x="3" y="7"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="-6,6 16,28 28,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-8" x2="-8" y1="30" y2="24"/>
   </symbol>
   <symbol id="switch2:shape19_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="14" y1="30" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="26" y1="38" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="14" y1="26" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="7" y1="38" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="27" y1="17" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="5" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="5" y1="46" y2="10"/>
    <rect height="26" stroke-width="0.416609" width="14" x="-2" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="34" y1="10" y2="40"/>
    <rect height="29" stroke-width="0.416609" width="9" x="12" y="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="5" y1="46" y2="10"/>
    <rect height="26" stroke-width="0.416609" width="14" x="-2" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="transformer2:shape4_0">
    <circle cx="31" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.911765" x1="1" x2="56" y1="49" y2="80"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="56" x2="56" y1="77" y2="77"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="56" x2="54" y1="80" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="51" x2="56" y1="80" y2="80"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="42" x2="26" y1="24" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="42" x2="26" y1="24" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="26" y1="33" y2="16"/>
   </symbol>
   <symbol id="transformer2:shape4_1">
    <ellipse cx="31" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="30" x2="30" y1="58" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="30" x2="38" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="22" x2="30" y1="74" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape89_0">
    <circle cx="30" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="42" y2="0"/>
    <polyline DF8003:Layer="PUBLIC" points="32,14 26,27 39,27 32,14 32,15 32,14 "/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,56 6,56 6,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="57" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="26" y1="57" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="35" y1="57" y2="53"/>
   </symbol>
   <symbol id="transformer2:shape89_1">
    <circle cx="31" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="30,87 26,78 36,78 30,87 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="87" y2="87"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="81" y2="81"/>
   </symbol>
   <symbol id="transformer2:shape2_0">
    <ellipse cx="13" cy="34" fillStyle="0" rx="13" ry="12.5" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="32" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="36" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="40" y2="36"/>
   </symbol>
   <symbol id="transformer2:shape2_1">
    <circle cx="13" cy="18" fillStyle="0" r="13" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="18" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="9" y1="20" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="18" y1="20" y2="12"/>
   </symbol>
   <symbol id="transformer2:shape29_0">
    <ellipse cx="13" cy="17" fillStyle="0" rx="13" ry="12.5" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="17" y1="15" y2="15"/>
   </symbol>
   <symbol id="transformer2:shape29_1">
    <circle cx="13" cy="34" fillStyle="0" r="13" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="17" y1="36" y2="36"/>
   </symbol>
   <symbol id="transformer2:shape54_0">
    <circle cx="39" cy="32" fillStyle="0" r="27" stroke-width="0.650262"/>
    <rect height="28" stroke-width="1" width="14" x="90" y="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="95" x2="98" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="93" x2="101" y1="25" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="97" x2="97" y1="28" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="103" x2="91" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="97" x2="97" y1="75" y2="40"/>
    <polyline points="58,100 64,100 " stroke-width="1"/>
    <polyline points="64,93 64,100 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.643357" x1="97" x2="39" y1="75" y2="75"/>
    <polyline DF8003:Layer="PUBLIC" points="39,19 32,34 47,34 39,19 39,19 39,19 "/>
   </symbol>
   <symbol id="transformer2:shape54_1">
    <circle cx="39" cy="70" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="64,100 1,37 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1fb8a70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1fb9c20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1fba610" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1fbb850" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1fbcb40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1fbd7e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1fbe380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1fbed80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1526740" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1526740" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1fc1c90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1fc1c90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1fc3830" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1fc3830" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_1fc48d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1fc6550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1fc7200" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1fc7fc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1fc8900" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1fc9fc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1fcacc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1fcb580" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1fcbd40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1fcce20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1fcd7a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1fce290" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1fcec50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1fd00d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1fd0c70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1fd1ca0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1fd28e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1fe10b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1fd41d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1fd57c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1fd6cf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1152" width="2085" x="3128" y="-1281"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="4989" x2="4989" y1="-737" y2="-723"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="4989" x2="5000" y1="-723" y2="-723"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-32309">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4126.000000 -892.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5289" ObjectName="SW-LF_LC.LF_LC_3611SW"/>
     <cge:Meas_Ref ObjectId="32309"/>
    <cge:TPSR_Ref TObjectID="5289"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-32311">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4157.000000 -1143.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5315" ObjectName="SW-LF_LC.LF_LC_36167SW"/>
     <cge:Meas_Ref ObjectId="32311"/>
    <cge:TPSR_Ref TObjectID="5315"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-32315">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4177.000000 -559.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5292" ObjectName="SW-LF_LC.LF_LC_0011SW"/>
     <cge:Meas_Ref ObjectId="32315"/>
    <cge:TPSR_Ref TObjectID="5292"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54440">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4610.000000 -559.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9617" ObjectName="SW-LF_LC.LF_LC_0022SW"/>
     <cge:Meas_Ref ObjectId="54440"/>
    <cge:TPSR_Ref TObjectID="9617"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54437">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4631.000000 -808.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9615" ObjectName="SW-LF_LC.LF_LC_30217SW"/>
     <cge:Meas_Ref ObjectId="54437"/>
    <cge:TPSR_Ref TObjectID="9615"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54444">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4345.000000 -474.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9620" ObjectName="SW-LF_LC.LF_LC_0122SW"/>
     <cge:Meas_Ref ObjectId="54444"/>
    <cge:TPSR_Ref TObjectID="9620"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-32334">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4950.000000 -485.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5310" ObjectName="SW-LF_LC.LF_LC_4811SW"/>
     <cge:Meas_Ref ObjectId="32334"/>
    <cge:TPSR_Ref TObjectID="5310"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-32335">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4950.000000 -385.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5311" ObjectName="SW-LF_LC.LF_LC_4812SW"/>
     <cge:Meas_Ref ObjectId="32335"/>
    <cge:TPSR_Ref TObjectID="5311"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-99578">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4901.000000 -363.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20337" ObjectName="SW-LF_LC.LF_LC_48127SW"/>
     <cge:Meas_Ref ObjectId="99578"/>
    <cge:TPSR_Ref TObjectID="20337"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-32325">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4456.000000 -485.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5301" ObjectName="SW-LF_LC.LF_LC_4841SW"/>
     <cge:Meas_Ref ObjectId="32325"/>
    <cge:TPSR_Ref TObjectID="5301"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-32326">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4456.000000 -385.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5302" ObjectName="SW-LF_LC.LF_LC_4842SW"/>
     <cge:Meas_Ref ObjectId="32326"/>
    <cge:TPSR_Ref TObjectID="5302"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-32331">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4752.000000 -485.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5307" ObjectName="SW-LF_LC.LF_LC_4821SW"/>
     <cge:Meas_Ref ObjectId="32331"/>
    <cge:TPSR_Ref TObjectID="5307"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-32332">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4752.000000 -385.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5308" ObjectName="SW-LF_LC.LF_LC_4822SW"/>
     <cge:Meas_Ref ObjectId="32332"/>
    <cge:TPSR_Ref TObjectID="5308"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-32319">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3924.000000 -482.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5295" ObjectName="SW-LF_LC.LF_LC_4861SW"/>
     <cge:Meas_Ref ObjectId="32319"/>
    <cge:TPSR_Ref TObjectID="5295"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-32320">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3924.000000 -382.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5296" ObjectName="SW-LF_LC.LF_LC_4862SW"/>
     <cge:Meas_Ref ObjectId="32320"/>
    <cge:TPSR_Ref TObjectID="5296"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-32322">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4080.000000 -485.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5298" ObjectName="SW-LF_LC.LF_LC_4851SW"/>
     <cge:Meas_Ref ObjectId="32322"/>
    <cge:TPSR_Ref TObjectID="5298"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-99908">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4080.000000 -385.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5299" ObjectName="SW-LF_LC.LF_LC_4852SW"/>
     <cge:Meas_Ref ObjectId="99908"/>
    <cge:TPSR_Ref TObjectID="5299"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-99892">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4610.000000 -483.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5304" ObjectName="SW-LF_LC.LF_LC_4831SW"/>
     <cge:Meas_Ref ObjectId="99892"/>
    <cge:TPSR_Ref TObjectID="5304"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3752.000000 -617.000000)" xlink:href="#switch2:shape36_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5000.000000 -633.000000)" xlink:href="#switch2:shape36_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4045.000000 -1112.000000)" xlink:href="#switch2:shape36_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-99796">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3777.000000 -481.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20338" ObjectName="SW-LF_LC.LF_LC_4881SW"/>
     <cge:Meas_Ref ObjectId="99796"/>
    <cge:TPSR_Ref TObjectID="20338"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-99797">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3777.000000 -381.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20339" ObjectName="SW-LF_LC.LF_LC_4882SW"/>
     <cge:Meas_Ref ObjectId="99797"/>
    <cge:TPSR_Ref TObjectID="20339"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4269.000000 -1127.000000)" xlink:href="#switch2:shape36_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-99335">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4126.000000 -1068.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20335" ObjectName="SW-LF_LC.LF_LC_3616SW"/>
     <cge:Meas_Ref ObjectId="99335"/>
    <cge:TPSR_Ref TObjectID="20335"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-99336">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4096.000000 -1022.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20336" ObjectName="SW-LF_LC.LF_LC_36160SW"/>
     <cge:Meas_Ref ObjectId="99336"/>
    <cge:TPSR_Ref TObjectID="20336"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-99010">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4177.000000 -811.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5286" ObjectName="SW-LF_LC.LF_LC_3011SW"/>
     <cge:Meas_Ref ObjectId="99010"/>
    <cge:TPSR_Ref TObjectID="5286"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-99012">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -0.717391 -0.928571 -0.000000 4146.000000 -814.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20332" ObjectName="SW-LF_LC.LF_LC_30117SW"/>
     <cge:Meas_Ref ObjectId="99012"/>
    <cge:TPSR_Ref TObjectID="20332"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-32310">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4571.000000 -948.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5288" ObjectName="SW-LF_LC.LF_LC_39017SW"/>
     <cge:Meas_Ref ObjectId="32310"/>
    <cge:TPSR_Ref TObjectID="5288"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-32312">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4506.000000 -890.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17542" ObjectName="SW-LF_LC.LF_LC_3901SW"/>
     <cge:Meas_Ref ObjectId="32312"/>
    <cge:TPSR_Ref TObjectID="17542"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-99247">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4611.000000 -821.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20333" ObjectName="SW-LF_LC.LF_LC_3021SW"/>
     <cge:Meas_Ref ObjectId="99247"/>
    <cge:TPSR_Ref TObjectID="20333"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-99675">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3748.000000 -556.000000)" xlink:href="#switch2:shape0_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5293" ObjectName="SW-LF_LC.LF_LC_0901SW"/>
     <cge:Meas_Ref ObjectId="99675"/>
    <cge:TPSR_Ref TObjectID="5293"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-99679">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4995.000000 -555.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17543" ObjectName="SW-LF_LC.LF_LC_0902SW"/>
     <cge:Meas_Ref ObjectId="99679"/>
    <cge:TPSR_Ref TObjectID="17543"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-99907">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.071429 -0.000000 0.000000 -1.000000 4610.000000 -385.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5305" ObjectName="SW-LF_LC.LF_LC_4832SW"/>
     <cge:Meas_Ref ObjectId="99907"/>
    <cge:TPSR_Ref TObjectID="5305"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3644.000000 -462.000000)" xlink:href="#switch2:shape19_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="LF_LC" endPointId="0" endStationName="PAS_T1" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_ShangLuoTLC" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4135,-1197 4135,-1231 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="19218" ObjectName="AC-35kV.LN_ShangLuoTLC"/>
    <cge:TPSR_Ref TObjectID="19218_SS-36"/></metadata>
   <polyline fill="none" opacity="0" points="4135,-1197 4135,-1231 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-LF_LC.LD_LC_486">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3924.000000 -271.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18111" ObjectName="EC-LF_LC.LD_LC_486"/>
    <cge:TPSR_Ref TObjectID="18111"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-LF_LC.LD_LC_485">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4080.000000 -266.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18110" ObjectName="EC-LF_LC.LD_LC_485"/>
    <cge:TPSR_Ref TObjectID="18110"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-LF_LC.LD_LC_484">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4456.000000 -273.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18109" ObjectName="EC-LF_LC.LD_LC_484"/>
    <cge:TPSR_Ref TObjectID="18109"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-LF_LC.LD_LC_483">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4611.000000 -274.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18108" ObjectName="EC-LF_LC.LD_LC_483"/>
    <cge:TPSR_Ref TObjectID="18108"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-LF_LC.LD_LC_488">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3777.000000 -270.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33980" ObjectName="EC-LF_LC.LD_LC_488"/>
    <cge:TPSR_Ref TObjectID="33980"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-LF_LC.LD_LC_482">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4752.000000 -279.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33981" ObjectName="EC-LF_LC.LD_LC_482"/>
    <cge:TPSR_Ref TObjectID="33981"/></metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_156b1a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4579.000000 -951.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_156bb70" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4685.000000 -807.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_156c570" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4211.000000 -1142.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_156cfa0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4875.000000 -362.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15421a0" refnum="0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4096.500000 -1069.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1551620" refnum="0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4147.500000 -847.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_149cbb0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3727.000000 -749.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14a1de0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4983.000000 -781.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_158f4b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4135,-1148 4162,-1148 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="20335@x" ObjectIDND1="0@x" ObjectIDND2="g_14e0660@0" ObjectIDZND0="5315@0" Pin0InfoVect0LinkObjId="SW-32311_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-99335_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_14e0660_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4135,-1148 4162,-1148 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1607200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4198,-1148 4215,-1148 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="5315@1" ObjectIDZND0="g_156c570@0" Pin0InfoVect0LinkObjId="g_156c570_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-32311_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4198,-1148 4215,-1148 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_161cf90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4050,-1117 4050,-1103 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4050,-1117 4050,-1103 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_157b0a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4515,-878 4515,-895 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="20362@0" ObjectIDZND0="17542@0" Pin0InfoVect0LinkObjId="SW-32312_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_14821d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4515,-878 4515,-895 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_15a5ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4515,-957 4530,-957 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_14d8220@0" ObjectIDND1="g_1499070@0" ObjectIDND2="17542@x" ObjectIDZND0="5288@1" Pin0InfoVect0LinkObjId="SW-32310_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_14d8220_0" Pin1InfoVect1LinkObjId="g_1499070_0" Pin1InfoVect2LinkObjId="SW-32312_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4515,-957 4530,-957 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14e7830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4566,-957 4583,-957 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="5288@0" ObjectIDZND0="g_156b1a0@0" Pin0InfoVect0LinkObjId="g_156b1a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-32310_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4566,-957 4583,-957 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_15a96b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4515,-931 4515,-957 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="17542@1" ObjectIDZND0="g_14d8220@0" ObjectIDZND1="g_1499070@0" ObjectIDZND2="5288@x" Pin0InfoVect0LinkObjId="g_14d8220_0" Pin0InfoVect1LinkObjId="g_1499070_0" Pin0InfoVect2LinkObjId="SW-32310_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-32312_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4515,-931 4515,-957 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_155f440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4515,-974 4471,-974 4471,-986 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="5288@x" ObjectIDND1="17542@x" ObjectIDND2="g_1499070@0" ObjectIDZND0="g_14d8220@0" Pin0InfoVect0LinkObjId="g_14d8220_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-32310_0" Pin1InfoVect1LinkObjId="SW-32312_0" Pin1InfoVect2LinkObjId="g_1499070_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4515,-974 4471,-974 4471,-986 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_157a790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4515,-957 4515,-974 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="5288@x" ObjectIDND1="17542@x" ObjectIDZND0="g_14d8220@0" ObjectIDZND1="g_1499070@0" Pin0InfoVect0LinkObjId="g_14d8220_0" Pin0InfoVect1LinkObjId="g_1499070_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-32310_0" Pin1InfoVect1LinkObjId="SW-32312_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4515,-957 4515,-974 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14d8030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4515,-974 4515,-1018 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="5288@x" ObjectIDND1="17542@x" ObjectIDND2="g_14d8220@0" ObjectIDZND0="g_1499070@0" Pin0InfoVect0LinkObjId="g_1499070_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-32310_0" Pin1InfoVect1LinkObjId="SW-32312_0" Pin1InfoVect2LinkObjId="g_14d8220_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4515,-974 4515,-1018 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1577b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3757,-543 3757,-561 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="29489@0" ObjectIDZND0="5293@1" Pin0InfoVect0LinkObjId="SW-99675_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_151bfe0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3757,-543 3757,-561 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1577d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3748,-694 3733,-694 3733,-705 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_149c1d0@0" Pin0InfoVect0LinkObjId="g_149c1d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3748,-694 3733,-694 3733,-705 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14e4040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4186,-618 4186,-600 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="5291@0" ObjectIDZND0="5292@1" Pin0InfoVect0LinkObjId="SW-32315_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-32314_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4186,-618 4186,-600 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_147edb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4620,-770 4620,-748 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="9613@0" ObjectIDZND0="9621@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54435_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4620,-770 4620,-748 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14801a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4619,-668 4619,-645 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="9621@0" ObjectIDZND0="9616@1" Pin0InfoVect0LinkObjId="SW-54439_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_147edb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4619,-668 4619,-645 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1481df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4619,-618 4619,-600 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="9616@0" ObjectIDZND0="9617@1" Pin0InfoVect0LinkObjId="SW-54440_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54439_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4619,-618 4619,-600 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1481fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4619,-564 4619,-543 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="9617@0" ObjectIDZND0="29490@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54440_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4619,-564 4619,-543 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14821d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4620,-862 4620,-878 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="20333@1" ObjectIDZND0="20362@0" Pin0InfoVect0LinkObjId="g_15379e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-99247_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4620,-862 4620,-878 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_15d4f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4620,-813 4636,-813 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="9613@x" ObjectIDND1="20333@x" ObjectIDZND0="9615@0" Pin0InfoVect0LinkObjId="SW-54437_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-54435_0" Pin1InfoVect1LinkObjId="SW-99247_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4620,-813 4636,-813 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_15d5120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4672,-813 4689,-813 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="9615@1" ObjectIDZND0="g_156bb70@0" Pin0InfoVect0LinkObjId="g_156bb70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54437_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4672,-813 4689,-813 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_15d5310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4620,-797 4620,-813 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="9613@1" ObjectIDZND0="9615@x" ObjectIDZND1="20333@x" Pin0InfoVect0LinkObjId="SW-54437_0" Pin0InfoVect1LinkObjId="SW-99247_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54435_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4620,-797 4620,-813 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_15d5500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4620,-813 4620,-826 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="9613@x" ObjectIDND1="9615@x" ObjectIDZND0="20333@0" Pin0InfoVect0LinkObjId="SW-99247_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-54435_0" Pin1InfoVect1LinkObjId="SW-54437_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4620,-813 4620,-826 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15d56f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5004,-543 5004,-561 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="29490@0" ObjectIDZND0="17543@0" Pin0InfoVect0LinkObjId="SW-99679_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1481fe0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5004,-543 5004,-561 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15d58e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5004,-615 5021,-615 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="17543@x" ObjectIDZND0="g_15d5cc0@0" Pin0InfoVect0LinkObjId="g_15d5cc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-99679_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5004,-615 5021,-615 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15d5ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5004,-597 5004,-614 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="17543@1" ObjectIDZND0="g_15d5cc0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_15d5cc0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-99679_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5004,-597 5004,-614 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15d66d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5004,-614 5004,-638 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_15d5cc0@0" ObjectIDND1="17543@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_15d5cc0_0" Pin1InfoVect1LinkObjId="SW-99679_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5004,-614 5004,-638 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_151bc00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4404,-543 4404,-479 4386,-479 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="29490@0" ObjectIDZND0="9620@1" Pin0InfoVect0LinkObjId="SW-54444_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1481fe0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4404,-543 4404,-479 4386,-479 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_151bdf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4350,-479 4330,-479 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="9620@0" ObjectIDZND0="9619@0" Pin0InfoVect0LinkObjId="SW-54443_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54444_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4350,-479 4330,-479 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_151bfe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4303,-479 4289,-479 4289,-543 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="9619@1" ObjectIDZND0="29489@0" Pin0InfoVect0LinkObjId="g_14721e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54443_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4303,-479 4289,-479 4289,-543 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_151dd30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4959,-543 4959,-526 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="29490@0" ObjectIDZND0="5310@1" Pin0InfoVect0LinkObjId="SW-32334_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1481fe0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4959,-543 4959,-526 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1587780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4959,-490 4959,-472 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="5310@0" ObjectIDZND0="5309@1" Pin0InfoVect0LinkObjId="SW-32333_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-32334_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4959,-490 4959,-472 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15894d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4959,-445 4959,-426 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="5309@0" ObjectIDZND0="5311@1" Pin0InfoVect0LinkObjId="SW-32335_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-32333_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4959,-445 4959,-426 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_158b760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4959,-390 4959,-368 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="5311@0" ObjectIDZND0="20337@x" ObjectIDZND1="g_14d9680@0" ObjectIDZND2="20337@x" Pin0InfoVect0LinkObjId="SW-99578_0" Pin0InfoVect1LinkObjId="g_14d9680_0" Pin0InfoVect2LinkObjId="SW-99578_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-32335_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4959,-390 4959,-368 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1579fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4959,-368 4942,-368 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="5311@x" ObjectIDND1="g_14d9680@0" ObjectIDND2="20337@x" ObjectIDZND0="20337@1" Pin0InfoVect0LinkObjId="SW-99578_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-32335_0" Pin1InfoVect1LinkObjId="g_14d9680_0" Pin1InfoVect2LinkObjId="SW-99578_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4959,-368 4942,-368 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_157a190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4906,-368 4893,-368 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="20337@0" ObjectIDZND0="g_156cfa0@0" Pin0InfoVect0LinkObjId="g_156cfa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-99578_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4906,-368 4893,-368 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_157a380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4959,-368 4959,-353 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="20337@x" ObjectIDND1="5311@x" ObjectIDND2="g_14d9680@0" ObjectIDZND0="g_14d9680@0" ObjectIDZND1="20337@x" ObjectIDZND2="5311@x" Pin0InfoVect0LinkObjId="g_14d9680_0" Pin0InfoVect1LinkObjId="SW-99578_0" Pin0InfoVect2LinkObjId="SW-32335_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-99578_0" Pin1InfoVect1LinkObjId="SW-32335_0" Pin1InfoVect2LinkObjId="g_14d9680_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4959,-368 4959,-353 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1589870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4959,-297 4959,-272 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="capacitor" ObjectIDND0="g_14d9680@0" ObjectIDZND0="40116@0" Pin0InfoVect0LinkObjId="CB-LF_LC.LF_LC_Cb1_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_14d9680_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4959,-297 4959,-272 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_159ea90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4465,-543 4465,-526 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="29490@0" ObjectIDZND0="5301@1" Pin0InfoVect0LinkObjId="SW-32325_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1481fe0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4465,-543 4465,-526 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15a09a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4465,-490 4465,-472 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="5301@0" ObjectIDZND0="5300@1" Pin0InfoVect0LinkObjId="SW-32324_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-32325_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4465,-490 4465,-472 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14ea6c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4465,-445 4465,-426 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="5300@0" ObjectIDZND0="5302@1" Pin0InfoVect0LinkObjId="SW-32326_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-32324_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4465,-445 4465,-426 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14ea8e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4465,-366 4496,-366 4496,-351 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="5302@x" ObjectIDND1="18109@x" ObjectIDZND0="g_14eaf40@0" Pin0InfoVect0LinkObjId="g_14eaf40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-32326_0" Pin1InfoVect1LinkObjId="EC-LF_LC.LD_LC_484_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4465,-366 4496,-366 4496,-351 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14eab00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4465,-390 4465,-366 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="5302@0" ObjectIDZND0="g_14eaf40@0" ObjectIDZND1="18109@x" Pin0InfoVect0LinkObjId="g_14eaf40_0" Pin0InfoVect1LinkObjId="EC-LF_LC.LD_LC_484_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-32326_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4465,-390 4465,-366 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14ead20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4465,-366 4465,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_14eaf40@0" ObjectIDND1="5302@x" ObjectIDZND0="18109@0" Pin0InfoVect0LinkObjId="EC-LF_LC.LD_LC_484_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_14eaf40_0" Pin1InfoVect1LinkObjId="SW-32326_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4465,-366 4465,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14ebff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4619,-445 4619,-426 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="5303@0" ObjectIDZND0="5305@1" Pin0InfoVect0LinkObjId="SW-99907_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-32327_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4619,-445 4619,-426 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14ec1e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4619,-366 4650,-366 4650,-351 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="18108@x" ObjectIDND1="5305@x" ObjectIDZND0="g_14ec7b0@0" Pin0InfoVect0LinkObjId="g_14ec7b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-LF_LC.LD_LC_483_0" Pin1InfoVect1LinkObjId="SW-99907_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4619,-366 4650,-366 4650,-351 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14ec3d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4619,-390 4619,-366 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="5305@0" ObjectIDZND0="g_14ec7b0@0" ObjectIDZND1="18108@x" Pin0InfoVect0LinkObjId="g_14ec7b0_0" Pin0InfoVect1LinkObjId="EC-LF_LC.LD_LC_483_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-99907_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4619,-390 4619,-366 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14ec5c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4619,-366 4620,-301 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_14ec7b0@0" ObjectIDND1="5305@x" ObjectIDZND0="18108@0" Pin0InfoVect0LinkObjId="EC-LF_LC.LD_LC_483_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_14ec7b0_0" Pin1InfoVect1LinkObjId="SW-99907_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4619,-366 4620,-301 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14d1500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4761,-543 4761,-526 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="29490@0" ObjectIDZND0="5307@1" Pin0InfoVect0LinkObjId="SW-32331_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1481fe0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4761,-543 4761,-526 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14d3380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4761,-490 4761,-472 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="5307@0" ObjectIDZND0="5306@1" Pin0InfoVect0LinkObjId="SW-32330_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-32331_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4761,-490 4761,-472 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14d57c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4761,-445 4761,-426 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="5306@0" ObjectIDZND0="5308@1" Pin0InfoVect0LinkObjId="SW-32332_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-32330_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4761,-445 4761,-426 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15dbae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3933,-543 3933,-523 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="29489@0" ObjectIDZND0="5295@1" Pin0InfoVect0LinkObjId="SW-32319_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_151bfe0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3933,-543 3933,-523 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15dd9f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3933,-487 3933,-469 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="5295@0" ObjectIDZND0="5294@1" Pin0InfoVect0LinkObjId="SW-32318_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-32319_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3933,-487 3933,-469 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_157d0b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3933,-442 3933,-423 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="5294@0" ObjectIDZND0="5296@1" Pin0InfoVect0LinkObjId="SW-32320_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-32318_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3933,-442 3933,-423 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_157d2d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3933,-363 3964,-363 3964,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="5296@x" ObjectIDND1="18111@x" ObjectIDZND0="g_157d930@0" Pin0InfoVect0LinkObjId="g_157d930_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-32320_0" Pin1InfoVect1LinkObjId="EC-LF_LC.LD_LC_486_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3933,-363 3964,-363 3964,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_157d4f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3933,-387 3933,-363 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="5296@0" ObjectIDZND0="g_157d930@0" ObjectIDZND1="18111@x" Pin0InfoVect0LinkObjId="g_157d930_0" Pin0InfoVect1LinkObjId="EC-LF_LC.LD_LC_486_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-32320_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3933,-387 3933,-363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_157d710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3933,-363 3933,-298 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_157d930@0" ObjectIDND1="5296@x" ObjectIDZND0="18111@0" Pin0InfoVect0LinkObjId="EC-LF_LC.LD_LC_486_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_157d930_0" Pin1InfoVect1LinkObjId="SW-32320_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3933,-363 3933,-298 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15808e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4089,-543 4089,-526 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="29489@0" ObjectIDZND0="5298@1" Pin0InfoVect0LinkObjId="SW-32322_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_151bfe0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4089,-543 4089,-526 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1479be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4089,-490 4089,-472 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="5298@0" ObjectIDZND0="5297@1" Pin0InfoVect0LinkObjId="SW-32321_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-32322_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4089,-490 4089,-472 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_147c0c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4089,-445 4089,-426 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="5297@0" ObjectIDZND0="5299@1" Pin0InfoVect0LinkObjId="SW-99908_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-32321_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4089,-445 4089,-426 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_147c320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4089,-366 4120,-366 4120,-351 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="5299@x" ObjectIDND1="18110@x" ObjectIDZND0="g_147ca40@0" Pin0InfoVect0LinkObjId="g_147ca40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-99908_0" Pin1InfoVect1LinkObjId="EC-LF_LC.LD_LC_485_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4089,-366 4120,-366 4120,-351 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_147c580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4089,-390 4089,-366 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="5299@0" ObjectIDZND0="g_147ca40@0" ObjectIDZND1="18110@x" Pin0InfoVect0LinkObjId="g_147ca40_0" Pin0InfoVect1LinkObjId="EC-LF_LC.LD_LC_485_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-99908_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4089,-390 4089,-366 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_147c7e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4089,-366 4089,-293 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_147ca40@0" ObjectIDND1="5299@x" ObjectIDZND0="18110@0" Pin0InfoVect0LinkObjId="EC-LF_LC.LD_LC_485_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_147ca40_0" Pin1InfoVect1LinkObjId="SW-99908_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4089,-366 4089,-293 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_156f150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4619,-543 4619,-524 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="29490@0" ObjectIDZND0="5304@1" Pin0InfoVect0LinkObjId="SW-99892_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1481fe0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4619,-543 4619,-524 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_156f3b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4619,-488 4619,-472 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="5304@0" ObjectIDZND0="5303@1" Pin0InfoVect0LinkObjId="SW-32327_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-99892_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4619,-488 4619,-472 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1529100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3786,-543 3786,-522 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="29489@0" ObjectIDZND0="20338@1" Pin0InfoVect0LinkObjId="SW-99796_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_151bfe0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3786,-543 3786,-522 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1592f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3786,-486 3786,-468 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="20338@0" ObjectIDZND0="9612@1" Pin0InfoVect0LinkObjId="SW-54428_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-99796_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3786,-486 3786,-468 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15959d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3786,-441 3786,-422 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="9612@0" ObjectIDZND0="20339@1" Pin0InfoVect0LinkObjId="SW-99797_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54428_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3786,-441 3786,-422 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1544e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4091,-1065 4091,-1072 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="20336@1" ObjectIDZND0="g_15421a0@0" Pin0InfoVect0LinkObjId="g_15421a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-99336_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4091,-1065 4091,-1072 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1552e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3757,-667 3757,-682 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_149d8a0@0" Pin0InfoVect0LinkObjId="g_149d8a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3757,-667 3757,-682 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1471f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3649,-422 3649,-463 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3649,-422 3649,-463 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14721e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3649,-515 3649,-543 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="29489@0" Pin0InfoVect0LinkObjId="g_151bfe0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3649,-515 3649,-543 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1473d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4135,-1109 4135,-1148 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="20335@1" ObjectIDZND0="5315@x" ObjectIDZND1="0@x" ObjectIDZND2="g_14e0660@0" Pin0InfoVect0LinkObjId="SW-32311_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_14e0660_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-99335_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4135,-1109 4135,-1148 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14636a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4141,-843 4141,-852 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="20332@1" ObjectIDZND0="g_1551620@0" Pin0InfoVect0LinkObjId="g_1551620_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-99012_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4141,-843 4141,-852 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14647c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4274,-1121 4274,-1132 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4274,-1121 4274,-1132 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14649b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4135,-1177 4274,-1177 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="20335@x" ObjectIDND1="5315@x" ObjectIDND2="g_14e0660@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-99335_0" Pin1InfoVect1LinkObjId="SW-32311_0" Pin1InfoVect2LinkObjId="g_14e0660_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4135,-1177 4274,-1177 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1464ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4104,-1167 4104,-1177 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_14e0660@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="20335@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-99335_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_14e0660_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4104,-1167 4104,-1177 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1465580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4050,-1161 4050,-1177 4104,-1177 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@1" ObjectIDZND0="g_14e0660@0" ObjectIDZND1="0@x" ObjectIDZND2="20335@x" Pin0InfoVect0LinkObjId="g_14e0660_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-99335_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4050,-1161 4050,-1177 4104,-1177 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14657e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3764,-609 3757,-609 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_15520b0@0" ObjectIDZND0="5293@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-99675_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15520b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3764,-609 3757,-609 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14662b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3757,-597 3757,-609 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="5293@0" ObjectIDZND0="g_15520b0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_15520b0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-99675_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3757,-597 3757,-609 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1466510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3757,-609 3757,-622 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_15520b0@0" ObjectIDND1="5293@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_15520b0_0" Pin1InfoVect1LinkObjId="SW-99675_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3757,-609 3757,-622 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_149d640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3733,-754 3733,-737 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="lightningRod" ObjectIDND0="g_149cbb0@0" ObjectIDZND0="g_149c1d0@1" Pin0InfoVect0LinkObjId="g_149c1d0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_149cbb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3733,-754 3733,-737 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14a2840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4989,-786 4989,-768 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="lightningRod" ObjectIDND0="g_14a1de0@0" ObjectIDZND0="g_14a14b0@1" Pin0InfoVect0LinkObjId="g_14a14b0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_14a1de0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4989,-786 4989,-768 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15304f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4959,-353 4959,-339 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="20337@x" ObjectIDND1="5311@x" ObjectIDND2="g_14d9680@0" ObjectIDZND0="g_14d9680@1" Pin0InfoVect0LinkObjId="g_14d9680_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-99578_0" Pin1InfoVect1LinkObjId="SW-32335_0" Pin1InfoVect2LinkObjId="g_14d9680_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4959,-353 4959,-339 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1530750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4959,-264 4932,-264 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_157a570@0" Pin0InfoVect0LinkObjId="g_157a570_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4959,-264 4932,-264 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15309b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4761,-390 4761,-306 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" ObjectIDND0="5308@0" ObjectIDZND0="33981@0" Pin0InfoVect0LinkObjId="EC-LF_LC.LD_LC_482_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-32332_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4761,-390 4761,-306 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1530c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3786,-386 3786,-297 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" ObjectIDND0="20339@0" ObjectIDZND0="33980@0" Pin0InfoVect0LinkObjId="EC-LF_LC.LD_LC_488_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-99797_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3786,-386 3786,-297 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1535d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4959,-368 4959,-353 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="20337@x" ObjectIDND1="5311@x" ObjectIDND2="g_14d9680@0" ObjectIDZND0="g_14d9680@0" ObjectIDZND1="20337@x" ObjectIDZND2="5311@x" Pin0InfoVect0LinkObjId="g_14d9680_0" Pin0InfoVect1LinkObjId="SW-99578_0" Pin0InfoVect2LinkObjId="SW-32335_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-99578_0" Pin1InfoVect1LinkObjId="SW-32335_0" Pin1InfoVect2LinkObjId="g_14d9680_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4959,-368 4959,-353 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1535fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5005,-683 5005,-705 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_14a2aa0@0" Pin0InfoVect0LinkObjId="g_14a2aa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5005,-683 5005,-705 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1536fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4135,-1148 4135,-1177 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="20335@x" ObjectIDND1="5315@x" ObjectIDZND0="0@x" ObjectIDZND1="g_14e0660@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_14e0660_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-99335_0" Pin1InfoVect1LinkObjId="SW-32311_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4135,-1148 4135,-1177 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1537240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4135,-1177 4104,-1177 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="20335@x" ObjectIDND2="5315@x" ObjectIDZND0="g_14e0660@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_14e0660_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-99335_0" Pin1InfoVect2LinkObjId="SW-32311_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4135,-1177 4104,-1177 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_15374a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4135,-1177 4135,-1198 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="0@x" ObjectIDND1="20335@x" ObjectIDND2="5315@x" ObjectIDZND0="19218@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-99335_0" Pin1InfoVect2LinkObjId="SW-32311_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4135,-1177 4135,-1198 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_15379e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4186,-852 4186,-878 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="5286@1" ObjectIDZND0="20362@0" Pin0InfoVect0LinkObjId="g_14821d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-99010_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4186,-852 4186,-878 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1538210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4141,-817 4141,-810 4186,-810 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="20332@0" ObjectIDZND0="5285@x" ObjectIDZND1="5286@x" Pin0InfoVect0LinkObjId="SW-32308_0" Pin0InfoVect1LinkObjId="SW-99010_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-99012_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4141,-817 4141,-810 4186,-810 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1538d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4186,-798 4186,-810 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="5285@1" ObjectIDZND0="20332@x" ObjectIDZND1="5286@x" Pin0InfoVect0LinkObjId="SW-99012_0" Pin0InfoVect1LinkObjId="SW-99010_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-32308_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4186,-798 4186,-810 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1538f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4186,-810 4186,-816 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="20332@x" ObjectIDND1="5285@x" ObjectIDZND0="5286@0" Pin0InfoVect0LinkObjId="SW-99010_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-99012_0" Pin1InfoVect1LinkObjId="SW-32308_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4186,-810 4186,-816 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13ca170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4135,-1073 4135,-1015 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="20335@0" ObjectIDZND0="20336@x" ObjectIDZND1="20334@x" Pin0InfoVect0LinkObjId="SW-99336_0" Pin0InfoVect1LinkObjId="SW-99333_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-99335_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4135,-1073 4135,-1015 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13ca360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4135,-1015 4091,-1015 4091,-1027 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="20335@x" ObjectIDND1="20334@x" ObjectIDZND0="20336@0" Pin0InfoVect0LinkObjId="SW-99336_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-99335_0" Pin1InfoVect1LinkObjId="SW-99333_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4135,-1015 4091,-1015 4091,-1027 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13caaa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4135,-897 4135,-878 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="5289@0" ObjectIDZND0="20362@0" Pin0InfoVect0LinkObjId="g_14821d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-32309_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4135,-897 4135,-878 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13cad00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4135,-1015 4135,-988 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="20335@x" ObjectIDND1="20336@x" ObjectIDZND0="20334@1" Pin0InfoVect0LinkObjId="SW-99333_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-99335_0" Pin1InfoVect1LinkObjId="SW-99336_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4135,-1015 4135,-988 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13caf60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4135,-961 4135,-933 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="20334@0" ObjectIDZND0="5289@1" Pin0InfoVect0LinkObjId="SW-32309_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-99333_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4135,-961 4135,-933 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14518a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4186,-771 4186,-754 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="5285@0" ObjectIDZND0="5313@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-32308_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4186,-771 4186,-754 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1451ae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4186,-662 4186,-645 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="5313@0" ObjectIDZND0="5291@1" Pin0InfoVect0LinkObjId="SW-32314_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_14518a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4186,-662 4186,-645 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1451d40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4186,-564 4186,-543 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="5292@0" ObjectIDZND0="29489@0" Pin0InfoVect0LinkObjId="g_151bfe0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-32315_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4186,-564 4186,-543 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="29489" cx="4289" cy="-543" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29490" cx="4619" cy="-543" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29490" cx="4959" cy="-543" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29490" cx="4761" cy="-543" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29489" cx="4089" cy="-543" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29490" cx="4404" cy="-543" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29490" cx="4619" cy="-543" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29489" cx="3933" cy="-543" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29489" cx="3786" cy="-543" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20362" cx="4515" cy="-878" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20362" cx="4620" cy="-878" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29489" cx="3757" cy="-543" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29490" cx="5004" cy="-543" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29489" cx="3649" cy="-543" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29490" cx="4465" cy="-543" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20362" cx="4186" cy="-878" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20362" cx="4135" cy="-878" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29489" cx="4186" cy="-543" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-32251" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3555.000000 -1085.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5261" ObjectName="DYN-LF_LC"/>
     <cge:Meas_Ref ObjectId="32251"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1324c60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,17)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1324c60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1324c60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,59)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1324c60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1324c60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,101)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1324c60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1324c60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,143)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1324c60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1324c60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,185)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_13ba420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_13ba420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_13ba420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_13ba420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_13ba420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_13ba420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_13ba420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_13ba420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_13ba420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_13ba420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_13ba420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_13ba420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_13ba420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_13ba420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_13ba420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_13ba420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_13ba420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_13ba420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_126f480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3283.000000 -1166.500000) translate(0,16)">罗川变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_122e6b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3904.000000 -1154.000000) translate(0,18)">35kV#1站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_1263e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4142.000000 -1201.000000) translate(0,18)">35kV上罗T线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_101f5e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4748.000000 -911.000000) translate(0,18)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_155f630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3655.000000 -796.000000) translate(0,18)">10kV I 段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_157a980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3595.000000 -573.000000) translate(0,18)">10kV I 段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_15896c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5044.000000 -209.000000) translate(0,18)">#1电容器750千乏</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_14ebb30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4444.000000 -262.000000) translate(0,18)">南河线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_14ed2b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4587.500000 -265.000000) translate(0,18)">彩云线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_14d59e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4731.000000 -268.000000) translate(0,18)">七彩云南线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_157e520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3904.000000 -266.000000) translate(0,18)">城区线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_147ce20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4058.000000 -260.000000) translate(0,18)">竹溪线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1563f20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4145.000000 -926.000000) translate(0,15)">3611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_15643d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4199.000000 -793.000000) translate(0,15)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1564610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3765.000000 -589.000000) translate(0,15)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1564900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5016.000000 -589.000000) translate(0,15)">0902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1564b50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4172.000000 -1137.000000) translate(0,15)">36167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1564fb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4630.000000 -792.000000) translate(0,15)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1565410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3942.000000 -465.000000) translate(0,15)">486</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1565650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3940.000000 -514.000000) translate(0,15)">4861</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_15658d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3940.000000 -414.000000) translate(0,15)">4862</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1565b10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4098.000000 -468.000000) translate(0,15)">485</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1565d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4096.000000 -417.000000) translate(0,15)">4852</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_15661b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4096.000000 -517.000000) translate(0,15)">4851</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_15663f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4196.000000 -641.000000) translate(0,15)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1566630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4629.000000 -641.000000) translate(0,15)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1566870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4628.000000 -591.000000) translate(0,15)">0022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1566ab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4195.000000 -591.000000) translate(0,15)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1566cf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4349.000000 -467.000000) translate(0,15)">0122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1566f30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4474.000000 -468.000000) translate(0,15)">484</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1567170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4472.000000 -417.000000) translate(0,15)">4842</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_15673b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4472.000000 -517.000000) translate(0,15)">4841</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_15675f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4627.000000 -417.000000) translate(0,15)">4832</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1567830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4628.000000 -517.000000) translate(0,15)">4831</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1567a70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4770.000000 -468.000000) translate(0,15)">482</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1567cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4768.000000 -417.000000) translate(0,15)">4822</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1567ef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4768.000000 -517.000000) translate(0,15)">4821</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1568130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4968.000000 -468.000000) translate(0,15)">481</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1568370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4966.000000 -417.000000) translate(0,15)">4812</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_15685b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4966.000000 -517.000000) translate(0,15)">4811</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_156d9a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5045.000000 -539.000000) translate(0,18)">10kV II 母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_156dea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4634.000000 -839.000000) translate(0,12)">30217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_156e0d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4571.000000 -853.000000) translate(0,12)">3021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_156e4f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4903.000000 -354.000000) translate(0,12)">48127</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1572b90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4628.000000 -466.000000) translate(0,15)">483</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1573cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3260.000000 -223.000000) translate(0,16)">18887829213</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,0)" font-family="SimSun" font-size="18" graphid="g_1575d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3866.000000 -463.000000) translate(0,15)">100/5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,0)" font-family="SimSun" font-size="18" graphid="g_15763d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4026.000000 -464.000000) translate(0,15)">600/5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,0)" font-family="SimSun" font-size="18" graphid="g_1576650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4406.000000 -465.000000) translate(0,15)">100/5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,0)" font-family="SimSun" font-size="18" graphid="g_1576890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4557.000000 -465.000000) translate(0,15)">100/5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,0)" font-family="SimSun" font-size="18" graphid="g_1576ad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4657.000000 -765.000000) translate(0,15)">SZ11-5000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_1525190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4463.000000 -1081.000000) translate(0,18)">35kV母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_158a2c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4943.000000 -832.000000) translate(0,18)">10kVⅡ段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_1595c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3758.000000 -265.000000) translate(0,18)">备用线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_15963d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3795.000000 -464.000000) translate(0,15)">488</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1596650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3793.000000 -513.000000) translate(0,15)">4881</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1596890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3793.000000 -413.000000) translate(0,15)">4882</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,0)" font-family="SimSun" font-size="18" graphid="g_1596ad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3719.000000 -462.000000) translate(0,15)">100/5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_14dd940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4282.000000 -1083.000000) translate(0,18)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_14e1410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4152.000000 -1092.000000) translate(0,15)">3616</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_15450d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4021.000000 -1046.000000) translate(0,15)">36160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_1472a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3610.000000 -313.000000) translate(0,18)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_14766d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4144.000000 -982.000000) translate(0,15)">361</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_1476d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4269.000000 -722.000000) translate(0,18)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1477a40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4304.000000 -503.000000) translate(0,15)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1478710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3147.000000 -812.000000) translate(0,15)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_1461da0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3496.000000 -1144.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_1462ff0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3496.000000 -1179.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_1463890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4081.000000 -836.000000) translate(0,13)">30117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1466770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4522.000000 -920.000000) translate(0,15)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1466da0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4528.000000 -979.000000) translate(0,15)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14688f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4260.000000 -747.000000) translate(0,12)">档位(档):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1469780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4650.000000 -720.000000) translate(0,12)">档位(档):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_149bb00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4535.000000 -1054.000000) translate(0,10)">U</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_14a0a60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4660.000000 -742.000000) translate(0,18)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15391c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4193.000000 -841.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_13d2e90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3133.000000 -184.000000) translate(0,17)">楚雄巡维中</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_13d2e90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3133.000000 -184.000000) translate(0,38)">心变运二班：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_13d51d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3260.000000 -194.500000) translate(0,16)">13508785260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_13d51d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3260.000000 -194.500000) translate(0,36)">18787879001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_13d51d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3260.000000 -194.500000) translate(0,56)">18787879002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_13d60f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3419.000000 -1166.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_13d6bd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4271.000000 -692.000000) translate(0,15)">SZ11-8000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_13d6bd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4271.000000 -692.000000) translate(0,33)">35000±3×2.5%/10500V</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_13d6bd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4271.000000 -692.000000) translate(0,51)">YNd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_13d6bd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4271.000000 -692.000000) translate(0,69)">7.45%</text>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-32308">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4177.065147 -763.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5285" ObjectName="SW-LF_LC.LF_LC_301BK"/>
     <cge:Meas_Ref ObjectId="32308"/>
    <cge:TPSR_Ref TObjectID="5285"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-32314">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4177.065147 -610.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5291" ObjectName="SW-LF_LC.LF_LC_001BK"/>
     <cge:Meas_Ref ObjectId="32314"/>
    <cge:TPSR_Ref TObjectID="5291"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54435">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4611.057818 -762.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9613" ObjectName="SW-LF_LC.LF_LC_302BK"/>
     <cge:Meas_Ref ObjectId="54435"/>
    <cge:TPSR_Ref TObjectID="9613"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54439">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4610.057818 -610.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9616" ObjectName="SW-LF_LC.LF_LC_002BK"/>
     <cge:Meas_Ref ObjectId="54439"/>
    <cge:TPSR_Ref TObjectID="9616"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54443">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4294.000000 -469.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9619" ObjectName="SW-LF_LC.LF_LC_012BK"/>
     <cge:Meas_Ref ObjectId="54443"/>
    <cge:TPSR_Ref TObjectID="9619"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-32333">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4950.000000 -437.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5309" ObjectName="SW-LF_LC.LF_LC_481BK"/>
     <cge:Meas_Ref ObjectId="32333"/>
    <cge:TPSR_Ref TObjectID="5309"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-32324">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4456.000000 -437.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5300" ObjectName="SW-LF_LC.LF_LC_484BK"/>
     <cge:Meas_Ref ObjectId="32324"/>
    <cge:TPSR_Ref TObjectID="5300"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-32330">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4752.000000 -437.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5306" ObjectName="SW-LF_LC.LF_LC_482BK"/>
     <cge:Meas_Ref ObjectId="32330"/>
    <cge:TPSR_Ref TObjectID="5306"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-32318">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3924.000000 -434.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5294" ObjectName="SW-LF_LC.LF_LC_486BK"/>
     <cge:Meas_Ref ObjectId="32318"/>
    <cge:TPSR_Ref TObjectID="5294"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-32321">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4080.000000 -437.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5297" ObjectName="SW-LF_LC.LF_LC_485BK"/>
     <cge:Meas_Ref ObjectId="32321"/>
    <cge:TPSR_Ref TObjectID="5297"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-32327">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4610.000000 -437.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5303" ObjectName="SW-LF_LC.LF_LC_483BK"/>
     <cge:Meas_Ref ObjectId="32327"/>
    <cge:TPSR_Ref TObjectID="5303"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54428">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3777.000000 -433.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9612" ObjectName="SW-LF_LC.LF_LC_488BK"/>
     <cge:Meas_Ref ObjectId="54428"/>
    <cge:TPSR_Ref TObjectID="9612"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-99333">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4126.065147 -953.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20334" ObjectName="SW-LF_LC.LF_LC_361BK"/>
     <cge:Meas_Ref ObjectId="99333"/>
    <cge:TPSR_Ref TObjectID="20334"/></metadata>
   </g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-LF_LC.LF_LC_Cb1">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4940.000000 -209.000000)" xlink:href="#capacitor:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40116" ObjectName="CB-LF_LC.LF_LC_Cb1"/>
    <cge:TPSR_Ref TObjectID="40116"/></metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_14d8220">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4464.000000 -981.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15d5cc0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5016.100163 -608.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_157a570">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4925.000000 -206.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14d9680">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4972.000000 -292.000000)" xlink:href="#lightningRod:shape105"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14eaf40">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4489.000000 -293.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14ec7b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4642.500000 -293.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_157d930">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3957.000000 -290.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_147ca40">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4113.000000 -293.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14e0660">
    <use class="BV-35KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4097.000000 -1109.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15520b0">
    <use class="BV-10KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3758.500000 -616.648208)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1499070">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4540.000000 -1054.000000)" xlink:href="#lightningRod:shape190"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_149c1d0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3713.000000 -700.000000)" xlink:href="#lightningRod:shape204"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_149d8a0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3744.000000 -709.000000)" xlink:href="#lightningRod:shape123"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14a14b0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4969.000000 -732.000000)" xlink:href="#lightningRod:shape204"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14a2aa0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4992.000000 -732.000000)" xlink:href="#lightningRod:shape123"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-98656" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4137.000000 -802.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="98656" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5285"/>
     <cge:Term_Ref ObjectID="7662"/>
    <cge:TPSR_Ref TObjectID="5285"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-98657" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4137.000000 -802.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="98657" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5285"/>
     <cge:Term_Ref ObjectID="7662"/>
    <cge:TPSR_Ref TObjectID="5285"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-98648" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4137.000000 -802.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="98648" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5285"/>
     <cge:Term_Ref ObjectID="7662"/>
    <cge:TPSR_Ref TObjectID="5285"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-98680" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4571.000000 -803.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="98680" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9613"/>
     <cge:Term_Ref ObjectID="7678"/>
    <cge:TPSR_Ref TObjectID="9613"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-98681" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4571.000000 -803.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="98681" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9613"/>
     <cge:Term_Ref ObjectID="7678"/>
    <cge:TPSR_Ref TObjectID="9613"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-98675" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4571.000000 -803.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="98675" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9613"/>
     <cge:Term_Ref ObjectID="7678"/>
    <cge:TPSR_Ref TObjectID="9613"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-98692" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4575.000000 -653.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="98692" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9616"/>
     <cge:Term_Ref ObjectID="7684"/>
    <cge:TPSR_Ref TObjectID="9616"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-98693" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4575.000000 -653.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="98693" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9616"/>
     <cge:Term_Ref ObjectID="7684"/>
    <cge:TPSR_Ref TObjectID="9616"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-98685" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4575.000000 -653.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="98685" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9616"/>
     <cge:Term_Ref ObjectID="7684"/>
    <cge:TPSR_Ref TObjectID="9616"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-98770" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4769.000000 -240.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="98770" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5306"/>
     <cge:Term_Ref ObjectID="7718"/>
    <cge:TPSR_Ref TObjectID="5306"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-98771" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4769.000000 -240.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="98771" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5306"/>
     <cge:Term_Ref ObjectID="7718"/>
    <cge:TPSR_Ref TObjectID="5306"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-98763" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4769.000000 -240.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="98763" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5306"/>
     <cge:Term_Ref ObjectID="7718"/>
    <cge:TPSR_Ref TObjectID="5306"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-98757" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4624.000000 -239.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="98757" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5303"/>
     <cge:Term_Ref ObjectID="7716"/>
    <cge:TPSR_Ref TObjectID="5303"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-98758" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4624.000000 -239.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="98758" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5303"/>
     <cge:Term_Ref ObjectID="7716"/>
    <cge:TPSR_Ref TObjectID="5303"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-98750" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4624.000000 -239.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="98750" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5303"/>
     <cge:Term_Ref ObjectID="7716"/>
    <cge:TPSR_Ref TObjectID="5303"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-98744" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4476.000000 -237.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="98744" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5300"/>
     <cge:Term_Ref ObjectID="7710"/>
    <cge:TPSR_Ref TObjectID="5300"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-98745" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4476.000000 -237.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="98745" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5300"/>
     <cge:Term_Ref ObjectID="7710"/>
    <cge:TPSR_Ref TObjectID="5300"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-98737" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4476.000000 -237.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="98737" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5300"/>
     <cge:Term_Ref ObjectID="7710"/>
    <cge:TPSR_Ref TObjectID="5300"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-98731" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4093.000000 -229.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="98731" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5297"/>
     <cge:Term_Ref ObjectID="7704"/>
    <cge:TPSR_Ref TObjectID="5297"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-98732" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4093.000000 -229.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="98732" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5297"/>
     <cge:Term_Ref ObjectID="7704"/>
    <cge:TPSR_Ref TObjectID="5297"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-98724" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4093.000000 -229.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="98724" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5297"/>
     <cge:Term_Ref ObjectID="7704"/>
    <cge:TPSR_Ref TObjectID="5297"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-98718" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3935.000000 -229.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="98718" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5294"/>
     <cge:Term_Ref ObjectID="7698"/>
    <cge:TPSR_Ref TObjectID="5294"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-98719" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3935.000000 -229.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="98719" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5294"/>
     <cge:Term_Ref ObjectID="7698"/>
    <cge:TPSR_Ref TObjectID="5294"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-98711" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3935.000000 -229.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="98711" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5294"/>
     <cge:Term_Ref ObjectID="7698"/>
    <cge:TPSR_Ref TObjectID="5294"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-98669" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4139.000000 -655.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="98669" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5291"/>
     <cge:Term_Ref ObjectID="7674"/>
    <cge:TPSR_Ref TObjectID="5291"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-98670" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4139.000000 -655.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="98670" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5291"/>
     <cge:Term_Ref ObjectID="7674"/>
    <cge:TPSR_Ref TObjectID="5291"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-98662" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4139.000000 -655.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="98662" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5291"/>
     <cge:Term_Ref ObjectID="7674"/>
    <cge:TPSR_Ref TObjectID="5291"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-98705" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4138.000000 -1276.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="98705" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20334"/>
     <cge:Term_Ref ObjectID="7688"/>
    <cge:TPSR_Ref TObjectID="20334"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-98706" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4138.000000 -1276.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="98706" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20334"/>
     <cge:Term_Ref ObjectID="7688"/>
    <cge:TPSR_Ref TObjectID="20334"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-98698" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4138.000000 -1276.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="98698" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20334"/>
     <cge:Term_Ref ObjectID="7688"/>
    <cge:TPSR_Ref TObjectID="20334"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-98856" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3783.000000 -231.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="98856" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9612"/>
     <cge:Term_Ref ObjectID="13665"/>
    <cge:TPSR_Ref TObjectID="9612"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-98857" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3783.000000 -231.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="98857" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9612"/>
     <cge:Term_Ref ObjectID="13665"/>
    <cge:TPSR_Ref TObjectID="9612"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-98849" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3783.000000 -231.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="98849" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9612"/>
     <cge:Term_Ref ObjectID="13665"/>
    <cge:TPSR_Ref TObjectID="9612"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-98784" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4951.000000 -192.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="98784" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5309"/>
     <cge:Term_Ref ObjectID="7724"/>
    <cge:TPSR_Ref TObjectID="5309"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-98776" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4951.000000 -192.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="98776" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5309"/>
     <cge:Term_Ref ObjectID="7724"/>
    <cge:TPSR_Ref TObjectID="5309"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-98843" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4305.000000 -461.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="98843" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9619"/>
     <cge:Term_Ref ObjectID="13661"/>
    <cge:TPSR_Ref TObjectID="9619"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-98844" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4305.000000 -461.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="98844" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9619"/>
     <cge:Term_Ref ObjectID="13661"/>
    <cge:TPSR_Ref TObjectID="9619"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-98836" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4305.000000 -461.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="98836" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9619"/>
     <cge:Term_Ref ObjectID="13661"/>
    <cge:TPSR_Ref TObjectID="9619"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="2" id="ME-99842" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4329.000000 -746.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="99842" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5313"/>
     <cge:Term_Ref ObjectID="13671"/>
    <cge:TPSR_Ref TObjectID="5313"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-98812" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3938.000000 -967.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="98812" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20362"/>
     <cge:Term_Ref ObjectID="28377"/>
    <cge:TPSR_Ref TObjectID="20362"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-98813" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3938.000000 -967.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="98813" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20362"/>
     <cge:Term_Ref ObjectID="28377"/>
    <cge:TPSR_Ref TObjectID="20362"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-98814" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3938.000000 -967.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="98814" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20362"/>
     <cge:Term_Ref ObjectID="28377"/>
    <cge:TPSR_Ref TObjectID="20362"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-98815" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3938.000000 -967.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="98815" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20362"/>
     <cge:Term_Ref ObjectID="28377"/>
    <cge:TPSR_Ref TObjectID="20362"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-98824" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3938.000000 -967.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="98824" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20362"/>
     <cge:Term_Ref ObjectID="28377"/>
    <cge:TPSR_Ref TObjectID="20362"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-98816" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3640.000000 -654.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="98816" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29489"/>
     <cge:Term_Ref ObjectID="13415"/>
    <cge:TPSR_Ref TObjectID="29489"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-98817" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3640.000000 -654.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="98817" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29489"/>
     <cge:Term_Ref ObjectID="13415"/>
    <cge:TPSR_Ref TObjectID="29489"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-98818" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3640.000000 -654.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="98818" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29489"/>
     <cge:Term_Ref ObjectID="13415"/>
    <cge:TPSR_Ref TObjectID="29489"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-98819" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3640.000000 -654.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="98819" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29489"/>
     <cge:Term_Ref ObjectID="13415"/>
    <cge:TPSR_Ref TObjectID="29489"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-98827" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3640.000000 -654.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="98827" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29489"/>
     <cge:Term_Ref ObjectID="13415"/>
    <cge:TPSR_Ref TObjectID="29489"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-98820" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5167.000000 -628.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="98820" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29490"/>
     <cge:Term_Ref ObjectID="13416"/>
    <cge:TPSR_Ref TObjectID="29490"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-98821" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5167.000000 -628.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="98821" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29490"/>
     <cge:Term_Ref ObjectID="13416"/>
    <cge:TPSR_Ref TObjectID="29490"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-98822" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5167.000000 -628.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="98822" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29490"/>
     <cge:Term_Ref ObjectID="13416"/>
    <cge:TPSR_Ref TObjectID="29490"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-98823" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5167.000000 -628.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="98823" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29490"/>
     <cge:Term_Ref ObjectID="13416"/>
    <cge:TPSR_Ref TObjectID="29490"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-98830" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5167.000000 -628.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="98830" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29490"/>
     <cge:Term_Ref ObjectID="13416"/>
    <cge:TPSR_Ref TObjectID="29490"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="2" id="ME-98694" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4718.000000 -719.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="98694" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9621"/>
     <cge:Term_Ref ObjectID="23903"/>
    <cge:TPSR_Ref TObjectID="9621"/></metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="3248" y="-1177"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3199" y="-1194"/></g>
   <g href="35kV罗川变35kV上罗T线361间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="30" x="4144" y="-982"/></g>
   <g href="35kV罗川变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="22" qtmmishow="hidden" width="77" x="4270" y="-722"/></g>
   <g href="35kV罗川变2号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="22" qtmmishow="hidden" width="77" x="4658" y="-742"/></g>
   <g href="35kV罗川变10kV备用线488间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="31" x="3795" y="-464"/></g>
   <g href="35kV罗川变10kV城区线486间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="31" x="3942" y="-465"/></g>
   <g href="35kV罗川变10kV竹溪线485间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="31" x="4098" y="-468"/></g>
   <g href="35kV罗川变10kV分段012间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="30" x="4304" y="-503"/></g>
   <g href="35kV罗川变10kV南河线484间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="32" x="4474" y="-468"/></g>
   <g href="35kV罗川变10kV彩云线483间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="31" x="4628" y="-466"/></g>
   <g href="35kV罗川变10kV七彩云南线482间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="31" x="4770" y="-468"/></g>
   <g href="35kV罗川变10kV电容器481间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="31" x="4968" y="-468"/></g>
   <g href="35kV罗川变GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="72" x="3147" y="-812"/></g>
   <g href="cx_配调_配网接线图35_禄丰.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3485" y="-1152"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3485" y="-1187"/></g>
   <g href="AVC罗川站.svg" style="fill-opacity:0"><rect height="47" qtmmishow="hidden" stroke="rgb(0,0,0)" width="45" x="3415" y="-1180"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1469af0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3876.000000 924.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_146a3d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3870.000000 939.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_146acb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3870.000000 953.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_146b210" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3862.000000 909.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_146b770" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3870.000000 968.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_146bae0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3576.000000 609.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_146bd50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3570.000000 624.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_146bf90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3570.000000 638.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_146c1d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3562.000000 594.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_146c410" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3570.000000 653.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_146c740" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5106.000000 585.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_146c9b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5100.000000 600.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_146cbf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5100.000000 614.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1497ba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5092.000000 570.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1497de0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5100.000000 629.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13cb2b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4097.000000 1246.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13cbe50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4083.000000 1276.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13cc9f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4072.000000 1261.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13cd3c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4099.000000 773.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13cd660" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4085.000000 803.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13cd8a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4074.000000 788.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13cdbd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4100.000000 627.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13cde30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4086.000000 657.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13ce070" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4075.000000 642.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13ce3a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4532.000000 774.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13ce600" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4518.000000 804.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13ce840" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4507.000000 789.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13ceb70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4536.000000 624.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13cedd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4522.000000 654.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13cf010" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4511.000000 639.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13cf340" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3744.000000 204.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13cf5a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3730.000000 234.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13cf7e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3719.000000 219.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13cfb10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3898.000000 201.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13cfd70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3884.000000 231.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13cffb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3873.000000 216.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13d02e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4055.000000 201.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13d0540" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4041.000000 231.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13d0780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4030.000000 216.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13d0ab0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4439.000000 209.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13d0d10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4425.000000 239.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13d0f50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4414.000000 224.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13d1280" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4588.000000 210.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13d14e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4574.000000 240.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13d1720" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4563.000000 225.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13d1a50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4731.000000 212.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13d1cb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4717.000000 242.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13d1ef0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4706.000000 227.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13d2220" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4267.000000 432.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13d2480" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4253.000000 462.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13d26c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4242.000000 447.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13d29f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4914.000000 181.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13d2c50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4889.000000 196.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-LF_LC.LF_LC_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="23902"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4588.000000 -663.000000)" xlink:href="#transformer2:shape4_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4588.000000 -663.000000)" xlink:href="#transformer2:shape4_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="9621" ObjectName="TF-LF_LC.LF_LC_2T"/>
    <cge:TPSR_Ref TObjectID="9621"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3618.000000 -329.000000)" xlink:href="#transformer2:shape89_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3618.000000 -329.000000)" xlink:href="#transformer2:shape89_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4037.000000 -1056.000000)" xlink:href="#transformer2:shape2_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4037.000000 -1056.000000)" xlink:href="#transformer2:shape2_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4261.000000 -1074.000000)" xlink:href="#transformer2:shape29_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4261.000000 -1074.000000)" xlink:href="#transformer2:shape29_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-LF_LC.LF_LC_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="13673"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4147.000000 -657.000000)" xlink:href="#transformer2:shape54_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4147.000000 -657.000000)" xlink:href="#transformer2:shape54_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="5313" ObjectName="TF-LF_LC.LF_LC_1T"/>
    <cge:TPSR_Ref TObjectID="5313"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="3248" y="-1177"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="3248" y="-1177"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3199" y="-1194"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3199" y="-1194"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="30" x="4144" y="-982"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="30" x="4144" y="-982"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="22" qtmmishow="hidden" width="77" x="4270" y="-722"/>
    </a>
   <metadata/><rect fill="white" height="22" opacity="0" stroke="white" transform="" width="77" x="4270" y="-722"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="22" qtmmishow="hidden" width="77" x="4658" y="-742"/>
    </a>
   <metadata/><rect fill="white" height="22" opacity="0" stroke="white" transform="" width="77" x="4658" y="-742"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="31" x="3795" y="-464"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="31" x="3795" y="-464"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="31" x="3942" y="-465"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="31" x="3942" y="-465"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="31" x="4098" y="-468"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="31" x="4098" y="-468"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="30" x="4304" y="-503"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="30" x="4304" y="-503"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="32" x="4474" y="-468"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="32" x="4474" y="-468"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="31" x="4628" y="-466"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="31" x="4628" y="-466"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="31" x="4770" y="-468"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="31" x="4770" y="-468"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="31" x="4968" y="-468"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="31" x="4968" y="-468"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="72" x="3147" y="-812"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="72" x="3147" y="-812"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3485" y="-1152"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3485" y="-1152"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3485" y="-1187"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3485" y="-1187"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <polygon fill="rgb(255,255,255)" points="3415,-1180 3412,-1183 3412,-1130 3415,-1133 3415,-1180" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(255,255,255)" points="3415,-1180 3412,-1183 3463,-1183 3460,-1180 3415,-1180" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(127,127,127)" points="3415,-1133 3412,-1130 3463,-1130 3460,-1133 3415,-1133" stroke="rgb(127,127,127)"/>
     <polygon fill="rgb(127,127,127)" points="3460,-1180 3463,-1183 3463,-1130 3460,-1133 3460,-1180" stroke="rgb(127,127,127)"/>
     <rect fill="rgb(255,255,255)" height="47" stroke="rgb(255,255,255)" width="45" x="3415" y="-1180"/>
     <rect fill="none" height="47" qtmmishow="hidden" stroke="rgb(0,0,0)" width="45" x="3415" y="-1180"/>
    </a>
   <metadata/></g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-LF_LC.LF_LC_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3885,-878 4834,-878 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="20362" ObjectName="BS-LF_LC.LF_LC_3IM"/>
    <cge:TPSR_Ref TObjectID="20362"/></metadata>
   <polyline fill="none" opacity="0" points="3885,-878 4834,-878 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-LF_LC.LF_LC_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3631,-543 4315,-543 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="29489" ObjectName="BS-LF_LC.LF_LC_9IM"/>
    <cge:TPSR_Ref TObjectID="29489"/></metadata>
   <polyline fill="none" opacity="0" points="3631,-543 4315,-543 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-LF_LC.LF_LC_9IIM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4371,-543 5055,-543 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="29490" ObjectName="BS-LF_LC.LF_LC_9IIM"/>
    <cge:TPSR_Ref TObjectID="29490"/></metadata>
   <polyline fill="none" opacity="0" points="4371,-543 5055,-543 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3236.000000 -1118.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3259.000000 -947.000000) translate(0,21)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3259.000000 -903.000000) translate(0,21)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-80998" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3258.000000 -1029.000000) translate(0,21)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="80998" ObjectName="LF_LC:LF_LC_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-80998" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3259.000000 -988.000000) translate(0,21)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="80998" ObjectName="LF_LC:LF_LC_sumP"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="LF_LC"/>
</svg>