<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-281" aopId="1835526" id="thSvg" product="E8000V2" version="1.0" viewBox="16 -1029 2432 1297">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="capacitor:shape25">
    <polyline arcFlag="1" points="26,105 24,105 22,104 21,104 19,103 18,102 16,101 15,99 14,97 14,96 13,94 13,92 13,90 14,88 14,87 15,85 16,84 18,82 19,81 21,80 22,80 24,79 26,79 28,79 30,80 31,80 33,81 34,82 36,84 37,85 38,87 38,88 39,90 39,92 " stroke-width="0.0972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="39" x2="26" y1="92" y2="92"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="26" x2="26" y1="105" y2="116"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="43" y1="19" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="43" y1="60" y2="52"/>
    <polyline arcFlag="1" points="43,19 44,19 45,19 45,19 46,19 46,20 47,20 47,21 48,21 48,22 48,22 48,23 49,24 49,24 49,25 48,26 48,26 48,27 48,27 47,28 47,28 46,29 46,29 45,29 45,30 44,30 43,30 " stroke-width="1"/>
    <polyline arcFlag="1" points="43,41 44,41 45,41 45,42 46,42 46,42 47,43 47,43 48,44 48,44 48,45 48,45 49,46 49,47 49,47 48,48 48,49 48,49 48,50 47,50 47,51 46,51 46,52 45,52 45,52 44,52 43,52 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.423529" x1="26" x2="26" y1="92" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.368819" x1="9" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.44164" x1="26" x2="43" y1="13" y2="13"/>
    <rect height="23" stroke-width="0.369608" width="12" x="20" y="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.356919" x1="7" x2="43" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="9" x2="9" y1="7" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="42" x2="42" y1="7" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72286" x1="26" x2="26" y1="19" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="17" x2="33" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="18" x2="33" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="16" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="21" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="0.607143" x1="7" x2="7" y1="60" y2="35"/>
    <rect height="24" stroke-width="0.398039" width="12" x="1" y="29"/>
    <polyline arcFlag="1" points="43,30 44,30 45,30 45,30 46,31 46,31 47,31 47,32 48,32 48,33 48,34 48,34 49,35 49,36 49,36 48,37 48,38 48,38 48,39 47,39 47,40 46,40 46,40 45,41 45,41 44,41 43,41 " stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="13" x2="4" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="earth:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="14" y2="14"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="5" y2="36"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="8" x2="8" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="14" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="5" x2="8" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="3" x2="10" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="12" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
   </symbol>
   <symbol id="lightningRod:shape157">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,36 0,46 10,46 5,36 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,24 0,14 10,14 5,24 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="59" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape10">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="17" y1="7" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="24" y1="19" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="15" y1="24" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="24" y1="17" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="10" y1="17" y2="24"/>
    <circle cx="17" cy="17" fillStyle="0" r="16" stroke-width="1.0625"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="17" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape174">
    <rect height="18" stroke-width="1.1697" width="11" x="1" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.1208" x1="7" x2="7" y1="14" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.1208" x1="7" x2="7" y1="39" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.447552" x1="7" x2="7" y1="7" y2="14"/>
   </symbol>
   <symbol id="lightningRod:shape4">
    <rect height="31" stroke-width="2" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="9" x2="9" y1="35" y2="6"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape45_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="5" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="34" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="25" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape45_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="9" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="34" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="14" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape45-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="5" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="34" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="25" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape45-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="9" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="34" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="14" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="4" y2="14"/>
   </symbol>
   <symbol id="transformer2:shape8_0">
    <circle cx="42" cy="16" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="41,15 41,40 70,40 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="56" x2="98" y1="14" y2="14"/>
    <polyline DF8003:Layer="PUBLIC" points="84,14 71,20 71,7 84,14 83,14 84,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="73" x2="73" y1="34" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="75" x2="75" y1="44" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="78" x2="78" y1="43" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="47" x2="72" y1="39" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="36" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="47" y1="15" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="47" y1="15" y2="11"/>
   </symbol>
   <symbol id="transformer2:shape8_1">
    <circle cx="20" cy="16" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="11" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="22" y1="15" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="22" y1="15" y2="11"/>
   </symbol>
   <symbol id="transformer2:shape4_0">
    <circle cx="31" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.911765" x1="1" x2="56" y1="49" y2="80"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="56" x2="56" y1="77" y2="77"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="56" x2="54" y1="80" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="51" x2="56" y1="80" y2="80"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="42" x2="26" y1="24" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="42" x2="26" y1="24" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="26" y1="33" y2="16"/>
   </symbol>
   <symbol id="transformer2:shape4_1">
    <ellipse cx="31" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="30" x2="30" y1="58" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="30" x2="38" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="22" x2="30" y1="74" y2="66"/>
   </symbol>
   <symbol id="voltageTransformer:shape135">
    <rect height="24" stroke-width="0.379884" width="14" x="1" y="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="6" y2="52"/>
    <ellipse cx="8" cy="67" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <ellipse cx="8" cy="59" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="6" y1="69" y2="69"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="5" y1="57" y2="57"/>
   </symbol>
   <symbol id="voltageTransformer:shape10">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="18" x2="15" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="16" x2="16" y1="4" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="3" x2="9" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="6" x2="3" y1="14" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="6" x2="9" y1="14" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="18" x2="15" y1="17" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="16" x2="16" y1="15" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="13" x2="15" y1="17" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="27" x2="24" y1="13" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="25" x2="25" y1="11" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="22" x2="24" y1="13" y2="11"/>
    <circle cx="7" cy="11" fillStyle="0" r="6" stroke-width="0.431185"/>
    <ellipse cx="15" cy="6" fillStyle="0" rx="6.5" ry="6" stroke-width="0.431185"/>
    <circle cx="24" cy="11" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="15" cy="14" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="13" x2="15" y1="6" y2="4"/>
   </symbol>
   <symbol id="voltageTransformer:shape54">
    <ellipse cx="8" cy="8" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <ellipse cx="8" cy="16" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="12" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="12" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="32" y2="23"/>
   </symbol>
   <symbol id="voltageTransformer:shape106">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="6" y2="16"/>
    <rect height="13" stroke-width="1" width="5" x="3" y="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="35" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="6" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="1" x2="1" y1="19" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="1" y1="28" y2="19"/>
    <ellipse cx="25" cy="22" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="25" y1="34" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="25" y1="36" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="28" x2="25" y1="36" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="37" y1="22" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="37" y1="24" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="37" y1="24" y2="22"/>
    <ellipse cx="25" cy="34" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="34" y1="37" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="38" x2="40" y1="37" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="34" y1="33" y2="33"/>
    <ellipse cx="36" cy="22" rx="8" ry="7.5" stroke-width="1"/>
    <ellipse cx="36" cy="34" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="8" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="7" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="9" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="25" y1="22" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="25" y1="24" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="28" x2="25" y1="24" y2="22"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_30fa120" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_30faac0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_30fb330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_30fc4b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_30fd710" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_30fe330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_30fed50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_30ff7d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3100180" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="55" stroke="rgb(255,0,0)" stroke-width="9.28571" width="98" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3100a60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 52.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3100a60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 52.000000) translate(0,35)">二种工作</text>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3102850" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3102850" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_3103700" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3105370" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3344620" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3344ea0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3345830" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">禁止操作</text>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3346e70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3347660" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3347c70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3348630" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3349810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_334a190" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_334ac80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="78" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3351260" transform="matrix(1.246377 -0.000000 0.000000 -1.035714 2.739130 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3352690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">测试</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3353d00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_336dad0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3367220" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_335fd10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_334d090" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1307" width="2442" x="11" y="-1034"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="448" x2="479" y1="33" y2="33"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="2224" x2="2255" y1="33" y2="33"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-232312">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1364.000000 -825.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38753" ObjectName="SW-CX_ZJD.CX_ZJD_3316SW"/>
     <cge:Meas_Ref ObjectId="232312"/>
    <cge:TPSR_Ref TObjectID="38753"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232314">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1386.000000 -880.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38755" ObjectName="SW-CX_ZJD.CX_ZJD_33167SW"/>
     <cge:Meas_Ref ObjectId="232314"/>
    <cge:TPSR_Ref TObjectID="38755"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232311">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1385.000000 -744.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38752" ObjectName="SW-CX_ZJD.CX_ZJD_33117SW"/>
     <cge:Meas_Ref ObjectId="232311"/>
    <cge:TPSR_Ref TObjectID="38752"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232313">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1385.000000 -808.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38754" ObjectName="SW-CX_ZJD.CX_ZJD_33160SW"/>
     <cge:Meas_Ref ObjectId="232313"/>
    <cge:TPSR_Ref TObjectID="38754"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232310">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1364.000000 -690.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38751" ObjectName="SW-CX_ZJD.CX_ZJD_3311SW"/>
     <cge:Meas_Ref ObjectId="232310"/>
    <cge:TPSR_Ref TObjectID="38751"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232315">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1300.000000 -920.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38756" ObjectName="SW-CX_ZJD.CX_ZJD_3313SW"/>
     <cge:Meas_Ref ObjectId="232315"/>
    <cge:TPSR_Ref TObjectID="38756"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232350">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1099.000000 -610.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38758" ObjectName="SW-CX_ZJD.CX_ZJD_3011SW"/>
     <cge:Meas_Ref ObjectId="232350"/>
    <cge:TPSR_Ref TObjectID="38758"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232351">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1122.000000 -557.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38759" ObjectName="SW-CX_ZJD.CX_ZJD_30117SW"/>
     <cge:Meas_Ref ObjectId="232351"/>
    <cge:TPSR_Ref TObjectID="38759"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232404">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1550.000000 -610.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38761" ObjectName="SW-CX_ZJD.CX_ZJD_3021SW"/>
     <cge:Meas_Ref ObjectId="232404"/>
    <cge:TPSR_Ref TObjectID="38761"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232405">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1573.000000 -557.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38762" ObjectName="SW-CX_ZJD.CX_ZJD_30217SW"/>
     <cge:Meas_Ref ObjectId="232405"/>
    <cge:TPSR_Ref TObjectID="38762"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232719">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1296.000000 -593.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38797" ObjectName="SW-CX_ZJD.CX_ZJD_3901SW"/>
     <cge:Meas_Ref ObjectId="232719"/>
    <cge:TPSR_Ref TObjectID="38797"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232721">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1317.000000 -648.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38799" ObjectName="SW-CX_ZJD.CX_ZJD_39010SW"/>
     <cge:Meas_Ref ObjectId="232721"/>
    <cge:TPSR_Ref TObjectID="38799"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232720">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1319.000000 -578.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38798" ObjectName="SW-CX_ZJD.CX_ZJD_39017SW"/>
     <cge:Meas_Ref ObjectId="232720"/>
    <cge:TPSR_Ref TObjectID="38798"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232722">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1636.000000 -694.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38800" ObjectName="SW-CX_ZJD.CX_ZJD_3321SW"/>
     <cge:Meas_Ref ObjectId="232722"/>
    <cge:TPSR_Ref TObjectID="38800"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232723">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1657.000000 -747.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38801" ObjectName="SW-CX_ZJD.CX_ZJD_33217SW"/>
     <cge:Meas_Ref ObjectId="232723"/>
    <cge:TPSR_Ref TObjectID="38801"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232791">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1098.000000 -256.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38825" ObjectName="SW-CX_ZJD.CX_ZJD_001XC1"/>
     <cge:Meas_Ref ObjectId="232791"/>
    <cge:TPSR_Ref TObjectID="38825"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232791">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1098.000000 -321.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38808" ObjectName="SW-CX_ZJD.CX_ZJD_001XC"/>
     <cge:Meas_Ref ObjectId="232791"/>
    <cge:TPSR_Ref TObjectID="38808"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232463">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 473.000000 -87.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38840" ObjectName="SW-CX_ZJD.CX_ZJD_031XC1"/>
     <cge:Meas_Ref ObjectId="232463"/>
    <cge:TPSR_Ref TObjectID="38840"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232464">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 508.000000 -41.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38765" ObjectName="SW-CX_ZJD.CX_ZJD_03160SW"/>
     <cge:Meas_Ref ObjectId="232464"/>
    <cge:TPSR_Ref TObjectID="38765"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232463">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 473.000000 -152.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38823" ObjectName="SW-CX_ZJD.CX_ZJD_031XC"/>
     <cge:Meas_Ref ObjectId="232463"/>
    <cge:TPSR_Ref TObjectID="38823"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232466">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 419.000000 55.000000)" xlink:href="#switch2:shape45_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38767" ObjectName="SW-CX_ZJD.CX_ZJD_03167BK"/>
     <cge:Meas_Ref ObjectId="232466"/>
    <cge:TPSR_Ref TObjectID="38767"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232465">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 473.000000 54.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38766" ObjectName="SW-CX_ZJD.CX_ZJD_0316SW"/>
     <cge:Meas_Ref ObjectId="232465"/>
    <cge:TPSR_Ref TObjectID="38766"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232512">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 644.000000 -40.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38774" ObjectName="SW-CX_ZJD.CX_ZJD_03260SW"/>
     <cge:Meas_Ref ObjectId="232512"/>
    <cge:TPSR_Ref TObjectID="38774"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232510">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 609.000000 -150.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38810" ObjectName="SW-CX_ZJD.CX_ZJD_032XC"/>
     <cge:Meas_Ref ObjectId="232510"/>
    <cge:TPSR_Ref TObjectID="38810"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232510">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 609.000000 -85.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38827" ObjectName="SW-CX_ZJD.CX_ZJD_032XC1"/>
     <cge:Meas_Ref ObjectId="232510"/>
    <cge:TPSR_Ref TObjectID="38827"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232511">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 609.000000 48.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38773" ObjectName="SW-CX_ZJD.CX_ZJD_0326SW"/>
     <cge:Meas_Ref ObjectId="232511"/>
    <cge:TPSR_Ref TObjectID="38773"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232792">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 645.000000 97.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38806" ObjectName="SW-CX_ZJD.CX_ZJD_03267SW"/>
     <cge:Meas_Ref ObjectId="232792"/>
    <cge:TPSR_Ref TObjectID="38806"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232535">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 791.000000 -40.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38777" ObjectName="SW-CX_ZJD.CX_ZJD_03360SW"/>
     <cge:Meas_Ref ObjectId="232535"/>
    <cge:TPSR_Ref TObjectID="38777"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232533">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 756.000000 -150.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38809" ObjectName="SW-CX_ZJD.CX_ZJD_033XC"/>
     <cge:Meas_Ref ObjectId="232533"/>
    <cge:TPSR_Ref TObjectID="38809"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232533">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 756.000000 -85.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38826" ObjectName="SW-CX_ZJD.CX_ZJD_033XC1"/>
     <cge:Meas_Ref ObjectId="232533"/>
    <cge:TPSR_Ref TObjectID="38826"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232534">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 756.000000 48.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38776" ObjectName="SW-CX_ZJD.CX_ZJD_0336SW"/>
     <cge:Meas_Ref ObjectId="232534"/>
    <cge:TPSR_Ref TObjectID="38776"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232536">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 792.000000 97.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38778" ObjectName="SW-CX_ZJD.CX_ZJD_03367SW"/>
     <cge:Meas_Ref ObjectId="232536"/>
    <cge:TPSR_Ref TObjectID="38778"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232724">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 940.000000 -41.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38802" ObjectName="SW-CX_ZJD.CX_ZJD_03410SW"/>
     <cge:Meas_Ref ObjectId="232724"/>
    <cge:TPSR_Ref TObjectID="38802"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232786">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 905.000000 -151.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38819" ObjectName="SW-CX_ZJD.CX_ZJD_0341XC"/>
     <cge:Meas_Ref ObjectId="232786"/>
    <cge:TPSR_Ref TObjectID="38819"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232786">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 905.000000 -86.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38836" ObjectName="SW-CX_ZJD.CX_ZJD_0341XC1"/>
     <cge:Meas_Ref ObjectId="232786"/>
    <cge:TPSR_Ref TObjectID="38836"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232650">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1077.000000 -38.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38792" ObjectName="SW-CX_ZJD.CX_ZJD_03560SW"/>
     <cge:Meas_Ref ObjectId="232650"/>
    <cge:TPSR_Ref TObjectID="38792"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232649">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1042.000000 -148.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38822" ObjectName="SW-CX_ZJD.CX_ZJD_035XC"/>
     <cge:Meas_Ref ObjectId="232649"/>
    <cge:TPSR_Ref TObjectID="38822"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232649">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1042.000000 -83.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38839" ObjectName="SW-CX_ZJD.CX_ZJD_035XC1"/>
     <cge:Meas_Ref ObjectId="232649"/>
    <cge:TPSR_Ref TObjectID="38839"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232651">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1095.000000 86.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38793" ObjectName="SW-CX_ZJD.CX_ZJD_0030SW"/>
     <cge:Meas_Ref ObjectId="232651"/>
    <cge:TPSR_Ref TObjectID="38793"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232700">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1530.000000 -91.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38828" ObjectName="SW-CX_ZJD.CX_ZJD_012XC1"/>
     <cge:Meas_Ref ObjectId="232700"/>
    <cge:TPSR_Ref TObjectID="38828"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232701">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1375.000000 -149.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38812" ObjectName="SW-CX_ZJD.CX_ZJD_0121XC"/>
     <cge:Meas_Ref ObjectId="232701"/>
    <cge:TPSR_Ref TObjectID="38812"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232701">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1375.000000 -98.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38829" ObjectName="SW-CX_ZJD.CX_ZJD_0121XC1"/>
     <cge:Meas_Ref ObjectId="232701"/>
    <cge:TPSR_Ref TObjectID="38829"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232700">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1530.000000 -156.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38811" ObjectName="SW-CX_ZJD.CX_ZJD_012XC"/>
     <cge:Meas_Ref ObjectId="232700"/>
    <cge:TPSR_Ref TObjectID="38811"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232606">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1214.000000 -42.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38788" ObjectName="SW-CX_ZJD.CX_ZJD_03660SW"/>
     <cge:Meas_Ref ObjectId="232606"/>
    <cge:TPSR_Ref TObjectID="38788"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232605">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1179.000000 -152.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38815" ObjectName="SW-CX_ZJD.CX_ZJD_036XC"/>
     <cge:Meas_Ref ObjectId="232605"/>
    <cge:TPSR_Ref TObjectID="38815"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232605">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1179.000000 -87.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38832" ObjectName="SW-CX_ZJD.CX_ZJD_036XC1"/>
     <cge:Meas_Ref ObjectId="232605"/>
    <cge:TPSR_Ref TObjectID="38832"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232725">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1294.000000 -100.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38835" ObjectName="SW-CX_ZJD.CX_ZJD_0901XC1"/>
     <cge:Meas_Ref ObjectId="232725"/>
    <cge:TPSR_Ref TObjectID="38835"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232725">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1294.000000 -151.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38818" ObjectName="SW-CX_ZJD.CX_ZJD_0901XC"/>
     <cge:Meas_Ref ObjectId="232725"/>
    <cge:TPSR_Ref TObjectID="38818"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232559">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1680.000000 -42.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38781" ObjectName="SW-CX_ZJD.CX_ZJD_04160SW"/>
     <cge:Meas_Ref ObjectId="232559"/>
    <cge:TPSR_Ref TObjectID="38781"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232557">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1645.000000 -152.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38814" ObjectName="SW-CX_ZJD.CX_ZJD_041XC"/>
     <cge:Meas_Ref ObjectId="232557"/>
    <cge:TPSR_Ref TObjectID="38814"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232557">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1645.000000 -87.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38831" ObjectName="SW-CX_ZJD.CX_ZJD_041XC1"/>
     <cge:Meas_Ref ObjectId="232557"/>
    <cge:TPSR_Ref TObjectID="38831"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232558">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1645.000000 46.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38780" ObjectName="SW-CX_ZJD.CX_ZJD_0416SW"/>
     <cge:Meas_Ref ObjectId="232558"/>
    <cge:TPSR_Ref TObjectID="38780"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232560">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1681.000000 95.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38782" ObjectName="SW-CX_ZJD.CX_ZJD_04167SW"/>
     <cge:Meas_Ref ObjectId="232560"/>
    <cge:TPSR_Ref TObjectID="38782"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232583">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1954.000000 -42.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38785" ObjectName="SW-CX_ZJD.CX_ZJD_04360SW"/>
     <cge:Meas_Ref ObjectId="232583"/>
    <cge:TPSR_Ref TObjectID="38785"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232581">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1919.000000 -152.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38813" ObjectName="SW-CX_ZJD.CX_ZJD_043XC"/>
     <cge:Meas_Ref ObjectId="232581"/>
    <cge:TPSR_Ref TObjectID="38813"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232581">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1919.000000 -87.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38830" ObjectName="SW-CX_ZJD.CX_ZJD_043XC1"/>
     <cge:Meas_Ref ObjectId="232581"/>
    <cge:TPSR_Ref TObjectID="38830"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232582">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1919.000000 46.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38784" ObjectName="SW-CX_ZJD.CX_ZJD_0436SW"/>
     <cge:Meas_Ref ObjectId="232582"/>
    <cge:TPSR_Ref TObjectID="38784"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232584">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1955.000000 95.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38786" ObjectName="SW-CX_ZJD.CX_ZJD_04367SW"/>
     <cge:Meas_Ref ObjectId="232584"/>
    <cge:TPSR_Ref TObjectID="38786"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232676">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1814.000000 -39.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38795" ObjectName="SW-CX_ZJD.CX_ZJD_04260SW"/>
     <cge:Meas_Ref ObjectId="232676"/>
    <cge:TPSR_Ref TObjectID="38795"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232675">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1779.000000 -149.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38820" ObjectName="SW-CX_ZJD.CX_ZJD_042XC"/>
     <cge:Meas_Ref ObjectId="232675"/>
    <cge:TPSR_Ref TObjectID="38820"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232675">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1779.000000 -84.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38837" ObjectName="SW-CX_ZJD.CX_ZJD_042XC1"/>
     <cge:Meas_Ref ObjectId="232675"/>
    <cge:TPSR_Ref TObjectID="38837"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232793">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1832.000000 85.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38843" ObjectName="SW-CX_ZJD.CX_ZJD_0040SW"/>
     <cge:Meas_Ref ObjectId="232793"/>
    <cge:TPSR_Ref TObjectID="38843"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232628">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2096.000000 -43.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38790" ObjectName="SW-CX_ZJD.CX_ZJD_04460SW"/>
     <cge:Meas_Ref ObjectId="232628"/>
    <cge:TPSR_Ref TObjectID="38790"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232627">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2061.000000 -153.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38816" ObjectName="SW-CX_ZJD.CX_ZJD_044XC"/>
     <cge:Meas_Ref ObjectId="232627"/>
    <cge:TPSR_Ref TObjectID="38816"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232627">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2061.000000 -88.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38833" ObjectName="SW-CX_ZJD.CX_ZJD_044XC1"/>
     <cge:Meas_Ref ObjectId="232627"/>
    <cge:TPSR_Ref TObjectID="38833"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232488">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2284.000000 -42.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38769" ObjectName="SW-CX_ZJD.CX_ZJD_04560SW"/>
     <cge:Meas_Ref ObjectId="232488"/>
    <cge:TPSR_Ref TObjectID="38769"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232487">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2249.000000 -152.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38821" ObjectName="SW-CX_ZJD.CX_ZJD_045XC"/>
     <cge:Meas_Ref ObjectId="232487"/>
    <cge:TPSR_Ref TObjectID="38821"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232490">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2195.000000 55.000000)" xlink:href="#switch2:shape45_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38771" ObjectName="SW-CX_ZJD.CX_ZJD_04567BK"/>
     <cge:Meas_Ref ObjectId="232490"/>
    <cge:TPSR_Ref TObjectID="38771"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232489">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2249.000000 54.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38770" ObjectName="SW-CX_ZJD.CX_ZJD_0456SW"/>
     <cge:Meas_Ref ObjectId="232489"/>
    <cge:TPSR_Ref TObjectID="38770"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232487">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2249.000000 -87.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38838" ObjectName="SW-CX_ZJD.CX_ZJD_045XC1"/>
     <cge:Meas_Ref ObjectId="232487"/>
    <cge:TPSR_Ref TObjectID="38838"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232726">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2372.000000 -100.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38834" ObjectName="SW-CX_ZJD.CX_ZJD_0902XC1"/>
     <cge:Meas_Ref ObjectId="232726"/>
    <cge:TPSR_Ref TObjectID="38834"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232726">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2372.000000 -151.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38817" ObjectName="SW-CX_ZJD.CX_ZJD_0902XC"/>
     <cge:Meas_Ref ObjectId="232726"/>
    <cge:TPSR_Ref TObjectID="38817"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232416">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1549.000000 -252.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38824" ObjectName="SW-CX_ZJD.CX_ZJD_002XC1"/>
     <cge:Meas_Ref ObjectId="232416"/>
    <cge:TPSR_Ref TObjectID="38824"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232416">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1549.000000 -317.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38807" ObjectName="SW-CX_ZJD.CX_ZJD_002XC"/>
     <cge:Meas_Ref ObjectId="232416"/>
    <cge:TPSR_Ref TObjectID="38807"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-235777">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1180.000000 38.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39360" ObjectName="SW-CX_ZJD.CX_ZJD_0366SW"/>
     <cge:Meas_Ref ObjectId="235777"/>
    <cge:TPSR_Ref TObjectID="39360"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-235776">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1203.000000 87.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39361" ObjectName="SW-CX_ZJD.CX_ZJD_03667SW"/>
     <cge:Meas_Ref ObjectId="235776"/>
    <cge:TPSR_Ref TObjectID="39361"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-235778">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2062.000000 38.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39363" ObjectName="SW-CX_ZJD.CX_ZJD_0446SW"/>
     <cge:Meas_Ref ObjectId="235778"/>
    <cge:TPSR_Ref TObjectID="39363"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-235779">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2085.000000 87.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39362" ObjectName="SW-CX_ZJD.CX_ZJD_04467SW"/>
     <cge:Meas_Ref ObjectId="235779"/>
    <cge:TPSR_Ref TObjectID="39362"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_ZJD.CX_ZJD_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="997,-680 1722,-680 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="38746" ObjectName="BS-CX_ZJD.CX_ZJD_3IM"/>
    <cge:TPSR_Ref TObjectID="38746"/></metadata>
   <polyline fill="none" opacity="0" points="997,-680 1722,-680 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_ZJD.CX_ZJD_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="434,-204 1415,-204 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="38747" ObjectName="BS-CX_ZJD.CX_ZJD_9IM"/>
    <cge:TPSR_Ref TObjectID="38747"/></metadata>
   <polyline fill="none" opacity="0" points="434,-204 1415,-204 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_ZJD.CX_ZJD_9IIM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1510,-205 2448,-205 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="38748" ObjectName="BS-CX_ZJD.CX_ZJD_9IIM"/>
    <cge:TPSR_Ref TObjectID="38748"/></metadata>
   <polyline fill="none" opacity="0" points="1510,-205 2448,-205 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-CX_ZJD.032Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 609.000000 123.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40934" ObjectName="EC-CX_ZJD.032Ld"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_ZJD.033Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 756.000000 120.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40935" ObjectName="EC-CX_ZJD.033Ld"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_ZJD.036Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1180.000000 133.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40936" ObjectName="EC-CX_ZJD.036Ld"/>
    <cge:TPSR_Ref TObjectID="40936"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_ZJD.041Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1645.000000 121.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40937" ObjectName="EC-CX_ZJD.041Ld"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_ZJD.043Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1919.000000 121.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40938" ObjectName="EC-CX_ZJD.043Ld"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_ZJD.044Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2062.000000 127.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40939" ObjectName="EC-CX_ZJD.044Ld"/>
    <cge:TPSR_Ref TObjectID="40939"/></metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_3228f10" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1435.000000 -743.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35e53e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1435.000000 -807.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ca4ec0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1435.000000 -879.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_362e8f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1172.000000 -556.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b17360" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1623.000000 -556.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_375efc0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1367.000000 -647.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35f7d50" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1707.000000 -746.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_354d000" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 511.000000 -20.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36733c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 446.000000 10.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3673e50" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 422.000000 19.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36d4930" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 647.000000 -18.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_389d3f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 648.000000 119.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_38a66e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 794.000000 -18.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_38b15e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 795.000000 119.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36a1040" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 943.000000 -19.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36ae4f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1080.000000 -16.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36be470" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1098.000000 184.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_364ae10" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1217.000000 -20.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36666a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1683.000000 -20.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_31bc710" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1684.000000 117.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_31c9290" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1957.000000 -20.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_31d41a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1958.000000 117.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_31ddd90" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1817.000000 -17.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_31edd00" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1835.000000 183.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_393fd50" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2099.000000 -21.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_394fce0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2287.000000 -20.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_395cf50" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2222.000000 10.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_395d9e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2198.000000 19.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_397bcf0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1370.000000 -577.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_399a790" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1206.000000 109.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_39a3170" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2088.000000 109.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_2a9b440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1108,-651 1108,-680 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="38758@1" ObjectIDZND0="38746@0" Pin0InfoVect0LinkObjId="g_335a1c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232350_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1108,-651 1108,-680 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2aab2e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1373,-749 1373,-768 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="breaker" EndDevType0="breaker" ObjectIDND0="38752@x" ObjectIDND1="38752@x" ObjectIDND2="38750@x" ObjectIDZND0="38750@0" Pin0InfoVect0LinkObjId="SW-232309_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-232311_0" Pin1InfoVect1LinkObjId="SW-232311_0" Pin1InfoVect2LinkObjId="SW-232309_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1373,-749 1373,-768 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_37e3060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1373,-799 1373,-812 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="38750@1" ObjectIDZND0="38753@x" ObjectIDZND1="38754@x" Pin0InfoVect0LinkObjId="SW-232312_0" Pin0InfoVect1LinkObjId="SW-232313_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232309_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1373,-799 1373,-812 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c99890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1373,-813 1373,-827 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="38750@x" ObjectIDND1="38754@x" ObjectIDZND0="38753@0" Pin0InfoVect0LinkObjId="SW-232312_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-232309_0" Pin1InfoVect1LinkObjId="SW-232313_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1373,-813 1373,-827 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_376f9a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1373,-749 1390,-749 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="38752@x" ObjectIDND1="38750@x" ObjectIDND2="38751@x" ObjectIDZND0="38752@0" Pin0InfoVect0LinkObjId="SW-232311_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-232311_0" Pin1InfoVect1LinkObjId="SW-232309_0" Pin1InfoVect2LinkObjId="SW-232310_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1373,-749 1390,-749 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bebf60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1426,-749 1440,-749 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="38752@1" ObjectIDZND0="g_3228f10@0" Pin0InfoVect0LinkObjId="g_3228f10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232311_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1426,-749 1440,-749 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3269450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1373,-813 1390,-813 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="38750@x" ObjectIDND1="38753@x" ObjectIDZND0="38754@0" Pin0InfoVect0LinkObjId="SW-232313_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-232309_0" Pin1InfoVect1LinkObjId="SW-232312_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1373,-813 1390,-813 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3762010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1426,-813 1440,-813 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="38754@1" ObjectIDZND0="g_35e53e0@0" Pin0InfoVect0LinkObjId="g_35e53e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232313_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1426,-813 1440,-813 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36886b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1427,-885 1440,-885 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="38755@1" ObjectIDZND0="g_2ca4ec0@0" Pin0InfoVect0LinkObjId="g_2ca4ec0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232314_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1427,-885 1440,-885 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b1bac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1373,-680 1373,-695 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="38746@0" ObjectIDZND0="38751@0" Pin0InfoVect0LinkObjId="SW-232310_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a9b440_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1373,-680 1373,-695 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b17c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1373,-870 1373,-885 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="38753@1" ObjectIDZND0="38755@x" ObjectIDZND1="38756@x" ObjectIDZND2="g_320f840@0" Pin0InfoVect0LinkObjId="SW-232314_0" Pin0InfoVect1LinkObjId="SW-232315_0" Pin0InfoVect2LinkObjId="g_320f840_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232312_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1373,-870 1373,-885 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3762380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1373,-885 1391,-885 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="38753@x" ObjectIDND1="38756@x" ObjectIDND2="g_320f840@0" ObjectIDZND0="38755@0" Pin0InfoVect0LinkObjId="SW-232314_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-232312_0" Pin1InfoVect1LinkObjId="SW-232315_0" Pin1InfoVect2LinkObjId="g_320f840_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1373,-885 1391,-885 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_321ff20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1373,-971 1373,-985 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" ObjectIDND0="38753@x" ObjectIDND1="38755@x" ObjectIDND2="g_320f840@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-232312_0" Pin1InfoVect1LinkObjId="SW-232314_0" Pin1InfoVect2LinkObjId="g_320f840_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1373,-971 1373,-985 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_377ced0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1373,-885 1373,-948 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="38753@x" ObjectIDND1="38755@x" ObjectIDZND0="38756@x" ObjectIDZND1="g_320f840@0" Pin0InfoVect0LinkObjId="SW-232315_0" Pin0InfoVect1LinkObjId="g_320f840_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-232312_0" Pin1InfoVect1LinkObjId="SW-232314_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1373,-885 1373,-948 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ca55b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1373,-948 1373,-971 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="38753@x" ObjectIDND1="38755@x" ObjectIDND2="g_320f840@0" ObjectIDZND0="38756@x" Pin0InfoVect0LinkObjId="SW-232315_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-232312_0" Pin1InfoVect1LinkObjId="SW-232314_0" Pin1InfoVect2LinkObjId="g_320f840_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1373,-948 1373,-971 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33ea2e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1342,-933 1342,-948 1373,-948 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_320f840@0" ObjectIDZND0="38753@x" ObjectIDZND1="38755@x" ObjectIDZND2="38756@x" Pin0InfoVect0LinkObjId="SW-232312_0" Pin0InfoVect1LinkObjId="SW-232314_0" Pin0InfoVect2LinkObjId="SW-232315_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_320f840_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1342,-933 1342,-948 1373,-948 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_312b9d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1309,-961 1309,-971 1373,-971 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="38756@1" ObjectIDZND0="38753@x" ObjectIDZND1="38755@x" ObjectIDZND2="g_320f840@0" Pin0InfoVect0LinkObjId="SW-232312_0" Pin0InfoVect1LinkObjId="SW-232314_0" Pin0InfoVect2LinkObjId="g_320f840_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232315_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1309,-961 1309,-971 1373,-971 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_376ac40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1309,-914 1309,-925 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" ObjectIDND0="g_338a920@0" ObjectIDZND0="38756@0" Pin0InfoVect0LinkObjId="SW-232315_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_338a920_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1309,-914 1309,-925 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_320f2f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1108,-562 1127,-562 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="38757@x" ObjectIDND1="38758@x" ObjectIDZND0="38759@0" Pin0InfoVect0LinkObjId="SW-232351_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-232349_0" Pin1InfoVect1LinkObjId="SW-232350_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1108,-562 1127,-562 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_320efd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1163,-562 1177,-562 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="38759@1" ObjectIDZND0="g_362e8f0@0" Pin0InfoVect0LinkObjId="g_362e8f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232351_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1163,-562 1177,-562 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a98660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1108,-538 1108,-562 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="38757@1" ObjectIDZND0="38759@x" ObjectIDZND1="38758@x" Pin0InfoVect0LinkObjId="SW-232351_0" Pin0InfoVect1LinkObjId="SW-232350_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232349_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1108,-538 1108,-562 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3140670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1108,-562 1108,-615 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="38759@x" ObjectIDND1="38757@x" ObjectIDZND0="38758@0" Pin0InfoVect0LinkObjId="SW-232350_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-232351_0" Pin1InfoVect1LinkObjId="SW-232349_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1108,-562 1108,-615 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ae7540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1373,-732 1373,-749 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="breaker" ObjectIDND0="38751@1" ObjectIDZND0="38752@x" ObjectIDZND1="38752@x" ObjectIDZND2="38750@x" Pin0InfoVect0LinkObjId="SW-232311_0" Pin0InfoVect1LinkObjId="SW-232311_0" Pin0InfoVect2LinkObjId="SW-232309_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232310_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1373,-732 1373,-749 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_37e2700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1373,-749 1373,-755 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" BeginDevType2="switch" EndDevType0="switch" EndDevType1="breaker" EndDevType2="switch" ObjectIDND0="38752@x" ObjectIDND1="38750@x" ObjectIDND2="38751@x" ObjectIDZND0="38752@x" ObjectIDZND1="38750@x" ObjectIDZND2="38751@x" Pin0InfoVect0LinkObjId="SW-232311_0" Pin0InfoVect1LinkObjId="SW-232309_0" Pin0InfoVect2LinkObjId="SW-232310_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-232311_0" Pin1InfoVect1LinkObjId="SW-232309_0" Pin1InfoVect2LinkObjId="SW-232310_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1373,-749 1373,-755 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b08ae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1559,-461 1559,-511 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="38841@1" ObjectIDZND0="38760@0" Pin0InfoVect0LinkObjId="SW-232403_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_39768d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1559,-461 1559,-511 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_380a6f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1561,-562 1578,-562 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="38760@x" ObjectIDND1="38761@x" ObjectIDZND0="38762@0" Pin0InfoVect0LinkObjId="SW-232405_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-232403_0" Pin1InfoVect1LinkObjId="SW-232404_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1561,-562 1578,-562 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_380a950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1614,-562 1628,-562 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="38762@1" ObjectIDZND0="g_2b17360@0" Pin0InfoVect0LinkObjId="g_2b17360_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232405_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1614,-562 1628,-562 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3139550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1559,-538 1559,-562 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="38760@1" ObjectIDZND0="38762@x" ObjectIDZND1="38761@x" Pin0InfoVect0LinkObjId="SW-232405_0" Pin0InfoVect1LinkObjId="SW-232404_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232403_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1559,-538 1559,-562 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3359f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1559,-562 1559,-615 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="38760@x" ObjectIDND1="38762@x" ObjectIDZND0="38761@0" Pin0InfoVect0LinkObjId="SW-232404_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-232403_0" Pin1InfoVect1LinkObjId="SW-232405_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1559,-562 1559,-615 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_335a1c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1559,-651 1559,-680 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="38761@1" ObjectIDZND0="38746@0" Pin0InfoVect0LinkObjId="g_2a9b440_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232404_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1559,-651 1559,-680 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ad4980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1305,-653 1322,-653 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="busSection" EndDevType0="switch" ObjectIDND0="38797@x" ObjectIDND1="38746@0" ObjectIDZND0="38799@0" Pin0InfoVect0LinkObjId="SW-232721_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-232719_0" Pin1InfoVect1LinkObjId="g_2a9b440_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1305,-653 1322,-653 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ad4be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1358,-653 1372,-653 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="38799@1" ObjectIDZND0="g_375efc0@0" Pin0InfoVect0LinkObjId="g_375efc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232721_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1358,-653 1372,-653 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ae8f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1305,-635 1305,-653 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="38797@1" ObjectIDZND0="38799@x" ObjectIDZND1="38746@0" Pin0InfoVect0LinkObjId="SW-232721_0" Pin0InfoVect1LinkObjId="g_2a9b440_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232719_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1305,-635 1305,-653 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_37c8e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1305,-653 1305,-680 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="38799@x" ObjectIDND1="38797@x" ObjectIDZND0="38746@0" Pin0InfoVect0LinkObjId="g_2a9b440_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-232721_0" Pin1InfoVect1LinkObjId="SW-232719_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1305,-653 1305,-680 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_37c9090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1360,-583 1374,-583 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="38798@1" ObjectIDZND0="g_397bcf0@0" Pin0InfoVect0LinkObjId="g_397bcf0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232720_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1360,-583 1374,-583 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ae99b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1307,-583 1324,-583 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="38797@x" ObjectIDND1="g_30b3d10@0" ObjectIDND2="g_30b42e0@0" ObjectIDZND0="38798@0" Pin0InfoVect0LinkObjId="SW-232720_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-232719_0" Pin1InfoVect1LinkObjId="g_30b3d10_0" Pin1InfoVect2LinkObjId="g_30b42e0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1307,-583 1324,-583 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_338a630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1305,-583 1305,-598 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="38798@x" ObjectIDND1="g_30b3d10@0" ObjectIDND2="g_30b42e0@0" ObjectIDZND0="38797@0" Pin0InfoVect0LinkObjId="SW-232719_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-232720_0" Pin1InfoVect1LinkObjId="g_30b3d10_0" Pin1InfoVect2LinkObjId="g_30b42e0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1305,-583 1305,-598 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_377f8f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1305,-562 1305,-569 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_30b3d10@0" ObjectIDZND0="38798@x" ObjectIDZND1="38797@x" ObjectIDZND2="g_30b42e0@0" Pin0InfoVect0LinkObjId="SW-232720_0" Pin0InfoVect1LinkObjId="SW-232719_0" Pin0InfoVect2LinkObjId="g_30b42e0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_30b3d10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1305,-562 1305,-569 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_377fb50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1305,-569 1305,-583 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_30b3d10@0" ObjectIDND1="g_30b42e0@0" ObjectIDZND0="38798@x" ObjectIDZND1="38797@x" Pin0InfoVect0LinkObjId="SW-232720_0" Pin0InfoVect1LinkObjId="SW-232719_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_30b3d10_0" Pin1InfoVect1LinkObjId="g_30b42e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1305,-569 1305,-583 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_377fdb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1260,-548 1260,-570 1305,-570 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_30b42e0@0" ObjectIDZND0="g_30b3d10@0" ObjectIDZND1="38798@x" ObjectIDZND2="38797@x" Pin0InfoVect0LinkObjId="g_30b3d10_0" Pin0InfoVect1LinkObjId="SW-232720_0" Pin0InfoVect2LinkObjId="SW-232719_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_30b42e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1260,-548 1260,-570 1305,-570 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35e4a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1305,-498 1305,-526 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_35e4ce0@0" ObjectIDZND0="g_30b3d10@1" Pin0InfoVect0LinkObjId="g_30b3d10_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_35e4ce0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1305,-498 1305,-526 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33740e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1645,-680 1645,-699 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="38746@0" ObjectIDZND0="38800@0" Pin0InfoVect0LinkObjId="SW-232722_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a9b440_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1645,-680 1645,-699 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35f7890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1645,-752 1662,-752 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="38800@x" ObjectIDND1="g_2a97cb0@0" ObjectIDZND0="38801@0" Pin0InfoVect0LinkObjId="SW-232723_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-232722_0" Pin1InfoVect1LinkObjId="g_2a97cb0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1645,-752 1662,-752 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35f7af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1698,-752 1712,-752 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="38801@1" ObjectIDZND0="g_35f7d50@0" Pin0InfoVect0LinkObjId="g_35f7d50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232723_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1698,-752 1712,-752 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35510a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1645,-735 1645,-752 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="38800@1" ObjectIDZND0="38801@x" ObjectIDZND1="g_2a97cb0@0" Pin0InfoVect0LinkObjId="SW-232723_0" Pin0InfoVect1LinkObjId="g_2a97cb0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232722_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1645,-735 1645,-752 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3551a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1645,-758 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1645,-758 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3551c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1645,-752 1645,-771 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="38800@x" ObjectIDND1="38801@x" ObjectIDZND0="g_2a97cb0@1" Pin0InfoVect0LinkObjId="g_2a97cb0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-232722_0" Pin1InfoVect1LinkObjId="SW-232723_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1645,-752 1645,-771 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3551e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1645,-825 1645,-801 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_2a97cb0@0" Pin0InfoVect0LinkObjId="g_2a97cb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3228f10_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1645,-825 1645,-801 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36980e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1108,-317 1108,-328 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="38803@1" ObjectIDZND0="38808@1" Pin0InfoVect0LinkObjId="SW-232791_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232787_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1108,-317 1108,-328 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3698910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1108,-280 1108,-290 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="38825@1" ObjectIDZND0="38803@0" Pin0InfoVect0LinkObjId="SW-232787_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232791_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1108,-280 1108,-290 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3698b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1108,-263 1108,-204 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="38825@0" ObjectIDZND0="38747@0" Pin0InfoVect0LinkObjId="g_366b570_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232791_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1108,-263 1108,-204 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36952b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="483,-148 483,-159 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="38764@1" ObjectIDZND0="38823@1" Pin0InfoVect0LinkObjId="SW-232463_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232462_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="483,-148 483,-159 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3695510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="483,-111 483,-121 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="38840@1" ObjectIDZND0="38764@0" Pin0InfoVect0LinkObjId="SW-232462_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232463_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="483,-111 483,-121 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_354da50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="517,-38 517,-46 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_354d000@0" ObjectIDZND0="38765@0" Pin0InfoVect0LinkObjId="SW-232464_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_354d000_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="517,-38 517,-46 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_354e9e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="483,-15 483,12 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3695770@0" ObjectIDZND0="38766@1" Pin0InfoVect0LinkObjId="SW-232465_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3695770_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="483,-15 483,12 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_366b570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="483,-176 483,-204 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="38823@0" ObjectIDZND0="38747@0" Pin0InfoVect0LinkObjId="g_3698b70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232463_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="483,-176 483,-204 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3672a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="428,50 428,190 482,190 482,180 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="capacitor" ObjectIDZND0="38844@0" Pin0InfoVect0LinkObjId="CB-CX_ZJD.CX_ZJD_Cb1_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="428,50 428,190 482,190 482,180 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3672ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="482,60 452,60 452,50 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" EndDevType0="switch" ObjectIDND0="38766@x" ObjectIDND1="38844@x" ObjectIDZND0="38767@0" Pin0InfoVect0LinkObjId="SW-232466_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-232465_0" Pin1InfoVect1LinkObjId="CB-CX_ZJD.CX_ZJD_Cb1_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="482,60 452,60 452,50 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3672f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="482,49 482,60 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="capacitor" ObjectIDND0="38766@0" ObjectIDZND0="38767@x" ObjectIDZND1="38844@x" Pin0InfoVect0LinkObjId="SW-232466_0" Pin0InfoVect1LinkObjId="CB-CX_ZJD.CX_ZJD_Cb1_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232465_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="482,49 482,60 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3673160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="482,60 482,69 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="capacitor" ObjectIDND0="38767@x" ObjectIDND1="38766@x" ObjectIDZND0="38844@1" Pin0InfoVect0LinkObjId="CB-CX_ZJD.CX_ZJD_Cb1_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-232466_0" Pin1InfoVect1LinkObjId="SW-232465_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="482,60 482,69 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31f5f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="452,5 452,14 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_36733c0@0" ObjectIDZND0="38767@1" Pin0InfoVect0LinkObjId="SW-232466_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36733c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="452,5 452,14 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31f8df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="450,-83 517,-83 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_354dcb0@0" ObjectIDZND0="38765@1" Pin0InfoVect0LinkObjId="SW-232464_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_354dcb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="450,-83 517,-83 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31f9050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="483,-94 483,-68 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="38840@0" ObjectIDZND0="g_3695770@1" Pin0InfoVect0LinkObjId="g_3695770_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232463_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="483,-94 483,-68 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31fb380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="619,-146 619,-157 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="38772@1" ObjectIDZND0="38810@1" Pin0InfoVect0LinkObjId="SW-232510_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232509_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="619,-146 619,-157 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31fb5e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="619,-109 619,-119 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="38827@1" ObjectIDZND0="38772@0" Pin0InfoVect0LinkObjId="SW-232509_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232510_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="619,-109 619,-119 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36d5380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="653,-36 653,-44 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_36d4930@0" ObjectIDZND0="38774@0" Pin0InfoVect0LinkObjId="SW-232512_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36d4930_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="653,-36 653,-44 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36d6310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="619,-174 619,-204 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="38810@0" ObjectIDZND0="38747@0" Pin0InfoVect0LinkObjId="g_3698b70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232510_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="619,-174 619,-204 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36d9550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="586,-81 653,-81 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_36d55e0@0" ObjectIDZND0="38774@1" Pin0InfoVect0LinkObjId="SW-232512_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36d55e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="586,-81 653,-81 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36d97b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="619,-92 619,-66 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="38827@0" ObjectIDZND0="g_31fb840@1" Pin0InfoVect0LinkObjId="g_31fb840_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232510_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="619,-92 619,-66 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36dca30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="618,-13 618,7 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_31fb840@0" ObjectIDZND0="38773@1" Pin0InfoVect0LinkObjId="SW-232511_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_31fb840_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="618,-13 618,7 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_389d190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="618,43 618,96 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" ObjectIDND0="38773@0" ObjectIDZND0="40934@0" Pin0InfoVect0LinkObjId="EC-CX_ZJD.032Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232511_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="618,43 618,96 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_389dde0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="654,101 654,93 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_389d3f0@0" ObjectIDZND0="38806@0" Pin0InfoVect0LinkObjId="SW-232792_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_389d3f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="654,101 654,93 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_389ecb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="587,56 654,56 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_389e040@0" ObjectIDZND0="38806@1" Pin0InfoVect0LinkObjId="SW-232792_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_389e040_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="587,56 654,56 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38a3180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="766,-146 766,-157 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="38775@1" ObjectIDZND0="38809@1" Pin0InfoVect0LinkObjId="SW-232533_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232532_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="766,-146 766,-157 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38a33e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="766,-109 766,-119 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="38826@1" ObjectIDZND0="38775@0" Pin0InfoVect0LinkObjId="SW-232532_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232533_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="766,-109 766,-119 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38a7130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="800,-36 800,-44 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_38a66e0@0" ObjectIDZND0="38777@0" Pin0InfoVect0LinkObjId="SW-232535_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_38a66e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="800,-36 800,-44 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38a80c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="766,-174 766,-204 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="38809@0" ObjectIDZND0="38747@0" Pin0InfoVect0LinkObjId="g_3698b70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232533_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="766,-174 766,-204 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38ab440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="733,-81 800,-81 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_38a7390@0" ObjectIDZND0="38777@1" Pin0InfoVect0LinkObjId="SW-232535_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_38a7390_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="733,-81 800,-81 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38ab6a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="766,-92 766,-66 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="38826@0" ObjectIDZND0="g_38a3640@1" Pin0InfoVect0LinkObjId="g_38a3640_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232533_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="766,-92 766,-66 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38ae920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="765,-13 765,7 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_38a3640@0" ObjectIDZND0="38776@1" Pin0InfoVect0LinkObjId="SW-232534_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_38a3640_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="765,-13 765,7 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38b1380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="765,43 765,93 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" ObjectIDND0="38776@0" ObjectIDZND0="40935@0" Pin0InfoVect0LinkObjId="EC-CX_ZJD.033Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232534_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="765,43 765,93 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38b2030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="801,101 801,93 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_38b15e0@0" ObjectIDZND0="38778@0" Pin0InfoVect0LinkObjId="SW-232536_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_38b15e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="801,101 801,93 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38b2fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="734,56 801,56 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_38b2290@0" ObjectIDZND0="38778@1" Pin0InfoVect0LinkObjId="SW-232536_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_38b2290_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="734,56 801,56 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38b5a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="915,-147 915,-158 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_39803a0@0" ObjectIDZND0="38819@1" Pin0InfoVect0LinkObjId="SW-232786_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_39803a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="915,-147 915,-158 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38b5c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="915,-110 915,-120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="38836@1" ObjectIDZND0="g_39803a0@1" Pin0InfoVect0LinkObjId="g_39803a0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232786_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="915,-110 915,-120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36a1a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="949,-37 949,-45 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_36a1040@0" ObjectIDZND0="38802@0" Pin0InfoVect0LinkObjId="SW-232724_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36a1040_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="949,-37 949,-45 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36a2a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="915,-175 915,-204 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="38819@0" ObjectIDZND0="38747@0" Pin0InfoVect0LinkObjId="g_3698b70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232786_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="915,-175 915,-204 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36a5ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="882,-82 949,-82 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_36a1cf0@0" ObjectIDZND0="38802@1" Pin0InfoVect0LinkObjId="SW-232724_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36a1cf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="882,-82 949,-82 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36a5d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="915,-93 915,-67 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="38836@0" ObjectIDZND0="g_38b5ee0@1" Pin0InfoVect0LinkObjId="g_38b5ee0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232786_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="915,-93 915,-67 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36a9040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="915,-14 915,5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_38b5ee0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_3228f10_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_38b5ee0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="915,-14 915,5 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36ab070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1052,-107 1052,-117 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="38839@1" ObjectIDZND0="38791@0" Pin0InfoVect0LinkObjId="SW-232648_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232649_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1052,-107 1052,-117 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36aef40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1086,-34 1086,-42 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_36ae4f0@0" ObjectIDZND0="38792@0" Pin0InfoVect0LinkObjId="SW-232650_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36ae4f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1086,-34 1086,-42 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36afed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1052,-172 1052,-204 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="38822@0" ObjectIDZND0="38747@0" Pin0InfoVect0LinkObjId="g_3698b70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232649_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1052,-172 1052,-204 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36b31c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1019,-79 1086,-79 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_36af1a0@0" ObjectIDZND0="38792@1" Pin0InfoVect0LinkObjId="SW-232650_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36af1a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1019,-79 1086,-79 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36b3420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1052,-90 1052,-64 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="38839@0" ObjectIDZND0="g_36ab2d0@1" Pin0InfoVect0LinkObjId="g_36ab2d0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232649_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1052,-90 1052,-64 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36b8810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1052,-144 1052,-155 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="38791@1" ObjectIDZND0="38822@1" Pin0InfoVect0LinkObjId="SW-232649_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232648_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1052,-144 1052,-155 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36b8a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1052,-11 1052,15 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_36ab2d0@0" ObjectIDZND0="g_36b8cd0@0" Pin0InfoVect0LinkObjId="g_36b8cd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36ab2d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1052,-11 1052,15 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36b9bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1104,45 1104,33 1103,32 1054,32 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="38793@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232651_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1104,45 1104,33 1103,32 1054,32 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36bc640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1104,81 1104,120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="38793@0" ObjectIDZND0="g_36bd840@0" Pin0InfoVect0LinkObjId="g_36bd840_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232651_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1104,81 1104,120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_36bc8a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1133,111 1133,97 1077,97 1077,108 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_36bcb10@0" ObjectIDZND0="g_36bf120@0" Pin0InfoVect0LinkObjId="g_36bf120_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36bcb10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1133,111 1133,97 1077,97 1077,108 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36beec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1104,154 1104,166 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="earth" ObjectIDND0="g_36bd840@1" ObjectIDZND0="g_36be470@0" Pin0InfoVect0LinkObjId="g_36be470_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36bd840_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1104,154 1104,166 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36c4ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1540,-152 1540,-163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="38796@1" ObjectIDZND0="38811@1" Pin0InfoVect0LinkObjId="SW-232700_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232699_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1540,-152 1540,-163 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36c5120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1540,-115 1540,-125 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="38828@1" ObjectIDZND0="38796@0" Pin0InfoVect0LinkObjId="SW-232699_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232700_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1540,-115 1540,-125 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36c5380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1540,-180 1540,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="38811@0" ObjectIDZND0="38748@0" Pin0InfoVect0LinkObjId="g_3668080_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232700_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1540,-180 1540,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36c55e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1385,-173 1385,-204 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="38812@0" ObjectIDZND0="38747@0" Pin0InfoVect0LinkObjId="g_3698b70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232701_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1385,-173 1385,-204 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36c5840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1385,-105 1385,-81 1540,-81 1540,-98 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="38829@0" ObjectIDZND0="38828@0" Pin0InfoVect0LinkObjId="SW-232700_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232701_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1385,-105 1385,-81 1540,-81 1540,-98 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3643c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1385,-156 1385,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="38812@1" ObjectIDZND0="38829@1" Pin0InfoVect0LinkObjId="SW-232701_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232701_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1385,-156 1385,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3647990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1189,-111 1189,-121 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="38832@1" ObjectIDZND0="38787@0" Pin0InfoVect0LinkObjId="SW-232604_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232605_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1189,-111 1189,-121 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_364b860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1223,-38 1223,-46 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_364ae10@0" ObjectIDZND0="38788@0" Pin0InfoVect0LinkObjId="SW-232606_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_364ae10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1223,-38 1223,-46 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_364c7f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1189,-176 1189,-204 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="38815@0" ObjectIDZND0="38747@0" Pin0InfoVect0LinkObjId="g_3698b70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232605_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1189,-176 1189,-204 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_364fae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1189,-94 1189,-68 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="38832@0" ObjectIDZND0="g_3647bf0@1" Pin0InfoVect0LinkObjId="g_3647bf0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232605_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1189,-94 1189,-68 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3654ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1189,-148 1189,-159 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="38787@1" ObjectIDZND0="38815@1" Pin0InfoVect0LinkObjId="SW-232605_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232604_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1189,-148 1189,-159 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3655130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1304,-175 1304,-204 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="38818@0" ObjectIDZND0="38747@0" Pin0InfoVect0LinkObjId="g_3698b70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232725_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1304,-175 1304,-204 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3658450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1304,-158 1304,-124 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="38818@1" ObjectIDZND0="38835@1" Pin0InfoVect0LinkObjId="SW-232725_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232725_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1304,-158 1304,-124 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_365bd70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1304,-50 1304,-24 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_365b4f0@1" ObjectIDZND0="g_365bfd0@0" Pin0InfoVect0LinkObjId="g_365bfd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_365b4f0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1304,-50 1304,-24 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_365fc00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1304,-93 1338,-93 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="38835@x" ObjectIDND1="g_365b4f0@0" ObjectIDZND0="g_36601d0@0" Pin0InfoVect0LinkObjId="g_36601d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-232725_0" Pin1InfoVect1LinkObjId="g_365b4f0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1304,-93 1338,-93 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_365fdf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1304,-107 1304,-93 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="38835@0" ObjectIDZND0="g_365b4f0@0" ObjectIDZND1="g_36601d0@0" Pin0InfoVect0LinkObjId="g_365b4f0_0" Pin0InfoVect1LinkObjId="g_36601d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232725_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1304,-107 1304,-93 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_365ffe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1304,-93 1304,-81 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="38835@x" ObjectIDND1="g_36601d0@0" ObjectIDZND0="g_365b4f0@0" Pin0InfoVect0LinkObjId="g_365b4f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-232725_0" Pin1InfoVect1LinkObjId="g_36601d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1304,-93 1304,-81 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3662fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1655,-148 1655,-159 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="38779@1" ObjectIDZND0="38814@1" Pin0InfoVect0LinkObjId="SW-232557_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232556_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1655,-148 1655,-159 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3663220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1655,-111 1655,-121 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="38831@1" ObjectIDZND0="38779@0" Pin0InfoVect0LinkObjId="SW-232556_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232557_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1655,-111 1655,-121 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36670f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1689,-38 1689,-46 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_36666a0@0" ObjectIDZND0="38781@0" Pin0InfoVect0LinkObjId="SW-232559_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36666a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1689,-38 1689,-46 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3668080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1655,-176 1655,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="38814@0" ObjectIDZND0="38748@0" Pin0InfoVect0LinkObjId="g_36c5380_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232557_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1655,-176 1655,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31b6750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1622,-83 1689,-83 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3667350@0" ObjectIDZND0="38781@1" Pin0InfoVect0LinkObjId="SW-232559_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3667350_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1622,-83 1689,-83 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31b69b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1655,-94 1655,-68 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="38831@0" ObjectIDZND0="g_3663480@1" Pin0InfoVect0LinkObjId="g_3663480_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232557_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1655,-94 1655,-68 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31b9a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1654,-15 1654,5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3663480@0" ObjectIDZND0="38780@1" Pin0InfoVect0LinkObjId="SW-232558_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3663480_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1654,-15 1654,5 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31bc4b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1654,41 1654,94 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" ObjectIDND0="38780@0" ObjectIDZND0="40937@0" Pin0InfoVect0LinkObjId="EC-CX_ZJD.041Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232558_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1654,41 1654,94 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31bd160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1690,99 1690,91 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_31bc710@0" ObjectIDZND0="38782@0" Pin0InfoVect0LinkObjId="SW-232560_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_31bc710_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1690,99 1690,91 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31be0f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1623,54 1690,54 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_31bd3c0@0" ObjectIDZND0="38782@1" Pin0InfoVect0LinkObjId="SW-232560_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_31bd3c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1623,54 1690,54 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31c9ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1963,-38 1963,-46 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_31c9290@0" ObjectIDZND0="38785@0" Pin0InfoVect0LinkObjId="SW-232583_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_31c9290_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1963,-38 1963,-46 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31cac70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1929,-176 1929,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="38813@0" ObjectIDZND0="38748@0" Pin0InfoVect0LinkObjId="g_36c5380_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232581_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1929,-176 1929,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31cdf60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1896,-83 1963,-83 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_31c9f40@0" ObjectIDZND0="38785@1" Pin0InfoVect0LinkObjId="SW-232583_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_31c9f40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1896,-83 1963,-83 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31ce1c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1929,-94 1929,-68 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="38830@0" ObjectIDZND0="g_31c6070@1" Pin0InfoVect0LinkObjId="g_31c6070_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232581_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1929,-94 1929,-68 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31d14e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1928,-15 1928,5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_31c6070@0" ObjectIDZND0="38784@1" Pin0InfoVect0LinkObjId="SW-232582_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_31c6070_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1928,-15 1928,5 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31d3f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1928,41 1928,94 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" ObjectIDND0="38784@0" ObjectIDZND0="40938@0" Pin0InfoVect0LinkObjId="EC-CX_ZJD.043Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232582_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1928,41 1928,94 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31d4bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1964,99 1964,91 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_31d41a0@0" ObjectIDZND0="38786@0" Pin0InfoVect0LinkObjId="SW-232584_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_31d41a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1964,99 1964,91 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31d5b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1897,54 1964,54 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_31d4e50@0" ObjectIDZND0="38786@1" Pin0InfoVect0LinkObjId="SW-232584_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_31d4e50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1897,54 1964,54 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31da6b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1929,-148 1929,-159 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="38783@1" ObjectIDZND0="38813@1" Pin0InfoVect0LinkObjId="SW-232581_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232580_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1929,-148 1929,-159 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31da910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1929,-111 1929,-121 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="38830@1" ObjectIDZND0="38783@0" Pin0InfoVect0LinkObjId="SW-232580_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232581_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1929,-111 1929,-121 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31de7e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1823,-35 1823,-43 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_31ddd90@0" ObjectIDZND0="38795@0" Pin0InfoVect0LinkObjId="SW-232676_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_31ddd90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1823,-35 1823,-43 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31df770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1789,-173 1789,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="38820@0" ObjectIDZND0="38748@0" Pin0InfoVect0LinkObjId="g_36c5380_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232675_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1789,-173 1789,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31e2af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1756,-80 1823,-80 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_31dea40@0" ObjectIDZND0="38795@1" Pin0InfoVect0LinkObjId="SW-232676_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_31dea40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1756,-80 1823,-80 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31e2d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1789,-91 1789,-65 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="38837@0" ObjectIDZND0="g_31dab70@1" Pin0InfoVect0LinkObjId="g_31dab70_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232675_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1789,-91 1789,-65 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31e80a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1789,-145 1789,-156 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="38794@1" ObjectIDZND0="38820@1" Pin0InfoVect0LinkObjId="SW-232675_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232674_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1789,-145 1789,-156 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31e8300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1789,-12 1789,14 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_31dab70@0" ObjectIDZND0="g_31e8560@0" Pin0InfoVect0LinkObjId="g_31e8560_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_31dab70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1789,-12 1789,14 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31e9460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1841,44 1841,32 1840,31 1791,31 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="38843@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232793_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1841,44 1841,32 1840,31 1791,31 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31ebed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1841,80 1841,119 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="38843@0" ObjectIDZND0="g_31ed0d0@0" Pin0InfoVect0LinkObjId="g_31ed0d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232793_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1841,80 1841,119 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_31ec130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1870,110 1870,96 1814,96 1814,107 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_31ec3a0@0" ObjectIDZND0="g_31ee9b0@0" Pin0InfoVect0LinkObjId="g_31ee9b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_31ec3a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1870,110 1870,96 1814,96 1814,107 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31ee750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1841,153 1841,165 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="earth" ObjectIDND0="g_31ed0d0@1" ObjectIDZND0="g_31edd00@0" Pin0InfoVect0LinkObjId="g_31edd00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_31ed0d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1841,153 1841,165 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31ef5c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1789,-108 1789,-118 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="38837@1" ObjectIDZND0="38794@0" Pin0InfoVect0LinkObjId="SW-232674_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232675_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1789,-108 1789,-118 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39407a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2105,-39 2105,-47 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_393fd50@0" ObjectIDZND0="38790@0" Pin0InfoVect0LinkObjId="SW-232628_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_393fd50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2105,-39 2105,-47 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3941730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2071,-177 2071,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="38816@0" ObjectIDZND0="38748@0" Pin0InfoVect0LinkObjId="g_36c5380_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232627_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2071,-177 2071,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3944a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2038,-84 2105,-84 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3940a00@0" ObjectIDZND0="38790@1" Pin0InfoVect0LinkObjId="SW-232628_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3940a00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2038,-84 2105,-84 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3944c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2071,-95 2071,-69 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="38833@0" ObjectIDZND0="g_31ef820@1" Pin0InfoVect0LinkObjId="g_31ef820_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232627_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2071,-95 2071,-69 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_394a070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2071,-149 2071,-160 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="38789@1" ObjectIDZND0="38816@1" Pin0InfoVect0LinkObjId="SW-232627_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232626_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2071,-149 2071,-160 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_394a2d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2071,-112 2071,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="38833@1" ObjectIDZND0="38789@0" Pin0InfoVect0LinkObjId="SW-232626_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232627_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2071,-112 2071,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_394c600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2259,-148 2259,-159 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="38768@1" ObjectIDZND0="38821@1" Pin0InfoVect0LinkObjId="SW-232487_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232486_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2259,-148 2259,-159 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_394c860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2259,-111 2259,-121 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="38838@1" ObjectIDZND0="38768@0" Pin0InfoVect0LinkObjId="SW-232486_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232487_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2259,-111 2259,-121 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3950730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2293,-38 2293,-46 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_394fce0@0" ObjectIDZND0="38769@0" Pin0InfoVect0LinkObjId="SW-232488_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_394fce0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2293,-38 2293,-46 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39516c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2259,-15 2259,12 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_394cac0@0" ObjectIDZND0="38770@1" Pin0InfoVect0LinkObjId="SW-232489_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_394cac0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2259,-15 2259,12 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3951920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2259,-176 2259,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="38821@0" ObjectIDZND0="38748@0" Pin0InfoVect0LinkObjId="g_36c5380_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232487_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2259,-176 2259,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_395c5c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2204,50 2204,190 2258,190 2258,180 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="capacitor" ObjectIDZND0="38845@0" Pin0InfoVect0LinkObjId="CB-CX_ZJD.CX_ZJD_Cb2_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2204,50 2204,190 2258,190 2258,180 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_395c830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2258,60 2228,60 2228,50 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" EndDevType0="switch" ObjectIDND0="38770@x" ObjectIDND1="38845@x" ObjectIDZND0="38771@0" Pin0InfoVect0LinkObjId="SW-232490_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-232489_0" Pin1InfoVect1LinkObjId="CB-CX_ZJD.CX_ZJD_Cb2_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2258,60 2228,60 2228,50 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_395ca90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2258,49 2258,60 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="capacitor" ObjectIDND0="38770@0" ObjectIDZND0="38771@x" ObjectIDZND1="38845@x" Pin0InfoVect0LinkObjId="SW-232490_0" Pin0InfoVect1LinkObjId="CB-CX_ZJD.CX_ZJD_Cb2_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232489_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2258,49 2258,60 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_395ccf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2258,60 2258,69 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="capacitor" ObjectIDND0="38771@x" ObjectIDND1="38770@x" ObjectIDZND0="38845@1" Pin0InfoVect0LinkObjId="CB-CX_ZJD.CX_ZJD_Cb2_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-232490_0" Pin1InfoVect1LinkObjId="SW-232489_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2258,60 2258,69 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_395e470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2228,5 2228,14 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_395cf50@0" ObjectIDZND0="38771@1" Pin0InfoVect0LinkObjId="SW-232490_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_395cf50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2228,5 2228,14 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39612b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2226,-83 2293,-83 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3950990@0" ObjectIDZND0="38769@1" Pin0InfoVect0LinkObjId="SW-232488_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3950990_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2226,-83 2293,-83 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3961510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2259,-94 2259,-68 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="38838@0" ObjectIDZND0="g_394cac0@1" Pin0InfoVect0LinkObjId="g_394cac0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232487_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2259,-94 2259,-68 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39650c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1156,-83 1223,-83 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_364bac0@0" ObjectIDZND0="38788@1" Pin0InfoVect0LinkObjId="SW-232606_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_364bac0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1156,-83 1223,-83 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39683e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2382,-158 2382,-124 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="38817@1" ObjectIDZND0="38834@1" Pin0InfoVect0LinkObjId="SW-232726_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232726_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2382,-158 2382,-124 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_396bd00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2382,-50 2382,-24 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_396b480@1" ObjectIDZND0="g_396bf60@0" Pin0InfoVect0LinkObjId="g_396bf60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_396b480_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2382,-50 2382,-24 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_396f540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2382,-93 2416,-93 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="38834@x" ObjectIDND1="g_396b480@0" ObjectIDZND0="g_396fb10@0" Pin0InfoVect0LinkObjId="g_396fb10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-232726_0" Pin1InfoVect1LinkObjId="g_396b480_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2382,-93 2416,-93 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_396f730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2382,-107 2382,-93 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="38834@0" ObjectIDZND0="g_396fb10@0" ObjectIDZND1="g_396b480@0" Pin0InfoVect0LinkObjId="g_396fb10_0" Pin0InfoVect1LinkObjId="g_396b480_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232726_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2382,-107 2382,-93 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_396f920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2382,-93 2382,-81 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_396fb10@0" ObjectIDND1="38834@x" ObjectIDZND0="g_396b480@0" Pin0InfoVect0LinkObjId="g_396b480_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_396fb10_0" Pin1InfoVect1LinkObjId="SW-232726_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2382,-93 2382,-81 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3970590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2382,-175 2382,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="38817@0" ObjectIDZND0="38748@0" Pin0InfoVect0LinkObjId="g_36c5380_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232726_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2382,-175 2382,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39761b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1559,-313 1559,-324 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="38763@1" ObjectIDZND0="38807@1" Pin0InfoVect0LinkObjId="SW-232416_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232415_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1559,-313 1559,-324 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3976410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1559,-276 1559,-286 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="38824@1" ObjectIDZND0="38763@0" Pin0InfoVect0LinkObjId="SW-232415_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232416_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1559,-276 1559,-286 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3976670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1559,-259 1559,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="38824@0" ObjectIDZND0="38748@0" Pin0InfoVect0LinkObjId="g_36c5380_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232416_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1559,-259 1559,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39768d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1559,-341 1559,-381 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="38807@0" ObjectIDZND0="38841@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232416_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1559,-341 1559,-381 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39972c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1108,-345 1108,-382 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="38808@0" ObjectIDZND0="38842@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-232791_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1108,-345 1108,-382 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3997520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1108,-462 1108,-511 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="38842@1" ObjectIDZND0="38757@0" Pin0InfoVect0LinkObjId="SW-232349_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_39972c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1108,-462 1108,-511 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_399b180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1212,91 1212,83 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_399a790@0" ObjectIDZND0="39361@0" Pin0InfoVect0LinkObjId="SW-235776_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_399a790_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1212,91 1212,83 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_399c110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1171,46 1189,46 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="load" ObjectIDND0="g_399b3e0@0" ObjectIDZND0="39360@x" ObjectIDZND1="39361@x" ObjectIDZND2="40936@x" Pin0InfoVect0LinkObjId="SW-235777_0" Pin0InfoVect1LinkObjId="SW-235776_0" Pin0InfoVect2LinkObjId="EC-CX_ZJD.036Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_399b3e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1171,46 1189,46 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39a0350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1189,33 1189,46 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="load" ObjectIDND0="39360@0" ObjectIDZND0="g_399b3e0@0" ObjectIDZND1="39361@x" ObjectIDZND2="40936@x" Pin0InfoVect0LinkObjId="g_399b3e0_0" Pin0InfoVect1LinkObjId="SW-235776_0" Pin0InfoVect2LinkObjId="EC-CX_ZJD.036Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235777_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1189,33 1189,46 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39a0540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1189,46 1189,106 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_399b3e0@0" ObjectIDND1="39360@x" ObjectIDND2="39361@x" ObjectIDZND0="40936@0" Pin0InfoVect0LinkObjId="EC-CX_ZJD.036Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_399b3e0_0" Pin1InfoVect1LinkObjId="SW-235777_0" Pin1InfoVect2LinkObjId="SW-235776_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1189,46 1189,106 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39a0730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1189,-15 1189,-3 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3647bf0@0" ObjectIDZND0="39360@1" Pin0InfoVect0LinkObjId="SW-235777_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3647bf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1189,-15 1189,-3 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39a0960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1212,46 1189,46 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="load" ObjectIDND0="39361@1" ObjectIDZND0="g_399b3e0@0" ObjectIDZND1="39360@x" ObjectIDZND2="40936@x" Pin0InfoVect0LinkObjId="g_399b3e0_0" Pin0InfoVect1LinkObjId="SW-235777_0" Pin0InfoVect2LinkObjId="EC-CX_ZJD.036Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235776_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1212,46 1189,46 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39a3bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2094,91 2094,83 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_39a3170@0" ObjectIDZND0="39362@0" Pin0InfoVect0LinkObjId="SW-235779_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_39a3170_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2094,91 2094,83 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39a4b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2053,46 2071,46 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="load" EndDevType2="lightningRod" ObjectIDND0="g_39a3e20@0" ObjectIDZND0="g_39a3e20@0" ObjectIDZND1="40939@x" ObjectIDZND2="g_39a3e20@0" Pin0InfoVect0LinkObjId="g_39a3e20_0" Pin0InfoVect1LinkObjId="EC-CX_ZJD.044Ld_0" Pin0InfoVect2LinkObjId="g_39a3e20_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_39a3e20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2053,46 2071,46 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39a7e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2071,33 2071,46 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="39363@0" ObjectIDZND0="g_39a3e20@0" ObjectIDZND1="g_39a3e20@0" ObjectIDZND2="40939@x" Pin0InfoVect0LinkObjId="g_39a3e20_0" Pin0InfoVect1LinkObjId="g_39a3e20_0" Pin0InfoVect2LinkObjId="EC-CX_ZJD.044Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235778_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2071,33 2071,46 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39a8010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2094,46 2071,46 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="39362@1" ObjectIDZND0="g_39a3e20@0" ObjectIDZND1="g_39a3e20@0" ObjectIDZND2="40939@x" Pin0InfoVect0LinkObjId="g_39a3e20_0" Pin0InfoVect1LinkObjId="g_39a3e20_0" Pin0InfoVect2LinkObjId="EC-CX_ZJD.044Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235779_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2094,46 2071,46 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39a8d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2071,-16 2071,-3 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_31ef820@0" ObjectIDZND0="39363@1" Pin0InfoVect0LinkObjId="SW-235778_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_31ef820_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2071,-16 2071,-3 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39a8fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2071,46 2071,76 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" EndDevType1="load" EndDevType2="lightningRod" ObjectIDND0="g_39a3e20@0" ObjectIDND1="39363@x" ObjectIDND2="g_39a3e20@0" ObjectIDZND0="g_39a3e20@0" ObjectIDZND1="40939@x" ObjectIDZND2="g_39a3e20@0" Pin0InfoVect0LinkObjId="g_39a3e20_0" Pin0InfoVect1LinkObjId="EC-CX_ZJD.044Ld_0" Pin0InfoVect2LinkObjId="g_39a3e20_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_39a3e20_0" Pin1InfoVect1LinkObjId="SW-235778_0" Pin1InfoVect2LinkObjId="g_39a3e20_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2071,46 2071,76 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39a9a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2071,46 2071,76 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="load" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="load" ObjectIDND0="g_39a3e20@0" ObjectIDND1="g_39a3e20@0" ObjectIDND2="40939@x" ObjectIDZND0="g_39a3e20@0" ObjectIDZND1="39363@x" ObjectIDZND2="40939@x" Pin0InfoVect0LinkObjId="g_39a3e20_0" Pin0InfoVect1LinkObjId="SW-235778_0" Pin0InfoVect2LinkObjId="EC-CX_ZJD.044Ld_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_39a3e20_0" Pin1InfoVect1LinkObjId="g_39a3e20_0" Pin1InfoVect2LinkObjId="EC-CX_ZJD.044Ld_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2071,46 2071,76 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39a9cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2071,76 2071,100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="load" ObjectIDND0="g_39a3e20@0" ObjectIDND1="39363@x" ObjectIDND2="g_39a3e20@0" ObjectIDZND0="40939@0" Pin0InfoVect0LinkObjId="EC-CX_ZJD.044Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_39a3e20_0" Pin1InfoVect1LinkObjId="SW-235778_0" Pin1InfoVect2LinkObjId="g_39a3e20_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2071,76 2071,100 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="38746" cx="1373" cy="-680" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38746" cx="1559" cy="-680" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38746" cx="1305" cy="-680" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38746" cx="1645" cy="-680" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38746" cx="1108" cy="-680" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38747" cx="1108" cy="-204" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38747" cx="483" cy="-204" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38747" cx="619" cy="-204" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38747" cx="766" cy="-204" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38747" cx="915" cy="-204" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38747" cx="1052" cy="-204" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38747" cx="1385" cy="-204" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38747" cx="1189" cy="-204" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38747" cx="1304" cy="-204" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38748" cx="1540" cy="-205" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38748" cx="1655" cy="-205" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38748" cx="1929" cy="-205" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38748" cx="1789" cy="-205" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38748" cx="2071" cy="-205" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38748" cx="2259" cy="-205" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38748" cx="2382" cy="-205" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38748" cx="1559" cy="-205" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-231931" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 288.500000 -911.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38693" ObjectName="DYN-CX_ZJD"/>
     <cge:Meas_Ref ObjectId="231931"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_313fa40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_313fa40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_313fa40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_313fa40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_313fa40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_313fa40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_313fa40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_313fa40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_313fa40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_313fa40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_313fa40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_313fa40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_313fa40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_313fa40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_313fa40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_313fa40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_313fa40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_313fa40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ca9a30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ca9a30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ca9a30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ca9a30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ca9a30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ca9a30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ca9a30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_2ad5970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 152.000000 -1001.500000) translate(0,16)">赵家店变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2bc1ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 911.000000 -466.000000) translate(0,15)">1号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2bc1ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 911.000000 -466.000000) translate(0,33)">SZ11-8000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2bc1ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 911.000000 -466.000000) translate(0,51)">35±3x2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2bc1ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 911.000000 -466.000000) translate(0,69)">YNd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2dc1e50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 36.000000 -611.000000) translate(0,15)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b12520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1591.500000 -942.000000) translate(0,15)">35kV1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ab54a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1385.000000 -790.000000) translate(0,12)">331</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37666b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1383.000000 -854.000000) translate(0,12)">3316</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3101710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1383.000000 -718.000000) translate(0,12)">3311</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30eeba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1398.000000 -913.000000) translate(0,12)">33167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3684e50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1419.000000 -839.000000) translate(0,12)">33160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3817660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1419.000000 -773.000000) translate(0,12)">33117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_338bcc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1387.000000 -998.000000) translate(0,15)">北</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_338bcc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1387.000000 -998.000000) translate(0,33)">店</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_338bcc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1387.000000 -998.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_377a560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1264.000000 -959.000000) translate(0,12)">3313</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a9c1b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1653.000000 -450.000000) translate(0,15)">2号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a9c1b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1653.000000 -450.000000) translate(0,33)">SZ11-8000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a9c1b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1653.000000 -450.000000) translate(0,51)">35±3x2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a9c1b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1653.000000 -450.000000) translate(0,69)">YNd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35f87e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1673.000000 -778.000000) translate(0,12)">33217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_35435a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1261.500000 -460.000000) translate(0,15)">35kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3698dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1123.000000 -310.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31f6300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 492.500000 28.000000) translate(0,12)">0316</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36d6570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 633.000000 -141.000000) translate(0,12)">032</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38a8320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 780.000000 -141.000000) translate(0,12)">033</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36b0130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1066.000000 -139.000000) translate(0,12)">035</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3643ef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1396.000000 -147.000000) translate(0,12)">0121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3644520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1556.000000 -146.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_364ca50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1203.000000 -143.000000) translate(0,12)">036</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_365ebd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1266.000000 22.000000) translate(0,12)">10kVⅠ母TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36682e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1669.000000 -143.000000) translate(0,12)">041</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31caed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1943.000000 -143.000000) translate(0,12)">043</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31df9d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1803.000000 -140.000000) translate(0,12)">042</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3941990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2085.000000 -144.000000) translate(0,12)">044</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3951b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2274.000000 -142.000000) translate(0,12)">045</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_395e860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2268.500000 28.000000) translate(0,12)">0456</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_396eb60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2344.000000 22.000000) translate(0,12)">10kVⅡ母TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3979930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 973.000000 -707.000000) translate(0,15)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3979f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 414.000000 -231.000000) translate(0,15)">10kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_397a860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2340.000000 -231.000000) translate(0,15)">10kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_397ae30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1115.000000 -640.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_397b070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1121.000000 -533.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_397b2b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1314.000000 -620.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_397b870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1397.000000 -659.000000) translate(0,12)">39010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_397bab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1399.000000 -588.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_397c4d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1129.000000 -587.000000) translate(0,12)">30117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_397caa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1652.000000 -724.000000) translate(0,12)">3321</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_397cce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1572.000000 -531.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_397cf20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1569.000000 -638.000000) translate(0,12)">3021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_397d160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1583.000000 -588.000000) translate(0,12)">30217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_397d3a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1573.000000 -307.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_397edb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 524.000000 -66.000000) translate(0,12)">03160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_397f3e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 373.000000 27.000000) translate(0,12)">03167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_397f620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 662.000000 -68.000000) translate(0,12)">03260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_397f860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 661.000000 69.000000) translate(0,12)">03267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_397faa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 809.000000 -67.000000) translate(0,12)">03360</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_397fce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 808.000000 66.000000) translate(0,12)">03367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_397ff20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 772.000000 18.000000) translate(0,12)">0336</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3980160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 626.000000 18.000000) translate(0,12)">0326</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3980ac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 932.000000 -139.000000) translate(0,12)">0341</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3980fb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 956.000000 -71.000000) translate(0,12)">03410</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39811f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1095.000000 -65.000000) translate(0,12)">03560</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3981430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1111.000000 55.000000) translate(0,12)">0030</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3981670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1230.000000 -72.000000) translate(0,12)">03660</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39818b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1319.000000 -150.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3981af0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1696.000000 -72.000000) translate(0,12)">04160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3981d30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1697.000000 64.000000) translate(0,12)">04167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3981f70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1661.000000 15.000000) translate(0,12)">0416</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39821b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1831.000000 -68.000000) translate(0,12)">04260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39823f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1848.000000 54.000000) translate(0,12)">0040</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3982630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1970.000000 -72.000000) translate(0,12)">04360</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3982870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1935.000000 15.000000) translate(0,12)">0436</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3982ab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1971.000000 64.000000) translate(0,12)">04367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3982cf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2112.000000 -73.000000) translate(0,12)">04460</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3982f30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2157.000000 33.000000) translate(0,12)">04567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3983170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2394.000000 -147.000000) translate(0,12)">0902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39833b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2191.000000 199.000000) translate(0,12)">2号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3984400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1809.000000 189.000000) translate(0,12)">2号接地变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3984fe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1074.000000 190.000000) translate(0,12)">1号接地变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3985560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 884.000000 107.000000) translate(0,12)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3985d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 425.000000 202.000000) translate(0,12)">1号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_398fa30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2301.000000 -72.000000) translate(0,12)">04560</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_3991b10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 293.000000 -1001.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3992c60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 495.000000 -142.000000) translate(0,12)">031</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3997780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1136.000000 -429.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3998180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1587.000000 -428.000000) translate(0,12)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_399eb70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1219.000000 59.000000) translate(0,12)">03667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_399f1a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1197.000000 8.000000) translate(0,12)">0366</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_399f3e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 135.000000 -68.000000) translate(0,12)">13638741120</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39a75b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2101.000000 59.000000) translate(0,12)">04467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39a7be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2079.000000 8.000000) translate(0,12)">0446</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39add70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 570.000000 127.000000) translate(0,12)">永仁施工Ⅰ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39af540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 715.000000 127.000000) translate(0,12)">永仁施工Ⅱ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39aff20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1170.000000 138.000000) translate(0,12)">红山线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39b1550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1603.000000 126.000000) translate(0,12)">大姚施工Ⅰ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39b20e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1887.000000 126.000000) translate(0,12)">大姚施工Ⅱ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39b2810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2044.000000 128.000000) translate(0,12)">大双沟线</text>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-232309">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1364.000000 -761.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38750" ObjectName="SW-CX_ZJD.CX_ZJD_331BK"/>
     <cge:Meas_Ref ObjectId="232309"/>
    <cge:TPSR_Ref TObjectID="38750"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232349">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1099.000000 -503.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38757" ObjectName="SW-CX_ZJD.CX_ZJD_301BK"/>
     <cge:Meas_Ref ObjectId="232349"/>
    <cge:TPSR_Ref TObjectID="38757"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232403">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1550.000000 -503.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38760" ObjectName="SW-CX_ZJD.CX_ZJD_302BK"/>
     <cge:Meas_Ref ObjectId="232403"/>
    <cge:TPSR_Ref TObjectID="38760"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232787">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1099.000000 -282.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38803" ObjectName="SW-CX_ZJD.CX_ZJD_001BK"/>
     <cge:Meas_Ref ObjectId="232787"/>
    <cge:TPSR_Ref TObjectID="38803"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232509">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 610.000000 -111.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38772" ObjectName="SW-CX_ZJD.CX_ZJD_032BK"/>
     <cge:Meas_Ref ObjectId="232509"/>
    <cge:TPSR_Ref TObjectID="38772"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232532">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 757.000000 -111.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38775" ObjectName="SW-CX_ZJD.CX_ZJD_033BK"/>
     <cge:Meas_Ref ObjectId="232532"/>
    <cge:TPSR_Ref TObjectID="38775"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232648">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1043.000000 -109.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38791" ObjectName="SW-CX_ZJD.CX_ZJD_035BK"/>
     <cge:Meas_Ref ObjectId="232648"/>
    <cge:TPSR_Ref TObjectID="38791"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232699">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1531.000000 -117.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38796" ObjectName="SW-CX_ZJD.CX_ZJD_012BK"/>
     <cge:Meas_Ref ObjectId="232699"/>
    <cge:TPSR_Ref TObjectID="38796"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232604">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1180.000000 -113.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38787" ObjectName="SW-CX_ZJD.CX_ZJD_036BK"/>
     <cge:Meas_Ref ObjectId="232604"/>
    <cge:TPSR_Ref TObjectID="38787"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232556">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1646.000000 -113.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38779" ObjectName="SW-CX_ZJD.CX_ZJD_041BK"/>
     <cge:Meas_Ref ObjectId="232556"/>
    <cge:TPSR_Ref TObjectID="38779"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232580">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1920.000000 -113.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38783" ObjectName="SW-CX_ZJD.CX_ZJD_043BK"/>
     <cge:Meas_Ref ObjectId="232580"/>
    <cge:TPSR_Ref TObjectID="38783"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232674">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1780.000000 -110.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38794" ObjectName="SW-CX_ZJD.CX_ZJD_042BK"/>
     <cge:Meas_Ref ObjectId="232674"/>
    <cge:TPSR_Ref TObjectID="38794"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232626">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2062.000000 -113.628866)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38789" ObjectName="SW-CX_ZJD.CX_ZJD_044BK"/>
     <cge:Meas_Ref ObjectId="232626"/>
    <cge:TPSR_Ref TObjectID="38789"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232486">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2250.000000 -113.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38768" ObjectName="SW-CX_ZJD.CX_ZJD_045BK"/>
     <cge:Meas_Ref ObjectId="232486"/>
    <cge:TPSR_Ref TObjectID="38768"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232415">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1550.000000 -278.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38763" ObjectName="SW-CX_ZJD.CX_ZJD_002BK"/>
     <cge:Meas_Ref ObjectId="232415"/>
    <cge:TPSR_Ref TObjectID="38763"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-232462">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 474.000000 -113.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38764" ObjectName="SW-CX_ZJD.CX_ZJD_031BK"/>
     <cge:Meas_Ref ObjectId="232462"/>
    <cge:TPSR_Ref TObjectID="38764"/></metadata>
   </g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-CX_ZJD.CX_ZJD_Cb1">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 456.000000 185.000000)" xlink:href="#capacitor:shape25"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38844" ObjectName="CB-CX_ZJD.CX_ZJD_Cb1"/>
    <cge:TPSR_Ref TObjectID="38844"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-CX_ZJD.CX_ZJD_Cb2">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2232.000000 185.000000)" xlink:href="#capacitor:shape25"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38845" ObjectName="CB-CX_ZJD.CX_ZJD_Cb2"/>
    <cge:TPSR_Ref TObjectID="38845"/></metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2a97cb0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1636.000000 -766.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_320f840">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1335.000000 -879.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_30b3d10">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1296.000000 -521.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_30b42e0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 1253.000000 -553.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3695770">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 478.000000 -10.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_354dcb0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 442.000000 -28.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_31fb840">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 613.000000 -8.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36d55e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 579.000000 -27.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_389e040">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 580.000000 110.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_38a3640">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 760.000000 -8.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_38a7390">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 726.000000 -27.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_38b2290">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 727.000000 110.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_38b5ee0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 910.000000 -9.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36a1cf0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 875.000000 -28.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36ab2d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1047.000000 -6.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36af1a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1012.000000 -25.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36b8cd0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1035.000000 48.000000)" xlink:href="#lightningRod:shape10"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36bcb10">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1126.000000 165.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36bd840">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1097.000000 159.000000)" xlink:href="#lightningRod:shape174"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3647bf0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1184.000000 -10.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_364bac0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1149.000000 -29.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_365b4f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1295.000000 -45.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36601d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1331.000000 -39.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3663480">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1649.000000 -10.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3667350">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1615.000000 -29.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_31bd3c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1616.000000 108.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_31c6070">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1923.000000 -10.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_31c9f40">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1889.000000 -29.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_31d4e50">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1890.000000 108.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_31dab70">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1784.000000 -7.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_31dea40">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1749.000000 -26.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_31e8560">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1772.000000 47.000000)" xlink:href="#lightningRod:shape10"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_31ec3a0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1863.000000 164.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_31ed0d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1834.000000 158.000000)" xlink:href="#lightningRod:shape174"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_31ef820">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2066.000000 -11.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3940a00">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2031.000000 -30.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_394cac0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2254.000000 -10.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3950990">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2219.000000 -29.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_396b480">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2373.000000 -45.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_396fb10">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2409.000000 -39.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_39803a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 906.000000 -110.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_399b3e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1164.000000 100.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_39a3e20">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2046.000000 100.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-232151" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1199.000000 -922.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="232151" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38750"/>
     <cge:Term_Ref ObjectID="58079"/>
    <cge:TPSR_Ref TObjectID="38750"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-232152" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1199.000000 -922.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="232152" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38750"/>
     <cge:Term_Ref ObjectID="58079"/>
    <cge:TPSR_Ref TObjectID="38750"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-232148" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1199.000000 -922.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="232148" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38750"/>
     <cge:Term_Ref ObjectID="58079"/>
    <cge:TPSR_Ref TObjectID="38750"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-232154" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1131.000000 -771.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="232154" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38746"/>
     <cge:Term_Ref ObjectID="58074"/>
    <cge:TPSR_Ref TObjectID="38746"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-232155" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1131.000000 -771.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="232155" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38746"/>
     <cge:Term_Ref ObjectID="58074"/>
    <cge:TPSR_Ref TObjectID="38746"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-232156" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1131.000000 -771.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="232156" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38746"/>
     <cge:Term_Ref ObjectID="58074"/>
    <cge:TPSR_Ref TObjectID="38746"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-232160" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1131.000000 -771.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="232160" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38746"/>
     <cge:Term_Ref ObjectID="58074"/>
    <cge:TPSR_Ref TObjectID="38746"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-232157" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1131.000000 -771.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="232157" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38746"/>
     <cge:Term_Ref ObjectID="58074"/>
    <cge:TPSR_Ref TObjectID="38746"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-232187" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 997.000000 -599.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="232187" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38757"/>
     <cge:Term_Ref ObjectID="58093"/>
    <cge:TPSR_Ref TObjectID="38757"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-232188" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 997.000000 -599.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="232188" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38757"/>
     <cge:Term_Ref ObjectID="58093"/>
    <cge:TPSR_Ref TObjectID="38757"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-232178" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 997.000000 -599.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="232178" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38757"/>
     <cge:Term_Ref ObjectID="58093"/>
    <cge:TPSR_Ref TObjectID="38757"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-232200" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1009.000000 -324.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="232200" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38803"/>
     <cge:Term_Ref ObjectID="58185"/>
    <cge:TPSR_Ref TObjectID="38803"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-232201" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1009.000000 -324.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="232201" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38803"/>
     <cge:Term_Ref ObjectID="58185"/>
    <cge:TPSR_Ref TObjectID="38803"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-232190" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1009.000000 -324.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="232190" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38803"/>
     <cge:Term_Ref ObjectID="58185"/>
    <cge:TPSR_Ref TObjectID="38803"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-232214" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1705.000000 -628.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="232214" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38760"/>
     <cge:Term_Ref ObjectID="58099"/>
    <cge:TPSR_Ref TObjectID="38760"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-232215" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1705.000000 -628.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="232215" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38760"/>
     <cge:Term_Ref ObjectID="58099"/>
    <cge:TPSR_Ref TObjectID="38760"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-232205" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1705.000000 -628.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="232205" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38760"/>
     <cge:Term_Ref ObjectID="58099"/>
    <cge:TPSR_Ref TObjectID="38760"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-232227" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1695.000000 -317.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="232227" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38763"/>
     <cge:Term_Ref ObjectID="58105"/>
    <cge:TPSR_Ref TObjectID="38763"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-232228" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1695.000000 -317.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="232228" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38763"/>
     <cge:Term_Ref ObjectID="58105"/>
    <cge:TPSR_Ref TObjectID="38763"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-232217" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1695.000000 -317.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="232217" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38763"/>
     <cge:Term_Ref ObjectID="58105"/>
    <cge:TPSR_Ref TObjectID="38763"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-232235" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 384.000000 -156.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="232235" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38764"/>
     <cge:Term_Ref ObjectID="58107"/>
    <cge:TPSR_Ref TObjectID="38764"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-232232" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 384.000000 -156.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="232232" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38764"/>
     <cge:Term_Ref ObjectID="58107"/>
    <cge:TPSR_Ref TObjectID="38764"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-232236" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 384.000000 -156.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="232236" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38764"/>
     <cge:Term_Ref ObjectID="58107"/>
    <cge:TPSR_Ref TObjectID="38764"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-232257" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 596.000000 159.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="232257" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38772"/>
     <cge:Term_Ref ObjectID="58123"/>
    <cge:TPSR_Ref TObjectID="38772"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-232258" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 596.000000 159.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="232258" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38772"/>
     <cge:Term_Ref ObjectID="58123"/>
    <cge:TPSR_Ref TObjectID="38772"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-232254" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 596.000000 159.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="232254" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38772"/>
     <cge:Term_Ref ObjectID="58123"/>
    <cge:TPSR_Ref TObjectID="38772"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-232263" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 734.000000 158.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="232263" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38775"/>
     <cge:Term_Ref ObjectID="58129"/>
    <cge:TPSR_Ref TObjectID="38775"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-232264" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 734.000000 158.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="232264" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38775"/>
     <cge:Term_Ref ObjectID="58129"/>
    <cge:TPSR_Ref TObjectID="38775"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-232260" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 734.000000 158.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="232260" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38775"/>
     <cge:Term_Ref ObjectID="58129"/>
    <cge:TPSR_Ref TObjectID="38775"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-232245" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1029.000000 159.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="232245" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38791"/>
     <cge:Term_Ref ObjectID="58161"/>
    <cge:TPSR_Ref TObjectID="38791"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-232246" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1029.000000 159.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="232246" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38791"/>
     <cge:Term_Ref ObjectID="58161"/>
    <cge:TPSR_Ref TObjectID="38791"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-232242" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1029.000000 159.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="232242" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38791"/>
     <cge:Term_Ref ObjectID="58161"/>
    <cge:TPSR_Ref TObjectID="38791"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-232269" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1178.000000 158.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="232269" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38787"/>
     <cge:Term_Ref ObjectID="58153"/>
    <cge:TPSR_Ref TObjectID="38787"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-232270" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1178.000000 158.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="232270" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38787"/>
     <cge:Term_Ref ObjectID="58153"/>
    <cge:TPSR_Ref TObjectID="38787"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-232266" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1178.000000 158.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="232266" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38787"/>
     <cge:Term_Ref ObjectID="58153"/>
    <cge:TPSR_Ref TObjectID="38787"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-232281" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1625.000000 155.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="232281" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38779"/>
     <cge:Term_Ref ObjectID="58137"/>
    <cge:TPSR_Ref TObjectID="38779"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-232282" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1625.000000 155.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="232282" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38779"/>
     <cge:Term_Ref ObjectID="58137"/>
    <cge:TPSR_Ref TObjectID="38779"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-232278" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1625.000000 155.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="232278" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38779"/>
     <cge:Term_Ref ObjectID="58137"/>
    <cge:TPSR_Ref TObjectID="38779"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-232275" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1478.000000 -69.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="232275" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38796"/>
     <cge:Term_Ref ObjectID="58171"/>
    <cge:TPSR_Ref TObjectID="38796"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-232276" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1478.000000 -69.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="232276" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38796"/>
     <cge:Term_Ref ObjectID="58171"/>
    <cge:TPSR_Ref TObjectID="38796"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-232272" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1478.000000 -69.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="232272" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38796"/>
     <cge:Term_Ref ObjectID="58171"/>
    <cge:TPSR_Ref TObjectID="38796"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-232251" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1799.000000 214.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="232251" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38794"/>
     <cge:Term_Ref ObjectID="58167"/>
    <cge:TPSR_Ref TObjectID="38794"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-232252" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1799.000000 214.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="232252" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38794"/>
     <cge:Term_Ref ObjectID="58167"/>
    <cge:TPSR_Ref TObjectID="38794"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-232248" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1799.000000 214.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="232248" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38794"/>
     <cge:Term_Ref ObjectID="58167"/>
    <cge:TPSR_Ref TObjectID="38794"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-232287" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1909.000000 154.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="232287" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38783"/>
     <cge:Term_Ref ObjectID="58145"/>
    <cge:TPSR_Ref TObjectID="38783"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-232288" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1909.000000 154.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="232288" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38783"/>
     <cge:Term_Ref ObjectID="58145"/>
    <cge:TPSR_Ref TObjectID="38783"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-232284" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1909.000000 154.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="232284" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38783"/>
     <cge:Term_Ref ObjectID="58145"/>
    <cge:TPSR_Ref TObjectID="38783"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-232293" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2060.000000 153.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="232293" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38789"/>
     <cge:Term_Ref ObjectID="58157"/>
    <cge:TPSR_Ref TObjectID="38789"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-232294" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2060.000000 153.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="232294" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38789"/>
     <cge:Term_Ref ObjectID="58157"/>
    <cge:TPSR_Ref TObjectID="38789"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-232290" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2060.000000 153.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="232290" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38789"/>
     <cge:Term_Ref ObjectID="58157"/>
    <cge:TPSR_Ref TObjectID="38789"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-232240" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2230.000000 223.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="232240" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38768"/>
     <cge:Term_Ref ObjectID="58115"/>
    <cge:TPSR_Ref TObjectID="38768"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-232237" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2230.000000 223.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="232237" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38768"/>
     <cge:Term_Ref ObjectID="58115"/>
    <cge:TPSR_Ref TObjectID="38768"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-232241" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2230.000000 223.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="232241" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38768"/>
     <cge:Term_Ref ObjectID="58115"/>
    <cge:TPSR_Ref TObjectID="38768"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-232170" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2398.000000 -318.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="232170" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38748"/>
     <cge:Term_Ref ObjectID="58076"/>
    <cge:TPSR_Ref TObjectID="38748"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-232171" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2398.000000 -318.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="232171" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38748"/>
     <cge:Term_Ref ObjectID="58076"/>
    <cge:TPSR_Ref TObjectID="38748"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-232172" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2398.000000 -318.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="232172" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38748"/>
     <cge:Term_Ref ObjectID="58076"/>
    <cge:TPSR_Ref TObjectID="38748"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-232176" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2398.000000 -318.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="232176" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38748"/>
     <cge:Term_Ref ObjectID="58076"/>
    <cge:TPSR_Ref TObjectID="38748"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-232173" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2398.000000 -318.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="232173" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38748"/>
     <cge:Term_Ref ObjectID="58076"/>
    <cge:TPSR_Ref TObjectID="38748"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-232162" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 427.000000 -314.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="232162" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38747"/>
     <cge:Term_Ref ObjectID="58075"/>
    <cge:TPSR_Ref TObjectID="38747"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-232163" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 427.000000 -314.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="232163" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38747"/>
     <cge:Term_Ref ObjectID="58075"/>
    <cge:TPSR_Ref TObjectID="38747"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-232164" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 427.000000 -314.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="232164" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38747"/>
     <cge:Term_Ref ObjectID="58075"/>
    <cge:TPSR_Ref TObjectID="38747"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-232168" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 427.000000 -314.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="232168" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38747"/>
     <cge:Term_Ref ObjectID="58075"/>
    <cge:TPSR_Ref TObjectID="38747"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-232165" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 427.000000 -314.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="232165" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38747"/>
     <cge:Term_Ref ObjectID="58075"/>
    <cge:TPSR_Ref TObjectID="38747"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="2" id="ME-232203" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1013.000000 -507.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="232203" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38842"/>
     <cge:Term_Ref ObjectID="58275"/>
    <cge:TPSR_Ref TObjectID="38842"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-232204" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1013.000000 -507.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="232204" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38842"/>
     <cge:Term_Ref ObjectID="58275"/>
    <cge:TPSR_Ref TObjectID="38842"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="2" id="ME-232230" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1755.000000 -488.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="232230" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38841"/>
     <cge:Term_Ref ObjectID="58274"/>
    <cge:TPSR_Ref TObjectID="38841"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-232231" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1755.000000 -488.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="232231" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38841"/>
     <cge:Term_Ref ObjectID="58274"/>
    <cge:TPSR_Ref TObjectID="38841"/></metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_客户变35.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="119" y="-1012"/></g>
   <g href="cx_索引_接线图_客户变110.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="70" y="-1029"/></g>
   <g href="AVC赵家店站.svg" style="fill-opacity:0"><rect height="56" qtmmishow="hidden" stroke="rgb(0,0,0)" width="53" x="285" y="-1018"/></g>
   <g href="35kV赵家店变CX_ZJD_331间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="1385" y="-790"/></g>
   <g href="35kV赵家店变CX_ZJD_031间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="495" y="-142"/></g>
   <g href="35kV赵家店变CX_ZJD_032间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="633" y="-141"/></g>
   <g href="35kV赵家店变CX_ZJD_033间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="780" y="-141"/></g>
   <g href="35kV赵家店变CX_ZJD_035间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1066" y="-139"/></g>
   <g href="35kV赵家店变10kV红山线036间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1203" y="-143"/></g>
   <g href="35kV赵家店变CX_ZJD_041间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="1669" y="-143"/></g>
   <g href="35kV赵家店变CX_ZJD_043间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1943" y="-143"/></g>
   <g href="35kV赵家店变CX_ZJD_042间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="1803" y="-140"/></g>
   <g href="35kV赵家店变10kV大双沟线044间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="2085" y="-144"/></g>
   <g href="35kV赵家店变CX_ZJD_045间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="2274" y="-142"/></g>
   <g href="35kV赵家店变CX_ZJD_012间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="1556" y="-146"/></g>
   <g href="35kV赵家店变GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="21" qtmmishow="hidden" width="78" x="29" y="-614"/></g>
   <g href="35kV赵家店变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="52" x="1136" y="-429"/></g>
   <g href="35kV赵家店变2号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="53" x="1587" y="-428"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35df870" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 375.000000 271.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b7f900" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 359.000000 256.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2aac670" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 369.000000 317.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_375edc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 369.000000 302.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3221fe0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 369.000000 287.000000) translate(0,12)">Uc(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_338ac70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 533.000000 -158.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_380af80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 547.000000 -188.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_362d5f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 523.000000 -173.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35ada90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 934.000000 600.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2caa240" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 948.000000 570.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3359cf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 924.000000 585.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_313e5e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1634.000000 318.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a9bcd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1648.000000 288.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ae8b80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1624.000000 303.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3189760" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1643.000000 629.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3781270" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1657.000000 599.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3356b90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1633.000000 614.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30fcda0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1140.000000 922.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37801e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1154.000000 892.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35dfa20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1130.000000 907.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3358450" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2348.000000 273.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3215c80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2332.000000 258.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3104bd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2342.000000 319.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_338ae10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2342.000000 304.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3138190" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2342.000000 289.000000) translate(0,12)">Uc(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3685ad0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1078.000000 727.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3815860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1062.000000 712.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c99dc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1072.000000 773.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_320fb40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1072.000000 758.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_360e8d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1072.000000 743.000000) translate(0,12)">Uc(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3359a50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 914.000000 491.000000) translate(0,12)">油温（℃）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_362ceb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 914.000000 506.000000) translate(0,12)">档位（档）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b0e830" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1659.000000 473.000000) translate(0,12)">油温（℃）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3620500" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1659.000000 488.000000) translate(0,12)">档位（档）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3644850" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1416.000000 71.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3644ab0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1430.000000 41.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3644cf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1406.000000 56.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39879f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 947.000000 327.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3987c50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 961.000000 297.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3987e90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 937.000000 312.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39892c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 313.000000 157.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39894f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 338.000000 142.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3989730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 344.000000 127.000000) translate(0,12)">Cos:</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_398d690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2157.000000 -222.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_398d8c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2182.000000 -237.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_398db00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2188.000000 -252.000000) translate(0,12)">Cos:</text>
   <metadata/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1660.000000 -820.000000)" xlink:href="#transformer2:shape8_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1660.000000 -820.000000)" xlink:href="#transformer2:shape8_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 930.000000 -0.000000)" xlink:href="#transformer2:shape8_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 930.000000 -0.000000)" xlink:href="#transformer2:shape8_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_ZJD.CX_ZJD_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="58277"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1077.000000 -377.000000)" xlink:href="#transformer2:shape4_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1077.000000 -377.000000)" xlink:href="#transformer2:shape4_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="38842" ObjectName="TF-CX_ZJD.CX_ZJD_1T"/>
    <cge:TPSR_Ref TObjectID="38842"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_ZJD.CX_ZJD_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="58273"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1528.000000 -376.000000)" xlink:href="#transformer2:shape4_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1528.000000 -376.000000)" xlink:href="#transformer2:shape4_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="38841" ObjectName="TF-CX_ZJD.CX_ZJD_2T"/>
    <cge:TPSR_Ref TObjectID="38841"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="119" y="-1012"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="119" y="-1012"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="70" y="-1029"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="70" y="-1029"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <polygon fill="rgb(255,255,255)" points="285,-1018 282,-1021 282,-959 285,-962 285,-1018" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(255,255,255)" points="285,-1018 282,-1021 341,-1021 338,-1018 285,-1018" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(127,127,127)" points="285,-962 282,-959 341,-959 338,-962 285,-962" stroke="rgb(127,127,127)"/>
     <polygon fill="rgb(127,127,127)" points="338,-1018 341,-1021 341,-959 338,-962 338,-1018" stroke="rgb(127,127,127)"/>
     <rect fill="rgb(255,255,255)" height="56" stroke="rgb(255,255,255)" width="53" x="285" y="-1018"/>
     <rect fill="none" height="56" qtmmishow="hidden" stroke="rgb(0,0,0)" width="53" x="285" y="-1018"/>
    </a>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="1385" y="-790"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="1385" y="-790"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="495" y="-142"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="495" y="-142"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="633" y="-141"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="633" y="-141"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="780" y="-141"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="780" y="-141"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1066" y="-139"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1066" y="-139"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1203" y="-143"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1203" y="-143"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="1669" y="-143"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="1669" y="-143"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1943" y="-143"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1943" y="-143"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="1803" y="-140"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="1803" y="-140"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="2085" y="-144"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="2085" y="-144"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="2274" y="-142"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="2274" y="-142"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="1556" y="-146"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="1556" y="-146"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="21" qtmmishow="hidden" width="78" x="29" y="-614"/>
    </a>
   <metadata/><rect fill="white" height="21" opacity="0" stroke="white" transform="" width="78" x="29" y="-614"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="52" x="1136" y="-429"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="52" x="1136" y="-429"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="53" x="1587" y="-428"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="53" x="1587" y="-428"/></g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_338a920">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 1301.000000 -919.000000)" xlink:href="#voltageTransformer:shape135"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35e4ce0">
    <use class="BV-35KV" transform="matrix(1.322581 -0.000000 0.000000 -1.467548 1286.000000 -466.000000)" xlink:href="#voltageTransformer:shape10"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36bf120">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1068.000000 140.000000)" xlink:href="#voltageTransformer:shape54"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_365bfd0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1279.000000 18.000000)" xlink:href="#voltageTransformer:shape106"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_31ee9b0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1805.000000 139.000000)" xlink:href="#voltageTransformer:shape54"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_396bf60">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2357.000000 18.000000)" xlink:href="#voltageTransformer:shape106"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 107.000000 -953.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_ZJD"/>
</svg>