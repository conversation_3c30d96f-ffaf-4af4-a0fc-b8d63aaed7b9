<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-13" aopId="787198" id="thSvg" product="E8000V2" version="1.0" viewBox="3109 -1270 2363 1316">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="36" y1="15" y2="4"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="36" y1="15" y2="4"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="capacitor:shape26">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.370253" x1="2" x2="2" y1="48" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="0.607143" x1="47" x2="47" y1="58" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="47" x2="47" y1="19" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="41" x2="53" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="51" x2="43" y1="17" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="49" x2="46" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="36" x2="21" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="37" x2="21" y1="17" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72286" x1="28" x2="28" y1="17" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="12" x2="12" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="45" x2="45" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.338133" x1="46" x2="12" y1="58" y2="58"/>
    <rect height="23" stroke-width="0.369608" width="12" x="22" y="30"/>
    <rect height="23" stroke-width="0.369608" width="12" x="41" y="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.395152" x1="28" x2="13" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.368819" x1="45" x2="12" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.42985" x1="28" x2="28" y1="91" y2="24"/>
    <polyline arcFlag="1" points="11,39 10,39 9,39 9,40 8,40 8,40 7,41 7,41 6,42 6,42 6,43 6,43 5,44 5,45 5,45 6,46 6,47 6,47 6,48 7,48 7,49 8,49 8,50 9,50 9,50 10,50 11,50 " stroke-width="1"/>
    <polyline arcFlag="1" points="11,28 10,28 9,28 9,28 8,29 8,29 7,29 7,30 6,30 6,31 6,32 6,32 5,33 5,34 5,34 6,35 6,36 6,36 6,37 7,37 7,38 8,38 8,38 9,39 9,39 10,39 11,39 " stroke-width="1"/>
    <polyline arcFlag="1" points="11,18 10,18 9,18 9,18 8,18 8,19 7,19 7,20 6,20 6,21 6,21 6,22 5,23 5,23 5,24 6,25 6,25 6,26 6,26 7,27 7,27 8,28 8,28 9,28 9,29 10,29 11,29 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="11" y1="57" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="12" y1="18" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="27" x2="27" y1="103" y2="111"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="40" x2="28" y1="90" y2="90"/>
    <polyline arcFlag="1" points="27,103 25,103 23,102 22,102 20,101 19,100 17,99 16,97 15,95 15,94 14,92 14,90 14,88 15,86 15,85 16,83 17,82 19,80 20,79 22,78 23,78 25,77 27,77 29,77 31,78 32,78 34,79 35,80 37,82 38,83 39,85 39,86 40,88 40,90 " stroke-width="0.0972"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="13" x2="4" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="5" x2="7" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="3" x2="9" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="lightningRod:shape54">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="6" x2="6" y1="58" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="6" x2="6" y1="8" y2="37"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="7" x2="7" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="8" x2="8" y1="12" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="17" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="5" x2="5" y1="3" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="1" x2="1" y1="5" y2="8"/>
    <rect height="12" stroke-width="1" width="26" x="18" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="59" x2="24" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape105">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="30" y1="40" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="4" x2="13" y1="40" y2="40"/>
    <circle cx="24" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="30" cy="15" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="34" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="13" x2="13" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="20" x2="20" y1="48" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.676705" x1="38" x2="20" y1="40" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="39" x2="39" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="46" x2="46" y1="48" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="58" x2="47" y1="40" y2="40"/>
   </symbol>
   <symbol id="lightningRod:shape57">
    <rect height="28" stroke-width="0.398039" width="12" x="1" y="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="0.897222" x1="7" x2="7" y1="62" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="3" y2="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="9" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.284591" x1="22" x2="36" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.27102" x1="23" x2="36" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="29" y1="54" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="29" y1="54" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.260875" x1="23" x2="35" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.27102" x1="23" x2="36" y1="25" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="29" y1="34" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="27" x2="30" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="25" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="35" x2="23" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="29" y1="9" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="43" y1="31" y2="31"/>
    <circle cx="55" cy="23" fillStyle="0" r="7" stroke-width="1"/>
    <circle cx="49" cy="30" fillStyle="0" r="7" stroke-width="1"/>
    <circle cx="61" cy="30" fillStyle="0" r="7" stroke-width="1"/>
    <circle cx="55" cy="35" fillStyle="0" r="7" stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape55">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="50" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="9" y1="41" y2="41"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="27,39 5,17 5,5 " stroke-width="1"/>
    <rect height="4" stroke-width="1" width="19" x="7" y="26"/>
   </symbol>
   <symbol id="lightningRod:shape164">
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="63" x2="63" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="60" x2="60" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="57" x2="57" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="57" x2="48" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape59">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="72" y2="64"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="12" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="12" y1="23" y2="23"/>
    <circle cx="9" cy="9" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="9" cy="20" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="13" y1="63" y2="63"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="31,61 9,39 9,30 " stroke-width="1"/>
    <rect height="4" stroke-width="1" width="19" x="11" y="48"/>
   </symbol>
   <symbol id="lightningRod:shape50">
    <polyline DF8003:Layer="PUBLIC" points="5,39 0,50 11,50 5,39 5,40 5,39 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.222222" x1="5" x2="5" y1="29" y2="33"/>
    <polyline DF8003:Layer="PUBLIC" points="5,25 0,14 11,14 5,25 5,24 5,25 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="59" y2="51"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape61">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="17" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="14" y2="14"/>
    <circle cx="37" cy="7" fillStyle="0" r="6.5" stroke-width="1"/>
    <circle cx="30" cy="15" fillStyle="0" r="6.5" stroke-width="1"/>
    <circle cx="30" cy="7" fillStyle="0" r="6.5" stroke-width="1"/>
    <circle cx="37" cy="15" fillStyle="0" r="6.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="30" y1="75" y2="22"/>
    <rect height="27" stroke-width="0.416667" width="14" x="23" y="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="29" y1="65" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="65" y2="34"/>
    <rect height="27" stroke-width="0.416667" width="14" x="0" y="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="20" y2="29"/>
   </symbol>
   <symbol id="lightningRod:shape157">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,36 0,46 10,46 5,36 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,24 0,14 10,14 5,24 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="59" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="57" x2="48" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="57" x2="57" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="60" x2="60" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="63" x2="63" y1="5" y2="8"/>
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
   </symbol>
   <symbol id="lightningRod:shape7">
    <polyline DF8003:Layer="PUBLIC" points="1,5 10,17 19,5 1,5 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="20" y2="23"/>
    <polyline DF8003:Layer="PUBLIC" points="19,44 10,32 1,44 19,44 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="29" y2="26"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape8_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="18" y2="43"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="18" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
   </symbol>
   <symbol id="switch2:shape8_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="44" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
   </symbol>
   <symbol id="switch2:shape8-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="18" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="18" y2="43"/>
   </symbol>
   <symbol id="switch2:shape8-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="44" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
   </symbol>
   <symbol id="transformer:shape16_0">
    <ellipse cx="70" cy="46" fillStyle="0" rx="26.5" ry="26" stroke-width="0.540424"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="0" x2="71" y1="29" y2="100"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="80" x2="73" y1="47" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="87" x2="80" y1="54" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="80" x2="80" y1="38" y2="47"/>
   </symbol>
   <symbol id="transformer:shape16_1">
    <ellipse cx="41" cy="61" fillStyle="0" rx="26" ry="26.5" stroke-width="0.540424"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="41" x2="34" y1="71" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="49" x2="41" y1="79" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="41" x2="41" y1="62" y2="71"/>
   </symbol>
   <symbol id="transformer:shape16-2">
    <circle cx="41" cy="30" fillStyle="0" r="26.5" stroke-width="0.55102"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="40" x2="31" y1="32" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="40" x2="49" y1="32" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="31" x2="49" y1="16" y2="16"/>
   </symbol>
   <symbol id="transformer2:shape12_0">
    <circle cx="31" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="32" y1="51" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="26" y2="26"/>
    <polyline DF8003:Layer="PUBLIC" points="32,14 26,27 39,27 32,14 32,15 32,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="42" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="35" y1="80" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="26" y1="81" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="82" y2="87"/>
   </symbol>
   <symbol id="transformer2:shape12_1">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="31,57 6,57 6,28 " stroke-width="1"/>
    <circle cx="31" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="35" y1="55" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="26" y1="56" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="57" y2="62"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2c542a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2c55440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2c55df0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2c56ac0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2c57c60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2c58900" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2c594a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2c59dc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_27d6c70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_27d6c70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2c5c420" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2c5c420" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2c5d200" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2c5d200" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_2c5dc60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2c5f910" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2c60560" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2c61440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2c61d20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2c63710" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2c64180" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2c64a40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2c65200" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2c662e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2c66c60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2c673a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2c67a80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2c69070" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2c69af0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2c6ab40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2c6b790" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2c79aa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2c6d090" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2c6e130" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2c6f650" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1326" width="2373" x="3104" y="-1275"/>
  </g><g id="CircleFilled_Layer">
   <circle DF8003:Layer="PUBLIC" cx="4051" cy="-1185" fill="rgb(0,0,0)" fillStyle="1" r="10" stroke="rgb(255,255,255)" stroke-width="1.75238"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3997" x2="3997" y1="-88" y2="-60"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="4876" x2="4876" y1="-86" y2="-58"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="5044" x2="5044" y1="-89" y2="-61"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,0)" stroke-width="1" x1="5463" x2="5472" y1="-857" y2="-857"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4416.000000 -435.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28230">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3900.000000 -780.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4563" ObjectName="SW-CX_WM.CX_WM_101BK"/>
     <cge:Meas_Ref ObjectId="28230"/>
    <cge:TPSR_Ref TObjectID="4563"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28245">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3899.000000 -440.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4572" ObjectName="SW-CX_WM.CX_WM_001BK"/>
     <cge:Meas_Ref ObjectId="28245"/>
    <cge:TPSR_Ref TObjectID="4572"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4537.000000 -426.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4788.000000 -628.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28240">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4828.000000 -880.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4569" ObjectName="SW-CX_WM.CX_WM_301BK"/>
     <cge:Meas_Ref ObjectId="28240"/>
    <cge:TPSR_Ref TObjectID="4569"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28491">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3720.000000 -957.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4614" ObjectName="SW-CX_WM.CX_WM_191BK"/>
     <cge:Meas_Ref ObjectId="28491"/>
    <cge:TPSR_Ref TObjectID="4614"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28393">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5043.000000 -930.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4598" ObjectName="SW-CX_WM.CX_WM_374BK"/>
     <cge:Meas_Ref ObjectId="28393"/>
    <cge:TPSR_Ref TObjectID="4598"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5037.000000 -738.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4538.000000 -782.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28191">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3998.000000 -958.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4557" ObjectName="SW-CX_WM.CX_WM_192BK"/>
     <cge:Meas_Ref ObjectId="28191"/>
    <cge:TPSR_Ref TObjectID="4557"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28145">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4242.000000 -958.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4551" ObjectName="SW-CX_WM.CX_WM_193BK"/>
     <cge:Meas_Ref ObjectId="28145"/>
    <cge:TPSR_Ref TObjectID="4551"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-55059">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4552.000000 -959.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4616" ObjectName="SW-CX_WM.CX_WM_194BK"/>
     <cge:Meas_Ref ObjectId="55059"/>
    <cge:TPSR_Ref TObjectID="4616"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28373">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4102.000000 -811.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4588" ObjectName="SW-CX_WM.CX_WM_112BK"/>
     <cge:Meas_Ref ObjectId="28373"/>
    <cge:TPSR_Ref TObjectID="4588"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28405">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5045.000000 -800.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4601" ObjectName="SW-CX_WM.CX_WM_375BK"/>
     <cge:Meas_Ref ObjectId="28405"/>
    <cge:TPSR_Ref TObjectID="4601"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5045.000000 -1151.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28381">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5040.000000 -1007.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4595" ObjectName="SW-CX_WM.CX_WM_373BK"/>
     <cge:Meas_Ref ObjectId="28381"/>
    <cge:TPSR_Ref TObjectID="4595"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5044.000000 -693.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4813.000000 -259.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5045.000000 -615.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5044.778846 -541.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5047.778846 -469.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5052.778846 -394.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4981.000000 -262.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4528.000000 -244.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.071429 -0.000000 0.000000 -1.000000 4601.000000 -248.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.071429 -0.000000 0.000000 -1.000000 4676.000000 -248.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4751.000000 -248.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-95793">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3638.000000 -240.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20064" ObjectName="SW-CX_WM.CX_WM_071BK"/>
     <cge:Meas_Ref ObjectId="95793"/>
    <cge:TPSR_Ref TObjectID="20064"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-95808">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.071429 -0.000000 0.000000 -1.000000 3735.000000 -244.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20067" ObjectName="SW-CX_WM.CX_WM_072BK"/>
     <cge:Meas_Ref ObjectId="95808"/>
    <cge:TPSR_Ref TObjectID="20067"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28452">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3934.000000 -256.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4612" ObjectName="SW-CX_WM.CX_WM_074BK"/>
     <cge:Meas_Ref ObjectId="28452"/>
    <cge:TPSR_Ref TObjectID="4612"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28417">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4251.000000 -242.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4604" ObjectName="SW-CX_WM.CX_WM_076BK"/>
     <cge:Meas_Ref ObjectId="28417"/>
    <cge:TPSR_Ref TObjectID="4604"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28431">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4350.000000 -245.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4607" ObjectName="SW-CX_WM.CX_WM_077BK"/>
     <cge:Meas_Ref ObjectId="28431"/>
    <cge:TPSR_Ref TObjectID="4607"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-95823">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.071429 -0.000000 0.000000 -1.000000 3834.000000 -239.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20070" ObjectName="SW-CX_WM.CX_WM_073BK"/>
     <cge:Meas_Ref ObjectId="95823"/>
    <cge:TPSR_Ref TObjectID="20070"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193562">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5040.000000 -1079.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29442" ObjectName="SW-CX_WM.CX_WM_372BK"/>
     <cge:Meas_Ref ObjectId="193562"/>
    <cge:TPSR_Ref TObjectID="29442"/></metadata>
   </g>
  </g><g id="Transformer_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_WM.CX_WM_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="6690"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3868.000000 -545.000000)" xlink:href="#transformer:shape16_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="6692"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3868.000000 -545.000000)" xlink:href="#transformer:shape16_1"/>
    </g>
    <g id="WD-2">
     <metadata>
      <cge:PSR_Ref ObjectId="6694"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3868.000000 -545.000000)" xlink:href="#transformer:shape16-2"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="4617" ObjectName="TF-CX_WM.CX_WM_1T"/>
    <cge:TPSR_Ref TObjectID="4617"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4506.000000 -591.000000)" xlink:href="#transformer:shape16_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4506.000000 -591.000000)" xlink:href="#transformer:shape16_1"/>
    </g>
    <g id="WD-2">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4506.000000 -591.000000)" xlink:href="#transformer:shape16-2"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" runFlow="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4760,-144 4760,-108 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4760,-144 4760,-108 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" runFlow="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4686,-144 4686,-110 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4686,-144 4686,-110 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" runFlow="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4611,-144 4611,-106 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4611,-144 4611,-106 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" runFlow="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4537,-140 4537,-106 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4537,-140 4537,-106 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_DDH" endPointId="0" endStationName="CX_WM" flowDrawDirect="1" flowShape="0" id="AC-110kV.duowan_line" runFlow="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4561,-1181 4561,-1135 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11712" ObjectName="AC-110kV.duowan_line"/>
    <cge:TPSR_Ref TObjectID="11712_SS-13"/></metadata>
   <polyline fill="none" opacity="0" points="4561,-1181 4561,-1135 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_YIZ" endPointId="0" endStationName="CX_WM" flowDrawDirect="1" flowShape="0" id="AC-110kV.yiwan_line" runFlow="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3729,-1180 3729,-1136 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11713" ObjectName="AC-110kV.yiwan_line"/>
    <cge:TPSR_Ref TObjectID="11713_SS-13"/></metadata>
   <polyline fill="none" opacity="0" points="3729,-1180 3729,-1136 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="PAS_T1" endPointId="0" endStationName="CX_WM" flowDrawDirect="1" flowShape="0" id="AC-110kV.yongwandiTwm_line" runFlow="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4251,-1139 4251,-1178 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="14288" ObjectName="AC-110kV.yongwandiTwm_line"/>
    <cge:TPSR_Ref TObjectID="14288_SS-13"/></metadata>
   <polyline fill="none" opacity="0" points="4251,-1139 4251,-1178 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_WM" endPointId="0" endStationName="PAS_T1" flowDrawDirect="1" flowShape="0" id="AC-110kV.yongganwanTwm_line" runFlow="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4007,-1139 4007,-1181 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="18018" ObjectName="AC-110kV.yongganwanTwm_line"/>
    <cge:TPSR_Ref TObjectID="18018_SS-13"/></metadata>
   <polyline fill="none" opacity="0" points="4007,-1139 4007,-1181 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_WM" endPointId="0" endStationName="DY_WB" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_mawanxian" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="5195,-1017 5218,-1017 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="37857" ObjectName="AC-35kV.LN_mawanxian"/>
    <cge:TPSR_Ref TObjectID="37857_SS-13"/></metadata>
   <polyline fill="none" opacity="0" points="5195,-1017 5218,-1017 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_WM" endPointId="0" endStationName="PAS_T1" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_wantamahongTwm" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="5200,-940 5224,-940 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="37860" ObjectName="AC-35kV.LN_wantamahongTwm"/>
    <cge:TPSR_Ref TObjectID="37860_SS-13"/></metadata>
   <polyline fill="none" opacity="0" points="5200,-940 5224,-940 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_WM" endPointId="0" endStationName="YR_ZH" flowDrawDirect="1" flowShape="0" id="AC-35kV.wanzhong_line" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="5204,-810 5234,-810 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="37447" ObjectName="AC-35kV.wanzhong_line"/>
    <cge:TPSR_Ref TObjectID="37447_SS-13"/></metadata>
   <polyline fill="none" opacity="0" points="5204,-810 5234,-810 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_WM" endPointId="0" endStationName="YR_YX" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_wanxing" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="5200,-1089 5224,-1089 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="43718" ObjectName="AC-35kV.LN_wanxing"/>
    <cge:TPSR_Ref TObjectID="43718_SS-13"/></metadata>
   <polyline fill="none" opacity="0" points="5200,-1089 5224,-1089 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-CX_WM.076Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4251.000000 -61.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14293" ObjectName="EC-CX_WM.076Ld"/>
    <cge:TPSR_Ref TObjectID="14293"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_WM.077Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4350.000000 -63.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14294" ObjectName="EC-CX_WM.077Ld"/>
    <cge:TPSR_Ref TObjectID="14294"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_WM.071Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3638.000000 -74.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20078" ObjectName="EC-CX_WM.071Ld"/>
    <cge:TPSR_Ref TObjectID="20078"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_WM.072Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3736.000000 -68.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20079" ObjectName="EC-CX_WM.072Ld"/>
    <cge:TPSR_Ref TObjectID="20079"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_WM.073Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3835.000000 -69.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20080" ObjectName="EC-CX_WM.073Ld"/>
    <cge:TPSR_Ref TObjectID="20080"/></metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1f500f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3972.000000 -753.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2247900" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4428.000000 -582.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_229cd60" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3790.000000 -1053.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21d07c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3790.000000 -996.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2235c30" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3790.000000 -951.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2215d70" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4608.000000 -827.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21b56f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3979.000000 -829.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21b7fb0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4608.000000 -698.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2217790" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4612.000000 -769.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22180d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3923.666667 -923.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22189d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3922.666667 -996.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_226cdf0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4068.000000 -1053.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_222fb90" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4068.000000 -996.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20b55b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4068.000000 -952.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21b1fa0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4312.000000 -1054.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21c52a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4312.000000 -997.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21d42b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4312.000000 -952.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21d7270" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4451.666667 -922.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2186580" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4450.666667 -999.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_225e020" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4622.000000 -1055.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2260fe0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4622.000000 -998.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2176dc0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4622.000000 -952.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2171c90" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4057.000000 -733.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_218d610" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4185.000000 -734.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22054e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4880.000000 -132.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_212bf50" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5048.000000 -135.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2164590" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4001.000000 -134.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23da330" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3972.000000 -669.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_245b510" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4025.000000 -78.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_245f680" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4025.000000 -51.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2465cd0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4904.000000 -76.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24695b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4904.000000 -49.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2470480" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5072.000000 -79.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2473d60" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5072.000000 -52.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-110KV" id="g_2404010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3977,-759 3964,-759 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1f500f0@0" ObjectIDZND0="4568@1" Pin0InfoVect0LinkObjId="SW-28235_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f500f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3977,-759 3964,-759 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2404200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3928,-759 3909,-759 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="4568@0" ObjectIDZND0="4565@x" ObjectIDZND1="4563@x" Pin0InfoVect0LinkObjId="SW-28232_0" Pin0InfoVect1LinkObjId="SW-28230_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28235_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3928,-759 3909,-759 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_24043f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3832,-612 3832,-599 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2264740@0" ObjectIDND1="4617@x" ObjectIDND2="4575@x" ObjectIDZND0="g_22529c0@0" Pin0InfoVect0LinkObjId="g_22529c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2264740_0" Pin1InfoVect1LinkObjId="g_2411690_0" Pin1InfoVect2LinkObjId="SW-28250_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3832,-612 3832,-599 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2404620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3855,-612 3855,-600 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="4617@x" ObjectIDND1="g_22529c0@0" ObjectIDND2="4575@x" ObjectIDZND0="g_2264740@0" Pin0InfoVect0LinkObjId="g_2264740_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2411690_0" Pin1InfoVect1LinkObjId="g_22529c0_0" Pin1InfoVect2LinkObjId="SW-28250_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3855,-612 3855,-600 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2404850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3911,-612 3855,-612 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="4617@x" ObjectIDZND0="g_2264740@0" ObjectIDZND1="g_22529c0@0" ObjectIDZND2="4575@x" Pin0InfoVect0LinkObjId="g_2264740_0" Pin0InfoVect1LinkObjId="g_22529c0_0" Pin0InfoVect2LinkObjId="SW-28250_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2411690_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3911,-612 3855,-612 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2404a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3855,-612 3832,-612 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2264740@0" ObjectIDND1="4617@x" ObjectIDZND0="g_22529c0@0" ObjectIDZND1="4575@x" Pin0InfoVect0LinkObjId="g_22529c0_0" Pin0InfoVect1LinkObjId="SW-28250_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2264740_0" Pin1InfoVect1LinkObjId="g_2411690_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3855,-612 3832,-612 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2404cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3804,-602 3804,-612 3832,-612 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="transformer" ObjectIDND0="4575@0" ObjectIDZND0="g_22529c0@0" ObjectIDZND1="g_2264740@0" ObjectIDZND2="4617@x" Pin0InfoVect0LinkObjId="g_22529c0_0" Pin0InfoVect1LinkObjId="g_2264740_0" Pin0InfoVect2LinkObjId="g_2411690_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28250_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3804,-602 3804,-612 3832,-612 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2404f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4457,-661 4457,-645 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="transformer" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_21fa3b0@0" ObjectIDND2="0@x" ObjectIDZND0="g_2247f30@0" Pin0InfoVect0LinkObjId="g_2247f30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_21fa3b0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4457,-661 4457,-645 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2405170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4434,-611 4434,-599 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_2247900@0" Pin0InfoVect0LinkObjId="g_2247900_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4434,-611 4434,-599 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24053d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4824,-638 4869,-638 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4824,-638 4869,-638 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2405630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4745,-638 4797,-638 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4745,-638 4797,-638 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2405890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4546,-546 4531,-546 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="g_2269730@0" Pin0InfoVect0LinkObjId="g_2269730_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4546,-546 4531,-546 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2405af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4546,-594 4546,-546 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@2" ObjectIDZND0="g_2269730@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_2269730_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_2" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4546,-594 4546,-546 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2405d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3729,-895 3729,-910 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4547@0" ObjectIDZND0="4615@1" Pin0InfoVect0LinkObjId="SW-28492_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3729,-895 3729,-910 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2405fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3729,-946 3729,-955 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="4615@0" ObjectIDZND0="4614@x" ObjectIDZND1="9693@x" Pin0InfoVect0LinkObjId="SW-28491_0" Pin0InfoVect1LinkObjId="SW-54984_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28492_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3729,-946 3729,-955 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2406210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3729,-955 3729,-965 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="4615@x" ObjectIDND1="9693@x" ObjectIDZND0="4614@0" Pin0InfoVect0LinkObjId="SW-28491_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-28492_0" Pin1InfoVect1LinkObjId="SW-54984_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3729,-955 3729,-965 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2406470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3782,-1059 3795,-1059 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="9696@1" ObjectIDZND0="g_229cd60@0" Pin0InfoVect0LinkObjId="g_229cd60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54987_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3782,-1059 3795,-1059 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_24066d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3729,-1002 3747,-1002 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4614@x" ObjectIDND1="9694@x" ObjectIDZND0="9695@0" Pin0InfoVect0LinkObjId="SW-54986_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-28491_0" Pin1InfoVect1LinkObjId="SW-54985_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3729,-1002 3747,-1002 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2406930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3782,-1002 3795,-1002 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="9695@1" ObjectIDZND0="g_21d07c0@0" Pin0InfoVect0LinkObjId="g_21d07c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54986_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3782,-1002 3795,-1002 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2406b90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3729,-992 3729,-1002 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="4614@1" ObjectIDZND0="9695@x" ObjectIDZND1="9694@x" Pin0InfoVect0LinkObjId="SW-54986_0" Pin0InfoVect1LinkObjId="SW-54985_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28491_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3729,-992 3729,-1002 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2406df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3729,-1002 3729,-1014 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4614@x" ObjectIDND1="9695@x" ObjectIDZND0="9694@1" Pin0InfoVect0LinkObjId="SW-54985_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-28491_0" Pin1InfoVect1LinkObjId="SW-54986_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3729,-1002 3729,-1014 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2407050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3729,-957 3747,-957 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4614@x" ObjectIDND1="4615@x" ObjectIDZND0="9693@0" Pin0InfoVect0LinkObjId="SW-54984_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-28491_0" Pin1InfoVect1LinkObjId="SW-28492_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3729,-957 3747,-957 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_24072b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3782,-957 3795,-957 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="9693@1" ObjectIDZND0="g_2235c30@0" Pin0InfoVect0LinkObjId="g_2235c30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54984_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3782,-957 3795,-957 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2407510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4967,-940 4994,-940 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4549@0" ObjectIDZND0="4596@0" Pin0InfoVect0LinkObjId="SW-28391_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2421e30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4967,-940 4994,-940 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2407770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5052,-940 5030,-940 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="4598@1" ObjectIDZND0="4596@1" Pin0InfoVect0LinkObjId="SW-28391_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28393_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5052,-940 5030,-940 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24079d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5100,-940 5079,-940 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="4597@0" ObjectIDZND0="4598@0" Pin0InfoVect0LinkObjId="SW-28393_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28392_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5100,-940 5079,-940 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2407c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4613,-833 4600,-833 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2215d70@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2215d70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4613,-833 4600,-833 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2407e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4564,-833 4547,-833 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4564,-833 4547,-833 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_24080f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3984,-835 3966,-835 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_21b56f0@0" ObjectIDZND0="4566@1" Pin0InfoVect0LinkObjId="SW-28233_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21b56f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3984,-835 3966,-835 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2408350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4613,-704 4600,-704 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_21b7fb0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21b7fb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4613,-704 4600,-704 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24085b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4564,-704 4547,-704 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="transformer" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4564,-704 4547,-704 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2408810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4617,-775 4599,-775 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2217790@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2217790_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4617,-775 4599,-775 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2408a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4547,-882 4547,-896 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="4548@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4547,-882 4547,-896 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2408cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4547,-833 4547,-846 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4547,-833 4547,-846 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2408f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4564,-775 4547,-775 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4564,-775 4547,-775 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2409190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4547,-833 4547,-818 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4547,-833 4547,-818 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24093f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4547,-790 4547,-775 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4547,-790 4547,-775 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2409650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4547,-775 4547,-753 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4547,-775 4547,-753 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24098b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4547,-717 4547,-704 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="transformer" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4547,-717 4547,-704 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2409b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4547,-704 4547,-679 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="transformer" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4547,-704 4547,-679 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2409d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3862,-929 3879,-929 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4547@0" ObjectIDND1="4576@x" ObjectIDZND0="4577@0" Pin0InfoVect0LinkObjId="SW-28320_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="SW-28319_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3862,-929 3879,-929 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2409fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3916,-929 3929,-929 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4577@1" ObjectIDZND0="g_22180d0@0" Pin0InfoVect0LinkObjId="g_22180d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28320_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3916,-929 3929,-929 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_240a230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3862,-895 3862,-928 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="4547@0" ObjectIDZND0="4577@x" ObjectIDZND1="4576@x" Pin0InfoVect0LinkObjId="SW-28320_0" Pin0InfoVect1LinkObjId="SW-28319_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3862,-895 3862,-928 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_240a490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3862,-1002 3881,-1002 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_2218ee0@0" ObjectIDND1="4576@x" ObjectIDZND0="4578@0" Pin0InfoVect0LinkObjId="SW-28321_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2218ee0_0" Pin1InfoVect1LinkObjId="SW-28319_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3862,-1002 3881,-1002 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_240a6f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3915,-1002 3928,-1002 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4578@1" ObjectIDZND0="g_22189d0@0" Pin0InfoVect0LinkObjId="g_22189d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28321_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3915,-1002 3928,-1002 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_240a950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4007,-895 4007,-911 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4547@0" ObjectIDZND0="4558@1" Pin0InfoVect0LinkObjId="SW-28192_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4007,-895 4007,-911 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_240abb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4007,-947 4007,-956 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="4558@0" ObjectIDZND0="4557@x" ObjectIDZND1="4560@x" Pin0InfoVect0LinkObjId="SW-28191_0" Pin0InfoVect1LinkObjId="SW-28194_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28192_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4007,-947 4007,-956 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_240ae10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4007,-956 4007,-966 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="4558@x" ObjectIDND1="4560@x" ObjectIDZND0="4557@0" Pin0InfoVect0LinkObjId="SW-28191_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-28192_0" Pin1InfoVect1LinkObjId="SW-28194_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4007,-956 4007,-966 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_240b070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4060,-1059 4073,-1059 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4561@1" ObjectIDZND0="g_226cdf0@0" Pin0InfoVect0LinkObjId="g_226cdf0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28195_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4060,-1059 4073,-1059 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_240b2d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4007,-1003 4025,-1003 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4557@x" ObjectIDND1="4559@x" ObjectIDZND0="4562@0" Pin0InfoVect0LinkObjId="SW-28196_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-28191_0" Pin1InfoVect1LinkObjId="SW-28193_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4007,-1003 4025,-1003 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_240b530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4060,-1002 4073,-1002 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4562@1" ObjectIDZND0="g_222fb90@0" Pin0InfoVect0LinkObjId="g_222fb90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28196_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4060,-1002 4073,-1002 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_240b790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4007,-993 4007,-1003 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="4557@1" ObjectIDZND0="4562@x" ObjectIDZND1="4559@x" Pin0InfoVect0LinkObjId="SW-28196_0" Pin0InfoVect1LinkObjId="SW-28193_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28191_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4007,-993 4007,-1003 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_240b9f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4007,-1003 4007,-1015 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4557@x" ObjectIDND1="4562@x" ObjectIDZND0="4559@1" Pin0InfoVect0LinkObjId="SW-28193_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-28191_0" Pin1InfoVect1LinkObjId="SW-28196_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4007,-1003 4007,-1015 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_240bc50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4007,-958 4025,-958 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4557@x" ObjectIDND1="4558@x" ObjectIDZND0="4560@0" Pin0InfoVect0LinkObjId="SW-28194_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-28191_0" Pin1InfoVect1LinkObjId="SW-28192_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4007,-958 4025,-958 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_240beb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4060,-958 4073,-958 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4560@1" ObjectIDZND0="g_20b55b0@0" Pin0InfoVect0LinkObjId="g_20b55b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28194_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4060,-958 4073,-958 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_240c110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4251,-896 4251,-911 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4548@0" ObjectIDZND0="4552@1" Pin0InfoVect0LinkObjId="SW-28146_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2408a70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4251,-896 4251,-911 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_240c370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4251,-947 4251,-956 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="4552@0" ObjectIDZND0="4551@x" ObjectIDZND1="4554@x" Pin0InfoVect0LinkObjId="SW-28145_0" Pin0InfoVect1LinkObjId="SW-28148_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28146_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4251,-947 4251,-956 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_240c5d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4251,-956 4251,-966 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="4552@x" ObjectIDND1="4554@x" ObjectIDZND0="4551@0" Pin0InfoVect0LinkObjId="SW-28145_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-28146_0" Pin1InfoVect1LinkObjId="SW-28148_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4251,-956 4251,-966 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_240c830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4304,-1060 4317,-1060 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4555@1" ObjectIDZND0="g_21b1fa0@0" Pin0InfoVect0LinkObjId="g_21b1fa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28149_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4304,-1060 4317,-1060 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_240ca90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4251,-1003 4268,-1003 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4551@x" ObjectIDND1="4553@x" ObjectIDZND0="4556@0" Pin0InfoVect0LinkObjId="SW-28150_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-28145_0" Pin1InfoVect1LinkObjId="SW-28147_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4251,-1003 4268,-1003 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_240ccf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4304,-1003 4317,-1003 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4556@1" ObjectIDZND0="g_21c52a0@0" Pin0InfoVect0LinkObjId="g_21c52a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28150_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4304,-1003 4317,-1003 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_240cf50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4251,-993 4251,-1003 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="4551@1" ObjectIDZND0="4556@x" ObjectIDZND1="4553@x" Pin0InfoVect0LinkObjId="SW-28150_0" Pin0InfoVect1LinkObjId="SW-28147_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28145_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4251,-993 4251,-1003 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_240d1b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4251,-1003 4251,-1015 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4551@x" ObjectIDND1="4556@x" ObjectIDZND0="4553@1" Pin0InfoVect0LinkObjId="SW-28147_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-28145_0" Pin1InfoVect1LinkObjId="SW-28150_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4251,-1003 4251,-1015 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_240d410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4251,-958 4268,-958 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4551@x" ObjectIDND1="4552@x" ObjectIDZND0="4554@0" Pin0InfoVect0LinkObjId="SW-28148_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-28145_0" Pin1InfoVect1LinkObjId="SW-28146_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4251,-958 4268,-958 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_240d670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4304,-958 4317,-958 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4554@1" ObjectIDZND0="g_21d42b0@0" Pin0InfoVect0LinkObjId="g_21d42b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28148_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4304,-958 4317,-958 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_240d8d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4390,-928 4408,-928 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4548@0" ObjectIDND1="4579@x" ObjectIDZND0="4580@0" Pin0InfoVect0LinkObjId="SW-28324_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2408a70_0" Pin1InfoVect1LinkObjId="SW-28323_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4390,-928 4408,-928 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_240db30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4444,-928 4457,-928 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4580@1" ObjectIDZND0="g_21d7270@0" Pin0InfoVect0LinkObjId="g_21d7270_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28324_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4444,-928 4457,-928 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_240dd90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4390,-896 4390,-928 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="4548@0" ObjectIDZND0="4580@x" ObjectIDZND1="4579@x" Pin0InfoVect0LinkObjId="SW-28324_0" Pin0InfoVect1LinkObjId="SW-28323_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2408a70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4390,-896 4390,-928 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_240dff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4390,-1005 4407,-1005 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_2187010@0" ObjectIDND1="4579@x" ObjectIDZND0="4581@0" Pin0InfoVect0LinkObjId="SW-28325_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2187010_0" Pin1InfoVect1LinkObjId="SW-28323_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4390,-1005 4407,-1005 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_240e250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4443,-1005 4456,-1005 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4581@1" ObjectIDZND0="g_2186580@0" Pin0InfoVect0LinkObjId="g_2186580_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28325_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4443,-1005 4456,-1005 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_240e4b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4561,-896 4561,-912 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4548@0" ObjectIDZND0="9697@1" Pin0InfoVect0LinkObjId="SW-55060_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2408a70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4561,-896 4561,-912 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_240e710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4561,-948 4561,-957 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="9697@0" ObjectIDZND0="4616@x" ObjectIDZND1="9699@x" Pin0InfoVect0LinkObjId="SW-55059_0" Pin0InfoVect1LinkObjId="SW-55062_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-55060_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4561,-948 4561,-957 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_240e970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4561,-957 4561,-967 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="9697@x" ObjectIDND1="9699@x" ObjectIDZND0="4616@0" Pin0InfoVect0LinkObjId="SW-55059_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-55060_0" Pin1InfoVect1LinkObjId="SW-55062_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4561,-957 4561,-967 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_240ebd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4614,-1061 4627,-1061 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="9700@1" ObjectIDZND0="g_225e020@0" Pin0InfoVect0LinkObjId="g_225e020_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-55063_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4614,-1061 4627,-1061 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_240ee30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4561,-1004 4578,-1004 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4616@x" ObjectIDND1="9698@x" ObjectIDZND0="9701@0" Pin0InfoVect0LinkObjId="SW-55064_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-55059_0" Pin1InfoVect1LinkObjId="SW-55061_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4561,-1004 4578,-1004 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_240f090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4614,-1004 4627,-1004 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="9701@1" ObjectIDZND0="g_2260fe0@0" Pin0InfoVect0LinkObjId="g_2260fe0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-55064_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4614,-1004 4627,-1004 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_240f2f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4561,-994 4561,-1004 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="4616@1" ObjectIDZND0="9701@x" ObjectIDZND1="9698@x" Pin0InfoVect0LinkObjId="SW-55064_0" Pin0InfoVect1LinkObjId="SW-55061_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-55059_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4561,-994 4561,-1004 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_240f550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4561,-1004 4561,-1016 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4616@x" ObjectIDND1="9701@x" ObjectIDZND0="9698@1" Pin0InfoVect0LinkObjId="SW-55061_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-55059_0" Pin1InfoVect1LinkObjId="SW-55064_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4561,-1004 4561,-1016 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_240f7b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4561,-958 4578,-958 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4616@x" ObjectIDND1="9697@x" ObjectIDZND0="9699@0" Pin0InfoVect0LinkObjId="SW-55062_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-55059_0" Pin1InfoVect1LinkObjId="SW-55060_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4561,-958 4578,-958 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_240fa10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4614,-958 4627,-958 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="9699@1" ObjectIDZND0="g_2176dc0@0" Pin0InfoVect0LinkObjId="g_2176dc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-55062_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4614,-958 4627,-958 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_240fc70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4063,-895 4063,-872 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4547@0" ObjectIDZND0="4589@0" Pin0InfoVect0LinkObjId="SW-28374_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4063,-895 4063,-872 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_240fed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4063,-768 4063,-750 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4591@1" ObjectIDZND0="g_2171c90@0" Pin0InfoVect0LinkObjId="g_2171c90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28376_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4063,-768 4063,-750 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2410130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4191,-896 4191,-873 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4548@0" ObjectIDZND0="4590@0" Pin0InfoVect0LinkObjId="SW-28375_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2408a70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4191,-896 4191,-873 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2410390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4191,-769 4191,-751 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4592@1" ObjectIDZND0="g_218d610@0" Pin0InfoVect0LinkObjId="g_218d610_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28377_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4191,-769 4191,-751 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_24105f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4063,-836 4063,-821 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="4589@1" ObjectIDZND0="4591@x" ObjectIDZND1="4588@x" Pin0InfoVect0LinkObjId="SW-28376_0" Pin0InfoVect1LinkObjId="SW-28373_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28374_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4063,-836 4063,-821 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2410850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4063,-821 4063,-804 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="4589@x" ObjectIDND1="4588@x" ObjectIDZND0="4591@0" Pin0InfoVect0LinkObjId="SW-28376_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-28374_0" Pin1InfoVect1LinkObjId="SW-28373_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4063,-821 4063,-804 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2410ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4191,-821 4191,-805 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4588@x" ObjectIDND1="4590@x" ObjectIDZND0="4592@0" Pin0InfoVect0LinkObjId="SW-28377_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-28373_0" Pin1InfoVect1LinkObjId="SW-28375_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4191,-821 4191,-805 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2410d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4457,-661 4434,-661 4434,-647 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="transformer" EndDevType0="switch" ObjectIDND0="g_2247f30@0" ObjectIDND1="g_21fa3b0@0" ObjectIDND2="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2247f30_0" Pin1InfoVect1LinkObjId="g_21fa3b0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4457,-661 4434,-661 4434,-647 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2410f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4063,-821 4111,-821 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="4591@x" ObjectIDND1="4589@x" ObjectIDZND0="4588@1" Pin0InfoVect0LinkObjId="SW-28373_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-28376_0" Pin1InfoVect1LinkObjId="SW-28374_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4063,-821 4111,-821 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_24111d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4138,-821 4191,-821 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="4588@0" ObjectIDZND0="4592@x" ObjectIDZND1="4590@x" Pin0InfoVect0LinkObjId="SW-28377_0" Pin0InfoVect1LinkObjId="SW-28375_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28373_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4138,-821 4191,-821 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2411430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3908,-536 3924,-536 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="4617@x" ObjectIDND1="4574@x" ObjectIDZND0="g_23fd5f0@0" Pin0InfoVect0LinkObjId="g_23fd5f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2411690_0" Pin1InfoVect1LinkObjId="SW-28247_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3908,-536 3924,-536 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2411690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3908,-522 3908,-536 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer" EndDevType1="lightningRod" ObjectIDND0="4574@0" ObjectIDZND0="4617@x" ObjectIDZND1="g_23fd5f0@0" Pin0InfoVect0LinkObjId="g_24118f0_0" Pin0InfoVect1LinkObjId="g_23fd5f0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28247_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3908,-522 3908,-536 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24118f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3908,-536 3908,-550 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="transformer" ObjectIDND0="4574@x" ObjectIDND1="g_23fd5f0@0" ObjectIDZND0="4617@2" Pin0InfoVect0LinkObjId="g_2411690_2" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-28247_0" Pin1InfoVect1LinkObjId="g_23fd5f0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3908,-536 3908,-550 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2411b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4967,-810 4996,-810 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4549@0" ObjectIDZND0="4599@0" Pin0InfoVect0LinkObjId="SW-28403_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2421e30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4967,-810 4996,-810 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2411db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5054,-810 5032,-810 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="4601@1" ObjectIDZND0="4599@1" Pin0InfoVect0LinkObjId="SW-28403_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28405_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5054,-810 5032,-810 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2412010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5102,-810 5081,-810 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="4600@0" ObjectIDZND0="4601@0" Pin0InfoVect0LinkObjId="SW-28405_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28404_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5102,-810 5081,-810 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2412270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5227,-870 5262,-870 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_2219250@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2219250_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5227,-870 5262,-870 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24124d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4967,-1161 4996,-1161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4549@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2421e30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4967,-1161 4996,-1161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2412730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5054,-1161 5032,-1161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5054,-1161 5032,-1161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2412990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5102,-1161 5081,-1161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5102,-1161 5081,-1161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2412bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4967,-1017 4991,-1017 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4549@0" ObjectIDZND0="4593@0" Pin0InfoVect0LinkObjId="SW-28379_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2421e30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4967,-1017 4991,-1017 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2412e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5049,-1017 5027,-1017 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="4595@1" ObjectIDZND0="4593@1" Pin0InfoVect0LinkObjId="SW-28379_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28381_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5049,-1017 5027,-1017 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24130b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5097,-1017 5076,-1017 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="4594@0" ObjectIDZND0="4595@0" Pin0InfoVect0LinkObjId="SW-28381_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28380_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5097,-1017 5076,-1017 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2413310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4968,-703 4995,-703 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4968,-703 4995,-703 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2413570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5053,-703 5031,-703 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5053,-703 5031,-703 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24137d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5101,-703 5080,-703 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5101,-703 5080,-703 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2413a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4822,-344 4822,-358 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4822,-344 4822,-358 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2413c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4822,-138 4835,-138 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_22022c0@0" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_22022c0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4822,-138 4835,-138 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2413ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4871,-138 4885,-138 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_22054e0@0" Pin0InfoVect0LinkObjId="g_22054e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4871,-138 4885,-138 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2414150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4822,-294 4822,-308 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4822,-294 4822,-308 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24143b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4822,-252 4822,-267 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4822,-252 4822,-267 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2414610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4822,-216 4822,-204 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_22022c0@0" Pin0InfoVect0LinkObjId="g_22022c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4822,-216 4822,-204 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2414870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4822,-151 4822,-138 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_22022c0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22022c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4822,-151 4822,-138 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2414ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4822,-138 4822,-127 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_22022c0@0" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_22022c0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4822,-138 4822,-127 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2414d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4546,-525 4546,-546 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer" EndDevType1="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_2269730@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_2269730_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4546,-525 4546,-546 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2414f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3908,-475 3908,-486 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="4572@1" ObjectIDZND0="4574@1" Pin0InfoVect0LinkObjId="SW-28247_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28245_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3908,-475 3908,-486 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24151f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3908,-357 3908,-399 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4550@0" ObjectIDZND0="4573@1" Pin0InfoVect0LinkObjId="SW-28246_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_241dbb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3908,-357 3908,-399 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2415450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3908,-435 3908,-448 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="4573@0" ObjectIDZND0="4572@0" Pin0InfoVect0LinkObjId="SW-28245_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28246_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3908,-435 3908,-448 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24156b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4968,-625 4996,-625 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4968,-625 4996,-625 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2415910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5054,-625 5032,-625 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5054,-625 5032,-625 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2415b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5102,-625 5081,-625 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5102,-625 5081,-625 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2415dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4968,-551 4996,-551 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4968,-551 4996,-551 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2416030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5054,-551 5032,-551 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5054,-551 5032,-551 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2416290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5102,-551 5081,-551 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5102,-551 5081,-551 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24164f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4968,-479 4999,-479 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4968,-479 4999,-479 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2416750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5057,-479 5035,-479 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5057,-479 5035,-479 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24169b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5105,-479 5084,-479 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5105,-479 5084,-479 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2416c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4968,-404 5004,-404 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4968,-404 5004,-404 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2416e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5062,-404 5040,-404 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5062,-404 5040,-404 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24170d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5110,-404 5089,-404 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5110,-404 5089,-404 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2417330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4990,-347 4990,-358 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4990,-347 4990,-358 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2417590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4990,-141 5003,-141 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_2128d30@0" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2128d30_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4990,-141 5003,-141 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24177f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5039,-141 5053,-141 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_212bf50@0" Pin0InfoVect0LinkObjId="g_212bf50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5039,-141 5053,-141 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2417a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4990,-297 4990,-311 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4990,-297 4990,-311 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2417cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4990,-255 4990,-270 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4990,-255 4990,-270 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2417f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4990,-219 4990,-207 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_2128d30@0" Pin0InfoVect0LinkObjId="g_2128d30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4990,-219 4990,-207 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2418170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4990,-156 4990,-141 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2128d30@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2128d30_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4990,-156 4990,-141 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24183d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4990,-141 4990,-130 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_2128d30@0" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2128d30_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4990,-141 4990,-130 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2418630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4537,-339 4537,-358 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4537,-339 4537,-358 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2418890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4537,-240 4537,-252 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_2133870@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2133870_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4537,-240 4537,-252 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2418af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4537,-176 4537,-187 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_2133870@1" Pin0InfoVect0LinkObjId="g_2133870_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4537,-176 4537,-187 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2418d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4537,-303 4537,-279 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4537,-303 4537,-279 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2418fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4611,-343 4611,-358 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4611,-343 4611,-358 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2419210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4611,-244 4611,-256 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_213b490@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_213b490_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4611,-244 4611,-256 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2419470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4611,-180 4611,-191 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_213b490@1" Pin0InfoVect0LinkObjId="g_213b490_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4611,-180 4611,-191 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24196d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4611,-307 4611,-283 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4611,-307 4611,-283 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2419930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4686,-343 4687,-358 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4686,-343 4687,-358 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2419b90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4686,-244 4686,-256 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_2143070@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2143070_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4686,-244 4686,-256 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2419df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4686,-180 4686,-191 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_2143070@1" Pin0InfoVect0LinkObjId="g_2143070_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4686,-180 4686,-191 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_241a050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4686,-307 4686,-283 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4686,-307 4686,-283 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_241a2b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4760,-343 4760,-358 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4760,-343 4760,-358 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_241a510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4760,-244 4760,-256 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_214ac50@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_214ac50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4760,-244 4760,-256 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_241a770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4760,-180 4760,-191 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_214ac50@1" Pin0InfoVect0LinkObjId="g_214ac50_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4760,-180 4760,-191 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_241a9d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4760,-307 4760,-283 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4760,-307 4760,-283 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_241ac30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5128,-358 5128,-342 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5128,-358 5128,-342 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_241ae90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5128,-306 5128,-291 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_2308aa0@0" Pin0InfoVect0LinkObjId="g_2308aa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5128,-306 5128,-291 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_241b0f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5128,-260 5128,-238 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2308aa0@1" ObjectIDZND0="g_2309320@0" Pin0InfoVect0LinkObjId="g_2309320_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2308aa0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5128,-260 5128,-238 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_241b350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5128,-185 5128,-168 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_2309320@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2309320_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5128,-185 5128,-168 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_241b5b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5247,-358 5247,-338 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5247,-358 5247,-338 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_241b810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5247,-302 5247,-280 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_230bd40@0" Pin0InfoVect0LinkObjId="g_230bd40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5247,-302 5247,-280 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_241ba70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4967,-1140 4947,-1140 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4549@0" ObjectIDZND0="4583@1" Pin0InfoVect0LinkObjId="SW-28334_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2421e30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4967,-1140 4947,-1140 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_241bcd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4911,-1140 4889,-1140 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="4583@0" ObjectIDZND0="g_230fc50@0" Pin0InfoVect0LinkObjId="g_230fc50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28334_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4911,-1140 4889,-1140 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_241bf30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4968,-442 4937,-442 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4968,-442 4937,-442 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_241c190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4903,-442 4878,-442 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_2311500@0" Pin0InfoVect0LinkObjId="g_2311500_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4903,-442 4878,-442 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_241c3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4379,-414 4379,-425 4425,-425 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="4587@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28361_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4379,-414 4379,-425 4425,-425 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_241c650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4497,-358 4497,-377 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4497,-358 4497,-377 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_241c8b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4497,-413 4497,-425 4452,-425 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4497,-413 4497,-425 4452,-425 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_241cb10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4191,-837 4191,-821 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="4590@1" ObjectIDZND0="4588@x" ObjectIDZND1="4592@x" Pin0InfoVect0LinkObjId="SW-28373_0" Pin0InfoVect1LinkObjId="SW-28377_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28375_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4191,-837 4191,-821 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_241cd70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4905,-638 4968,-638 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4905,-638 4968,-638 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_241cfd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4546,-358 4546,-379 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4546,-358 4546,-379 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_241d230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4546,-415 4546,-434 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4546,-415 4546,-434 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_241d490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4546,-461 4546,-489 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4546,-461 4546,-489 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_241d6f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3647,-236 3647,-248 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_2151c40@0" ObjectIDZND0="20064@0" Pin0InfoVect0LinkObjId="SW-95793_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2151c40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3647,-236 3647,-248 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_241d950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3647,-172 3647,-183 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="20063@0" ObjectIDZND0="g_2151c40@1" Pin0InfoVect0LinkObjId="g_2151c40_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-95792_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3647,-172 3647,-183 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_241dbb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3745,-339 3745,-357 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="20065@0" ObjectIDZND0="4550@0" Pin0InfoVect0LinkObjId="g_241f370_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-95806_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3745,-339 3745,-357 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_241de10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3745,-240 3745,-252 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_2159940@0" ObjectIDZND0="20067@0" Pin0InfoVect0LinkObjId="SW-95808_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2159940_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3745,-240 3745,-252 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_241e070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3745,-176 3745,-187 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="20066@0" ObjectIDZND0="g_2159940@1" Pin0InfoVect0LinkObjId="g_2159940_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-95807_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3745,-176 3745,-187 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_241e2d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3745,-303 3745,-279 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="20065@1" ObjectIDZND0="20067@1" Pin0InfoVect0LinkObjId="SW-95808_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-95806_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3745,-303 3745,-279 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_241e530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3647,-357 3647,-335 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4550@0" ObjectIDZND0="20062@0" Pin0InfoVect0LinkObjId="SW-95791_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_241dbb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3647,-357 3647,-335 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_241e790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3647,-299 3647,-275 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="20062@1" ObjectIDZND0="20064@1" Pin0InfoVect0LinkObjId="SW-95793_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-95791_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3647,-299 3647,-275 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_241e9f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3992,-140 4006,-140 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4613@1" ObjectIDZND0="g_2164590@0" Pin0InfoVect0LinkObjId="g_2164590_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28455_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3992,-140 4006,-140 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_241ec50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3943,-291 3943,-305 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="4612@1" ObjectIDZND0="4608@1" Pin0InfoVect0LinkObjId="SW-28448_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28452_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3943,-291 3943,-305 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_241eeb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3943,-249 3943,-264 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="4611@0" ObjectIDZND0="4612@0" Pin0InfoVect0LinkObjId="SW-28452_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28451_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3943,-249 3943,-264 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_241f110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3943,-213 3943,-202 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="4611@1" ObjectIDZND0="g_2161640@1" Pin0InfoVect0LinkObjId="g_2161640_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28451_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3943,-213 3943,-202 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_241f370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3943,-341 3943,-357 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="4608@0" ObjectIDZND0="4550@0" Pin0InfoVect0LinkObjId="g_241dbb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28448_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3943,-341 3943,-357 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_241f5d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4082,-357 4082,-341 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4550@0" ObjectIDZND0="4586@0" Pin0InfoVect0LinkObjId="SW-28360_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_241dbb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4082,-357 4082,-341 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_241f830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4082,-305 4082,-289 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="4586@1" ObjectIDZND0="g_21676e0@0" Pin0InfoVect0LinkObjId="g_21676e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28360_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4082,-305 4082,-289 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_241fa90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4082,-258 4082,-240 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_21676e0@1" ObjectIDZND0="g_2167f60@1" Pin0InfoVect0LinkObjId="g_2167f60_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21676e0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4082,-258 4082,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_241fcf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4082,-187 4082,-167 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_2167f60@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2167f60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4082,-187 4082,-167 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_241ff50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4170,-357 4170,-338 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4550@0" ObjectIDZND0="4585@0" Pin0InfoVect0LinkObjId="SW-28358_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_241dbb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4170,-357 4170,-338 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24201b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4170,-302 4170,-280 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="4585@1" ObjectIDZND0="g_216a650@0" Pin0InfoVect0LinkObjId="g_216a650_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28358_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4170,-302 4170,-280 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2420410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4260,-337 4260,-357 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="4602@0" ObjectIDZND0="4550@0" Pin0InfoVect0LinkObjId="g_241dbb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28415_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4260,-337 4260,-357 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2420670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4260,-301 4260,-277 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="4602@1" ObjectIDZND0="4604@1" Pin0InfoVect0LinkObjId="SW-28417_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28415_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4260,-301 4260,-277 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24208d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4359,-304 4359,-280 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="4605@1" ObjectIDZND0="4607@1" Pin0InfoVect0LinkObjId="SW-28431_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28429_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4359,-304 4359,-280 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2420b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4359,-340 4359,-357 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="4605@0" ObjectIDZND0="4550@0" Pin0InfoVect0LinkObjId="g_241dbb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28429_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4359,-340 4359,-357 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2420d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4480,-661 4547,-661 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="transformer" ObjectIDND0="g_21fa3b0@0" ObjectIDND1="0@x" ObjectIDND2="g_2247f30@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_21fa3b0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_2247f30_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4480,-661 4547,-661 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2420ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4480,-645 4480,-661 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_21fa3b0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="g_2247f30@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_2247f30_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21fa3b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4480,-645 4480,-661 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2421250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4480,-661 4457,-661 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_21fa3b0@0" ObjectIDND1="0@x" ObjectIDZND0="0@x" ObjectIDZND1="g_2247f30@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_2247f30_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_21fa3b0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4480,-661 4457,-661 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24214b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4602,-638 4709,-638 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4602,-638 4709,-638 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2421710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3964,-592 4094,-592 4094,-532 4765,-532 4765,-890 4784,-890 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="switch" ObjectIDND0="4617@0" ObjectIDZND0="4571@0" Pin0InfoVect0LinkObjId="SW-28242_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2411690_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3964,-592 4094,-592 4094,-532 4765,-532 4765,-890 4784,-890 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2421970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4820,-890 4837,-890 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="4571@1" ObjectIDZND0="4569@1" Pin0InfoVect0LinkObjId="SW-28240_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28242_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4820,-890 4837,-890 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2421bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4864,-890 4881,-890 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="4569@0" ObjectIDZND0="4570@0" Pin0InfoVect0LinkObjId="SW-28241_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28240_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4864,-890 4881,-890 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2421e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4917,-890 4967,-890 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="4570@1" ObjectIDZND0="4549@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28241_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4917,-890 4967,-890 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2422090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4967,-790 4989,-790 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4549@0" ObjectIDZND0="4584@0" Pin0InfoVect0LinkObjId="SW-28336_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2421e30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4967,-790 4989,-790 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24222f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5025,-790 5046,-790 5046,-773 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="4584@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28336_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5025,-790 5046,-790 5046,-773 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2422550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4968,-726 4990,-726 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4968,-726 4990,-726 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24227b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5026,-726 5046,-726 5046,-746 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5026,-726 5046,-726 5046,-746 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2422a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3862,-1028 3862,-1002 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2218ee0@0" ObjectIDZND0="4578@x" ObjectIDZND1="4576@x" Pin0InfoVect0LinkObjId="SW-28321_0" Pin0InfoVect1LinkObjId="SW-28319_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2218ee0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3862,-1028 3862,-1002 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2422c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3862,-928 3862,-946 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4547@0" ObjectIDND1="4577@x" ObjectIDZND0="4576@1" Pin0InfoVect0LinkObjId="SW-28319_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="SW-28320_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3862,-928 3862,-946 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2422ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3862,-982 3862,-1002 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="4576@0" ObjectIDZND0="g_2218ee0@0" ObjectIDZND1="4578@x" Pin0InfoVect0LinkObjId="g_2218ee0_0" Pin0InfoVect1LinkObjId="SW-28321_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28319_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3862,-982 3862,-1002 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2423130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4390,-1005 4390,-1027 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="4581@x" ObjectIDND1="4579@x" ObjectIDZND0="g_2187010@0" Pin0InfoVect0LinkObjId="g_2187010_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-28325_0" Pin1InfoVect1LinkObjId="SW-28323_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4390,-1005 4390,-1027 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2423390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4390,-928 4390,-950 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4548@0" ObjectIDND1="4580@x" ObjectIDZND0="4579@1" Pin0InfoVect0LinkObjId="SW-28323_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2408a70_0" Pin1InfoVect1LinkObjId="SW-28324_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4390,-928 4390,-950 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_24235f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4390,-986 4390,-1005 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="4579@0" ObjectIDZND0="g_2187010@0" ObjectIDZND1="4581@x" Pin0InfoVect0LinkObjId="g_2187010_0" Pin0InfoVect1LinkObjId="SW-28325_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28323_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4390,-986 4390,-1005 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2423850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3909,-759 3909,-788 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="4565@x" ObjectIDND1="4568@x" ObjectIDZND0="4563@0" Pin0InfoVect0LinkObjId="SW-28230_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-28232_0" Pin1InfoVect1LinkObjId="SW-28235_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3909,-759 3909,-788 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2423ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3909,-759 3909,-737 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4563@x" ObjectIDND1="4568@x" ObjectIDZND0="4565@0" Pin0InfoVect0LinkObjId="SW-28232_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-28230_0" Pin1InfoVect1LinkObjId="SW-28235_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3909,-759 3909,-737 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2423d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3977,-675 3964,-675 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_23da330@0" ObjectIDZND0="4567@1" Pin0InfoVect0LinkObjId="SW-28234_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_23da330_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3977,-675 3964,-675 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2423f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3928,-675 3909,-675 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="transformer" ObjectIDND0="4567@0" ObjectIDZND0="4565@x" ObjectIDZND1="4617@x" Pin0InfoVect0LinkObjId="SW-28232_0" Pin0InfoVect1LinkObjId="g_2411690_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28234_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3928,-675 3909,-675 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_24241d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3909,-701 3909,-675 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="transformer" ObjectIDND0="4565@1" ObjectIDZND0="4567@x" ObjectIDZND1="4617@x" Pin0InfoVect0LinkObjId="SW-28234_0" Pin0InfoVect1LinkObjId="g_2411690_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28232_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3909,-701 3909,-675 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2424430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3909,-675 3909,-633 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="transformer" ObjectIDND0="4565@x" ObjectIDND1="4567@x" ObjectIDZND0="4617@1" Pin0InfoVect0LinkObjId="g_2411690_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-28232_0" Pin1InfoVect1LinkObjId="SW-28234_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3909,-675 3909,-633 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2424690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3943,-149 3943,-140 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2161640@0" ObjectIDZND0="4613@x" ObjectIDZND1="4609@x" Pin0InfoVect0LinkObjId="SW-28455_0" Pin0InfoVect1LinkObjId="SW-28449_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2161640_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3943,-149 3943,-140 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24248f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3943,-140 3956,-140 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_2161640@0" ObjectIDND1="4609@x" ObjectIDZND0="4613@0" Pin0InfoVect0LinkObjId="SW-28455_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2161640_0" Pin1InfoVect1LinkObjId="SW-28449_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3943,-140 3956,-140 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2424b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3943,-129 3943,-140 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="4609@0" ObjectIDZND0="4613@x" ObjectIDZND1="g_2161640@0" Pin0InfoVect0LinkObjId="SW-28455_0" Pin0InfoVect1LinkObjId="g_2161640_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28449_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3943,-129 3943,-140 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2424db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5157,-810 5157,-812 5157,-835 5172,-835 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="powerLine" EndDevType0="lightningRod" ObjectIDND0="g_211a320@0" ObjectIDND1="4600@x" ObjectIDND2="37447@1" ObjectIDZND0="g_211b3b0@0" Pin0InfoVect0LinkObjId="g_211b3b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_211a320_0" Pin1InfoVect1LinkObjId="SW-28404_0" Pin1InfoVect2LinkObjId="g_2428fc0_1" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5157,-810 5157,-812 5157,-835 5172,-835 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2425000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5182,-780 5157,-780 5157,-810 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="powerLine" ObjectIDND0="g_211a320@0" ObjectIDZND0="g_211b3b0@0" ObjectIDZND1="4600@x" ObjectIDZND2="37447@1" Pin0InfoVect0LinkObjId="g_211b3b0_0" Pin0InfoVect1LinkObjId="SW-28404_0" Pin0InfoVect2LinkObjId="g_2428fc0_1" Pin0Num="1" Pin1InfoVect0LinkObjId="g_211a320_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5182,-780 5157,-780 5157,-810 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2425240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5157,-810 5138,-810 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" ObjectIDND0="g_211a320@0" ObjectIDND1="g_211b3b0@0" ObjectIDND2="37447@1" ObjectIDZND0="4600@1" Pin0InfoVect0LinkObjId="SW-28404_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_211a320_0" Pin1InfoVect1LinkObjId="g_211b3b0_0" Pin1InfoVect2LinkObjId="g_2428fc0_1" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5157,-810 5138,-810 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24254a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5177,-987 5152,-987 5152,-1017 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="g_21c0260@0" ObjectIDZND0="4594@x" ObjectIDZND1="g_21c12f0@0" ObjectIDZND2="37857@1" Pin0InfoVect0LinkObjId="SW-28380_0" Pin0InfoVect1LinkObjId="g_21c12f0_0" Pin0InfoVect2LinkObjId="g_2429220_1" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21c0260_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5177,-987 5152,-987 5152,-1017 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2425700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5152,-1017 5133,-1017 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" ObjectIDND0="g_21c0260@0" ObjectIDND1="g_21c12f0@0" ObjectIDND2="37857@1" ObjectIDZND0="4594@1" Pin0InfoVect0LinkObjId="SW-28380_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_21c0260_0" Pin1InfoVect1LinkObjId="g_21c12f0_0" Pin1InfoVect2LinkObjId="g_2429220_1" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5152,-1017 5133,-1017 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2425960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5182,-1131 5157,-1131 5157,-1161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_21946d0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_2195760@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_2195760_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21946d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5182,-1131 5157,-1131 5157,-1161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2425bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5157,-1161 5138,-1161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_21946d0@0" ObjectIDND1="g_2195760@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_21946d0_0" Pin1InfoVect1LinkObjId="g_2195760_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5157,-1161 5138,-1161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2425e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5152,-1017 5152,-1019 5152,-1042 5167,-1042 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="powerLine" EndDevType0="lightningRod" ObjectIDND0="g_21c0260@0" ObjectIDND1="4594@x" ObjectIDND2="37857@1" ObjectIDZND0="g_21c12f0@0" Pin0InfoVect0LinkObjId="g_21c12f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_21c0260_0" Pin1InfoVect1LinkObjId="SW-28380_0" Pin1InfoVect2LinkObjId="g_2429220_1" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5152,-1017 5152,-1019 5152,-1042 5167,-1042 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2426070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5157,-1161 5157,-1163 5157,-1186 5172,-1186 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_21946d0@0" ObjectIDND1="0@x" ObjectIDZND0="g_2195760@0" Pin0InfoVect0LinkObjId="g_2195760_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_21946d0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5157,-1161 5157,-1163 5157,-1186 5172,-1186 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_24262c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3930,-835 3909,-835 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="4566@0" ObjectIDZND0="4563@x" ObjectIDZND1="4564@x" Pin0InfoVect0LinkObjId="SW-28230_0" Pin0InfoVect1LinkObjId="SW-28231_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28233_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3930,-835 3909,-835 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2426500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3909,-835 3909,-815 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="4566@x" ObjectIDND1="4564@x" ObjectIDZND0="4563@1" Pin0InfoVect0LinkObjId="SW-28230_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-28233_0" Pin1InfoVect1LinkObjId="SW-28231_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3909,-835 3909,-815 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2426760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3909,-895 3909,-884 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4547@0" ObjectIDZND0="4564@0" Pin0InfoVect0LinkObjId="SW-28231_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3909,-895 3909,-884 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_24269c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3909,-848 3909,-835 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="4564@1" ObjectIDZND0="4566@x" ObjectIDZND1="4563@x" Pin0InfoVect0LinkObjId="SW-28233_0" Pin0InfoVect1LinkObjId="SW-28230_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28231_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3909,-848 3909,-835 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2426c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5156,-703 5237,-703 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" ObjectIDND0="g_220b2f0@0" ObjectIDND1="0@x" ObjectIDND2="g_23f5460@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_220b2f0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_23f5460_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5156,-703 5237,-703 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2426e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5181,-673 5156,-673 5156,-703 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_220b2f0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_23f5460@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_23f5460_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_220b2f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5181,-673 5156,-673 5156,-703 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24270e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5156,-703 5137,-703 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_220b2f0@0" ObjectIDND1="g_23f5460@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_220b2f0_0" Pin1InfoVect1LinkObjId="g_23f5460_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5156,-703 5137,-703 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2427340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5157,-625 5237,-625 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" ObjectIDND0="g_2226d90@0" ObjectIDND1="0@x" ObjectIDND2="g_2227e20@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2226d90_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_2227e20_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5157,-625 5237,-625 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24275a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5182,-595 5157,-595 5157,-625 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_2226d90@0" ObjectIDZND0="0@x" ObjectIDZND1="g_2227e20@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_2227e20_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2226d90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5182,-595 5157,-595 5157,-625 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2427800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5157,-625 5138,-625 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_2226d90@0" ObjectIDND1="g_2227e20@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2226d90_0" Pin1InfoVect1LinkObjId="g_2227e20_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5157,-625 5138,-625 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2427a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5157,-551 5238,-551 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" ObjectIDND0="g_219f2f0@0" ObjectIDND1="0@x" ObjectIDND2="g_21a0380@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_219f2f0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_21a0380_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5157,-551 5238,-551 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2427cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5182,-521 5157,-521 5157,-551 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_219f2f0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_21a0380@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_21a0380_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_219f2f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5182,-521 5157,-521 5157,-551 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2427f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5157,-551 5138,-551 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_219f2f0@0" ObjectIDND1="g_21a0380@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_219f2f0_0" Pin1InfoVect1LinkObjId="g_21a0380_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5157,-551 5138,-551 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2428180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5160,-479 5238,-479 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" ObjectIDND0="g_21a35c0@0" ObjectIDND1="0@x" ObjectIDND2="g_21a4650@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_21a35c0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_21a4650_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5160,-479 5238,-479 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24283e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5185,-449 5160,-449 5160,-479 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_21a35c0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_21a4650@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_21a4650_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21a35c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5185,-449 5160,-449 5160,-479 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2428640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5160,-479 5141,-479 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_21a35c0@0" ObjectIDND1="g_21a4650@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_21a35c0_0" Pin1InfoVect1LinkObjId="g_21a4650_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5160,-479 5141,-479 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24288a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5165,-404 5238,-404 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" ObjectIDND0="g_20a5d20@0" ObjectIDND1="0@x" ObjectIDND2="g_20a6e00@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_20a5d20_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_20a6e00_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5165,-404 5238,-404 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2428b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5190,-374 5165,-374 5165,-404 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_20a5d20@0" ObjectIDZND0="0@x" ObjectIDZND1="g_20a6e00@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_20a6e00_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20a5d20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5190,-374 5165,-374 5165,-404 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2428d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5165,-404 5146,-404 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_20a5d20@0" ObjectIDND1="g_20a6e00@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_20a5d20_0" Pin1InfoVect1LinkObjId="g_20a6e00_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5165,-404 5146,-404 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2428fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5157,-810 5204,-810 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_211a320@0" ObjectIDND1="g_211b3b0@0" ObjectIDND2="4600@x" ObjectIDZND0="37447@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_211a320_0" Pin1InfoVect1LinkObjId="g_211b3b0_0" Pin1InfoVect2LinkObjId="SW-28404_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5157,-810 5204,-810 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2429220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5152,-1017 5195,-1017 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="powerLine" ObjectIDND0="g_21c0260@0" ObjectIDND1="4594@x" ObjectIDND2="g_21c12f0@0" ObjectIDZND0="37857@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_21c0260_0" Pin1InfoVect1LinkObjId="SW-28380_0" Pin1InfoVect2LinkObjId="g_21c12f0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5152,-1017 5195,-1017 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2429480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5157,-1161 5235,-1161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" ObjectIDND0="g_21946d0@0" ObjectIDND1="0@x" ObjectIDND2="g_2195760@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_21946d0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_2195760_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5157,-1161 5235,-1161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_24296e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3706,-1102 3729,-1102 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_223bc00@0" ObjectIDZND0="g_23fa450@0" ObjectIDZND1="9694@x" ObjectIDZND2="9696@x" Pin0InfoVect0LinkObjId="g_23fa450_0" Pin0InfoVect1LinkObjId="SW-54985_0" Pin0InfoVect2LinkObjId="SW-54987_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_223bc00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3706,-1102 3729,-1102 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2429940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3729,-1102 3729,-1137 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_223bc00@0" ObjectIDND1="g_23fa450@0" ObjectIDND2="9694@x" ObjectIDZND0="11713@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_223bc00_0" Pin1InfoVect1LinkObjId="g_23fa450_0" Pin1InfoVect2LinkObjId="SW-54985_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3729,-1102 3729,-1137 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2429ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3753,-1081 3729,-1081 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="powerLine" EndDevType2="switch" ObjectIDND0="g_23fa450@0" ObjectIDZND0="g_223bc00@0" ObjectIDZND1="11713@1" ObjectIDZND2="9694@x" Pin0InfoVect0LinkObjId="g_223bc00_0" Pin0InfoVect1LinkObjId="g_2429940_1" Pin0InfoVect2LinkObjId="SW-54985_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_23fa450_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3753,-1081 3729,-1081 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2429e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3729,-1081 3729,-1102 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="g_23fa450@0" ObjectIDND1="9694@x" ObjectIDND2="9696@x" ObjectIDZND0="g_223bc00@0" ObjectIDZND1="11713@1" Pin0InfoVect0LinkObjId="g_223bc00_0" Pin0InfoVect1LinkObjId="g_2429940_1" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_23fa450_0" Pin1InfoVect1LinkObjId="SW-54985_0" Pin1InfoVect2LinkObjId="SW-54987_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3729,-1081 3729,-1102 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_242a060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3747,-1059 3729,-1059 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="9696@0" ObjectIDZND0="9694@x" ObjectIDZND1="g_223bc00@0" ObjectIDZND2="11713@1" Pin0InfoVect0LinkObjId="SW-54985_0" Pin0InfoVect1LinkObjId="g_223bc00_0" Pin0InfoVect2LinkObjId="g_2429940_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54987_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3747,-1059 3729,-1059 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_242a2c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3729,-1050 3729,-1059 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="9694@0" ObjectIDZND0="9696@x" ObjectIDZND1="g_223bc00@0" ObjectIDZND2="11713@1" Pin0InfoVect0LinkObjId="SW-54987_0" Pin0InfoVect1LinkObjId="g_223bc00_0" Pin0InfoVect2LinkObjId="g_2429940_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54985_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3729,-1050 3729,-1059 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_242a520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3729,-1059 3729,-1081 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="powerLine" EndDevType2="lightningRod" ObjectIDND0="9694@x" ObjectIDND1="9696@x" ObjectIDZND0="g_223bc00@0" ObjectIDZND1="11713@1" ObjectIDZND2="g_23fa450@0" Pin0InfoVect0LinkObjId="g_223bc00_0" Pin0InfoVect1LinkObjId="g_2429940_1" Pin0InfoVect2LinkObjId="g_23fa450_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-54985_0" Pin1InfoVect1LinkObjId="SW-54987_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3729,-1059 3729,-1081 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_242a780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3984,-1103 4007,-1103 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_225b220@0" ObjectIDZND0="g_23faed0@0" ObjectIDZND1="4559@x" ObjectIDZND2="4561@x" Pin0InfoVect0LinkObjId="g_23faed0_0" Pin0InfoVect1LinkObjId="SW-28193_0" Pin0InfoVect2LinkObjId="SW-28195_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_225b220_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3984,-1103 4007,-1103 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_242a9e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4007,-1103 4007,-1139 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_225b220@0" ObjectIDND1="g_23faed0@0" ObjectIDND2="4559@x" ObjectIDZND0="18018@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_225b220_0" Pin1InfoVect1LinkObjId="g_23faed0_0" Pin1InfoVect2LinkObjId="SW-28193_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4007,-1103 4007,-1139 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_242ac40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4031,-1082 4007,-1082 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="powerLine" EndDevType2="switch" ObjectIDND0="g_23faed0@0" ObjectIDZND0="g_225b220@0" ObjectIDZND1="18018@1" ObjectIDZND2="4559@x" Pin0InfoVect0LinkObjId="g_225b220_0" Pin0InfoVect1LinkObjId="g_242a9e0_1" Pin0InfoVect2LinkObjId="SW-28193_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_23faed0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4031,-1082 4007,-1082 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_242aea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4007,-1082 4007,-1103 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="g_23faed0@0" ObjectIDND1="4559@x" ObjectIDND2="4561@x" ObjectIDZND0="g_225b220@0" ObjectIDZND1="18018@1" Pin0InfoVect0LinkObjId="g_225b220_0" Pin0InfoVect1LinkObjId="g_242a9e0_1" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_23faed0_0" Pin1InfoVect1LinkObjId="SW-28193_0" Pin1InfoVect2LinkObjId="SW-28195_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4007,-1082 4007,-1103 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_242b100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4024,-1059 4007,-1059 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="4561@0" ObjectIDZND0="4559@x" ObjectIDZND1="g_225b220@0" ObjectIDZND2="18018@1" Pin0InfoVect0LinkObjId="SW-28193_0" Pin0InfoVect1LinkObjId="g_225b220_0" Pin0InfoVect2LinkObjId="g_242a9e0_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28195_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4024,-1059 4007,-1059 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_242b360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4007,-1051 4007,-1060 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="4559@0" ObjectIDZND0="4561@x" ObjectIDZND1="g_225b220@0" ObjectIDZND2="18018@1" Pin0InfoVect0LinkObjId="SW-28195_0" Pin0InfoVect1LinkObjId="g_225b220_0" Pin0InfoVect2LinkObjId="g_242a9e0_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28193_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4007,-1051 4007,-1060 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_242b5c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4007,-1060 4007,-1082 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="powerLine" EndDevType2="lightningRod" ObjectIDND0="4559@x" ObjectIDND1="4561@x" ObjectIDZND0="g_225b220@0" ObjectIDZND1="18018@1" ObjectIDZND2="g_23faed0@0" Pin0InfoVect0LinkObjId="g_225b220_0" Pin0InfoVect1LinkObjId="g_242a9e0_1" Pin0InfoVect2LinkObjId="g_23faed0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-28193_0" Pin1InfoVect1LinkObjId="SW-28195_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4007,-1060 4007,-1082 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_242b820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5152,-941 5152,-871 5182,-871 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="4597@x" ObjectIDND1="g_27b6c10@0" ObjectIDND2="g_2112dd0@0" ObjectIDZND0="g_2219250@0" Pin0InfoVect0LinkObjId="g_2219250_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-28392_0" Pin1InfoVect1LinkObjId="g_27b6c10_0" Pin1InfoVect2LinkObjId="g_2112dd0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5152,-941 5152,-871 5182,-871 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_242ba80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4275,-1082 4251,-1082 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_23fbb80@0" ObjectIDZND0="4553@x" ObjectIDZND1="4555@x" ObjectIDZND2="g_21ae550@0" Pin0InfoVect0LinkObjId="SW-28147_0" Pin0InfoVect1LinkObjId="SW-28149_0" Pin0InfoVect2LinkObjId="g_21ae550_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_23fbb80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4275,-1082 4251,-1082 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_242bce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4268,-1060 4251,-1060 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="4555@0" ObjectIDZND0="4553@x" ObjectIDZND1="g_23fbb80@0" ObjectIDZND2="g_21ae550@0" Pin0InfoVect0LinkObjId="SW-28147_0" Pin0InfoVect1LinkObjId="g_23fbb80_0" Pin0InfoVect2LinkObjId="g_21ae550_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28149_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4268,-1060 4251,-1060 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_242bf40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4251,-1051 4251,-1060 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="4553@0" ObjectIDZND0="4555@x" ObjectIDZND1="g_23fbb80@0" ObjectIDZND2="g_21ae550@0" Pin0InfoVect0LinkObjId="SW-28149_0" Pin0InfoVect1LinkObjId="g_23fbb80_0" Pin0InfoVect2LinkObjId="g_21ae550_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28147_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4251,-1051 4251,-1060 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_242c1a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4251,-1060 4251,-1082 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="4553@x" ObjectIDND1="4555@x" ObjectIDZND0="g_23fbb80@0" ObjectIDZND1="g_21ae550@0" ObjectIDZND2="14288@1" Pin0InfoVect0LinkObjId="g_23fbb80_0" Pin0InfoVect1LinkObjId="g_21ae550_0" Pin0InfoVect2LinkObjId="g_242c8c0_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-28147_0" Pin1InfoVect1LinkObjId="SW-28149_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4251,-1060 4251,-1082 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_242c400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4228,-1103 4251,-1103 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_21ae550@0" ObjectIDZND0="4553@x" ObjectIDZND1="4555@x" ObjectIDZND2="g_23fbb80@0" Pin0InfoVect0LinkObjId="SW-28147_0" Pin0InfoVect1LinkObjId="SW-28149_0" Pin0InfoVect2LinkObjId="g_23fbb80_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21ae550_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4228,-1103 4251,-1103 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_242c660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4251,-1082 4251,-1103 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="4553@x" ObjectIDND1="4555@x" ObjectIDND2="g_23fbb80@0" ObjectIDZND0="g_21ae550@0" ObjectIDZND1="14288@1" Pin0InfoVect0LinkObjId="g_21ae550_0" Pin0InfoVect1LinkObjId="g_242c8c0_1" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-28147_0" Pin1InfoVect1LinkObjId="SW-28149_0" Pin1InfoVect2LinkObjId="g_23fbb80_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4251,-1082 4251,-1103 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_242c8c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4251,-1103 4251,-1139 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="powerLine" ObjectIDND0="4553@x" ObjectIDND1="4555@x" ObjectIDND2="g_23fbb80@0" ObjectIDZND0="14288@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-28147_0" Pin1InfoVect1LinkObjId="SW-28149_0" Pin1InfoVect2LinkObjId="g_23fbb80_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4251,-1103 4251,-1139 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_242cb20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4538,-1104 4561,-1104 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_21e0430@0" ObjectIDZND0="g_23fc880@0" ObjectIDZND1="9698@x" ObjectIDZND2="9700@x" Pin0InfoVect0LinkObjId="g_23fc880_0" Pin0InfoVect1LinkObjId="SW-55061_0" Pin0InfoVect2LinkObjId="SW-55063_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21e0430_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4538,-1104 4561,-1104 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_242cd80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4561,-1104 4561,-1137 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_21e0430@0" ObjectIDND1="g_23fc880@0" ObjectIDND2="9698@x" ObjectIDZND0="11712@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_21e0430_0" Pin1InfoVect1LinkObjId="g_23fc880_0" Pin1InfoVect2LinkObjId="SW-55061_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4561,-1104 4561,-1137 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_242cfe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4585,-1083 4561,-1083 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="powerLine" EndDevType2="switch" ObjectIDND0="g_23fc880@0" ObjectIDZND0="g_21e0430@0" ObjectIDZND1="11712@1" ObjectIDZND2="9698@x" Pin0InfoVect0LinkObjId="g_21e0430_0" Pin0InfoVect1LinkObjId="g_242cd80_1" Pin0InfoVect2LinkObjId="SW-55061_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_23fc880_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4585,-1083 4561,-1083 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_242d240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4561,-1083 4561,-1104 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="g_23fc880@0" ObjectIDND1="9698@x" ObjectIDND2="9700@x" ObjectIDZND0="g_21e0430@0" ObjectIDZND1="11712@1" Pin0InfoVect0LinkObjId="g_21e0430_0" Pin0InfoVect1LinkObjId="g_242cd80_1" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_23fc880_0" Pin1InfoVect1LinkObjId="SW-55061_0" Pin1InfoVect2LinkObjId="SW-55063_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4561,-1083 4561,-1104 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_242d4a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4578,-1061 4561,-1061 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="9700@0" ObjectIDZND0="9698@x" ObjectIDZND1="g_21e0430@0" ObjectIDZND2="11712@1" Pin0InfoVect0LinkObjId="SW-55061_0" Pin0InfoVect1LinkObjId="g_21e0430_0" Pin0InfoVect2LinkObjId="g_242cd80_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-55063_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4578,-1061 4561,-1061 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_242d700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4561,-1052 4561,-1061 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="9698@0" ObjectIDZND0="9700@x" ObjectIDZND1="g_21e0430@0" ObjectIDZND2="11712@1" Pin0InfoVect0LinkObjId="SW-55063_0" Pin0InfoVect1LinkObjId="g_21e0430_0" Pin0InfoVect2LinkObjId="g_242cd80_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-55061_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4561,-1052 4561,-1061 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_242d960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4561,-1061 4561,-1083 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="powerLine" EndDevType2="lightningRod" ObjectIDND0="9698@x" ObjectIDND1="9700@x" ObjectIDZND0="g_21e0430@0" ObjectIDZND1="11712@1" ObjectIDZND2="g_23fc880@0" Pin0InfoVect0LinkObjId="g_21e0430_0" Pin0InfoVect1LinkObjId="g_242cd80_1" Pin0InfoVect2LinkObjId="g_23fc880_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-55061_0" Pin1InfoVect1LinkObjId="SW-55063_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4561,-1061 4561,-1083 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_242dbc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4286,-120 4286,-131 4260,-131 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_23fe360@0" ObjectIDZND0="14293@x" ObjectIDZND1="4603@x" Pin0InfoVect0LinkObjId="EC-CX_WM.076Ld_0" Pin0InfoVect1LinkObjId="SW-28416_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_23fe360_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4286,-120 4286,-131 4260,-131 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_242de20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4260,-88 4260,-131 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="14293@0" ObjectIDZND0="g_23fe360@0" ObjectIDZND1="4603@x" Pin0InfoVect0LinkObjId="g_23fe360_0" Pin0InfoVect1LinkObjId="SW-28416_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-CX_WM.076Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4260,-88 4260,-131 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_242e080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4260,-131 4260,-138 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="14293@x" ObjectIDND1="g_23fe360@0" ObjectIDZND0="4603@1" Pin0InfoVect0LinkObjId="SW-28416_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-CX_WM.076Ld_0" Pin1InfoVect1LinkObjId="g_23fe360_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4260,-131 4260,-138 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_242e2e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4384,-122 4384,-133 4359,-133 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_23ff0d0@0" ObjectIDZND0="14294@x" ObjectIDZND1="4606@x" Pin0InfoVect0LinkObjId="EC-CX_WM.077Ld_0" Pin0InfoVect1LinkObjId="SW-28430_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_23ff0d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4384,-122 4384,-133 4359,-133 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_242e540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4359,-90 4359,-133 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="14294@0" ObjectIDZND0="g_23ff0d0@0" ObjectIDZND1="4606@x" Pin0InfoVect0LinkObjId="g_23ff0d0_0" Pin0InfoVect1LinkObjId="SW-28430_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-CX_WM.077Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4359,-90 4359,-133 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_242e7a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4359,-133 4359,-141 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="14294@x" ObjectIDND1="g_23ff0d0@0" ObjectIDZND0="4606@1" Pin0InfoVect0LinkObjId="SW-28430_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-CX_WM.077Ld_0" Pin1InfoVect1LinkObjId="g_23ff0d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4359,-133 4359,-141 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_242ea00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4260,-250 4260,-233 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" ObjectIDND0="4604@0" ObjectIDZND0="g_2401870@1" Pin0InfoVect0LinkObjId="g_2401870_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28417_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4260,-250 4260,-233 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_242ec60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4260,-194 4260,-174 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2401870@0" ObjectIDZND0="4603@0" Pin0InfoVect0LinkObjId="SW-28416_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2401870_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4260,-194 4260,-174 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_242eec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4359,-177 4359,-194 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="4606@0" ObjectIDZND0="g_24020b0@0" Pin0InfoVect0LinkObjId="g_24020b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28430_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4359,-177 4359,-194 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_242f120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4359,-233 4359,-253 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_24020b0@1" ObjectIDZND0="4607@0" Pin0InfoVect0LinkObjId="SW-28431_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24020b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4359,-233 4359,-253 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2451760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5156,-703 5156,-728 5171,-728 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_220b2f0@0" ObjectIDND1="0@x" ObjectIDZND0="g_23f5460@0" Pin0InfoVect0LinkObjId="g_23f5460_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_220b2f0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5156,-703 5156,-728 5171,-728 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24519c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5157,-625 5157,-649 5172,-649 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2226d90@0" ObjectIDND1="0@x" ObjectIDZND0="g_2227e20@0" Pin0InfoVect0LinkObjId="g_2227e20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2226d90_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5157,-625 5157,-649 5172,-649 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2451c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5157,-551 5157,-575 5173,-575 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_219f2f0@0" ObjectIDND1="0@x" ObjectIDZND0="g_21a0380@0" Pin0InfoVect0LinkObjId="g_21a0380_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_219f2f0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5157,-551 5157,-575 5173,-575 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2451e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5160,-479 5160,-503 5176,-503 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_21a35c0@0" ObjectIDND1="0@x" ObjectIDZND0="g_21a4650@0" Pin0InfoVect0LinkObjId="g_21a4650_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_21a35c0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5160,-479 5160,-503 5176,-503 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24520e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5165,-404 5165,-428 5181,-428 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_20a5d20@0" ObjectIDND1="0@x" ObjectIDZND0="g_20a6e00@0" Pin0InfoVect0LinkObjId="g_20a6e00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_20a5d20_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5165,-404 5165,-428 5181,-428 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_245bfa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4016,-84 4030,-84 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4610@1" ObjectIDZND0="g_245b510@0" Pin0InfoVect0LinkObjId="g_245b510_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28450_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4016,-84 4030,-84 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_245c200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3943,-84 3980,-84 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" EndDevType0="switch" ObjectIDND0="4609@x" ObjectIDND1="12211@x" ObjectIDZND0="4610@0" Pin0InfoVect0LinkObjId="SW-28450_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-28449_0" Pin1InfoVect1LinkObjId="CB-CX_WM.CX_WM_1C_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3943,-84 3980,-84 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_245ccf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3943,-93 3943,-84 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="capacitor" ObjectIDND0="4609@1" ObjectIDZND0="4610@x" ObjectIDZND1="12211@x" Pin0InfoVect0LinkObjId="SW-28450_0" Pin0InfoVect1LinkObjId="CB-CX_WM.CX_WM_1C_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28449_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3943,-93 3943,-84 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_245cf50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3943,-84 3943,-70 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="capacitor" ObjectIDND0="4610@x" ObjectIDND1="4609@x" ObjectIDZND0="12211@0" Pin0InfoVect0LinkObjId="CB-CX_WM.CX_WM_1C_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-28450_0" Pin1InfoVect1LinkObjId="SW-28449_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3943,-84 3943,-70 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2460110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4017,-57 4030,-57 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_245f680@0" Pin0InfoVect0LinkObjId="g_245f680_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4017,-57 4030,-57 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2460370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3981,-57 3975,-57 3975,43 3944,43 3944,37 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" ObjectIDND0="0@0" ObjectIDZND0="12211@1" Pin0InfoVect0LinkObjId="CB-CX_WM.CX_WM_1C_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3981,-57 3975,-57 3975,43 3944,43 3944,37 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2466760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4895,-82 4909,-82 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_2465cd0@0" Pin0InfoVect0LinkObjId="g_2465cd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4895,-82 4909,-82 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24669c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4822,-82 4859,-82 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4822,-82 4859,-82 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2466c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4822,-91 4822,-82 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="capacitor" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4822,-91 4822,-82 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2466e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4822,-82 4822,-68 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="capacitor" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4822,-82 4822,-68 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_246a040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4896,-55 4909,-55 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_24695b0@0" Pin0InfoVect0LinkObjId="g_24695b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4896,-55 4909,-55 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_246a2a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4860,-55 4854,-55 4854,45 4823,45 4823,39 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4860,-55 4854,-55 4854,45 4823,45 4823,39 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2470f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5063,-85 5077,-85 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_2470480@0" Pin0InfoVect0LinkObjId="g_2470480_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5063,-85 5077,-85 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2471170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4990,-85 5027,-85 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4990,-85 5027,-85 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24713d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4990,-94 4990,-85 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="capacitor" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4990,-94 4990,-85 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2471630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4990,-85 4990,-71 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="capacitor" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4990,-85 4990,-71 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24747f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5064,-58 5077,-58 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_2473d60@0" Pin0InfoVect0LinkObjId="g_2473d60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5064,-58 5077,-58 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2474a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5028,-58 5022,-58 5022,42 4991,42 4991,36 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5028,-58 5022,-58 5022,42 4991,42 4991,36 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2480450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3844,-334 3844,-357 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="20068@0" ObjectIDZND0="4550@0" Pin0InfoVect0LinkObjId="g_241dbb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-95821_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3844,-334 3844,-357 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24806b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3844,-235 3844,-247 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_247f700@0" ObjectIDZND0="20070@0" Pin0InfoVect0LinkObjId="SW-95823_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_247f700_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3844,-235 3844,-247 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2480910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3844,-171 3844,-182 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="20069@0" ObjectIDZND0="g_247f700@1" Pin0InfoVect0LinkObjId="g_247f700_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-95822_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3844,-171 3844,-182 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2480b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3844,-298 3844,-274 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="20068@1" ObjectIDZND0="20070@1" Pin0InfoVect0LinkObjId="SW-95823_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-95821_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3844,-298 3844,-274 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2483bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3870,-115 3870,-126 3844,-126 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_24813a0@0" ObjectIDZND0="20069@x" ObjectIDZND1="20080@x" Pin0InfoVect0LinkObjId="SW-95822_0" Pin0InfoVect1LinkObjId="EC-CX_WM.073Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24813a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3870,-115 3870,-126 3844,-126 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2483e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3771,-119 3771,-133 3745,-133 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_2482110@0" ObjectIDZND0="20066@x" ObjectIDZND1="20079@x" Pin0InfoVect0LinkObjId="SW-95807_0" Pin0InfoVect1LinkObjId="EC-CX_WM.072Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2482110_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3771,-119 3771,-133 3745,-133 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24840b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3673,-115 3673,-127 3647,-127 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_2482e80@0" ObjectIDZND0="20063@x" ObjectIDZND1="20078@x" Pin0InfoVect0LinkObjId="SW-95792_0" Pin0InfoVect1LinkObjId="EC-CX_WM.071Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2482e80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3673,-115 3673,-127 3647,-127 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24861c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3647,-136 3647,-127 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="20063@1" ObjectIDZND0="g_2482e80@0" ObjectIDZND1="20078@x" Pin0InfoVect0LinkObjId="g_2482e80_0" Pin0InfoVect1LinkObjId="EC-CX_WM.071Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-95792_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3647,-136 3647,-127 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2486420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3647,-127 3647,-101 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_2482e80@0" ObjectIDND1="20063@x" ObjectIDZND0="20078@0" Pin0InfoVect0LinkObjId="EC-CX_WM.071Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2482e80_0" Pin1InfoVect1LinkObjId="SW-95792_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3647,-127 3647,-101 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2486f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3745,-140 3745,-133 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="20066@1" ObjectIDZND0="g_2482110@0" ObjectIDZND1="20079@x" Pin0InfoVect0LinkObjId="g_2482110_0" Pin0InfoVect1LinkObjId="EC-CX_WM.072Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-95807_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3745,-140 3745,-133 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2487170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3745,-133 3745,-95 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_2482110@0" ObjectIDND1="20066@x" ObjectIDZND0="20079@0" Pin0InfoVect0LinkObjId="EC-CX_WM.072Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2482110_0" Pin1InfoVect1LinkObjId="SW-95807_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3745,-133 3745,-95 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2487c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3844,-135 3844,-126 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="20069@1" ObjectIDZND0="g_24813a0@0" ObjectIDZND1="20080@x" Pin0InfoVect0LinkObjId="g_24813a0_0" Pin0InfoVect1LinkObjId="EC-CX_WM.073Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-95822_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3844,-135 3844,-126 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2487ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3844,-126 3844,-96 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_24813a0@0" ObjectIDND1="20069@x" ObjectIDZND0="20080@0" Pin0InfoVect0LinkObjId="EC-CX_WM.073Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_24813a0_0" Pin1InfoVect1LinkObjId="SW-95822_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3844,-126 3844,-96 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_248ae00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4379,-378 4379,-357 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="4587@1" ObjectIDZND0="4550@0" Pin0InfoVect0LinkObjId="g_241dbb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28361_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4379,-378 4379,-357 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27b7cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5193,-905 5159,-905 5159,-940 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_27b6c10@0" ObjectIDZND0="g_2112dd0@0" ObjectIDZND1="g_2219250@0" ObjectIDZND2="4597@x" Pin0InfoVect0LinkObjId="g_2112dd0_0" Pin0InfoVect1LinkObjId="g_2219250_0" Pin0InfoVect2LinkObjId="SW-28392_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_27b6c10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5193,-905 5159,-905 5159,-940 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27b8a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5159,-940 5200,-940 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="powerLine" ObjectIDND0="g_27b6c10@0" ObjectIDND1="g_2112dd0@0" ObjectIDND2="g_2219250@0" ObjectIDZND0="37860@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_27b6c10_0" Pin1InfoVect1LinkObjId="g_2112dd0_0" Pin1InfoVect2LinkObjId="g_2219250_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5159,-940 5200,-940 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27b8c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5159,-940 5159,-963 5170,-963 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_27b6c10@0" ObjectIDND1="g_2219250@0" ObjectIDND2="4597@x" ObjectIDZND0="g_2112dd0@0" Pin0InfoVect0LinkObjId="g_2112dd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_27b6c10_0" Pin1InfoVect1LinkObjId="g_2219250_0" Pin1InfoVect2LinkObjId="SW-28392_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5159,-940 5159,-963 5170,-963 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27bac70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5136,-940 5152,-940 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="4597@1" ObjectIDZND0="g_2219250@0" ObjectIDZND1="g_27b6c10@0" ObjectIDZND2="g_2112dd0@0" Pin0InfoVect0LinkObjId="g_2219250_0" Pin0InfoVect1LinkObjId="g_27b6c10_0" Pin0InfoVect2LinkObjId="g_2112dd0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28392_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5136,-940 5152,-940 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27bae60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5152,-940 5159,-940 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="g_2219250@0" ObjectIDND1="4597@x" ObjectIDZND0="g_27b6c10@0" ObjectIDZND1="g_2112dd0@0" ObjectIDZND2="37860@1" Pin0InfoVect0LinkObjId="g_27b6c10_0" Pin0InfoVect1LinkObjId="g_2112dd0_0" Pin0InfoVect2LinkObjId="g_27b8a00_1" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2219250_0" Pin1InfoVect1LinkObjId="SW-28392_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5152,-940 5159,-940 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27ce610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4967,-1089 4991,-1089 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4549@0" ObjectIDZND0="29440@0" Pin0InfoVect0LinkObjId="SW-193559_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2421e30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4967,-1089 4991,-1089 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27ce800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5049,-1089 5027,-1089 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29442@1" ObjectIDZND0="29440@1" Pin0InfoVect0LinkObjId="SW-193559_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193562_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5049,-1089 5027,-1089 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27ce9f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5097,-1089 5076,-1089 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="29441@0" ObjectIDZND0="29442@0" Pin0InfoVect0LinkObjId="SW-193562_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193560_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5097,-1089 5076,-1089 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27cec00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5152,-1089 5152,-1091 5152,-1114 5167,-1114 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="powerLine" EndDevType0="lightningRod" ObjectIDND0="g_27cbe10@0" ObjectIDND1="29441@x" ObjectIDND2="43718@1" ObjectIDZND0="g_27ccef0@0" Pin0InfoVect0LinkObjId="g_27ccef0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_27cbe10_0" Pin1InfoVect1LinkObjId="SW-193560_0" Pin1InfoVect2LinkObjId="g_27cf290_1" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5152,-1089 5152,-1091 5152,-1114 5167,-1114 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27cee20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5177,-1059 5152,-1059 5152,-1089 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="powerLine" ObjectIDND0="g_27cbe10@0" ObjectIDZND0="g_27ccef0@0" ObjectIDZND1="29441@x" ObjectIDZND2="43718@1" Pin0InfoVect0LinkObjId="g_27ccef0_0" Pin0InfoVect1LinkObjId="SW-193560_0" Pin0InfoVect2LinkObjId="g_27cf290_1" Pin0Num="1" Pin1InfoVect0LinkObjId="g_27cbe10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5177,-1059 5152,-1059 5152,-1089 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27cf030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5152,-1089 5133,-1089 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" ObjectIDND0="g_27ccef0@0" ObjectIDND1="g_27cbe10@0" ObjectIDND2="43718@1" ObjectIDZND0="29441@1" Pin0InfoVect0LinkObjId="SW-193560_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_27ccef0_0" Pin1InfoVect1LinkObjId="g_27cbe10_0" Pin1InfoVect2LinkObjId="g_27cf290_1" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5152,-1089 5133,-1089 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27cf290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5152,-1089 5202,-1089 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_27ccef0@0" ObjectIDND1="g_27cbe10@0" ObjectIDND2="29441@x" ObjectIDZND0="43718@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_27ccef0_0" Pin1InfoVect1LinkObjId="g_27cbe10_0" Pin1InfoVect2LinkObjId="SW-193560_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5152,-1089 5202,-1089 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="4547" cx="3729" cy="-895" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4548" cx="4547" cy="-896" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4547" cx="3862" cy="-895" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4547" cx="4007" cy="-895" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4548" cx="4251" cy="-896" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4548" cx="4390" cy="-896" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4548" cx="4561" cy="-896" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4547" cx="4063" cy="-895" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4548" cx="4191" cy="-896" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4822" cy="-358" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4990" cy="-358" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4537" cy="-358" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4611" cy="-358" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4687" cy="-358" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4760" cy="-358" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="5128" cy="-358" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="5247" cy="-358" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4497" cy="-358" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4546" cy="-358" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4550" cx="3745" cy="-357" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4550" cx="3647" cy="-357" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4550" cx="3943" cy="-357" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4550" cx="4260" cy="-357" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4550" cx="3845" cy="-357" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4550" cx="4082" cy="-357" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4550" cx="4170" cy="-357" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4550" cx="4359" cy="-357" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4550" cx="4379" cy="-357" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4547" cx="3909" cy="-895" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4550" cx="3908" cy="-357" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4549" cx="4967" cy="-940" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4549" cx="4967" cy="-810" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4549" cx="4967" cy="-1161" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4549" cx="4967" cy="-1017" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4549" cx="4967" cy="-890" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4549" cx="4967" cy="-790" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4968" cy="-703" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4968" cy="-625" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4968" cy="-551" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4968" cy="-479" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4968" cy="-404" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4968" cy="-442" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4968" cy="-638" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4968" cy="-726" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4549" cx="4967" cy="-1140" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-13" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3419.000000 -1087.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="13" ObjectName="DYN-CX_WM"/>
     <cge:Meas_Ref ObjectId="13"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22af220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1041.000000) translate(0,17)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22af220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1041.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22af220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1041.000000) translate(0,59)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22af220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1041.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22af220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1041.000000) translate(0,101)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22af220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1041.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22af220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1041.000000) translate(0,143)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22af220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1041.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22af220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1041.000000) translate(0,185)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ffe0d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -589.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ffe0d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -589.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ffe0d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -589.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ffe0d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -589.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ffe0d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -589.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ffe0d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -589.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ffe0d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -589.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ffe0d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -589.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ffe0d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -589.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ffe0d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -589.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ffe0d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -589.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ffe0d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -589.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ffe0d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -589.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ffe0d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -589.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ffe0d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -589.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ffe0d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -589.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ffe0d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -589.000000) translate(0,353)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20f03d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3739.000000 -1192.000000) translate(0,15)">迤</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20f03d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3739.000000 -1192.000000) translate(0,33)">万</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20f03d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3739.000000 -1192.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20f1360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4013.000000 -1184.000000) translate(0,15)">永</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20f1360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4013.000000 -1184.000000) translate(0,33)">干</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20f1360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4013.000000 -1184.000000) translate(0,51)">万</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20f1360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4013.000000 -1184.000000) translate(0,69)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20f1d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4268.000000 -1184.000000) translate(0,15)">永</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20f1d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4268.000000 -1184.000000) translate(0,33)">万</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20f1d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4268.000000 -1184.000000) translate(0,51)">的</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20f1d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4268.000000 -1184.000000) translate(0,69)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20f21c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4569.000000 -1164.000000) translate(0,15)">多</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20f21c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4569.000000 -1164.000000) translate(0,33)">万</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20f21c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4569.000000 -1164.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20f2750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3617.000000 -137.000000) translate(0,15)">立</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20f2750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3617.000000 -137.000000) translate(0,33)">溪</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20f2750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3617.000000 -137.000000) translate(0,51)">冬</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20f2750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3617.000000 -137.000000) translate(0,69)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20f32e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3917.000000 -178.000000) translate(0,15)">1</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20f32e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3917.000000 -178.000000) translate(0,33)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20f32e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3917.000000 -178.000000) translate(0,51)">电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20f32e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3917.000000 -178.000000) translate(0,69)">容</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20f32e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3917.000000 -178.000000) translate(0,87)">器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20f32e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3917.000000 -178.000000) translate(0,105)">组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20fb6d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4103.000000 -173.000000) translate(0,15)">1</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20fb6d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4103.000000 -173.000000) translate(0,33)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20fb6d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4103.000000 -173.000000) translate(0,51)">站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20fb6d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4103.000000 -173.000000) translate(0,69)">用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20fb6d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4103.000000 -173.000000) translate(0,87)">变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20fc260" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4117.000000 -292.000000) translate(0,15)">I</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20fc260" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4117.000000 -292.000000) translate(0,33)">段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20fc260" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4117.000000 -292.000000) translate(0,51)">母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20fc260" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4117.000000 -292.000000) translate(0,69)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20fc260" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4117.000000 -292.000000) translate(0,87)">TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20fd4a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4586.000000 -138.000000) translate(0,15)">预</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20fd4a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4586.000000 -138.000000) translate(0,33)">留</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20fdda0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4509.000000 -139.000000) translate(0,15)">预</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20fdda0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4509.000000 -139.000000) translate(0,33)">留</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20fe1d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4662.000000 -139.000000) translate(0,15)">预</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20fe1d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4662.000000 -139.000000) translate(0,33)">留</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20fe410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4739.000000 -138.000000) translate(0,15)">预</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20fe410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4739.000000 -138.000000) translate(0,33)">留</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20fe650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4948.000000 -54.000000) translate(0,15)">预</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20fe650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4948.000000 -54.000000) translate(0,33)">留</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20fe890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4785.000000 -46.000000) translate(0,15)">预</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20fe890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4785.000000 -46.000000) translate(0,33)">留</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20fead0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5090.000000 -175.000000) translate(0,15)">预</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20fead0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5090.000000 -175.000000) translate(0,33)">留</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20fed10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5196.000000 -256.000000) translate(0,15)">预</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20fed10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5196.000000 -256.000000) translate(0,33)">留</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20fef50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5240.000000 -723.000000) translate(0,15)">预留</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20ff190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5240.000000 -644.000000) translate(0,15)">预留</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20ff3d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5239.000000 -570.000000) translate(0,15)">预留</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20ff610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5240.000000 -499.000000) translate(0,15)">预留</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20ff850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5243.000000 -424.000000) translate(0,15)">预留</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20ffa90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5244.000000 -829.000000) translate(0,15)">万中线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20ffed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5228.000000 -947.000000) translate(0,15)">万他马红线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2100a70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5230.000000 -1027.000000) translate(0,15)">马湾线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2100fd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5347.000000 -866.000000) translate(0,15)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21015b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5242.000000 -1185.000000) translate(0,15)">预留</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21017f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3678.000000 -890.000000) translate(0,15)">110kVI母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21020f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4595.000000 -918.000000) translate(0,15)">110kVII母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2102370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3605.000000 -385.000000) translate(0,15)">10kVI母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21025b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4972.000000 -1197.000000) translate(0,15)">35kVI母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2102eb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4833.000000 -425.000000) translate(0,15)">预留</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2103130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4845.000000 -1126.000000) translate(0,15)">I段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2103370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3692.000000 -986.000000) translate(0,12)">191</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2103c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3736.000000 -935.000000) translate(0,12)">1911</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23d3770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3869.000000 -971.000000) translate(0,12)">1901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23d3f80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3878.000000 -954.000000) translate(0,12)">19010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23d4180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3877.000000 -1028.000000) translate(0,12)">19017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23d4450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3972.000000 -987.000000) translate(0,12)">192</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23d49c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4014.000000 -936.000000) translate(0,12)">1921</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23d4ea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4022.000000 -981.000000) translate(0,12)">19217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23d50e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4022.000000 -1025.000000) translate(0,12)">19260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23d5600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4022.000000 -1054.000000) translate(0,12)">19267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23d5880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4014.000000 -1040.000000) translate(0,12)">1926</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23d5ac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4215.000000 -987.000000) translate(0,12)">193</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23d5de0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4258.000000 -936.000000) translate(0,12)">1932</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23d6260" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4257.000000 -1040.000000) translate(0,12)">1936</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23d64a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4266.000000 -982.000000) translate(0,12)">19327</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23d66e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4266.000000 -1025.000000) translate(0,12)">19360</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23d6920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4266.000000 -1054.000000) translate(0,12)">19367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23d6b60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4397.000000 -975.000000) translate(0,12)">1902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23d6da0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4406.000000 -954.000000) translate(0,12)">19020</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23d6fe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4405.000000 -1031.000000) translate(0,12)">19027</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23d7220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4529.000000 -989.000000) translate(0,12)">194</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23d74d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3922.000000 -809.000000) translate(0,12)">101</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23d79c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3918.000000 -726.000000) translate(0,12)">1016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23d7c00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3914.000000 -872.000000) translate(0,12)">1011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23d7e40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3927.000000 -830.000000) translate(0,12)">10117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23d8080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3926.000000 -785.000000) translate(0,12)">10160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23dadc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3926.000000 -701.000000) translate(0,12)">10167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23db3f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4113.000000 -845.000000) translate(0,12)">112</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23db630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4070.000000 -861.000000) translate(0,12)">1121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23db870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4198.000000 -862.000000) translate(0,12)">1122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23dbab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4070.000000 -793.000000) translate(0,12)">11217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23dbcf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4198.000000 -794.000000) translate(0,12)">11227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23dbf30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3756.000000 -591.000000) translate(0,12)">1010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23dc170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4386.000000 -401.000000) translate(0,12)">0121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23dc3b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3918.000000 -469.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23dc5f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3916.000000 -424.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23dc830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3916.000000 -511.000000) translate(0,12)">0016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23ded60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4086.000000 -330.000000) translate(0,12)">0751</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23df7b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4177.000000 -327.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23e41c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5055.000000 -834.000000) translate(0,12)">375</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23e47f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4998.000000 -836.000000) translate(0,12)">3751</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23e4a30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5104.000000 -836.000000) translate(0,12)">3756</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23e4c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4996.000000 -966.000000) translate(0,12)">3741</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23e4eb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5102.000000 -966.000000) translate(0,12)">3746</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23e50f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5050.000000 -1041.000000) translate(0,12)">373</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23e5330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5098.000000 -1043.000000) translate(0,12)">3736</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23e5570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4993.000000 -1043.000000) translate(0,12)">3731</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23e57b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4991.000000 -785.000000) translate(0,12)">3121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_23e59f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4975.000000 -395.000000) translate(0,15)">35kVII母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_23e5c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5273.000000 -379.000000) translate(0,15)">10kVII母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23e5e70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4838.000000 -914.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23e60b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4786.000000 -916.000000) translate(0,12)">3016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23e62f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4883.000000 -916.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23e6530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4913.000000 -1166.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_23ebf50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3971.000000 -581.000000) translate(0,15)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_23f14c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3278.000000 -1167.500000) translate(0,16)">万马变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23f6830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4568.000000 -1041.000000) translate(0,12)">1946</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23f6d20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4568.000000 -937.000000) translate(0,12)">1942</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23f6f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4587.000000 -1058.000000) translate(0,12)">19467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23f71a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4588.000000 -1026.000000) translate(0,12)">19460</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23f73e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4588.000000 -980.000000) translate(0,12)">19427</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23f7620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3736.000000 -1039.000000) translate(0,12)">1916</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23f7860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3752.000000 -1056.000000) translate(0,12)">19167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23f7aa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3753.000000 -1021.000000) translate(0,12)">19160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23f7ce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3750.000000 -975.000000) translate(0,12)">19117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2401240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3996.000000 -50.000000) translate(0,12)">07400</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_24030b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3418.000000 -1169.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2458640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3264.000000 -253.000000) translate(0,17)">4707</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_248b5f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4425.000000 -455.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27b9d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5053.000000 -964.000000) translate(0,12)">374</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27bb1f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3853.000000 -268.000000) translate(0,12)">073</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27bb7b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3952.000000 -285.000000) translate(0,12)">074</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27bb9f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4269.000000 -271.000000) translate(0,12)">076</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27bbc30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4368.000000 -274.000000) translate(0,12)">077</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27bbe70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3656.000000 -269.000000) translate(0,12)">071</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27bc0b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3754.000000 -273.000000) translate(0,12)">072</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27bc430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3752.000000 -328.000000) translate(0,12)">0721</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27bc740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3752.000000 -165.000000) translate(0,12)">0726</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27bc980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3654.000000 -324.000000) translate(0,12)">0711</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27bcbc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3654.000000 -161.000000) translate(0,12)">0716</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27bce00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3851.000000 -323.000000) translate(0,12)">0731</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27bd040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3851.000000 -160.000000) translate(0,12)">0736</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27bd280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3950.000000 -330.000000) translate(0,12)">0741</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27bd4c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3950.000000 -238.000000) translate(0,12)">0743</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27bd700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3950.000000 -118.000000) translate(0,12)">0746</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27bd940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3996.000000 -107.000000) translate(0,12)">07467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27bdb80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3954.000000 -166.000000) translate(0,12)">07460</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27bddc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4267.000000 -326.000000) translate(0,12)">0761</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27be000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4267.000000 -163.000000) translate(0,12)">0766</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27be240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4366.000000 -329.000000) translate(0,12)">0771</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27be480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4366.000000 -166.000000) translate(0,12)">0776</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" graphid="g_27bedd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3166.000000 -803.000000) translate(0,20)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_27c0720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4069.000000 -522.000000) translate(0,15)">1号主变参数:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_27c0720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4069.000000 -522.000000) translate(0,33)">SFSZ11-40000/110</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_27c0720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4069.000000 -522.000000) translate(0,51)">110±8×1.25%/38.5±2×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_27c0720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4069.000000 -522.000000) translate(0,69)">40/40/40MVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_27c0720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4069.000000 -522.000000) translate(0,87)">YNyn0d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_27c0720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4069.000000 -522.000000) translate(0,105)">Ud1-2=10.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_27c0720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4069.000000 -522.000000) translate(0,123)">Ud1-3=17.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_27c0720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4069.000000 -522.000000) translate(0,141)">Ud2-3=6.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_27cdc60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5230.000000 -1098.000000) translate(0,15)">万兴线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27cfe60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4993.000000 -1111.000000) translate(0,12)">3721</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27d0490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5098.000000 -1111.000000) translate(0,12)">3726</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27d06d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5050.000000 -1113.000000) translate(0,12)">372</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_27d0910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5350.000000 -640.000000) translate(0,15)">调试区</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_27d1a60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3109.000000 -553.000000) translate(0,16)">1、全站停电检修前应挂“全站停电检修”牌，复电后</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_27d1a60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3109.000000 -553.000000) translate(0,36)">方可摘除“全站停电检修”牌。</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_27d1a60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3109.000000 -553.000000) translate(0,56)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_27d1a60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3109.000000 -553.000000) translate(0,76)">2、各类间隔工作时，停电完成后应在相应间隔挂</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_27d1a60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3109.000000 -553.000000) translate(0,96)">“禁止合闸，有人工作”牌，复电前方可摘除。</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_27d1a60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3109.000000 -553.000000) translate(0,116)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_27d1a60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3109.000000 -553.000000) translate(0,136)">3、线路工作时，停电完成后应在相应间隔</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_27d1a60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3109.000000 -553.000000) translate(0,156)">挂“禁止合闸，线路有人工作”牌，复电前方可摘除。</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_27d1a60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3109.000000 -553.000000) translate(0,176)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_27d1a60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3109.000000 -553.000000) translate(0,196)">4、现场工作影响对应间隔四遥信息正确性的，应挂</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_27d1a60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3109.000000 -553.000000) translate(0,216)">“禁止刷新”牌，工作结束后方可摘除。</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_27d1a60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3109.000000 -553.000000) translate(0,236)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_27d1a60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3109.000000 -553.000000) translate(0,256)">5、现场开展相应间隔四遥信息核对前，应挂“调试一”牌，</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_27d1a60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3109.000000 -553.000000) translate(0,276)">核对工作结束后方可摘除。</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_27e03d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3516.000000 -1150.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_27d3730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3516.000000 -1185.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27e6880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5251.000000 -990.000000) translate(0,12)">Ux(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27e7160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5254.000000 -1070.000000) translate(0,12)">Ux(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27e77d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5269.000000 -911.000000) translate(0,12)">Ux(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27e7e40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5232.000000 -768.000000) translate(0,12)">Ux(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_27e84b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4234.000000 -114.000000) translate(0,15)">团</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_27e84b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4234.000000 -114.000000) translate(0,33)">山</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_27e84b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4234.000000 -114.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_27e8ca0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3812.000000 -124.000000) translate(0,15)">进</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_27e8ca0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3812.000000 -124.000000) translate(0,33)">化</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_27e8ca0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3812.000000 -124.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_27e95a0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4334.000000 -112.000000) translate(0,15)">昔</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_27e95a0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4334.000000 -112.000000) translate(0,33)">丙</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_27e95a0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4334.000000 -112.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="20" graphid="g_27f2af0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3111.000000 -206.000000) translate(0,16)">永仁巡维中心：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2263e70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3262.000000 -216.500000) translate(0,17)">13638777384</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2263e70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3262.000000 -216.500000) translate(0,38)">13987885824</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27f69e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3611.000000 -1057.000000) translate(0,12)">Ux(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27f6ce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3909.000000 -1130.000000) translate(0,12)">Ux(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27f6ef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4145.000000 -1130.000000) translate(0,12)">Ux(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27f7130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4449.000000 -1133.000000) translate(0,12)">Ux(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_27fafa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3714.000000 -141.000000) translate(0,15)">丙</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_27fafa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3714.000000 -141.000000) translate(0,33)">和</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_27fafa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3714.000000 -141.000000) translate(0,51)">良</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_27fafa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3714.000000 -141.000000) translate(0,69)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" graphid="g_27fbbf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3168.000000 -715.000000) translate(0,20)">隔刀远控</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ac4aa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3817.000000 -1123.000000) translate(0,12)">110kVI段母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ac4aa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3817.000000 -1123.000000) translate(0,27)">线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2947ff0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4349.000000 -1125.000000) translate(0,12)">110kVII段母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2947ff0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4349.000000 -1125.000000) translate(0,27)">线电压互感器</text>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23ec6d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4812.000000 860.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23eda20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4801.000000 845.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23eec00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4826.000000 830.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23ef620" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3960.000000 482.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23ef8f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3949.000000 467.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23efb30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3974.000000 452.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23eff50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3785.000000 823.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23f0210" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3774.000000 808.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23f0450" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3799.000000 793.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23f0870" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3990.000000 0.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23f0b70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4015.000000 -15.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23f8010" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3982.000000 616.000000) translate(0,12)">油温（℃）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23f93c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3982.000000 631.000000) translate(0,12)">档位（档）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_248bb90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3548.000000 897.000000) translate(0,12)">U0（V）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_248c160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3541.000000 914.000000) translate(0,12)">Uc（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_248ca40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3541.000000 942.000000) translate(0,12)">Ua（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_248ccc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3541.000000 929.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_248d1e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3534.000000 881.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_248d460" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3547.000000 864.000000) translate(0,12)">F（Hz）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_248e0f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4681.000000 972.000000) translate(0,12)">U0（V）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_248e370" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4674.000000 989.000000) translate(0,12)">Uc（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_248e5b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4674.000000 1017.000000) translate(0,12)">Ua（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_248e7f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4674.000000 1004.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_248ea30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4667.000000 956.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_248ec70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4680.000000 939.000000) translate(0,12)">F（Hz）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_248efa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3582.000000 470.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27ab800" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3582.000000 454.000000) translate(0,12)">Uc（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27aba40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3589.000000 438.000000) translate(0,12)">U0（V）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27abc80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3573.000000 423.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27abec0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3582.000000 485.000000) translate(0,12)">Ua（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27ac100" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3589.000000 407.000000) translate(0,12)">F（Hz）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27ac430" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4822.000000 1255.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27ac6b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4822.000000 1239.000000) translate(0,12)">Uc（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27ac8f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4829.000000 1223.000000) translate(0,12)">U0（V）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27acb30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4813.000000 1208.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27acd70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4822.000000 1270.000000) translate(0,12)">Ua（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27acfb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4829.000000 1192.000000) translate(0,12)">F（Hz）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_27e9f60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3689.000000 1244.000000) translate(0,11)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_27eb2d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3678.000000 1229.000000) translate(0,11)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_27ec310" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3703.000000 1214.000000) translate(0,11)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_27ecd00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3964.000000 1248.000000) translate(0,11)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_27ecfa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3953.000000 1233.000000) translate(0,11)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_27ed1e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3978.000000 1218.000000) translate(0,11)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_27ed510" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4204.000000 1230.000000) translate(0,11)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_27ed770" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4193.000000 1215.000000) translate(0,11)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_27ed9b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4218.000000 1200.000000) translate(0,11)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_27edce0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4520.000000 1229.000000) translate(0,11)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_27edf40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4509.000000 1214.000000) translate(0,11)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_27ee180" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4534.000000 1199.000000) translate(0,11)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_27ee4b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3587.000000 49.000000) translate(0,11)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_27ee710" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3576.000000 34.000000) translate(0,11)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_27ee950" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3601.000000 19.000000) translate(0,11)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_27eec80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3690.000000 46.000000) translate(0,11)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_27eeee0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3679.000000 31.000000) translate(0,11)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_27ef120" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3704.000000 16.000000) translate(0,11)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_27ef450" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3797.000000 44.000000) translate(0,11)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_27ef6b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3786.000000 29.000000) translate(0,11)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_27ef8f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3811.000000 14.000000) translate(0,11)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_27efc20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4208.000000 51.000000) translate(0,11)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_27efe80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4197.000000 36.000000) translate(0,11)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_27f00c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4222.000000 21.000000) translate(0,11)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_27f03f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4314.000000 50.000000) translate(0,11)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_27f0650" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4303.000000 35.000000) translate(0,11)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_27f0890" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4328.000000 20.000000) translate(0,11)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_27f0bc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5298.000000 1127.000000) translate(0,11)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_27f0e20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5287.000000 1112.000000) translate(0,11)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_27f1060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5312.000000 1097.000000) translate(0,11)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_27f1390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5302.000000 1043.000000) translate(0,11)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_27f15f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5291.000000 1028.000000) translate(0,11)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_27f1830" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5316.000000 1013.000000) translate(0,11)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_27f1b60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5334.000000 962.000000) translate(0,11)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_27f1dc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5323.000000 947.000000) translate(0,11)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_27f2000" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5348.000000 932.000000) translate(0,11)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_27f2330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5317.000000 831.000000) translate(0,11)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_27f2590" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5306.000000 816.000000) translate(0,11)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_27f27d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5331.000000 801.000000) translate(0,11)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27f7460" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4074.000000 717.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27f76c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4099.000000 702.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27f7900" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4085.000000 732.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-28232">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3900.000000 -696.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4565" ObjectName="SW-CX_WM.CX_WM_1016SW"/>
     <cge:Meas_Ref ObjectId="28232"/>
    <cge:TPSR_Ref TObjectID="4565"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28235">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3923.000000 -754.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4568" ObjectName="SW-CX_WM.CX_WM_10160SW"/>
     <cge:Meas_Ref ObjectId="28235"/>
    <cge:TPSR_Ref TObjectID="4568"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28247">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3899.000000 -481.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4574" ObjectName="SW-CX_WM.CX_WM_0016SW"/>
     <cge:Meas_Ref ObjectId="28247"/>
    <cge:TPSR_Ref TObjectID="4574"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28246">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3899.000000 -394.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4573" ObjectName="SW-CX_WM.CX_WM_0011SW"/>
     <cge:Meas_Ref ObjectId="28246"/>
    <cge:TPSR_Ref TObjectID="4573"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4425.000000 -606.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4537.000000 -484.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4537.000000 -374.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4704.000000 -633.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4864.000000 -633.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28242">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4779.000000 -885.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4571" ObjectName="SW-CX_WM.CX_WM_3016SW"/>
     <cge:Meas_Ref ObjectId="28242"/>
    <cge:TPSR_Ref TObjectID="4571"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28241">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4876.000000 -885.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4570" ObjectName="SW-CX_WM.CX_WM_3011SW"/>
     <cge:Meas_Ref ObjectId="28241"/>
    <cge:TPSR_Ref TObjectID="4570"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28492">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3720.000000 -905.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4615" ObjectName="SW-CX_WM.CX_WM_1911SW"/>
     <cge:Meas_Ref ObjectId="28492"/>
    <cge:TPSR_Ref TObjectID="4615"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54985">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3720.000000 -1009.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9694" ObjectName="SW-CX_WM.CX_WM_1916SW"/>
     <cge:Meas_Ref ObjectId="54985"/>
    <cge:TPSR_Ref TObjectID="9694"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54987">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3742.000000 -1054.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9696" ObjectName="SW-CX_WM.CX_WM_19167SW"/>
     <cge:Meas_Ref ObjectId="54987"/>
    <cge:TPSR_Ref TObjectID="9696"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54986">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3742.000000 -997.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9695" ObjectName="SW-CX_WM.CX_WM_19160SW"/>
     <cge:Meas_Ref ObjectId="54986"/>
    <cge:TPSR_Ref TObjectID="9695"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54984">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3742.000000 -952.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9693" ObjectName="SW-CX_WM.CX_WM_19117SW"/>
     <cge:Meas_Ref ObjectId="54984"/>
    <cge:TPSR_Ref TObjectID="9693"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28391">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4989.000000 -935.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4596" ObjectName="SW-CX_WM.CX_WM_3741SW"/>
     <cge:Meas_Ref ObjectId="28391"/>
    <cge:TPSR_Ref TObjectID="4596"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28392">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5095.000000 -935.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4597" ObjectName="SW-CX_WM.CX_WM_3746SW"/>
     <cge:Meas_Ref ObjectId="28392"/>
    <cge:TPSR_Ref TObjectID="4597"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28336">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4984.000000 -785.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4584" ObjectName="SW-CX_WM.CX_WM_3121SW"/>
     <cge:Meas_Ref ObjectId="28336"/>
    <cge:TPSR_Ref TObjectID="4584"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4985.000000 -721.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4538.000000 -841.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4559.000000 -828.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4538.000000 -712.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28233">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3925.000000 -830.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4566" ObjectName="SW-CX_WM.CX_WM_10117SW"/>
     <cge:Meas_Ref ObjectId="28233"/>
    <cge:TPSR_Ref TObjectID="4566"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4559.000000 -699.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4558.000000 -770.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28320">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3874.000000 -924.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4577" ObjectName="SW-CX_WM.CX_WM_19010SW"/>
     <cge:Meas_Ref ObjectId="28320"/>
    <cge:TPSR_Ref TObjectID="4577"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28321">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3876.000000 -997.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4578" ObjectName="SW-CX_WM.CX_WM_19017SW"/>
     <cge:Meas_Ref ObjectId="28321"/>
    <cge:TPSR_Ref TObjectID="4578"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28192">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3998.000000 -906.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4558" ObjectName="SW-CX_WM.CX_WM_1921SW"/>
     <cge:Meas_Ref ObjectId="28192"/>
    <cge:TPSR_Ref TObjectID="4558"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28193">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3998.000000 -1010.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4559" ObjectName="SW-CX_WM.CX_WM_1926SW"/>
     <cge:Meas_Ref ObjectId="28193"/>
    <cge:TPSR_Ref TObjectID="4559"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28195">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4019.000000 -1054.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4561" ObjectName="SW-CX_WM.CX_WM_19267SW"/>
     <cge:Meas_Ref ObjectId="28195"/>
    <cge:TPSR_Ref TObjectID="4561"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28196">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4020.000000 -998.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4562" ObjectName="SW-CX_WM.CX_WM_19260SW"/>
     <cge:Meas_Ref ObjectId="28196"/>
    <cge:TPSR_Ref TObjectID="4562"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28194">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4020.000000 -953.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4560" ObjectName="SW-CX_WM.CX_WM_19217SW"/>
     <cge:Meas_Ref ObjectId="28194"/>
    <cge:TPSR_Ref TObjectID="4560"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28146">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4242.000000 -906.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4552" ObjectName="SW-CX_WM.CX_WM_1932SW"/>
     <cge:Meas_Ref ObjectId="28146"/>
    <cge:TPSR_Ref TObjectID="4552"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28147">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4242.000000 -1010.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4553" ObjectName="SW-CX_WM.CX_WM_1936SW"/>
     <cge:Meas_Ref ObjectId="28147"/>
    <cge:TPSR_Ref TObjectID="4553"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28149">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4263.000000 -1055.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4555" ObjectName="SW-CX_WM.CX_WM_19367SW"/>
     <cge:Meas_Ref ObjectId="28149"/>
    <cge:TPSR_Ref TObjectID="4555"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28150">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4263.000000 -998.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4556" ObjectName="SW-CX_WM.CX_WM_19360SW"/>
     <cge:Meas_Ref ObjectId="28150"/>
    <cge:TPSR_Ref TObjectID="4556"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28148">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4263.000000 -953.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4554" ObjectName="SW-CX_WM.CX_WM_19327SW"/>
     <cge:Meas_Ref ObjectId="28148"/>
    <cge:TPSR_Ref TObjectID="4554"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28324">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4403.000000 -923.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4580" ObjectName="SW-CX_WM.CX_WM_19020SW"/>
     <cge:Meas_Ref ObjectId="28324"/>
    <cge:TPSR_Ref TObjectID="4580"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28325">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4402.000000 -1000.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4581" ObjectName="SW-CX_WM.CX_WM_19027SW"/>
     <cge:Meas_Ref ObjectId="28325"/>
    <cge:TPSR_Ref TObjectID="4581"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-55060">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4552.000000 -907.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9697" ObjectName="SW-CX_WM.CX_WM_1942SW"/>
     <cge:Meas_Ref ObjectId="55060"/>
    <cge:TPSR_Ref TObjectID="9697"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-55061">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4552.000000 -1011.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9698" ObjectName="SW-CX_WM.CX_WM_1946SW"/>
     <cge:Meas_Ref ObjectId="55061"/>
    <cge:TPSR_Ref TObjectID="9698"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-55063">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4573.000000 -1056.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9700" ObjectName="SW-CX_WM.CX_WM_19467SW"/>
     <cge:Meas_Ref ObjectId="55063"/>
    <cge:TPSR_Ref TObjectID="9700"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-55064">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4573.000000 -999.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9701" ObjectName="SW-CX_WM.CX_WM_19460SW"/>
     <cge:Meas_Ref ObjectId="55064"/>
    <cge:TPSR_Ref TObjectID="9701"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-55062">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4573.000000 -953.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9699" ObjectName="SW-CX_WM.CX_WM_19427SW"/>
     <cge:Meas_Ref ObjectId="55062"/>
    <cge:TPSR_Ref TObjectID="9699"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28374">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4054.000000 -831.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4589" ObjectName="SW-CX_WM.CX_WM_1121SW"/>
     <cge:Meas_Ref ObjectId="28374"/>
    <cge:TPSR_Ref TObjectID="4589"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28376">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4054.000000 -763.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4591" ObjectName="SW-CX_WM.CX_WM_11217SW"/>
     <cge:Meas_Ref ObjectId="28376"/>
    <cge:TPSR_Ref TObjectID="4591"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28375">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4182.000000 -832.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4590" ObjectName="SW-CX_WM.CX_WM_1122SW"/>
     <cge:Meas_Ref ObjectId="28375"/>
    <cge:TPSR_Ref TObjectID="4590"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28377">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4182.000000 -764.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4592" ObjectName="SW-CX_WM.CX_WM_11227SW"/>
     <cge:Meas_Ref ObjectId="28377"/>
    <cge:TPSR_Ref TObjectID="4592"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28403">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4991.000000 -805.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4599" ObjectName="SW-CX_WM.CX_WM_3751SW"/>
     <cge:Meas_Ref ObjectId="28403"/>
    <cge:TPSR_Ref TObjectID="4599"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28404">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5097.000000 -805.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4600" ObjectName="SW-CX_WM.CX_WM_3756SW"/>
     <cge:Meas_Ref ObjectId="28404"/>
    <cge:TPSR_Ref TObjectID="4600"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4991.000000 -1156.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5097.000000 -1156.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28380">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5092.000000 -1012.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4594" ObjectName="SW-CX_WM.CX_WM_3736SW"/>
     <cge:Meas_Ref ObjectId="28380"/>
    <cge:TPSR_Ref TObjectID="4594"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28379">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4986.000000 -1012.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4593" ObjectName="SW-CX_WM.CX_WM_3731SW"/>
     <cge:Meas_Ref ObjectId="28379"/>
    <cge:TPSR_Ref TObjectID="4593"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4990.000000 -698.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5096.000000 -698.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4813.000000 -303.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4813.000000 -211.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4830.000000 -133.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4991.000000 -620.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5097.000000 -620.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5097.000000 -546.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4991.000000 -546.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4994.000000 -474.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5100.000000 -474.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4999.000000 -399.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5105.000000 -399.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4981.000000 -306.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4981.000000 -214.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4998.000000 -136.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4528.000000 -298.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4528.000000 -135.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 4603.000000 -302.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 4603.000000 -139.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 4678.000000 -302.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 4678.000000 -139.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4751.000000 -302.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4751.000000 -139.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5119.000000 -301.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5238.000000 -297.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28361">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4370.000000 -373.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4587" ObjectName="SW-CX_WM.CX_WM_0121SW"/>
     <cge:Meas_Ref ObjectId="28361"/>
    <cge:TPSR_Ref TObjectID="4587"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4488.000000 -372.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-95791">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3638.000000 -294.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20062" ObjectName="SW-CX_WM.CX_WM_0711SW"/>
     <cge:Meas_Ref ObjectId="95791"/>
    <cge:TPSR_Ref TObjectID="20062"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-95792">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3638.000000 -131.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20063" ObjectName="SW-CX_WM.CX_WM_0716SW"/>
     <cge:Meas_Ref ObjectId="95792"/>
    <cge:TPSR_Ref TObjectID="20063"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-95806">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 3737.000000 -298.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20065" ObjectName="SW-CX_WM.CX_WM_0721SW"/>
     <cge:Meas_Ref ObjectId="95806"/>
    <cge:TPSR_Ref TObjectID="20065"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-95807">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 3737.000000 -135.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20066" ObjectName="SW-CX_WM.CX_WM_0726SW"/>
     <cge:Meas_Ref ObjectId="95807"/>
    <cge:TPSR_Ref TObjectID="20066"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28448">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3934.000000 -300.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4608" ObjectName="SW-CX_WM.CX_WM_0741SW"/>
     <cge:Meas_Ref ObjectId="28448"/>
    <cge:TPSR_Ref TObjectID="4608"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28451">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3934.000000 -208.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4611" ObjectName="SW-CX_WM.CX_WM_0743SW"/>
     <cge:Meas_Ref ObjectId="28451"/>
    <cge:TPSR_Ref TObjectID="4611"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28455">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3951.000000 -135.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4613" ObjectName="SW-CX_WM.CX_WM_07460SW"/>
     <cge:Meas_Ref ObjectId="28455"/>
    <cge:TPSR_Ref TObjectID="4613"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28360">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4073.000000 -300.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4586" ObjectName="SW-CX_WM.CX_WM_0751SW"/>
     <cge:Meas_Ref ObjectId="28360"/>
    <cge:TPSR_Ref TObjectID="4586"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28358">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4161.000000 -297.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4585" ObjectName="SW-CX_WM.CX_WM_0901SW"/>
     <cge:Meas_Ref ObjectId="28358"/>
    <cge:TPSR_Ref TObjectID="4585"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28415">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4251.000000 -296.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4602" ObjectName="SW-CX_WM.CX_WM_0761SW"/>
     <cge:Meas_Ref ObjectId="28415"/>
    <cge:TPSR_Ref TObjectID="4602"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28416">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4251.000000 -133.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4603" ObjectName="SW-CX_WM.CX_WM_0766SW"/>
     <cge:Meas_Ref ObjectId="28416"/>
    <cge:TPSR_Ref TObjectID="4603"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28429">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4350.000000 -299.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4605" ObjectName="SW-CX_WM.CX_WM_0771SW"/>
     <cge:Meas_Ref ObjectId="28429"/>
    <cge:TPSR_Ref TObjectID="4605"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28430">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4350.000000 -136.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4606" ObjectName="SW-CX_WM.CX_WM_0776SW"/>
     <cge:Meas_Ref ObjectId="28430"/>
    <cge:TPSR_Ref TObjectID="4606"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28319">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3853.000000 -941.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4576" ObjectName="SW-CX_WM.CX_WM_1901SW"/>
     <cge:Meas_Ref ObjectId="28319"/>
    <cge:TPSR_Ref TObjectID="4576"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28323">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4381.000000 -945.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4579" ObjectName="SW-CX_WM.CX_WM_1902SW"/>
     <cge:Meas_Ref ObjectId="28323"/>
    <cge:TPSR_Ref TObjectID="4579"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28234">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3923.000000 -670.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4567" ObjectName="SW-CX_WM.CX_WM_10167SW"/>
     <cge:Meas_Ref ObjectId="28234"/>
    <cge:TPSR_Ref TObjectID="4567"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28449">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3934.000000 -88.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4609" ObjectName="SW-CX_WM.CX_WM_0746SW"/>
     <cge:Meas_Ref ObjectId="28449"/>
    <cge:TPSR_Ref TObjectID="4609"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4813.000000 -86.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4981.000000 -89.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28231">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3900.000000 -843.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4564" ObjectName="SW-CX_WM.CX_WM_1011SW"/>
     <cge:Meas_Ref ObjectId="28231"/>
    <cge:TPSR_Ref TObjectID="4564"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4898.000000 -437.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28334">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4906.000000 -1135.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4583" ObjectName="SW-CX_WM.CX_WM_3901SW"/>
     <cge:Meas_Ref ObjectId="28334"/>
    <cge:TPSR_Ref TObjectID="4583"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28450">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3975.000000 -79.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4610" ObjectName="SW-CX_WM.CX_WM_07467SW"/>
     <cge:Meas_Ref ObjectId="28450"/>
    <cge:TPSR_Ref TObjectID="4610"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3976.000000 -52.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4854.000000 -77.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4855.000000 -50.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5022.000000 -80.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5023.000000 -53.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-95821">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 3836.000000 -293.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20068" ObjectName="SW-CX_WM.CX_WM_0731SW"/>
     <cge:Meas_Ref ObjectId="95821"/>
    <cge:TPSR_Ref TObjectID="20068"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-95822">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 3836.000000 -130.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20069" ObjectName="SW-CX_WM.CX_WM_0736SW"/>
     <cge:Meas_Ref ObjectId="95822"/>
    <cge:TPSR_Ref TObjectID="20069"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193559">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4986.000000 -1084.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29440" ObjectName="SW-CX_WM.CX_WM_3721SW"/>
     <cge:Meas_Ref ObjectId="193559"/>
    <cge:TPSR_Ref TObjectID="29440"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193560">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5092.000000 -1084.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29441" ObjectName="SW-CX_WM.CX_WM_3726SW"/>
     <cge:Meas_Ref ObjectId="193560"/>
    <cge:TPSR_Ref TObjectID="29441"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28250">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3795.000000 -551.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4575" ObjectName="SW-CX_WM.CX_WM_1010SW"/>
     <cge:Meas_Ref ObjectId="28250"/>
    <cge:TPSR_Ref TObjectID="4575"/></metadata>
   </g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-CX_WM.CX_WM_1C">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3916.000000 42.000000)" xlink:href="#capacitor:shape26"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12211" ObjectName="CB-CX_WM.CX_WM_1C"/>
    <cge:TPSR_Ref TObjectID="12211"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4795.000000 44.000000)" xlink:href="#capacitor:shape26"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4963.000000 41.000000)" xlink:href="#capacitor:shape26"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_22529c0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3826.000000 -542.000000)" xlink:href="#lightningRod:shape54"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2264740">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3848.000000 -546.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2247f30">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4451.000000 -588.000000)" xlink:href="#lightningRod:shape54"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21fa3b0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4473.000000 -591.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2269730">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4472.000000 -540.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_223bc00">
    <use class="BV-110KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 3711.000000 -1062.000000)" xlink:href="#lightningRod:shape105"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2218ee0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3855.000000 -1090.000000)" xlink:href="#lightningRod:shape57"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2219250">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5232.000000 -865.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_225b220">
    <use class="BV-110KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 3989.000000 -1063.000000)" xlink:href="#lightningRod:shape105"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21ae550">
    <use class="BV-110KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4233.000000 -1063.000000)" xlink:href="#lightningRod:shape105"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2187010">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4383.000000 -1089.000000)" xlink:href="#lightningRod:shape57"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21e0430">
    <use class="BV-110KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4543.000000 -1064.000000)" xlink:href="#lightningRod:shape105"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2112dd0">
    <use class="BV-35KV" transform="matrix(0.984375 -0.000000 0.000000 -0.933333 5166.000000 -956.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_211a320">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5254.000000 -770.000000)" xlink:href="#lightningRod:shape59"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_211b3b0">
    <use class="BV-35KV" transform="matrix(0.984375 -0.000000 0.000000 -0.933333 5168.000000 -827.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21946d0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5254.000000 -1121.000000)" xlink:href="#lightningRod:shape59"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2195760">
    <use class="BV-0KV" transform="matrix(0.984375 -0.000000 0.000000 -0.933333 5168.000000 -1178.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21c0260">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5249.000000 -977.000000)" xlink:href="#lightningRod:shape59"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21c12f0">
    <use class="BV-35KV" transform="matrix(0.984375 -0.000000 0.000000 -0.933333 5163.000000 -1034.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_220b2f0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5253.000000 -663.000000)" xlink:href="#lightningRod:shape59"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22022c0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4817.000000 -146.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2226d90">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5254.000000 -585.000000)" xlink:href="#lightningRod:shape59"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2227e20">
    <use class="BV-0KV" transform="matrix(0.984375 -0.000000 0.000000 -0.933333 5168.000000 -641.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_219f2f0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5254.110577 -511.000000)" xlink:href="#lightningRod:shape59"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21a0380">
    <use class="BV-0KV" transform="matrix(0.984375 -0.000000 0.000000 -0.933333 5169.000000 -567.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21a35c0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5257.110577 -439.000000)" xlink:href="#lightningRod:shape59"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21a4650">
    <use class="BV-0KV" transform="matrix(0.984375 -0.000000 0.000000 -0.933333 5172.000000 -495.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20a5d20">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5262.110577 -364.000000)" xlink:href="#lightningRod:shape59"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20a6e00">
    <use class="BV-0KV" transform="matrix(0.984375 -0.000000 0.000000 -0.933333 5177.000000 -420.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2128d30">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4985.000000 -151.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2133870">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4532.000000 -182.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_213b490">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4606.333333 -186.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2143070">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4681.166667 -186.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_214ac50">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4755.000000 -186.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2308aa0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 5137.000000 -296.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2309320">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5123.000000 -180.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_230bd40">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5217.000000 -205.000000)" xlink:href="#lightningRod:shape61"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_230fc50">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4814.500000 -1170.500000)" xlink:href="#lightningRod:shape61"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2311500">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4803.500000 -471.500000)" xlink:href="#lightningRod:shape61"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2151c40">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3642.000000 -178.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2159940">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3740.333333 -182.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2161640">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3938.000000 -144.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21676e0">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4091.000000 -294.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2167f60">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4077.000000 -182.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_216a650">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4140.000000 -205.000000)" xlink:href="#lightningRod:shape61"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23f5460">
    <use class="BV-0KV" transform="matrix(0.984375 -0.000000 0.000000 -0.933333 5167.000000 -720.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23fa450">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3749.000000 -1073.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23faed0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4027.000000 -1074.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23fbb80">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4271.000000 -1074.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23fc880">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4581.000000 -1075.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23fd5f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3920.000000 -528.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23fe360">
    <use class="BV-10KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4277.500000 -124.500000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23ff0d0">
    <use class="BV-10KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4375.500000 -126.500000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2401870">
    <use class="BV-10KV" transform="matrix(0.500000 -0.000000 0.000000 -1.000000 4255.000000 -189.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24020b0">
    <use class="BV-10KV" transform="matrix(0.500000 -0.000000 0.000000 -1.000000 4354.000000 -189.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_247f700">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3839.333333 -177.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24813a0">
    <use class="BV-10KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3861.500000 -119.500000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2482110">
    <use class="BV-10KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3762.500000 -123.500000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2482e80">
    <use class="BV-10KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3664.500000 -119.500000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_27b6c10">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5265.000000 -895.000000)" xlink:href="#lightningRod:shape59"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_27cbe10">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5249.000000 -1049.000000)" xlink:href="#lightningRod:shape59"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_27ccef0">
    <use class="BV-35KV" transform="matrix(0.984375 -0.000000 0.000000 -0.933333 5163.000000 -1106.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-28131" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3743.000000 -1244.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28131" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4614"/>
     <cge:Term_Ref ObjectID="6683"/>
    <cge:TPSR_Ref TObjectID="4614"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-28132" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3743.000000 -1244.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28132" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4614"/>
     <cge:Term_Ref ObjectID="6683"/>
    <cge:TPSR_Ref TObjectID="4614"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-28126" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3743.000000 -1244.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28126" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4614"/>
     <cge:Term_Ref ObjectID="6683"/>
    <cge:TPSR_Ref TObjectID="4614"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-27975" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3842.000000 -823.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27975" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4563"/>
     <cge:Term_Ref ObjectID="6581"/>
    <cge:TPSR_Ref TObjectID="4563"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-27976" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3842.000000 -823.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27976" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4563"/>
     <cge:Term_Ref ObjectID="6581"/>
    <cge:TPSR_Ref TObjectID="4563"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-27971" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3842.000000 -823.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27971" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4563"/>
     <cge:Term_Ref ObjectID="6581"/>
    <cge:TPSR_Ref TObjectID="4563"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-27990" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4018.000000 -482.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27990" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4572"/>
     <cge:Term_Ref ObjectID="6599"/>
    <cge:TPSR_Ref TObjectID="4572"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-27991" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4018.000000 -482.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27991" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4572"/>
     <cge:Term_Ref ObjectID="6599"/>
    <cge:TPSR_Ref TObjectID="4572"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-27986" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4018.000000 -482.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27986" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4572"/>
     <cge:Term_Ref ObjectID="6599"/>
    <cge:TPSR_Ref TObjectID="4572"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-27982" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4868.000000 -861.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27982" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4569"/>
     <cge:Term_Ref ObjectID="6593"/>
    <cge:TPSR_Ref TObjectID="4569"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-27983" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4868.000000 -861.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27983" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4569"/>
     <cge:Term_Ref ObjectID="6593"/>
    <cge:TPSR_Ref TObjectID="4569"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-27978" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4868.000000 -861.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27978" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4569"/>
     <cge:Term_Ref ObjectID="6593"/>
    <cge:TPSR_Ref TObjectID="4569"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-28060" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5388.000000 -963.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28060" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4598"/>
     <cge:Term_Ref ObjectID="6651"/>
    <cge:TPSR_Ref TObjectID="4598"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-28061" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5388.000000 -963.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28061" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4598"/>
     <cge:Term_Ref ObjectID="6651"/>
    <cge:TPSR_Ref TObjectID="4598"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-54958" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5388.000000 -963.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="54958" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4598"/>
     <cge:Term_Ref ObjectID="6651"/>
    <cge:TPSR_Ref TObjectID="4598"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-27967" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4020.000000 -1247.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27967" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4557"/>
     <cge:Term_Ref ObjectID="6569"/>
    <cge:TPSR_Ref TObjectID="4557"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-27968" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4020.000000 -1247.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27968" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4557"/>
     <cge:Term_Ref ObjectID="6569"/>
    <cge:TPSR_Ref TObjectID="4557"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-27962" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4020.000000 -1247.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27962" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4557"/>
     <cge:Term_Ref ObjectID="6569"/>
    <cge:TPSR_Ref TObjectID="4557"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-27958" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4262.000000 -1231.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27958" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4551"/>
     <cge:Term_Ref ObjectID="6557"/>
    <cge:TPSR_Ref TObjectID="4551"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-27959" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4262.000000 -1231.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27959" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4551"/>
     <cge:Term_Ref ObjectID="6557"/>
    <cge:TPSR_Ref TObjectID="4551"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-27953" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4262.000000 -1231.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27953" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4551"/>
     <cge:Term_Ref ObjectID="6557"/>
    <cge:TPSR_Ref TObjectID="4551"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-28140" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4575.000000 -1228.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28140" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4616"/>
     <cge:Term_Ref ObjectID="6687"/>
    <cge:TPSR_Ref TObjectID="4616"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-28141" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4575.000000 -1228.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28141" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4616"/>
     <cge:Term_Ref ObjectID="6687"/>
    <cge:TPSR_Ref TObjectID="4616"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-28135" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4575.000000 -1228.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28135" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4616"/>
     <cge:Term_Ref ObjectID="6687"/>
    <cge:TPSR_Ref TObjectID="4616"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-28050" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4143.000000 -729.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28050" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4588"/>
     <cge:Term_Ref ObjectID="6631"/>
    <cge:TPSR_Ref TObjectID="4588"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-28051" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4143.000000 -729.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28051" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4588"/>
     <cge:Term_Ref ObjectID="6631"/>
    <cge:TPSR_Ref TObjectID="4588"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-28046" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4143.000000 -729.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28046" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4588"/>
     <cge:Term_Ref ObjectID="6631"/>
    <cge:TPSR_Ref TObjectID="4588"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-28067" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5373.500000 -831.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28067" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4601"/>
     <cge:Term_Ref ObjectID="6657"/>
    <cge:TPSR_Ref TObjectID="4601"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-28068" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5373.500000 -831.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28068" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4601"/>
     <cge:Term_Ref ObjectID="6657"/>
    <cge:TPSR_Ref TObjectID="4601"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-28063" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5373.500000 -831.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28063" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4601"/>
     <cge:Term_Ref ObjectID="6657"/>
    <cge:TPSR_Ref TObjectID="4601"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-28057" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5356.000000 -1039.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28057" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4595"/>
     <cge:Term_Ref ObjectID="6645"/>
    <cge:TPSR_Ref TObjectID="4595"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-28058" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5356.000000 -1039.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28058" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4595"/>
     <cge:Term_Ref ObjectID="6645"/>
    <cge:TPSR_Ref TObjectID="4595"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-28053" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5356.000000 -1039.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28053" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4595"/>
     <cge:Term_Ref ObjectID="6645"/>
    <cge:TPSR_Ref TObjectID="4595"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-28074" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4263.000000 -50.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28074" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4604"/>
     <cge:Term_Ref ObjectID="6663"/>
    <cge:TPSR_Ref TObjectID="4604"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-28075" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4263.000000 -50.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28075" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4604"/>
     <cge:Term_Ref ObjectID="6663"/>
    <cge:TPSR_Ref TObjectID="4604"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-28070" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4263.000000 -50.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28070" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4604"/>
     <cge:Term_Ref ObjectID="6663"/>
    <cge:TPSR_Ref TObjectID="4604"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-28081" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4368.000000 -50.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28081" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4607"/>
     <cge:Term_Ref ObjectID="6669"/>
    <cge:TPSR_Ref TObjectID="4607"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-28082" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4368.000000 -50.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28082" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4607"/>
     <cge:Term_Ref ObjectID="6669"/>
    <cge:TPSR_Ref TObjectID="4607"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-28077" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4368.000000 -50.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28077" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4607"/>
     <cge:Term_Ref ObjectID="6669"/>
    <cge:TPSR_Ref TObjectID="4607"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-28088" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4058.000000 -0.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28088" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4612"/>
     <cge:Term_Ref ObjectID="6679"/>
    <cge:TPSR_Ref TObjectID="4612"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-28084" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4058.000000 -0.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28084" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4612"/>
     <cge:Term_Ref ObjectID="6679"/>
    <cge:TPSR_Ref TObjectID="4612"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-28020" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3662.000000 -481.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28020" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4550"/>
     <cge:Term_Ref ObjectID="6556"/>
    <cge:TPSR_Ref TObjectID="4550"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-28021" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3662.000000 -481.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28021" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4550"/>
     <cge:Term_Ref ObjectID="6556"/>
    <cge:TPSR_Ref TObjectID="4550"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-28022" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3662.000000 -481.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28022" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4550"/>
     <cge:Term_Ref ObjectID="6556"/>
    <cge:TPSR_Ref TObjectID="4550"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-28026" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3662.000000 -481.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28026" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4550"/>
     <cge:Term_Ref ObjectID="6556"/>
    <cge:TPSR_Ref TObjectID="4550"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-28023" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3662.000000 -481.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28023" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4550"/>
     <cge:Term_Ref ObjectID="6556"/>
    <cge:TPSR_Ref TObjectID="4550"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-28027" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3662.000000 -481.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28027" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4550"/>
     <cge:Term_Ref ObjectID="6556"/>
    <cge:TPSR_Ref TObjectID="4550"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-27996" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3627.000000 -938.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27996" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4547"/>
     <cge:Term_Ref ObjectID="6553"/>
    <cge:TPSR_Ref TObjectID="4547"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-27997" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3627.000000 -938.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27997" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4547"/>
     <cge:Term_Ref ObjectID="6553"/>
    <cge:TPSR_Ref TObjectID="4547"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-27998" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3627.000000 -938.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27998" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4547"/>
     <cge:Term_Ref ObjectID="6553"/>
    <cge:TPSR_Ref TObjectID="4547"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-28002" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3627.000000 -938.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28002" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4547"/>
     <cge:Term_Ref ObjectID="6553"/>
    <cge:TPSR_Ref TObjectID="4547"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-27999" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3627.000000 -938.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27999" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4547"/>
     <cge:Term_Ref ObjectID="6553"/>
    <cge:TPSR_Ref TObjectID="4547"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-28003" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3627.000000 -938.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28003" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4547"/>
     <cge:Term_Ref ObjectID="6553"/>
    <cge:TPSR_Ref TObjectID="4547"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-28004" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4753.000000 -1014.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28004" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4548"/>
     <cge:Term_Ref ObjectID="6554"/>
    <cge:TPSR_Ref TObjectID="4548"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-28005" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4753.000000 -1014.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28005" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4548"/>
     <cge:Term_Ref ObjectID="6554"/>
    <cge:TPSR_Ref TObjectID="4548"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-28006" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4753.000000 -1014.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28006" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4548"/>
     <cge:Term_Ref ObjectID="6554"/>
    <cge:TPSR_Ref TObjectID="4548"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-28010" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4753.000000 -1014.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28010" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4548"/>
     <cge:Term_Ref ObjectID="6554"/>
    <cge:TPSR_Ref TObjectID="4548"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-28007" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4753.000000 -1014.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28007" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4548"/>
     <cge:Term_Ref ObjectID="6554"/>
    <cge:TPSR_Ref TObjectID="4548"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-28011" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4753.000000 -1014.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28011" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4548"/>
     <cge:Term_Ref ObjectID="6554"/>
    <cge:TPSR_Ref TObjectID="4548"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="44" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-27995" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4076.000000 -632.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27995" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4617"/>
     <cge:Term_Ref ObjectID="6691"/>
    <cge:TPSR_Ref TObjectID="4617"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="44" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-27994" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4076.000000 -632.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27994" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4617"/>
     <cge:Term_Ref ObjectID="6691"/>
    <cge:TPSR_Ref TObjectID="4617"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-28012" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4908.000000 -1267.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28012" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4549"/>
     <cge:Term_Ref ObjectID="6555"/>
    <cge:TPSR_Ref TObjectID="4549"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-28013" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4908.000000 -1267.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28013" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4549"/>
     <cge:Term_Ref ObjectID="6555"/>
    <cge:TPSR_Ref TObjectID="4549"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-28014" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4908.000000 -1267.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28014" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4549"/>
     <cge:Term_Ref ObjectID="6555"/>
    <cge:TPSR_Ref TObjectID="4549"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-28018" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4908.000000 -1267.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28018" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4549"/>
     <cge:Term_Ref ObjectID="6555"/>
    <cge:TPSR_Ref TObjectID="4549"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-28015" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4908.000000 -1267.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28015" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4549"/>
     <cge:Term_Ref ObjectID="6555"/>
    <cge:TPSR_Ref TObjectID="4549"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-28019" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4908.000000 -1267.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28019" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4549"/>
     <cge:Term_Ref ObjectID="6555"/>
    <cge:TPSR_Ref TObjectID="4549"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-95774" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3637.000000 -47.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="95774" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20064"/>
     <cge:Term_Ref ObjectID="28016"/>
    <cge:TPSR_Ref TObjectID="20064"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-95775" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3637.000000 -47.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="95775" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20064"/>
     <cge:Term_Ref ObjectID="28016"/>
    <cge:TPSR_Ref TObjectID="20064"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-95770" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3637.000000 -47.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="95770" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20064"/>
     <cge:Term_Ref ObjectID="28016"/>
    <cge:TPSR_Ref TObjectID="20064"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-95781" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3739.000000 -46.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="95781" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20067"/>
     <cge:Term_Ref ObjectID="28022"/>
    <cge:TPSR_Ref TObjectID="20067"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-95782" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3739.000000 -46.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="95782" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20067"/>
     <cge:Term_Ref ObjectID="28022"/>
    <cge:TPSR_Ref TObjectID="20067"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-95777" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3739.000000 -46.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="95777" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20067"/>
     <cge:Term_Ref ObjectID="28022"/>
    <cge:TPSR_Ref TObjectID="20067"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-95788" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3849.000000 -43.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="95788" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20070"/>
     <cge:Term_Ref ObjectID="28028"/>
    <cge:TPSR_Ref TObjectID="20070"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-95789" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3849.000000 -43.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="95789" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20070"/>
     <cge:Term_Ref ObjectID="28028"/>
    <cge:TPSR_Ref TObjectID="20070"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-95784" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3849.000000 -43.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="95784" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20070"/>
     <cge:Term_Ref ObjectID="28028"/>
    <cge:TPSR_Ref TObjectID="20070"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-193586" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5352.000000 -1126.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="193586" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29442"/>
     <cge:Term_Ref ObjectID="41936"/>
    <cge:TPSR_Ref TObjectID="29442"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-193587" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5352.000000 -1126.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="193587" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29442"/>
     <cge:Term_Ref ObjectID="41936"/>
    <cge:TPSR_Ref TObjectID="29442"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-193583" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5352.000000 -1126.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="193583" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29442"/>
     <cge:Term_Ref ObjectID="41936"/>
    <cge:TPSR_Ref TObjectID="29442"/></metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="3238" y="-1178"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3189" y="-1195"/></g>
   <g href="AVC万马站.svg" style="fill-opacity:0"><rect height="46" qtmmishow="hidden" width="92" x="3390" y="-1182"/></g>
   <g href="cx_地调_重要用电用户表.svg" style="fill-opacity:0"><rect height="22" qtmmishow="hidden" width="22" x="4040" y="-1196"/></g>
   <g href="110kV万马变110kV迤万线191断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="3692" y="-986"/></g>
   <g href="110kV万马变110kV永干万线192断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="3972" y="-987"/></g>
   <g href="110kV万马变110kV分段112断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="24" x="4113" y="-845"/></g>
   <g href="110kV万马变110kV永万的线193断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4215" y="-987"/></g>
   <g href="110kV万马变110kV多万线194断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4529" y="-989"/></g>
   <g href="110kV万马变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="63" x="3971" y="-581"/></g>
   <g href="110kV万马变35kV马湾线373断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="5050" y="-1041"/></g>
   <g href="110kV万马变35kV万他马红线374断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="5053" y="-964"/></g>
   <g href="110kV万马变35kV万中线375断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="5055" y="-834"/></g>
   <g href="110kV万马变10kV立溪冬线071断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="3656" y="-269"/></g>
   <g href="110kV万马变10kV丙和良线072断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="3754" y="-273"/></g>
   <g href="110kV万马变10kV进化线073断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="3853" y="-268"/></g>
   <g href="110kV万马变10kV1号电容器074断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="3952" y="-285"/></g>
   <g href="110kV万马变10kV团山线076断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4269" y="-271"/></g>
   <g href="110kV万马变10kV昔丙线077断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4368" y="-274"/></g>
   <g href="110kV万马变GG虚设备间隔接线图_0.svg" style="fill-opacity:0"><rect height="24" qtmmishow="hidden" width="96" x="3166" y="-803"/></g>
   <g href="110kV万马变35kV万兴线372断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="5050" y="-1113"/></g>
   <g href="cx_配调_配网接线图110.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3505" y="-1158"/></g>
   <g href="cx_索引_接线图_局属变110.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3505" y="-1193"/></g>
   <g href="110kV万马变隔刀开关远方遥控清单.svg" style="fill-opacity:0"><rect height="24" qtmmishow="hidden" width="96" x="3166" y="-716"/></g>
  </g><g id="IosButton_Layer">
   <g DF8003:Layer="PUBLIC">
    <polygon fill="rgb(255,255,255)" points="3390,-1183 3387,-1186 3387,-1131 3390,-1134 3390,-1183" stroke="rgb(255,255,255)"/>
    <polygon fill="rgb(255,255,255)" points="3390,-1183 3387,-1186 3485,-1186 3482,-1183 3390,-1183" stroke="rgb(255,255,255)"/>
    <polygon fill="rgb(127,127,127)" points="3390,-1134 3387,-1131 3485,-1131 3482,-1134 3390,-1134" stroke="rgb(127,127,127)"/>
    <polygon fill="rgb(127,127,127)" points="3482,-1183 3485,-1186 3485,-1131 3482,-1134 3482,-1183" stroke="rgb(127,127,127)"/>
    <rect fill="rgb(255,255,255)" height="49" stroke="rgb(255,255,255)" width="92" x="3390" y="-1183"/>
   <metadata/><rect fill="white" height="49" opacity="0" stroke="white" transform="" width="92" x="3390" y="-1183"/></g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_WM.CX_WM_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3605,-358 4416,-358 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="4550" ObjectName="BS-CX_WM.CX_WM_9IM"/>
    <cge:TPSR_Ref TObjectID="4550"/></metadata>
   <polyline fill="none" opacity="0" points="3605,-358 4416,-358 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_WM.CX_WM_1IM">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3677,-896 4083,-896 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="4547" ObjectName="BS-CX_WM.CX_WM_1IM"/>
    <cge:TPSR_Ref TObjectID="4547"/></metadata>
   <polyline fill="none" opacity="0" points="3677,-896 4083,-896 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_WM.CX_WM_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4968,-1190 4968,-779 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="4549" ObjectName="BS-CX_WM.CX_WM_3IM"/>
    <cge:TPSR_Ref TObjectID="4549"/></metadata>
   <polyline fill="none" opacity="0" points="4968,-1190 4968,-779 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4969,-743 4969,-385 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4969,-743 4969,-385 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_WM.CX_WM_1IIM">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4163,-897 4674,-897 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="4548" ObjectName="BS-CX_WM.CX_WM_1IIM"/>
    <cge:TPSR_Ref TObjectID="4548"/></metadata>
   <polyline fill="none" opacity="0" points="4163,-897 4674,-897 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4478,-359 5300,-359 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4478,-359 5300,-359 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3226.000000 -1119.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-62638" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3260.538462 -963.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="62638" ObjectName="CX_WM:CX_WM_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-61618" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3260.538462 -922.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="61618" ObjectName="CX_WM:CX_WM_sumQ"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-27975" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3259.538462 -1040.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27975" ObjectName="CX_WM:CX_WM_101BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-62638" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3259.538462 -1000.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="62638" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-28129" ratioFlag="0">
    <text fill="rgb(0,205,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3671.000000 -1057.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28129" ObjectName="CX_WM:CX_WM_191BK_U"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-27965" ratioFlag="0">
    <text fill="rgb(0,205,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3962.000000 -1128.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27965" ObjectName="CX_WM:CX_WM_192BK_U"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-27956" ratioFlag="0">
    <text fill="rgb(0,205,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4204.000000 -1132.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27956" ObjectName="CX_WM:CX_WM_193BK_U"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-28138" ratioFlag="0">
    <text fill="rgb(0,205,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4498.000000 -1133.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28138" ObjectName="CX_WM:CX_WM_194BK_U"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-28056" ratioFlag="0">
    <text fill="rgb(0,205,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5306.000000 -991.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28056" ObjectName="CX_WM:CX_WM_373BK_Uab"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-227497" ratioFlag="0">
    <text fill="rgb(0,205,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5309.000000 -1071.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="227497" ObjectName="CX_WM:CX_WM_372BK_Uab"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-54962" ratioFlag="0">
    <text fill="rgb(0,205,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5325.000000 -910.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="54962" ObjectName="CX_WM:CX_WM_374BK_Uab"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-28066" ratioFlag="0">
    <text fill="rgb(0,205,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5287.000000 -769.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28066" ObjectName="CX_WM:CX_WM_375BK_Uab"/>
    </metadata>
   </g>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-1199"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-599"/>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="3238" y="-1178"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="3238" y="-1178"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3189" y="-1195"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3189" y="-1195"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="46" qtmmishow="hidden" width="92" x="3390" y="-1182"/>
    </a>
   <metadata/><rect fill="white" height="46" opacity="0" stroke="white" transform="" width="92" x="3390" y="-1182"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="22" qtmmishow="hidden" width="22" x="4040" y="-1196"/>
    </a>
   <metadata/><rect fill="white" height="22" opacity="0" stroke="white" transform="" width="22" x="4040" y="-1196"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="3692" y="-986"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="3692" y="-986"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="3972" y="-987"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="3972" y="-987"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="24" x="4113" y="-845"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="24" x="4113" y="-845"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4215" y="-987"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4215" y="-987"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4529" y="-989"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4529" y="-989"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="63" x="3971" y="-581"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="63" x="3971" y="-581"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="5050" y="-1041"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="5050" y="-1041"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="5053" y="-964"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="5053" y="-964"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="5055" y="-834"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="5055" y="-834"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="3656" y="-269"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="3656" y="-269"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="3754" y="-273"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="3754" y="-273"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="3853" y="-268"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="3853" y="-268"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="3952" y="-285"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="3952" y="-285"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4269" y="-271"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4269" y="-271"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4368" y="-274"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4368" y="-274"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="24" qtmmishow="hidden" width="96" x="3166" y="-803"/>
    </a>
   <metadata/><rect fill="white" height="24" opacity="0" stroke="white" transform="" width="96" x="3166" y="-803"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="5050" y="-1113"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="5050" y="-1113"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3505" y="-1158"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3505" y="-1158"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3505" y="-1193"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3505" y="-1193"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="24" qtmmishow="hidden" width="96" x="3166" y="-716"/>
    </a>
   <metadata/><rect fill="white" height="24" opacity="0" stroke="white" transform="" width="96" x="3166" y="-716"/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5355.000000 -839.000000)" xlink:href="#transformer2:shape12_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5355.000000 -839.000000)" xlink:href="#transformer2:shape12_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 5097.500000 -75.500000)" xlink:href="#transformer2:shape12_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 5097.500000 -75.500000)" xlink:href="#transformer2:shape12_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4051.500000 -74.500000)" xlink:href="#transformer2:shape12_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4051.500000 -74.500000)" xlink:href="#transformer2:shape12_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_WM"/>
</svg>