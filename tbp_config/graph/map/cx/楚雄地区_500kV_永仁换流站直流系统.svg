<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-183" aopId="1835014" id="thSvg" product="E8000V2" version="1.0" viewBox="3522 -1366 2350 1588">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="36" y1="15" y2="4"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="36" y1="15" y2="4"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="capacitor:shape5">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.484375" x1="31" x2="0" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.484375" x1="31" x2="0" y1="19" y2="19"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="earth:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="14" y2="14"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape105">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="13" x2="13" y1="39" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.44" x1="13" x2="13" y1="5" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="0" x2="12" y1="26" y2="26"/>
    <polyline points="13,39 15,39 17,38 18,38 20,37 21,36 23,35 24,33 25,31 25,30 26,28 26,26 26,24 25,22 25,21 24,19 23,18 21,16 20,15 18,14 17,14 15,13 13,13 11,13 9,14 8,14 6,15 5,16 3,18 2,19 1,21 1,22 0,24 0,26 " stroke-width="0.0972"/>
   </symbol>
   <symbol id="lightningRod:shape180">
    <polyline arcFlag="1" points="5,12 5,10 5,9 5,8 6,7 6,6 7,5 8,4 9,3 10,2 12,1 13,1 15,1 16,0 18,0 19,1 21,1 22,1 24,2 25,3 26,4 27,5 28,6 29,7 29,8 29,9 29,10 29,12 " stroke-width="0.402481"/>
   </symbol>
   <symbol id="lightningRod:shape124">
    <rect height="61" stroke-width="0.978236" width="39" x="1" y="5"/>
    <polyline DF8003:Layer="PUBLIC" points="12,25 28,25 20,37 12,25 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="21" x2="21" y1="63" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="20" x2="20" y1="66" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="28" y1="42" y2="42"/>
   </symbol>
   <symbol id="reactance:shape0">
    <polyline arcFlag="1" points="13,13 15,13 17,14 18,14 20,15 21,16 23,17 24,19 25,21 25,22 26,24 26,26 26,28 25,30 25,31 24,33 23,34 21,36 20,37 18,38 17,38 15,39 13,39 11,39 9,38 8,38 6,37 5,36 3,34 2,33 1,31 1,30 0,28 0,26 " stroke-width="0.0972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="0" x2="12" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.44" x1="13" x2="13" y1="47" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="13" x2="13" y1="13" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="transformer2:shape0_0">
    <circle cx="25" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="17" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="34" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="17" x2="34" y1="18" y2="18"/>
   </symbol>
   <symbol id="transformer2:shape0_1">
    <ellipse cx="25" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="24" y1="58" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="32" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="16" x2="24" y1="74" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape15_0">
    <ellipse cx="25" cy="29" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="17" x2="25" y1="32" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="33" y1="24" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="25" y1="16" y2="24"/>
   </symbol>
   <symbol id="transformer2:shape15_1">
    <circle cx="25" cy="61" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="16" x2="24" y1="75" y2="67"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="32" y1="67" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="24" y1="59" y2="67"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_182aef0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_182b710" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_182be80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_182cb90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_182dc00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_182e680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_182f160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_182fc10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_18305d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1830e40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1830e40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1832a70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1832a70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_1833dc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1835990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_18365e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1836e60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1837790" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1838f60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1839790" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_123c190" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_183aa60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_183bc90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_183c610" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_183d100" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="78" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1842360" transform="matrix(1.246377 -0.000000 0.000000 -1.035714 2.739130 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1842ff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">测试</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_183ee60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_18402f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1841070" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_184faf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_18452f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(0,0,0)" height="1598" width="2360" x="3517" y="-1371"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-127395">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4711.000000 -576.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23550" ObjectName="SW-CX_YRH.CX_YRH_0010SW"/>
     <cge:Meas_Ref ObjectId="127395"/>
    <cge:TPSR_Ref TObjectID="23550"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127406">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5109.000000 -576.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23561" ObjectName="SW-CX_YRH.CX_YRH_0030BK"/>
     <cge:Meas_Ref ObjectId="127406"/>
    <cge:TPSR_Ref TObjectID="23561"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127415">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5536.000000 -475.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23570" ObjectName="SW-CX_YRH.CX_YRH_0050BK"/>
     <cge:Meas_Ref ObjectId="127415"/>
    <cge:TPSR_Ref TObjectID="23570"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127397">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4711.000000 -373.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23552" ObjectName="SW-CX_YRH.CX_YRH_0020SW"/>
     <cge:Meas_Ref ObjectId="127397"/>
    <cge:TPSR_Ref TObjectID="23552"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127410">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5160.000000 -356.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23565" ObjectName="SW-CX_YRH.CX_YRH_0040BK"/>
     <cge:Meas_Ref ObjectId="127410"/>
    <cge:TPSR_Ref TObjectID="23565"/></metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" runFlow="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" fill="none" points="3600,-885 3544,-885 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="3600,-885 3544,-885 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" runFlow="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" fill="none" points="3603,-100 3547,-100 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="3603,-100 3547,-100 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" runFlow="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" fill="none" points="5729,-1165 5805,-1165 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="5729,-1165 5805,-1165 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" runFlow="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" fill="none" points="5727,196 5802,196 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="5727,196 5802,196 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Reactance_Layer">
   <g DF8003:Layer="PUBLIC" id="RB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4340.000000 -922.000000)" xlink:href="#reactance:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="RB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="RB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4383.000000 -857.000000)" xlink:href="#reactance:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="RB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="RB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4383.000000 -779.000000)" xlink:href="#reactance:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="RB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="RB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4356.000000 -2.000000)" xlink:href="#reactance:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="RB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="RB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4399.000000 -141.000000)" xlink:href="#reactance:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="RB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="RB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4399.000000 -63.000000)" xlink:href="#reactance:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="RB-0"/>
    </metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_19ec380" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3796.000000 222.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19ecd70" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3641.000000 -357.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19ed7c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3944.000000 119.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19ee250" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3946.000000 -288.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19eeca0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3796.000000 -393.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19ef730" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4438.000000 -331.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19f01c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4439.000000 -247.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19f0c50" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4438.000000 112.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19f16e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5124.000000 120.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19f2170" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5163.000000 -333.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19f2bc0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5238.000000 119.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19f3650" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4822.000000 -287.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19f40a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5431.000000 -387.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19f4af0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5671.000000 -388.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19f5540" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5026.000000 -664.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19f5fd0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4822.000000 -663.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19f6a60" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5258.000000 -661.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19f74f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5124.000000 -1071.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19f7f40" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5239.000000 -1069.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19f8990" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5807.000000 -479.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19f9420" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4422.000000 -694.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19f9eb0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4423.000000 -609.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19fa940" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4423.000000 -1062.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19fb3d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3650.000000 -1044.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19fbe20" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3793.000000 -559.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19fc870" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3650.000000 -692.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19fd300" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3793.000000 -1175.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19fdd90" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3941.000000 -663.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19fe820" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3943.000000 -1070.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19ff270" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3646.000000 181.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-0KV" id="g_182a410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3656,-1101 3656,-1062 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="earth" ObjectIDND0="0@x" ObjectIDZND0="g_19fb3d0@0" Pin0InfoVect0LinkObjId="g_19fb3d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="RB-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3656,-1101 3656,-1062 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_182a670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3656,-697 3656,-654 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="transformer2" ObjectIDND0="g_19fc870@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="RB-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_19fc870_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3656,-697 3656,-654 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_10fe1a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3636,-1102 3599,-1102 3599,-655 3639,-655 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="transformer2" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="RB-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="RB-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3636,-1102 3599,-1102 3599,-655 3639,-655 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_11015e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3716,-1101 3799,-1101 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="23526@x" Pin0InfoVect0LinkObjId="SW-127371_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="RB-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3716,-1101 3799,-1101 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1101840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3831,-1101 3799,-1101 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="transformer2" EndDevType1="switch" ObjectIDZND0="0@x" ObjectIDZND1="23526@x" Pin0InfoVect0LinkObjId="RB-0_0" Pin0InfoVect1LinkObjId="SW-127371_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3831,-1101 3799,-1101 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1101aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3799,-1101 3799,-1125 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDZND0="23526@0" Pin0InfoVect0LinkObjId="SW-127371_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="RB-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3799,-1101 3799,-1125 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1101d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3799,-1161 3799,-1180 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23526@1" ObjectIDZND0="g_19fd300@0" Pin0InfoVect0LinkObjId="g_19fd300_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127371_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3799,-1161 3799,-1180 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1194070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3717,-654 3799,-654 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="23527@x" Pin0InfoVect0LinkObjId="SW-127372_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="RB-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3717,-654 3799,-654 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1194260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3799,-654 3831,-654 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="23527@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="RB-0_0" Pin1InfoVect1LinkObjId="SW-127372_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3799,-654 3831,-654 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_11969d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3799,-654 3799,-631 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDZND0="23527@1" Pin0InfoVect0LinkObjId="SW-127372_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="RB-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3799,-654 3799,-631 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1196c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3799,-595 3799,-577 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23527@0" ObjectIDZND0="g_19fbe20@0" Pin0InfoVect0LinkObjId="g_19fbe20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127372_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3799,-595 3799,-577 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1199690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3949,-1165 3949,-1142 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_1821690@0" ObjectIDND1="g_1a023c0@0" ObjectIDZND0="23528@1" Pin0InfoVect0LinkObjId="SW-127373_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1821690_0" Pin1InfoVect1LinkObjId="g_1a023c0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3949,-1165 3949,-1142 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_11998f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3949,-1106 3949,-1088 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23528@0" ObjectIDZND0="g_19fe820@0" Pin0InfoVect0LinkObjId="g_19fe820_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127373_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3949,-1106 3949,-1088 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1a5c040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3947,-649 3947,-668 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23529@1" ObjectIDZND0="g_19fdd90@0" Pin0InfoVect0LinkObjId="g_19fdd90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127374_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3947,-649 3947,-668 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1a5cb30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3850,-1131 3850,-1165 3949,-1165 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_1a023c0@1" ObjectIDZND0="23528@x" ObjectIDZND1="g_1821690@0" Pin0InfoVect0LinkObjId="SW-127373_0" Pin0InfoVect1LinkObjId="g_1821690_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a023c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3850,-1131 3850,-1165 3949,-1165 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1a5d620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3947,-613 3947,-586 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="breaker" ObjectIDND0="23529@0" ObjectIDZND0="23551@x" ObjectIDZND1="23535@x" ObjectIDZND2="23550@x" Pin0InfoVect0LinkObjId="SW-127396_0" Pin0InfoVect1LinkObjId="SW-127380_0" Pin0InfoVect2LinkObjId="SW-127395_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127374_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3947,-613 3947,-586 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1a5d880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3947,-586 3850,-586 3850,-624 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="23529@x" ObjectIDND1="23551@x" ObjectIDND2="23535@x" ObjectIDZND0="g_1a016c0@0" Pin0InfoVect0LinkObjId="g_1a016c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-127374_0" Pin1InfoVect1LinkObjId="SW-127396_0" Pin1InfoVect2LinkObjId="SW-127380_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3947,-586 3850,-586 3850,-624 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1a5dae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3850,-1071 3850,-686 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1a023c0@0" ObjectIDZND0="g_1a016c0@1" Pin0InfoVect0LinkObjId="g_1a016c0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a023c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3850,-1071 3850,-686 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1a60200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3652,163 3652,126 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="transformer2" ObjectIDND0="g_19ff270@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="RB-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_19ff270_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3652,163 3652,126 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1a60460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3647,-362 3647,-319 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="transformer2" ObjectIDND0="g_19ecd70@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="RB-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_19ecd70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3647,-362 3647,-319 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1a606c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3628,-320 3602,-320 3602,126 3634,126 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="transformer2" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="RB-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="RB-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3628,-320 3602,-320 3602,126 3634,126 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1a63370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3708,-319 3802,-319 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="23531@x" Pin0InfoVect0LinkObjId="SW-127376_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="RB-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3708,-319 3802,-319 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1a635d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3834,-319 3802,-319 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="transformer2" EndDevType1="switch" ObjectIDZND0="0@x" ObjectIDZND1="23531@x" Pin0InfoVect0LinkObjId="RB-0_0" Pin0InfoVect1LinkObjId="SW-127376_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3834,-319 3802,-319 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1a63830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3802,-319 3802,-343 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDZND0="23531@0" Pin0InfoVect0LinkObjId="SW-127376_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="RB-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3802,-319 3802,-343 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1a63a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3802,-379 3802,-398 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23531@1" ObjectIDZND0="g_19eeca0@0" Pin0InfoVect0LinkObjId="g_19eeca0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127376_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3802,-379 3802,-398 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1a64560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3715,127 3802,127 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="23530@x" Pin0InfoVect0LinkObjId="SW-127375_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3715,127 3802,127 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1a64750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3802,127 3834,127 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="23530@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127375_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3802,127 3834,127 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1a66f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3802,127 3802,150 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="23530@1" Pin0InfoVect0LinkObjId="SW-127375_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3802,127 3802,150 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1a671d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3802,186 3802,204 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23530@0" ObjectIDZND0="g_19ec380@0" Pin0InfoVect0LinkObjId="g_19ec380_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127375_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3802,186 3802,204 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1a69c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3952,-383 3952,-360 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_19ffcc0@0" ObjectIDND1="23539@x" ObjectIDND2="23553@x" ObjectIDZND0="23533@1" Pin0InfoVect0LinkObjId="SW-127378_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_19ffcc0_0" Pin1InfoVect1LinkObjId="SW-127384_0" Pin1InfoVect2LinkObjId="SW-127398_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3952,-383 3952,-360 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1a69e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3952,-324 3952,-306 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23533@0" ObjectIDZND0="g_19ee250@0" Pin0InfoVect0LinkObjId="g_19ee250_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127378_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3952,-324 3952,-306 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1819870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3950,133 3950,114 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23532@1" ObjectIDZND0="g_19ed7c0@0" Pin0InfoVect0LinkObjId="g_19ed7c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127377_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3950,133 3950,114 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1819ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3853,-349 3853,-383 3952,-383 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_19ffcc0@1" ObjectIDZND0="23533@x" ObjectIDZND1="23539@x" ObjectIDZND2="23553@x" Pin0InfoVect0LinkObjId="SW-127378_0" Pin0InfoVect1LinkObjId="SW-127384_0" Pin0InfoVect2LinkObjId="SW-127398_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_19ffcc0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3853,-349 3853,-383 3952,-383 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1819d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3950,169 3950,196 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="23532@0" ObjectIDZND0="g_1425530@0" ObjectIDZND1="g_1a009c0@0" Pin0InfoVect0LinkObjId="g_1425530_0" Pin0InfoVect1LinkObjId="g_1a009c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127377_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3950,169 3950,196 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1819f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3950,196 3853,196 3853,157 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="23532@x" ObjectIDND1="g_1425530@0" ObjectIDZND0="g_1a009c0@0" Pin0InfoVect0LinkObjId="g_1a009c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127377_0" Pin1InfoVect1LinkObjId="g_1425530_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3950,196 3853,196 3853,157 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_181a1f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3853,-289 3853,96 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_19ffcc0@0" ObjectIDZND0="g_1a009c0@1" Pin0InfoVect0LinkObjId="g_1a009c0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_19ffcc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3853,-289 3853,96 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1823aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4129,-1165 4143,-1165 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1821690@1" ObjectIDZND0="g_1822fe0@0" Pin0InfoVect0LinkObjId="g_1822fe0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1821690_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4129,-1165 4143,-1165 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1896ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4412,-1068 4427,-1068 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23536@1" ObjectIDZND0="g_19fa940@0" Pin0InfoVect0LinkObjId="g_19fa940_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127381_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4412,-1068 4427,-1068 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1896d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4411,-700 4426,-700 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23537@1" ObjectIDZND0="g_19f9420@0" Pin0InfoVect0LinkObjId="g_19f9420_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127382_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4411,-700 4426,-700 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_18994a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4412,-615 4427,-615 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23551@1" ObjectIDZND0="g_19f9eb0@0" Pin0InfoVect0LinkObjId="g_19f9eb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127396_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4412,-615 4427,-615 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_189bc30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4354,-615 4376,-615 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="breaker" EndDevType0="switch" ObjectIDND0="23529@x" ObjectIDND1="g_1a016c0@0" ObjectIDND2="23550@x" ObjectIDZND0="23551@0" Pin0InfoVect0LinkObjId="SW-127396_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-127374_0" Pin1InfoVect1LinkObjId="g_1a016c0_0" Pin1InfoVect2LinkObjId="SW-127395_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4354,-615 4376,-615 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_18a04a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4828,-649 4828,-668 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23554@1" ObjectIDZND0="g_19f5fd0@0" Pin0InfoVect0LinkObjId="g_19f5fd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127399_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4828,-649 4828,-668 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_18a0700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4828,-613 4828,-586 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="23554@0" ObjectIDZND0="23550@x" ObjectIDZND1="23558@x" ObjectIDZND2="23555@x" Pin0InfoVect0LinkObjId="SW-127395_0" Pin0InfoVect1LinkObjId="SW-127403_0" Pin0InfoVect2LinkObjId="SW-127400_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127399_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4828,-613 4828,-586 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_18a1410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4747,-586 4828,-586 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="23550@0" ObjectIDZND0="23558@x" ObjectIDZND1="23554@x" ObjectIDZND2="23555@x" Pin0InfoVect0LinkObjId="SW-127403_0" Pin0InfoVect1LinkObjId="SW-127399_0" Pin0InfoVect2LinkObjId="SW-127400_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127395_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4747,-586 4828,-586 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_18a2120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4828,-586 4828,-551 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="23550@x" ObjectIDND1="23554@x" ObjectIDND2="23555@x" ObjectIDZND0="23558@1" Pin0InfoVect0LinkObjId="SW-127403_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-127395_0" Pin1InfoVect1LinkObjId="SW-127399_0" Pin1InfoVect2LinkObjId="SW-127400_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4828,-586 4828,-551 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_18a2380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4828,-420 4828,-383 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="23559@0" ObjectIDZND0="23552@x" ObjectIDZND1="23556@x" ObjectIDZND2="23557@x" Pin0InfoVect0LinkObjId="SW-127397_0" Pin0InfoVect1LinkObjId="SW-127401_0" Pin0InfoVect2LinkObjId="SW-127402_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127404_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4828,-420 4828,-383 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_18a4de0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4828,-382 4828,-359 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="23552@x" ObjectIDND1="23559@x" ObjectIDND2="23557@x" ObjectIDZND0="23556@1" Pin0InfoVect0LinkObjId="SW-127401_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-127397_0" Pin1InfoVect1LinkObjId="SW-127404_0" Pin1InfoVect2LinkObjId="SW-127402_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4828,-382 4828,-359 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_18a5040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4828,-323 4828,-305 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23556@0" ObjectIDZND0="g_19f3650@0" Pin0InfoVect0LinkObjId="g_19f3650_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127401_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4828,-323 4828,-305 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_110a570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4828,-586 4920,-586 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="23550@x" ObjectIDND1="23558@x" ObjectIDND2="23554@x" ObjectIDZND0="23555@0" Pin0InfoVect0LinkObjId="SW-127400_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-127395_0" Pin1InfoVect1LinkObjId="SW-127403_0" Pin1InfoVect2LinkObjId="SW-127399_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4828,-586 4920,-586 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_110a7d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4828,-383 4920,-383 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="23552@x" ObjectIDND1="23559@x" ObjectIDND2="23556@x" ObjectIDZND0="23557@0" Pin0InfoVect0LinkObjId="SW-127402_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-127397_0" Pin1InfoVect1LinkObjId="SW-127404_0" Pin1InfoVect2LinkObjId="SW-127401_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4828,-383 4920,-383 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_110b4e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4956,-586 5032,-586 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="23555@1" ObjectIDZND0="23561@x" ObjectIDZND1="23560@x" ObjectIDZND2="23557@x" Pin0InfoVect0LinkObjId="SW-127406_0" Pin0InfoVect1LinkObjId="SW-127405_0" Pin0InfoVect2LinkObjId="SW-127402_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127400_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4956,-586 5032,-586 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_110b740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5032,-586 5032,-383 4956,-383 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="23561@x" ObjectIDND1="23560@x" ObjectIDND2="23555@x" ObjectIDZND0="23557@1" Pin0InfoVect0LinkObjId="SW-127402_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-127406_0" Pin1InfoVect1LinkObjId="SW-127405_0" Pin1InfoVect2LinkObjId="SW-127400_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5032,-586 5032,-383 4956,-383 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_110e1a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5032,-650 5032,-669 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23560@1" ObjectIDZND0="g_19f5540@0" Pin0InfoVect0LinkObjId="g_19f5540_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127405_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5032,-650 5032,-669 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_110e400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5032,-614 5032,-587 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="23560@0" ObjectIDZND0="23561@x" ObjectIDZND1="23555@x" ObjectIDZND2="23557@x" Pin0InfoVect0LinkObjId="SW-127406_0" Pin0InfoVect1LinkObjId="SW-127400_0" Pin0InfoVect2LinkObjId="SW-127402_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127405_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5032,-614 5032,-587 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1110470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5032,-586 5118,-586 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="breaker" ObjectIDND0="23560@x" ObjectIDND1="23555@x" ObjectIDND2="23557@x" ObjectIDZND0="23561@1" Pin0InfoVect0LinkObjId="SW-127406_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-127405_0" Pin1InfoVect1LinkObjId="SW-127400_0" Pin1InfoVect2LinkObjId="SW-127402_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5032,-586 5118,-586 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1112c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5145,-586 5192,-586 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="23561@0" ObjectIDZND0="23562@0" Pin0InfoVect0LinkObjId="SW-127407_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127406_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5145,-586 5192,-586 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1115660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5264,-647 5264,-666 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23563@1" ObjectIDZND0="g_19f6a60@0" Pin0InfoVect0LinkObjId="g_19f6a60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127408_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5264,-647 5264,-666 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_11158c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5264,-611 5264,-584 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="23563@0" ObjectIDZND0="23545@x" ObjectIDZND1="23549@x" ObjectIDZND2="23562@x" Pin0InfoVect0LinkObjId="SW-127390_0" Pin0InfoVect1LinkObjId="SW-127394_0" Pin0InfoVect2LinkObjId="SW-127407_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127408_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5264,-611 5264,-584 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_11163b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5228,-586 5264,-586 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="23562@1" ObjectIDZND0="23545@x" ObjectIDZND1="23549@x" ObjectIDZND2="23563@x" Pin0InfoVect0LinkObjId="SW-127390_0" Pin0InfoVect1LinkObjId="SW-127394_0" Pin0InfoVect2LinkObjId="SW-127408_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127407_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5228,-586 5264,-586 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1116ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5346,-1165 5729,-1165 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="23543@x" ObjectIDND1="23544@x" ObjectIDND2="23545@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="RB-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-127388_0" Pin1InfoVect1LinkObjId="SW-127389_0" Pin1InfoVect2LinkObjId="SW-127390_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5346,-1165 5729,-1165 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1117990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5346,-586 5264,-586 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="23545@x" ObjectIDND1="23549@x" ObjectIDZND0="23563@x" ObjectIDZND1="23562@x" Pin0InfoVect0LinkObjId="SW-127408_0" Pin0InfoVect1LinkObjId="SW-127407_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127390_0" Pin1InfoVect1LinkObjId="SW-127394_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5346,-586 5264,-586 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1118480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5346,196 5727,196 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="23548@x" ObjectIDND1="23547@x" ObjectIDND2="23549@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="RB-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-127393_0" Pin1InfoVect1LinkObjId="SW-127392_0" Pin1InfoVect2LinkObjId="SW-127394_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5346,196 5727,196 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1118f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4828,-515 4828,-485 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="23558@0" ObjectIDZND0="23559@x" ObjectIDZND1="g_18d2080@0" Pin0InfoVect0LinkObjId="SW-127404_0" Pin0InfoVect1LinkObjId="g_18d2080_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127403_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4828,-515 4828,-485 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_11191d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4828,-485 4828,-456 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="23558@x" ObjectIDND1="g_18d2080@0" ObjectIDZND0="23559@1" Pin0InfoVect0LinkObjId="SW-127404_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127403_0" Pin1InfoVect1LinkObjId="g_18d2080_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4828,-485 4828,-456 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_140d7c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5500,-485 5545,-485 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="23569@1" ObjectIDZND0="23570@1" Pin0InfoVect0LinkObjId="SW-127415_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127414_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5500,-485 5545,-485 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_140da20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5572,-485 5610,-485 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="23570@0" ObjectIDZND0="23571@0" Pin0InfoVect0LinkObjId="SW-127416_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127415_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5572,-485 5610,-485 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_140e730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5437,-485 5464,-485 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_18d2890@0" ObjectIDND1="23566@x" ObjectIDND2="23567@x" ObjectIDZND0="23569@0" Pin0InfoVect0LinkObjId="SW-127414_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_18d2890_0" Pin1InfoVect1LinkObjId="SW-127411_0" Pin1InfoVect2LinkObjId="SW-127412_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5437,-485 5464,-485 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_140f440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5646,-485 5677,-485 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="earth" ObjectIDND0="23571@1" ObjectIDZND0="23568@x" ObjectIDZND1="23567@x" ObjectIDZND2="g_19f8990@0" Pin0InfoVect0LinkObjId="SW-127413_0" Pin0InfoVect1LinkObjId="SW-127412_0" Pin0InfoVect2LinkObjId="g_19f8990_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127416_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5646,-485 5677,-485 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1411bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5437,-485 5437,-549 5545,-549 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="23569@x" ObjectIDND1="g_18d2890@0" ObjectIDND2="23566@x" ObjectIDZND0="23567@0" Pin0InfoVect0LinkObjId="SW-127412_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-127414_0" Pin1InfoVect1LinkObjId="g_18d2890_0" Pin1InfoVect2LinkObjId="SW-127411_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5437,-485 5437,-549 5545,-549 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1411e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5581,-549 5677,-549 5677,-485 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="earth" ObjectIDND0="23567@1" ObjectIDZND0="23571@x" ObjectIDZND1="23568@x" ObjectIDZND2="g_19f8990@0" Pin0InfoVect0LinkObjId="SW-127416_0" Pin0InfoVect1LinkObjId="SW-127413_0" Pin0InfoVect2LinkObjId="g_19f8990_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127412_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5581,-549 5677,-549 5677,-485 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1414890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5437,-485 5437,-459 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="23569@x" ObjectIDND1="g_18d2890@0" ObjectIDND2="23567@x" ObjectIDZND0="23566@1" Pin0InfoVect0LinkObjId="SW-127411_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-127414_0" Pin1InfoVect1LinkObjId="g_18d2890_0" Pin1InfoVect2LinkObjId="SW-127412_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5437,-485 5437,-459 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1414af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5437,-423 5437,-405 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23566@0" ObjectIDZND0="g_19f40a0@0" Pin0InfoVect0LinkObjId="g_19f40a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127411_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5437,-423 5437,-405 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1417550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5677,-485 5677,-460 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="earth" EndDevType0="switch" ObjectIDND0="23571@x" ObjectIDND1="23567@x" ObjectIDND2="g_19f8990@0" ObjectIDZND0="23568@1" Pin0InfoVect0LinkObjId="SW-127413_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-127416_0" Pin1InfoVect1LinkObjId="SW-127412_0" Pin1InfoVect2LinkObjId="g_19f8990_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5677,-485 5677,-460 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_14177b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5677,-424 5677,-406 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23568@0" ObjectIDZND0="g_19f4af0@0" Pin0InfoVect0LinkObjId="g_19f4af0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127413_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5677,-424 5677,-406 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_141a210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4369,-337 4391,-337 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="23539@x" ObjectIDND1="g_19ffcc0@0" ObjectIDND2="23533@x" ObjectIDZND0="23553@0" Pin0InfoVect0LinkObjId="SW-127398_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-127384_0" Pin1InfoVect1LinkObjId="g_19ffcc0_0" Pin1InfoVect2LinkObjId="SW-127378_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4369,-337 4391,-337 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_141a470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4427,-337 4442,-337 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23553@1" ObjectIDZND0="g_19ef730@0" Pin0InfoVect0LinkObjId="g_19ef730_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127398_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4427,-337 4442,-337 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_141cc00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4369,-316 4369,-337 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="23539@1" ObjectIDZND0="23553@x" ObjectIDZND1="g_19ffcc0@0" ObjectIDZND2="23533@x" Pin0InfoVect0LinkObjId="SW-127398_0" Pin0InfoVect1LinkObjId="g_19ffcc0_0" Pin0InfoVect2LinkObjId="SW-127378_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127384_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4369,-316 4369,-337 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_141ce60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4369,-337 4369,-383 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="breaker" ObjectIDND0="23539@x" ObjectIDND1="23553@x" ObjectIDZND0="g_19ffcc0@0" ObjectIDZND1="23533@x" ObjectIDZND2="23552@x" Pin0InfoVect0LinkObjId="g_19ffcc0_0" Pin0InfoVect1LinkObjId="SW-127378_0" Pin0InfoVect2LinkObjId="SW-127397_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127384_0" Pin1InfoVect1LinkObjId="SW-127398_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4369,-337 4369,-383 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_141d0c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4428,-253 4443,-253 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23541@1" ObjectIDZND0="g_19f01c0@0" Pin0InfoVect0LinkObjId="g_19f01c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127386_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4428,-253 4443,-253 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_141f850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4369,-253 4392,-253 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="reactance" BeginDevType2="capacitor" EndDevType0="switch" ObjectIDND0="23539@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="23541@0" Pin0InfoVect0LinkObjId="SW-127386_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-127384_0" Pin1InfoVect1LinkObjId="RB-0_0" Pin1InfoVect2LinkObjId="RB-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4369,-253 4392,-253 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1425070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4427,106 4442,106 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23540@1" ObjectIDZND0="g_19f0c50@0" Pin0InfoVect0LinkObjId="g_19f0c50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127385_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4427,106 4442,106 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_14252d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4369,196 4369,165 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_1426240@0" ObjectIDND1="23546@x" ObjectIDND2="23547@x" ObjectIDZND0="23538@0" Pin0InfoVect0LinkObjId="SW-127383_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1426240_0" Pin1InfoVect1LinkObjId="SW-127391_0" Pin1InfoVect2LinkObjId="SW-127392_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4369,196 4369,165 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1427010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4148,196 4168,196 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1425530@1" ObjectIDZND0="g_1426240@0" Pin0InfoVect0LinkObjId="g_1426240_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1425530_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4148,196 4168,196 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1429050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4747,-383 4828,-383 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="23552@0" ObjectIDZND0="23559@x" ObjectIDZND1="23556@x" ObjectIDZND2="23557@x" Pin0InfoVect0LinkObjId="SW-127404_0" Pin0InfoVect1LinkObjId="SW-127401_0" Pin0InfoVect2LinkObjId="SW-127402_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127397_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4747,-383 4828,-383 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_18a8050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5169,-485 5169,-456 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_18d2890@0" ObjectIDND1="g_18d2080@0" ObjectIDZND0="23564@1" Pin0InfoVect0LinkObjId="SW-127409_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_18d2890_0" Pin1InfoVect1LinkObjId="g_18d2080_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5169,-485 5169,-456 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_18aaab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5130,-1165 5130,-1143 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="23543@x" ObjectIDND1="23534@x" ObjectIDND2="g_1822fe0@0" ObjectIDZND0="23542@1" Pin0InfoVect0LinkObjId="SW-127387_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-127388_0" Pin1InfoVect1LinkObjId="SW-127379_0" Pin1InfoVect2LinkObjId="g_1822fe0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5130,-1165 5130,-1143 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_18aad10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5130,-1107 5130,-1089 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23542@0" ObjectIDZND0="g_19f74f0@0" Pin0InfoVect0LinkObjId="g_19f74f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127387_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5130,-1107 5130,-1089 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_18ad770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5245,-1165 5245,-1141 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="powerLine" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="23545@x" ObjectIDND1="0@1" ObjectIDND2="23543@x" ObjectIDZND0="23544@1" Pin0InfoVect0LinkObjId="SW-127389_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-127390_0" Pin1InfoVect1LinkObjId="RB-0_1" Pin1InfoVect2LinkObjId="SW-127388_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5245,-1165 5245,-1141 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_18ad9d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5245,-1105 5245,-1087 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23544@0" ObjectIDZND0="g_19f7f40@0" Pin0InfoVect0LinkObjId="g_19f7f40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127389_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5245,-1105 5245,-1087 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_18b09f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5130,-1165 5169,-1165 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="23534@x" ObjectIDND1="g_1822fe0@0" ObjectIDND2="23542@x" ObjectIDZND0="23543@0" Pin0InfoVect0LinkObjId="SW-127388_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-127379_0" Pin1InfoVect1LinkObjId="g_1822fe0_0" Pin1InfoVect2LinkObjId="SW-127387_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5130,-1165 5169,-1165 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_18b14e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5205,-1165 5245,-1165 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="powerLine" EndDevType2="switch" ObjectIDND0="23543@1" ObjectIDZND0="23545@x" ObjectIDZND1="0@1" ObjectIDZND2="23544@x" Pin0InfoVect0LinkObjId="SW-127390_0" Pin0InfoVect1LinkObjId="RB-0_1" Pin0InfoVect2LinkObjId="SW-127389_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127388_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5205,-1165 5245,-1165 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_18b1740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5245,-1165 5346,-1165 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="powerLine" ObjectIDND0="23543@x" ObjectIDND1="23544@x" ObjectIDZND0="23545@x" ObjectIDZND1="0@1" Pin0InfoVect0LinkObjId="SW-127390_0" Pin0InfoVect1LinkObjId="RB-0_1" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127388_0" Pin1InfoVect1LinkObjId="SW-127389_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5245,-1165 5346,-1165 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_18b41a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5346,-1165 5346,-1077 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="powerLine" EndDevType0="switch" ObjectIDND0="23543@x" ObjectIDND1="23544@x" ObjectIDND2="0@1" ObjectIDZND0="23545@1" Pin0InfoVect0LinkObjId="SW-127390_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-127388_0" Pin1InfoVect1LinkObjId="SW-127389_0" Pin1InfoVect2LinkObjId="RB-0_1" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5346,-1165 5346,-1077 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_18b4400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5346,-1041 5346,-586 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="23545@0" ObjectIDZND0="23563@x" ObjectIDZND1="23562@x" ObjectIDZND2="23549@x" Pin0InfoVect0LinkObjId="SW-127408_0" Pin0InfoVect1LinkObjId="SW-127407_0" Pin0InfoVect2LinkObjId="SW-127394_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127390_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5346,-1041 5346,-586 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_18b6e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5244,133 5244,114 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23548@1" ObjectIDZND0="g_19f2bc0@0" Pin0InfoVect0LinkObjId="g_19f2bc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127393_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5244,133 5244,114 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_18b70c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5244,169 5244,196 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="powerLine" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="23548@0" ObjectIDZND0="0@1" ObjectIDZND1="23549@x" ObjectIDZND2="23547@x" Pin0InfoVect0LinkObjId="RB-0_1" Pin0InfoVect1LinkObjId="SW-127394_0" Pin0InfoVect2LinkObjId="SW-127392_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127393_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5244,169 5244,196 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_18b9b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5130,134 5130,115 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23546@1" ObjectIDZND0="g_19f16e0@0" Pin0InfoVect0LinkObjId="g_19f16e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127391_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5130,134 5130,115 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_18b9d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5130,170 5130,197 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="23546@0" ObjectIDZND0="23547@x" ObjectIDZND1="23538@x" ObjectIDZND2="g_1426240@0" Pin0InfoVect0LinkObjId="SW-127392_0" Pin0InfoVect1LinkObjId="SW-127383_0" Pin0InfoVect2LinkObjId="g_1426240_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127391_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5130,170 5130,197 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_18ba870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5243,196 5346,196 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="powerLine" EndDevType1="switch" ObjectIDND0="23548@x" ObjectIDND1="23547@x" ObjectIDZND0="0@1" ObjectIDZND1="23549@x" Pin0InfoVect0LinkObjId="RB-0_1" Pin0InfoVect1LinkObjId="SW-127394_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127393_0" Pin1InfoVect1LinkObjId="SW-127392_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5243,196 5346,196 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_18bd890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5130,196 5167,196 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="23546@x" ObjectIDND1="23538@x" ObjectIDND2="g_1426240@0" ObjectIDZND0="23547@0" Pin0InfoVect0LinkObjId="SW-127392_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-127391_0" Pin1InfoVect1LinkObjId="SW-127383_0" Pin1InfoVect2LinkObjId="g_1426240_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5130,196 5167,196 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_18bdaf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5203,196 5243,196 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="powerLine" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="23547@1" ObjectIDZND0="0@1" ObjectIDZND1="23549@x" ObjectIDZND2="23548@x" Pin0InfoVect0LinkObjId="RB-0_1" Pin0InfoVect1LinkObjId="SW-127394_0" Pin0InfoVect2LinkObjId="SW-127393_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127392_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5203,196 5243,196 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_18c0550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5346,-586 5346,65 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="23563@x" ObjectIDND1="23562@x" ObjectIDND2="23545@x" ObjectIDZND0="23549@1" Pin0InfoVect0LinkObjId="SW-127394_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-127408_0" Pin1InfoVect1LinkObjId="SW-127407_0" Pin1InfoVect2LinkObjId="SW-127390_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5346,-586 5346,65 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_18c07b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5346,101 5346,196 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="powerLine" ObjectIDND0="23549@0" ObjectIDZND0="23548@x" ObjectIDZND1="23547@x" ObjectIDZND2="0@1" Pin0InfoVect0LinkObjId="SW-127393_0" Pin0InfoVect1LinkObjId="SW-127392_0" Pin0InfoVect2LinkObjId="RB-0_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127394_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5346,101 5346,196 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_18c7670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4088,-1165 3949,-1165 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_1821690@0" ObjectIDZND0="23528@x" ObjectIDZND1="g_1a023c0@0" Pin0InfoVect0LinkObjId="SW-127373_0" Pin0InfoVect1LinkObjId="g_1a023c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1821690_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4088,-1165 3949,-1165 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_18c7fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4353,-615 4353,-586 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="breaker" ObjectIDND0="23551@x" ObjectIDND1="23535@x" ObjectIDZND0="23529@x" ObjectIDZND1="g_1a016c0@0" ObjectIDZND2="23550@x" Pin0InfoVect0LinkObjId="SW-127374_0" Pin0InfoVect1LinkObjId="g_1a016c0_0" Pin0InfoVect2LinkObjId="SW-127395_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127396_0" Pin1InfoVect1LinkObjId="SW-127380_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4353,-615 4353,-586 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_18c81d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4353,-640 4353,-615 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="23535@0" ObjectIDZND0="23551@x" ObjectIDZND1="23529@x" ObjectIDZND2="g_1a016c0@0" Pin0InfoVect0LinkObjId="SW-127396_0" Pin0InfoVect1LinkObjId="SW-127374_0" Pin0InfoVect2LinkObjId="g_1a016c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127380_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4353,-640 4353,-615 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_18c8400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4353,-1165 4353,-1136 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_1822fe0@0" ObjectIDND1="23543@x" ObjectIDND2="23542@x" ObjectIDZND0="23534@1" Pin0InfoVect0LinkObjId="SW-127379_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1822fe0_0" Pin1InfoVect1LinkObjId="SW-127388_0" Pin1InfoVect2LinkObjId="SW-127387_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4353,-1165 4353,-1136 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_18c99c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4353,-991 4353,-969 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="reactance" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="RB-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="RB-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4353,-991 4353,-969 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_18cc720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4396,-904 4353,-904 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="reactance" EndDevType0="reactance" EndDevType1="capacitor" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="RB-0_0" Pin0InfoVect1LinkObjId="RB-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="RB-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4396,-904 4353,-904 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_18cd210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4353,-927 4353,-904 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="reactance" EndDevType0="reactance" EndDevType1="capacitor" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="RB-0_0" Pin0InfoVect1LinkObjId="RB-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="RB-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4353,-927 4353,-904 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_18cd470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4353,-904 4353,-890 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="reactance" BeginDevType1="reactance" EndDevType0="capacitor" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="RB-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="RB-0_0" Pin1InfoVect1LinkObjId="RB-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4353,-904 4353,-890 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_18cd6d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4396,-826 4353,-826 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="reactance" EndDevType0="capacitor" EndDevType1="reactance" EndDevType2="capacitor" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="RB-0_0" Pin0InfoVect1LinkObjId="RB-0_0" Pin0InfoVect2LinkObjId="RB-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="RB-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4396,-826 4353,-826 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_18ce1c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4353,-826 4353,-812 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="reactance" BeginDevType1="reactance" BeginDevType2="capacitor" EndDevType0="capacitor" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="RB-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="RB-0_0" Pin1InfoVect1LinkObjId="RB-0_0" Pin1InfoVect2LinkObjId="RB-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4353,-826 4353,-812 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_18ce420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4396,-862 4353,-862 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="reactance" EndDevType0="capacitor" EndDevType1="reactance" EndDevType2="capacitor" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="RB-0_0" Pin0InfoVect1LinkObjId="RB-0_0" Pin0InfoVect2LinkObjId="RB-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="RB-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4396,-862 4353,-862 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_18cef10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4353,-875 4353,-862 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="reactance" EndDevType1="reactance" EndDevType2="capacitor" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="RB-0_0" Pin0InfoVect1LinkObjId="RB-0_0" Pin0InfoVect2LinkObjId="RB-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="RB-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4353,-875 4353,-862 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_18cf170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4353,-862 4353,-826 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="reactance" BeginDevType1="capacitor" EndDevType0="reactance" EndDevType1="capacitor" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="RB-0_0" Pin0InfoVect1LinkObjId="RB-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="RB-0_0" Pin1InfoVect1LinkObjId="RB-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4353,-862 4353,-826 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_18cf3d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4396,-784 4353,-784 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="reactance" EndDevType0="capacitor" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="23537@x" ObjectIDZND2="23535@x" Pin0InfoVect0LinkObjId="RB-0_0" Pin0InfoVect1LinkObjId="SW-127382_0" Pin0InfoVect2LinkObjId="SW-127380_0" Pin0Num="1" Pin1InfoVect0LinkObjId="RB-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4396,-784 4353,-784 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_18cfec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4353,-797 4353,-784 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="reactance" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="23537@x" ObjectIDZND2="23535@x" Pin0InfoVect0LinkObjId="RB-0_0" Pin0InfoVect1LinkObjId="SW-127382_0" Pin0InfoVect2LinkObjId="SW-127380_0" Pin0Num="1" Pin1InfoVect0LinkObjId="RB-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4353,-797 4353,-784 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_18d0120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4376,-1068 4353,-1068 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="capacitor" ObjectIDND0="23536@0" ObjectIDZND0="23534@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-127379_0" Pin0InfoVect1LinkObjId="RB-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127381_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4376,-1068 4353,-1068 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_18d0c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4353,-1100 4353,-1068 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="capacitor" ObjectIDND0="23534@0" ObjectIDZND0="23536@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-127381_0" Pin0InfoVect1LinkObjId="RB-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127379_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4353,-1100 4353,-1068 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_18d0e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4353,-1068 4353,-1006 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="capacitor" ObjectIDND0="23536@x" ObjectIDND1="23534@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="RB-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127381_0" Pin1InfoVect1LinkObjId="SW-127379_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4353,-1068 4353,-1006 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_18d10d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4375,-700 4353,-700 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="reactance" EndDevType1="capacitor" EndDevType2="switch" ObjectIDND0="23537@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="23535@x" Pin0InfoVect0LinkObjId="RB-0_0" Pin0InfoVect1LinkObjId="RB-0_0" Pin0InfoVect2LinkObjId="SW-127380_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127382_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4375,-700 4353,-700 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_18d1bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4353,-784 4353,-700 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="reactance" BeginDevType1="capacitor" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="23537@x" ObjectIDZND1="23535@x" Pin0InfoVect0LinkObjId="SW-127382_0" Pin0InfoVect1LinkObjId="SW-127380_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="RB-0_0" Pin1InfoVect1LinkObjId="RB-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4353,-784 4353,-700 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_18d1e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4353,-700 4353,-676 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="reactance" BeginDevType2="capacitor" EndDevType0="switch" ObjectIDND0="23537@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="23535@1" Pin0InfoVect0LinkObjId="SW-127380_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-127382_0" Pin1InfoVect1LinkObjId="RB-0_0" Pin1InfoVect2LinkObjId="RB-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4353,-700 4353,-676 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_18d30a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5169,-485 5334,-485 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="23564@x" ObjectIDND1="g_18d2080@0" ObjectIDZND0="g_18d2890@0" Pin0InfoVect0LinkObjId="g_18d2890_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127409_0" Pin1InfoVect1LinkObjId="g_18d2080_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5169,-485 5334,-485 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_18d3300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5358,-485 5437,-485 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_18d2890@1" ObjectIDZND0="23569@x" ObjectIDZND1="23566@x" ObjectIDZND2="23567@x" Pin0InfoVect0LinkObjId="SW-127414_0" Pin0InfoVect1LinkObjId="SW-127411_0" Pin0InfoVect2LinkObjId="SW-127412_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_18d2890_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5358,-485 5437,-485 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_18d3560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4828,-485 5020,-485 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="23558@x" ObjectIDND1="23559@x" ObjectIDZND0="g_18d2080@0" Pin0InfoVect0LinkObjId="g_18d2080_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127403_0" Pin1InfoVect1LinkObjId="SW-127404_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4828,-485 5020,-485 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_18d37c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5044,-485 5169,-485 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_18d2080@1" ObjectIDZND0="23564@x" ObjectIDZND1="g_18d2890@0" Pin0InfoVect0LinkObjId="SW-127409_0" Pin0InfoVect1LinkObjId="g_18d2890_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_18d2080_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5044,-485 5169,-485 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_18d42b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4185,-1165 4353,-1165 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1822fe0@1" ObjectIDZND0="23534@x" ObjectIDZND1="23543@x" ObjectIDZND2="23542@x" Pin0InfoVect0LinkObjId="SW-127379_0" Pin0InfoVect1LinkObjId="SW-127388_0" Pin0InfoVect2LinkObjId="SW-127387_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1822fe0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4185,-1165 4353,-1165 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_18d4da0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3947,-586 4353,-586 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="breaker" ObjectIDND0="23529@x" ObjectIDND1="g_1a016c0@0" ObjectIDZND0="23551@x" ObjectIDZND1="23535@x" ObjectIDZND2="23550@x" Pin0InfoVect0LinkObjId="SW-127396_0" Pin0InfoVect1LinkObjId="SW-127380_0" Pin0InfoVect2LinkObjId="SW-127395_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127374_0" Pin1InfoVect1LinkObjId="g_1a016c0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3947,-586 4353,-586 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_18d5000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5130,-1165 4353,-1165 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="23543@x" ObjectIDND1="23542@x" ObjectIDZND0="23534@x" ObjectIDZND1="g_1822fe0@0" Pin0InfoVect0LinkObjId="SW-127379_0" Pin0InfoVect1LinkObjId="g_1822fe0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127388_0" Pin1InfoVect1LinkObjId="SW-127387_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5130,-1165 4353,-1165 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_18d5260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4720,-586 4353,-586 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="23550@1" ObjectIDZND0="23551@x" ObjectIDZND1="23535@x" ObjectIDZND2="23529@x" Pin0InfoVect0LinkObjId="SW-127396_0" Pin0InfoVect1LinkObjId="SW-127380_0" Pin0InfoVect2LinkObjId="SW-127374_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127395_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4720,-586 4353,-586 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_18d7ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5169,-420 5169,-391 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="23564@0" ObjectIDZND0="23565@1" Pin0InfoVect0LinkObjId="SW-127410_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127409_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5169,-420 5169,-391 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_18d7e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5169,-364 5169,-351 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="earth" ObjectIDND0="23565@0" ObjectIDZND0="g_19f2170@0" Pin0InfoVect0LinkObjId="g_19f2170_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127410_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5169,-364 5169,-351 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_18d8060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5677,-485 5811,-485 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="earth" ObjectIDND0="23571@x" ObjectIDND1="23568@x" ObjectIDND2="23567@x" ObjectIDZND0="g_19f8990@0" Pin0InfoVect0LinkObjId="g_19f8990_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-127416_0" Pin1InfoVect1LinkObjId="SW-127413_0" Pin1InfoVect2LinkObjId="SW-127412_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5677,-485 5811,-485 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_18da1a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4106,196 3950,196 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_1425530@0" ObjectIDZND0="23532@x" ObjectIDZND1="g_1a009c0@0" Pin0InfoVect0LinkObjId="SW-127377_0" Pin0InfoVect1LinkObjId="g_1a009c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1425530_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4106,196 3950,196 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_18de370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4412,-110 4369,-110 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="reactance" EndDevType0="capacitor" EndDevType1="reactance" EndDevType2="capacitor" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="RB-0_0" Pin0InfoVect1LinkObjId="RB-0_0" Pin0InfoVect2LinkObjId="RB-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="RB-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4412,-110 4369,-110 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_18de5d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4369,-110 4369,-96 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="reactance" BeginDevType1="reactance" BeginDevType2="capacitor" EndDevType0="capacitor" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="RB-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="RB-0_0" Pin1InfoVect1LinkObjId="RB-0_0" Pin1InfoVect2LinkObjId="RB-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4369,-110 4369,-96 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_19e5620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4412,-146 4369,-146 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="reactance" EndDevType0="capacitor" EndDevType1="reactance" EndDevType2="capacitor" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="RB-0_0" Pin0InfoVect1LinkObjId="RB-0_0" Pin0InfoVect2LinkObjId="RB-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="RB-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4412,-146 4369,-146 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_19e5880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4369,-159 4369,-146 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="reactance" EndDevType1="reactance" EndDevType2="capacitor" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="RB-0_0" Pin0InfoVect1LinkObjId="RB-0_0" Pin0InfoVect2LinkObjId="RB-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="RB-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4369,-159 4369,-146 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_19e5ae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4369,-146 4369,-110 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="reactance" BeginDevType1="capacitor" EndDevType0="reactance" EndDevType1="capacitor" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="RB-0_0" Pin0InfoVect1LinkObjId="RB-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="RB-0_0" Pin1InfoVect1LinkObjId="RB-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4369,-146 4369,-110 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_19e6df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4369,18 4369,-7 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="reactance" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="RB-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="RB-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4369,18 4369,-7 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_19e78c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4369,-280 4369,-253 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="reactance" EndDevType2="capacitor" ObjectIDND0="23539@0" ObjectIDZND0="23541@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-127386_0" Pin0InfoVect1LinkObjId="RB-0_0" Pin0InfoVect2LinkObjId="RB-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127384_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4369,-280 4369,-253 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_19e7b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4412,-188 4369,-188 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="reactance" EndDevType0="switch" EndDevType1="switch" EndDevType2="capacitor" ObjectIDND0="0@1" ObjectIDZND0="23541@x" ObjectIDZND1="23539@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-127386_0" Pin0InfoVect1LinkObjId="SW-127384_0" Pin0InfoVect2LinkObjId="RB-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="RB-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4412,-188 4369,-188 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_19e85d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4369,-253 4369,-188 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="reactance" EndDevType1="capacitor" ObjectIDND0="23541@x" ObjectIDND1="23539@x" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="RB-0_0" Pin0InfoVect1LinkObjId="RB-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127386_0" Pin1InfoVect1LinkObjId="SW-127384_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4369,-253 4369,-188 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_19e8830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4369,-188 4369,-174 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="reactance" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="capacitor" ObjectIDND0="0@x" ObjectIDND1="23541@x" ObjectIDND2="23539@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="RB-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="RB-0_0" Pin1InfoVect1LinkObjId="SW-127386_0" Pin1InfoVect2LinkObjId="SW-127384_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4369,-188 4369,-174 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_19e8a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4412,-68 4369,-68 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="reactance" EndDevType0="reactance" EndDevType1="capacitor" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="RB-0_0" Pin0InfoVect1LinkObjId="RB-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="RB-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4412,-68 4369,-68 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_19e9560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4369,-49 4369,-68 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="reactance" EndDevType0="reactance" EndDevType1="capacitor" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="RB-0_0" Pin0InfoVect1LinkObjId="RB-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="RB-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4369,-49 4369,-68 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_19e97c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4369,-68 4369,-81 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="reactance" BeginDevType1="reactance" EndDevType0="capacitor" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="RB-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="RB-0_0" Pin1InfoVect1LinkObjId="RB-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4369,-68 4369,-81 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_19e9a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4391,106 4369,106 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="capacitor" ObjectIDND0="23540@0" ObjectIDZND0="23538@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-127383_0" Pin0InfoVect1LinkObjId="RB-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127385_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4391,106 4369,106 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_19ea460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4369,129 4369,106 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="capacitor" ObjectIDND0="23538@1" ObjectIDZND0="23540@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-127385_0" Pin0InfoVect1LinkObjId="RB-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127383_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4369,129 4369,106 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_19ea6c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4369,106 4369,33 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="capacitor" ObjectIDND0="23540@x" ObjectIDND1="23538@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="RB-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127385_0" Pin1InfoVect1LinkObjId="SW-127383_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4369,106 4369,33 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_19eb190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3952,-383 4369,-383 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="breaker" ObjectIDND0="g_19ffcc0@0" ObjectIDND1="23533@x" ObjectIDZND0="23539@x" ObjectIDZND1="23553@x" ObjectIDZND2="23552@x" Pin0InfoVect0LinkObjId="SW-127384_0" Pin0InfoVect1LinkObjId="SW-127398_0" Pin0InfoVect2LinkObjId="SW-127397_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_19ffcc0_0" Pin1InfoVect1LinkObjId="SW-127378_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3952,-383 4369,-383 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_19ebc60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4210,196 4369,196 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1426240@1" ObjectIDZND0="23538@x" ObjectIDZND1="23546@x" ObjectIDZND2="23547@x" Pin0InfoVect0LinkObjId="SW-127383_0" Pin0InfoVect1LinkObjId="SW-127391_0" Pin0InfoVect2LinkObjId="SW-127392_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1426240_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4210,196 4369,196 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_19ebec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4720,-383 4369,-383 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="23552@1" ObjectIDZND0="23539@x" ObjectIDZND1="23553@x" ObjectIDZND2="g_19ffcc0@0" Pin0InfoVect0LinkObjId="SW-127384_0" Pin0InfoVect1LinkObjId="SW-127398_0" Pin0InfoVect2LinkObjId="g_19ffcc0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127397_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4720,-383 4369,-383 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_19ec120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5130,196 4369,196 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="23546@x" ObjectIDND1="23547@x" ObjectIDZND0="23538@x" ObjectIDZND1="g_1426240@0" Pin0InfoVect0LinkObjId="SW-127383_0" Pin0InfoVect1LinkObjId="g_1426240_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127391_0" Pin1InfoVect1LinkObjId="SW-127392_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5130,196 4369,196 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer"/><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1101f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3769.000000 -1085.000000) translate(0,15)">011FQ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1102740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3770.000000 -687.000000) translate(0,15)">012FQ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a63cf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3772.000000 -303.000000) translate(0,15)">022FQ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a64320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3773.000000 94.000000) translate(0,15)">021FQ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_18223a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4082.000000 -1197.000000) translate(0,15)">011PB</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1822d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4141.000000 -1197.000000) translate(0,15)">012PB</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_18c0a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3659.000000 -1147.000000) translate(0,15)">011B</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_18c1040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3659.000000 -625.000000) translate(0,15)">012B</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_18c1280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3522.000000 -907.000000) translate(0,15)">500kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_18c14c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4424.000000 -912.000000) translate(0,15)">011LB</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_18c1a30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5744.000000 -548.000000) translate(0,15)">永富直流永仁</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_18c1a30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5744.000000 -548.000000) translate(0,33)">侧接地极线路</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_18c3ad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5685.000000 -1147.000000) translate(0,15)">永富直流极1线路</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_18c3fa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5816.000000 -1175.000000) translate(0,15)">+500kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_18c46a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5818.000000 188.000000) translate(0,15)">-500kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_18c4d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5685.500000 165.000000) translate(0,15)">永富直流极2线路</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_18c5570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3527.000000 -130.000000) translate(0,15)">500kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_18c5890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4102.000000 160.000000) translate(0,15)">021PB</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_18c5ad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4167.000000 160.000000) translate(0,15)">022PB</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_18c5d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4451.000000 -81.000000) translate(0,15)">021LB</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_18c5f50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3649.000000 -292.000000) translate(0,15)">022B</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_18c6190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3653.000000 79.000000) translate(0,15)">021B</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimSun" font-size="38" graphid="g_18c6d20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4645.500000 -1313.500000) translate(0,31)">500kV永仁换流站直流系统</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_18d82c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5760.000000 -474.000000) translate(0,15)">武屯接地级</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_18d82c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5760.000000 -474.000000) translate(0,33)">址侧设备</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_18d9a80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3742.000000 -895.000000) translate(0,15)">极1</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_18d9f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3742.000000 -106.000000) translate(0,15)">极2</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a030c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3956.000000 -1131.000000) translate(0,12)">051107</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a036f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3740.000000 -1148.000000) translate(0,12)">050117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a03930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3740.000000 -618.000000) translate(0,12)">050127</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a03b70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3954.000000 -638.000000) translate(0,12)">001207</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a03db0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4360.000000 -1125.000000) translate(0,12)">05111</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a03ff0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4371.000000 -1094.000000) translate(0,12)">051117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a04230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4360.000000 -665.000000) translate(0,12)">00111</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a04470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4370.000000 -722.000000) translate(0,12)">001117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a046b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4370.000000 -637.000000) translate(0,12)">001017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a048f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3743.000000 165.000000) translate(0,12)">050217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a04b30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3743.000000 -363.000000) translate(0,12)">050227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a04d70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3957.000000 143.000000) translate(0,12)">052107</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a04fb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4318.000000 139.000000) translate(0,12)">05221</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a051f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4385.000000 80.000000) translate(0,12)">052217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a05430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4318.000000 -305.000000) translate(0,12)">00221</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a05670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4386.000000 -279.000000) translate(0,12)">002217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a058b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4385.000000 -363.000000) translate(0,12)">002017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a05af0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3959.000000 -349.000000) translate(0,12)">002207</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a05d30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4835.000000 -638.000000) translate(0,12)">001027</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a05f70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4918.000000 -612.000000) translate(0,12)">00102A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a061b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4835.000000 -540.000000) translate(0,12)">00102</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a063f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5039.000000 -639.000000) translate(0,12)">00102A7</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a06630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4717.000000 -610.000000) translate(0,12)">0010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a06870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4717.000000 -407.000000) translate(0,12)">0020</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a06ab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4835.000000 -445.000000) translate(0,12)">00202</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a06cf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4917.000000 -409.000000) translate(0,12)">00202A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a06f30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4835.000000 -348.000000) translate(0,12)">002027</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a07170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5168.000000 -1191.000000) translate(0,12)">05105</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a073b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5137.000000 -1132.000000) translate(0,12)">051057</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a075f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5353.000000 -1066.000000) translate(0,12)">05121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a07830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5353.000000 75.000000) translate(0,12)">05122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a07a70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5251.000000 143.000000) translate(0,12)">051227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a07cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5165.000000 170.000000) translate(0,12)">05205</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a07ef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5137.000000 144.000000) translate(0,12)">052057</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a08130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5252.000000 -1130.000000) translate(0,12)">051217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a08370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5115.000000 -610.000000) translate(0,12)">0030</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a085b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5189.000000 -612.000000) translate(0,12)">00302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a087f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5271.000000 -636.000000) translate(0,12)">003027</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a08a30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5178.000000 -385.000000) translate(0,12)">0040</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a08c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5176.000000 -445.000000) translate(0,12)">00401</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a08eb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5542.000000 -509.000000) translate(0,12)">0050</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a090f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5684.000000 -449.000000) translate(0,12)">005027</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a09330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5462.000000 -511.000000) translate(0,12)">00501</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a09570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5444.000000 -448.000000) translate(0,12)">005017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a097b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5542.000000 -575.000000) translate(0,12)">00600</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a099f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5608.000000 -511.000000) translate(0,12)">00502</text>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-127371">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3790.000000 -1120.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23526" ObjectName="SW-CX_YRH.CX_YRH_050117SW"/>
     <cge:Meas_Ref ObjectId="127371"/>
    <cge:TPSR_Ref TObjectID="23526"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127372">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3790.000000 -590.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23527" ObjectName="SW-CX_YRH.CX_YRH_050127SW"/>
     <cge:Meas_Ref ObjectId="127372"/>
    <cge:TPSR_Ref TObjectID="23527"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127373">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3940.000000 -1101.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23528" ObjectName="SW-CX_YRH.CX_YRH_051107SW"/>
     <cge:Meas_Ref ObjectId="127373"/>
    <cge:TPSR_Ref TObjectID="23528"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127374">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3938.000000 -608.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23529" ObjectName="SW-CX_YRH.CX_YRH_001207SW"/>
     <cge:Meas_Ref ObjectId="127374"/>
    <cge:TPSR_Ref TObjectID="23529"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127376">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3793.000000 -338.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23531" ObjectName="SW-CX_YRH.CX_YRH_050227SW"/>
     <cge:Meas_Ref ObjectId="127376"/>
    <cge:TPSR_Ref TObjectID="23531"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127375">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3793.000000 191.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23530" ObjectName="SW-CX_YRH.CX_YRH_050217SW"/>
     <cge:Meas_Ref ObjectId="127375"/>
    <cge:TPSR_Ref TObjectID="23530"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127378">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3943.000000 -319.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23533" ObjectName="SW-CX_YRH.CX_YRH_002207SW"/>
     <cge:Meas_Ref ObjectId="127378"/>
    <cge:TPSR_Ref TObjectID="23533"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127377">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3941.000000 174.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23532" ObjectName="SW-CX_YRH.CX_YRH_052107SW"/>
     <cge:Meas_Ref ObjectId="127377"/>
    <cge:TPSR_Ref TObjectID="23532"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127403">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4819.000000 -510.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23558" ObjectName="SW-CX_YRH.CX_YRH_00102SW"/>
     <cge:Meas_Ref ObjectId="127403"/>
    <cge:TPSR_Ref TObjectID="23558"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127404">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4819.000000 -415.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23559" ObjectName="SW-CX_YRH.CX_YRH_00202SW"/>
     <cge:Meas_Ref ObjectId="127404"/>
    <cge:TPSR_Ref TObjectID="23559"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127379">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4344.000000 -1095.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23534" ObjectName="SW-CX_YRH.CX_YRH_05111SW"/>
     <cge:Meas_Ref ObjectId="127379"/>
    <cge:TPSR_Ref TObjectID="23534"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127380">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4344.000000 -635.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23535" ObjectName="SW-CX_YRH.CX_YRH_00111SW"/>
     <cge:Meas_Ref ObjectId="127380"/>
    <cge:TPSR_Ref TObjectID="23535"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127381">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4371.000000 -1063.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23536" ObjectName="SW-CX_YRH.CX_YRH_051117SW"/>
     <cge:Meas_Ref ObjectId="127381"/>
    <cge:TPSR_Ref TObjectID="23536"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127382">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4370.000000 -695.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23537" ObjectName="SW-CX_YRH.CX_YRH_001117SW"/>
     <cge:Meas_Ref ObjectId="127382"/>
    <cge:TPSR_Ref TObjectID="23537"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127396">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4371.000000 -610.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23551" ObjectName="SW-CX_YRH.CX_YRH_001017SW"/>
     <cge:Meas_Ref ObjectId="127396"/>
    <cge:TPSR_Ref TObjectID="23551"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127399">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4819.000000 -608.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23554" ObjectName="SW-CX_YRH.CX_YRH_001027SW"/>
     <cge:Meas_Ref ObjectId="127399"/>
    <cge:TPSR_Ref TObjectID="23554"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127401">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4819.000000 -318.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23556" ObjectName="SW-CX_YRH.CX_YRH_002027SW"/>
     <cge:Meas_Ref ObjectId="127401"/>
    <cge:TPSR_Ref TObjectID="23556"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127400">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4915.000000 -581.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23555" ObjectName="SW-CX_YRH.CX_YRH_00102ASW"/>
     <cge:Meas_Ref ObjectId="127400"/>
    <cge:TPSR_Ref TObjectID="23555"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127402">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4915.000000 -378.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23557" ObjectName="SW-CX_YRH.CX_YRH_00202ASW"/>
     <cge:Meas_Ref ObjectId="127402"/>
    <cge:TPSR_Ref TObjectID="23557"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127405">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5023.000000 -609.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23560" ObjectName="SW-CX_YRH.CX_YRH_00102A7SW"/>
     <cge:Meas_Ref ObjectId="127405"/>
    <cge:TPSR_Ref TObjectID="23560"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127407">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5187.000000 -581.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23562" ObjectName="SW-CX_YRH.CX_YRH_00302SW"/>
     <cge:Meas_Ref ObjectId="127407"/>
    <cge:TPSR_Ref TObjectID="23562"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127408">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5255.000000 -606.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23563" ObjectName="SW-CX_YRH.CX_YRH_003027SW"/>
     <cge:Meas_Ref ObjectId="127408"/>
    <cge:TPSR_Ref TObjectID="23563"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127414">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5459.000000 -480.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23569" ObjectName="SW-CX_YRH.CX_YRH_00501SW"/>
     <cge:Meas_Ref ObjectId="127414"/>
    <cge:TPSR_Ref TObjectID="23569"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127416">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5605.000000 -480.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23571" ObjectName="SW-CX_YRH.CX_YRH_00502SW"/>
     <cge:Meas_Ref ObjectId="127416"/>
    <cge:TPSR_Ref TObjectID="23571"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127412">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5540.000000 -544.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23567" ObjectName="SW-CX_YRH.CX_YRH_00600SW"/>
     <cge:Meas_Ref ObjectId="127412"/>
    <cge:TPSR_Ref TObjectID="23567"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127411">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5428.000000 -418.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23566" ObjectName="SW-CX_YRH.CX_YRH_005017SW"/>
     <cge:Meas_Ref ObjectId="127411"/>
    <cge:TPSR_Ref TObjectID="23566"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127413">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5668.000000 -419.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23568" ObjectName="SW-CX_YRH.CX_YRH_005027SW"/>
     <cge:Meas_Ref ObjectId="127413"/>
    <cge:TPSR_Ref TObjectID="23568"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127384">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4360.000000 -275.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23539" ObjectName="SW-CX_YRH.CX_YRH_00221SW"/>
     <cge:Meas_Ref ObjectId="127384"/>
    <cge:TPSR_Ref TObjectID="23539"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127398">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4386.000000 -332.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23553" ObjectName="SW-CX_YRH.CX_YRH_002017SW"/>
     <cge:Meas_Ref ObjectId="127398"/>
    <cge:TPSR_Ref TObjectID="23553"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127386">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4387.000000 -248.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23541" ObjectName="SW-CX_YRH.CX_YRH_002217SW"/>
     <cge:Meas_Ref ObjectId="127386"/>
    <cge:TPSR_Ref TObjectID="23541"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127383">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4360.000000 170.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23538" ObjectName="SW-CX_YRH.CX_YRH_05221SW"/>
     <cge:Meas_Ref ObjectId="127383"/>
    <cge:TPSR_Ref TObjectID="23538"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127385">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4386.000000 111.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23540" ObjectName="SW-CX_YRH.CX_YRH_052217SW"/>
     <cge:Meas_Ref ObjectId="127385"/>
    <cge:TPSR_Ref TObjectID="23540"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127409">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5160.000000 -415.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23564" ObjectName="SW-CX_YRH.CX_YRH_00401SW"/>
     <cge:Meas_Ref ObjectId="127409"/>
    <cge:TPSR_Ref TObjectID="23564"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127387">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5121.000000 -1102.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23542" ObjectName="SW-CX_YRH.CX_YRH_051057SW"/>
     <cge:Meas_Ref ObjectId="127387"/>
    <cge:TPSR_Ref TObjectID="23542"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127389">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5236.000000 -1100.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23544" ObjectName="SW-CX_YRH.CX_YRH_051217SW"/>
     <cge:Meas_Ref ObjectId="127389"/>
    <cge:TPSR_Ref TObjectID="23544"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127388">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5164.000000 -1160.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23543" ObjectName="SW-CX_YRH.CX_YRH_05105SW"/>
     <cge:Meas_Ref ObjectId="127388"/>
    <cge:TPSR_Ref TObjectID="23543"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127390">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5337.000000 -1036.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23545" ObjectName="SW-CX_YRH.CX_YRH_05121SW"/>
     <cge:Meas_Ref ObjectId="127390"/>
    <cge:TPSR_Ref TObjectID="23545"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127393">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5235.000000 174.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23548" ObjectName="SW-CX_YRH.CX_YRH_051227SW"/>
     <cge:Meas_Ref ObjectId="127393"/>
    <cge:TPSR_Ref TObjectID="23548"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127391">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5121.000000 175.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23546" ObjectName="SW-CX_YRH.CX_YRH_052057SW"/>
     <cge:Meas_Ref ObjectId="127391"/>
    <cge:TPSR_Ref TObjectID="23546"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127392">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5162.000000 201.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23547" ObjectName="SW-CX_YRH.CX_YRH_05205SW"/>
     <cge:Meas_Ref ObjectId="127392"/>
    <cge:TPSR_Ref TObjectID="23547"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127394">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5337.000000 106.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23549" ObjectName="SW-CX_YRH.CX_YRH_05122SW"/>
     <cge:Meas_Ref ObjectId="127394"/>
    <cge:TPSR_Ref TObjectID="23549"/></metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-500KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3722.000000 -630.000000)" xlink:href="#transformer2:shape0_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3722.000000 -630.000000)" xlink:href="#transformer2:shape0_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3631.000000 -1127.000000)" xlink:href="#transformer2:shape15_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-500KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3631.000000 -1127.000000)" xlink:href="#transformer2:shape15_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3713.000000 -295.000000)" xlink:href="#transformer2:shape0_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3713.000000 -295.000000)" xlink:href="#transformer2:shape0_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3629.000000 101.000000)" xlink:href="#transformer2:shape15_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-500KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3629.000000 101.000000)" xlink:href="#transformer2:shape15_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 4736.500000 -1227.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="楚雄地区_500kV_永仁换流站.svg" style="fill-opacity:0"><rect height="84" qtmmishow="hidden" width="534" x="4570" y="-1337"/></g>
   <g href="楚雄地区_500kV_永仁换流站.svg" style="fill-opacity:0"><rect height="131" qtmmishow="hidden" width="173" x="4471" y="-1365"/></g>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="276" lineStyle="1" stroke="rgb(255,255,255)" stroke-dasharray="10 5 " stroke-width="1" width="118" x="4298" y="-1034"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="70" lineStyle="2" stroke="rgb(255,255,255)" stroke-dasharray="10 5 " stroke-width="1" width="121" x="5744" y="-496"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="276" lineStyle="1" stroke="rgb(255,255,255)" stroke-dasharray="10 5 " stroke-width="1" width="118" x="4314" y="-219"/>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1821690">
    <use class="BV-500KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 4082.000000 -1152.000000)" xlink:href="#lightningRod:shape105"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1822fe0">
    <use class="BV-500KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 4138.000000 -1152.000000)" xlink:href="#lightningRod:shape105"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1425530">
    <use class="BV-500KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 4101.000000 209.000000)" xlink:href="#lightningRod:shape105"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1426240">
    <use class="BV-500KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 4163.000000 209.000000)" xlink:href="#lightningRod:shape105"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_18d2080">
    <use class="BV-500KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5015.000000 -473.000000)" xlink:href="#lightningRod:shape180"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_18d2890">
    <use class="BV-500KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5329.000000 -473.000000)" xlink:href="#lightningRod:shape180"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19ffcc0">
    <use class="BV-500KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3833.000000 -284.000000)" xlink:href="#lightningRod:shape124"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a009c0">
    <use class="BV-500KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3833.000000 162.000000)" xlink:href="#lightningRod:shape124"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a016c0">
    <use class="BV-500KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3830.000000 -620.000000)" xlink:href="#lightningRod:shape124"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a023c0">
    <use class="BV-500KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3830.000000 -1066.000000)" xlink:href="#lightningRod:shape124"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4338.000000 -987.000000)" xlink:href="#capacitor:shape5"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4338.000000 -871.000000)" xlink:href="#capacitor:shape5"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4338.000000 -793.000000)" xlink:href="#capacitor:shape5"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4354.000000 38.000000)" xlink:href="#capacitor:shape5"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4354.000000 -155.000000)" xlink:href="#capacitor:shape5"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4354.000000 -77.000000)" xlink:href="#capacitor:shape5"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="84" qtmmishow="hidden" width="534" x="4570" y="-1337"/>
    </a>
   <metadata/><rect fill="white" height="84" opacity="0" stroke="white" transform="" width="534" x="4570" y="-1337"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="131" qtmmishow="hidden" width="173" x="4471" y="-1365"/>
    </a>
   <metadata/><rect fill="white" height="131" opacity="0" stroke="white" transform="" width="173" x="4471" y="-1365"/></g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1"/>
</svg>