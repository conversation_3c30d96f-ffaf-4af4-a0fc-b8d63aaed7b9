<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-41" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="3121 -1199 1889 1205">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="29" x2="29" y1="7" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="4" x2="22" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="22" x2="22" y1="0" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="25" x2="25" y1="6" y2="13"/>
   </symbol>
   <symbol id="lightningRod:shape55">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="50" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="9" y1="41" y2="41"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="27,39 5,17 5,5 " stroke-width="1"/>
    <rect height="4" stroke-width="1" width="19" x="7" y="26"/>
   </symbol>
   <symbol id="lightningRod:shape123">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="2" y2="2"/>
    <ellipse cx="8" cy="8" fillStyle="0" rx="8.5" ry="7.5" stroke-width="0.155709"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="25" x2="20" y1="8" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="25" x2="20" y1="8" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="20" x2="20" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="8" x2="5" y1="7" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="10" x2="8" y1="9" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="8" x2="8" y1="4" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="14" x2="11" y1="18" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="16" x2="14" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="14" x2="14" y1="15" y2="18"/>
    <ellipse cx="19" cy="8" fillStyle="0" rx="8.5" ry="7.5" stroke-width="0.155709"/>
    <ellipse cx="14" cy="16" fillStyle="0" rx="9" ry="7.5" stroke-width="0.155709"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="9" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="1" y2="1"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
   </symbol>
   <symbol id="lightningRod:shape195">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="23" y1="6" y2="6"/>
    <rect height="28" stroke-width="1" width="5" x="12" y="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="4" y1="19" y2="38"/>
   </symbol>
   <symbol id="lightningRod:shape74">
    <circle cx="39" cy="14" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="13" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="16" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="19" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="19" y2="19"/>
    <circle cx="30" cy="9" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="30" cy="20" fillStyle="0" r="8.5" stroke-width="1"/>
    <rect height="27" stroke-width="0.416667" width="14" x="0" y="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="71" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="31" y1="71" y2="71"/>
    <rect height="27" stroke-width="0.416667" width="14" x="24" y="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="82" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0587025" x1="31" x2="34" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.173913" x1="30" x2="30" y1="22" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.108974" x1="30" x2="27" y1="23" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0587025" x1="31" x2="34" y1="9" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.173913" x1="30" x2="30" y1="7" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.108974" x1="30" x2="27" y1="8" y2="11"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="41,17 39,15 45,15 43,18 " stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape192">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="26" y1="9" y2="9"/>
    <polyline DF8003:Layer="PUBLIC" points="5,19 17,9 5,0 5,19 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="38,1 26,10 38,19 38,1 " stroke-width="1"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="7" x2="15" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="15" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="7" x2="15" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="15" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="transformer2:shape11_0">
    <ellipse cx="13" cy="34" fillStyle="0" rx="13" ry="12.5" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="40" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="36" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="32" y2="36"/>
   </symbol>
   <symbol id="transformer2:shape11_1">
    <circle cx="13" cy="16" fillStyle="0" r="13" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="20" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="16" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="12" y2="16"/>
   </symbol>
   <symbol id="transformer2:shape0_0">
    <circle cx="25" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="17" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="34" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="17" x2="34" y1="18" y2="18"/>
   </symbol>
   <symbol id="transformer2:shape0_1">
    <ellipse cx="25" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="24" y1="58" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="32" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="16" x2="24" y1="74" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape21_0">
    <circle cx="37" cy="66" fillStyle="0" r="26.5" stroke-width="0.63865"/>
    <polyline points="64,100 1,37 " stroke-width="1"/>
    <polyline points="58,100 64,100 " stroke-width="1"/>
    <polyline points="64,100 64,93 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="32" y1="71" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="32" y1="71" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="46" x2="38" y1="63" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="46" x2="38" y1="63" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="38" y1="70" y2="79"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="38" y1="70" y2="79"/>
   </symbol>
   <symbol id="transformer2:shape21_1">
    <ellipse cx="37" cy="29" fillStyle="0" rx="26.5" ry="25.5" stroke-width="0.62032"/>
    <polyline DF8003:Layer="PUBLIC" points="38,34 31,19 46,19 38,34 38,34 38,34 "/>
   </symbol>
   <symbol id="transformer2:shape25_0">
    <circle cx="16" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,57 40,57 40,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="42" y2="0"/>
    <polyline DF8003:Layer="PUBLIC" points="15,14 21,27 9,27 15,14 15,15 15,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="43" x2="40" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="15" y1="52" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="57" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="57" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="58" y2="62"/>
   </symbol>
   <symbol id="transformer2:shape25_1">
    <circle cx="16" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="12,76 19,76 16,83 12,76 "/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_278b2a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_278c400" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_278cdf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_278de10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_278f070" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_278fc90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_27906f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_27911b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2403810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2403810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_27941f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_27941f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2795f80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2795f80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_2796fa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2798ba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2799790" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_279a550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_279ae90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_279c540" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_279d130" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_279d9b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_279e170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_279f250" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_279fbd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_27a06c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_27a1080" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_27a24e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_27a3000" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_27a3fe0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_27a4ca0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_27b34a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_27a62a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_27a74d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_27a8ab0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1215" width="1899" x="3116" y="-1204"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,0)" stroke-width="1" x1="5007" x2="5009" y1="-390" y2="-381"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-37644">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4004.000000 -658.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6002" ObjectName="SW-CX_FM.CX_FM_301BK"/>
     <cge:Meas_Ref ObjectId="37644"/>
    <cge:TPSR_Ref TObjectID="6002"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37650">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4004.000000 -443.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6008" ObjectName="SW-CX_FM.CX_FM_001BK"/>
     <cge:Meas_Ref ObjectId="37650"/>
    <cge:TPSR_Ref TObjectID="6008"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37729">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3724.000000 -267.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6029" ObjectName="SW-CX_FM.CX_FM_091BK"/>
     <cge:Meas_Ref ObjectId="37729"/>
    <cge:TPSR_Ref TObjectID="6029"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37652">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4644.000000 -450.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6010" ObjectName="SW-CX_FM.CX_FM_002BK"/>
     <cge:Meas_Ref ObjectId="37652"/>
    <cge:TPSR_Ref TObjectID="6010"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37646">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4644.000000 -665.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6004" ObjectName="SW-CX_FM.CX_FM_302BK"/>
     <cge:Meas_Ref ObjectId="37646"/>
    <cge:TPSR_Ref TObjectID="6004"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37654">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3901.000000 -267.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6012" ObjectName="SW-CX_FM.CX_FM_092BK"/>
     <cge:Meas_Ref ObjectId="37654"/>
    <cge:TPSR_Ref TObjectID="6012"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37732">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4096.000000 -267.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6032" ObjectName="SW-CX_FM.CX_FM_093BK"/>
     <cge:Meas_Ref ObjectId="37732"/>
    <cge:TPSR_Ref TObjectID="6032"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37660">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4444.000000 -267.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6018" ObjectName="SW-CX_FM.CX_FM_095BK"/>
     <cge:Meas_Ref ObjectId="37660"/>
    <cge:TPSR_Ref TObjectID="6018"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37663">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4620.000000 -267.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6021" ObjectName="SW-CX_FM.CX_FM_096BK"/>
     <cge:Meas_Ref ObjectId="37663"/>
    <cge:TPSR_Ref TObjectID="6021"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37657">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4269.000000 -268.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6015" ObjectName="SW-CX_FM.CX_FM_094BK"/>
     <cge:Meas_Ref ObjectId="37657"/>
    <cge:TPSR_Ref TObjectID="6015"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_FM.CX_FM_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3591,-373 4898,-373 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="5999" ObjectName="BS-CX_FM.CX_FM_9IM"/>
    <cge:TPSR_Ref TObjectID="5999"/></metadata>
   <polyline fill="none" opacity="0" points="3591,-373 4898,-373 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_FM.CX_FM_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3955,-783 4728,-783 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="5998" ObjectName="BS-CX_FM.CX_FM_3IM"/>
    <cge:TPSR_Ref TObjectID="5998"/></metadata>
   <polyline fill="none" opacity="0" points="3955,-783 4728,-783 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-CX_FM.091Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3724.000000 -100.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34375" ObjectName="EC-CX_FM.091Ld"/>
    <cge:TPSR_Ref TObjectID="34375"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_FM.092Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3901.000000 -99.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34376" ObjectName="EC-CX_FM.092Ld"/>
    <cge:TPSR_Ref TObjectID="34376"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_FM.093Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4096.000000 -95.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34377" ObjectName="EC-CX_FM.093Ld"/>
    <cge:TPSR_Ref TObjectID="34377"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_FM.094Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4269.000000 -97.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34378" ObjectName="EC-CX_FM.094Ld"/>
    <cge:TPSR_Ref TObjectID="34378"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_FM.095Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4444.000000 -98.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34379" ObjectName="EC-CX_FM.095Ld"/>
    <cge:TPSR_Ref TObjectID="34379"/></metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_32b3ed0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4464.000000 -743.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28b12b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4390.000000 -905.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_2828220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4372,-753 4394,-753 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5998@0" ObjectIDND1="6006@x" ObjectIDZND0="6007@0" Pin0InfoVect0LinkObjId="SW-37649_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_32a4c70_0" Pin1InfoVect1LinkObjId="SW-37648_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4372,-753 4394,-753 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_228b520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4430,-753 4469,-753 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6007@1" ObjectIDZND0="g_32b3ed0@0" Pin0InfoVect0LinkObjId="g_32b3ed0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37649_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4430,-753 4469,-753 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35e4500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4013,-783 4013,-764 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5998@0" ObjectIDZND0="6003@1" Pin0InfoVect0LinkObjId="SW-37645_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_32a4c70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4013,-783 4013,-764 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_39df100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4356,-915 4395,-915 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6001@1" ObjectIDZND0="g_28b12b0@0" Pin0InfoVect0LinkObjId="g_28b12b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37643_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4356,-915 4395,-915 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f01910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4013,-373 4013,-391 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5999@0" ObjectIDZND0="6009@0" Pin0InfoVect0LinkObjId="SW-37651_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3c9faf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4013,-373 4013,-391 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2654cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4013,-427 4013,-451 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6009@1" ObjectIDZND0="6008@0" Pin0InfoVect0LinkObjId="SW-37650_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37651_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4013,-427 4013,-451 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2862750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4013,-728 4013,-693 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6003@0" ObjectIDZND0="6002@1" Pin0InfoVect0LinkObjId="SW-37644_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37645_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4013,-728 4013,-693 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_224aa10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4013,-478 4013,-529 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="6008@1" ObjectIDZND0="6025@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37650_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4013,-478 4013,-529 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c45530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4013,-609 4013,-666 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="6025@1" ObjectIDZND0="6002@0" Pin0InfoVect0LinkObjId="SW-37644_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_224aa10_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4013,-609 4013,-666 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bbdf00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4245,-995 4196,-995 4196,-913 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_3357bc0@0" ObjectIDND1="6001@x" ObjectIDND2="6000@x" ObjectIDZND0="g_2716910@0" Pin0InfoVect0LinkObjId="g_2716910_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3357bc0_0" Pin1InfoVect1LinkObjId="SW-37643_0" Pin1InfoVect2LinkObjId="SW-37642_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4245,-995 4196,-995 4196,-913 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3299b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4245,-995 4245,-976 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="powerLine" EndDevType0="lightningRod" ObjectIDND0="6001@x" ObjectIDND1="6000@x" ObjectIDND2="34564@1" ObjectIDZND0="g_3357bc0@0" Pin0InfoVect0LinkObjId="g_3357bc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-37643_0" Pin1InfoVect1LinkObjId="SW-37642_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4245,-995 4245,-976 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_348fe20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4245,-931 4245,-905 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_3357bc0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_32b3ed0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3357bc0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4245,-931 4245,-905 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22b4730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4245,-995 4298,-995 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="powerLine" ObjectIDND0="g_3357bc0@0" ObjectIDND1="g_2716910@0" ObjectIDZND0="6001@x" ObjectIDZND1="6000@x" ObjectIDZND2="34564@1" Pin0InfoVect0LinkObjId="SW-37643_0" Pin0InfoVect1LinkObjId="SW-37642_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3357bc0_0" Pin1InfoVect1LinkObjId="g_2716910_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4245,-995 4298,-995 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bcb020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4298,-1027 4298,-995 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="34564@1" ObjectIDZND0="g_3357bc0@0" ObjectIDZND1="g_2716910@0" ObjectIDZND2="6001@x" Pin0InfoVect0LinkObjId="g_3357bc0_0" Pin0InfoVect1LinkObjId="g_2716910_0" Pin0InfoVect2LinkObjId="SW-37643_0" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4298,-1027 4298,-995 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cbc020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4298,-783 4298,-828 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5998@0" ObjectIDZND0="6000@0" Pin0InfoVect0LinkObjId="SW-37642_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_32a4c70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4298,-783 4298,-828 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36c8610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4320,-915 4298,-915 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="6001@0" ObjectIDZND0="6000@x" ObjectIDZND1="g_3357bc0@0" ObjectIDZND2="g_2716910@0" Pin0InfoVect0LinkObjId="SW-37642_0" Pin0InfoVect1LinkObjId="g_3357bc0_0" Pin0InfoVect2LinkObjId="g_2716910_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37643_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4320,-915 4298,-915 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28c2310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4298,-864 4298,-915 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="6000@1" ObjectIDZND0="6001@x" ObjectIDZND1="g_3357bc0@0" ObjectIDZND2="g_2716910@0" Pin0InfoVect0LinkObjId="SW-37643_0" Pin0InfoVect1LinkObjId="g_3357bc0_0" Pin0InfoVect2LinkObjId="g_2716910_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37642_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4298,-864 4298,-915 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32a32f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4298,-915 4298,-995 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="6001@x" ObjectIDND1="6000@x" ObjectIDZND0="g_3357bc0@0" ObjectIDZND1="g_2716910@0" ObjectIDZND2="34564@1" Pin0InfoVect0LinkObjId="g_3357bc0_0" Pin0InfoVect1LinkObjId="g_2716910_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-37643_0" Pin1InfoVect1LinkObjId="SW-37642_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4298,-915 4298,-995 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32a4c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4372,-753 4372,-783 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="6007@x" ObjectIDND1="6006@x" ObjectIDZND0="5998@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-37649_0" Pin1InfoVect1LinkObjId="SW-37648_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4372,-753 4372,-783 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_335ade0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4401,-631 4401,-662 4372,-662 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_2773a90@0" ObjectIDZND0="6006@x" ObjectIDZND1="g_39a9830@0" Pin0InfoVect0LinkObjId="SW-37648_0" Pin0InfoVect1LinkObjId="g_39a9830_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2773a90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4401,-631 4401,-662 4372,-662 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24662a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3733,-373 3733,-353 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5999@0" ObjectIDZND0="6030@1" Pin0InfoVect0LinkObjId="SW-37730_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3c9faf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3733,-373 3733,-353 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1db6080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3733,-317 3733,-302 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6030@0" ObjectIDZND0="6029@1" Pin0InfoVect0LinkObjId="SW-37729_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37730_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3733,-317 3733,-302 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1db59a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3733,-275 3733,-260 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="6029@0" ObjectIDZND0="6031@1" Pin0InfoVect0LinkObjId="SW-37731_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37729_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3733,-275 3733,-260 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34d0290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3733,-211 3764,-211 3764,-194 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="6031@x" ObjectIDND1="34375@x" ObjectIDZND0="g_39de380@0" Pin0InfoVect0LinkObjId="g_39de380_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-37731_0" Pin1InfoVect1LinkObjId="EC-CX_FM.091Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3733,-211 3764,-211 3764,-194 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2618fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3733,-224 3733,-211 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="6031@0" ObjectIDZND0="g_39de380@0" ObjectIDZND1="34375@x" Pin0InfoVect0LinkObjId="g_39de380_0" Pin0InfoVect1LinkObjId="EC-CX_FM.091Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37731_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3733,-224 3733,-211 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2667520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3733,-211 3733,-127 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="6031@x" ObjectIDND1="g_39de380@0" ObjectIDZND0="34375@0" Pin0InfoVect0LinkObjId="EC-CX_FM.091Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-37731_0" Pin1InfoVect1LinkObjId="g_39de380_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3733,-211 3733,-127 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29d52e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4653,-783 4653,-771 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5998@0" ObjectIDZND0="6005@1" Pin0InfoVect0LinkObjId="SW-37647_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_32a4c70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4653,-783 4653,-771 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2840850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4653,-735 4653,-700 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6005@0" ObjectIDZND0="6004@1" Pin0InfoVect0LinkObjId="SW-37646_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37647_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4653,-735 4653,-700 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22aa7a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4653,-485 4653,-536 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="6010@1" ObjectIDZND0="6026@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37652_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4653,-485 4653,-536 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2834d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4653,-623 4653,-673 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="6026@0" ObjectIDZND0="6004@0" Pin0InfoVect0LinkObjId="SW-37646_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22aa7a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4653,-623 4653,-673 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1be0720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3910,-373 3910,-353 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5999@0" ObjectIDZND0="6013@1" Pin0InfoVect0LinkObjId="SW-37655_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3c9faf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3910,-373 3910,-353 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39dd210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3910,-317 3910,-302 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6013@0" ObjectIDZND0="6012@1" Pin0InfoVect0LinkObjId="SW-37654_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37655_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3910,-317 3910,-302 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39e88e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3910,-275 3910,-260 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="6012@0" ObjectIDZND0="6014@1" Pin0InfoVect0LinkObjId="SW-37656_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37654_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3910,-275 3910,-260 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c2a2f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3910,-212 3941,-212 3941,-194 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="6014@x" ObjectIDND1="34376@x" ObjectIDZND0="g_3cbd6c0@0" Pin0InfoVect0LinkObjId="g_3cbd6c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-37656_0" Pin1InfoVect1LinkObjId="EC-CX_FM.092Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3910,-212 3941,-212 3941,-194 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39e5550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3910,-224 3910,-210 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="6014@0" ObjectIDZND0="g_3cbd6c0@0" ObjectIDZND1="34376@x" Pin0InfoVect0LinkObjId="g_3cbd6c0_0" Pin0InfoVect1LinkObjId="EC-CX_FM.092Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37656_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3910,-224 3910,-210 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22a91d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3910,-210 3910,-126 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="6014@x" ObjectIDND1="g_3cbd6c0@0" ObjectIDZND0="34376@0" Pin0InfoVect0LinkObjId="EC-CX_FM.092Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-37656_0" Pin1InfoVect1LinkObjId="g_3cbd6c0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3910,-210 3910,-126 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23f7cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4105,-373 4105,-353 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5999@0" ObjectIDZND0="6033@1" Pin0InfoVect0LinkObjId="SW-37733_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3c9faf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4105,-373 4105,-353 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c5dae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4105,-317 4105,-302 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6033@0" ObjectIDZND0="6032@1" Pin0InfoVect0LinkObjId="SW-37732_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37733_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4105,-317 4105,-302 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28761f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4105,-275 4105,-260 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="6032@0" ObjectIDZND0="6034@1" Pin0InfoVect0LinkObjId="SW-37734_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37732_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4105,-275 4105,-260 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_264d7d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4105,-212 4132,-212 4132,-194 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="load" EndDevType0="lightningRod" ObjectIDND0="6034@x" ObjectIDND1="g_1ba2ba0@0" ObjectIDND2="34377@x" ObjectIDZND0="g_3ca6b40@0" Pin0InfoVect0LinkObjId="g_3ca6b40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-37734_0" Pin1InfoVect1LinkObjId="g_1ba2ba0_0" Pin1InfoVect2LinkObjId="EC-CX_FM.093Ld_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4105,-212 4132,-212 4132,-194 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2614690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4105,-224 4105,-212 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="6034@0" ObjectIDZND0="g_3ca6b40@0" ObjectIDZND1="g_1ba2ba0@0" ObjectIDZND2="34377@x" Pin0InfoVect0LinkObjId="g_3ca6b40_0" Pin0InfoVect1LinkObjId="g_1ba2ba0_0" Pin0InfoVect2LinkObjId="EC-CX_FM.093Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37734_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4105,-224 4105,-212 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3cb4e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4278,-276 4278,-260 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="6015@0" ObjectIDZND0="6017@1" Pin0InfoVect0LinkObjId="SW-37659_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37657_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4278,-276 4278,-260 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_290f400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4278,-212 4308,-212 4308,-194 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="6017@x" ObjectIDND1="34378@x" ObjectIDZND0="g_1f30fc0@0" Pin0InfoVect0LinkObjId="g_1f30fc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-37659_0" Pin1InfoVect1LinkObjId="EC-CX_FM.094Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4278,-212 4308,-212 4308,-194 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2840a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4453,-373 4453,-353 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5999@0" ObjectIDZND0="6019@1" Pin0InfoVect0LinkObjId="SW-37661_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3c9faf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4453,-373 4453,-353 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ef6400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4453,-317 4453,-304 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6019@0" ObjectIDZND0="6018@1" Pin0InfoVect0LinkObjId="SW-37660_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37661_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4453,-317 4453,-304 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39be520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4453,-275 4453,-260 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="6018@0" ObjectIDZND0="6020@1" Pin0InfoVect0LinkObjId="SW-37662_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37660_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4453,-275 4453,-260 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_271c620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4453,-212 4484,-212 4484,-194 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="6020@x" ObjectIDND1="34379@x" ObjectIDZND0="g_1eb14d0@0" Pin0InfoVect0LinkObjId="g_1eb14d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-37662_0" Pin1InfoVect1LinkObjId="EC-CX_FM.095Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4453,-212 4484,-212 4484,-194 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28f0840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4629,-373 4629,-353 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5999@0" ObjectIDZND0="6022@1" Pin0InfoVect0LinkObjId="SW-37664_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3c9faf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4629,-373 4629,-353 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39c37d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4629,-317 4629,-302 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6022@0" ObjectIDZND0="6021@1" Pin0InfoVect0LinkObjId="SW-37663_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37664_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4629,-317 4629,-302 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1be93a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4629,-275 4629,-260 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="6021@0" ObjectIDZND0="6023@1" Pin0InfoVect0LinkObjId="SW-37665_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37663_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4629,-275 4629,-260 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27420a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4629,-212 4660,-212 4660,-194 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="6023@x" ObjectIDZND0="g_26fd230@0" Pin0InfoVect0LinkObjId="g_26fd230_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37665_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4629,-212 4660,-212 4660,-194 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39c2710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4821,-373 4821,-353 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5999@0" ObjectIDZND0="6024@1" Pin0InfoVect0LinkObjId="SW-37666_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3c9faf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4821,-373 4821,-353 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2687510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4821,-317 4821,-272 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="6024@0" ObjectIDZND0="g_29105f0@0" ObjectIDZND1="g_2699dc0@0" Pin0InfoVect0LinkObjId="g_29105f0_0" Pin0InfoVect1LinkObjId="g_2699dc0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37666_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4821,-317 4821,-272 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2282d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4788,-241 4788,-271 4821,-271 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_2699dc0@0" ObjectIDZND0="6024@x" ObjectIDZND1="g_29105f0@0" Pin0InfoVect0LinkObjId="SW-37666_0" Pin0InfoVect1LinkObjId="g_29105f0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2699dc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4788,-241 4788,-271 4821,-271 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c644c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4821,-271 4853,-271 4853,-256 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="6024@x" ObjectIDND1="g_2699dc0@0" ObjectIDZND0="g_29105f0@0" Pin0InfoVect0LinkObjId="g_29105f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-37666_0" Pin1InfoVect1LinkObjId="g_2699dc0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4821,-271 4853,-271 4853,-256 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28c2d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4853,-212 4853,-191 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_29105f0@1" ObjectIDZND0="g_2779480@0" Pin0InfoVect0LinkObjId="g_2779480_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29105f0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4853,-212 4853,-191 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_277b000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4372,-753 4372,-730 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="busSection" EndDevType0="switch" ObjectIDND0="6007@x" ObjectIDND1="5998@0" ObjectIDZND0="6006@1" Pin0InfoVect0LinkObjId="SW-37648_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-37649_0" Pin1InfoVect1LinkObjId="g_32a4c70_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4372,-753 4372,-730 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d20b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4372,-694 4372,-662 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="6006@0" ObjectIDZND0="g_39a9830@0" ObjectIDZND1="g_2773a90@0" Pin0InfoVect0LinkObjId="g_39a9830_0" Pin0InfoVect1LinkObjId="g_2773a90_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37648_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4372,-694 4372,-662 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28ce330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4372,-662 4337,-662 4337,-643 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="6006@x" ObjectIDND1="g_2773a90@0" ObjectIDZND0="g_39a9830@1" Pin0InfoVect0LinkObjId="g_39a9830_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-37648_0" Pin1InfoVect1LinkObjId="g_2773a90_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4372,-662 4337,-662 4337,-643 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ca7110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4337,-613 4337,-594 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_39a9830@0" ObjectIDZND0="g_2840650@0" Pin0InfoVect0LinkObjId="g_2840650_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_39a9830_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4337,-613 4337,-594 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28f2970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4653,-373 4653,-398 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5999@0" ObjectIDZND0="6011@0" Pin0InfoVect0LinkObjId="SW-37653_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3c9faf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4653,-373 4653,-398 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2651ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4653,-434 4653,-458 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6011@1" ObjectIDZND0="6010@0" Pin0InfoVect0LinkObjId="SW-37652_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37653_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4653,-434 4653,-458 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2710c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4278,-303 4278,-319 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="6015@1" ObjectIDZND0="6016@0" Pin0InfoVect0LinkObjId="SW-37658_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37657_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4278,-303 4278,-319 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c9faf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4278,-355 4278,-373 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="6016@1" ObjectIDZND0="5999@0" Pin0InfoVect0LinkObjId="g_1ee53a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37658_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4278,-355 4278,-373 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ee53a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3662,-431 3662,-373 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="busSection" ObjectIDND0="g_1eaf5e0@0" ObjectIDZND0="5999@0" Pin0InfoVect0LinkObjId="g_3c9faf0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1eaf5e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3662,-431 3662,-373 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3217c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3696,-538 3651,-538 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1effe10@0" ObjectIDZND0="g_1eaf5e0@0" Pin0InfoVect0LinkObjId="g_1eaf5e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1effe10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3696,-538 3651,-538 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28b5ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3651,-464 3651,-538 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1eaf5e0@1" ObjectIDZND0="g_1effe10@0" Pin0InfoVect0LinkObjId="g_1effe10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1eaf5e0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3651,-464 3651,-538 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_271c1e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3651,-538 3651,-600 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" ObjectIDND0="g_1eaf5e0@0" ObjectIDND1="g_1effe10@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1eaf5e0_0" Pin1InfoVect1LinkObjId="g_1effe10_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3651,-538 3651,-600 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2741800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4028,-110 4028,-124 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_3c68570@1" Pin0InfoVect0LinkObjId="g_3c68570_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_32b3ed0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4028,-110 4028,-124 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c68ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4105,-201 4078,-201 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="load" EndDevType0="lightningRod" ObjectIDND0="6034@x" ObjectIDND1="g_3ca6b40@0" ObjectIDND2="34377@x" ObjectIDZND0="g_1ba2ba0@0" Pin0InfoVect0LinkObjId="g_1ba2ba0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-37734_0" Pin1InfoVect1LinkObjId="g_3ca6b40_0" Pin1InfoVect2LinkObjId="EC-CX_FM.093Ld_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4105,-201 4078,-201 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c66260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4105,-212 4105,-201 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="6034@x" ObjectIDND1="g_3ca6b40@0" ObjectIDZND0="g_1ba2ba0@0" ObjectIDZND1="34377@x" Pin0InfoVect0LinkObjId="g_1ba2ba0_0" Pin0InfoVect1LinkObjId="EC-CX_FM.093Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-37734_0" Pin1InfoVect1LinkObjId="g_3ca6b40_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4105,-212 4105,-201 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c664a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4105,-201 4105,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="load" ObjectIDND0="6034@x" ObjectIDND1="g_3ca6b40@0" ObjectIDND2="g_1ba2ba0@0" ObjectIDZND0="34377@0" Pin0InfoVect0LinkObjId="EC-CX_FM.093Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-37734_0" Pin1InfoVect1LinkObjId="g_3ca6b40_0" Pin1InfoVect2LinkObjId="g_1ba2ba0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4105,-201 4105,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35fcdd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4453,-224 4453,-212 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="6020@0" ObjectIDZND0="g_1eb14d0@0" ObjectIDZND1="34379@x" Pin0InfoVect0LinkObjId="g_1eb14d0_0" Pin0InfoVect1LinkObjId="EC-CX_FM.095Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37662_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4453,-224 4453,-212 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1edbb80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4629,-224 4629,-212 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="6023@0" ObjectIDZND0="g_26fd230@0" Pin0InfoVect0LinkObjId="g_26fd230_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37665_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4629,-224 4629,-212 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ec0a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4453,-125 4453,-212 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="34379@0" ObjectIDZND0="g_1eb14d0@0" ObjectIDZND1="6020@x" Pin0InfoVect0LinkObjId="g_1eb14d0_0" Pin0InfoVect1LinkObjId="SW-37662_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-CX_FM.095Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4453,-125 4453,-212 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22e7560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4629,-97 4629,-212 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" EndDevType1="switch" ObjectIDZND0="g_26fd230@0" ObjectIDZND1="6023@x" Pin0InfoVect0LinkObjId="g_26fd230_0" Pin0InfoVect1LinkObjId="SW-37665_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4629,-97 4629,-212 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c4a020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4278,-124 4278,-212 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="34378@0" ObjectIDZND0="g_1f30fc0@0" ObjectIDZND1="6017@x" Pin0InfoVect0LinkObjId="g_1f30fc0_0" Pin0InfoVect1LinkObjId="SW-37659_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-CX_FM.094Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4278,-124 4278,-212 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c4a260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4278,-212 4278,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_1f30fc0@0" ObjectIDND1="34378@x" ObjectIDZND0="6017@0" Pin0InfoVect0LinkObjId="SW-37659_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1f30fc0_0" Pin1InfoVect1LinkObjId="EC-CX_FM.094Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4278,-212 4278,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3ca3300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4029,-169 4029,-201 4045,-201 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3c68570@0" ObjectIDZND0="g_1ba2ba0@1" Pin0InfoVect0LinkObjId="g_1ba2ba0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3c68570_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4029,-169 4029,-201 4045,-201 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="5998" cx="4013" cy="-783" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5998" cx="4298" cy="-783" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5998" cx="4372" cy="-783" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5998" cx="4653" cy="-783" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5999" cx="4013" cy="-373" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5999" cx="3733" cy="-373" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5999" cx="3910" cy="-373" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5999" cx="4105" cy="-373" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5999" cx="4453" cy="-373" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5999" cx="4629" cy="-373" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5999" cx="4821" cy="-373" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5999" cx="4653" cy="-373" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5999" cx="4278" cy="-373" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5999" cx="3662" cy="-373" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-37310" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3410.000000 -1086.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5892" ObjectName="DYN-CX_FM"/>
     <cge:Meas_Ref ObjectId="37310"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26be310" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4333.000000 -568.000000) translate(0,12)">35kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_283db00" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4271.000000 -985.000000) translate(0,12)">35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_283db00" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4271.000000 -985.000000) translate(0,27)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_283db00" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4271.000000 -985.000000) translate(0,42)">1</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_283db00" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4271.000000 -985.000000) translate(0,57)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_283db00" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4271.000000 -985.000000) translate(0,72)">站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_283db00" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4271.000000 -985.000000) translate(0,87)">用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_283db00" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4271.000000 -985.000000) translate(0,102)">变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f33760" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3702.000000 -188.000000) translate(0,12)">10</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f33760" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3702.000000 -188.000000) translate(0,27)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f33760" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3702.000000 -188.000000) translate(0,42)">富</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f33760" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3702.000000 -188.000000) translate(0,57)">II</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f33760" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3702.000000 -188.000000) translate(0,72)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f33760" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3702.000000 -188.000000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22f4a60" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3879.000000 -187.000000) translate(0,12)">10</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22f4a60" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3879.000000 -187.000000) translate(0,27)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22f4a60" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3879.000000 -187.000000) translate(0,42)">集</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22f4a60" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3879.000000 -187.000000) translate(0,57)">镇</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22f4a60" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3879.000000 -187.000000) translate(0,72)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2880280" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4082.000000 -192.000000) translate(0,12)">10</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2880280" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4082.000000 -192.000000) translate(0,27)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2880280" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4082.000000 -192.000000) translate(0,42)">富</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2880280" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4082.000000 -192.000000) translate(0,57)">I</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2880280" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4082.000000 -192.000000) translate(0,72)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2880280" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4082.000000 -192.000000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c68220" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4246.000000 -185.000000) translate(0,12)">10</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c68220" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4246.000000 -185.000000) translate(0,27)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c68220" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4246.000000 -185.000000) translate(0,42)">富</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c68220" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4246.000000 -185.000000) translate(0,57)">军</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c68220" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4246.000000 -185.000000) translate(0,72)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f037c0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4422.000000 -183.000000) translate(0,12)">10</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f037c0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4422.000000 -183.000000) translate(0,27)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f037c0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4422.000000 -183.000000) translate(0,42)">富</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f037c0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4422.000000 -183.000000) translate(0,57)">苍</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f037c0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4422.000000 -183.000000) translate(0,72)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bafd90" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4598.000000 -182.000000) translate(0,12)">10</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bafd90" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4598.000000 -182.000000) translate(0,27)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bafd90" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4598.000000 -182.000000) translate(0,42)">备</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bafd90" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4598.000000 -182.000000) translate(0,57)">用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26ff120" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4788.000000 -165.000000) translate(0,12)">10kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23fe360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4318.000000 -941.000000) translate(0,12)">39117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25dfcc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4305.000000 -853.000000) translate(0,12)">3911</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28be290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4392.000000 -779.000000) translate(0,12)">39010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_281c530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4379.000000 -719.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28bb820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4020.000000 -753.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d0c090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4662.000000 -694.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39be160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4660.000000 -760.000000) translate(0,12)">3021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28cea30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4662.000000 -479.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2429200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4660.000000 -423.000000) translate(0,12)">0021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28f2b60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4022.000000 -472.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3be8cf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4020.000000 -416.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26994c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3740.000000 -249.000000) translate(0,12)">0916</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2298fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3740.000000 -342.000000) translate(0,12)">0911</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28f3300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3917.000000 -249.000000) translate(0,12)">0926</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28f3540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3917.000000 -342.000000) translate(0,12)">0921</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1dc4000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4108.000000 -249.000000) translate(0,12)">0936</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1dc4240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4108.000000 -342.000000) translate(0,12)">0931</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28ab930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4284.000000 -342.000000) translate(0,12)">0941</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39baa70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4284.000000 -249.000000) translate(0,12)">0946</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39bacb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4460.000000 -249.000000) translate(0,12)">0956</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28beed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4460.000000 -341.000000) translate(0,12)">0951</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28bf0f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4636.000000 -249.000000) translate(0,12)">0966</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2774d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4636.000000 -341.000000) translate(0,12)">0961</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2774f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3905.000000 -802.000000) translate(0,12)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2741c50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3651.000000 -359.000000) translate(0,12)">10kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c0a900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4828.000000 -341.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_39a71a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3141.000000 -1026.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_39a71a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3141.000000 -1026.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_39a71a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3141.000000 -1026.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_39a71a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3141.000000 -1026.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_39a71a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3141.000000 -1026.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_39a71a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3141.000000 -1026.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_39a71a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3141.000000 -1026.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1eea8b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3142.000000 -588.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1eea8b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3142.000000 -588.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1eea8b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3142.000000 -588.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1eea8b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3142.000000 -588.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1eea8b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3142.000000 -588.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1eea8b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3142.000000 -588.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1eea8b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3142.000000 -588.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1eea8b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3142.000000 -588.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1eea8b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3142.000000 -588.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1eea8b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3142.000000 -588.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1eea8b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3142.000000 -588.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1eea8b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3142.000000 -588.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1eea8b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3142.000000 -588.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1eea8b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3142.000000 -588.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1eea8b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3142.000000 -588.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1eea8b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3142.000000 -588.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1eea8b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3142.000000 -588.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1eea8b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3142.000000 -588.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_270cc20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3284.500000 -1166.500000) translate(0,16)">富民变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2777b60" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4262.000000 -1124.000000) translate(0,12)">35kV龙富线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28bed40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3594.000000 -662.000000) translate(0,12)">10kV信号发生源</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1eb9b70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3996.000000 -119.000000) translate(0,12)">10</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1eb9b70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3996.000000 -119.000000) translate(0,27)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1eb9b70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3996.000000 -119.000000) translate(0,42)">2</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1eb9b70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3996.000000 -119.000000) translate(0,57)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1eb9b70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3996.000000 -119.000000) translate(0,72)">站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1eb9b70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3996.000000 -119.000000) translate(0,87)">用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1eb9b70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3996.000000 -119.000000) translate(0,102)">变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c5a4e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3991.000000 -9.000000) translate(0,12)">SH15-50/10</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2710580" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4053.000000 -585.000000) translate(0,12)">温度（℃）：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39c1110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4022.000000 -687.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c48060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3927.000000 -579.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ef3c20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4570.000000 -603.000000) translate(0,12)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c477d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3742.000000 -296.000000) translate(0,12)">091</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c47ad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3919.000000 -296.000000) translate(0,12)">092</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ef5500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4110.000000 -296.000000) translate(0,12)">093</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ef5740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4286.000000 -296.000000) translate(0,12)">094</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ef4490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4462.000000 -296.000000) translate(0,12)">095</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ef46a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4638.000000 -296.000000) translate(0,12)">096</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2465810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3286.000000 -232.000000) translate(0,17)">3890113</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_1f32b40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3493.000000 -1122.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_3c96f30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3493.000000 -1157.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_26cb980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3165.000000 -757.000000) translate(0,16)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26cafa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3898.000000 -552.000000) translate(0,12)">S9-4000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26cafa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3898.000000 -552.000000) translate(0,27)">Yd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26cafa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3898.000000 -552.000000) translate(0,42)">Uk%=7.33</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_281bed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4519.000000 -580.000000) translate(0,12)">SZ9-4000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_281bed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4519.000000 -580.000000) translate(0,27)">Yd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_281bed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4519.000000 -580.000000) translate(0,42)">Uk%=6.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3c697a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3262.000000 -171.500000) translate(0,17)">13908784302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3721280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3135.000000 -189.000000) translate(0,17)">楚雄巡维中</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3721280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3135.000000 -189.000000) translate(0,38)">心变运一班：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_359f530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3626.500000 -1138.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" graphid="g_28b7490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3156.000000 -692.000000) translate(0,20)">隔刀远控</text>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3123" y="-1198"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3122" y="-1078"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3122" y="-598"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="43" stroke="rgb(0,255,0)" stroke-width="1" width="63" x="3613" y="-643"/>
   <rect DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" fillStyle="1" height="42" stroke="rgb(255,255,255)" stroke-width="1" width="72" x="3612" y="-1151"/>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_LT" endPointId="0" endStationName="CX_FM" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_LongFu" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4298,-1025 4298,-1079 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="34564" ObjectName="AC-35kV.LN_LongFu"/>
    <cge:TPSR_Ref TObjectID="34564_SS-41"/></metadata>
   <polyline fill="none" opacity="0" points="4298,-1025 4298,-1079 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_3357bc0">
    <use class="BV-35KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4240.000000 -926.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2840650">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4324.000000 -569.000000)" xlink:href="#lightningRod:shape123"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29105f0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4848.000000 -206.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2779480">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4840.000000 -167.000000)" xlink:href="#lightningRod:shape123"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_39a9830">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4328.000000 -607.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_39de380">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3757.000000 -136.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3cbd6c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3934.000000 -136.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3ca6b40">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4125.000000 -136.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f30fc0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4301.000000 -136.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1eb14d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4477.000000 -136.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26fd230">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4653.000000 -136.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2699dc0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4781.000000 -183.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2773a90">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4394.000000 -573.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2716910">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4189.000000 -855.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1eaf5e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3646.000000 -426.000000)" xlink:href="#lightningRod:shape195"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1effe10">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3777.000000 -507.000000)" xlink:href="#lightningRod:shape74"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3c68570">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4023.000000 -119.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ba2ba0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4041.000000 -192.000000)" xlink:href="#lightningRod:shape192"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-37570" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4072.000000 -871.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37570" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5998"/>
     <cge:Term_Ref ObjectID="8698"/>
    <cge:TPSR_Ref TObjectID="5998"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-37571" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4072.000000 -871.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37571" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5998"/>
     <cge:Term_Ref ObjectID="8698"/>
    <cge:TPSR_Ref TObjectID="5998"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-37572" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4072.000000 -871.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37572" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5998"/>
     <cge:Term_Ref ObjectID="8698"/>
    <cge:TPSR_Ref TObjectID="5998"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-37573" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4072.000000 -871.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37573" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5998"/>
     <cge:Term_Ref ObjectID="8698"/>
    <cge:TPSR_Ref TObjectID="5998"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-37582" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4072.000000 -871.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37582" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5998"/>
     <cge:Term_Ref ObjectID="8698"/>
    <cge:TPSR_Ref TObjectID="5998"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-37593" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3814.000000 -465.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37593" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5999"/>
     <cge:Term_Ref ObjectID="8699"/>
    <cge:TPSR_Ref TObjectID="5999"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-37594" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3814.000000 -465.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37594" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5999"/>
     <cge:Term_Ref ObjectID="8699"/>
    <cge:TPSR_Ref TObjectID="5999"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-37595" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3814.000000 -465.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37595" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5999"/>
     <cge:Term_Ref ObjectID="8699"/>
    <cge:TPSR_Ref TObjectID="5999"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-37596" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3814.000000 -465.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37596" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5999"/>
     <cge:Term_Ref ObjectID="8699"/>
    <cge:TPSR_Ref TObjectID="5999"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-37605" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3814.000000 -465.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37605" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5999"/>
     <cge:Term_Ref ObjectID="8699"/>
    <cge:TPSR_Ref TObjectID="5999"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-37726" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4086.000000 -93.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37726" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6032"/>
     <cge:Term_Ref ObjectID="8728"/>
    <cge:TPSR_Ref TObjectID="6032"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-37727" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4086.000000 -93.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37727" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6032"/>
     <cge:Term_Ref ObjectID="8728"/>
    <cge:TPSR_Ref TObjectID="6032"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-37723" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4086.000000 -93.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37723" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6032"/>
     <cge:Term_Ref ObjectID="8728"/>
    <cge:TPSR_Ref TObjectID="6032"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-37601" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4123.000000 -486.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37601" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6008"/>
     <cge:Term_Ref ObjectID="8704"/>
    <cge:TPSR_Ref TObjectID="6008"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-37602" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4123.000000 -486.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37602" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6008"/>
     <cge:Term_Ref ObjectID="8704"/>
    <cge:TPSR_Ref TObjectID="6008"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-37597" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4123.000000 -486.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37597" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6008"/>
     <cge:Term_Ref ObjectID="8704"/>
    <cge:TPSR_Ref TObjectID="6008"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-37612" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4764.000000 -490.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37612" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6010"/>
     <cge:Term_Ref ObjectID="8712"/>
    <cge:TPSR_Ref TObjectID="6010"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-37613" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4764.000000 -490.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37613" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6010"/>
     <cge:Term_Ref ObjectID="8712"/>
    <cge:TPSR_Ref TObjectID="6010"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-37608" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4764.000000 -490.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37608" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6010"/>
     <cge:Term_Ref ObjectID="8712"/>
    <cge:TPSR_Ref TObjectID="6010"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-37637" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4616.000000 -93.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37637" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6021"/>
     <cge:Term_Ref ObjectID="8746"/>
    <cge:TPSR_Ref TObjectID="6021"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-37638" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4616.000000 -93.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37638" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6021"/>
     <cge:Term_Ref ObjectID="8746"/>
    <cge:TPSR_Ref TObjectID="6021"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-37634" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4616.000000 -93.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37634" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6021"/>
     <cge:Term_Ref ObjectID="8746"/>
    <cge:TPSR_Ref TObjectID="6021"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-37631" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4440.000000 -93.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37631" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6018"/>
     <cge:Term_Ref ObjectID="8740"/>
    <cge:TPSR_Ref TObjectID="6018"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-37632" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4440.000000 -93.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37632" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6018"/>
     <cge:Term_Ref ObjectID="8740"/>
    <cge:TPSR_Ref TObjectID="6018"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-37628" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4440.000000 -93.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37628" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6018"/>
     <cge:Term_Ref ObjectID="8740"/>
    <cge:TPSR_Ref TObjectID="6018"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-37625" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4264.000000 -93.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37625" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6015"/>
     <cge:Term_Ref ObjectID="8734"/>
    <cge:TPSR_Ref TObjectID="6015"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-37626" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4264.000000 -93.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37626" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6015"/>
     <cge:Term_Ref ObjectID="8734"/>
    <cge:TPSR_Ref TObjectID="6015"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-37622" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4264.000000 -93.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37622" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6015"/>
     <cge:Term_Ref ObjectID="8734"/>
    <cge:TPSR_Ref TObjectID="6015"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-37619" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3895.000000 -93.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37619" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6012"/>
     <cge:Term_Ref ObjectID="8722"/>
    <cge:TPSR_Ref TObjectID="6012"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-37620" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3895.000000 -93.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37620" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6012"/>
     <cge:Term_Ref ObjectID="8722"/>
    <cge:TPSR_Ref TObjectID="6012"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-37616" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3895.000000 -93.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37616" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6012"/>
     <cge:Term_Ref ObjectID="8722"/>
    <cge:TPSR_Ref TObjectID="6012"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-37720" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3748.000000 -93.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37720" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6029"/>
     <cge:Term_Ref ObjectID="8716"/>
    <cge:TPSR_Ref TObjectID="6029"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-37721" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3748.000000 -93.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37721" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6029"/>
     <cge:Term_Ref ObjectID="8716"/>
    <cge:TPSR_Ref TObjectID="6029"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-37717" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3748.000000 -93.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37717" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6029"/>
     <cge:Term_Ref ObjectID="8716"/>
    <cge:TPSR_Ref TObjectID="6029"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-100101" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4819.000000 -609.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="100101" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6026"/>
     <cge:Term_Ref ObjectID="8768"/>
    <cge:TPSR_Ref TObjectID="6026"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-37578" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4140.000000 -709.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37578" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6002"/>
     <cge:Term_Ref ObjectID="8700"/>
    <cge:TPSR_Ref TObjectID="6002"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-37579" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4140.000000 -709.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37579" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6002"/>
     <cge:Term_Ref ObjectID="8700"/>
    <cge:TPSR_Ref TObjectID="6002"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-37574" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4140.000000 -709.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37574" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6002"/>
     <cge:Term_Ref ObjectID="8700"/>
    <cge:TPSR_Ref TObjectID="6002"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-100077" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4140.000000 -709.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="100077" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6002"/>
     <cge:Term_Ref ObjectID="8700"/>
    <cge:TPSR_Ref TObjectID="6002"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-37589" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4768.000000 -711.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37589" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6004"/>
     <cge:Term_Ref ObjectID="8708"/>
    <cge:TPSR_Ref TObjectID="6004"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-37590" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4768.000000 -711.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37590" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6004"/>
     <cge:Term_Ref ObjectID="8708"/>
    <cge:TPSR_Ref TObjectID="6004"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-37585" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4768.000000 -711.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37585" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6004"/>
     <cge:Term_Ref ObjectID="8708"/>
    <cge:TPSR_Ref TObjectID="6004"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-100089" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4768.000000 -711.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="100089" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6004"/>
     <cge:Term_Ref ObjectID="8708"/>
    <cge:TPSR_Ref TObjectID="6004"/></metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="139" x="3244" y="-1177"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3196" y="-1194"/></g>
   <g href="35kV富民变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="52" x="3927" y="-579"/></g>
   <g href="35kV富民变2号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="53" x="4570" y="-603"/></g>
   <g href="35kV富民变10kV富Ⅱ回线091断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="3742" y="-296"/></g>
   <g href="35kV富民变10kV集镇线092断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="3919" y="-296"/></g>
   <g href="35kV富民变10kV富Ⅰ回线093断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4110" y="-296"/></g>
   <g href="35kV富民变10kV富军线094断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4286" y="-296"/></g>
   <g href="35kV富民变10kV富苍线095断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4462" y="-296"/></g>
   <g href="35kV富民变10kV备用线096断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4638" y="-296"/></g>
   <g href="cx_配调_配网接线图35_楚雄.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3482" y="-1130"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3482" y="-1165"/></g>
   <g href="35kV富民变GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="80" x="3165" y="-757"/></g>
   <g href="AVC富民站.svg" style="fill-opacity:0"><rect height="44" qtmmishow="hidden" width="73" x="3611" y="-1151"/></g>
   <g href="35kV富民变隔刀开关远方遥控清单.svg" style="fill-opacity:0"><rect height="24" qtmmishow="hidden" width="96" x="3154" y="-693"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cbcfc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4064.000000 486.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28aac40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4078.000000 456.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c76870" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4053.000000 471.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34903e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4704.000000 491.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d00270" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4718.000000 461.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d004b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4693.000000 476.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39a6ad0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3690.000000 94.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c6af30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3704.000000 64.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c6b170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3679.000000 79.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_273f650" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3994.000000 825.000000) translate(0,12)">U0（V）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2714630" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3987.000000 841.000000) translate(0,12)">Uc（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2714280" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3987.000000 854.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27115d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3979.000000 810.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1efc4e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3987.000000 872.000000) translate(0,12)">Ua（kV）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1efc740" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3736.000000 417.000000) translate(0,12)">U0（V）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39e07d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3729.000000 433.000000) translate(0,12)">Uc（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39e09e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3729.000000 446.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2714ed0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3721.000000 402.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2715100" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3729.000000 464.000000) translate(0,12)">Ua（kV）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f02bf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4723.000000 594.000000) translate(0,12)">温度（℃）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39e3aa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4723.000000 609.000000) translate(0,12)">档位（档）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26fe7b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4070.000000 697.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bb0240" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4095.000000 682.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bb0480" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4100.000000 666.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cbc260" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4081.000000 712.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26c6d60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4700.000000 696.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26c6fd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4725.000000 681.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c5fac0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4730.000000 665.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c5fc90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4711.000000 711.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_283a900" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3840.000000 95.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c65560" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3854.000000 65.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25ce880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3829.000000 80.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25cebb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4210.000000 94.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28c59d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4224.000000 64.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28c5c10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4199.000000 79.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26c72c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4388.000000 94.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26c7560" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4402.000000 64.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cb2bf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4377.000000 79.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cb2e30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4562.000000 94.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c61350" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4576.000000 64.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c61590" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4551.000000 79.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4232.000000 -858.000000)" xlink:href="#transformer2:shape11_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4232.000000 -858.000000)" xlink:href="#transformer2:shape11_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_FM.CX_FM_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="8766"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3988.000000 -524.000000)" xlink:href="#transformer2:shape0_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3988.000000 -524.000000)" xlink:href="#transformer2:shape0_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="6025" ObjectName="TF-CX_FM.CX_FM_1T"/>
    <cge:TPSR_Ref TObjectID="6025"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_FM.CX_FM_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="28386"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4615.000000 -531.000000)" xlink:href="#transformer2:shape21_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4615.000000 -531.000000)" xlink:href="#transformer2:shape21_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="6026" ObjectName="TF-CX_FM.CX_FM_2T"/>
    <cge:TPSR_Ref TObjectID="6026"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4013.000000 -17.000000)" xlink:href="#transformer2:shape25_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4013.000000 -17.000000)" xlink:href="#transformer2:shape25_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="139" x="3244" y="-1177"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="139" x="3244" y="-1177"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3196" y="-1194"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3196" y="-1194"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="52" x="3927" y="-579"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="52" x="3927" y="-579"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="53" x="4570" y="-603"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="53" x="4570" y="-603"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="3742" y="-296"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="3742" y="-296"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="3919" y="-296"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="3919" y="-296"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4110" y="-296"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4110" y="-296"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4286" y="-296"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4286" y="-296"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4462" y="-296"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4462" y="-296"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4638" y="-296"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4638" y="-296"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3482" y="-1130"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3482" y="-1130"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3482" y="-1165"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3482" y="-1165"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="80" x="3165" y="-757"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="80" x="3165" y="-757"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="44" qtmmishow="hidden" width="73" x="3611" y="-1151"/>
    </a>
   <metadata/><rect fill="white" height="44" opacity="0" stroke="white" transform="" width="73" x="3611" y="-1151"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="24" qtmmishow="hidden" width="96" x="3154" y="-693"/>
    </a>
   <metadata/><rect fill="white" height="24" opacity="0" stroke="white" transform="" width="96" x="3154" y="-693"/></g>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-37651">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3998.000000 -369.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6009" ObjectName="SW-CX_FM.CX_FM_0011SW"/>
     <cge:Meas_Ref ObjectId="37651"/>
    <cge:TPSR_Ref TObjectID="6009"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37649">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4403.000000 -727.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6007" ObjectName="SW-CX_FM.CX_FM_39010SW"/>
     <cge:Meas_Ref ObjectId="37649"/>
    <cge:TPSR_Ref TObjectID="6007"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37643">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4329.000000 -889.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6001" ObjectName="SW-CX_FM.CX_FM_39117SW"/>
     <cge:Meas_Ref ObjectId="37643"/>
    <cge:TPSR_Ref TObjectID="6001"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37642">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4283.000000 -806.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6000" ObjectName="SW-CX_FM.CX_FM_3911SW"/>
     <cge:Meas_Ref ObjectId="37642"/>
    <cge:TPSR_Ref TObjectID="6000"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37645">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 3998.000000 -706.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6003" ObjectName="SW-CX_FM.CX_FM_3011SW"/>
     <cge:Meas_Ref ObjectId="37645"/>
    <cge:TPSR_Ref TObjectID="6003"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37730">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3718.000000 -295.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6030" ObjectName="SW-CX_FM.CX_FM_0911SW"/>
     <cge:Meas_Ref ObjectId="37730"/>
    <cge:TPSR_Ref TObjectID="6030"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37731">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3718.000000 -202.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6031" ObjectName="SW-CX_FM.CX_FM_0916SW"/>
     <cge:Meas_Ref ObjectId="37731"/>
    <cge:TPSR_Ref TObjectID="6031"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37653">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4638.000000 -376.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6011" ObjectName="SW-CX_FM.CX_FM_0021SW"/>
     <cge:Meas_Ref ObjectId="37653"/>
    <cge:TPSR_Ref TObjectID="6011"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37647">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4638.000000 -713.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6005" ObjectName="SW-CX_FM.CX_FM_3021SW"/>
     <cge:Meas_Ref ObjectId="37647"/>
    <cge:TPSR_Ref TObjectID="6005"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37655">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3895.000000 -295.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6013" ObjectName="SW-CX_FM.CX_FM_0921SW"/>
     <cge:Meas_Ref ObjectId="37655"/>
    <cge:TPSR_Ref TObjectID="6013"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37656">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3895.000000 -202.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6014" ObjectName="SW-CX_FM.CX_FM_0926SW"/>
     <cge:Meas_Ref ObjectId="37656"/>
    <cge:TPSR_Ref TObjectID="6014"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37733">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4090.000000 -295.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6033" ObjectName="SW-CX_FM.CX_FM_0931SW"/>
     <cge:Meas_Ref ObjectId="37733"/>
    <cge:TPSR_Ref TObjectID="6033"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37734">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4090.000000 -202.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6034" ObjectName="SW-CX_FM.CX_FM_0936SW"/>
     <cge:Meas_Ref ObjectId="37734"/>
    <cge:TPSR_Ref TObjectID="6034"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37658">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4263.000000 -297.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6016" ObjectName="SW-CX_FM.CX_FM_0941SW"/>
     <cge:Meas_Ref ObjectId="37658"/>
    <cge:TPSR_Ref TObjectID="6016"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37659">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4263.000000 -202.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6017" ObjectName="SW-CX_FM.CX_FM_0946SW"/>
     <cge:Meas_Ref ObjectId="37659"/>
    <cge:TPSR_Ref TObjectID="6017"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37661">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4438.000000 -295.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6019" ObjectName="SW-CX_FM.CX_FM_0951SW"/>
     <cge:Meas_Ref ObjectId="37661"/>
    <cge:TPSR_Ref TObjectID="6019"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37662">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4438.000000 -202.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6020" ObjectName="SW-CX_FM.CX_FM_0956SW"/>
     <cge:Meas_Ref ObjectId="37662"/>
    <cge:TPSR_Ref TObjectID="6020"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37664">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4614.000000 -295.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6022" ObjectName="SW-CX_FM.CX_FM_0961SW"/>
     <cge:Meas_Ref ObjectId="37664"/>
    <cge:TPSR_Ref TObjectID="6022"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37665">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4614.000000 -202.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6023" ObjectName="SW-CX_FM.CX_FM_0966SW"/>
     <cge:Meas_Ref ObjectId="37665"/>
    <cge:TPSR_Ref TObjectID="6023"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37666">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4806.000000 -295.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6024" ObjectName="SW-CX_FM.CX_FM_0901SW"/>
     <cge:Meas_Ref ObjectId="37666"/>
    <cge:TPSR_Ref TObjectID="6024"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37648">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4357.000000 -672.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6006" ObjectName="SW-CX_FM.CX_FM_3901SW"/>
     <cge:Meas_Ref ObjectId="37648"/>
    <cge:TPSR_Ref TObjectID="6006"/></metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-100088" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4149.000000 -587.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="100088" ObjectName="CX_FM:CX_FM_1T_Tmp"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-100100" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4819.000000 -594.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="100100" ObjectName="CX_FM:CX_FM_2T_Tmp"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3232.500000 -1118.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-200689" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3256.000000 -982.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="200689" ObjectName="CX_FM:CX_FM_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-200690" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3255.000000 -941.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="200690" ObjectName="CX_FM:CX_FM_sumQ"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_FM"/>
</svg>