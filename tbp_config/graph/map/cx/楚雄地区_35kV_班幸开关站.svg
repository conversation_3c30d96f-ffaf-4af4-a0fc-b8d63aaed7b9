<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-179" aopId="786686" id="thSvg" product="E8000V2" version="1.0" viewBox="-530 -948 2039 1096">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape8_0">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="91" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="82" y2="91"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="91" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="15" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="breaker2:shape8_1">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="7" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="99" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
   </symbol>
   <symbol id="breaker2:shape8-UnNor1">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="15" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="91" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="82" y2="91"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="91" y2="82"/>
   </symbol>
   <symbol id="breaker2:shape8-UnNor2">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="7" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="98" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
   </symbol>
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="currentTransformer:shape2">
    <circle cx="21" cy="11" fillStyle="0" r="6.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="30" y1="23" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="17" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="8" x2="8" y1="41" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="5" x2="5" y1="32" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="1" x2="1" y1="33" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="24" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.27102" x1="24" x2="24" y1="29" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.260875" x1="17" x2="17" y1="29" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="53" x2="43" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.27102" x1="43" x2="43" y1="29" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.284591" x1="36" x2="36" y1="29" y2="42"/>
    <circle cx="30" cy="7" fillStyle="0" r="6.5" stroke-width="1"/>
    <circle cx="30" cy="16" fillStyle="0" r="6.5" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape10">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="0" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.267029" x1="17" x2="9" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.349915" x1="20" x2="5" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="13" x2="13" y1="14" y2="5"/>
   </symbol>
   <symbol id="generator:shape4">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="8" x2="8" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape157">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,36 0,46 10,46 5,36 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,24 0,14 10,14 5,24 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="59" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape146">
    <rect height="19" stroke-width="1" width="35" x="0" y="0"/>
    <polyline points="17,19 17,30 " stroke-width="1"/>
    <text font-family="SimSun" font-size="15" graphid="g_3cf8ea0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 17.000000) translate(0,12)">SVG</text>
   </symbol>
   <symbol id="lightningRod:shape76">
    <rect height="14" stroke-width="1" width="27" x="18" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="5" x2="5" y1="11" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="9" x2="18" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="8" x2="8" y1="2" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="58" x2="23" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape37">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,63 0,73 10,73 5,63 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,28 0,18 10,18 5,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="86" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="63" x2="63" y1="6" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="61" x2="61" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="58" x2="58" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="58" x2="49" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape40">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.1301" x1="6" x2="6" y1="17" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.1301" x1="0" x2="13" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.1301" x1="4" x2="9" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.06525" x1="5" x2="8" y1="2" y2="2"/>
    <polyline arcFlag="1" points="7,29 7,29 7,29 7,29 8,29 8,30 8,30 8,30 9,30 9,31 9,31 9,31 9,32 9,32 9,32 9,33 9,33 9,34 9,34 8,34 8,34 8,35 8,35 7,35 7,35 7,35 " stroke-width="0.323101"/>
    <polyline arcFlag="1" points="6,17 7,17 7,17 7,17 7,17 8,17 8,17 8,18 8,18 8,18 8,19 9,19 9,19 9,20 9,20 9,20 8,21 8,21 8,21 8,22 8,22 8,22 7,22 7,23 7,23 7,23 " stroke-width="0.323101"/>
    <polyline arcFlag="1" points="7,23 7,23 7,23 7,23 8,23 8,23 8,24 8,24 9,24 9,24 9,25 9,25 9,25 9,26 9,26 9,27 9,27 9,27 9,28 8,28 8,28 8,28 8,29 7,29 7,29 7,29 " stroke-width="0.323101"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.35042" x1="7" x2="7" y1="46" y2="35"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="transformer2:shape63_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="59" x2="61" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="57" x2="63" y1="25" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="55" x2="65" y1="27" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="60" y1="23" y2="43"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="28" x2="28" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="26" y1="81" y2="81"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="26,56 60,56 60,27 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="26" y1="42" y2="0"/>
    <circle cx="26" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="21" y1="56" y2="61"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="26" y1="56" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="31" y1="56" y2="61"/>
    <polyline DF8003:Layer="PUBLIC" points="26,10 20,23 33,23 26,10 26,11 26,10 "/>
   </symbol>
   <symbol id="transformer2:shape63_1">
    <circle cx="26" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="26" y1="81" y2="81"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="24" y1="85" y2="87"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="30,85 32,83 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="26,76 23,76 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="26" y1="76" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="22" y1="81" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="30" y1="81" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="26" y1="81" y2="76"/>
   </symbol>
   <symbol id="transformer2:shape3_0">
    <ellipse cx="13" cy="34" fillStyle="0" rx="13" ry="12.5" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="40" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="36" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="32" y2="36"/>
   </symbol>
   <symbol id="transformer2:shape3_1">
    <circle cx="13" cy="18" fillStyle="0" r="13" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="20" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="16" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="12" y2="16"/>
   </symbol>
   <symbol id="transformer2:shape65_0">
    <circle cx="15" cy="115" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="78" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="16" y1="79" y2="64"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="42" y1="93" y2="79"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="43" y1="79" y2="79"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="42" y1="93" y2="93"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="17" y1="51" y2="51"/>
    <polyline DF8003:Layer="PUBLIC" points="16,46 10,59 22,59 16,46 16,47 16,46 "/>
    <polyline DF8003:Layer="PUBLIC" points="16,33 10,20 22,20 16,33 16,32 16,33 "/>
    <circle cx="15" cy="93" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="11" y1="93" y2="98"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="93" y2="88"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="21" y1="93" y2="98"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.306122" x1="11" x2="22" y1="113" y2="113"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.306122" x1="16" x2="22" y1="123" y2="113"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.306122" x1="16" x2="11" y1="123" y2="113"/>
   </symbol>
   <symbol id="transformer2:shape65_1"/>
   <symbol id="transformer2:shape64_0">
    <circle cx="36" cy="109" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="82" x2="82" y1="85" y2="87"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="80" x2="80" y1="83" y2="89"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="78" x2="78" y1="81" y2="91"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="78" y1="86" y2="86"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="37" y1="53" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="35" y1="111" y2="111"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="34" y1="115" y2="117"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="39,115 41,113 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="36,106 33,106 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="35" y1="106" y2="106"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="36,61 65,76 65,86 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="36" y1="72" y2="4"/>
    <circle cx="36" cy="87" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="32" y1="86" y2="91"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="36" y1="86" y2="81"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="32" y1="111" y2="115"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="39" y1="111" y2="115"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="36" y1="111" y2="106"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="40" y1="86" y2="91"/>
    <polyline DF8003:Layer="PUBLIC" points="36,48 30,61 42,61 36,48 36,49 36,48 "/>
    <polyline DF8003:Layer="PUBLIC" points="36,35 30,22 42,22 36,35 36,34 36,35 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="36" y1="124" y2="204"/>
    <polyline DF8003:Layer="PUBLIC" points="36,147 30,160 42,160 36,147 36,148 36,147 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="5" y1="111" y2="111"/>
   </symbol>
   <symbol id="transformer2:shape64_1"/>
   <symbol id="voltageTransformer:shape97">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="37" y1="57" y2="122"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="1" y1="29" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="12" y1="29" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="0" x2="12" y1="9" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="10" x2="2" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="8" x2="4" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="6" y1="51" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="51" y2="8"/>
    <circle cx="49" cy="42" fillStyle="0" r="8" stroke-width="0.570276"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="22" y1="45" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="28" x2="22" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="28" x2="26" y1="41" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="49" x2="52" y1="42" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="46" x2="49" y1="45" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="49" x2="49" y1="42" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="37" x2="40" y1="34" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="34" x2="37" y1="37" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="37" x2="37" y1="34" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="34" x2="37" y1="52" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="37" x2="40" y1="49" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="37" x2="37" y1="49" y2="46"/>
    <circle cx="25" cy="42" fillStyle="0" r="7.5" stroke-width="0.536731"/>
    <circle cx="37" cy="35" fillStyle="0" r="8" stroke-width="0.570276"/>
    <circle cx="37" cy="49" fillStyle="0" r="8" stroke-width="0.570276"/>
    <rect height="13" stroke-width="1" width="7" x="3" y="20"/>
    <rect height="27" stroke-width="0.416667" width="14" x="30" y="76"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_23be810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23eb170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_23ebb10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_236fe60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2370eb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2371990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23723b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2439880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_236e7b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_236e7b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_243ca20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_243ca20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_24360c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_24360c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_24370e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2438d50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_250ff40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2510cf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2511440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_24171b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_25132e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2513ba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_24125d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2413390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2413d10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2414800" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_24151c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_24783b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2415c00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2440690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_24412b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2689790" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2442080" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_23904c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2391aa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1106" width="2049" x="-535" y="-953"/>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c93620" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -151.000000 486.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_42a8610" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -151.000000 501.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3624ff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -159.000000 436.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36251d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -143.000000 421.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36253b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -152.000000 452.000000) translate(0,12)">3U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40e5bd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -151.000000 471.000000) translate(0,12)">Uc(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40e5d70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 814.000000 574.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40e5f70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 814.000000 544.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ab0b60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 814.000000 559.000000) translate(0,12)">Ub(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e72490" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 383.000000 513.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2605050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 406.000000 498.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2605220" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 394.000000 528.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fe63a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -192.000000 -113.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fe6570" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -169.000000 -128.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22ee060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -181.000000 -98.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 967.000000 -492.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1238.000000 -495.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-125725">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 311.000000 -478.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22997" ObjectName="SW-CX_BX.CX_BX_341BK"/>
     <cge:Meas_Ref ObjectId="125725"/>
    <cge:TPSR_Ref TObjectID="22997"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-125751">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -90.000000 -283.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23014" ObjectName="SW-CX_BX.CX_BX_351BK"/>
     <cge:Meas_Ref ObjectId="125751"/>
    <cge:TPSR_Ref TObjectID="23014"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-125745">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 87.000000 -283.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23009" ObjectName="SW-CX_BX.CX_BX_352BK"/>
     <cge:Meas_Ref ObjectId="125745"/>
    <cge:TPSR_Ref TObjectID="23009"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-125730">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 423.000000 -274.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23000" ObjectName="SW-CX_BX.CX_BX_353BK"/>
     <cge:Meas_Ref ObjectId="125730"/>
    <cge:TPSR_Ref TObjectID="23000"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-125735">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 599.000000 -270.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23003" ObjectName="SW-CX_BX.CX_BX_354BK"/>
     <cge:Meas_Ref ObjectId="125735"/>
    <cge:TPSR_Ref TObjectID="23003"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-125740">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 775.000000 -274.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23006" ObjectName="SW-CX_BX.CX_BX_355BK"/>
     <cge:Meas_Ref ObjectId="125740"/>
    <cge:TPSR_Ref TObjectID="23006"/></metadata>
   </g>
  </g><g id="CurrentTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_3f44360">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 242.000000 -667.000000)" xlink:href="#currentTransformer:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Generator_Layer">
   <g DF8003:Layer="PUBLIC" id="SM-CX_BX.P1">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 427.000000 -37.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43328" ObjectName="SM-CX_BX.P1"/>
    <cge:TPSR_Ref TObjectID="43328"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_BX.P2">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 603.000000 -37.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43329" ObjectName="SM-CX_BX.P2"/>
    <cge:TPSR_Ref TObjectID="43329"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_BX.P3">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 779.000000 -39.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43330" ObjectName="SM-CX_BX.P3"/>
    <cge:TPSR_Ref TObjectID="43330"/></metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_FS" endPointId="0" endStationName="CX_BX" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_Fangban" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="320,-910 320,-946 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="31882" ObjectName="AC-35kV.LN_Fangban"/>
    <cge:TPSR_Ref TObjectID="31882_SS-179"/></metadata>
   <polyline fill="none" opacity="0" points="320,-910 320,-946 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_3ef1f20" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -161.695431 -199.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_41c26a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.055838 -198.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_435b0c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.055838 -57.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f7f910" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 349.000000 -154.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_43acb20" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 525.000000 -154.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_356c550" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 701.000000 -155.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a7f7f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 388.000000 -633.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2352060" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1430.000000 -468.000000)" xlink:href="#earth:shape10"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_29d3870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-80,-230 -44,-230 -44,-218 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="23016@x" ObjectIDND1="g_3455000@0" ObjectIDND2="23026@x" ObjectIDZND0="g_3eef090@0" Pin0InfoVect0LinkObjId="g_3eef090_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-125753_0" Pin1InfoVect1LinkObjId="g_3455000_0" Pin1InfoVect2LinkObjId="SW-125752_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-80,-230 -44,-230 -44,-218 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25e7240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-80,-232 -80,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_3eef090@0" ObjectIDND1="23026@x" ObjectIDZND0="23016@x" ObjectIDZND1="g_3455000@0" Pin0InfoVect0LinkObjId="SW-125753_0" Pin0InfoVect1LinkObjId="g_3455000_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3eef090_0" Pin1InfoVect1LinkObjId="SW-125752_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-80,-232 -80,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3437240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-80,-205 -94,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_3eef090@0" ObjectIDND1="23026@x" ObjectIDND2="g_3455000@0" ObjectIDZND0="23016@1" Pin0InfoVect0LinkObjId="SW-125753_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3eef090_0" Pin1InfoVect1LinkObjId="SW-125752_0" Pin1InfoVect2LinkObjId="g_3455000_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-80,-205 -94,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_23060d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-130,-205 -144,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23016@0" ObjectIDZND0="g_3ef1f20@0" Pin0InfoVect0LinkObjId="g_3ef1f20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-125753_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-130,-205 -144,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_356c2f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-80,-205 -80,-183 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_3eef090@0" ObjectIDND1="23026@x" ObjectIDND2="23016@x" ObjectIDZND0="g_3455000@1" Pin0InfoVect0LinkObjId="g_3455000_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3eef090_0" Pin1InfoVect1LinkObjId="SW-125752_0" Pin1InfoVect2LinkObjId="SW-125753_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-80,-205 -80,-183 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3286ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-80,-130 -80,-103 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_3455000@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3455000_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-80,-130 -80,-103 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3dff120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-151,-53 -151,-42 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="23017@0" ObjectIDZND0="g_3ef2ee0@0" Pin0InfoVect0LinkObjId="g_3ef2ee0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-125754_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-151,-53 -151,-42 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3372db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-122,-90 -122,-79 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="23017@x" ObjectIDZND0="g_3ef3590@0" Pin0InfoVect0LinkObjId="g_3ef3590_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-125754_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-122,-90 -122,-79 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2381b90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-101,-90 -122,-90 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="23017@x" ObjectIDZND1="g_3ef3590@0" Pin0InfoVect0LinkObjId="SW-125754_0" Pin0InfoVect1LinkObjId="g_3ef3590_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-101,-90 -122,-90 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34267a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-122,-90 -151,-90 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_3ef3590@0" ObjectIDZND0="23017@1" Pin0InfoVect0LinkObjId="SW-125754_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_3ef3590_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-122,-90 -151,-90 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2aafcd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="96,-230 136,-230 136,-220 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="23011@x" ObjectIDND1="g_40e59e0@0" ObjectIDND2="23025@x" ObjectIDZND0="g_3286ad0@0" Pin0InfoVect0LinkObjId="g_3286ad0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-125747_0" Pin1InfoVect1LinkObjId="g_40e59e0_0" Pin1InfoVect2LinkObjId="SW-125746_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="96,-230 136,-230 136,-220 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4463740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="96,-230 96,-204 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_3286ad0@0" ObjectIDND1="23025@x" ObjectIDZND0="23011@x" ObjectIDZND1="g_40e59e0@0" Pin0InfoVect0LinkObjId="SW-125747_0" Pin0InfoVect1LinkObjId="g_40e59e0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3286ad0_0" Pin1InfoVect1LinkObjId="SW-125746_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="96,-230 96,-204 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33d1230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="96,-204 84,-204 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_3286ad0@0" ObjectIDND1="23025@x" ObjectIDND2="g_40e59e0@0" ObjectIDZND0="23011@1" Pin0InfoVect0LinkObjId="SW-125747_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3286ad0_0" Pin1InfoVect1LinkObjId="SW-125746_0" Pin1InfoVect2LinkObjId="g_40e59e0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="96,-204 84,-204 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32595a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="48,-204 34,-204 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23011@0" ObjectIDZND0="g_41c26a0@0" Pin0InfoVect0LinkObjId="g_41c26a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-125747_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="48,-204 34,-204 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33c8890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="96,-204 96,-183 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_3286ad0@0" ObjectIDND1="23025@x" ObjectIDND2="23011@x" ObjectIDZND0="g_40e59e0@1" Pin0InfoVect0LinkObjId="g_40e59e0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3286ad0_0" Pin1InfoVect1LinkObjId="SW-125746_0" Pin1InfoVect2LinkObjId="SW-125747_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="96,-204 96,-183 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34fc770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="96,-130 96,-115 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_40e59e0@0" ObjectIDZND0="23012@1" Pin0InfoVect0LinkObjId="SW-125748_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_40e59e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="96,-130 96,-115 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36040b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="48,-63 34,-63 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23013@0" ObjectIDZND0="g_435b0c0@0" Pin0InfoVect0LinkObjId="g_435b0c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-125749_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="48,-63 34,-63 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3df0a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="96,-63 84,-63 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer2" EndDevType0="switch" ObjectIDND0="23012@x" ObjectIDND1="0@x" ObjectIDZND0="23013@1" Pin0InfoVect0LinkObjId="SW-125749_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-125748_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="96,-63 84,-63 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ceb8f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="96,-79 96,-63 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="transformer2" ObjectIDND0="23012@0" ObjectIDZND0="23013@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-125749_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-125748_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="96,-79 96,-63 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35fa650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="96,-63 96,-45 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="transformer2" ObjectIDND0="23013@x" ObjectIDND1="23012@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-125749_0" Pin1InfoVect1LinkObjId="SW-125748_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="96,-63 96,-45 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3d8aa30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="96,-3 96,16 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_46b4f70@0" Pin0InfoVect0LinkObjId="g_46b4f70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="96,-3 96,16 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_40948f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="256,-368 256,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="22996@0" ObjectIDZND0="23018@0" Pin0InfoVect0LinkObjId="SW-125756_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="256,-368 256,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a7c7c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="256,-304 291,-304 291,-284 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="23018@x" ObjectIDND1="g_34e6440@0" ObjectIDZND0="g_338e340@0" Pin0InfoVect0LinkObjId="g_338e340_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-125756_0" Pin1InfoVect1LinkObjId="g_34e6440_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="256,-304 291,-304 291,-284 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_264ef00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="256,-331 256,-304 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" ObjectIDND0="23018@1" ObjectIDZND0="g_338e340@0" ObjectIDZND1="g_34e6440@0" Pin0InfoVect0LinkObjId="g_338e340_0" Pin0InfoVect1LinkObjId="g_34e6440_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-125756_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="256,-331 256,-304 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f20560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="256,-304 256,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="23018@x" ObjectIDND1="g_338e340@0" ObjectIDZND0="g_34e6440@0" Pin0InfoVect0LinkObjId="g_34e6440_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-125756_0" Pin1InfoVect1LinkObjId="g_338e340_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="256,-304 256,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e14f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="432,-223 469,-223 469,-204 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="23002@x" ObjectIDND1="g_3fd8260@0" ObjectIDND2="23022@x" ObjectIDZND0="g_29c18e0@0" Pin0InfoVect0LinkObjId="g_29c18e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-125732_0" Pin1InfoVect1LinkObjId="g_3fd8260_0" Pin1InfoVect2LinkObjId="SW-125731_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="432,-223 469,-223 469,-204 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2381710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="432,-223 432,-159 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_29c18e0@0" ObjectIDND1="23022@x" ObjectIDZND0="23002@x" ObjectIDZND1="g_3fd8260@0" Pin0InfoVect0LinkObjId="SW-125732_0" Pin0InfoVect1LinkObjId="g_3fd8260_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_29c18e0_0" Pin1InfoVect1LinkObjId="SW-125731_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="432,-223 432,-159 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29c21f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="432,-160 416,-160 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_29c18e0@0" ObjectIDND1="23022@x" ObjectIDND2="g_3fd8260@0" ObjectIDZND0="23002@1" Pin0InfoVect0LinkObjId="SW-125732_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_29c18e0_0" Pin1InfoVect1LinkObjId="SW-125731_0" Pin1InfoVect2LinkObjId="g_3fd8260_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="432,-160 416,-160 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a45b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="380,-160 367,-160 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23002@0" ObjectIDZND0="g_3f7f910@0" Pin0InfoVect0LinkObjId="g_3f7f910_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-125732_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="380,-160 367,-160 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ca04d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="432,-159 432,-135 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_29c18e0@0" ObjectIDND1="23022@x" ObjectIDND2="23002@x" ObjectIDZND0="g_3fd8260@1" Pin0InfoVect0LinkObjId="g_3fd8260_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_29c18e0_0" Pin1InfoVect1LinkObjId="SW-125731_0" Pin1InfoVect2LinkObjId="SW-125732_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="432,-159 432,-135 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3392a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="432,-82 432,-58 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="generator" ObjectIDND0="g_3fd8260@0" ObjectIDZND0="43328@0" Pin0InfoVect0LinkObjId="SM-CX_BX.P1_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3fd8260_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="432,-82 432,-58 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a88ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="608,-223 645,-223 645,-204 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="23005@x" ObjectIDND1="g_3ca58b0@0" ObjectIDND2="23023@x" ObjectIDZND0="g_235b9a0@0" Pin0InfoVect0LinkObjId="g_235b9a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-125737_0" Pin1InfoVect1LinkObjId="g_3ca58b0_0" Pin1InfoVect2LinkObjId="SW-125736_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="608,-223 645,-223 645,-204 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3676430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="608,-223 608,-159 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_235b9a0@0" ObjectIDND1="23023@x" ObjectIDZND0="23005@x" ObjectIDZND1="g_3ca58b0@0" Pin0InfoVect0LinkObjId="SW-125737_0" Pin0InfoVect1LinkObjId="g_3ca58b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_235b9a0_0" Pin1InfoVect1LinkObjId="SW-125736_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="608,-223 608,-159 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ef3850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="608,-160 592,-160 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_235b9a0@0" ObjectIDND1="23023@x" ObjectIDND2="g_3ca58b0@0" ObjectIDZND0="23005@1" Pin0InfoVect0LinkObjId="SW-125737_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_235b9a0_0" Pin1InfoVect1LinkObjId="SW-125736_0" Pin1InfoVect2LinkObjId="g_3ca58b0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="608,-160 592,-160 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ca22c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="556,-160 543,-160 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23005@0" ObjectIDZND0="g_43acb20@0" Pin0InfoVect0LinkObjId="g_43acb20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-125737_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="556,-160 543,-160 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_42863d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="608,-159 608,-134 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="23005@x" ObjectIDND1="g_235b9a0@0" ObjectIDND2="23023@x" ObjectIDZND0="g_3ca58b0@1" Pin0InfoVect0LinkObjId="g_3ca58b0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-125737_0" Pin1InfoVect1LinkObjId="g_235b9a0_0" Pin1InfoVect2LinkObjId="SW-125736_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="608,-159 608,-134 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_238cd40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="608,-81 608,-58 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="generator" ObjectIDND0="g_3ca58b0@0" ObjectIDZND0="43329@0" Pin0InfoVect0LinkObjId="SM-CX_BX.P2_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3ca58b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="608,-81 608,-58 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3fd6320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="784,-224 821,-224 821,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="23008@x" ObjectIDND1="g_42539c0@0" ObjectIDND2="23024@x" ObjectIDZND0="g_3d477e0@0" Pin0InfoVect0LinkObjId="g_3d477e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-125742_0" Pin1InfoVect1LinkObjId="g_42539c0_0" Pin1InfoVect2LinkObjId="SW-125741_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="784,-224 821,-224 821,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3face60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="784,-224 784,-160 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_3d477e0@0" ObjectIDND1="23024@x" ObjectIDZND0="23008@x" ObjectIDZND1="g_42539c0@0" Pin0InfoVect0LinkObjId="SW-125742_0" Pin0InfoVect1LinkObjId="g_42539c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3d477e0_0" Pin1InfoVect1LinkObjId="SW-125741_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="784,-224 784,-160 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a806d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="784,-161 768,-161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_3d477e0@0" ObjectIDND1="23024@x" ObjectIDND2="g_42539c0@0" ObjectIDZND0="23008@1" Pin0InfoVect0LinkObjId="SW-125742_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3d477e0_0" Pin1InfoVect1LinkObjId="SW-125741_0" Pin1InfoVect2LinkObjId="g_42539c0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="784,-161 768,-161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d10110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="732,-161 719,-161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23008@0" ObjectIDZND0="g_356c550@0" Pin0InfoVect0LinkObjId="g_356c550_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-125742_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="732,-161 719,-161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e1fe90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="784,-160 784,-136 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="23008@x" ObjectIDND1="g_3d477e0@0" ObjectIDND2="23024@x" ObjectIDZND0="g_42539c0@1" Pin0InfoVect0LinkObjId="g_42539c0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-125742_0" Pin1InfoVect1LinkObjId="g_3d477e0_0" Pin1InfoVect2LinkObjId="SW-125741_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="784,-160 784,-136 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_43a8eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="784,-83 784,-60 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="generator" ObjectIDND0="g_42539c0@0" ObjectIDZND0="43330@0" Pin0InfoVect0LinkObjId="SM-CX_BX.P3_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_42539c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="784,-83 784,-60 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3df1140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="320,-590 286,-590 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="currentTransformer" EndDevType0="lightningRod" ObjectIDND0="22999@x" ObjectIDND1="g_2b2afa0@0" ObjectIDND2="g_3f44360@0" ObjectIDZND0="g_418e5e0@0" Pin0InfoVect0LinkObjId="g_418e5e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-125727_0" Pin1InfoVect1LinkObjId="g_2b2afa0_0" Pin1InfoVect2LinkObjId="g_3f44360_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="320,-590 286,-590 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34247c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="320,-590 320,-639 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="currentTransformer" ObjectIDND0="g_418e5e0@0" ObjectIDND1="22998@x" ObjectIDZND0="22999@x" ObjectIDZND1="g_2b2afa0@0" ObjectIDZND2="g_3f44360@0" Pin0InfoVect0LinkObjId="SW-125727_0" Pin0InfoVect1LinkObjId="g_2b2afa0_0" Pin0InfoVect2LinkObjId="g_3f44360_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_418e5e0_0" Pin1InfoVect1LinkObjId="SW-125726_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="320,-590 320,-639 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3eb7600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="320,-639 340,-639 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_418e5e0@0" ObjectIDND1="22998@x" ObjectIDND2="g_2b2afa0@0" ObjectIDZND0="22999@0" Pin0InfoVect0LinkObjId="SW-125727_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_418e5e0_0" Pin1InfoVect1LinkObjId="SW-125726_0" Pin1InfoVect2LinkObjId="g_2b2afa0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="320,-639 340,-639 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_235b3c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="376,-639 392,-639 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="22999@1" ObjectIDZND0="g_2a7f7f0@0" Pin0InfoVect0LinkObjId="g_2a7f7f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-125727_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="376,-639 392,-639 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2351700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="320,-702 296,-702 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="currentTransformer" ObjectIDND0="g_418e5e0@0" ObjectIDND1="22998@x" ObjectIDND2="22999@x" ObjectIDZND0="g_3f44360@0" Pin0InfoVect0LinkObjId="g_3f44360_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_418e5e0_0" Pin1InfoVect1LinkObjId="SW-125726_0" Pin1InfoVect2LinkObjId="SW-125727_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="320,-702 296,-702 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25fe9c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="320,-639 320,-702 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="currentTransformer" ObjectIDND0="g_418e5e0@0" ObjectIDND1="22998@x" ObjectIDND2="22999@x" ObjectIDZND0="g_2b2afa0@0" ObjectIDZND1="g_3f44360@0" Pin0InfoVect0LinkObjId="g_2b2afa0_0" Pin0InfoVect1LinkObjId="g_3f44360_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_418e5e0_0" Pin1InfoVect1LinkObjId="SW-125726_0" Pin1InfoVect2LinkObjId="SW-125727_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="320,-639 320,-702 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_42e4e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="320,-702 320,-757 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_418e5e0@0" ObjectIDND1="22998@x" ObjectIDND2="22999@x" ObjectIDZND0="g_2b2afa0@0" Pin0InfoVect0LinkObjId="g_2b2afa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_418e5e0_0" Pin1InfoVect1LinkObjId="SW-125726_0" Pin1InfoVect2LinkObjId="SW-125727_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="320,-702 320,-757 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_41ef010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="320,-884 352,-884 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="powerLine" EndDevType0="lightningRod" ObjectIDND0="g_2b2afa0@0" ObjectIDND1="31882@1" ObjectIDZND0="g_41b19e0@0" Pin0InfoVect0LinkObjId="g_41b19e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2b2afa0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="320,-884 352,-884 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a9b960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="320,-837 320,-884 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="g_2b2afa0@1" ObjectIDZND0="g_41b19e0@0" ObjectIDZND1="31882@1" Pin0InfoVect0LinkObjId="g_41b19e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b2afa0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="320,-837 320,-884 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3430750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="320,-913 320,-884 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="31882@1" ObjectIDZND0="g_41b19e0@0" ObjectIDZND1="g_2b2afa0@0" Pin0InfoVect0LinkObjId="g_41b19e0_0" Pin0InfoVect1LinkObjId="g_2b2afa0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="320,-913 320,-884 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2ae18a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="977,-634 1065,-566 1065,-448 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="transformer2" EndDevType0="busSection" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="977,-634 1065,-566 1065,-448 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2aba850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="977,-499 977,-458 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" ObjectIDND0="0@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="977,-499 977,-458 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_334f000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="977,-591 977,-634 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" EndDevType1="transformer2" ObjectIDND0="0@0" ObjectIDZND0="0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="977,-591 977,-634 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_334f340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="977,-655 977,-634 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="busSection" EndDevType1="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="977,-655 977,-634 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_22d7510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="912,-761 912,-746 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="g_331d0c0@0" Pin0InfoVect0LinkObjId="g_331d0c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="912,-761 912,-746 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3d8eb20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="946,-761 912,-761 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="g_331d0c0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_331d0c0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="946,-761 912,-761 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3e19350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="912,-761 867,-761 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" EndDevType0="switch" ObjectIDND0="g_331d0c0@0" ObjectIDND1="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_331d0c0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="912,-761 867,-761 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_26055b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="867,-725 867,-708 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_4068190@0" Pin0InfoVect0LinkObjId="g_4068190_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="867,-725 867,-708 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3ff6e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="977,-438 977,-401 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="busSection" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="977,-438 977,-401 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3ebee00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1248,-637 1336,-569 1336,-448 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="transformer2" EndDevType0="busSection" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1248,-637 1336,-569 1336,-448 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a786a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1248,-502 1248,-459 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" ObjectIDND0="0@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1248,-502 1248,-459 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3ef0a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1248,-594 1248,-637 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" EndDevType1="transformer2" ObjectIDND0="0@0" ObjectIDZND0="0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1248,-594 1248,-637 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2ae2080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1248,-438 1248,-401 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="busSection" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1248,-438 1248,-401 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_29c0f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1248,-852 1248,-777 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="transformer2" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1248,-852 1248,-777 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_29cbe00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1248,-652 1248,-637 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" EndDevType1="busSection" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1248,-652 1248,-637 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3c94840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1443,-448 1443,-473 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_2352060@0" Pin0InfoVect0LinkObjId="g_2352060_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1443,-448 1443,-473 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2aa73e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="320,-369 320,-441 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="22996@0" ObjectIDZND0="23021@0" Pin0InfoVect0LinkObjId="SW-125726_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="320,-369 320,-441 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a258b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="320,-458 320,-486 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="23021@1" ObjectIDZND0="22997@0" Pin0InfoVect0LinkObjId="SW-125725_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-125726_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="320,-458 320,-486 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3531010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="320,-513 320,-533 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22997@1" ObjectIDZND0="22998@1" Pin0InfoVect0LinkObjId="SW-125726_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-125725_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="320,-513 320,-533 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_327b7d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="320,-550 320,-590 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="22998@0" ObjectIDZND0="g_418e5e0@0" ObjectIDZND1="22999@x" ObjectIDZND2="g_2b2afa0@0" Pin0InfoVect0LinkObjId="g_418e5e0_0" Pin0InfoVect1LinkObjId="SW-125727_0" Pin0InfoVect2LinkObjId="g_2b2afa0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-125726_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="320,-550 320,-590 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35c3260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-80,-368 -80,-355 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="22996@0" ObjectIDZND0="23015@0" Pin0InfoVect0LinkObjId="SW-125752_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-80,-368 -80,-355 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ee1660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-80,-246 -80,-232 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="23026@0" ObjectIDZND0="23016@x" ObjectIDZND1="g_3455000@0" ObjectIDZND2="g_3eef090@0" Pin0InfoVect0LinkObjId="SW-125753_0" Pin0InfoVect1LinkObjId="g_3455000_0" Pin0InfoVect2LinkObjId="g_3eef090_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-125752_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-80,-246 -80,-232 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e098e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-80,-338 -80,-318 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="23015@1" ObjectIDZND0="23014@1" Pin0InfoVect0LinkObjId="SW-125751_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-125752_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-80,-338 -80,-318 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_42bab30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-80,-291 -80,-263 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="23014@0" ObjectIDZND0="23026@1" Pin0InfoVect0LinkObjId="SW-125752_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-125751_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-80,-291 -80,-263 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33136e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="96,-369 96,-355 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="22996@0" ObjectIDZND0="23010@0" Pin0InfoVect0LinkObjId="SW-125746_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="96,-369 96,-355 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f7f110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="96,-246 96,-230 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="23025@0" ObjectIDZND0="g_3286ad0@0" ObjectIDZND1="23011@x" ObjectIDZND2="g_40e59e0@0" Pin0InfoVect0LinkObjId="g_3286ad0_0" Pin0InfoVect1LinkObjId="SW-125747_0" Pin0InfoVect2LinkObjId="g_40e59e0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-125746_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="96,-246 96,-230 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26019f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="96,-338 96,-318 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="23010@1" ObjectIDZND0="23009@1" Pin0InfoVect0LinkObjId="SW-125745_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-125746_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="96,-338 96,-318 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cf1750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="96,-291 96,-263 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="23009@0" ObjectIDZND0="23025@1" Pin0InfoVect0LinkObjId="SW-125746_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-125745_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="96,-291 96,-263 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d8f3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="432,-367 432,-346 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="22996@0" ObjectIDZND0="23001@0" Pin0InfoVect0LinkObjId="SW-125731_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="432,-367 432,-346 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2622c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="432,-237 432,-223 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="23022@0" ObjectIDZND0="g_29c18e0@0" ObjectIDZND1="23002@x" ObjectIDZND2="g_3fd8260@0" Pin0InfoVect0LinkObjId="g_29c18e0_0" Pin0InfoVect1LinkObjId="SW-125732_0" Pin0InfoVect2LinkObjId="g_3fd8260_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-125731_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="432,-237 432,-223 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4462fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="432,-329 432,-309 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="23001@1" ObjectIDZND0="23000@1" Pin0InfoVect0LinkObjId="SW-125730_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-125731_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="432,-329 432,-309 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2af8c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="432,-282 432,-254 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="23000@0" ObjectIDZND0="23022@1" Pin0InfoVect0LinkObjId="SW-125731_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-125730_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="432,-282 432,-254 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35c65e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="608,-366 608,-342 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="22996@0" ObjectIDZND0="23004@0" Pin0InfoVect0LinkObjId="SW-125736_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="608,-366 608,-342 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35c6810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="608,-233 608,-223 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="23023@0" ObjectIDZND0="g_235b9a0@0" ObjectIDZND1="23005@x" ObjectIDZND2="g_3ca58b0@0" Pin0InfoVect0LinkObjId="g_235b9a0_0" Pin0InfoVect1LinkObjId="SW-125737_0" Pin0InfoVect2LinkObjId="g_3ca58b0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-125736_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="608,-233 608,-223 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3fb8720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="608,-325 608,-305 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="23004@1" ObjectIDZND0="23003@1" Pin0InfoVect0LinkObjId="SW-125735_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-125736_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="608,-325 608,-305 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3fb8980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="608,-278 608,-250 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="23003@0" ObjectIDZND0="23023@1" Pin0InfoVect0LinkObjId="SW-125736_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-125735_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="608,-278 608,-250 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c933f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="784,-370 784,-346 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="22996@0" ObjectIDZND0="23007@0" Pin0InfoVect0LinkObjId="SW-125741_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="784,-370 784,-346 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2abc6e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="784,-237 784,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="23024@0" ObjectIDZND0="g_3d477e0@0" ObjectIDZND1="23008@x" ObjectIDZND2="g_42539c0@0" Pin0InfoVect0LinkObjId="g_3d477e0_0" Pin0InfoVect1LinkObjId="SW-125742_0" Pin0InfoVect2LinkObjId="g_42539c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-125741_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="784,-237 784,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2abc910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="784,-329 784,-309 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="23007@1" ObjectIDZND0="23006@1" Pin0InfoVect0LinkObjId="SW-125740_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-125741_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="784,-329 784,-309 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_42a83b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="784,-282 784,-254 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="23006@0" ObjectIDZND0="23024@1" Pin0InfoVect0LinkObjId="SW-125741_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-125740_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="784,-282 784,-254 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectPoint_Layer"/><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-125722" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -119.000000 103.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="125722" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23014"/>
     <cge:Term_Ref ObjectID="32407"/>
    <cge:TPSR_Ref TObjectID="23014"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-125723" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -119.000000 103.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="125723" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23014"/>
     <cge:Term_Ref ObjectID="32407"/>
    <cge:TPSR_Ref TObjectID="23014"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-125712" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -119.000000 103.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="125712" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23014"/>
     <cge:Term_Ref ObjectID="32407"/>
    <cge:TPSR_Ref TObjectID="23014"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-125709" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 64.000000 102.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="125709" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23009"/>
     <cge:Term_Ref ObjectID="32397"/>
    <cge:TPSR_Ref TObjectID="23009"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-125710" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 64.000000 102.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="125710" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23009"/>
     <cge:Term_Ref ObjectID="32397"/>
    <cge:TPSR_Ref TObjectID="23009"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-125699" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 64.000000 102.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="125699" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23009"/>
     <cge:Term_Ref ObjectID="32397"/>
    <cge:TPSR_Ref TObjectID="23009"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-125670" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 400.000000 44.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="125670" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23000"/>
     <cge:Term_Ref ObjectID="32379"/>
    <cge:TPSR_Ref TObjectID="23000"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-125671" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 400.000000 44.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="125671" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23000"/>
     <cge:Term_Ref ObjectID="32379"/>
    <cge:TPSR_Ref TObjectID="23000"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-125660" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 400.000000 44.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="125660" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23000"/>
     <cge:Term_Ref ObjectID="32379"/>
    <cge:TPSR_Ref TObjectID="23000"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-125683" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 577.000000 45.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="125683" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23003"/>
     <cge:Term_Ref ObjectID="32385"/>
    <cge:TPSR_Ref TObjectID="23003"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-125684" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 577.000000 45.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="125684" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23003"/>
     <cge:Term_Ref ObjectID="32385"/>
    <cge:TPSR_Ref TObjectID="23003"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-125673" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 577.000000 45.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="125673" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23003"/>
     <cge:Term_Ref ObjectID="32385"/>
    <cge:TPSR_Ref TObjectID="23003"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-125696" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 753.000000 45.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="125696" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23006"/>
     <cge:Term_Ref ObjectID="32391"/>
    <cge:TPSR_Ref TObjectID="23006"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-125697" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 753.000000 45.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="125697" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23006"/>
     <cge:Term_Ref ObjectID="32391"/>
    <cge:TPSR_Ref TObjectID="23006"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-125686" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 753.000000 45.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="125686" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23006"/>
     <cge:Term_Ref ObjectID="32391"/>
    <cge:TPSR_Ref TObjectID="23006"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-125657" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 456.000000 -525.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="125657" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22997"/>
     <cge:Term_Ref ObjectID="32373"/>
    <cge:TPSR_Ref TObjectID="22997"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-125658" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 456.000000 -525.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="125658" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22997"/>
     <cge:Term_Ref ObjectID="32373"/>
    <cge:TPSR_Ref TObjectID="22997"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-125647" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 456.000000 -525.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="125647" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22997"/>
     <cge:Term_Ref ObjectID="32373"/>
    <cge:TPSR_Ref TObjectID="22997"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-125639" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -81.000000 -498.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="125639" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22996"/>
     <cge:Term_Ref ObjectID="32372"/>
    <cge:TPSR_Ref TObjectID="22996"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-125640" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -81.000000 -498.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="125640" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22996"/>
     <cge:Term_Ref ObjectID="32372"/>
    <cge:TPSR_Ref TObjectID="22996"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-125641" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -81.000000 -498.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="125641" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22996"/>
     <cge:Term_Ref ObjectID="32372"/>
    <cge:TPSR_Ref TObjectID="22996"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-125645" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -81.000000 -498.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="125645" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22996"/>
     <cge:Term_Ref ObjectID="32372"/>
    <cge:TPSR_Ref TObjectID="22996"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-125642" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -81.000000 -498.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="125642" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22996"/>
     <cge:Term_Ref ObjectID="32372"/>
    <cge:TPSR_Ref TObjectID="22996"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-125646" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -81.000000 -498.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="125646" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22996"/>
     <cge:Term_Ref ObjectID="32372"/>
    <cge:TPSR_Ref TObjectID="22996"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="155" x="-427" y="-904"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="155" x="-427" y="-904"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-479" y="-921"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-479" y="-921"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <polygon fill="rgb(255,255,255)" points="-270,-759 -273,-762 -273,-708 -270,-711 -270,-759" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(255,255,255)" points="-270,-759 -273,-762 -124,-762 -127,-759 -270,-759" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(112,119,119)" points="-270,-711 -273,-708 -124,-708 -127,-711 -270,-711" stroke="rgb(112,119,119)"/>
     <polygon fill="rgb(112,119,119)" points="-127,-759 -124,-762 -124,-708 -127,-711 -127,-759" stroke="rgb(112,119,119)"/>
     <rect fill="rgb(224,238,238)" height="48" stroke="rgb(224,238,238)" width="143" x="-270" y="-759"/>
     <rect fill="none" height="48" qtmmishow="hidden" stroke="rgb(255,0,0)" width="143" x="-270" y="-759"/>
    </a>
   <metadata/></g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_地调直调_光伏.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="155" x="-427" y="-904"/></g>
   <g href="cx_索引_接线图_地调直调_光伏.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-479" y="-921"/></g>
   <g href="AVC班幸开关站.svg" style="fill-opacity:0"><rect height="48" qtmmishow="hidden" stroke="rgb(255,0,0)" width="143" x="-270" y="-759"/></g>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(65,105,225)" stroke-width="1" x1="92" x2="65" y1="-97" y2="-97"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(65,105,225)" stroke-width="1" x1="65" x2="65" y1="-97" y2="-67"/>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_34e6440">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 219.000000 -116.000000)" xlink:href="#voltageTransformer:shape97"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -105.695431 -9.000000)" xlink:href="#transformer2:shape63_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -105.695431 -9.000000)" xlink:href="#transformer2:shape63_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 83.055838 2.000000)" xlink:href="#transformer2:shape3_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 83.055838 2.000000)" xlink:href="#transformer2:shape3_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1232.000000 -647.000000)" xlink:href="#transformer2:shape65_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1232.000000 -647.000000)" xlink:href="#transformer2:shape65_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 941.000000 -650.000000)" xlink:href="#transformer2:shape64_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 941.000000 -650.000000)" xlink:href="#transformer2:shape64_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -439.000000 -845.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-125657" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 -406.461538 -712.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="125657" ObjectName="CX_BX:CX_BX_341BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-125658" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 -406.461538 -671.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="125658" ObjectName="CX_BX:CX_BX_341BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-125758" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 879.000000 -577.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="125758" ObjectName="CX_BX:CX_BX_GG_Ua_86"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-125759" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 879.000000 -559.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="125759" ObjectName="CX_BX:CX_BX_GG_Ub_87"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-125760" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 879.000000 -541.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="125760" ObjectName="CX_BX:CX_BX_GG_Uc_88"/>
    </metadata>
   </g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-125244" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -240.500000 -813.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22930" ObjectName="DYN-CX_BX"/>
     <cge:Meas_Ref ObjectId="125244"/>
    </metadata>
   </g>
  </g><g id="ArcThreePoints_Layer">
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="978,-437 979,-437 980,-438 981,-438 982,-439 983,-440 983,-441 984,-442 985,-443 985,-444 985,-446 985,-447 985,-448 985,-450 985,-451 985,-452 984,-453 984,-454 983,-456 982,-456 981,-457 980,-458 979,-458 978,-459 977,-459 " stroke="rgb(65,105,225)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="1248,-438 1249,-438 1250,-439 1251,-439 1252,-440 1253,-441 1254,-442 1255,-443 1255,-444 1256,-445 1256,-447 1256,-448 1256,-449 1256,-451 1256,-452 1256,-453 1255,-454 1254,-455 1254,-457 1253,-457 1252,-458 1251,-459 1249,-459 1248,-460 1247,-460 " stroke="rgb(65,105,225)" stroke-width="1"/>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_BX.CX_BX_3ⅠM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-153,-368 833,-368 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="22996" ObjectName="BS-CX_BX.CX_BX_3ⅠM"/>
    <cge:TPSR_Ref TObjectID="22996"/></metadata>
   <polyline fill="none" opacity="0" points="-153,-368 833,-368 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="912,-401 1509,-401 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="912,-401 1509,-401 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="913,-448 1507,-448 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="913,-448 1507,-448 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="22996" cx="256" cy="-368" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1065" cy="-448" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1336" cy="-448" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1443" cy="-448" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="977" cy="-401" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1248" cy="-401" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-125753">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -134.695431 -200.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23016" ObjectName="SW-CX_BX.CX_BX_35167SW"/>
     <cge:Meas_Ref ObjectId="125753"/>
    <cge:TPSR_Ref TObjectID="23016"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-125754">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -159.695431 -49.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23017" ObjectName="SW-CX_BX.CX_BX_3010SW"/>
     <cge:Meas_Ref ObjectId="125754"/>
    <cge:TPSR_Ref TObjectID="23017"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-125747">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 43.055838 -199.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23011" ObjectName="SW-CX_BX.CX_BX_35260SW"/>
     <cge:Meas_Ref ObjectId="125747"/>
    <cge:TPSR_Ref TObjectID="23011"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-125748">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 87.055838 -74.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23012" ObjectName="SW-CX_BX.CX_BX_3526SW"/>
     <cge:Meas_Ref ObjectId="125748"/>
    <cge:TPSR_Ref TObjectID="23012"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-125749">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 43.055838 -58.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23013" ObjectName="SW-CX_BX.CX_BX_35267SW"/>
     <cge:Meas_Ref ObjectId="125749"/>
    <cge:TPSR_Ref TObjectID="23013"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-125756">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 246.000000 -324.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23018" ObjectName="SW-CX_BX.CX_BX_3901XC"/>
     <cge:Meas_Ref ObjectId="125756"/>
    <cge:TPSR_Ref TObjectID="23018"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-125732">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 375.000000 -155.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23002" ObjectName="SW-CX_BX.CX_BX_35367SW"/>
     <cge:Meas_Ref ObjectId="125732"/>
    <cge:TPSR_Ref TObjectID="23002"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-125737">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 551.000000 -155.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23005" ObjectName="SW-CX_BX.CX_BX_35467SW"/>
     <cge:Meas_Ref ObjectId="125737"/>
    <cge:TPSR_Ref TObjectID="23005"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-125742">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 727.000000 -156.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23008" ObjectName="SW-CX_BX.CX_BX_35567SW"/>
     <cge:Meas_Ref ObjectId="125742"/>
    <cge:TPSR_Ref TObjectID="23008"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-125727">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 335.000000 -634.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22999" ObjectName="SW-CX_BX.CX_BX_34167SW"/>
     <cge:Meas_Ref ObjectId="125727"/>
    <cge:TPSR_Ref TObjectID="22999"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 858.000000 -720.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-125726">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 310.000000 -526.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22998" ObjectName="SW-CX_BX.CX_BX_341XC"/>
     <cge:Meas_Ref ObjectId="125726"/>
    <cge:TPSR_Ref TObjectID="22998"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-125726">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 310.000000 -434.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23021" ObjectName="SW-CX_BX.CX_BX_341XC1"/>
     <cge:Meas_Ref ObjectId="125726"/>
    <cge:TPSR_Ref TObjectID="23021"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-125752">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -90.000000 -331.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23015" ObjectName="SW-CX_BX.CX_BX_351XC"/>
     <cge:Meas_Ref ObjectId="125752"/>
    <cge:TPSR_Ref TObjectID="23015"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-125752">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -90.000000 -239.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23026" ObjectName="SW-CX_BX.CX_BX_351XC1"/>
     <cge:Meas_Ref ObjectId="125752"/>
    <cge:TPSR_Ref TObjectID="23026"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-125746">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 86.000000 -331.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23010" ObjectName="SW-CX_BX.CX_BX_352XC"/>
     <cge:Meas_Ref ObjectId="125746"/>
    <cge:TPSR_Ref TObjectID="23010"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-125746">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 86.000000 -239.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23025" ObjectName="SW-CX_BX.CX_BX_352XC1"/>
     <cge:Meas_Ref ObjectId="125746"/>
    <cge:TPSR_Ref TObjectID="23025"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-125731">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 422.000000 -322.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23001" ObjectName="SW-CX_BX.CX_BX_353XC"/>
     <cge:Meas_Ref ObjectId="125731"/>
    <cge:TPSR_Ref TObjectID="23001"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-125731">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 422.000000 -230.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23022" ObjectName="SW-CX_BX.CX_BX_353XC1"/>
     <cge:Meas_Ref ObjectId="125731"/>
    <cge:TPSR_Ref TObjectID="23022"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-125736">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 598.000000 -318.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23004" ObjectName="SW-CX_BX.CX_BX_354XC"/>
     <cge:Meas_Ref ObjectId="125736"/>
    <cge:TPSR_Ref TObjectID="23004"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-125736">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 598.000000 -226.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23023" ObjectName="SW-CX_BX.CX_BX_354XC1"/>
     <cge:Meas_Ref ObjectId="125736"/>
    <cge:TPSR_Ref TObjectID="23023"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-125741">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 774.000000 -322.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23007" ObjectName="SW-CX_BX.CX_BX_355XC"/>
     <cge:Meas_Ref ObjectId="125741"/>
    <cge:TPSR_Ref TObjectID="23007"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-125741">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 774.000000 -230.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23024" ObjectName="SW-CX_BX.CX_BX_355XC1"/>
     <cge:Meas_Ref ObjectId="125741"/>
    <cge:TPSR_Ref TObjectID="23024"/></metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3443680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -156.000000 18.000000) translate(0,15)">35kV1号接地变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3443680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -156.000000 18.000000) translate(0,33)"> 及消弧线圈</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_264e9f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -530.000000 -314.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_264e9f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -530.000000 -314.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_264e9f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -530.000000 -314.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_264e9f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -530.000000 -314.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_264e9f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -530.000000 -314.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_264e9f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -530.000000 -314.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_264e9f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -530.000000 -314.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_264e9f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -530.000000 -314.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_264e9f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -530.000000 -314.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_264e9f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -530.000000 -314.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_264e9f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -530.000000 -314.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_264e9f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -530.000000 -314.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_264e9f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -530.000000 -314.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_264e9f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -530.000000 -314.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_264e9f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -530.000000 -314.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_264e9f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -530.000000 -314.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_264e9f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -530.000000 -314.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_264e9f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -530.000000 -314.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b025f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -530.000000 -752.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b025f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -530.000000 -752.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b025f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -530.000000 -752.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b025f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -530.000000 -752.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b025f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -530.000000 -752.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b025f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -530.000000 -752.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b025f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -530.000000 -752.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_29cc130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -397.000000 -893.500000) translate(0,16)">班幸开关站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3ea1c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6.000000 57.000000) translate(0,15)">1号动态无功补偿装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3ea1c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6.000000 57.000000) translate(0,33)">      ±6MVar</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3424a60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 213.000000 -101.000000) translate(0,15)"> 35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3424a60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 213.000000 -101.000000) translate(0,33)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3f7c220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 347.000000 -14.000000) translate(0,15)">     1号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3f7c220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 347.000000 -14.000000) translate(0,33)">（1-10、17号发电机）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ae1670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 549.000000 -23.000000) translate(0,15)">  2号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ae1670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 549.000000 -23.000000) translate(0,33)">（13、14、16、</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ae1670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 549.000000 -23.000000) translate(0,51)">27-35号发电机）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_32fad30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 735.000000 -23.000000) translate(0,15)">  3号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_32fad30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 735.000000 -23.000000) translate(0,33)">（11、12、15、</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_32fad30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 735.000000 -23.000000) translate(0,51)">18-26号发电机）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_43a93f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 278.000000 -867.000000) translate(0,15)">35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_43a93f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 278.000000 -867.000000) translate(0,33)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_43a93f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 278.000000 -867.000000) translate(0,51)">方</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_43a93f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 278.000000 -867.000000) translate(0,69)">班</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_43a93f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 278.000000 -867.000000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_263b8c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 868.000000 -815.000000) translate(0,15)">1号接地变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_263b8c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 868.000000 -815.000000) translate(0,33)">及消弧线圈</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b46500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 939.000000 -877.000000) translate(0,15)">1号所用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3551440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 943.500000 -898.000000) translate(0,15)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_26021e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1158.500000 -916.000000) translate(0,15)">10kV猛莲线那窝支线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3f20a60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1185.500000 -895.000000) translate(0,15)">千艺公司支线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3f20a60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1185.500000 -895.000000) translate(0,33)">1号所用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2656df0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1273.000000 -787.000000) translate(0,15)">备用所用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2656df0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1273.000000 -787.000000) translate(0,33)">（施工变）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_41e3b00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1111.000000 -389.000000) translate(0,15)">0.4kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3443960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 337.000000 -674.000000) translate(0,15)">34167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_367b7b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 337.000000 -514.000000) translate(0,15)">341</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b61d30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3.000000 -394.000000) translate(0,15)"> 35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_41342c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -64.000000 -308.000000) translate(0,15)">351</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_40bc6c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 115.000000 -313.000000) translate(0,15)">352</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34fc250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 450.000000 -308.000000) translate(0,15)">353</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_35c25c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 624.000000 -305.000000) translate(0,15)">354</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_353b1c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 803.000000 -307.000000) translate(0,15)">355</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_238d250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -142.000000 -232.000000) translate(0,15)">35167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3d6f880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -206.000000 -76.000000) translate(0,15)">3010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_44629b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -56.000000 -98.000000) translate(0,15)">2000kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_358e5c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 32.000000 -234.000000) translate(0,15)">35260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_358d8c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 106.000000 -105.000000) translate(0,15)">3526</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3654e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 33.000000 -56.000000) translate(0,15)">35267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a7fe80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 113.000000 -32.000000) translate(0,15)">6000kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b47210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 273.000000 -343.000000) translate(0,15)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2abcc20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 374.000000 -192.000000) translate(0,15)">35367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3f1de30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 546.000000 -190.000000) translate(0,15)">35467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_411bbb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 720.000000 -187.000000) translate(0,15)">35567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3ce9280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 809.000000 -747.000000) translate(0,15)">3010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a51ee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -419.000000 37.000000) translate(0,15)">4928</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_35790f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -244.000000 -745.000000) translate(0,16)">AGC/AVC</text>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_3eef090">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -50.695431 -164.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3455000">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -84.695431 -125.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3ef3590">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -128.695431 -25.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3286ad0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 129.055838 -166.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_40e59e0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 91.055838 -125.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_46b4f70">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 79.055838 46.000000)" xlink:href="#lightningRod:shape146"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_338e340">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 284.000000 -230.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29c18e0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 462.000000 -150.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3fd8260">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 427.000000 -77.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_235b9a0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 638.000000 -150.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3ca58b0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 603.000000 -76.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3d477e0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 814.000000 -151.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_42539c0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 779.000000 -78.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_418e5e0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 229.000000 -583.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b2afa0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 315.000000 -752.000000)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_41b19e0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 348.000000 -876.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_331d0c0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 905.000000 -692.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3ef2ee0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -158.000000 6.000000)" xlink:href="#lightningRod:shape40"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4068190">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 860.000000 -660.000000)" xlink:href="#lightningRod:shape40"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_BX"/>
</svg>