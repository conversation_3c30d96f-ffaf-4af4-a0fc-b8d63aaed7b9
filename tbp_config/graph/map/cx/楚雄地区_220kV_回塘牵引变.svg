<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-267" aopId="786686" id="thSvg" product="E8000V2" version="1.0" viewBox="-54 -983 1147 988">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="8" x2="5" y1="1" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="10" x2="3" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="8" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="1" x2="12" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="63" x2="63" y1="6" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="61" x2="61" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="58" x2="58" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="58" x2="49" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape4_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="14" x2="39" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="34" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="34" y1="7" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="5" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape4_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="11" y1="3" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="3" x2="43" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape4-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="11" x2="11" y1="3" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="43" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape4-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="11" x2="11" y1="3" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="43" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape40_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="45" x2="19" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="45" x2="19" y1="37" y2="37"/>
    <circle cx="32" cy="20" fillStyle="0" r="4.5" stroke-width="0.680952"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="44" x2="20" y1="9" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="18" y1="13" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="13" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="22" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="19" y2="19"/>
   </symbol>
   <symbol id="switch2:shape40_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="13" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="14" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="21" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="23" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="10" x2="18" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="45" x2="18" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="45" x2="18" y1="36" y2="36"/>
    <ellipse cx="32" cy="19" fillStyle="0" rx="4" ry="4.5" stroke-width="0.680952"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="37" y2="4"/>
   </symbol>
   <symbol id="switch2:shape40-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="45" x2="19" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="45" x2="19" y1="37" y2="37"/>
    <circle cx="32" cy="20" fillStyle="0" r="4.5" stroke-width="0.680952"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="44" x2="20" y1="9" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="18" y1="13" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="13" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="22" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="19" y2="19"/>
   </symbol>
   <symbol id="switch2:shape40-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="13" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="14" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="21" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="23" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="10" x2="18" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="45" x2="18" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="45" x2="18" y1="36" y2="36"/>
    <ellipse cx="32" cy="19" fillStyle="0" rx="4" ry="4.5" stroke-width="0.680952"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="37" y2="4"/>
   </symbol>
   <symbol id="switch2:shape34_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="31" x2="14" y1="13" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape34_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="33" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape34-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="31" x2="14" y1="13" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape34-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="33" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
   </symbol>
   <symbol id="transformer2:shape11_0">
    <ellipse cx="13" cy="34" fillStyle="0" rx="13" ry="12.5" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="40" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="36" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="32" y2="36"/>
   </symbol>
   <symbol id="transformer2:shape11_1">
    <circle cx="13" cy="16" fillStyle="0" r="13" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="20" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="16" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="12" y2="16"/>
   </symbol>
   <symbol id="voltageTransformer:shape52">
    <circle cx="27" cy="15" r="7.5" stroke-width="0.804311"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="27,28 13,28 13,1 40,1 40,8 " stroke-width="0.964286"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="24" x2="27" y1="17" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="27" x2="27" y1="15" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="30" x2="27" y1="17" y2="15"/>
    <circle cx="27" cy="27" r="7.5" stroke-width="0.804311"/>
    <circle cx="39" cy="27" r="7.5" stroke-width="0.804311"/>
    <polyline points="27,15 6,15 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.16541" x1="4" x2="4" y1="12" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.07362" x1="2" x2="2" y1="13" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.16541" x1="6" x2="6" y1="10" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="24" x2="27" y1="30" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="27" x2="27" y1="28" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="30" x2="27" y1="30" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="40" x2="40" y1="15" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="37" x2="40" y1="17" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="43" x2="40" y1="17" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="39" x2="37" y1="30" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="41" x2="43" y1="30" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="43" x2="37" y1="26" y2="26"/>
    <circle cx="39" cy="15" r="7.5" stroke-width="0.804311"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_31b04d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_31b0ee0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_31b1860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_31b2a70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_31b3d60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_31b4a00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_31b55a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_31b5fa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_31b6810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_31b71f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注1</text>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_31b7ce0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注2</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_31b8320" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_31b9cd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_31ba8e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_31bb380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_31bbce0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">禁止操作</text>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_31bd700" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_31be170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_31bea30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_31bf420" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_31c0600" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_31c0f80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_31c1a70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="78" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_31c6d90" transform="matrix(1.246377 -0.000000 0.000000 -1.035714 2.739130 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_31c79e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">测试</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_31c3800" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_31c4c80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_31c5a00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_31c8e00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_31ca260" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="998" width="1157" x="-59" y="-988"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-217506">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 898.000000 -442.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33650" ObjectName="SW-CX_HTQ.CX_HTQ_112BK"/>
     <cge:Meas_Ref ObjectId="217506"/>
    <cge:TPSR_Ref TObjectID="33650"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-217496">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 530.000000 -440.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33649" ObjectName="SW-CX_HTQ.CX_HTQ_111BK"/>
     <cge:Meas_Ref ObjectId="217496"/>
    <cge:TPSR_Ref TObjectID="33649"/></metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_3157d10">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 411.000000 -492.000000)" xlink:href="#voltageTransformer:shape52"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e2d360">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 978.000000 -489.000000)" xlink:href="#voltageTransformer:shape52"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2e5ba20" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 452.000000 -794.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e5c1e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 816.000000 -794.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_30cf7a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 615.000000 -627.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3307c10" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 807.000000 -626.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-0KV" id="g_3276250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="539,-737 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="539,-737 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_28e57c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="470,-800 486,-800 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2e5ba20@0" ObjectIDZND0="33652@0" Pin0InfoVect0LinkObjId="SW-217498_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e5ba20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="470,-800 486,-800 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2de0000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="834,-800 850,-800 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2e5c1e0@0" ObjectIDZND0="33659@0" Pin0InfoVect0LinkObjId="SW-217508_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e5c1e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="834,-800 850,-800 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2de2110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="566,-688 539,-688 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_30932c0@0" ObjectIDZND0="33651@x" ObjectIDZND1="33657@x" ObjectIDZND2="33655@x" Pin0InfoVect0LinkObjId="SW-217497_0" Pin0InfoVect1LinkObjId="SW-217503_0" Pin0InfoVect2LinkObjId="SW-217501_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_30932c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="566,-688 539,-688 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2b49630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="539,-688 539,-721 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_30932c0@0" ObjectIDND1="33657@x" ObjectIDND2="33655@x" ObjectIDZND0="33651@0" Pin0InfoVect0LinkObjId="SW-217497_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_30932c0_0" Pin1InfoVect1LinkObjId="SW-217503_0" Pin1InfoVect2LinkObjId="SW-217501_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="539,-688 539,-721 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2b324e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="522,-800 539,-800 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="33652@1" ObjectIDZND0="33651@x" Pin0InfoVect0LinkObjId="SW-217497_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-217498_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="522,-800 539,-800 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2b2ee10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="539,-757 539,-800 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="33651@1" ObjectIDZND0="33652@x" Pin0InfoVect0LinkObjId="SW-217498_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-217497_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="539,-757 539,-800 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_32abda0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="539,-800 539,-862 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="33652@x" ObjectIDND1="33651@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-217498_0" Pin1InfoVect1LinkObjId="SW-217497_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="539,-800 539,-862 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_32786a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="886,-800 907,-800 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="33659@1" ObjectIDZND0="33658@x" Pin0InfoVect0LinkObjId="SW-217507_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-217508_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="886,-800 907,-800 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_3294d40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="907,-758 907,-800 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="33658@1" ObjectIDZND0="33659@x" Pin0InfoVect0LinkObjId="SW-217508_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-217507_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="907,-758 907,-800 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_328bd80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="907,-800 907,-861 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="33659@x" ObjectIDND1="33658@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-217508_0" Pin1InfoVect1LinkObjId="SW-217507_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="907,-800 907,-861 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2b30c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="604,-633 619,-633 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="33657@1" ObjectIDZND0="g_30cf7a0@0" Pin0InfoVect0LinkObjId="g_30cf7a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-217503_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="604,-633 619,-633 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_319d3a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="825,-632 841,-632 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3307c10@0" ObjectIDZND0="33664@0" Pin0InfoVect0LinkObjId="SW-217513_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3307c10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="825,-632 841,-632 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_31ab700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="567,-632 539,-632 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="33657@0" ObjectIDZND0="g_30932c0@0" ObjectIDZND1="33651@x" ObjectIDZND2="33655@x" Pin0InfoVect0LinkObjId="g_30932c0_0" Pin0InfoVect1LinkObjId="SW-217497_0" Pin0InfoVect2LinkObjId="SW-217501_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-217503_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="567,-632 539,-632 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_30d0820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="539,-632 539,-688 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="voltageTransformer" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="33657@x" ObjectIDND1="33655@x" ObjectIDND2="g_3157d10@0" ObjectIDZND0="g_30932c0@0" ObjectIDZND1="33651@x" Pin0InfoVect0LinkObjId="g_30932c0_0" Pin0InfoVect1LinkObjId="SW-217497_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-217503_0" Pin1InfoVect1LinkObjId="SW-217501_0" Pin1InfoVect2LinkObjId="g_3157d10_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="539,-632 539,-688 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_3151740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="935,-688 907,-688 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2f44c50@0" ObjectIDZND0="33658@x" ObjectIDZND1="33664@x" ObjectIDZND2="33662@x" Pin0InfoVect0LinkObjId="SW-217507_0" Pin0InfoVect1LinkObjId="SW-217513_0" Pin0InfoVect2LinkObjId="SW-217511_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f44c50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="935,-688 907,-688 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_31a20e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="907,-688 907,-722 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2f44c50@0" ObjectIDND1="33664@x" ObjectIDND2="33662@x" ObjectIDZND0="33658@0" Pin0InfoVect0LinkObjId="SW-217507_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2f44c50_0" Pin1InfoVect1LinkObjId="SW-217513_0" Pin1InfoVect2LinkObjId="SW-217511_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="907,-688 907,-722 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_31568e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="877,-632 907,-632 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="33664@1" ObjectIDZND0="g_2f44c50@0" ObjectIDZND1="33658@x" ObjectIDZND2="33662@x" Pin0InfoVect0LinkObjId="g_2f44c50_0" Pin0InfoVect1LinkObjId="SW-217507_0" Pin0InfoVect2LinkObjId="SW-217511_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-217513_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="877,-632 907,-632 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_3158b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="907,-632 907,-688 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="voltageTransformer" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="33664@x" ObjectIDND1="33662@x" ObjectIDND2="g_2e2d360@0" ObjectIDZND0="g_2f44c50@0" ObjectIDZND1="33658@x" Pin0InfoVect0LinkObjId="g_2f44c50_0" Pin0InfoVect1LinkObjId="SW-217507_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-217513_0" Pin1InfoVect1LinkObjId="SW-217511_0" Pin1InfoVect2LinkObjId="g_2e2d360_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="907,-632 907,-688 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2f3de80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="539,-320 539,-376 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="33646@0" ObjectIDZND0="33653@0" Pin0InfoVect0LinkObjId="SW-217499_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="539,-320 539,-376 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_33322b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="907,-321 907,-377 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="33647@0" ObjectIDZND0="33660@0" Pin0InfoVect0LinkObjId="SW-217509_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="907,-321 907,-377 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2e4e280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="539,-632 539,-581 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="33657@x" ObjectIDND1="g_30932c0@0" ObjectIDND2="33651@x" ObjectIDZND0="33655@x" ObjectIDZND1="g_3157d10@0" ObjectIDZND2="g_2df3600@0" Pin0InfoVect0LinkObjId="SW-217501_0" Pin0InfoVect1LinkObjId="g_3157d10_0" Pin0InfoVect2LinkObjId="g_2df3600_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-217503_0" Pin1InfoVect1LinkObjId="g_30932c0_0" Pin1InfoVect2LinkObjId="SW-217497_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="539,-632 539,-581 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2e518a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="539,-581 539,-551 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="33657@x" ObjectIDND1="g_30932c0@0" ObjectIDND2="33651@x" ObjectIDZND0="33655@1" Pin0InfoVect0LinkObjId="SW-217501_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-217503_0" Pin1InfoVect1LinkObjId="g_30932c0_0" Pin1InfoVect2LinkObjId="SW-217497_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="539,-581 539,-551 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2e52280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="438,-527 438,-581 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_3157d10@0" ObjectIDZND0="g_2df3600@0" ObjectIDZND1="33657@x" ObjectIDZND2="g_30932c0@0" Pin0InfoVect0LinkObjId="g_2df3600_0" Pin0InfoVect1LinkObjId="SW-217503_0" Pin0InfoVect2LinkObjId="g_30932c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3157d10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="438,-527 438,-581 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29bc690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="406,-581 438,-581 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2df3600@0" ObjectIDZND0="g_3157d10@0" ObjectIDZND1="33657@x" ObjectIDZND2="g_30932c0@0" Pin0InfoVect0LinkObjId="g_3157d10_0" Pin0InfoVect1LinkObjId="SW-217503_0" Pin0InfoVect2LinkObjId="g_30932c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2df3600_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="406,-581 438,-581 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_3347f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="438,-581 539,-581 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_3157d10@0" ObjectIDND1="g_2df3600@0" ObjectIDZND0="33657@x" ObjectIDZND1="g_30932c0@0" ObjectIDZND2="33651@x" Pin0InfoVect0LinkObjId="SW-217503_0" Pin0InfoVect1LinkObjId="g_30932c0_0" Pin0InfoVect2LinkObjId="SW-217497_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3157d10_0" Pin1InfoVect1LinkObjId="g_2df3600_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="438,-581 539,-581 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2f3bcf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="907,-632 907,-581 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="33664@x" ObjectIDND1="g_2f44c50@0" ObjectIDND2="33658@x" ObjectIDZND0="33662@x" ObjectIDZND1="g_2e2d360@0" ObjectIDZND2="g_2e39080@0" Pin0InfoVect0LinkObjId="SW-217511_0" Pin0InfoVect1LinkObjId="g_2e2d360_0" Pin0InfoVect2LinkObjId="g_2e39080_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-217513_0" Pin1InfoVect1LinkObjId="g_2f44c50_0" Pin1InfoVect2LinkObjId="SW-217507_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="907,-632 907,-581 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2e54990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="907,-581 907,-550 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="33664@x" ObjectIDND1="g_2f44c50@0" ObjectIDND2="33658@x" ObjectIDZND0="33662@1" Pin0InfoVect0LinkObjId="SW-217511_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-217513_0" Pin1InfoVect1LinkObjId="g_2f44c50_0" Pin1InfoVect2LinkObjId="SW-217507_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="907,-581 907,-550 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2f58ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1005,-524 1005,-581 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_2e2d360@0" ObjectIDZND0="33664@x" ObjectIDZND1="g_2f44c50@0" ObjectIDZND2="33658@x" Pin0InfoVect0LinkObjId="SW-217513_0" Pin0InfoVect1LinkObjId="g_2f44c50_0" Pin0InfoVect2LinkObjId="SW-217507_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e2d360_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1005,-524 1005,-581 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2e4c370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="907,-581 1005,-581 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" ObjectIDND0="33664@x" ObjectIDND1="g_2f44c50@0" ObjectIDND2="33658@x" ObjectIDZND0="g_2e2d360@0" ObjectIDZND1="g_2e39080@0" Pin0InfoVect0LinkObjId="g_2e2d360_0" Pin0InfoVect1LinkObjId="g_2e39080_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-217513_0" Pin1InfoVect1LinkObjId="g_2f44c50_0" Pin1InfoVect2LinkObjId="SW-217507_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="907,-581 1005,-581 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2e4f130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1005,-581 1040,-581 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2e2d360@0" ObjectIDND1="33664@x" ObjectIDND2="g_2f44c50@0" ObjectIDZND0="g_2e39080@0" Pin0InfoVect0LinkObjId="g_2e39080_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2e2d360_0" Pin1InfoVect1LinkObjId="SW-217513_0" Pin1InfoVect2LinkObjId="g_2f44c50_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1005,-581 1040,-581 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_3152230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="532,-503 539,-503 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="33656@1" ObjectIDZND0="33655@x" ObjectIDZND1="33649@x" Pin0InfoVect0LinkObjId="SW-217501_0" Pin0InfoVect1LinkObjId="SW-217496_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-217502_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="532,-503 539,-503 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2f364a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="539,-519 539,-503 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="33655@0" ObjectIDZND0="33656@x" ObjectIDZND1="33649@x" Pin0InfoVect0LinkObjId="SW-217502_0" Pin0InfoVect1LinkObjId="SW-217496_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-217501_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="539,-519 539,-503 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_297e400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="539,-503 539,-475 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="33656@x" ObjectIDND1="33655@x" ObjectIDZND0="33649@1" Pin0InfoVect0LinkObjId="SW-217496_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-217502_0" Pin1InfoVect1LinkObjId="SW-217501_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="539,-503 539,-475 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2e35510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="496,-503 485,-503 485,-521 521,-521 521,-533 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="33656@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-217502_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="496,-503 485,-503 485,-521 521,-521 521,-533 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_352a740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="899,-505 907,-505 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="33663@1" ObjectIDZND0="33650@x" ObjectIDZND1="33662@x" Pin0InfoVect0LinkObjId="SW-217506_0" Pin0InfoVect1LinkObjId="SW-217511_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-217512_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="899,-505 907,-505 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_32d8c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="907,-477 907,-502 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="33650@1" ObjectIDZND0="33663@x" ObjectIDZND1="33662@x" Pin0InfoVect0LinkObjId="SW-217512_0" Pin0InfoVect1LinkObjId="SW-217511_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-217506_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="907,-477 907,-502 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_32dcc00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="907,-502 907,-518 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="33663@x" ObjectIDND1="33650@x" ObjectIDZND0="33662@0" Pin0InfoVect0LinkObjId="SW-217511_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-217512_0" Pin1InfoVect1LinkObjId="SW-217506_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="907,-502 907,-518 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2f3ec20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="863,-505 852,-505 852,-522 889,-522 889,-532 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="33663@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-217512_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="863,-505 852,-505 852,-522 889,-522 889,-532 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_3335a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="531,-421 539,-421 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="33654@1" ObjectIDZND0="33653@x" ObjectIDZND1="33649@x" Pin0InfoVect0LinkObjId="SW-217499_0" Pin0InfoVect1LinkObjId="SW-217496_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-217500_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="531,-421 539,-421 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_33e1050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="539,-408 539,-421 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="33653@1" ObjectIDZND0="33654@x" ObjectIDZND1="33649@x" Pin0InfoVect0LinkObjId="SW-217500_0" Pin0InfoVect1LinkObjId="SW-217496_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-217499_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="539,-408 539,-421 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_31ba180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="539,-421 539,-448 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="33654@x" ObjectIDND1="33653@x" ObjectIDZND0="33649@0" Pin0InfoVect0LinkObjId="SW-217496_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-217500_0" Pin1InfoVect1LinkObjId="SW-217499_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="539,-421 539,-448 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_319dee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="898,-421 907,-421 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="33661@1" ObjectIDZND0="33660@x" ObjectIDZND1="33650@x" Pin0InfoVect0LinkObjId="SW-217509_0" Pin0InfoVect1LinkObjId="SW-217506_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-217510_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="898,-421 907,-421 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2e4b820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="907,-409 907,-421 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="33660@1" ObjectIDZND0="33661@x" ObjectIDZND1="33650@x" Pin0InfoVect0LinkObjId="SW-217510_0" Pin0InfoVect1LinkObjId="SW-217506_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-217509_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="907,-409 907,-421 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2f57380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="907,-421 907,-450 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="33661@x" ObjectIDND1="33660@x" ObjectIDZND0="33650@0" Pin0InfoVect0LinkObjId="SW-217506_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-217510_0" Pin1InfoVect1LinkObjId="SW-217509_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="907,-421 907,-450 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2e2aa90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="495,-421 485,-421 485,-406 521,-406 521,-390 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="33654@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-217500_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="495,-421 485,-421 485,-406 521,-406 521,-390 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_3274bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="862,-421 852,-421 852,-406 889,-406 889,-391 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="33661@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-217510_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="862,-421 852,-421 852,-406 889,-406 889,-391 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer"/><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-217333" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 245.000000 -866.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33571" ObjectName="DYN-CX_HTQ"/>
     <cge:Meas_Ref ObjectId="217333"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_32cf7b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -43.000000 -816.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_32cf7b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -43.000000 -816.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_32cf7b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -43.000000 -816.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_32cf7b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -43.000000 -816.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_32cf7b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -43.000000 -816.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_32cf7b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -43.000000 -816.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_32cf7b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -43.000000 -816.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_31b1330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -42.000000 -373.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_31b1330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -42.000000 -373.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_31b1330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -42.000000 -373.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_31b1330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -42.000000 -373.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_31b1330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -42.000000 -373.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_31b1330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -42.000000 -373.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_31b1330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -42.000000 -373.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_31b1330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -42.000000 -373.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_31b1330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -42.000000 -373.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_31b1330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -42.000000 -373.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_31b1330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -42.000000 -373.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_31b1330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -42.000000 -373.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_31b1330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -42.000000 -373.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_31b1330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -42.000000 -373.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_31b1330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -42.000000 -373.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_31b1330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -42.000000 -373.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_31b1330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -42.000000 -373.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_31b1330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -42.000000 -373.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="18" graphid="g_32cd6f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 87.000000 -947.500000) translate(0,15)">回塘牵引变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_31530b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 480.000000 -895.000000) translate(0,17)">500kV仁和变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_319d650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 551.000000 -856.000000) translate(0,17)">仁</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_319d650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 551.000000 -856.000000) translate(0,38)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_319d650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 551.000000 -856.000000) translate(0,59)">牵</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_319d650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 551.000000 -856.000000) translate(0,80)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_31540c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 847.000000 -895.000000) translate(0,17)">220kV方山变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2e3e3b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 917.000000 -856.000000) translate(0,17)">方</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2e3e3b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 917.000000 -856.000000) translate(0,38)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2e3e3b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 917.000000 -856.000000) translate(0,59)">牵</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2e3e3b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 917.000000 -856.000000) translate(0,80)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_3339960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 627.000000 -202.000000) translate(0,20)">220kV回塘牵引变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(240,255,255)" font-family="SimSun" font-size="15" graphid="g_3224500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 547.000000 -743.000000) translate(0,12)">2416</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(240,255,255)" font-family="SimSun" font-size="15" graphid="g_30a4ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 484.000000 -826.000000) translate(0,12)">24167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(240,255,255)" font-family="SimSun" font-size="15" graphid="g_31598a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 573.000000 -656.000000) translate(0,12)">1810</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(240,255,255)" font-family="SimSun" font-size="15" graphid="g_298f7f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 557.000000 -541.000000) translate(0,12)">1811</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(240,255,255)" font-family="SimSun" font-size="15" graphid="g_298fa00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 483.000000 -498.000000) translate(0,12)">18110</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(240,255,255)" font-family="SimSun" font-size="15" graphid="g_2e4f700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 551.000000 -468.000000) translate(0,12)">111</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(240,255,255)" font-family="SimSun" font-size="15" graphid="g_2e4f930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 477.000000 -444.000000) translate(0,12)">18120</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(240,255,255)" font-family="SimSun" font-size="15" graphid="g_2b32800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 556.000000 -400.000000) translate(0,12)">1812</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(240,255,255)" font-family="SimSun" font-size="19" graphid="g_2b32a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 584.000000 -278.000000) translate(0,16)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(240,255,255)" font-family="SimSun" font-size="15" graphid="g_2de0a80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 914.000000 -747.000000) translate(0,12)">2426</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(240,255,255)" font-family="SimSun" font-size="15" graphid="g_2b2fdf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 848.000000 -826.000000) translate(0,12)">24267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(240,255,255)" font-family="SimSun" font-size="15" graphid="g_2b30030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 843.000000 -658.000000) translate(0,12)">1820</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(240,255,255)" font-family="SimSun" font-size="15" graphid="g_3275420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 921.000000 -541.000000) translate(0,12)">1821</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(240,255,255)" font-family="SimSun" font-size="15" graphid="g_3275650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 854.000000 -498.000000) translate(0,12)">18210</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(240,255,255)" font-family="SimSun" font-size="15" graphid="g_2f39c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 916.000000 -471.000000) translate(0,12)">112</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(240,255,255)" font-family="SimSun" font-size="15" graphid="g_2f39ea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 855.000000 -446.000000) translate(0,12)">18220</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(240,255,255)" font-family="SimSun" font-size="15" graphid="g_2df27a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 922.000000 -401.000000) translate(0,12)">1822</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(240,255,255)" font-family="SimSun" font-size="19" graphid="g_2df29e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 944.000000 -274.000000) translate(0,16)">2号主变</text>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-217498">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 481.000000 -795.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33652" ObjectName="SW-CX_HTQ.CX_HTQ_24167SW"/>
     <cge:Meas_Ref ObjectId="217498"/>
    <cge:TPSR_Ref TObjectID="33652"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-217508">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 845.000000 -795.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33659" ObjectName="SW-CX_HTQ.CX_HTQ_24267SW"/>
     <cge:Meas_Ref ObjectId="217508"/>
    <cge:TPSR_Ref TObjectID="33659"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-217497">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 530.000000 -716.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33651" ObjectName="SW-CX_HTQ.CX_HTQ_2416SW"/>
     <cge:Meas_Ref ObjectId="217497"/>
    <cge:TPSR_Ref TObjectID="33651"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-217507">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 898.000000 -717.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33658" ObjectName="SW-CX_HTQ.CX_HTQ_2426SW"/>
     <cge:Meas_Ref ObjectId="217507"/>
    <cge:TPSR_Ref TObjectID="33658"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-217503">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 562.000000 -627.000000)" xlink:href="#switch2:shape4_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33657" ObjectName="SW-CX_HTQ.CX_HTQ_1810SW"/>
     <cge:Meas_Ref ObjectId="217503"/>
    <cge:TPSR_Ref TObjectID="33657"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-217513">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 836.000000 -627.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33664" ObjectName="SW-CX_HTQ.CX_HTQ_1820SW"/>
     <cge:Meas_Ref ObjectId="217513"/>
    <cge:TPSR_Ref TObjectID="33664"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-217501">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 507.000000 -514.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33655" ObjectName="SW-CX_HTQ.CX_HTQ_1811SW"/>
     <cge:Meas_Ref ObjectId="217501"/>
    <cge:TPSR_Ref TObjectID="33655"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-217511">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 875.000000 -513.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33662" ObjectName="SW-CX_HTQ.CX_HTQ_1821SW"/>
     <cge:Meas_Ref ObjectId="217511"/>
    <cge:TPSR_Ref TObjectID="33662"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-217499">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 507.000000 -371.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33653" ObjectName="SW-CX_HTQ.CX_HTQ_1812SW"/>
     <cge:Meas_Ref ObjectId="217499"/>
    <cge:TPSR_Ref TObjectID="33653"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-217509">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 875.000000 -372.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33660" ObjectName="SW-CX_HTQ.CX_HTQ_1822SW"/>
     <cge:Meas_Ref ObjectId="217509"/>
    <cge:TPSR_Ref TObjectID="33660"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-217502">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 491.000000 -498.000000)" xlink:href="#switch2:shape34_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33656" ObjectName="SW-CX_HTQ.CX_HTQ_18110SW"/>
     <cge:Meas_Ref ObjectId="217502"/>
    <cge:TPSR_Ref TObjectID="33656"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-217512">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 858.000000 -500.000000)" xlink:href="#switch2:shape34_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33663" ObjectName="SW-CX_HTQ.CX_HTQ_18210SW"/>
     <cge:Meas_Ref ObjectId="217512"/>
    <cge:TPSR_Ref TObjectID="33663"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-217500">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 490.000000 -416.000000)" xlink:href="#switch2:shape34_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33654" ObjectName="SW-CX_HTQ.CX_HTQ_18120SW"/>
     <cge:Meas_Ref ObjectId="217500"/>
    <cge:TPSR_Ref TObjectID="33654"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-217510">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 857.000000 -416.000000)" xlink:href="#switch2:shape34_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33661" ObjectName="SW-CX_HTQ.CX_HTQ_18220SW"/>
     <cge:Meas_Ref ObjectId="217510"/>
    <cge:TPSR_Ref TObjectID="33661"/></metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2df3600">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 399.000000 -523.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e39080">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1033.000000 -523.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_30932c0">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 562.000000 -680.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f44c50">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 931.000000 -680.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-217483" prefix="P  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 302.000000 -462.000000) translate(0,12)">P   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217483" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="33649"/>
     <cge:Term_Ref ObjectID="49289"/>
    <cge:TPSR_Ref TObjectID="33649"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-217484" prefix="Q " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 302.000000 -462.000000) translate(0,27)">Q  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217484" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="33649"/>
     <cge:Term_Ref ObjectID="49289"/>
    <cge:TPSR_Ref TObjectID="33649"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-217476" prefix="Ia  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 302.000000 -462.000000) translate(0,42)">Ia   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217476" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="33649"/>
     <cge:Term_Ref ObjectID="49289"/>
    <cge:TPSR_Ref TObjectID="33649"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-217479" prefix="Uab " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 302.000000 -462.000000) translate(0,57)">Uab  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217479" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="33649"/>
     <cge:Term_Ref ObjectID="49289"/>
    <cge:TPSR_Ref TObjectID="33649"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ubc" PreSymbol="0" appendix="" decimal="2" id="ME-217480" prefix="Ubc " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 302.000000 -462.000000) translate(0,72)">Ubc  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217480" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="33649"/>
     <cge:Term_Ref ObjectID="49289"/>
    <cge:TPSR_Ref TObjectID="33649"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Uca" PreSymbol="0" appendix="" decimal="2" id="ME-217481" prefix="Uca " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 302.000000 -462.000000) translate(0,87)">Uca  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217481" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="33649"/>
     <cge:Term_Ref ObjectID="49289"/>
    <cge:TPSR_Ref TObjectID="33649"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-217482" prefix="Hz " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 302.000000 -462.000000) translate(0,102)">Hz  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217482" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="33649"/>
     <cge:Term_Ref ObjectID="49289"/>
    <cge:TPSR_Ref TObjectID="33649"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-217493" prefix="P  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1032.000000 -485.000000) translate(0,12)">P   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217493" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="33650"/>
     <cge:Term_Ref ObjectID="49291"/>
    <cge:TPSR_Ref TObjectID="33650"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-217494" prefix="Q " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1032.000000 -485.000000) translate(0,27)">Q  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217494" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="33650"/>
     <cge:Term_Ref ObjectID="49291"/>
    <cge:TPSR_Ref TObjectID="33650"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-217486" prefix="Ia  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1032.000000 -485.000000) translate(0,42)">Ia   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217486" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="33650"/>
     <cge:Term_Ref ObjectID="49291"/>
    <cge:TPSR_Ref TObjectID="33650"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-217489" prefix="Uab " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1032.000000 -485.000000) translate(0,57)">Uab  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217489" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="33650"/>
     <cge:Term_Ref ObjectID="49291"/>
    <cge:TPSR_Ref TObjectID="33650"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ubc" PreSymbol="0" appendix="" decimal="2" id="ME-217490" prefix="Ubc " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1032.000000 -485.000000) translate(0,72)">Ubc  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217490" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="33650"/>
     <cge:Term_Ref ObjectID="49291"/>
    <cge:TPSR_Ref TObjectID="33650"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Uca" PreSymbol="0" appendix="" decimal="2" id="ME-217491" prefix="Uca " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1032.000000 -485.000000) translate(0,87)">Uca  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217491" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="33650"/>
     <cge:Term_Ref ObjectID="49291"/>
    <cge:TPSR_Ref TObjectID="33650"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-217492" prefix="Hz " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1032.000000 -485.000000) translate(0,102)">Hz  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217492" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="33650"/>
     <cge:Term_Ref ObjectID="49291"/>
    <cge:TPSR_Ref TObjectID="33650"/></metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_客户变.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="65" y="-961"/></g>
   <g href="cx_索引_接线图_客户变.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="16" y="-978"/></g>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="-53" y="-862"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="-52" y="-982"/>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 53.000000 -902.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-226245" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 82.000000 -773.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="226245" ObjectName="CX_HTQ:CX_HTQ_sumP"/>
    </metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_HTQ.CX_HTQ_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="49287"/>
     </metadata>
     <use class="BV-220KV" transform="matrix(2.653846 -0.000000 0.000000 -2.615385 873.000000 -199.000000)" xlink:href="#transformer2:shape11_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(2.653846 -0.000000 0.000000 -2.615385 873.000000 -199.000000)" xlink:href="#transformer2:shape11_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="33647" ObjectName="TF-CX_HTQ.CX_HTQ_2T"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_HTQ.CX_HTQ_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="49283"/>
     </metadata>
     <use class="BV-220KV" transform="matrix(2.653846 -0.000000 0.000000 -2.615385 505.000000 -198.000000)" xlink:href="#transformer2:shape11_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(2.653846 -0.000000 0.000000 -2.615385 505.000000 -198.000000)" xlink:href="#transformer2:shape11_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="33646" ObjectName="TF-CX_HTQ.CX_HTQ_1T"/>
    </metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="65" y="-961"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="65" y="-961"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="16" y="-978"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="16" y="-978"/></g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_HTQ"/>
</svg>