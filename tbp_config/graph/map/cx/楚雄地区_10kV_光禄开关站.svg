<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-293" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="3114 -1231 2171 1234">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape39">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.278409" x1="49" x2="49" y1="6" y2="9"/>
    <rect height="8" stroke-width="0.75" width="18" x="11" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="24" x2="22" y1="7" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="22" x2="24" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="4" x2="24" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.351519" x1="29" x2="43" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.351519" x1="43" x2="43" y1="0" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.351519" x1="46" x2="46" y1="4" y2="10"/>
   </symbol>
   <symbol id="lightningRod:shape141">
    <polyline DF8003:Layer="PUBLIC" points="18,1 18,16 30,8 18,1 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="4" x2="84" y1="9" y2="9"/>
    <polyline DF8003:Layer="PUBLIC" points="72,1 72,16 60,8 72,1 "/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="8" x2="8" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape132">
    <rect height="16" stroke-width="1" width="31" x="5" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="36" y1="9" y2="9"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape8_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="18" y2="43"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="18" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
   </symbol>
   <symbol id="switch2:shape8_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="44" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
   </symbol>
   <symbol id="switch2:shape8-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="18" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="18" y2="43"/>
   </symbol>
   <symbol id="switch2:shape8-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="44" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="transformer2:shape35_0">
    <circle cx="16" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,57 40,57 40,26 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="42" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="42" x2="38" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="16" y1="51" y2="26"/>
    <polyline DF8003:Layer="PUBLIC" points="16,14 10,26 22,26 16,14 16,15 16,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="57" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="56" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="56" y2="51"/>
   </symbol>
   <symbol id="transformer2:shape35_1">
    <circle cx="16" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="82" y2="87"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="21" y1="81" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="11" y1="81" y2="76"/>
   </symbol>
   <symbol id="voltageTransformer:shape80">
    <ellipse cx="8" cy="16" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <rect height="24" stroke-width="0.379884" width="14" x="1" y="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="67" y2="23"/>
    <ellipse cx="8" cy="8" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
   </symbol>
   <symbol id="voltageTransformer:shape125">
    <circle cx="8" cy="22" fillStyle="0" r="7" stroke-width="0.492782"/>
    <polyline points="51,14 51,8 " stroke-width="1"/>
    <polyline points="51,22 51,14 " stroke-width="1"/>
    <polyline points="51,14 26,14 " stroke-width="1"/>
    <polyline points="26,31 47,31 51,31 51,22 " stroke-width="1"/>
    <polyline points="51,22 39,22 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.16541" x1="56" x2="46" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.07362" x1="53" x2="49" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.16541" x1="54" x2="48" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="20" x2="24" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="32" x2="36" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="20" x2="24" y1="16" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="32" x2="32" y1="21" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="36" x2="32" y1="24" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="28" x2="32" y1="24" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="16" x2="20" y1="19" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="24" x2="20" y1="19" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="20" x2="20" y1="16" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="24" x2="20" y1="31" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="20" x2="20" y1="28" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="16" x2="20" y1="31" y2="28"/>
    <circle cx="32" cy="22" fillStyle="0" r="7" stroke-width="0.492782"/>
    <circle cx="20" cy="17" fillStyle="0" r="7" stroke-width="0.492782"/>
    <circle cx="20" cy="27" fillStyle="0" r="7" stroke-width="0.492782"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="9" x2="11" y1="24" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="7" x2="5" y1="24" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="5" x2="11" y1="20" y2="20"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_298e5e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2959090" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_29bb830" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2965e00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2619b10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2a4de60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2996f50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a55a60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29604e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="55" stroke="rgb(255,0,0)" stroke-width="9.28571" width="98" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a20790" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 52.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a20790" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 52.000000) translate(0,35)">二种工作</text>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a39f80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a39f80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_29427c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_261f800" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2633a10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2964960" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2965640" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">禁止操作</text>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2951aa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_296f100" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a3ca20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2971920" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_261a4e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a32c70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a5d720" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="78" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2a1be60" transform="matrix(1.246377 -0.000000 0.000000 -1.035714 2.739130 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2989400" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">测试</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2631320" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2995f40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29679c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_295a0c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2a4ff30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1244" width="2181" x="3109" y="-1236"/>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2076d60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3470.000000 741.000000) translate(0,12)">Ua（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b9a2b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3476.500000 695.000000) translate(0,12)">U0（V）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b9a710" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3462.000000 681.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b9aba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3470.000000 710.000000) translate(0,12)">Uc（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b9b0c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3470.000000 725.000000) translate(0,12)">Ub（kV）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b9b520" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3454.000000 65.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b9c750" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3443.000000 50.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2aeef20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3468.000000 35.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2aef860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5144.000000 744.000000) translate(0,12)">Ua（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2aefb10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5150.500000 698.000000) translate(0,12)">U0（V）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2aefd50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5136.000000 684.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2aeff90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5144.000000 713.000000) translate(0,12)">Uc（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2af01d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5144.000000 728.000000) translate(0,12)">Ub（kV）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2af1720" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3954.000000 765.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2af1970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3943.000000 750.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2af1b40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3968.000000 735.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2af1f60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4786.000000 768.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2af2220" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4775.000000 753.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2af2460" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4800.000000 738.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b68a00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4313.000000 445.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b68cc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4302.000000 430.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b68f00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4327.000000 415.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b698b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3718.000000 65.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b69b00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3707.000000 50.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b69d40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3732.000000 35.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b6ad10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4815.000000 74.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b6afa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4804.000000 59.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b6b1b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4829.000000 44.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b6b5d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5033.000000 74.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b6b890" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5022.000000 59.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b6bad0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5047.000000 44.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-250681">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3875.000000 -724.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41871" ObjectName="SW-CX_GLK.CX_GLK_042BK"/>
     <cge:Meas_Ref ObjectId="250681"/>
    <cge:TPSR_Ref TObjectID="41871"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-250861">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4306.000000 -532.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41899" ObjectName="SW-CX_GLK.CX_GLK_012BK"/>
     <cge:Meas_Ref ObjectId="250861"/>
    <cge:TPSR_Ref TObjectID="41899"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-250771">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3772.000000 -533.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41884" ObjectName="SW-CX_GLK.CX_GLK_044BK"/>
     <cge:Meas_Ref ObjectId="250771"/>
    <cge:TPSR_Ref TObjectID="41884"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-250741">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3547.000000 -531.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41879" ObjectName="SW-CX_GLK.CX_GLK_043BK"/>
     <cge:Meas_Ref ObjectId="250741"/>
    <cge:TPSR_Ref TObjectID="41879"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-250801">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4890.000000 -536.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41889" ObjectName="SW-CX_GLK.CX_GLK_053BK"/>
     <cge:Meas_Ref ObjectId="250801"/>
    <cge:TPSR_Ref TObjectID="41889"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-250831">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5082.000000 -536.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41894" ObjectName="SW-CX_GLK.CX_GLK_054BK"/>
     <cge:Meas_Ref ObjectId="250831"/>
    <cge:TPSR_Ref TObjectID="41894"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-250711">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4714.000000 -727.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41875" ObjectName="SW-CX_GLK.CX_GLK_055BK"/>
     <cge:Meas_Ref ObjectId="250711"/>
    <cge:TPSR_Ref TObjectID="41875"/></metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2a49440">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3836.000000 -940.000000)" xlink:href="#voltageTransformer:shape80"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_263a020">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4675.000000 -942.000000)" xlink:href="#voltageTransformer:shape80"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2980730">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3985.000000 -225.000000)" xlink:href="#voltageTransformer:shape125"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a15c80">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4714.000000 -219.000000)" xlink:href="#voltageTransformer:shape125"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3410.000000 -101.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3546.000000 -107.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3771.000000 -106.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4889.000000 -109.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5081.000000 -107.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5246.000000 -111.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2971450">
    <use class="BV-0KV" transform="matrix(0.000000 -1.326531 -1.214286 -0.000000 3941.000000 -871.673469)" xlink:href="#lightningRod:shape39"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a4c430">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3892.682803 -942.000000)" xlink:href="#lightningRod:shape141"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_210c430">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3935.682803 -1175.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_290b6d0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.326531 -1.214286 -0.000000 4780.000000 -873.673469)" xlink:href="#lightningRod:shape39"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_290ae10">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4776.000000 -1175.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_216ebe0">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4732.000000 -944.000000)" xlink:href="#lightningRod:shape141"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_215c380">
    <use class="BV-10KV" transform="matrix(-0.000000 1.326531 1.214286 0.000000 3498.000000 -435.326531)" xlink:href="#lightningRod:shape39"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_268f4c0">
    <use class="BV-10KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3547.317197 -365.000000)" xlink:href="#lightningRod:shape141"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ac9aa0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 3503.317197 -134.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26332d0">
    <use class="BV-10KV" transform="matrix(-0.000000 1.326531 1.214286 0.000000 3723.000000 -434.326531)" xlink:href="#lightningRod:shape39"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2985860">
    <use class="BV-10KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3772.317197 -364.000000)" xlink:href="#lightningRod:shape141"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2612e90">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 3728.317197 -133.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_294c400">
    <use class="BV-10KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3996.500000 -573.500000)" xlink:href="#lightningRod:shape132"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2969190">
    <use class="BV-10KV" transform="matrix(-0.000000 1.326531 1.214286 0.000000 4044.000000 -567.326531)" xlink:href="#lightningRod:shape39"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2962200">
    <use class="BV-10KV" transform="matrix(-0.000000 1.326531 1.214286 0.000000 4773.000000 -566.326531)" xlink:href="#lightningRod:shape39"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_297acc0">
    <use class="BV-10KV" transform="matrix(-0.000000 1.326531 1.214286 0.000000 4841.000000 -437.326531)" xlink:href="#lightningRod:shape39"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a2fb10">
    <use class="BV-10KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4890.317197 -367.000000)" xlink:href="#lightningRod:shape141"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a3b360">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4846.317197 -136.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2989b10">
    <use class="BV-10KV" transform="matrix(-0.000000 1.326531 1.214286 0.000000 5033.000000 -435.326531)" xlink:href="#lightningRod:shape39"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a36760">
    <use class="BV-10KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 5082.317197 -365.000000)" xlink:href="#lightningRod:shape141"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a4a480">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 5038.317197 -134.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20e5750">
    <use class="BV-10KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4189.500000 -574.500000)" xlink:href="#lightningRod:shape132"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2073f60">
    <use class="BV-10KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4568.500000 -574.500000)" xlink:href="#lightningRod:shape132"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2074860">
    <use class="BV-10KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4725.500000 -564.500000)" xlink:href="#lightningRod:shape132"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3225.500000 -1117.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-250606" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3509.000000 -65.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="250606" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41879"/>
     <cge:Term_Ref ObjectID="15772"/>
    <cge:TPSR_Ref TObjectID="41879"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-250607" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3509.000000 -65.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="250607" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41879"/>
     <cge:Term_Ref ObjectID="15772"/>
    <cge:TPSR_Ref TObjectID="41879"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-250603" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3509.000000 -65.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="250603" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41879"/>
     <cge:Term_Ref ObjectID="15772"/>
    <cge:TPSR_Ref TObjectID="41879"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-250638" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3552.000000 -739.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="250638" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41868"/>
     <cge:Term_Ref ObjectID="15752"/>
    <cge:TPSR_Ref TObjectID="41868"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-250639" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3552.000000 -739.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="250639" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41868"/>
     <cge:Term_Ref ObjectID="15752"/>
    <cge:TPSR_Ref TObjectID="41868"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-250640" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3552.000000 -739.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="250640" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41868"/>
     <cge:Term_Ref ObjectID="15752"/>
    <cge:TPSR_Ref TObjectID="41868"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-250644" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3552.000000 -739.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="250644" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41868"/>
     <cge:Term_Ref ObjectID="15752"/>
    <cge:TPSR_Ref TObjectID="41868"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-250641" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3552.000000 -739.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="250641" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41868"/>
     <cge:Term_Ref ObjectID="15752"/>
    <cge:TPSR_Ref TObjectID="41868"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-250646" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5226.000000 -741.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="250646" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41869"/>
     <cge:Term_Ref ObjectID="15753"/>
    <cge:TPSR_Ref TObjectID="41869"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-250647" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5226.000000 -741.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="250647" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41869"/>
     <cge:Term_Ref ObjectID="15753"/>
    <cge:TPSR_Ref TObjectID="41869"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-250648" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5226.000000 -741.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="250648" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41869"/>
     <cge:Term_Ref ObjectID="15753"/>
    <cge:TPSR_Ref TObjectID="41869"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-250652" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5226.000000 -741.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="250652" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41869"/>
     <cge:Term_Ref ObjectID="15753"/>
    <cge:TPSR_Ref TObjectID="41869"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-250649" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5226.000000 -741.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="250649" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41869"/>
     <cge:Term_Ref ObjectID="15753"/>
    <cge:TPSR_Ref TObjectID="41869"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-250592" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4010.000000 -765.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="250592" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41871"/>
     <cge:Term_Ref ObjectID="15756"/>
    <cge:TPSR_Ref TObjectID="41871"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-250593" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4010.000000 -765.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="250593" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41871"/>
     <cge:Term_Ref ObjectID="15756"/>
    <cge:TPSR_Ref TObjectID="41871"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-250589" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4010.000000 -765.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="250589" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41871"/>
     <cge:Term_Ref ObjectID="15756"/>
    <cge:TPSR_Ref TObjectID="41871"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-250599" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4840.000000 -767.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="250599" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41875"/>
     <cge:Term_Ref ObjectID="15764"/>
    <cge:TPSR_Ref TObjectID="41875"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-250600" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4840.000000 -767.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="250600" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41875"/>
     <cge:Term_Ref ObjectID="15764"/>
    <cge:TPSR_Ref TObjectID="41875"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-250596" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4840.000000 -767.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="250596" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41875"/>
     <cge:Term_Ref ObjectID="15764"/>
    <cge:TPSR_Ref TObjectID="41875"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-250634" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4369.000000 -443.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="250634" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41899"/>
     <cge:Term_Ref ObjectID="16197"/>
    <cge:TPSR_Ref TObjectID="41899"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-250635" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4369.000000 -443.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="250635" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41899"/>
     <cge:Term_Ref ObjectID="16197"/>
    <cge:TPSR_Ref TObjectID="41899"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-250631" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4369.000000 -443.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="250631" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41899"/>
     <cge:Term_Ref ObjectID="16197"/>
    <cge:TPSR_Ref TObjectID="41899"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-250613" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3778.000000 -67.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="250613" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41884"/>
     <cge:Term_Ref ObjectID="16167"/>
    <cge:TPSR_Ref TObjectID="41884"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-250614" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3778.000000 -67.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="250614" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41884"/>
     <cge:Term_Ref ObjectID="16167"/>
    <cge:TPSR_Ref TObjectID="41884"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-250610" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3778.000000 -67.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="250610" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41884"/>
     <cge:Term_Ref ObjectID="16167"/>
    <cge:TPSR_Ref TObjectID="41884"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-250620" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4873.000000 -73.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="250620" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41889"/>
     <cge:Term_Ref ObjectID="16177"/>
    <cge:TPSR_Ref TObjectID="41889"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-250621" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4873.000000 -73.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="250621" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41889"/>
     <cge:Term_Ref ObjectID="16177"/>
    <cge:TPSR_Ref TObjectID="41889"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-250617" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4873.000000 -73.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="250617" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41889"/>
     <cge:Term_Ref ObjectID="16177"/>
    <cge:TPSR_Ref TObjectID="41889"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-250627" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5091.000000 -75.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="250627" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41894"/>
     <cge:Term_Ref ObjectID="16187"/>
    <cge:TPSR_Ref TObjectID="41894"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-250628" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5091.000000 -75.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="250628" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41894"/>
     <cge:Term_Ref ObjectID="16187"/>
    <cge:TPSR_Ref TObjectID="41894"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-250624" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5091.000000 -75.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="250624" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41894"/>
     <cge:Term_Ref ObjectID="16187"/>
    <cge:TPSR_Ref TObjectID="41894"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="139" x="3237" y="-1176"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="139" x="3237" y="-1176"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3189" y="-1193"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3189" y="-1193"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="3901" y="-753"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="3901" y="-753"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="3565" y="-560"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="3565" y="-560"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="3790" y="-562"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="3790" y="-562"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4735" y="-756"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4735" y="-756"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="4324" y="-561"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="4324" y="-561"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4908" y="-565"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4908" y="-565"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="5100" y="-565"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="5100" y="-565"/></g>
  </g><g id="MotifButton_Layer">
   <g href="cx_配调_配网接线图开闭所.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="139" x="3237" y="-1176"/></g>
   <g href="cx_配调_配网接线图开闭所.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3189" y="-1193"/></g>
   <g href="10kV光禄开关站CX_GLK_042间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="3901" y="-753"/></g>
   <g href="10kV光禄开关站CX_GLK_043间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="3565" y="-560"/></g>
   <g href="10kV光禄开关站CX_GLK_044间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="3790" y="-562"/></g>
   <g href="10kV光禄开关站CX_GLK_055间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4735" y="-756"/></g>
   <g href="10kV光禄开关站CX_GLK_012间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="4324" y="-561"/></g>
   <g href="10kV光禄开关站CX_GLK_053间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4908" y="-565"/></g>
   <g href="10kV光禄开关站CX_GLK_054间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="5100" y="-565"/></g>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3115" y="-1077"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3116" y="-1197"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3115" y="-597"/>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_GLK.CX_GLK_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3407,-652 4352,-652 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="41868" ObjectName="BS-CX_GLK.CX_GLK_9IM"/>
    <cge:TPSR_Ref TObjectID="41868"/></metadata>
   <polyline fill="none" opacity="0" points="3407,-652 4352,-652 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_GLK.CX_GLK_9IIM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4416,-651 5285,-651 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="41869" ObjectName="BS-CX_GLK.CX_GLK_9IIM"/>
    <cge:TPSR_Ref TObjectID="41869"/></metadata>
   <polyline fill="none" opacity="0" points="4416,-651 5285,-651 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Link_Layer">
   <g class="BV-0KV" id="g_295d4c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3828,-873 3828,-853 3933,-853 3933,-879 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_2a49440@0" ObjectIDZND0="g_2971450@0" Pin0InfoVect0LinkObjId="g_2971450_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a49440_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3828,-873 3828,-853 3933,-853 3933,-879 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_296da00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3884,-1026 3884,-1052 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2a4c430@0" ObjectIDZND0="41872@1" Pin0InfoVect0LinkObjId="SW-250682_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a4c430_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3884,-1026 3884,-1052 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_295ed30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3884,-1088 3884,-1101 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="41872@0" ObjectIDZND0="g_210c430@0" Pin0InfoVect0LinkObjId="g_210c430_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-250682_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3884,-1088 3884,-1101 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a4e880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3884,-1101 3884,-1173 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" ObjectIDND0="41872@x" ObjectIDND1="g_210c430@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-250682_0" Pin1InfoVect1LinkObjId="g_210c430_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3884,-1101 3884,-1173 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21b4340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3929,-1121 3929,-1101 3884,-1101 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_210c430@0" ObjectIDZND0="41872@x" Pin0InfoVect0LinkObjId="SW-250682_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_210c430_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3929,-1121 3929,-1101 3884,-1101 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_20b5000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4667,-875 4667,-855 4772,-855 4772,-881 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_263a020@0" ObjectIDZND0="g_290b6d0@0" Pin0InfoVect0LinkObjId="g_290b6d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_263a020_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4667,-875 4667,-855 4772,-855 4772,-881 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2647930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4723,-1028 4723,-1054 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_216ebe0@0" ObjectIDZND0="41876@1" Pin0InfoVect0LinkObjId="SW-250712_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_216ebe0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4723,-1028 4723,-1054 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21a50e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4724,-1088 4724,-1101 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="41876@0" ObjectIDZND0="g_290ae10@0" Pin0InfoVect0LinkObjId="g_290ae10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-250712_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4724,-1088 4724,-1101 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_203a1b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4724,-1101 4724,-1175 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" ObjectIDND0="41876@x" ObjectIDND1="g_290ae10@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-250712_0" Pin1InfoVect1LinkObjId="g_290ae10_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4724,-1101 4724,-1175 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2060d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4769,-1121 4769,-1101 4724,-1101 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_290ae10@0" ObjectIDZND0="41876@x" Pin0InfoVect0LinkObjId="SW-250712_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_290ae10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4769,-1121 4769,-1101 4724,-1101 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_290a220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3611,-434 3611,-454 3506,-454 3506,-428 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="41881@0" ObjectIDZND0="g_215c380@0" Pin0InfoVect0LinkObjId="g_215c380_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-250743_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3611,-434 3611,-454 3506,-454 3506,-428 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29650a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3556,-281 3556,-255 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_268f4c0@0" ObjectIDZND0="41880@1" Pin0InfoVect0LinkObjId="SW-250742_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_268f4c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3556,-281 3556,-255 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2968da0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3555,-221 3555,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="41880@0" ObjectIDZND0="g_2ac9aa0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_2ac9aa0_0" Pin0InfoVect1LinkObjId="g_2a49440_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-250742_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3555,-221 3555,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_217d040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3555,-208 3555,-134 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="41880@x" ObjectIDND1="g_2ac9aa0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_2a49440_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-250742_0" Pin1InfoVect1LinkObjId="g_2ac9aa0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3555,-208 3555,-134 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2963d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3510,-188 3510,-208 3555,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_2ac9aa0@0" ObjectIDZND0="41880@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-250742_0" Pin0InfoVect1LinkObjId="g_2a49440_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ac9aa0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3510,-188 3510,-208 3555,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_294a3e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3836,-433 3836,-453 3731,-453 3731,-427 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="41886@0" ObjectIDZND0="g_26332d0@0" Pin0InfoVect0LinkObjId="g_26332d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-250773_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3836,-433 3836,-453 3731,-453 3731,-427 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29939b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3781,-280 3781,-254 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2985860@0" ObjectIDZND0="41885@1" Pin0InfoVect0LinkObjId="SW-250772_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2985860_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3781,-280 3781,-254 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_295c400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3780,-220 3780,-207 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="41885@0" ObjectIDZND0="g_2612e90@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_2612e90_0" Pin0InfoVect1LinkObjId="g_2a49440_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-250772_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3780,-220 3780,-207 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21f1980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3780,-207 3780,-133 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="41885@x" ObjectIDND1="g_2612e90@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_2a49440_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-250772_0" Pin1InfoVect1LinkObjId="g_2612e90_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3780,-207 3780,-133 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21f14c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3735,-187 3735,-207 3780,-207 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_2612e90@0" ObjectIDZND0="41885@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-250772_0" Pin0InfoVect1LinkObjId="g_2a49440_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2612e90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3735,-187 3735,-207 3780,-207 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_298d4e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4052,-560 4052,-580 4005,-580 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2969190@0" ObjectIDZND0="g_294c400@0" ObjectIDZND1="41904@x" Pin0InfoVect0LinkObjId="g_294c400_0" Pin0InfoVect1LinkObjId="SW-250932_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2969190_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4052,-560 4052,-580 4005,-580 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2977d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4198,-305 4198,-244 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" ObjectIDND0="0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a49440_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4198,-305 4198,-244 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2961fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4577,-305 4577,-244 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" ObjectIDND0="0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a49440_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4577,-305 4577,-244 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29b8040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4781,-559 4781,-579 4735,-579 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_2962200@0" ObjectIDZND0="41906@x" ObjectIDZND1="g_2074860@0" Pin0InfoVect0LinkObjId="SW-250940_0" Pin0InfoVect1LinkObjId="g_2074860_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2962200_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4781,-559 4781,-579 4735,-579 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_269aff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4954,-436 4954,-456 4849,-456 4849,-430 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="41891@0" ObjectIDZND0="g_297acc0@0" Pin0InfoVect0LinkObjId="g_297acc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-250803_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4954,-436 4954,-456 4849,-456 4849,-430 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_269b240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4899,-283 4899,-257 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2a2fb10@0" ObjectIDZND0="41890@1" Pin0InfoVect0LinkObjId="SW-250802_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a2fb10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4899,-283 4899,-257 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a3aea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4898,-223 4898,-210 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="41890@0" ObjectIDZND0="g_2a3b360@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_2a3b360_0" Pin0InfoVect1LinkObjId="g_2a49440_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-250802_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4898,-223 4898,-210 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a3b100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4898,-210 4898,-136 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="41890@x" ObjectIDND1="g_2a3b360@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_2a49440_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-250802_0" Pin1InfoVect1LinkObjId="g_2a3b360_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4898,-210 4898,-136 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29bb200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4853,-190 4853,-210 4898,-210 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_2a3b360@0" ObjectIDZND0="41890@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-250802_0" Pin0InfoVect1LinkObjId="g_2a49440_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a3b360_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4853,-190 4853,-210 4898,-210 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a362b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5146,-434 5146,-454 5041,-454 5041,-428 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="41896@0" ObjectIDZND0="g_2989b10@0" Pin0InfoVect0LinkObjId="g_2989b10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-250833_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5146,-434 5146,-454 5041,-454 5041,-428 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a36500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5091,-281 5091,-255 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2a36760@0" ObjectIDZND0="41895@1" Pin0InfoVect0LinkObjId="SW-250832_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a36760_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5091,-281 5091,-255 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a49fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5090,-221 5090,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="41895@0" ObjectIDZND0="g_2a4a480@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_2a4a480_0" Pin0InfoVect1LinkObjId="g_2a49440_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-250832_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5090,-221 5090,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a4a220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5090,-208 5090,-134 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="41895@x" ObjectIDND1="g_2a4a480@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_2a49440_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-250832_0" Pin1InfoVect1LinkObjId="g_2a4a480_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5090,-208 5090,-134 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2987a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5045,-188 5045,-208 5090,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_2a4a480@0" ObjectIDZND0="41895@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-250832_0" Pin0InfoVect1LinkObjId="g_2a49440_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a4a480_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5045,-188 5045,-208 5090,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a4d960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5255,-651 5255,-138 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="load" ObjectIDND0="41869@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_2a49440_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20e1d70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5255,-651 5255,-138 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29556e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3419,-652 3419,-128 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="load" ObjectIDND0="41868@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_2a49440_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21f7180_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3419,-652 3419,-128 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e5b850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3884,-812 3884,-947 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="41874@0" ObjectIDZND0="g_2a4c430@1" Pin0InfoVect0LinkObjId="g_2a4c430_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-250683_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3884,-812 3884,-947 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e5bab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3884,-652 3884,-683 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="41868@0" ObjectIDZND0="41873@0" Pin0InfoVect0LinkObjId="SW-250683_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21f7180_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3884,-652 3884,-683 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29842a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3884,-765 3884,-795 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="41871@1" ObjectIDZND0="41874@1" Pin0InfoVect0LinkObjId="SW-250683_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-250681_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3884,-765 3884,-795 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2057ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3556,-490 3556,-360 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="41883@0" ObjectIDZND0="g_268f4c0@1" Pin0InfoVect0LinkObjId="g_268f4c0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-250744_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3556,-490 3556,-360 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2058140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3556,-539 3556,-507 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="41879@0" ObjectIDZND0="41883@1" Pin0InfoVect0LinkObjId="SW-250744_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-250741_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3556,-539 3556,-507 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20583a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3556,-651 3556,-619 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="41868@0" ObjectIDZND0="41882@0" Pin0InfoVect0LinkObjId="SW-250744_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21f7180_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3556,-651 3556,-619 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2058600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3556,-602 3556,-566 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="41882@1" ObjectIDZND0="41879@1" Pin0InfoVect0LinkObjId="SW-250741_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-250744_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3556,-602 3556,-566 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2058860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3781,-492 3781,-359 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="41888@0" ObjectIDZND0="g_2985860@1" Pin0InfoVect0LinkObjId="g_2985860_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-250774_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3781,-492 3781,-359 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2054cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3781,-541 3781,-509 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="41884@0" ObjectIDZND0="41888@1" Pin0InfoVect0LinkObjId="SW-250774_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-250771_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3781,-541 3781,-509 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2054f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3781,-652 3781,-621 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="41868@0" ObjectIDZND0="41887@0" Pin0InfoVect0LinkObjId="SW-250774_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21f7180_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3781,-652 3781,-621 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2055190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3781,-604 3781,-568 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="41887@1" ObjectIDZND0="41884@1" Pin0InfoVect0LinkObjId="SW-250771_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-250774_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3781,-604 3781,-568 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20553f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4198,-493 4198,-357 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="41909@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_2a49440_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-251005_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4198,-493 4198,-357 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2055650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4198,-542 4198,-510 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_20e5750@1" ObjectIDZND0="41909@1" Pin0InfoVect0LinkObjId="SW-251005_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20e5750_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4198,-542 4198,-510 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20558b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4198,-651 4198,-622 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="41868@0" ObjectIDZND0="41908@0" Pin0InfoVect0LinkObjId="SW-251005_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21f7180_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4198,-651 4198,-622 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2055b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4198,-605 4198,-569 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="41908@1" ObjectIDZND0="g_20e5750@0" Pin0InfoVect0LinkObjId="g_20e5750_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-251005_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4198,-605 4198,-569 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2056170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4315,-540 4315,-508 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="41899@0" ObjectIDZND0="41901@1" Pin0InfoVect0LinkObjId="SW-250862_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-250861_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4315,-540 4315,-508 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20563d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4315,-651 4315,-620 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="41868@0" ObjectIDZND0="41900@0" Pin0InfoVect0LinkObjId="SW-250862_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21f7180_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4315,-651 4315,-620 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2056630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4315,-603 4315,-567 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="41900@1" ObjectIDZND0="41899@1" Pin0InfoVect0LinkObjId="SW-250861_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-250862_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4315,-603 4315,-567 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2056890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4577,-493 4577,-357 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="41911@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_2a49440_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-251006_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4577,-493 4577,-357 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20ebe70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4577,-542 4577,-510 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2073f60@1" ObjectIDZND0="41911@1" Pin0InfoVect0LinkObjId="SW-251006_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2073f60_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4577,-542 4577,-510 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20ec0d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4577,-651 4577,-622 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="41869@0" ObjectIDZND0="41910@0" Pin0InfoVect0LinkObjId="SW-251006_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20e1d70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4577,-651 4577,-622 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20ec330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4577,-605 4577,-569 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="41910@1" ObjectIDZND0="g_2073f60@0" Pin0InfoVect0LinkObjId="g_2073f60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-251006_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4577,-605 4577,-569 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20ec590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4899,-495 4899,-362 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="41893@0" ObjectIDZND0="g_2a2fb10@1" Pin0InfoVect0LinkObjId="g_2a2fb10_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-250804_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4899,-495 4899,-362 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20ec7f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4899,-544 4899,-512 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="41889@0" ObjectIDZND0="41893@1" Pin0InfoVect0LinkObjId="SW-250804_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-250801_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4899,-544 4899,-512 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20eca50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4899,-650 4899,-624 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="41869@0" ObjectIDZND0="41892@0" Pin0InfoVect0LinkObjId="SW-250804_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20e1d70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4899,-650 4899,-624 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20eccb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4899,-607 4899,-571 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="41892@1" ObjectIDZND0="41889@1" Pin0InfoVect0LinkObjId="SW-250801_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-250804_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4899,-607 4899,-571 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21ff780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4723,-652 4723,-686 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="41869@0" ObjectIDZND0="41877@0" Pin0InfoVect0LinkObjId="SW-250713_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20e1d70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4723,-652 4723,-686 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21ff9e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4723,-703 4723,-735 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="41877@1" ObjectIDZND0="41875@0" Pin0InfoVect0LinkObjId="SW-250711_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-250713_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4723,-703 4723,-735 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21ffc40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4723,-762 4723,-798 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="41875@1" ObjectIDZND0="41878@1" Pin0InfoVect0LinkObjId="SW-250713_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-250711_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4723,-762 4723,-798 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21ffea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4723,-815 4723,-949 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="41878@0" ObjectIDZND0="g_216ebe0@1" Pin0InfoVect0LinkObjId="g_216ebe0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-250713_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4723,-815 4723,-949 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2200100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5091,-495 5091,-360 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="41898@0" ObjectIDZND0="g_2a36760@1" Pin0InfoVect0LinkObjId="g_2a36760_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-250834_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5091,-495 5091,-360 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2200360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5091,-544 5091,-512 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="41894@0" ObjectIDZND0="41898@1" Pin0InfoVect0LinkObjId="SW-250834_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-250831_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5091,-544 5091,-512 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22005c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5091,-650 5091,-624 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="41869@0" ObjectIDZND0="41897@0" Pin0InfoVect0LinkObjId="SW-250834_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20e1d70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5091,-650 5091,-624 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21fa4f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5091,-607 5091,-571 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="41897@1" ObjectIDZND0="41894@1" Pin0InfoVect0LinkObjId="SW-250831_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-250834_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5091,-607 5091,-571 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21fafe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4005,-568 4005,-580 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_294c400@0" ObjectIDZND0="g_2969190@0" ObjectIDZND1="41904@x" Pin0InfoVect0LinkObjId="g_2969190_0" Pin0InfoVect1LinkObjId="SW-250932_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_294c400_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4005,-568 4005,-580 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21f6f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4005,-580 4005,-608 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_2969190@0" ObjectIDND1="g_294c400@0" ObjectIDZND0="41904@1" Pin0InfoVect0LinkObjId="SW-250932_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2969190_0" Pin1InfoVect1LinkObjId="g_294c400_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4005,-580 4005,-608 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21f7180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4005,-625 4005,-652 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="41904@0" ObjectIDZND0="41868@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-250932_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4005,-625 4005,-652 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_205ca10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4734,-579 4734,-602 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_2962200@0" ObjectIDND1="g_2074860@0" ObjectIDZND0="41906@1" Pin0InfoVect0LinkObjId="SW-250940_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2962200_0" Pin1InfoVect1LinkObjId="g_2074860_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4734,-579 4734,-602 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20e1d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4734,-619 4734,-651 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="41906@0" ObjectIDZND0="41869@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-250940_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4734,-619 4734,-651 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20e10b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4005,-260 4005,-492 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" ObjectIDND0="g_2980730@0" ObjectIDZND0="41905@0" Pin0InfoVect0LinkObjId="SW-250932_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2980730_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4005,-260 4005,-492 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20e1310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4005,-509 4005,-537 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="41905@1" ObjectIDZND0="g_294c400@1" Pin0InfoVect0LinkObjId="g_294c400_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-250932_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4005,-509 4005,-537 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_205d590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4734,-252 4734,-494 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" ObjectIDND0="g_2a15c80@0" ObjectIDZND0="41907@0" Pin0InfoVect0LinkObjId="SW-250940_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a15c80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4734,-252 4734,-494 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a320e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4315,-491 4315,-455 4457,-455 4457,-492 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="41901@0" ObjectIDZND0="41903@0" Pin0InfoVect0LinkObjId="SW-250863_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-250862_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4315,-491 4315,-455 4457,-455 4457,-492 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20e4a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4457,-651 4457,-622 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="41869@0" ObjectIDZND0="41902@0" Pin0InfoVect0LinkObjId="SW-250863_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20e1d70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4457,-651 4457,-622 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20e4cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4457,-610 4457,-509 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="41902@1" ObjectIDZND0="41903@1" Pin0InfoVect0LinkObjId="SW-250863_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-250863_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4457,-610 4457,-509 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20e54f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3884,-732 3884,-700 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="41871@0" ObjectIDZND0="41873@1" Pin0InfoVect0LinkObjId="SW-250683_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-250681_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3884,-732 3884,-700 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2075160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4734,-511 4734,-528 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="41907@1" ObjectIDZND0="g_2074860@1" Pin0InfoVect0LinkObjId="g_2074860_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-250940_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4734,-511 4734,-528 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20753c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4734,-559 4734,-579 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2074860@0" ObjectIDZND0="g_2962200@0" ObjectIDZND1="41906@x" Pin0InfoVect0LinkObjId="g_2962200_0" Pin0InfoVect1LinkObjId="SW-250940_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2074860_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4734,-559 4734,-579 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-247701" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3431.000000 -1083.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41616" ObjectName="DYN-CX_GLK"/>
     <cge:Meas_Ref ObjectId="247701"/>
    </metadata>
   </g>
  </g><g id="CircleFilled_Layer">
   <circle DF8003:Layer="PUBLIC" cx="3884" cy="-988" fill="none" fillStyle="0" r="7.5" stroke="rgb(0,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="4723" cy="-987" fill="none" fillStyle="0" r="7.5" stroke="rgb(0,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="3556" cy="-319" fill="none" fillStyle="0" r="7.5" stroke="rgb(0,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="3780" cy="-318" fill="none" fillStyle="0" r="7.5" stroke="rgb(0,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="4899" cy="-318" fill="none" fillStyle="0" r="7.5" stroke="rgb(0,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="5092" cy="-322" fill="none" fillStyle="0" r="7.5" stroke="rgb(0,255,0)" stroke-width="1"/>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4182.000000 -263.000000)" xlink:href="#transformer2:shape35_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4182.000000 -263.000000)" xlink:href="#transformer2:shape35_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4561.000000 -263.000000)" xlink:href="#transformer2:shape35_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4561.000000 -263.000000)" xlink:href="#transformer2:shape35_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21f2bf0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3971.000000 -220.000000) translate(0,15)">10kV I母TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ac9ca0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3504.000000 -95.000000) translate(0,15)">10kV福光线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_271aec0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4164.000000 -226.000000) translate(0,15)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e96af0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3402.000000 -687.000000) translate(0,15)">IM段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e94810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5074.000000 -692.000000) translate(0,15)">IIM段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2183440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3135.000000 -587.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2183440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3135.000000 -587.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2183440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3135.000000 -587.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2183440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3135.000000 -587.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2183440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3135.000000 -587.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2183440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3135.000000 -587.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2183440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3135.000000 -587.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2183440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3135.000000 -587.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2183440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3135.000000 -587.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2183440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3135.000000 -587.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2183440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3135.000000 -587.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2183440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3135.000000 -587.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2183440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3135.000000 -587.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2183440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3135.000000 -587.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2183440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3135.000000 -587.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2183440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3135.000000 -587.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2183440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3135.000000 -587.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2183440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3135.000000 -587.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1e02180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3134.000000 -1025.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1e02180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3134.000000 -1025.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1e02180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3134.000000 -1025.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1e02180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3134.000000 -1025.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1e02180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3134.000000 -1025.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1e02180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3134.000000 -1025.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1e02180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3134.000000 -1025.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_1e14c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3257.500000 -1165.500000) translate(0,16)">光禄开关站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e8f4b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4410.000000 -575.000000) translate(0,12)">0122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_295b4b0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3392.000000 -98.000000) translate(0,15)">预留1</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_295bc00" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3736.000000 -97.000000) translate(0,15)">10kV后营线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_295bec0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4694.000000 -212.000000) translate(0,15)">10kV II母TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_295c110" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4542.000000 -227.000000) translate(0,15)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a29920" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4857.000000 -97.000000) translate(0,15)">10kV白龙线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a29b60" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5239.000000 -100.000000) translate(0,15)">预留2</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a29d80" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5052.000000 -93.000000) translate(0,15)">10kV班刘线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a29fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3632.000000 -421.000000) translate(0,12)">04367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a2a240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3582.000000 -239.000000) translate(0,12)">0436</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a2a630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3850.000000 -426.000000) translate(0,12)">04467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a36a90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3809.000000 -233.000000) translate(0,12)">0446</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a36cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3954.000000 -562.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a37160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4144.000000 -560.000000) translate(0,12)">0461</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a373e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4596.000000 -564.000000) translate(0,12)">0512</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a37620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4673.000000 -552.000000) translate(0,12)">0902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a37860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4921.000000 -234.000000) translate(0,12)">0536</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a5c0f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4970.000000 -425.000000) translate(0,12)">05367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a5c330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5160.000000 -420.000000) translate(0,12)">05467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a5c570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5116.000000 -231.000000) translate(0,12)">0546</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a5c7b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4664.000000 -1081.000000) translate(0,12)">0556</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a5c9f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3820.000000 -1084.000000) translate(0,12)">0426</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a5cc30" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3847.000000 -1231.000000) translate(0,15)">10kV草海线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a2aa60" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4688.000000 -1225.000000) translate(0,15)">10kV工业园区II回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b6bd10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3901.000000 -753.000000) translate(0,12)">042</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b6c090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3565.000000 -560.000000) translate(0,12)">043</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b6c3a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3790.000000 -562.000000) translate(0,12)">044</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b6c5e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4324.000000 -561.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b6c820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4908.000000 -565.000000) translate(0,12)">053</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b6ca60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5100.000000 -565.000000) translate(0,12)">054</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b6cf20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4735.000000 -756.000000) translate(0,12)">055</text>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-250682">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3874.682803 -1047.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41872" ObjectName="SW-CX_GLK.CX_GLK_0426SW"/>
     <cge:Meas_Ref ObjectId="250682"/>
    <cge:TPSR_Ref TObjectID="41872"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-250712">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4715.000000 -1047.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41876" ObjectName="SW-CX_GLK.CX_GLK_0556SW"/>
     <cge:Meas_Ref ObjectId="250712"/>
    <cge:TPSR_Ref TObjectID="41876"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-250742">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3564.317197 -262.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41880" ObjectName="SW-CX_GLK.CX_GLK_0436SW"/>
     <cge:Meas_Ref ObjectId="250742"/>
    <cge:TPSR_Ref TObjectID="41880"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-250743">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3602.000000 -383.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41881" ObjectName="SW-CX_GLK.CX_GLK_04367SW"/>
     <cge:Meas_Ref ObjectId="250743"/>
    <cge:TPSR_Ref TObjectID="41881"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-250772">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3789.317197 -261.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41885" ObjectName="SW-CX_GLK.CX_GLK_0446SW"/>
     <cge:Meas_Ref ObjectId="250772"/>
    <cge:TPSR_Ref TObjectID="41885"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-250773">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3827.000000 -382.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41886" ObjectName="SW-CX_GLK.CX_GLK_04467SW"/>
     <cge:Meas_Ref ObjectId="250773"/>
    <cge:TPSR_Ref TObjectID="41886"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-250802">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4907.317197 -264.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41890" ObjectName="SW-CX_GLK.CX_GLK_0536SW"/>
     <cge:Meas_Ref ObjectId="250802"/>
    <cge:TPSR_Ref TObjectID="41890"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-250803">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4945.000000 -385.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41891" ObjectName="SW-CX_GLK.CX_GLK_05367SW"/>
     <cge:Meas_Ref ObjectId="250803"/>
    <cge:TPSR_Ref TObjectID="41891"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-250832">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 5099.317197 -262.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41895" ObjectName="SW-CX_GLK.CX_GLK_0546SW"/>
     <cge:Meas_Ref ObjectId="250832"/>
    <cge:TPSR_Ref TObjectID="41895"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-250833">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5137.000000 -383.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41896" ObjectName="SW-CX_GLK.CX_GLK_05467SW"/>
     <cge:Meas_Ref ObjectId="250833"/>
    <cge:TPSR_Ref TObjectID="41896"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-250683">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3874.000000 -788.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41874" ObjectName="SW-CX_GLK.CX_GLK_042XC1"/>
     <cge:Meas_Ref ObjectId="250683"/>
    <cge:TPSR_Ref TObjectID="41874"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-250683">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3874.000000 -676.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41873" ObjectName="SW-CX_GLK.CX_GLK_042XC"/>
     <cge:Meas_Ref ObjectId="250683"/>
    <cge:TPSR_Ref TObjectID="41873"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-250862">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4305.000000 -596.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41900" ObjectName="SW-CX_GLK.CX_GLK_012XC"/>
     <cge:Meas_Ref ObjectId="250862"/>
    <cge:TPSR_Ref TObjectID="41900"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-250862">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4305.000000 -484.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41901" ObjectName="SW-CX_GLK.CX_GLK_012XC1"/>
     <cge:Meas_Ref ObjectId="250862"/>
    <cge:TPSR_Ref TObjectID="41901"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-250774">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3771.000000 -597.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41887" ObjectName="SW-CX_GLK.CX_GLK_044XC"/>
     <cge:Meas_Ref ObjectId="250774"/>
    <cge:TPSR_Ref TObjectID="41887"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-250774">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3771.000000 -485.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41888" ObjectName="SW-CX_GLK.CX_GLK_044XC1"/>
     <cge:Meas_Ref ObjectId="250774"/>
    <cge:TPSR_Ref TObjectID="41888"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-250744">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3546.000000 -595.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41882" ObjectName="SW-CX_GLK.CX_GLK_043XC"/>
     <cge:Meas_Ref ObjectId="250744"/>
    <cge:TPSR_Ref TObjectID="41882"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-250744">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3546.000000 -483.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41883" ObjectName="SW-CX_GLK.CX_GLK_043XC1"/>
     <cge:Meas_Ref ObjectId="250744"/>
    <cge:TPSR_Ref TObjectID="41883"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-251005">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4188.000000 -598.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41908" ObjectName="SW-CX_GLK.CX_GLK_046XC"/>
     <cge:Meas_Ref ObjectId="251005"/>
    <cge:TPSR_Ref TObjectID="41908"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-251005">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4188.000000 -486.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41909" ObjectName="SW-CX_GLK.CX_GLK_046XC1"/>
     <cge:Meas_Ref ObjectId="251005"/>
    <cge:TPSR_Ref TObjectID="41909"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-251006">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4567.000000 -598.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41910" ObjectName="SW-CX_GLK.CX_GLK_051XC"/>
     <cge:Meas_Ref ObjectId="251006"/>
    <cge:TPSR_Ref TObjectID="41910"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-251006">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4567.000000 -486.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41911" ObjectName="SW-CX_GLK.CX_GLK_051XC1"/>
     <cge:Meas_Ref ObjectId="251006"/>
    <cge:TPSR_Ref TObjectID="41911"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-250804">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4889.000000 -600.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41892" ObjectName="SW-CX_GLK.CX_GLK_053XC"/>
     <cge:Meas_Ref ObjectId="250804"/>
    <cge:TPSR_Ref TObjectID="41892"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-250804">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4889.000000 -488.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41893" ObjectName="SW-CX_GLK.CX_GLK_053XC1"/>
     <cge:Meas_Ref ObjectId="250804"/>
    <cge:TPSR_Ref TObjectID="41893"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-250834">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5081.000000 -600.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41897" ObjectName="SW-CX_GLK.CX_GLK_054XC"/>
     <cge:Meas_Ref ObjectId="250834"/>
    <cge:TPSR_Ref TObjectID="41897"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-250834">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5081.000000 -488.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41898" ObjectName="SW-CX_GLK.CX_GLK_054XC1"/>
     <cge:Meas_Ref ObjectId="250834"/>
    <cge:TPSR_Ref TObjectID="41898"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-250713">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4713.000000 -791.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41878" ObjectName="SW-CX_GLK.CX_GLK_055XC1"/>
     <cge:Meas_Ref ObjectId="250713"/>
    <cge:TPSR_Ref TObjectID="41878"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-250713">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4713.000000 -679.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41877" ObjectName="SW-CX_GLK.CX_GLK_055XC"/>
     <cge:Meas_Ref ObjectId="250713"/>
    <cge:TPSR_Ref TObjectID="41877"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-250932">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3995.000000 -601.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41904" ObjectName="SW-CX_GLK.CX_GLK_045XC"/>
     <cge:Meas_Ref ObjectId="250932"/>
    <cge:TPSR_Ref TObjectID="41904"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-250940">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4724.000000 -595.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41906" ObjectName="SW-CX_GLK.CX_GLK_052XC"/>
     <cge:Meas_Ref ObjectId="250940"/>
    <cge:TPSR_Ref TObjectID="41906"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-250932">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3995.000000 -485.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41905" ObjectName="SW-CX_GLK.CX_GLK_045XC1"/>
     <cge:Meas_Ref ObjectId="250932"/>
    <cge:TPSR_Ref TObjectID="41905"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-250940">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4724.000000 -487.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41907" ObjectName="SW-CX_GLK.CX_GLK_052XC1"/>
     <cge:Meas_Ref ObjectId="250940"/>
    <cge:TPSR_Ref TObjectID="41907"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-250863">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4447.000000 -485.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41903" ObjectName="SW-CX_GLK.CX_GLK_0122XC1"/>
     <cge:Meas_Ref ObjectId="250863"/>
    <cge:TPSR_Ref TObjectID="41903"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-250863">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4447.000000 -596.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41902" ObjectName="SW-CX_GLK.CX_GLK_0122XC"/>
     <cge:Meas_Ref ObjectId="250863"/>
    <cge:TPSR_Ref TObjectID="41902"/></metadata>
   </g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="41869" cx="4734" cy="-651" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="41869" cx="5255" cy="-651" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="41868" cx="3884" cy="-652" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="41869" cx="4723" cy="-651" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="41868" cx="3556" cy="-652" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="41868" cx="3781" cy="-652" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="41868" cx="4198" cy="-652" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="41869" cx="4577" cy="-651" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="41869" cx="4899" cy="-651" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="41869" cx="5091" cy="-651" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="41868" cx="4315" cy="-652" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="41868" cx="4005" cy="-652" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="41869" cx="4457" cy="-651" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="41868" cx="3419" cy="-652" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_GLK"/>
</svg>