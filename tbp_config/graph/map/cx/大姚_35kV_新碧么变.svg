<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-235" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="-753 -1271 2271 1232">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="capacitor:shape36">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.370253" x1="2" x2="2" y1="45" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="0.607143" x1="47" x2="47" y1="63" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="47" x2="47" y1="24" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="41" x2="53" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="51" x2="43" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="48" x2="45" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="36" x2="21" y1="25" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72286" x1="28" x2="28" y1="17" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="12" x2="12" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="45" x2="45" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.338133" x1="28" x2="11" y1="58" y2="58"/>
    <rect height="23" stroke-width="0.369608" width="12" x="22" y="30"/>
    <rect height="23" stroke-width="0.369608" width="12" x="41" y="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.368819" x1="45" x2="12" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.42985" x1="28" x2="28" y1="79" y2="25"/>
    <polyline arcFlag="1" points="11,39 10,39 9,39 9,40 8,40 8,40 7,41 7,41 6,42 6,42 6,43 6,43 5,44 5,45 5,45 6,46 6,47 6,47 6,48 7,48 7,49 8,49 8,50 9,50 9,50 10,50 11,50 " stroke-width="1"/>
    <polyline arcFlag="1" points="11,28 10,28 9,28 9,28 8,29 8,29 7,29 7,30 6,30 6,31 6,32 6,32 5,33 5,34 5,34 6,35 6,36 6,36 6,37 7,37 7,38 8,38 8,38 9,39 9,39 10,39 11,39 " stroke-width="1"/>
    <polyline arcFlag="1" points="11,18 10,18 9,18 9,18 8,18 8,19 7,19 7,20 6,20 6,21 6,21 6,22 5,23 5,23 5,24 6,25 6,25 6,26 6,26 7,27 7,27 8,28 8,28 9,28 9,29 10,29 11,29 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="11" y1="58" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="11" y1="18" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="29" x2="29" y1="92" y2="100"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="16" x2="28" y1="79" y2="79"/>
    <polyline points="29,92 31,92 33,91 34,91 36,90 37,89 39,88 40,86 41,84 41,83 42,81 42,79 42,77 41,75 41,74 40,72 39,71 37,69 36,68 34,67 33,67 31,66 29,66 27,66 25,67 24,67 22,68 21,69 19,71 18,72 17,74 17,75 16,77 16,79 " stroke-width="0.0972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.338133" x1="47" x2="28" y1="63" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="36" x2="21" y1="17" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.395152" x1="28" x2="11" y1="11" y2="11"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="14" y2="14"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="14" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="5" x2="8" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="3" x2="10" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="12" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
   </symbol>
   <symbol id="lightningRod:shape50">
    <polyline DF8003:Layer="PUBLIC" points="5,39 0,50 11,50 5,39 5,40 5,39 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.222222" x1="5" x2="5" y1="29" y2="33"/>
    <polyline DF8003:Layer="PUBLIC" points="5,25 0,14 11,14 5,25 5,24 5,25 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="59" y2="51"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="8" x2="8" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape21">
    <rect height="26" stroke-width="1.99997" width="11" x="2" y="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="7" x2="7" y1="50" y2="5"/>
   </symbol>
   <symbol id="lightningRod:shape7">
    <polyline DF8003:Layer="PUBLIC" points="1,5 10,17 19,5 1,5 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="20" y2="23"/>
    <polyline DF8003:Layer="PUBLIC" points="19,44 10,32 1,44 19,44 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="29" y2="26"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <rect height="14" stroke-width="1" width="27" x="18" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="5" x2="5" y1="11" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="9" x2="18" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="8" x2="8" y1="2" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="58" x2="23" y1="7" y2="7"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="transformer2:shape25_0">
    <circle cx="16" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,57 40,57 40,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="42" y2="0"/>
    <polyline DF8003:Layer="PUBLIC" points="15,14 21,27 9,27 15,14 15,15 15,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="43" x2="40" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="15" y1="52" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="57" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="57" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="58" y2="62"/>
   </symbol>
   <symbol id="transformer2:shape25_1">
    <circle cx="16" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="12,76 19,76 16,83 12,76 "/>
   </symbol>
   <symbol id="transformer2:shape24_0">
    <circle cx="16" cy="20" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="39" x2="14" y1="47" y2="72"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="42" x2="39" y1="77" y2="77"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="74" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="71" y2="71"/>
    <polyline DF8003:Layer="PUBLIC" points="14,84 20,71 7,71 14,84 14,83 14,84 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="56" y2="98"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="15" y1="19" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="19" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="13" y2="19"/>
   </symbol>
   <symbol id="transformer2:shape24_1">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,41 40,41 40,70 " stroke-width="1"/>
    <circle cx="16" cy="42" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="41" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="43" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="42" y2="47"/>
   </symbol>
   <symbol id="transformer2:shape70_0">
    <circle cx="39" cy="32" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="64,93 64,100 " stroke-width="1.22222"/>
    <polyline points="58,100 64,100 " stroke-width="1.22222"/>
    <polyline DF8003:Layer="PUBLIC" points="39,19 32,34 47,34 39,19 39,19 39,19 "/>
   </symbol>
   <symbol id="transformer2:shape70_1">
    <circle cx="39" cy="70" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="64,100 1,37 " stroke-width="1.22222"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
   </symbol>
   <symbol id="voltageTransformer:shape21">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="27" x2="27" y1="11" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="11" y2="5"/>
    <circle cx="27" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="13" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
   </symbol>
   <symbol id="voltageTransformer:shape65">
    <ellipse cx="19" cy="19" rx="7.5" ry="6.5" stroke-width="0.66594"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="46" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="41" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.10043" x1="20" x2="20" y1="9" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="23" x2="20" y1="7" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="17" x2="20" y1="7" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.10043" x1="8" x2="8" y1="20" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="11" x2="8" y1="18" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="5" x2="8" y1="18" y2="20"/>
    <ellipse cx="19" cy="9" rx="7.5" ry="7" stroke-width="0.66594"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.246311" x1="5" x2="9" y1="7" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.245503" x1="5" x2="9" y1="8" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.238574" x1="9" x2="9" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="41" y1="18" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="35" y1="30" y2="38"/>
    <rect height="13" stroke-width="1" width="7" x="32" y="17"/>
    <ellipse cx="8" cy="19" rx="7.5" ry="6.5" stroke-width="0.66594"/>
    <ellipse cx="8" cy="9" rx="7.5" ry="7" stroke-width="0.66594"/>
    <polyline points="27,8 35,8 35,18 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="38" y1="40" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="37" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="40" y1="38" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.10043" x1="20" x2="20" y1="20" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="23" x2="20" y1="18" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="17" x2="20" y1="18" y2="20"/>
   </symbol>
   <symbol id="voltageTransformer:shape54">
    <ellipse cx="8" cy="8" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <ellipse cx="8" cy="16" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="12" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="12" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="32" y2="23"/>
   </symbol>
   <symbol id="voltageTransformer:shape11">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="18" x2="15" y1="11" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="13" x2="15" y1="11" y2="9"/>
    <circle cx="15" cy="19" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="24" cy="16" fillStyle="0" r="6" stroke-width="0.431185"/>
    <ellipse cx="15" cy="11" fillStyle="0" rx="6.5" ry="6" stroke-width="0.431185"/>
    <circle cx="7" cy="16" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="22" x2="24" y1="18" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="25" x2="25" y1="16" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="27" x2="24" y1="18" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="13" x2="15" y1="22" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="16" x2="16" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="18" x2="15" y1="22" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="6" x2="9" y1="19" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="6" x2="3" y1="19" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="3" x2="9" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="16" x2="16" y1="9" y2="7"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_38ba160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_38bae20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_38bb7e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_38bc5c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_38bd6c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_38be070" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_38beb90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_38bf550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_3313aa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_3313aa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_38c1df0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_38c1df0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_38c3950" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_38c3950" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_38c4720" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_38c5f50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_38c6b10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_38c77f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_38c7ec0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_38c9570" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_38ca020" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_38ca860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_38cafa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_392fd80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_38cba80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_38cc310" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_38ccc40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_38cd6c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_38ce010" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_38cf950" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_38d04f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_38d6970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_38d75b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_38d1960" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_38d2c00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1242" width="2281" x="-758" y="-1276"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,0)" stroke-width="1" x1="1516" x2="1516" y1="-563" y2="-541"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-185759">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 727.241796 -972.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28154" ObjectName="SW-DY_BM.DY_BM_363BK"/>
     <cge:Meas_Ref ObjectId="185759"/>
    <cge:TPSR_Ref TObjectID="28154"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186032">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -153.758204 -416.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28182" ObjectName="SW-DY_BM.DY_BM_062BK"/>
     <cge:Meas_Ref ObjectId="186032"/>
    <cge:TPSR_Ref TObjectID="28182"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186272">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 700.241796 -414.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28212" ObjectName="SW-DY_BM.DY_BM_066BK"/>
     <cge:Meas_Ref ObjectId="186272"/>
    <cge:TPSR_Ref TObjectID="28212"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-185691">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 55.241796 -971.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28147" ObjectName="SW-DY_BM.DY_BM_362BK"/>
     <cge:Meas_Ref ObjectId="185691"/>
    <cge:TPSR_Ref TObjectID="28147"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-185827">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 368.000000 -968.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28161" ObjectName="SW-DY_BM.DY_BM_312BK"/>
     <cge:Meas_Ref ObjectId="185827"/>
    <cge:TPSR_Ref TObjectID="28161"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-185898">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 55.241796 -777.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28166" ObjectName="SW-DY_BM.DY_BM_301BK"/>
     <cge:Meas_Ref ObjectId="185898"/>
    <cge:TPSR_Ref TObjectID="28166"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-185922">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 55.241796 -557.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28169" ObjectName="SW-DY_BM.DY_BM_001BK"/>
     <cge:Meas_Ref ObjectId="185922"/>
    <cge:TPSR_Ref TObjectID="28169"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186072">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 73.241796 -413.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28187" ObjectName="SW-DY_BM.DY_BM_063BK"/>
     <cge:Meas_Ref ObjectId="186072"/>
    <cge:TPSR_Ref TObjectID="28187"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186112">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 298.241796 -412.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28192" ObjectName="SW-DY_BM.DY_BM_064BK"/>
     <cge:Meas_Ref ObjectId="186112"/>
    <cge:TPSR_Ref TObjectID="28192"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186152">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 488.241796 -413.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28197" ObjectName="SW-DY_BM.DY_BM_065BK"/>
     <cge:Meas_Ref ObjectId="186152"/>
    <cge:TPSR_Ref TObjectID="28197"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186192">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1140.241796 -414.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28202" ObjectName="SW-DY_BM.DY_BM_067BK"/>
     <cge:Meas_Ref ObjectId="186192"/>
    <cge:TPSR_Ref TObjectID="28202"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186232">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1369.241796 -413.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28207" ObjectName="SW-DY_BM.DY_BM_068BK"/>
     <cge:Meas_Ref ObjectId="186232"/>
    <cge:TPSR_Ref TObjectID="28207"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186310">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 874.500000 -608.741796)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28218" ObjectName="SW-DY_BM.DY_BM_012BK"/>
     <cge:Meas_Ref ObjectId="186310"/>
    <cge:TPSR_Ref TObjectID="28218"/></metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_3113730">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 820.500000 -1204.500000)" xlink:href="#voltageTransformer:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35e6c40">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1196.000000 -1125.000000)" xlink:href="#voltageTransformer:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34e9fc0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -212.000000 -1129.000000)" xlink:href="#voltageTransformer:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35b60b0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 148.500000 -1203.500000)" xlink:href="#voltageTransformer:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_387edd0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 219.000000 -272.000000)" xlink:href="#voltageTransformer:shape54"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f279b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1052.000000 -707.000000)" xlink:href="#voltageTransformer:shape11"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f29740">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 561.000000 -698.000000)" xlink:href="#voltageTransformer:shape11"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="DY_BM" endPointId="0" endStationName="DY_GH" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_bigui" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="63,-1180 63,-1222 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="38086" ObjectName="AC-35kV.LN_bigui"/>
    <cge:TPSR_Ref TObjectID="38086_SS-235"/></metadata>
   <polyline fill="none" opacity="0" points="63,-1180 63,-1222 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="DY_BM" endPointId="0" endStationName="DY_BC" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_beibi" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="735,-1175 735,-1211 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="38074" ObjectName="AC-35kV.LN_beibi"/>
    <cge:TPSR_Ref TObjectID="38074_SS-235"/></metadata>
   <polyline fill="none" opacity="0" points="735,-1175 735,-1211 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-DY_BM.DY_BM_062Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -153.238710 -187.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33982" ObjectName="EC-DY_BM.DY_BM_062Ld"/>
    <cge:TPSR_Ref TObjectID="33982"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-DY_BM.DY_BM_063Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 72.761290 -184.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33983" ObjectName="EC-DY_BM.DY_BM_063Ld"/>
    <cge:TPSR_Ref TObjectID="33983"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-DY_BM.DY_BM_064Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 297.761290 -183.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33984" ObjectName="EC-DY_BM.DY_BM_064Ld"/>
    <cge:TPSR_Ref TObjectID="33984"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-DY_BM.DY_BM_065Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 487.761290 -184.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33985" ObjectName="EC-DY_BM.DY_BM_065Ld"/>
    <cge:TPSR_Ref TObjectID="33985"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-DY_BM.067Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1139.761290 -185.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33986" ObjectName="EC-DY_BM.067Ld"/>
    <cge:TPSR_Ref TObjectID="33986"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-DY_BM.068Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1368.761290 -184.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33987" ObjectName="EC-DY_BM.068Ld"/>
    <cge:TPSR_Ref TObjectID="33987"/></metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_35963f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 617.000000 -1082.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_330b9f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 618.000000 -1018.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35fad10" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 617.000000 -956.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3536970" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -102.000000 -297.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_357d9d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 799.000000 -379.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3450db0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 799.000000 -259.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35100d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1048.000000 -894.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_359ad70" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1153.000000 -1109.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_348e0e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1252.000000 -900.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35fff80" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -255.000000 -1113.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35fda40" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -156.000000 -904.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35a7e90" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -55.000000 -1081.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_359b4c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -54.000000 -1017.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35af370" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -55.000000 -955.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34c9160" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 240.000000 -952.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34c9b90" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 520.000000 -953.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3491050" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 150.000000 -816.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_38908f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -278.000000 -294.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3535120" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 124.000000 -294.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4d6f130" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 349.000000 -293.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3e5eb00" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 539.000000 -294.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_38eb7e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1191.000000 -295.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3900480" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1420.000000 -294.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_39183f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 652.000000 -314.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_35d3630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="647,-1024 636,-1024 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="28159@0" ObjectIDZND0="g_330b9f0@0" Pin0InfoVect0LinkObjId="g_330b9f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-185768_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="647,-1024 636,-1024 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_310fdb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="683,-1024 737,-1024 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="28159@1" ObjectIDZND0="28156@x" ObjectIDZND1="28154@x" Pin0InfoVect0LinkObjId="SW-185763_0" Pin0InfoVect1LinkObjId="SW-185759_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-185768_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="683,-1024 737,-1024 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35a8830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="646,-1088 635,-1088 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="28160@0" ObjectIDZND0="g_35963f0@0" Pin0InfoVect0LinkObjId="g_35963f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-185769_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="646,-1088 635,-1088 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3578660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="736,-1038 736,-1024 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="28156@0" ObjectIDZND0="28159@x" ObjectIDZND1="28154@x" Pin0InfoVect0LinkObjId="SW-185768_0" Pin0InfoVect1LinkObjId="SW-185759_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-185763_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="736,-1038 736,-1024 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_355e3d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="736,-1024 736,-1007 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="28159@x" ObjectIDND1="28156@x" ObjectIDZND0="28154@1" Pin0InfoVect0LinkObjId="SW-185759_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-185768_0" Pin1InfoVect1LinkObjId="SW-185763_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="736,-1024 736,-1007 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35faa60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="646,-962 635,-962 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="28158@0" ObjectIDZND0="g_35fad10@0" Pin0InfoVect0LinkObjId="g_35fad10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-185767_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="646,-962 635,-962 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3411f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="682,-962 736,-962 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="28158@1" ObjectIDZND0="28154@x" ObjectIDZND1="28155@x" Pin0InfoVect0LinkObjId="SW-185759_0" Pin0InfoVect1LinkObjId="SW-185761_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-185767_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="682,-962 736,-962 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35b7130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="736,-980 736,-962 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="28154@0" ObjectIDZND0="28158@x" ObjectIDZND1="28155@x" Pin0InfoVect0LinkObjId="SW-185767_0" Pin0InfoVect1LinkObjId="SW-185761_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-185759_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="736,-980 736,-962 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30ce570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="736,-962 736,-949 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="28158@x" ObjectIDND1="28154@x" ObjectIDZND0="28155@1" Pin0InfoVect0LinkObjId="SW-185761_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-185767_0" Pin1InfoVect1LinkObjId="SW-185759_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="736,-962 736,-949 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32b6cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-144,-463 -144,-451 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28183@1" ObjectIDZND0="28182@1" Pin0InfoVect0LinkObjId="SW-186032_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186034_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-144,-463 -144,-451 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3289f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-144,-424 -144,-412 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="28182@0" ObjectIDZND0="28184@1" Pin0InfoVect0LinkObjId="SW-186034_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186032_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-144,-424 -144,-412 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3634f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-113,-238 -144,-238 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_35da230@0" ObjectIDZND0="28185@x" ObjectIDZND1="33982@x" Pin0InfoVect0LinkObjId="SW-186036_0" Pin0InfoVect1LinkObjId="EC-DY_BM.DY_BM_062Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_35da230_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-113,-238 -144,-238 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35d61e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-144,-252 -144,-238 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="28185@0" ObjectIDZND0="g_35da230@0" ObjectIDZND1="33982@x" Pin0InfoVect0LinkObjId="g_35da230_0" Pin0InfoVect1LinkObjId="EC-DY_BM.DY_BM_062Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186036_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-144,-252 -144,-238 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3324a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-144,-238 -144,-214 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_35da230@0" ObjectIDND1="28185@x" ObjectIDZND0="33982@0" Pin0InfoVect0LinkObjId="EC-DY_BM.DY_BM_062Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_35da230_0" Pin1InfoVect1LinkObjId="SW-186036_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-144,-238 -144,-214 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3636b90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-144,-376 -96,-376 -96,-366 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="28184@x" ObjectIDND1="g_35c2de0@0" ObjectIDND2="g_3508ac0@0" ObjectIDZND0="28186@1" Pin0InfoVect0LinkObjId="SW-186037_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-186034_0" Pin1InfoVect1LinkObjId="g_35c2de0_0" Pin1InfoVect2LinkObjId="g_3508ac0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-144,-376 -96,-376 -96,-366 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e6db00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-144,-395 -144,-376 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="28184@0" ObjectIDZND0="28186@x" ObjectIDZND1="g_35c2de0@0" ObjectIDZND2="g_3508ac0@0" Pin0InfoVect0LinkObjId="SW-186037_0" Pin0InfoVect1LinkObjId="g_35c2de0_0" Pin0InfoVect2LinkObjId="g_3508ac0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186034_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-144,-395 -144,-376 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35ae910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-144,-376 -144,-359 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="28186@x" ObjectIDND1="28184@x" ObjectIDND2="g_3508ac0@0" ObjectIDZND0="g_35c2de0@0" Pin0InfoVect0LinkObjId="g_35c2de0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-186037_0" Pin1InfoVect1LinkObjId="SW-186034_0" Pin1InfoVect2LinkObjId="g_3508ac0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-144,-376 -144,-359 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_361d9b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-96,-330 -96,-315 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="28186@0" ObjectIDZND0="g_3536970@0" Pin0InfoVect0LinkObjId="g_3536970_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186037_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-96,-330 -96,-315 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_354dae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-144,-306 -144,-288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_35c2de0@1" ObjectIDZND0="28185@1" Pin0InfoVect0LinkObjId="SW-186036_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_35c2de0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-144,-306 -144,-288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3518500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-144,-480 -144,-518 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28183@0" ObjectIDZND0="28144@0" Pin0InfoVect0LinkObjId="g_3508600_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186034_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-144,-480 -144,-518 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35caa60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="709,-461 709,-449 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28213@1" ObjectIDZND0="28212@1" Pin0InfoVect0LinkObjId="SW-186272_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186274_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="709,-461 709,-449 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34c17c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="709,-422 709,-410 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="28212@0" ObjectIDZND0="28214@1" Pin0InfoVect0LinkObjId="SW-186274_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186272_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="709,-422 709,-410 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_331c300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="709,-323 709,-312 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_362f1f0@1" ObjectIDZND0="28215@1" Pin0InfoVect0LinkObjId="SW-186276_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_362f1f0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="709,-323 709,-312 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_356a210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="709,-385 757,-385 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="28214@x" ObjectIDND1="g_362f1f0@0" ObjectIDND2="g_3f2bcf0@0" ObjectIDZND0="28216@0" Pin0InfoVect0LinkObjId="SW-186277_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-186274_0" Pin1InfoVect1LinkObjId="g_362f1f0_0" Pin1InfoVect2LinkObjId="g_3f2bcf0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="709,-385 757,-385 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34831c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="709,-393 709,-385 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="28214@0" ObjectIDZND0="28216@x" ObjectIDZND1="g_362f1f0@0" ObjectIDZND2="g_3f2bcf0@0" Pin0InfoVect0LinkObjId="SW-186277_0" Pin0InfoVect1LinkObjId="g_362f1f0_0" Pin0InfoVect2LinkObjId="g_3f2bcf0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186274_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="709,-393 709,-385 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35573d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="709,-385 709,-376 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="28216@x" ObjectIDND1="28214@x" ObjectIDND2="g_3f2bcf0@0" ObjectIDZND0="g_362f1f0@0" Pin0InfoVect0LinkObjId="g_362f1f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-186277_0" Pin1InfoVect1LinkObjId="SW-186274_0" Pin1InfoVect2LinkObjId="g_3f2bcf0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="709,-385 709,-376 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_353bdb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="803,-385 793,-385 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_357d9d0@0" ObjectIDZND0="28216@1" Pin0InfoVect0LinkObjId="SW-186277_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_357d9d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="803,-385 793,-385 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3511970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="803,-265 793,-265 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3450db0@0" ObjectIDZND0="28217@1" Pin0InfoVect0LinkObjId="SW-186278_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3450db0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="803,-265 793,-265 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35f4850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="735,-1133 735,-1176 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="28157@x" ObjectIDND1="g_3638770@0" ObjectIDND2="28160@x" ObjectIDZND0="38074@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-185765_0" Pin1InfoVect1LinkObjId="g_3638770_0" Pin1InfoVect2LinkObjId="SW-185769_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="735,-1133 735,-1176 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35a4400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="790,-1133 811,-1133 811,-1153 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="28157@1" ObjectIDZND0="g_3625dd0@0" Pin0InfoVect0LinkObjId="g_3625dd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-185765_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="790,-1133 811,-1133 811,-1153 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_357e020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="811,-1197 811,-1209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_3625dd0@1" ObjectIDZND0="g_3113730@0" Pin0InfoVect0LinkObjId="g_3113730_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3625dd0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="811,-1197 811,-1209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_359a1b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="754,-1133 735,-1133 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="28157@0" ObjectIDZND0="g_3638770@0" ObjectIDZND1="28160@x" ObjectIDZND2="28156@x" Pin0InfoVect0LinkObjId="g_3638770_0" Pin0InfoVect1LinkObjId="SW-185769_0" Pin0InfoVect2LinkObjId="SW-185763_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-185765_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="754,-1133 735,-1133 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35ecb70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="704,-1170 704,-1133 735,-1133 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_3638770@0" ObjectIDZND0="28157@x" ObjectIDZND1="28160@x" ObjectIDZND2="28156@x" Pin0InfoVect0LinkObjId="SW-185765_0" Pin0InfoVect1LinkObjId="SW-185769_0" Pin0InfoVect2LinkObjId="SW-185763_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3638770_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="704,-1170 704,-1133 735,-1133 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_333b040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="735,-1133 735,-1089 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="28157@x" ObjectIDND1="g_3638770@0" ObjectIDND2="38074@1" ObjectIDZND0="28160@x" ObjectIDZND1="28156@x" Pin0InfoVect0LinkObjId="SW-185769_0" Pin0InfoVect1LinkObjId="SW-185763_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-185765_0" Pin1InfoVect1LinkObjId="g_3638770_0" Pin1InfoVect2LinkObjId="g_35f4850_1" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="735,-1133 735,-1089 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35e7480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="682,-1088 736,-1088 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="28160@1" ObjectIDZND0="28157@x" ObjectIDZND1="g_3638770@0" ObjectIDZND2="38074@1" Pin0InfoVect0LinkObjId="SW-185765_0" Pin0InfoVect1LinkObjId="g_3638770_0" Pin0InfoVect2LinkObjId="g_35f4850_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-185769_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="682,-1088 736,-1088 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35d6430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="736,-1088 736,-1074 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" ObjectIDND0="28157@x" ObjectIDND1="g_3638770@0" ObjectIDND2="38074@1" ObjectIDZND0="28156@1" Pin0InfoVect0LinkObjId="SW-185763_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-185765_0" Pin1InfoVect1LinkObjId="g_3638770_0" Pin1InfoVect2LinkObjId="g_35f4850_1" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="736,-1088 736,-1074 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_362f960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="992,-1044 992,-1025 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_35d6660@1" Pin0InfoVect0LinkObjId="g_35d6660_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3113730_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="992,-1044 992,-1025 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_350fe70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1054,-927 1054,-912 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="28224@0" ObjectIDZND0="g_35100d0@0" Pin0InfoVect0LinkObjId="g_35100d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186369_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1054,-927 1054,-912 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_348e860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="992,-972 1054,-972 1054,-964 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_35d6660@1" ObjectIDND1="28223@x" ObjectIDZND0="28224@1" Pin0InfoVect0LinkObjId="SW-186369_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_35d6660_1" Pin1InfoVect1LinkObjId="SW-186367_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="992,-972 1054,-972 1054,-964 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35e69e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="992,-981 992,-972 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_35d6660@0" ObjectIDZND0="28224@x" ObjectIDZND1="28223@x" Pin0InfoVect0LinkObjId="SW-186369_0" Pin0InfoVect1LinkObjId="SW-186367_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_35d6660_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="992,-981 992,-972 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35dc3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="992,-972 992,-961 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="28224@x" ObjectIDND1="g_35d6660@1" ObjectIDZND0="28223@1" Pin0InfoVect0LinkObjId="SW-186367_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-186369_0" Pin1InfoVect1LinkObjId="g_35d6660_1" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="992,-972 992,-961 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35a3dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1216,-1127 1216,-1118 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_35e6c40@0" ObjectIDZND0="g_3e6d5b0@1" Pin0InfoVect0LinkObjId="g_3e6d5b0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_35e6c40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1216,-1127 1216,-1118 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35d5b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1216,-1055 1248,-1055 1248,-1069 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_3e6d5b0@0" ObjectIDND1="28175@x" ObjectIDND2="28177@x" ObjectIDZND0="g_3502430@0" Pin0InfoVect0LinkObjId="g_3502430_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3e6d5b0_0" Pin1InfoVect1LinkObjId="SW-186004_0" Pin1InfoVect2LinkObjId="SW-186007_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1216,-1055 1248,-1055 1248,-1069 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35d5da0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1216,-1074 1216,-1055 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_3e6d5b0@0" ObjectIDZND0="g_3502430@0" ObjectIDZND1="28175@x" ObjectIDZND2="28177@x" Pin0InfoVect0LinkObjId="g_3502430_0" Pin0InfoVect1LinkObjId="SW-186004_0" Pin0InfoVect2LinkObjId="SW-186007_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3e6d5b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1216,-1074 1216,-1055 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35021d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1216,-1055 1216,-1030 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_3502430@0" ObjectIDND1="g_3e6d5b0@0" ObjectIDND2="28177@x" ObjectIDZND0="28175@1" Pin0InfoVect0LinkObjId="SW-186004_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3502430_0" Pin1InfoVect1LinkObjId="g_3e6d5b0_0" Pin1InfoVect2LinkObjId="SW-186007_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1216,-1055 1216,-1030 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_362bef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1216,-1055 1159,-1055 1159,-1065 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_3502430@0" ObjectIDND1="g_3e6d5b0@0" ObjectIDND2="28175@x" ObjectIDZND0="28177@0" Pin0InfoVect0LinkObjId="SW-186007_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3502430_0" Pin1InfoVect1LinkObjId="g_3e6d5b0_0" Pin1InfoVect2LinkObjId="SW-186004_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1216,-1055 1159,-1055 1159,-1065 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34c0030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1159,-1114 1159,-1101 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_359ad70@0" ObjectIDZND0="28177@1" Pin0InfoVect0LinkObjId="SW-186007_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_359ad70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1159,-1114 1159,-1101 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_348de80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1258,-933 1258,-918 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="28176@0" ObjectIDZND0="g_348e0e0@0" Pin0InfoVect0LinkObjId="g_348e0e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186006_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1258,-933 1258,-918 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_352c250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1258,-969 1258,-978 1216,-978 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="28176@1" ObjectIDZND0="28175@x" ObjectIDZND1="28143@0" Pin0InfoVect0LinkObjId="SW-186004_0" Pin0InfoVect1LinkObjId="g_34e9b00_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186006_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1258,-969 1258,-978 1216,-978 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35445a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1216,-994 1216,-978 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="28175@0" ObjectIDZND0="28176@x" ObjectIDZND1="28143@0" Pin0InfoVect0LinkObjId="SW-186006_0" Pin0InfoVect1LinkObjId="g_34e9b00_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186004_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1216,-994 1216,-978 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34e9b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1216,-978 1216,-884 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="28176@x" ObjectIDND1="28175@x" ObjectIDZND0="28143@0" Pin0InfoVect0LinkObjId="g_34e9d60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-186006_0" Pin1InfoVect1LinkObjId="SW-186004_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1216,-978 1216,-884 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34e9d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="992,-925 992,-884 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28223@0" ObjectIDZND0="28143@0" Pin0InfoVect0LinkObjId="g_34e9b00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186367_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="992,-925 992,-884 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3571fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-192,-1131 -192,-1122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_34e9fc0@0" ObjectIDZND0="g_38d4170@1" Pin0InfoVect0LinkObjId="g_38d4170_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34e9fc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-192,-1131 -192,-1122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3572240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-192,-1059 -160,-1059 -160,-1073 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_38d4170@0" ObjectIDND1="28172@x" ObjectIDND2="28174@1" ObjectIDZND0="g_3605310@0" Pin0InfoVect0LinkObjId="g_3605310_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_38d4170_0" Pin1InfoVect1LinkObjId="SW-185994_0" Pin1InfoVect2LinkObjId="SW-185997_1" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-192,-1059 -160,-1059 -160,-1073 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35724a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-192,-1078 -192,-1059 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_38d4170@0" ObjectIDZND0="g_3605310@0" ObjectIDZND1="28172@x" ObjectIDZND2="28174@1" Pin0InfoVect0LinkObjId="g_3605310_0" Pin0InfoVect1LinkObjId="SW-185994_0" Pin0InfoVect2LinkObjId="SW-185997_1" Pin0Num="1" Pin1InfoVect0LinkObjId="g_38d4170_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-192,-1078 -192,-1059 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36050b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-192,-1059 -192,-1034 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_3605310@0" ObjectIDND1="g_38d4170@0" ObjectIDND2="28174@1" ObjectIDZND0="28172@1" Pin0InfoVect0LinkObjId="SW-185994_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3605310_0" Pin1InfoVect1LinkObjId="g_38d4170_0" Pin1InfoVect2LinkObjId="SW-185997_1" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-192,-1059 -192,-1034 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35ffd20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-192,-1059 -249,-1059 -249,-1069 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_3605310@0" ObjectIDND1="g_38d4170@0" ObjectIDND2="28172@x" ObjectIDZND0="28174@0" Pin0InfoVect0LinkObjId="SW-185997_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3605310_0" Pin1InfoVect1LinkObjId="g_38d4170_0" Pin1InfoVect2LinkObjId="SW-185994_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-192,-1059 -249,-1059 -249,-1069 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35287f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-249,-1118 -249,-1105 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_35fff80@0" ObjectIDZND0="28174@1" Pin0InfoVect0LinkObjId="SW-185997_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_35fff80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-249,-1118 -249,-1105 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35d8310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-150,-973 -150,-982 -192,-982 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="28173@1" ObjectIDZND0="28172@x" ObjectIDZND1="28142@0" Pin0InfoVect0LinkObjId="SW-185994_0" Pin0InfoVect1LinkObjId="g_35d87d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-185996_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-150,-973 -150,-982 -192,-982 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35d8570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-192,-998 -192,-982 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="28172@0" ObjectIDZND0="28173@0" ObjectIDZND1="28142@0" Pin0InfoVect0LinkObjId="SW-185996_0" Pin0InfoVect1LinkObjId="g_35d87d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-185994_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-192,-998 -192,-982 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35d87d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-192,-982 -192,-881 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="28173@0" ObjectIDND1="28172@x" ObjectIDZND0="28142@0" Pin0InfoVect0LinkObjId="g_35518f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-185996_0" Pin1InfoVect1LinkObjId="SW-185994_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-192,-982 -192,-881 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_359b260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-25,-1023 -36,-1023 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="28152@0" ObjectIDZND0="g_359b4c0@0" Pin0InfoVect0LinkObjId="g_359b4c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-185700_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-25,-1023 -36,-1023 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34f0660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="11,-1023 65,-1023 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="28152@1" ObjectIDZND0="28149@x" ObjectIDZND1="28147@x" Pin0InfoVect0LinkObjId="SW-185695_0" Pin0InfoVect1LinkObjId="SW-185691_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-185700_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="11,-1023 65,-1023 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34f08c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-26,-1087 -37,-1087 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="28153@0" ObjectIDZND0="g_35a7e90@0" Pin0InfoVect0LinkObjId="g_35a7e90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-185701_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-26,-1087 -37,-1087 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35d7a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="64,-1037 64,-1023 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="28149@0" ObjectIDZND0="28152@0" ObjectIDZND1="28147@x" Pin0InfoVect0LinkObjId="SW-185700_0" Pin0InfoVect1LinkObjId="SW-185691_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-185695_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="64,-1037 64,-1023 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35d7cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="64,-1023 64,-1006 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="28152@0" ObjectIDND1="28149@x" ObjectIDZND0="28147@1" Pin0InfoVect0LinkObjId="SW-185691_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-185700_0" Pin1InfoVect1LinkObjId="SW-185695_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="64,-1023 64,-1006 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35af110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-26,-961 -37,-961 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="28151@0" ObjectIDZND0="g_35af370@0" Pin0InfoVect0LinkObjId="g_35af370_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-185699_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-26,-961 -37,-961 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34d1690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="10,-961 64,-961 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="28151@1" ObjectIDZND0="28147@x" ObjectIDZND1="28148@x" Pin0InfoVect0LinkObjId="SW-185691_0" Pin0InfoVect1LinkObjId="SW-185693_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-185699_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="10,-961 64,-961 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_359f1c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="64,-979 64,-961 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="28147@0" ObjectIDZND0="28151@x" ObjectIDZND1="28148@x" Pin0InfoVect0LinkObjId="SW-185699_0" Pin0InfoVect1LinkObjId="SW-185693_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-185691_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="64,-979 64,-961 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3552650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="64,-961 64,-948 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="28151@x" ObjectIDND1="28147@x" ObjectIDZND0="28148@1" Pin0InfoVect0LinkObjId="SW-185693_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-185699_0" Pin1InfoVect1LinkObjId="SW-185691_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="64,-961 64,-948 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35ed150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="63,-1132 63,-1182 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="28150@x" ObjectIDND1="g_3561370@0" ObjectIDND2="28153@x" ObjectIDZND0="38086@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-185697_0" Pin1InfoVect1LinkObjId="g_3561370_0" Pin1InfoVect2LinkObjId="SW-185701_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="63,-1132 63,-1182 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35b5670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="118,-1132 139,-1132 139,-1152 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="28150@1" ObjectIDZND0="g_35b58d0@0" Pin0InfoVect0LinkObjId="g_35b58d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-185697_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="118,-1132 139,-1132 139,-1152 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3565cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="139,-1196 139,-1208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_35b58d0@1" ObjectIDZND0="g_35b60b0@0" Pin0InfoVect0LinkObjId="g_35b60b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_35b58d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="139,-1196 139,-1208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3621080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="82,-1132 63,-1132 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="28150@0" ObjectIDZND0="g_3561370@0" ObjectIDZND1="28153@x" ObjectIDZND2="28149@x" Pin0InfoVect0LinkObjId="g_3561370_0" Pin0InfoVect1LinkObjId="SW-185701_0" Pin0InfoVect2LinkObjId="SW-185695_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-185697_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="82,-1132 63,-1132 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36212e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="32,-1169 32,-1132 63,-1132 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_3561370@0" ObjectIDZND0="28150@x" ObjectIDZND1="28153@x" ObjectIDZND2="28149@x" Pin0InfoVect0LinkObjId="SW-185697_0" Pin0InfoVect1LinkObjId="SW-185701_0" Pin0InfoVect2LinkObjId="SW-185695_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3561370_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="32,-1169 32,-1132 63,-1132 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3621540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="63,-1132 63,-1088 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="28150@x" ObjectIDND1="g_3561370@0" ObjectIDND2="38086@1" ObjectIDZND0="28153@x" ObjectIDZND1="28149@x" Pin0InfoVect0LinkObjId="SW-185701_0" Pin0InfoVect1LinkObjId="SW-185695_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-185697_0" Pin1InfoVect1LinkObjId="g_3561370_0" Pin1InfoVect2LinkObjId="g_35ed150_1" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="63,-1132 63,-1088 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36217a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="10,-1087 64,-1087 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="28153@1" ObjectIDZND0="28150@x" ObjectIDZND1="g_3561370@0" ObjectIDZND2="38086@1" Pin0InfoVect0LinkObjId="SW-185697_0" Pin0InfoVect1LinkObjId="g_3561370_0" Pin0InfoVect2LinkObjId="g_35ed150_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-185701_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="10,-1087 64,-1087 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3621a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="64,-1087 64,-1073 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" ObjectIDND0="28150@x" ObjectIDND1="g_3561370@0" ObjectIDND2="38086@1" ObjectIDZND0="28149@1" Pin0InfoVect0LinkObjId="SW-185695_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-185697_0" Pin1InfoVect1LinkObjId="g_3561370_0" Pin1InfoVect2LinkObjId="g_35ed150_1" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="64,-1087 64,-1073 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34288a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="461,-907 461,-884 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28163@0" ObjectIDZND0="28143@0" Pin0InfoVect0LinkObjId="g_34e9b00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-185831_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="461,-907 461,-884 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35518f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="320,-907 320,-881 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28162@0" ObjectIDZND0="28142@0" Pin0InfoVect0LinkObjId="g_35d87d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-185829_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="320,-907 320,-881 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34c8f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="269,-958 258,-958 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="28164@0" ObjectIDZND0="g_34c9160@0" Pin0InfoVect0LinkObjId="g_34c9160_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-185833_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="269,-958 258,-958 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3598f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="524,-959 513,-959 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_34c9b90@0" ObjectIDZND0="28165@1" Pin0InfoVect0LinkObjId="SW-185834_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34c9b90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="524,-959 513,-959 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35606c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="305,-958 320,-958 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="28164@1" ObjectIDZND0="28162@x" ObjectIDZND1="28161@x" Pin0InfoVect0LinkObjId="SW-185829_0" Pin0InfoVect1LinkObjId="SW-185827_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-185833_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="305,-958 320,-958 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3429c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="320,-943 320,-958 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="28162@1" ObjectIDZND0="28164@x" ObjectIDZND1="28161@x" Pin0InfoVect0LinkObjId="SW-185833_0" Pin0InfoVect1LinkObjId="SW-185827_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-185829_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="320,-943 320,-958 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3429ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="320,-958 320,-978 377,-978 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="28164@x" ObjectIDND1="28162@x" ObjectIDZND0="28161@1" Pin0InfoVect0LinkObjId="SW-185827_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-185833_0" Pin1InfoVect1LinkObjId="SW-185829_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="320,-958 320,-978 377,-978 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_342a150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="462,-959 477,-959 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="28163@x" ObjectIDND1="28161@x" ObjectIDZND0="28165@0" Pin0InfoVect0LinkObjId="SW-185834_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-185831_0" Pin1InfoVect1LinkObjId="SW-185827_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="462,-959 477,-959 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35c7430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="461,-943 461,-959 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="28163@1" ObjectIDZND0="28165@x" ObjectIDZND1="28161@x" Pin0InfoVect0LinkObjId="SW-185834_0" Pin0InfoVect1LinkObjId="SW-185827_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-185831_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="461,-943 461,-959 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35c7670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="461,-959 461,-978 404,-978 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="28165@x" ObjectIDND1="28163@x" ObjectIDZND0="28161@0" Pin0InfoVect0LinkObjId="SW-185827_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-185834_0" Pin1InfoVect1LinkObjId="SW-185831_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="461,-959 461,-978 404,-978 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3311280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="64,-912 64,-881 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28148@0" ObjectIDZND0="28142@0" Pin0InfoVect0LinkObjId="g_35d87d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-185693_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="64,-912 64,-881 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3311a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="736,-913 736,-884 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28155@0" ObjectIDZND0="28143@0" Pin0InfoVect0LinkObjId="g_34e9b00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-185761_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="736,-913 736,-884 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3491ae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="154,-822 143,-822 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3491050@0" ObjectIDZND0="28168@1" Pin0InfoVect0LinkObjId="SW-185902_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3491050_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="154,-822 143,-822 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3494270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="64,-822 107,-822 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="28167@x" ObjectIDND1="28166@x" ObjectIDZND0="28168@0" Pin0InfoVect0LinkObjId="SW-185902_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-185900_0" Pin1InfoVect1LinkObjId="SW-185898_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="64,-822 107,-822 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3469070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="64,-832 64,-822 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="28167@0" ObjectIDZND0="28168@x" ObjectIDZND1="28166@x" Pin0InfoVect0LinkObjId="SW-185902_0" Pin0InfoVect1LinkObjId="SW-185898_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-185900_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="64,-832 64,-822 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34692d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="64,-822 64,-812 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="28168@x" ObjectIDND1="28167@x" ObjectIDZND0="28166@1" Pin0InfoVect0LinkObjId="SW-185898_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-185902_0" Pin1InfoVect1LinkObjId="SW-185900_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="64,-822 64,-812 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3469530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="64,-785 63,-771 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="28166@0" ObjectIDZND0="28228@1" Pin0InfoVect0LinkObjId="g_346ab20_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-185898_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="64,-785 63,-771 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3469790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="64,-868 64,-881 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28167@1" ObjectIDZND0="28142@0" Pin0InfoVect0LinkObjId="g_35d87d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-185900_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="64,-868 64,-881 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_346ab20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="64,-679 64,-688 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_3469fa0@1" ObjectIDZND0="28228@0" Pin0InfoVect0LinkObjId="g_3469530_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3469fa0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="64,-679 64,-688 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3896340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="64,-604 64,-592 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28170@1" ObjectIDZND0="28169@1" Pin0InfoVect0LinkObjId="SW-185922_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-185924_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="64,-604 64,-592 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3899420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="64,-629 64,-620 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_3898670@0" ObjectIDND1="g_3469fa0@0" ObjectIDZND0="28170@0" Pin0InfoVect0LinkObjId="SW-185924_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3898670_0" Pin1InfoVect1LinkObjId="g_3469fa0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="64,-629 64,-620 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_351a580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="41,-629 64,-629 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_3898670@0" ObjectIDZND0="28170@x" ObjectIDZND1="g_3469fa0@0" Pin0InfoVect0LinkObjId="SW-185924_0" Pin0InfoVect1LinkObjId="g_3469fa0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3898670_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="41,-629 64,-629 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_351a7e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="64,-629 64,-640 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="28170@x" ObjectIDND1="g_3898670@0" ObjectIDZND0="g_3469fa0@0" Pin0InfoVect0LinkObjId="g_3469fa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-185924_0" Pin1InfoVect1LinkObjId="g_3898670_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="64,-629 64,-640 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_351aa40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="64,-553 64,-565 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28171@1" ObjectIDZND0="28169@0" Pin0InfoVect0LinkObjId="SW-185922_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-185924_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="64,-553 64,-565 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_351dcd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-320,-405 -320,-416 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="28226@1" ObjectIDZND0="g_351df30@0" Pin0InfoVect0LinkObjId="g_351df30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186374_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-320,-405 -320,-416 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3314e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-320,-460 -320,-471 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_351df30@1" ObjectIDZND0="28225@1" Pin0InfoVect0LinkObjId="SW-186374_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_351df30_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-320,-460 -320,-471 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3890690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-272,-327 -272,-312 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="28227@0" ObjectIDZND0="g_38908f0@0" Pin0InfoVect0LinkObjId="g_38908f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186376_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-272,-327 -272,-312 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3891340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-320,-303 -320,-285 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_3317f10@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_3113730_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3317f10_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-320,-303 -320,-285 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38915a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-320,-388 -320,-373 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="28226@0" ObjectIDZND0="g_3317f10@0" ObjectIDZND1="28227@x" ObjectIDZND2="g_35078d0@0" Pin0InfoVect0LinkObjId="g_3317f10_0" Pin0InfoVect1LinkObjId="SW-186376_0" Pin0InfoVect2LinkObjId="g_35078d0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186374_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-320,-388 -320,-373 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38922b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-320,-356 -320,-373 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_3317f10@0" ObjectIDZND0="28226@x" ObjectIDZND1="28227@x" ObjectIDZND2="g_35078d0@0" Pin0InfoVect0LinkObjId="SW-186374_0" Pin0InfoVect1LinkObjId="SW-186376_0" Pin0InfoVect2LinkObjId="g_35078d0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3317f10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-320,-356 -320,-373 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3892510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-320,-373 -272,-373 -272,-363 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="28226@x" ObjectIDND1="g_3317f10@0" ObjectIDND2="g_35078d0@0" ObjectIDZND0="28227@1" Pin0InfoVect0LinkObjId="SW-186376_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-186374_0" Pin1InfoVect1LinkObjId="g_3317f10_0" Pin1InfoVect2LinkObjId="g_35078d0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-320,-373 -272,-373 -272,-363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3507670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-320,-373 -353,-373 -353,-360 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="28226@x" ObjectIDND1="g_3317f10@0" ObjectIDND2="28227@x" ObjectIDZND0="g_35078d0@0" Pin0InfoVect0LinkObjId="g_35078d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-186374_0" Pin1InfoVect1LinkObjId="g_3317f10_0" Pin1InfoVect2LinkObjId="SW-186376_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-320,-373 -353,-373 -353,-360 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3508600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-320,-488 -320,-518 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28225@0" ObjectIDZND0="28144@0" Pin0InfoVect0LinkObjId="g_3518500_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186374_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-320,-488 -320,-518 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3508860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-145,-376 -178,-376 -178,-363 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="28186@x" ObjectIDND1="28184@x" ObjectIDND2="g_35c2de0@0" ObjectIDZND0="g_3508ac0@0" Pin0InfoVect0LinkObjId="g_3508ac0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-186037_0" Pin1InfoVect1LinkObjId="SW-186034_0" Pin1InfoVect2LinkObjId="g_35c2de0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-145,-376 -178,-376 -178,-363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_354ba60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="82,-460 82,-448 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28188@1" ObjectIDZND0="28187@1" Pin0InfoVect0LinkObjId="SW-186072_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186074_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="82,-460 82,-448 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_345a590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="82,-421 82,-409 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="28187@0" ObjectIDZND0="28189@1" Pin0InfoVect0LinkObjId="SW-186074_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186072_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="82,-421 82,-409 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3531af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="113,-235 82,-235 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_35304b0@0" ObjectIDZND0="28190@x" ObjectIDZND1="33983@x" Pin0InfoVect0LinkObjId="SW-186076_0" Pin0InfoVect1LinkObjId="EC-DY_BM.DY_BM_063Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_35304b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="113,-235 82,-235 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3531ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="82,-249 82,-235 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="28190@0" ObjectIDZND0="g_35304b0@0" ObjectIDZND1="33983@x" Pin0InfoVect0LinkObjId="g_35304b0_0" Pin0InfoVect1LinkObjId="EC-DY_BM.DY_BM_063Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186076_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="82,-249 82,-235 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3531ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="82,-235 82,-211 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_35304b0@0" ObjectIDND1="28190@x" ObjectIDZND0="33983@0" Pin0InfoVect0LinkObjId="EC-DY_BM.DY_BM_063Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_35304b0_0" Pin1InfoVect1LinkObjId="SW-186076_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="82,-235 82,-211 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35320c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="82,-373 130,-373 130,-363 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="28189@x" ObjectIDND1="g_345a7f0@0" ObjectIDND2="g_34f5b60@0" ObjectIDZND0="28191@1" Pin0InfoVect0LinkObjId="SW-186077_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-186074_0" Pin1InfoVect1LinkObjId="g_345a7f0_0" Pin1InfoVect2LinkObjId="g_34f5b60_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="82,-373 130,-373 130,-363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35322f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="82,-392 82,-373 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="28189@0" ObjectIDZND0="28191@x" ObjectIDZND1="g_345a7f0@0" ObjectIDZND2="g_34f5b60@0" Pin0InfoVect0LinkObjId="SW-186077_0" Pin0InfoVect1LinkObjId="g_345a7f0_0" Pin0InfoVect2LinkObjId="g_34f5b60_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186074_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="82,-392 82,-373 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3532520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="82,-373 82,-356 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="28191@x" ObjectIDND1="28189@x" ObjectIDND2="g_34f5b60@0" ObjectIDZND0="g_345a7f0@0" Pin0InfoVect0LinkObjId="g_345a7f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-186077_0" Pin1InfoVect1LinkObjId="SW-186074_0" Pin1InfoVect2LinkObjId="g_34f5b60_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="82,-373 82,-356 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3534ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="130,-327 130,-312 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="28191@0" ObjectIDZND0="g_3535120@0" Pin0InfoVect0LinkObjId="g_3535120_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186077_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="130,-327 130,-312 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3535b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="82,-303 82,-285 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_345a7f0@1" ObjectIDZND0="28190@1" Pin0InfoVect0LinkObjId="SW-186076_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_345a7f0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="82,-303 82,-285 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3535dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="82,-477 82,-518 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28188@0" ObjectIDZND0="28144@0" Pin0InfoVect0LinkObjId="g_3518500_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186074_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="82,-477 82,-518 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34f5970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="81,-373 48,-373 48,-360 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="28191@x" ObjectIDND1="28189@x" ObjectIDND2="g_345a7f0@0" ObjectIDZND0="g_34f5b60@0" Pin0InfoVect0LinkObjId="g_34f5b60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-186077_0" Pin1InfoVect1LinkObjId="SW-186074_0" Pin1InfoVect2LinkObjId="g_345a7f0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="81,-373 48,-373 48,-360 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34f78c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="307,-459 307,-447 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28193@1" ObjectIDZND0="28192@1" Pin0InfoVect0LinkObjId="SW-186112_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186114_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="307,-459 307,-447 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34f9bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="307,-420 307,-408 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="28192@0" ObjectIDZND0="28194@1" Pin0InfoVect0LinkObjId="SW-186114_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186112_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="307,-420 307,-408 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34dee80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="338,-234 307,-234 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_34dd340@0" ObjectIDZND0="28195@x" ObjectIDZND1="33984@x" Pin0InfoVect0LinkObjId="SW-186116_0" Pin0InfoVect1LinkObjId="EC-DY_BM.DY_BM_064Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34dd340_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="338,-234 307,-234 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34df070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="307,-248 307,-234 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="28195@0" ObjectIDZND0="g_34dd340@0" ObjectIDZND1="33984@x" Pin0InfoVect0LinkObjId="g_34dd340_0" Pin0InfoVect1LinkObjId="EC-DY_BM.DY_BM_064Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186116_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="307,-248 307,-234 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34df260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="307,-234 307,-210 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_34dd340@0" ObjectIDND1="28195@x" ObjectIDZND0="33984@0" Pin0InfoVect0LinkObjId="EC-DY_BM.DY_BM_064Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_34dd340_0" Pin1InfoVect1LinkObjId="SW-186116_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="307,-234 307,-210 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34df450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="307,-372 355,-372 355,-362 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="28194@x" ObjectIDND1="g_34f9e50@0" ObjectIDND2="g_4d711a0@0" ObjectIDZND0="28196@1" Pin0InfoVect0LinkObjId="SW-186117_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-186114_0" Pin1InfoVect1LinkObjId="g_34f9e50_0" Pin1InfoVect2LinkObjId="g_4d711a0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="307,-372 355,-372 355,-362 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34df680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="307,-391 307,-372 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="28194@0" ObjectIDZND0="28196@x" ObjectIDZND1="g_34f9e50@0" ObjectIDZND2="g_4d711a0@0" Pin0InfoVect0LinkObjId="SW-186117_0" Pin0InfoVect1LinkObjId="g_34f9e50_0" Pin0InfoVect2LinkObjId="g_4d711a0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186114_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="307,-391 307,-372 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34df8b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="307,-372 307,-355 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="28196@x" ObjectIDND1="28194@x" ObjectIDND2="g_4d711a0@0" ObjectIDZND0="g_34f9e50@0" Pin0InfoVect0LinkObjId="g_34f9e50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-186117_0" Pin1InfoVect1LinkObjId="SW-186114_0" Pin1InfoVect2LinkObjId="g_4d711a0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="307,-372 307,-355 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4d6eed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="355,-326 355,-311 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="28196@0" ObjectIDZND0="g_4d6f130@0" Pin0InfoVect0LinkObjId="g_4d6f130_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186117_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="355,-326 355,-311 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4d6fb80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="307,-302 307,-284 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_34f9e50@1" ObjectIDZND0="28195@1" Pin0InfoVect0LinkObjId="SW-186116_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34f9e50_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="307,-302 307,-284 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4d6fde0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="307,-476 307,-518 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28193@0" ObjectIDZND0="28144@0" Pin0InfoVect0LinkObjId="g_3518500_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186114_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="307,-476 307,-518 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_387f9e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="228,-317 228,-303 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_387e4d0@0" ObjectIDZND0="g_387edd0@0" Pin0InfoVect0LinkObjId="g_387edd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_387e4d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="228,-317 228,-303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38804d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="306,-372 273,-372 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="28196@x" ObjectIDND1="28194@x" ObjectIDND2="g_34f9e50@0" ObjectIDZND0="g_4d711a0@0" ObjectIDZND1="g_387e4d0@0" Pin0InfoVect0LinkObjId="g_4d711a0_0" Pin0InfoVect1LinkObjId="g_387e4d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-186117_0" Pin1InfoVect1LinkObjId="SW-186114_0" Pin1InfoVect2LinkObjId="g_34f9e50_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="306,-372 273,-372 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3880730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="273,-372 273,-359 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="28196@x" ObjectIDND1="28194@x" ObjectIDND2="g_34f9e50@0" ObjectIDZND0="g_4d711a0@0" Pin0InfoVect0LinkObjId="g_4d711a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-186117_0" Pin1InfoVect1LinkObjId="SW-186114_0" Pin1InfoVect2LinkObjId="g_34f9e50_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="273,-372 273,-359 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3880990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="228,-361 228,-372 273,-372 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_387e4d0@1" ObjectIDZND0="28196@x" ObjectIDZND1="28194@x" ObjectIDZND2="g_34f9e50@0" Pin0InfoVect0LinkObjId="SW-186117_0" Pin0InfoVect1LinkObjId="SW-186114_0" Pin0InfoVect2LinkObjId="g_34f9e50_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_387e4d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="228,-361 228,-372 273,-372 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3886b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="497,-460 497,-448 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28198@1" ObjectIDZND0="28197@1" Pin0InfoVect0LinkObjId="SW-186152_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186154_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="497,-460 497,-448 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3888e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="497,-421 497,-409 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="28197@0" ObjectIDZND0="28199@1" Pin0InfoVect0LinkObjId="SW-186154_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186152_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="497,-421 497,-409 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e5b6d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="528,-235 497,-235 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_3e5a310@0" ObjectIDZND0="28200@x" ObjectIDZND1="33985@x" Pin0InfoVect0LinkObjId="SW-186156_0" Pin0InfoVect1LinkObjId="EC-DY_BM.DY_BM_065Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3e5a310_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="528,-235 497,-235 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e5b8c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="497,-249 497,-235 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="28200@0" ObjectIDZND0="g_3e5a310@0" ObjectIDZND1="33985@x" Pin0InfoVect0LinkObjId="g_3e5a310_0" Pin0InfoVect1LinkObjId="EC-DY_BM.DY_BM_065Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186156_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="497,-249 497,-235 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e5bab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="497,-235 497,-211 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_3e5a310@0" ObjectIDND1="28200@x" ObjectIDZND0="33985@0" Pin0InfoVect0LinkObjId="EC-DY_BM.DY_BM_065Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3e5a310_0" Pin1InfoVect1LinkObjId="SW-186156_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="497,-235 497,-211 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e5bce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="497,-373 545,-373 545,-363 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="28199@x" ObjectIDND1="g_38890c0@0" ObjectIDND2="g_3e60e40@0" ObjectIDZND0="28201@1" Pin0InfoVect0LinkObjId="SW-186157_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-186154_0" Pin1InfoVect1LinkObjId="g_38890c0_0" Pin1InfoVect2LinkObjId="g_3e60e40_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="497,-373 545,-373 545,-363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e5bf10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="497,-392 497,-373 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="28199@0" ObjectIDZND0="28201@x" ObjectIDZND1="g_38890c0@0" ObjectIDZND2="g_3e60e40@0" Pin0InfoVect0LinkObjId="SW-186157_0" Pin0InfoVect1LinkObjId="g_38890c0_0" Pin0InfoVect2LinkObjId="g_3e60e40_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186154_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="497,-392 497,-373 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e5c140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="497,-373 497,-356 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="28201@x" ObjectIDND1="28199@x" ObjectIDND2="g_3e60e40@0" ObjectIDZND0="g_38890c0@0" Pin0InfoVect0LinkObjId="g_38890c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-186157_0" Pin1InfoVect1LinkObjId="SW-186154_0" Pin1InfoVect2LinkObjId="g_3e60e40_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="497,-373 497,-356 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e5e8a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="545,-327 545,-312 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="28201@0" ObjectIDZND0="g_3e5eb00@0" Pin0InfoVect0LinkObjId="g_3e5eb00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186157_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="545,-327 545,-312 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e5f4f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="497,-303 497,-285 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_38890c0@1" ObjectIDZND0="28200@1" Pin0InfoVect0LinkObjId="SW-186156_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_38890c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="497,-303 497,-285 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e5f750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="497,-477 497,-518 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28198@0" ObjectIDZND0="28144@0" Pin0InfoVect0LinkObjId="g_3518500_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186154_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="497,-477 497,-518 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e60c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="496,-373 463,-373 463,-360 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="28201@x" ObjectIDND1="28199@x" ObjectIDND2="g_38890c0@0" ObjectIDZND0="g_3e60e40@0" Pin0InfoVect0LinkObjId="g_3e60e40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-186157_0" Pin1InfoVect1LinkObjId="SW-186154_0" Pin1InfoVect2LinkObjId="g_38890c0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="496,-373 463,-373 463,-360 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e68400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1149,-461 1149,-449 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28203@1" ObjectIDZND0="28202@1" Pin0InfoVect0LinkObjId="SW-186192_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186194_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1149,-461 1149,-449 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e6a630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1149,-422 1149,-410 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="28202@0" ObjectIDZND0="28204@1" Pin0InfoVect0LinkObjId="SW-186194_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186192_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1149,-422 1149,-410 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38e8370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1180,-236 1149,-236 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_38e6970@0" ObjectIDZND0="28205@x" ObjectIDZND1="33986@x" Pin0InfoVect0LinkObjId="SW-186196_0" Pin0InfoVect1LinkObjId="EC-DY_BM.067Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_38e6970_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1180,-236 1149,-236 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38e8560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1149,-250 1149,-236 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="28205@0" ObjectIDZND0="g_38e6970@0" ObjectIDZND1="33986@x" Pin0InfoVect0LinkObjId="g_38e6970_0" Pin0InfoVect1LinkObjId="EC-DY_BM.067Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186196_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1149,-250 1149,-236 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38e8750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1149,-236 1149,-212 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_38e6970@0" ObjectIDND1="28205@x" ObjectIDZND0="33986@0" Pin0InfoVect0LinkObjId="EC-DY_BM.067Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_38e6970_0" Pin1InfoVect1LinkObjId="SW-186196_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1149,-236 1149,-212 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38e8980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1149,-374 1197,-374 1197,-364 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="28204@x" ObjectIDND1="g_3e6a890@0" ObjectIDND2="g_38edb80@0" ObjectIDZND0="28206@1" Pin0InfoVect0LinkObjId="SW-186197_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-186194_0" Pin1InfoVect1LinkObjId="g_3e6a890_0" Pin1InfoVect2LinkObjId="g_38edb80_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1149,-374 1197,-374 1197,-364 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38e8bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1149,-393 1149,-374 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="28204@0" ObjectIDZND0="28206@x" ObjectIDZND1="g_3e6a890@0" ObjectIDZND2="g_38edb80@0" Pin0InfoVect0LinkObjId="SW-186197_0" Pin0InfoVect1LinkObjId="g_3e6a890_0" Pin0InfoVect2LinkObjId="g_38edb80_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186194_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1149,-393 1149,-374 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38e8de0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1149,-374 1149,-357 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="28206@x" ObjectIDND1="28204@x" ObjectIDND2="g_38edb80@0" ObjectIDZND0="g_3e6a890@0" Pin0InfoVect0LinkObjId="g_3e6a890_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-186197_0" Pin1InfoVect1LinkObjId="SW-186194_0" Pin1InfoVect2LinkObjId="g_38edb80_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1149,-374 1149,-357 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38eb580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1197,-328 1197,-313 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="28206@0" ObjectIDZND0="g_38eb7e0@0" Pin0InfoVect0LinkObjId="g_38eb7e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186197_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1197,-328 1197,-313 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38ec230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1149,-304 1149,-286 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3e6a890@1" ObjectIDZND0="28205@1" Pin0InfoVect0LinkObjId="SW-186196_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3e6a890_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1149,-304 1149,-286 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38ec490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1149,-478 1149,-520 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28203@0" ObjectIDZND0="28145@0" Pin0InfoVect0LinkObjId="g_3901130_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186194_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1149,-478 1149,-520 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38ed990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1148,-374 1115,-374 1115,-361 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="28206@x" ObjectIDND1="28204@x" ObjectIDND2="g_3e6a890@0" ObjectIDZND0="g_38edb80@0" Pin0InfoVect0LinkObjId="g_38edb80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-186197_0" Pin1InfoVect1LinkObjId="SW-186194_0" Pin1InfoVect2LinkObjId="g_3e6a890_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1148,-374 1115,-374 1115,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38f5740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1378,-460 1378,-448 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28208@1" ObjectIDZND0="28207@1" Pin0InfoVect0LinkObjId="SW-186232_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186234_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1378,-460 1378,-448 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38f7a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1378,-421 1378,-409 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="28207@0" ObjectIDZND0="28209@1" Pin0InfoVect0LinkObjId="SW-186234_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186232_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1378,-421 1378,-409 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38fd000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1409,-235 1378,-235 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_38fb940@0" ObjectIDZND0="28210@x" ObjectIDZND1="33987@x" Pin0InfoVect0LinkObjId="SW-186236_0" Pin0InfoVect1LinkObjId="EC-DY_BM.068Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_38fb940_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1409,-235 1378,-235 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38fd1f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1378,-249 1378,-235 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="28210@0" ObjectIDZND0="g_38fb940@0" ObjectIDZND1="33987@x" Pin0InfoVect0LinkObjId="g_38fb940_0" Pin0InfoVect1LinkObjId="EC-DY_BM.068Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186236_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1378,-249 1378,-235 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38fd3e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1378,-235 1378,-211 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="28210@x" ObjectIDND1="g_38fb940@0" ObjectIDZND0="33987@0" Pin0InfoVect0LinkObjId="EC-DY_BM.068Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-186236_0" Pin1InfoVect1LinkObjId="g_38fb940_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1378,-235 1378,-211 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38fd5f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1378,-373 1426,-373 1426,-363 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="28209@x" ObjectIDND1="g_3902820@0" ObjectIDND2="g_38f7cd0@0" ObjectIDZND0="28211@1" Pin0InfoVect0LinkObjId="SW-186237_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-186234_0" Pin1InfoVect1LinkObjId="g_3902820_0" Pin1InfoVect2LinkObjId="g_38f7cd0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1378,-373 1426,-373 1426,-363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38fd820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1378,-392 1378,-373 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="28209@0" ObjectIDZND0="28211@x" ObjectIDZND1="g_3902820@0" ObjectIDZND2="g_38f7cd0@0" Pin0InfoVect0LinkObjId="SW-186237_0" Pin0InfoVect1LinkObjId="g_3902820_0" Pin0InfoVect2LinkObjId="g_38f7cd0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186234_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1378,-392 1378,-373 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38fda50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1378,-373 1378,-356 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="28209@x" ObjectIDND1="28211@x" ObjectIDND2="g_3902820@0" ObjectIDZND0="g_38f7cd0@0" Pin0InfoVect0LinkObjId="g_38f7cd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-186234_0" Pin1InfoVect1LinkObjId="SW-186237_0" Pin1InfoVect2LinkObjId="g_3902820_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1378,-373 1378,-356 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3900220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1426,-327 1426,-312 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="28211@0" ObjectIDZND0="g_3900480@0" Pin0InfoVect0LinkObjId="g_3900480_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186237_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1426,-327 1426,-312 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3900ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1378,-303 1378,-285 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_38f7cd0@1" ObjectIDZND0="28210@1" Pin0InfoVect0LinkObjId="SW-186236_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_38f7cd0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1378,-303 1378,-285 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3901130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1378,-477 1378,-520 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28208@0" ObjectIDZND0="28145@0" Pin0InfoVect0LinkObjId="g_38ec490_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186234_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1378,-477 1378,-520 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3902630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1377,-373 1344,-373 1344,-360 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="28209@x" ObjectIDND1="28211@x" ObjectIDND2="g_38f7cd0@0" ObjectIDZND0="g_3902820@0" Pin0InfoVect0LinkObjId="g_3902820_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-186234_0" Pin1InfoVect1LinkObjId="SW-186237_0" Pin1InfoVect2LinkObjId="g_38f7cd0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1377,-373 1344,-373 1344,-360 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3908ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1069,-642 1069,-653 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_39081c0@1" ObjectIDZND0="28180@1" Pin0InfoVect0LinkObjId="SW-186018_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_39081c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1069,-642 1069,-653 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_390bb60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1068,-711 1068,-670 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" ObjectIDND0="g_3f279b0@0" ObjectIDZND0="28180@0" Pin0InfoVect0LinkObjId="SW-186018_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3f279b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1068,-711 1068,-670 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_390bdc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1102,-587 1069,-587 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_310fff0@0" ObjectIDZND0="g_39081c0@0" ObjectIDZND1="28181@x" Pin0InfoVect0LinkObjId="g_39081c0_0" Pin0InfoVect1LinkObjId="SW-186018_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_310fff0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1102,-587 1069,-587 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_390c8b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1069,-598 1069,-587 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_39081c0@0" ObjectIDZND0="g_310fff0@0" ObjectIDZND1="28181@x" Pin0InfoVect0LinkObjId="g_310fff0_0" Pin0InfoVect1LinkObjId="SW-186018_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_39081c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1069,-598 1069,-587 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_390cb10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1069,-587 1069,-575 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_310fff0@0" ObjectIDND1="g_39081c0@0" ObjectIDZND0="28181@1" Pin0InfoVect0LinkObjId="SW-186018_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_310fff0_0" Pin1InfoVect1LinkObjId="g_39081c0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1069,-587 1069,-575 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_390cd70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1069,-558 1069,-520 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28181@0" ObjectIDZND0="28145@0" Pin0InfoVect0LinkObjId="g_38ec490_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186018_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1069,-558 1069,-520 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3911740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="578,-633 578,-651 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_3910e40@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3910e40_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="578,-633 578,-651 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39147e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="577,-702 577,-661 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" ObjectIDND0="g_3f29740@0" ObjectIDZND0="28178@0" Pin0InfoVect0LinkObjId="SW-186014_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3f29740_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="577,-702 577,-661 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3914a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="611,-578 578,-578 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_390cfd0@0" ObjectIDZND0="g_3910e40@0" ObjectIDZND1="28179@x" Pin0InfoVect0LinkObjId="g_3910e40_0" Pin0InfoVect1LinkObjId="SW-186014_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_390cfd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="611,-578 578,-578 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3914ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="578,-589 578,-578 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_3910e40@0" ObjectIDZND0="g_390cfd0@0" ObjectIDZND1="28179@x" Pin0InfoVect0LinkObjId="g_390cfd0_0" Pin0InfoVect1LinkObjId="SW-186014_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3910e40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="578,-589 578,-578 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3914f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="578,-578 578,-566 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_390cfd0@0" ObjectIDND1="g_3910e40@0" ObjectIDZND0="28179@1" Pin0InfoVect0LinkObjId="SW-186014_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_390cfd0_0" Pin1InfoVect1LinkObjId="g_3910e40_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="578,-578 578,-566 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3918190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="706,-291 661,-291 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="706,-291 661,-291 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3918e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="658,-307 658,-319 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_39183f0@0" Pin0InfoVect0LinkObjId="g_39183f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3113730_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="658,-307 658,-319 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39190e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="64,-536 64,-518 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28171@0" ObjectIDZND0="28144@0" Pin0InfoVect0LinkObjId="g_3518500_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-185924_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="64,-536 64,-518 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3919340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="578,-549 578,-518 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28179@0" ObjectIDZND0="28144@0" Pin0InfoVect0LinkObjId="g_3518500_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186014_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="578,-549 578,-518 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39195a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="709,-478 709,-518 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28213@0" ObjectIDZND0="28144@0" Pin0InfoVect0LinkObjId="g_3518500_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186274_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="709,-478 709,-518 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3919800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="827,-618 839,-618 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28219@1" ObjectIDZND0="28218@1" Pin0InfoVect0LinkObjId="SW-186310_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186312_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="827,-618 839,-618 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3efb670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="866,-618 878,-618 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="28218@0" ObjectIDZND0="28220@1" Pin0InfoVect0LinkObjId="SW-186312_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186310_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="866,-618 878,-618 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3f07750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="945,-539 945,-520 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28222@0" ObjectIDZND0="28145@0" Pin0InfoVect0LinkObjId="g_38ec490_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186314_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="945,-539 945,-520 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3f07f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="945,-556 945,-579 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="28222@1" ObjectIDZND0="28221@1" Pin0InfoVect0LinkObjId="SW-186314_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186314_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="945,-556 945,-579 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3f081e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="945,-596 945,-619 896,-619 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="28221@0" ObjectIDZND0="28220@0" Pin0InfoVect0LinkObjId="SW-186312_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186314_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="945,-596 945,-619 896,-619 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3f08440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="810,-618 765,-618 765,-518 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28219@0" ObjectIDZND0="28144@0" Pin0InfoVect0LinkObjId="g_3518500_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186312_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="810,-618 765,-618 765,-518 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f10550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-150,-937 -150,-922 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="28173@0" ObjectIDZND0="g_35fda40@0" Pin0InfoVect0LinkObjId="g_35fda40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-185996_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-150,-937 -150,-922 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3f15340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="709,-265 709,-253 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="capacitor" ObjectIDND0="28217@x" ObjectIDND1="28215@x" ObjectIDZND0="39787@0" Pin0InfoVect0LinkObjId="CB-DY_BM.CX_DJ_Cb1_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-186278_0" Pin1InfoVect1LinkObjId="SW-186276_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="709,-265 709,-253 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3f15cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="757,-265 709,-265 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" EndDevType1="switch" ObjectIDND0="28217@0" ObjectIDZND0="39787@x" ObjectIDZND1="28215@x" Pin0InfoVect0LinkObjId="CB-DY_BM.CX_DJ_Cb1_0" Pin0InfoVect1LinkObjId="SW-186276_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186278_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="757,-265 709,-265 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3f15ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="709,-265 709,-276 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="39787@x" ObjectIDND1="28217@x" ObjectIDZND0="28215@0" Pin0InfoVect0LinkObjId="SW-186276_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="CB-DY_BM.CX_DJ_Cb1_0" Pin1InfoVect1LinkObjId="SW-186278_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="709,-265 709,-276 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3f2bb00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="709,-386 636,-386 636,-373 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="28216@x" ObjectIDND1="28214@x" ObjectIDND2="g_362f1f0@0" ObjectIDZND0="g_3f2bcf0@0" Pin0InfoVect0LinkObjId="g_3f2bcf0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-186277_0" Pin1InfoVect1LinkObjId="SW-186274_0" Pin1InfoVect2LinkObjId="g_362f1f0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="709,-386 636,-386 636,-373 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3f2dd90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="708,-158 708,-148 658,-148 658,-271 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" ObjectIDND0="39787@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_3113730_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="CB-DY_BM.CX_DJ_Cb1_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="708,-158 708,-148 658,-148 658,-271 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="28143" cx="1216" cy="-884" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28143" cx="992" cy="-884" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28143" cx="461" cy="-884" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28142" cx="-192" cy="-881" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28142" cx="320" cy="-881" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28142" cx="64" cy="-881" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28143" cx="736" cy="-884" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28142" cx="64" cy="-881" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28145" cx="1378" cy="-520" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28145" cx="1149" cy="-520" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28145" cx="945" cy="-520" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28144" cx="-144" cy="-518" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28144" cx="-320" cy="-518" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28144" cx="82" cy="-518" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28144" cx="307" cy="-518" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28144" cx="497" cy="-518" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28144" cx="64" cy="-518" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28144" cx="578" cy="-518" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28144" cx="709" cy="-518" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28144" cx="765" cy="-518" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28145" cx="1069" cy="-520" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-153553" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -453.500000 -1166.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26044" ObjectName="DYN-DY_BM"/>
     <cge:Meas_Ref ObjectId="153553"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimSun" font-size="20" graphid="g_3247830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -565.000000 -1243.500000) translate(0,16)">碧么变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_35fc720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -1102.000000) translate(0,17)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_35fc720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -1102.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_35fc720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -1102.000000) translate(0,59)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_35fc720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -1102.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_35fc720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -1102.000000) translate(0,101)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_35fc720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -1102.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_35fc720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -1102.000000) translate(0,143)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_35fc720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -1102.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_35fc720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -1102.000000) translate(0,185)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34a1a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -664.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34a1a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -664.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34a1a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -664.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34a1a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -664.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34a1a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -664.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34a1a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -664.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34a1a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -664.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34a1a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -664.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34a1a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -664.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34a1a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -664.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34a1a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -664.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34a1a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -664.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34a1a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -664.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34a1a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -664.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34a1a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -664.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34a1a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -664.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34a1a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -664.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34a1a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -664.000000) translate(0,374)">联系方式：0878-6148364</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35c4ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 528.000000 -742.000000) translate(0,12)">10kVⅠ段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35c6550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 948.096525 -1157.000000) translate(0,12)">35kV1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35c6ab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 713.096525 -1263.000000) translate(0,12)">北碧线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34a63d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 792.096525 -1256.000000) translate(0,12)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35b3e70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -164.000000 -178.000000) translate(0,12)">碧昙线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3618340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 651.000000 -141.000000) translate(0,12)">1号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35569d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1019.000000 -752.000000) translate(0,12)">10kVⅡ段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35ecdd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -366.903475 -183.000000) translate(0,12)">10kV2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35dc650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1150.000000 -1186.000000) translate(0,12)">35kVⅡ段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35a73e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.096525 -1264.000000) translate(0,12)">碧桂线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_360a660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 120.096525 -1255.000000) translate(0,12)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35311e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 62.000000 -175.000000) translate(0,12)">碧菜线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34de070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 287.000000 -174.000000) translate(0,12)">碧么电站并网线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e5af40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 477.000000 -175.000000) translate(0,12)">大水箐线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38e76a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1129.000000 -176.000000) translate(0,12)">大古衙线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38fc670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -175.000000) translate(0,12)">碧新线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f0bd10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -238.000000 -1191.000000) translate(0,12)">35kVⅠ段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f0c350" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 74.000000 -1000.000000) translate(0,12)">362</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f0c980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 71.000000 -937.000000) translate(0,12)">3621</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f0cbc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 71.000000 -1062.000000) translate(0,12)">3626</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f0ce00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 83.000000 -1158.000000) translate(0,12)">3629</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f0d0f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -28.000000 -1113.000000) translate(0,12)">36267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f0d610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -27.000000 -1049.000000) translate(0,12)">36260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f0d850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -28.000000 -987.000000) translate(0,12)">36217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f0da90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 746.000000 -1001.000000) translate(0,12)">363</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f0dcd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 755.000000 -1159.000000) translate(0,12)">3639</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f0df10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 743.000000 -1063.000000) translate(0,12)">3636</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f0e150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 743.000000 -938.000000) translate(0,12)">3632</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f0e390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 643.000000 -988.000000) translate(0,12)">36327</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f0e5d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 644.000000 -1050.000000) translate(0,12)">36360</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f0e810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 643.000000 -1114.000000) translate(0,12)">36367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f0ea50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 999.000000 -950.000000) translate(0,12)">3642</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f0ec90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1061.000000 -953.000000) translate(0,12)">36427</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f0eed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1223.000000 -1019.000000) translate(0,12)">3902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f0f110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1166.000000 -1090.000000) translate(0,12)">39027</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f0f350" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1265.000000 -958.000000) translate(0,12)">39020</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f0f590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 378.000000 -1002.000000) translate(0,12)">312</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f0f7d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 327.000000 -932.000000) translate(0,12)">3121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f0fa10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 468.000000 -932.000000) translate(0,12)">3122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f0fc50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 475.000000 -985.000000) translate(0,12)">31227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f0fe90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 267.000000 -984.000000) translate(0,12)">31217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f100d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -185.000000 -1023.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f10310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -143.000000 -962.000000) translate(0,12)">39010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f10740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -242.000000 -1094.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f109f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 74.000000 -806.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f10c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 71.000000 -857.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f10e70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 105.000000 -848.000000) translate(0,12)">30117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f110b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 74.000000 -586.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f112f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -264.000000 -869.000000) translate(0,12)">35kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f11530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1136.000000 -872.000000) translate(0,12)">35kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f11770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -164.000000 -541.000000) translate(0,12)">10kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f119b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1184.000000 -544.000000) translate(0,12)">10kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f11bf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 524.000000 -607.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f11e30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1080.000000 -669.000000) translate(0,12)">0902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f12560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 840.000000 -642.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f12880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 957.000000 -572.000000) translate(0,12)">0122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f12ac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -265.000000 -352.000000) translate(0,12)">06117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f12d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -303.000000 -445.000000) translate(0,12)">0611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f12f40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -135.000000 -445.000000) translate(0,12)">062</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f13180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -89.000000 -355.000000) translate(0,12)">06267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f133c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -137.000000 -277.000000) translate(0,12)">0626</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f13600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 92.000000 -442.000000) translate(0,12)">063</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f13840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 89.000000 -274.000000) translate(0,12)">0636</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f13a80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 137.000000 -352.000000) translate(0,12)">06367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f13cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 317.000000 -441.000000) translate(0,12)">064</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f13f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 314.000000 -273.000000) translate(0,12)">0646</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f14140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 362.000000 -351.000000) translate(0,12)">06467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f14380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 507.000000 -442.000000) translate(0,12)">065</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f145c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 504.000000 -274.000000) translate(0,12)">0656</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f14800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 552.000000 -352.000000) translate(0,12)">06567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f14a40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 719.000000 -443.000000) translate(0,12)">066</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f14c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 754.000000 -411.000000) translate(0,12)">06667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f14ec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 716.000000 -301.000000) translate(0,12)">0666</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f15100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 754.000000 -291.000000) translate(0,12)">06660</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f160b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1159.000000 -443.000000) translate(0,12)">067</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f16530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1204.000000 -353.000000) translate(0,12)">06767</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f16770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1156.000000 -275.000000) translate(0,12)">0676</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f169b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1388.000000 -442.000000) translate(0,12)">068</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f16c60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1433.000000 -352.000000) translate(0,12)">06867</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f171b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1385.000000 -274.000000) translate(0,12)">0686</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f212a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -131.000000 -715.000000) translate(0,12)">SZ11-5000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f212a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -131.000000 -715.000000) translate(0,27)">35±3×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f212a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -131.000000 -715.000000) translate(0,42)">Yd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f212a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -131.000000 -715.000000) translate(0,57)">6.97%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_3f23fe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -445.000000 -1226.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_3f25410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -445.000000 -1261.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f25ac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 377.000000 -960.000000) translate(0,12)">母联</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f2b4d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 210.000000 -271.000000) translate(0,12)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_3f2dfb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -503.500000 -1139.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3f2eff0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -686.000000 -831.000000) translate(0,16)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3f30240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -753.000000 -267.000000) translate(0,17)">姚安巡维中心：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3f31870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -599.000000 -277.500000) translate(0,17)">18787878958</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3f31870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -599.000000 -277.500000) translate(0,38)">18787878954</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f33400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -65.000000 -737.000000) translate(0,12)">1号主变</text>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" fillStyle="1" height="42" stroke="rgb(255,255,255)" stroke-width="1" width="72" x="-520" y="-1150"/>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-DY_BM.DY_BM_3ⅡM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="432,-884 1421,-884 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="28143" ObjectName="BS-DY_BM.DY_BM_3ⅡM"/>
    <cge:TPSR_Ref TObjectID="28143"/></metadata>
   <polyline fill="none" opacity="0" points="432,-884 1421,-884 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-DY_BM.DY_BM_3ⅠM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="350,-881 -355,-881 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="28142" ObjectName="BS-DY_BM.DY_BM_3ⅠM"/>
    <cge:TPSR_Ref TObjectID="28142"/></metadata>
   <polyline fill="none" opacity="0" points="350,-881 -355,-881 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-DY_BM.DY_BM_9ⅡM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="917,-520 1422,-520 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="28145" ObjectName="BS-DY_BM.DY_BM_9ⅡM"/>
    <cge:TPSR_Ref TObjectID="28145"/></metadata>
   <polyline fill="none" opacity="0" points="917,-520 1422,-520 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-DY_BM.DY_BM_9ⅠM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-359,-518 796,-518 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="28144" ObjectName="BS-DY_BM.DY_BM_9ⅠM"/>
    <cge:TPSR_Ref TObjectID="28144"/></metadata>
   <polyline fill="none" opacity="0" points="-359,-518 796,-518 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -335.000000 -192.000000)" xlink:href="#transformer2:shape25_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -335.000000 -192.000000)" xlink:href="#transformer2:shape25_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 977.000000 -1039.000000)" xlink:href="#transformer2:shape24_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 977.000000 -1039.000000)" xlink:href="#transformer2:shape24_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-DY_BM.DY_BM_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="40060"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(0.818182 -0.000000 0.000000 -0.862745 32.000000 -688.000000)" xlink:href="#transformer2:shape70_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(0.818182 -0.000000 0.000000 -0.862745 32.000000 -688.000000)" xlink:href="#transformer2:shape70_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="28228" ObjectName="TF-DY_BM.DY_BM_1T"/>
    <cge:TPSR_Ref TObjectID="28228"/></metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -657.000000 -1196.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-185560" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -594.000000 -1103.000000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185560" ObjectName="DY_BM:DY_BM_301BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-185560" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -594.000000 -1060.000000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185560" ObjectName="DY_BM:DY_BM_301BK_P"/>
    </metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="-605" y="-1254"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="-605" y="-1254"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-654" y="-1271"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-654" y="-1271"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="74" y="-1000"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="74" y="-1000"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="746" y="-1001"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="746" y="-1001"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="-135" y="-445"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="-135" y="-445"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="92" y="-442"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="92" y="-442"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="317" y="-441"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="317" y="-441"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="378" y="-1002"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="378" y="-1002"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="719" y="-443"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="719" y="-443"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1159" y="-443"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1159" y="-443"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1388" y="-442"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1388" y="-442"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="840" y="-642"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="840" y="-642"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="507" y="-442"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="507" y="-442"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="-456" y="-1234"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="-456" y="-1234"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="-456" y="-1269"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="-456" y="-1269"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="44" qtmmishow="hidden" width="73" x="-520" y="-1151"/>
    </a>
   <metadata/><rect fill="white" height="44" opacity="0" stroke="white" transform="" width="73" x="-520" y="-1151"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="80" x="-686" y="-831"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="80" x="-686" y="-831"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="52" x="-65" y="-737"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="52" x="-65" y="-737"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a30940" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1328.000000 981.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_331b9b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1322.000000 1034.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3514d60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1314.000000 946.000000) translate(0,12)">Ubc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36337a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1314.000000 928.000000) translate(0,12)">Uca(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35f4db0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1330.000000 911.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3512ea0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1322.000000 1015.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35f1ce0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1314.000000 964.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35f1f20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1322.000000 998.000000) translate(0,12)">Uc(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35dae00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1329.000000 617.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3893e60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1323.000000 670.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38940a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1315.000000 582.000000) translate(0,12)">Ubc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3112f70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1315.000000 564.000000) translate(0,12)">Uca(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3113170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1331.000000 547.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3562e80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1323.000000 651.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36172e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1315.000000 600.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3617520" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1323.000000 634.000000) translate(0,12)">Uc(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34566b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -341.000000 613.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3564320" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -347.000000 666.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3564560" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -355.000000 578.000000) translate(0,12)">Ubc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3555700" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -355.000000 560.000000) translate(0,12)">Uca(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3555910" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -339.000000 543.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34ea3f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -347.000000 647.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34ea5f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -355.000000 596.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_353d8c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -347.000000 630.000000) translate(0,12)">Uc(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3560df0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -161.000000 113.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35b05f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -161.000000 96.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35b0870" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -161.000000 78.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34a5fe0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -157.000000 61.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38c4f00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -175.000000 150.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_361b340" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -186.500000 131.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_353c770" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 807.000000 998.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_331c760" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 807.000000 981.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_331c9a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 807.000000 963.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35a5cb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 811.000000 946.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35a5ef0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 793.000000 1035.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a30560" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 781.500000 1016.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34bd570" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 139.000000 609.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34bd7f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 139.000000 592.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3564740" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 139.000000 574.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3564970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 143.000000 557.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35c1c80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 125.000000 646.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35c1e90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 113.500000 627.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35577c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 132.000000 744.000000) translate(0,12)">档位:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36095d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 102.500000 722.000000) translate(0,12)">油温(℃):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3582540" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 371.000000 1078.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34ed310" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 371.000000 1061.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34ed540" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 371.000000 1043.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38d5740" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 375.000000 1026.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38d5950" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 357.000000 1115.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3324570" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 345.500000 1096.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33248a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 682.000000 101.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34bdee0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 682.000000 84.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34be0f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 682.000000 66.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35f4610" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 656.500000 119.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35fe020" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -338.000000 980.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35fe510" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -344.000000 1033.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35cb800" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -352.000000 945.000000) translate(0,12)">Ubc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35cba40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -352.000000 927.000000) translate(0,12)">Uca(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35cbc80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -336.000000 910.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35cbec0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -344.000000 1014.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35bb920" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -352.000000 963.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35bbb30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -344.000000 997.000000) translate(0,12)">Uc(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35529e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 135.000000 997.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3552ed0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 135.000000 980.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31164d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 135.000000 962.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31166e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 139.000000 945.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3116920" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 121.000000 1034.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3116b60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 109.500000 1015.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34f4940" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 65.000000 110.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34f4e30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 65.000000 93.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34f5070" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 65.000000 75.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34f52b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 69.000000 58.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34f54f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 51.000000 147.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34f5730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 39.500000 128.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4d70170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 290.000000 109.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4d70660" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 290.000000 92.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4d708a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 290.000000 74.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4d70ae0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 294.000000 57.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4d70d20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 276.000000 146.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4d70f60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 264.500000 127.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e5fae0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 480.000000 110.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e60110" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 480.000000 93.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e60350" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 480.000000 75.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e60590" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 484.000000 58.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e607d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 466.000000 147.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e60a10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 454.500000 128.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38ec820" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1132.000000 111.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38ece50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1132.000000 94.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38ed090" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1132.000000 76.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38ed2d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1136.000000 59.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38ed510" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1118.000000 148.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38ed750" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1106.500000 129.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39014c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1361.000000 110.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3901af0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1361.000000 93.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3901d30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1361.000000 75.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3901f70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1365.000000 58.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39021b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1347.000000 147.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39023f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1335.500000 128.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f1d540" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 821.000000 715.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f1d7d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 821.000000 698.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f1da10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 821.000000 680.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f1dc50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 825.000000 663.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f1de90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 807.000000 752.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f1e0d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 795.500000 733.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f204d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -59.000000 829.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f20760" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -59.000000 812.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f209a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -59.000000 794.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f20be0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -55.000000 777.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f20e20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -73.000000 866.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f21060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -84.500000 847.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3f34740" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 79.000000 1226.000000) translate(0,12)">程</text>
    <circle DF8003:Layer="PUBLIC" cx="86" cy="1219" fill="none" fillStyle="0" r="10.5" stroke="rgb(255,0,0)" stroke-width="0.963841"/>
   <metadata/></g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-DY_BM.CX_DJ_Cb1">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 680.000000 -153.000000)" xlink:href="#capacitor:shape36"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39787" ObjectName="CB-DY_BM.CX_DJ_Cb1"/>
    <cge:TPSR_Ref TObjectID="39787"/></metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-185571" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -290.000000 -1035.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185571" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28142"/>
     <cge:Term_Ref ObjectID="39890"/>
    <cge:TPSR_Ref TObjectID="28142"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-185572" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -290.000000 -1035.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185572" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28142"/>
     <cge:Term_Ref ObjectID="39890"/>
    <cge:TPSR_Ref TObjectID="28142"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-185573" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -290.000000 -1035.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185573" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28142"/>
     <cge:Term_Ref ObjectID="39890"/>
    <cge:TPSR_Ref TObjectID="28142"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-185577" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -290.000000 -1035.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185577" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28142"/>
     <cge:Term_Ref ObjectID="39890"/>
    <cge:TPSR_Ref TObjectID="28142"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-185574" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -290.000000 -1035.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185574" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28142"/>
     <cge:Term_Ref ObjectID="39890"/>
    <cge:TPSR_Ref TObjectID="28142"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ubc" PreSymbol="0" appendix="" decimal="2" id="ME-185575" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -290.000000 -1035.000000) translate(0,105)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185575" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28142"/>
     <cge:Term_Ref ObjectID="39890"/>
    <cge:TPSR_Ref TObjectID="28142"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uca" PreSymbol="0" appendix="" decimal="2" id="ME-185576" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -290.000000 -1035.000000) translate(0,123)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185576" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28142"/>
     <cge:Term_Ref ObjectID="39890"/>
    <cge:TPSR_Ref TObjectID="28142"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-185578" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -290.000000 -1035.000000) translate(0,141)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185578" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28142"/>
     <cge:Term_Ref ObjectID="39890"/>
    <cge:TPSR_Ref TObjectID="28142"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-185579" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1378.000000 -1036.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185579" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28143"/>
     <cge:Term_Ref ObjectID="39891"/>
    <cge:TPSR_Ref TObjectID="28143"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-185580" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1378.000000 -1036.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185580" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28143"/>
     <cge:Term_Ref ObjectID="39891"/>
    <cge:TPSR_Ref TObjectID="28143"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-185581" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1378.000000 -1036.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185581" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28143"/>
     <cge:Term_Ref ObjectID="39891"/>
    <cge:TPSR_Ref TObjectID="28143"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-185585" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1378.000000 -1036.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185585" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28143"/>
     <cge:Term_Ref ObjectID="39891"/>
    <cge:TPSR_Ref TObjectID="28143"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-185582" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1378.000000 -1036.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185582" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28143"/>
     <cge:Term_Ref ObjectID="39891"/>
    <cge:TPSR_Ref TObjectID="28143"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ubc" PreSymbol="0" appendix="" decimal="2" id="ME-185583" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1378.000000 -1036.000000) translate(0,105)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185583" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28143"/>
     <cge:Term_Ref ObjectID="39891"/>
    <cge:TPSR_Ref TObjectID="28143"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uca" PreSymbol="0" appendix="" decimal="2" id="ME-185584" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1378.000000 -1036.000000) translate(0,123)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185584" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28143"/>
     <cge:Term_Ref ObjectID="39891"/>
    <cge:TPSR_Ref TObjectID="28143"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-185586" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1378.000000 -1036.000000) translate(0,141)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185586" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28143"/>
     <cge:Term_Ref ObjectID="39891"/>
    <cge:TPSR_Ref TObjectID="28143"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-185587" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -288.000000 -669.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185587" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28144"/>
     <cge:Term_Ref ObjectID="39892"/>
    <cge:TPSR_Ref TObjectID="28144"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-185588" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -288.000000 -669.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185588" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28144"/>
     <cge:Term_Ref ObjectID="39892"/>
    <cge:TPSR_Ref TObjectID="28144"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-185589" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -288.000000 -669.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185589" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28144"/>
     <cge:Term_Ref ObjectID="39892"/>
    <cge:TPSR_Ref TObjectID="28144"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-185593" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -288.000000 -669.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185593" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28144"/>
     <cge:Term_Ref ObjectID="39892"/>
    <cge:TPSR_Ref TObjectID="28144"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-185590" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -288.000000 -669.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185590" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28144"/>
     <cge:Term_Ref ObjectID="39892"/>
    <cge:TPSR_Ref TObjectID="28144"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ubc" PreSymbol="0" appendix="" decimal="2" id="ME-185591" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -288.000000 -669.000000) translate(0,105)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185591" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28144"/>
     <cge:Term_Ref ObjectID="39892"/>
    <cge:TPSR_Ref TObjectID="28144"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uca" PreSymbol="0" appendix="" decimal="2" id="ME-185592" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -288.000000 -669.000000) translate(0,123)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185592" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28144"/>
     <cge:Term_Ref ObjectID="39892"/>
    <cge:TPSR_Ref TObjectID="28144"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-185594" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -288.000000 -669.000000) translate(0,141)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185594" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28144"/>
     <cge:Term_Ref ObjectID="39892"/>
    <cge:TPSR_Ref TObjectID="28144"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-185595" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1381.000000 -674.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185595" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28145"/>
     <cge:Term_Ref ObjectID="39893"/>
    <cge:TPSR_Ref TObjectID="28145"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-185596" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1381.000000 -674.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185596" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28145"/>
     <cge:Term_Ref ObjectID="39893"/>
    <cge:TPSR_Ref TObjectID="28145"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-185597" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1381.000000 -674.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185597" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28145"/>
     <cge:Term_Ref ObjectID="39893"/>
    <cge:TPSR_Ref TObjectID="28145"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-185601" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1381.000000 -674.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185601" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28145"/>
     <cge:Term_Ref ObjectID="39893"/>
    <cge:TPSR_Ref TObjectID="28145"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-185598" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1381.000000 -674.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185598" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28145"/>
     <cge:Term_Ref ObjectID="39893"/>
    <cge:TPSR_Ref TObjectID="28145"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ubc" PreSymbol="0" appendix="" decimal="2" id="ME-185599" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1381.000000 -674.000000) translate(0,105)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185599" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28145"/>
     <cge:Term_Ref ObjectID="39893"/>
    <cge:TPSR_Ref TObjectID="28145"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uca" PreSymbol="0" appendix="" decimal="2" id="ME-185600" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1381.000000 -674.000000) translate(0,123)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185600" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28145"/>
     <cge:Term_Ref ObjectID="39893"/>
    <cge:TPSR_Ref TObjectID="28145"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-185602" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1381.000000 -674.000000) translate(0,141)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185602" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28145"/>
     <cge:Term_Ref ObjectID="39893"/>
    <cge:TPSR_Ref TObjectID="28145"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-185541" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 180.000000 -1035.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185541" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28147"/>
     <cge:Term_Ref ObjectID="39896"/>
    <cge:TPSR_Ref TObjectID="28147"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-185542" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 180.000000 -1035.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185542" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28147"/>
     <cge:Term_Ref ObjectID="39896"/>
    <cge:TPSR_Ref TObjectID="28147"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-185537" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 180.000000 -1035.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185537" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28147"/>
     <cge:Term_Ref ObjectID="39896"/>
    <cge:TPSR_Ref TObjectID="28147"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-185538" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 180.000000 -1035.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185538" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28147"/>
     <cge:Term_Ref ObjectID="39896"/>
    <cge:TPSR_Ref TObjectID="28147"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-185539" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 180.000000 -1035.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185539" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28147"/>
     <cge:Term_Ref ObjectID="39896"/>
    <cge:TPSR_Ref TObjectID="28147"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-185543" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 180.000000 -1035.000000) translate(0,105)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185543" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28147"/>
     <cge:Term_Ref ObjectID="39896"/>
    <cge:TPSR_Ref TObjectID="28147"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-185548" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 851.000000 -1035.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185548" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28154"/>
     <cge:Term_Ref ObjectID="39910"/>
    <cge:TPSR_Ref TObjectID="28154"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-185549" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 851.000000 -1035.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185549" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28154"/>
     <cge:Term_Ref ObjectID="39910"/>
    <cge:TPSR_Ref TObjectID="28154"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-185544" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 851.000000 -1035.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185544" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28154"/>
     <cge:Term_Ref ObjectID="39910"/>
    <cge:TPSR_Ref TObjectID="28154"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-185545" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 851.000000 -1035.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185545" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28154"/>
     <cge:Term_Ref ObjectID="39910"/>
    <cge:TPSR_Ref TObjectID="28154"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-185546" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 851.000000 -1035.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185546" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28154"/>
     <cge:Term_Ref ObjectID="39910"/>
    <cge:TPSR_Ref TObjectID="28154"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-185550" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 851.000000 -1035.000000) translate(0,105)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185550" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28154"/>
     <cge:Term_Ref ObjectID="39910"/>
    <cge:TPSR_Ref TObjectID="28154"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="3" id="ME-185566" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 185.000000 -646.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185566" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28169"/>
     <cge:Term_Ref ObjectID="39940"/>
    <cge:TPSR_Ref TObjectID="28169"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="3" id="ME-185567" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 185.000000 -646.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185567" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28169"/>
     <cge:Term_Ref ObjectID="39940"/>
    <cge:TPSR_Ref TObjectID="28169"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="3" id="ME-185563" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 185.000000 -646.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185563" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28169"/>
     <cge:Term_Ref ObjectID="39940"/>
    <cge:TPSR_Ref TObjectID="28169"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="3" id="ME-185564" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 185.000000 -646.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185564" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28169"/>
     <cge:Term_Ref ObjectID="39940"/>
    <cge:TPSR_Ref TObjectID="28169"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="3" id="ME-185565" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 185.000000 -646.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185565" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28169"/>
     <cge:Term_Ref ObjectID="39940"/>
    <cge:TPSR_Ref TObjectID="28169"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="3" id="ME-185568" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 185.000000 -646.000000) translate(0,105)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185568" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28169"/>
     <cge:Term_Ref ObjectID="39940"/>
    <cge:TPSR_Ref TObjectID="28169"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-185606" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -117.000000 -154.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185606" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28182"/>
     <cge:Term_Ref ObjectID="39966"/>
    <cge:TPSR_Ref TObjectID="28182"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-185607" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -117.000000 -154.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185607" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28182"/>
     <cge:Term_Ref ObjectID="39966"/>
    <cge:TPSR_Ref TObjectID="28182"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-185603" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -117.000000 -154.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185603" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28182"/>
     <cge:Term_Ref ObjectID="39966"/>
    <cge:TPSR_Ref TObjectID="28182"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-185604" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -117.000000 -154.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185604" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28182"/>
     <cge:Term_Ref ObjectID="39966"/>
    <cge:TPSR_Ref TObjectID="28182"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-185605" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -117.000000 -154.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185605" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28182"/>
     <cge:Term_Ref ObjectID="39966"/>
    <cge:TPSR_Ref TObjectID="28182"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-185608" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -117.000000 -154.000000) translate(0,105)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185608" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28182"/>
     <cge:Term_Ref ObjectID="39966"/>
    <cge:TPSR_Ref TObjectID="28182"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-185612" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 109.000000 -151.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185612" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28187"/>
     <cge:Term_Ref ObjectID="39976"/>
    <cge:TPSR_Ref TObjectID="28187"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-185613" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 109.000000 -151.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185613" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28187"/>
     <cge:Term_Ref ObjectID="39976"/>
    <cge:TPSR_Ref TObjectID="28187"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-185609" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 109.000000 -151.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185609" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28187"/>
     <cge:Term_Ref ObjectID="39976"/>
    <cge:TPSR_Ref TObjectID="28187"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-185610" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 109.000000 -151.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185610" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28187"/>
     <cge:Term_Ref ObjectID="39976"/>
    <cge:TPSR_Ref TObjectID="28187"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-185611" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 109.000000 -151.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185611" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28187"/>
     <cge:Term_Ref ObjectID="39976"/>
    <cge:TPSR_Ref TObjectID="28187"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-185614" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 109.000000 -151.000000) translate(0,105)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185614" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28187"/>
     <cge:Term_Ref ObjectID="39976"/>
    <cge:TPSR_Ref TObjectID="28187"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-185618" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 336.000000 -148.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185618" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28192"/>
     <cge:Term_Ref ObjectID="39986"/>
    <cge:TPSR_Ref TObjectID="28192"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-185619" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 336.000000 -148.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185619" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28192"/>
     <cge:Term_Ref ObjectID="39986"/>
    <cge:TPSR_Ref TObjectID="28192"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-185615" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 336.000000 -148.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185615" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28192"/>
     <cge:Term_Ref ObjectID="39986"/>
    <cge:TPSR_Ref TObjectID="28192"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-185616" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 336.000000 -148.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185616" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28192"/>
     <cge:Term_Ref ObjectID="39986"/>
    <cge:TPSR_Ref TObjectID="28192"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-185617" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 336.000000 -148.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185617" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28192"/>
     <cge:Term_Ref ObjectID="39986"/>
    <cge:TPSR_Ref TObjectID="28192"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-185620" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 336.000000 -148.000000) translate(0,105)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185620" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28192"/>
     <cge:Term_Ref ObjectID="39986"/>
    <cge:TPSR_Ref TObjectID="28192"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-185624" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 523.000000 -148.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185624" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28197"/>
     <cge:Term_Ref ObjectID="39996"/>
    <cge:TPSR_Ref TObjectID="28197"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-185625" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 523.000000 -148.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185625" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28197"/>
     <cge:Term_Ref ObjectID="39996"/>
    <cge:TPSR_Ref TObjectID="28197"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-185621" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 523.000000 -148.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185621" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28197"/>
     <cge:Term_Ref ObjectID="39996"/>
    <cge:TPSR_Ref TObjectID="28197"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-185622" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 523.000000 -148.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185622" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28197"/>
     <cge:Term_Ref ObjectID="39996"/>
    <cge:TPSR_Ref TObjectID="28197"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-185623" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 523.000000 -148.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185623" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28197"/>
     <cge:Term_Ref ObjectID="39996"/>
    <cge:TPSR_Ref TObjectID="28197"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-185626" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 523.000000 -148.000000) translate(0,105)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185626" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28197"/>
     <cge:Term_Ref ObjectID="39996"/>
    <cge:TPSR_Ref TObjectID="28197"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-185630" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1178.000000 -147.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185630" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28202"/>
     <cge:Term_Ref ObjectID="40006"/>
    <cge:TPSR_Ref TObjectID="28202"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-185631" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1178.000000 -147.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185631" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28202"/>
     <cge:Term_Ref ObjectID="40006"/>
    <cge:TPSR_Ref TObjectID="28202"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-185627" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1178.000000 -147.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185627" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28202"/>
     <cge:Term_Ref ObjectID="40006"/>
    <cge:TPSR_Ref TObjectID="28202"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-185628" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1178.000000 -147.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185628" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28202"/>
     <cge:Term_Ref ObjectID="40006"/>
    <cge:TPSR_Ref TObjectID="28202"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-185629" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1178.000000 -147.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185629" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28202"/>
     <cge:Term_Ref ObjectID="40006"/>
    <cge:TPSR_Ref TObjectID="28202"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-185632" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1178.000000 -147.000000) translate(0,105)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185632" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28202"/>
     <cge:Term_Ref ObjectID="40006"/>
    <cge:TPSR_Ref TObjectID="28202"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-185636" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1408.000000 -149.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185636" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28207"/>
     <cge:Term_Ref ObjectID="40016"/>
    <cge:TPSR_Ref TObjectID="28207"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-185637" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1408.000000 -149.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185637" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28207"/>
     <cge:Term_Ref ObjectID="40016"/>
    <cge:TPSR_Ref TObjectID="28207"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-185633" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1408.000000 -149.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185633" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28207"/>
     <cge:Term_Ref ObjectID="40016"/>
    <cge:TPSR_Ref TObjectID="28207"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-185634" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1408.000000 -149.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185634" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28207"/>
     <cge:Term_Ref ObjectID="40016"/>
    <cge:TPSR_Ref TObjectID="28207"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-185635" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1408.000000 -149.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185635" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28207"/>
     <cge:Term_Ref ObjectID="40016"/>
    <cge:TPSR_Ref TObjectID="28207"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-185638" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1408.000000 -149.000000) translate(0,105)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185638" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28207"/>
     <cge:Term_Ref ObjectID="40016"/>
    <cge:TPSR_Ref TObjectID="28207"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-185554" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 413.000000 -1115.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185554" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28161"/>
     <cge:Term_Ref ObjectID="39924"/>
    <cge:TPSR_Ref TObjectID="28161"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-185555" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 413.000000 -1115.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185555" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28161"/>
     <cge:Term_Ref ObjectID="39924"/>
    <cge:TPSR_Ref TObjectID="28161"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-185551" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 413.000000 -1115.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185551" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28161"/>
     <cge:Term_Ref ObjectID="39924"/>
    <cge:TPSR_Ref TObjectID="28161"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-185552" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 413.000000 -1115.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185552" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28161"/>
     <cge:Term_Ref ObjectID="39924"/>
    <cge:TPSR_Ref TObjectID="28161"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-185553" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 413.000000 -1115.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185553" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28161"/>
     <cge:Term_Ref ObjectID="39924"/>
    <cge:TPSR_Ref TObjectID="28161"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-185556" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 413.000000 -1115.000000) translate(0,105)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185556" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28161"/>
     <cge:Term_Ref ObjectID="39924"/>
    <cge:TPSR_Ref TObjectID="28161"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-185647" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 867.000000 -754.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185647" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28218"/>
     <cge:Term_Ref ObjectID="40038"/>
    <cge:TPSR_Ref TObjectID="28218"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-185648" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 867.000000 -754.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185648" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28218"/>
     <cge:Term_Ref ObjectID="40038"/>
    <cge:TPSR_Ref TObjectID="28218"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-185644" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 867.000000 -754.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185644" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28218"/>
     <cge:Term_Ref ObjectID="40038"/>
    <cge:TPSR_Ref TObjectID="28218"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-185645" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 867.000000 -754.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185645" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28218"/>
     <cge:Term_Ref ObjectID="40038"/>
    <cge:TPSR_Ref TObjectID="28218"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-185646" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 867.000000 -754.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185646" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28218"/>
     <cge:Term_Ref ObjectID="40038"/>
    <cge:TPSR_Ref TObjectID="28218"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-185649" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 867.000000 -754.000000) translate(0,105)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185649" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28218"/>
     <cge:Term_Ref ObjectID="40038"/>
    <cge:TPSR_Ref TObjectID="28218"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-185642" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 727.000000 -122.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185642" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28212"/>
     <cge:Term_Ref ObjectID="40026"/>
    <cge:TPSR_Ref TObjectID="28212"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-185639" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 727.000000 -122.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185639" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28212"/>
     <cge:Term_Ref ObjectID="40026"/>
    <cge:TPSR_Ref TObjectID="28212"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-185640" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 727.000000 -122.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185640" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28212"/>
     <cge:Term_Ref ObjectID="40026"/>
    <cge:TPSR_Ref TObjectID="28212"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-185641" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 727.000000 -122.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185641" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28212"/>
     <cge:Term_Ref ObjectID="40026"/>
    <cge:TPSR_Ref TObjectID="28212"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-185569" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 177.000000 -745.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185569" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28228"/>
     <cge:Term_Ref ObjectID="40058"/>
    <cge:TPSR_Ref TObjectID="28228"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-185570" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 176.000000 -723.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185570" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28228"/>
     <cge:Term_Ref ObjectID="40061"/>
    <cge:TPSR_Ref TObjectID="28228"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-185560" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -12.000000 -868.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185560" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28166"/>
     <cge:Term_Ref ObjectID="39934"/>
    <cge:TPSR_Ref TObjectID="28166"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-185561" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -12.000000 -868.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185561" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28166"/>
     <cge:Term_Ref ObjectID="39934"/>
    <cge:TPSR_Ref TObjectID="28166"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-185557" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -12.000000 -868.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185557" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28166"/>
     <cge:Term_Ref ObjectID="39934"/>
    <cge:TPSR_Ref TObjectID="28166"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-185558" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -12.000000 -868.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185558" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28166"/>
     <cge:Term_Ref ObjectID="39934"/>
    <cge:TPSR_Ref TObjectID="28166"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-185559" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -12.000000 -868.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185559" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28166"/>
     <cge:Term_Ref ObjectID="39934"/>
    <cge:TPSR_Ref TObjectID="28166"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-185562" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -12.000000 -868.000000) translate(0,105)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185562" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28166"/>
     <cge:Term_Ref ObjectID="39934"/>
    <cge:TPSR_Ref TObjectID="28166"/></metadata>
   </g>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-185763">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 727.241796 -1033.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28156" ObjectName="SW-DY_BM.DY_BM_3636SW"/>
     <cge:Meas_Ref ObjectId="185763"/>
    <cge:TPSR_Ref TObjectID="28156"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-185761">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 727.241796 -908.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28155" ObjectName="SW-DY_BM.DY_BM_3632SW"/>
     <cge:Meas_Ref ObjectId="185761"/>
    <cge:TPSR_Ref TObjectID="28155"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-185768">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 642.152542 -1018.636364)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28159" ObjectName="SW-DY_BM.DY_BM_36360SW"/>
     <cge:Meas_Ref ObjectId="185768"/>
    <cge:TPSR_Ref TObjectID="28159"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-185769">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 641.152542 -1082.636364)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28160" ObjectName="SW-DY_BM.DY_BM_36367SW"/>
     <cge:Meas_Ref ObjectId="185769"/>
    <cge:TPSR_Ref TObjectID="28160"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-185767">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 641.152542 -956.636364)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28158" ObjectName="SW-DY_BM.DY_BM_36327SW"/>
     <cge:Meas_Ref ObjectId="185767"/>
    <cge:TPSR_Ref TObjectID="28158"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186034">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -154.000000 -456.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28183" ObjectName="SW-DY_BM.DY_BM_062XC"/>
     <cge:Meas_Ref ObjectId="186034"/>
    <cge:TPSR_Ref TObjectID="28183"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186034">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -154.000000 -388.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28184" ObjectName="SW-DY_BM.DY_BM_062XC1"/>
     <cge:Meas_Ref ObjectId="186034"/>
    <cge:TPSR_Ref TObjectID="28184"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186036">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -153.238710 -247.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28185" ObjectName="SW-DY_BM.DY_BM_0626SW"/>
     <cge:Meas_Ref ObjectId="186036"/>
    <cge:TPSR_Ref TObjectID="28185"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186037">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -105.238710 -325.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28186" ObjectName="SW-DY_BM.DY_BM_06267SW"/>
     <cge:Meas_Ref ObjectId="186037"/>
    <cge:TPSR_Ref TObjectID="28186"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186274">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 699.000000 -454.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28213" ObjectName="SW-DY_BM.DY_BM_066XC"/>
     <cge:Meas_Ref ObjectId="186274"/>
    <cge:TPSR_Ref TObjectID="28213"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186274">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 699.000000 -386.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28214" ObjectName="SW-DY_BM.DY_BM_066XC1"/>
     <cge:Meas_Ref ObjectId="186274"/>
    <cge:TPSR_Ref TObjectID="28214"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186276">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 699.761290 -271.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28215" ObjectName="SW-DY_BM.DY_BM_0666SW"/>
     <cge:Meas_Ref ObjectId="186276"/>
    <cge:TPSR_Ref TObjectID="28215"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186277">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 752.152542 -379.636364)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28216" ObjectName="SW-DY_BM.DY_BM_06667SW"/>
     <cge:Meas_Ref ObjectId="186277"/>
    <cge:TPSR_Ref TObjectID="28216"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186278">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 752.152542 -259.636364)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28217" ObjectName="SW-DY_BM.DY_BM_06660SW"/>
     <cge:Meas_Ref ObjectId="186278"/>
    <cge:TPSR_Ref TObjectID="28217"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-185765">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 749.152542 -1127.636364)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28157" ObjectName="SW-DY_BM.DY_BM_3639SW"/>
     <cge:Meas_Ref ObjectId="185765"/>
    <cge:TPSR_Ref TObjectID="28157"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186367">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 983.241796 -920.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28223" ObjectName="SW-DY_BM.DY_BM_3642SW"/>
     <cge:Meas_Ref ObjectId="186367"/>
    <cge:TPSR_Ref TObjectID="28223"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186369">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1044.761290 -923.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28224" ObjectName="SW-DY_BM.DY_BM_36427SW"/>
     <cge:Meas_Ref ObjectId="186369"/>
    <cge:TPSR_Ref TObjectID="28224"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186007">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1149.761290 -1060.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28177" ObjectName="SW-DY_BM.DY_BM_39027SW"/>
     <cge:Meas_Ref ObjectId="186007"/>
    <cge:TPSR_Ref TObjectID="28177"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186004">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1207.241796 -989.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28175" ObjectName="SW-DY_BM.DY_BM_3902SW"/>
     <cge:Meas_Ref ObjectId="186004"/>
    <cge:TPSR_Ref TObjectID="28175"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186006">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1248.761290 -928.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28176" ObjectName="SW-DY_BM.DY_BM_39020SW"/>
     <cge:Meas_Ref ObjectId="186006"/>
    <cge:TPSR_Ref TObjectID="28176"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-185997">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -258.238710 -1064.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28174" ObjectName="SW-DY_BM.DY_BM_39017SW"/>
     <cge:Meas_Ref ObjectId="185997"/>
    <cge:TPSR_Ref TObjectID="28174"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-185994">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -200.758204 -993.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28172" ObjectName="SW-DY_BM.DY_BM_3901SW"/>
     <cge:Meas_Ref ObjectId="185994"/>
    <cge:TPSR_Ref TObjectID="28172"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-185996">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -159.238710 -932.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28173" ObjectName="SW-DY_BM.DY_BM_39010SW"/>
     <cge:Meas_Ref ObjectId="185996"/>
    <cge:TPSR_Ref TObjectID="28173"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-185695">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 55.241796 -1032.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28149" ObjectName="SW-DY_BM.DY_BM_3626SW"/>
     <cge:Meas_Ref ObjectId="185695"/>
    <cge:TPSR_Ref TObjectID="28149"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-185693">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 55.241796 -907.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28148" ObjectName="SW-DY_BM.DY_BM_3621SW"/>
     <cge:Meas_Ref ObjectId="185693"/>
    <cge:TPSR_Ref TObjectID="28148"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-185700">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -29.847458 -1017.636364)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28152" ObjectName="SW-DY_BM.DY_BM_36260SW"/>
     <cge:Meas_Ref ObjectId="185700"/>
    <cge:TPSR_Ref TObjectID="28152"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-185701">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -30.847458 -1081.636364)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28153" ObjectName="SW-DY_BM.DY_BM_36267SW"/>
     <cge:Meas_Ref ObjectId="185701"/>
    <cge:TPSR_Ref TObjectID="28153"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-185699">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -30.847458 -955.636364)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28151" ObjectName="SW-DY_BM.DY_BM_36217SW"/>
     <cge:Meas_Ref ObjectId="185699"/>
    <cge:TPSR_Ref TObjectID="28151"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-185697">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 77.152542 -1126.636364)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28150" ObjectName="SW-DY_BM.DY_BM_3629SW"/>
     <cge:Meas_Ref ObjectId="185697"/>
    <cge:TPSR_Ref TObjectID="28150"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-185831">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 452.241796 -902.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28163" ObjectName="SW-DY_BM.DY_BM_3122SW"/>
     <cge:Meas_Ref ObjectId="185831"/>
    <cge:TPSR_Ref TObjectID="28163"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-185829">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 311.241796 -902.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28162" ObjectName="SW-DY_BM.DY_BM_3121SW"/>
     <cge:Meas_Ref ObjectId="185829"/>
    <cge:TPSR_Ref TObjectID="28162"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-185833">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 264.152542 -952.636364)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28164" ObjectName="SW-DY_BM.DY_BM_31217SW"/>
     <cge:Meas_Ref ObjectId="185833"/>
    <cge:TPSR_Ref TObjectID="28164"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-185834">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 472.152542 -953.636364)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28165" ObjectName="SW-DY_BM.DY_BM_31227SW"/>
     <cge:Meas_Ref ObjectId="185834"/>
    <cge:TPSR_Ref TObjectID="28165"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-185900">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 55.241796 -827.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28167" ObjectName="SW-DY_BM.DY_BM_3011SW"/>
     <cge:Meas_Ref ObjectId="185900"/>
    <cge:TPSR_Ref TObjectID="28167"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-185902">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 102.152542 -816.636364)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28168" ObjectName="SW-DY_BM.DY_BM_30117SW"/>
     <cge:Meas_Ref ObjectId="185902"/>
    <cge:TPSR_Ref TObjectID="28168"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-185924">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 54.000000 -596.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28170" ObjectName="SW-DY_BM.DY_BM_001XC"/>
     <cge:Meas_Ref ObjectId="185924"/>
    <cge:TPSR_Ref TObjectID="28170"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-185924">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 54.000000 -529.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28171" ObjectName="SW-DY_BM.DY_BM_001XC1"/>
     <cge:Meas_Ref ObjectId="185924"/>
    <cge:TPSR_Ref TObjectID="28171"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186374">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -330.000000 -381.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28226" ObjectName="SW-DY_BM.DY_BM_0611XC1"/>
     <cge:Meas_Ref ObjectId="186374"/>
    <cge:TPSR_Ref TObjectID="28226"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186374">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -330.000000 -464.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28225" ObjectName="SW-DY_BM.DY_BM_0611XC"/>
     <cge:Meas_Ref ObjectId="186374"/>
    <cge:TPSR_Ref TObjectID="28225"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186376">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -281.238710 -322.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28227" ObjectName="SW-DY_BM.DY_BM_06117SW"/>
     <cge:Meas_Ref ObjectId="186376"/>
    <cge:TPSR_Ref TObjectID="28227"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186074">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 72.000000 -453.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28188" ObjectName="SW-DY_BM.DY_BM_063XC"/>
     <cge:Meas_Ref ObjectId="186074"/>
    <cge:TPSR_Ref TObjectID="28188"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186074">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 72.000000 -385.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28189" ObjectName="SW-DY_BM.DY_BM_063XC1"/>
     <cge:Meas_Ref ObjectId="186074"/>
    <cge:TPSR_Ref TObjectID="28189"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186076">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 72.761290 -244.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28190" ObjectName="SW-DY_BM.DY_BM_0636SW"/>
     <cge:Meas_Ref ObjectId="186076"/>
    <cge:TPSR_Ref TObjectID="28190"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186077">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 120.761290 -322.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28191" ObjectName="SW-DY_BM.DY_BM_06367SW"/>
     <cge:Meas_Ref ObjectId="186077"/>
    <cge:TPSR_Ref TObjectID="28191"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186116">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 297.761290 -243.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28195" ObjectName="SW-DY_BM.DY_BM_0646SW"/>
     <cge:Meas_Ref ObjectId="186116"/>
    <cge:TPSR_Ref TObjectID="28195"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186117">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 345.761290 -321.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28196" ObjectName="SW-DY_BM.DY_BM_06467SW"/>
     <cge:Meas_Ref ObjectId="186117"/>
    <cge:TPSR_Ref TObjectID="28196"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186114">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 297.000000 -452.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28193" ObjectName="SW-DY_BM.DY_BM_064XC"/>
     <cge:Meas_Ref ObjectId="186114"/>
    <cge:TPSR_Ref TObjectID="28193"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186114">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 297.000000 -384.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28194" ObjectName="SW-DY_BM.DY_BM_064XC1"/>
     <cge:Meas_Ref ObjectId="186114"/>
    <cge:TPSR_Ref TObjectID="28194"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186154">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 487.000000 -453.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28198" ObjectName="SW-DY_BM.DY_BM_065XC"/>
     <cge:Meas_Ref ObjectId="186154"/>
    <cge:TPSR_Ref TObjectID="28198"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186154">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 487.000000 -385.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28199" ObjectName="SW-DY_BM.DY_BM_065XC1"/>
     <cge:Meas_Ref ObjectId="186154"/>
    <cge:TPSR_Ref TObjectID="28199"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186156">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 487.761290 -244.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28200" ObjectName="SW-DY_BM.DY_BM_0656SW"/>
     <cge:Meas_Ref ObjectId="186156"/>
    <cge:TPSR_Ref TObjectID="28200"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186157">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 535.761290 -322.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28201" ObjectName="SW-DY_BM.DY_BM_06567SW"/>
     <cge:Meas_Ref ObjectId="186157"/>
    <cge:TPSR_Ref TObjectID="28201"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186194">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1139.000000 -454.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28203" ObjectName="SW-DY_BM.DY_BM_067XC"/>
     <cge:Meas_Ref ObjectId="186194"/>
    <cge:TPSR_Ref TObjectID="28203"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186194">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1139.000000 -386.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28204" ObjectName="SW-DY_BM.DY_BM_067XC1"/>
     <cge:Meas_Ref ObjectId="186194"/>
    <cge:TPSR_Ref TObjectID="28204"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186196">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1139.761290 -245.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28205" ObjectName="SW-DY_BM.DY_BM_0676SW"/>
     <cge:Meas_Ref ObjectId="186196"/>
    <cge:TPSR_Ref TObjectID="28205"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186197">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1187.761290 -323.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28206" ObjectName="SW-DY_BM.DY_BM_06767SW"/>
     <cge:Meas_Ref ObjectId="186197"/>
    <cge:TPSR_Ref TObjectID="28206"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186234">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1368.000000 -453.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28208" ObjectName="SW-DY_BM.DY_BM_068XC"/>
     <cge:Meas_Ref ObjectId="186234"/>
    <cge:TPSR_Ref TObjectID="28208"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186234">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1368.000000 -385.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28209" ObjectName="SW-DY_BM.DY_BM_068XC1"/>
     <cge:Meas_Ref ObjectId="186234"/>
    <cge:TPSR_Ref TObjectID="28209"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186236">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1368.761290 -244.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28210" ObjectName="SW-DY_BM.DY_BM_0686SW"/>
     <cge:Meas_Ref ObjectId="186236"/>
    <cge:TPSR_Ref TObjectID="28210"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186237">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1416.761290 -322.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28211" ObjectName="SW-DY_BM.DY_BM_06867SW"/>
     <cge:Meas_Ref ObjectId="186237"/>
    <cge:TPSR_Ref TObjectID="28211"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186018">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1059.000000 -551.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28181" ObjectName="SW-DY_BM.DY_BM_0902XC1"/>
     <cge:Meas_Ref ObjectId="186018"/>
    <cge:TPSR_Ref TObjectID="28181"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186018">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1058.000000 -646.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28180" ObjectName="SW-DY_BM.DY_BM_0902XC"/>
     <cge:Meas_Ref ObjectId="186018"/>
    <cge:TPSR_Ref TObjectID="28180"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186014">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 568.000000 -542.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28179" ObjectName="SW-DY_BM.DY_BM_0901XC1"/>
     <cge:Meas_Ref ObjectId="186014"/>
    <cge:TPSR_Ref TObjectID="28179"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186014">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 567.000000 -637.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28178" ObjectName="SW-DY_BM.DY_BM_0901XC"/>
     <cge:Meas_Ref ObjectId="186014"/>
    <cge:TPSR_Ref TObjectID="28178"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 667.000000 -312.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186312">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 834.500000 -608.500000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28219" ObjectName="SW-DY_BM.DY_BM_012XC"/>
     <cge:Meas_Ref ObjectId="186312"/>
    <cge:TPSR_Ref TObjectID="28219"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186312">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 903.500000 -609.500000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28220" ObjectName="SW-DY_BM.DY_BM_012XC1"/>
     <cge:Meas_Ref ObjectId="186312"/>
    <cge:TPSR_Ref TObjectID="28220"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186314">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 935.000000 -572.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28221" ObjectName="SW-DY_BM.DY_BM_0122XC"/>
     <cge:Meas_Ref ObjectId="186314"/>
    <cge:TPSR_Ref TObjectID="28221"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186314">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 935.000000 -532.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28222" ObjectName="SW-DY_BM.DY_BM_0122XC1"/>
     <cge:Meas_Ref ObjectId="186314"/>
    <cge:TPSR_Ref TObjectID="28222"/></metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="-605" y="-1254"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-654" y="-1271"/></g>
   <g href="35kV碧么变DY_BM_362间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="74" y="-1000"/></g>
   <g href="大姚35kV碧么变DY_BM_363间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="746" y="-1001"/></g>
   <g href="35kV碧么变DY_BM_062间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="-135" y="-445"/></g>
   <g href="35kV碧么变10kV碧菜线063断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="92" y="-442"/></g>
   <g href="35kV碧么变10kV碧么电站并网线064断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="317" y="-441"/></g>
   <g href="大姚35kV碧么变DY_BM_312间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="378" y="-1002"/></g>
   <g href="35kV碧么变10kV1号电容器066断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="719" y="-443"/></g>
   <g href="35kV碧么变10kV大古衙线067断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1159" y="-443"/></g>
   <g href="35kV碧么变10kV碧新线068断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1388" y="-442"/></g>
   <g href="35kV碧么变10kV分段012断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="840" y="-642"/></g>
   <g href="35kV碧么变10kV大水箐线065断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="507" y="-442"/></g>
   <g href="cx_配调_配网接线图35_大姚.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="-456" y="-1234"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="-456" y="-1269"/></g>
   <g href="AVC碧么站.svg" style="fill-opacity:0"><rect height="44" qtmmishow="hidden" width="73" x="-520" y="-1151"/></g>
   <g href="35kV碧么变GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="80" x="-686" y="-831"/></g>
   <g href="35kV碧么变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="52" x="-65" y="-737"/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_3638770">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 697.000000 -1166.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35c2de0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -149.000000 -301.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35da230">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 -120.323558 -184.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_362f1f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 704.000000 -318.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_310fff0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1095.000000 -583.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3625dd0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 804.000000 -1148.000000)" xlink:href="#lightningRod:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35d6660">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 985.000000 -976.000000)" xlink:href="#lightningRod:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3e6d5b0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1209.000000 -1069.000000)" xlink:href="#lightningRod:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3502430">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1241.000000 -1065.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_38d4170">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -199.000000 -1073.000000)" xlink:href="#lightningRod:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3605310">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -167.000000 -1069.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3561370">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 25.000000 -1165.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35b58d0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 132.000000 -1147.000000)" xlink:href="#lightningRod:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3469fa0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 54.000000 -635.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3898670">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -16.000000 -622.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_351df30">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -327.000000 -411.000000)" xlink:href="#lightningRod:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3317f10">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -325.000000 -298.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35078d0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 -360.323558 -306.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3508ac0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 -185.323558 -309.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_345a7f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 77.000000 -298.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35304b0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 105.676442 -181.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34f5b60">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 40.676442 -306.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34f9e50">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 302.000000 -297.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34dd340">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 330.676442 -180.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4d711a0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 265.676442 -305.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_387e4d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 221.000000 -312.000000)" xlink:href="#lightningRod:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_38890c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 492.000000 -298.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3e5a310">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 520.676442 -181.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3e60e40">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 455.676442 -306.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3e6a890">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1144.000000 -299.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_38e6970">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1172.676442 -182.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_38edb80">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1107.676442 -307.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_38f7cd0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1373.000000 -298.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_38fb940">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1401.676442 -181.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3902820">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1336.676442 -306.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_39081c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1062.000000 -593.000000)" xlink:href="#lightningRod:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_390cfd0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 604.000000 -574.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3910e40">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 571.000000 -584.000000)" xlink:href="#lightningRod:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f2bcf0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 628.676442 -319.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="DY_BM"/>
</svg>