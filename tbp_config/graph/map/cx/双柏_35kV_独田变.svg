<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-340" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="-184 -1164 2342 1415">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="capacitor:shape15">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.370253" x1="2" x2="2" y1="45" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="47" x2="47" y1="16" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="41" x2="53" y1="16" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="51" x2="43" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="50" x2="47" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="38" x2="18" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="38" x2="18" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72286" x1="28" x2="28" y1="14" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="11" x2="11" y1="2" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="46" x2="46" y1="2" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="0.607143" x1="47" x2="47" y1="54" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.338133" x1="46" x2="12" y1="55" y2="55"/>
    <rect height="23" stroke-width="0.398039" width="12" x="22" y="27"/>
    <rect height="24" stroke-width="0.398039" width="12" x="41" y="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.395152" x1="28" x2="13" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.368819" x1="45" x2="12" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.42985" x1="28" x2="28" y1="88" y2="21"/>
    <polyline arcFlag="1" points="11,36 10,36 9,36 9,37 8,37 8,37 7,38 7,38 6,39 6,39 6,40 6,40 5,41 5,42 5,42 6,43 6,44 6,44 6,45 7,45 7,46 8,46 8,47 9,47 9,47 10,47 11,47 " stroke-width="1"/>
    <polyline arcFlag="1" points="11,25 10,25 9,25 9,25 8,26 8,26 7,26 7,27 6,27 6,28 6,29 6,29 5,30 5,31 5,31 6,32 6,33 6,33 6,34 7,34 7,35 8,35 8,35 9,36 9,36 10,36 11,36 " stroke-width="1"/>
    <polyline arcFlag="1" points="11,15 10,15 9,15 9,15 8,15 8,16 7,16 7,17 6,17 6,18 6,18 6,19 5,20 5,20 5,21 6,22 6,22 6,23 6,23 7,24 7,24 8,25 8,25 9,25 9,26 10,26 11,26 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="11" y1="54" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="12" y1="15" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="27" x2="27" y1="99" y2="107"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="40" x2="28" y1="87" y2="87"/>
    <polyline arcFlag="1" points="27,100 25,100 23,99 22,99 20,98 19,97 17,96 16,94 15,92 15,91 14,89 14,87 14,85 15,83 15,82 16,80 17,79 19,77 20,76 22,75 23,75 25,74 27,74 29,74 31,75 32,75 34,76 35,77 37,79 38,80 39,82 39,83 40,85 40,87 " stroke-width="0.0972"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="8" x2="5" y1="1" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="10" x2="3" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="8" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="1" x2="12" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape157">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,36 0,46 10,46 5,36 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,24 0,14 10,14 5,24 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="59" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <rect height="14" stroke-width="1" width="27" x="18" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="5" x2="5" y1="11" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="9" x2="18" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="8" x2="8" y1="2" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="58" x2="23" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape21">
    <rect height="26" stroke-width="1.99997" width="11" x="2" y="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="7" x2="7" y1="50" y2="5"/>
   </symbol>
   <symbol id="lightningRod:shape4">
    <rect height="31" stroke-width="2" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="9" x2="9" y1="35" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape10">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="17" y1="7" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="24" y1="19" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="15" y1="24" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="24" y1="17" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="10" y1="17" y2="24"/>
    <circle cx="17" cy="17" fillStyle="0" r="16" stroke-width="1.0625"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="17" y1="7" y2="7"/>
   </symbol>
   <symbol id="load:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape8_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="18" y2="43"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="18" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
   </symbol>
   <symbol id="switch2:shape8_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="44" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
   </symbol>
   <symbol id="switch2:shape8-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="18" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="18" y2="43"/>
   </symbol>
   <symbol id="switch2:shape8-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="44" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape7_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="18" x2="43" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="18" x2="9" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="5" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="9" x2="9" y1="14" y2="0"/>
   </symbol>
   <symbol id="switch2:shape7_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="3" x2="3" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="6" x2="6" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="10" x2="10" y1="14" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="43" x2="10" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape7-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="3" x2="3" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="6" x2="6" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="10" x2="10" y1="14" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="43" x2="10" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape7-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="3" x2="3" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="6" x2="6" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="10" x2="10" y1="14" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="43" x2="10" y1="7" y2="7"/>
   </symbol>
   <symbol id="transformer2:shape21_0">
    <circle cx="37" cy="66" fillStyle="0" r="26.5" stroke-width="0.63865"/>
    <polyline points="64,100 1,37 " stroke-width="1"/>
    <polyline points="58,100 64,100 " stroke-width="1"/>
    <polyline points="64,100 64,93 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="32" y1="71" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="32" y1="71" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="46" x2="38" y1="63" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="46" x2="38" y1="63" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="38" y1="70" y2="79"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="38" y1="70" y2="79"/>
   </symbol>
   <symbol id="transformer2:shape21_1">
    <ellipse cx="37" cy="29" fillStyle="0" rx="26.5" ry="25.5" stroke-width="0.62032"/>
    <polyline DF8003:Layer="PUBLIC" points="38,34 31,19 46,19 38,34 38,34 38,34 "/>
   </symbol>
   <symbol id="transformer2:shape38_0">
    <circle cx="30" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="31,57 6,57 6,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="42" y2="0"/>
    <polyline DF8003:Layer="PUBLIC" points="32,14 26,27 39,27 32,14 32,15 32,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="32" y1="51" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="57" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="26" y1="57" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="35" y1="57" y2="53"/>
   </symbol>
   <symbol id="transformer2:shape38_1">
    <circle cx="31" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="30,87 26,78 36,78 30,87 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="87" y2="87"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="81" y2="81"/>
   </symbol>
   <symbol id="transformer2:shape97_0">
    <circle cx="17" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="16,14 10,26 22,26 16,14 16,15 16,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="16" y1="51" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="42" x2="38" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="42" y2="0"/>
    <polyline DF8003:Layer="PUBLIC" points="12,78 19,78 16,85 12,78 "/>
   </symbol>
   <symbol id="transformer2:shape97_1">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,57 40,57 40,26 " stroke-width="1"/>
    <circle cx="16" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="56" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="56" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="57" y2="62"/>
   </symbol>
   <symbol id="voltageTransformer:shape106">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="6" y2="16"/>
    <rect height="13" stroke-width="1" width="5" x="3" y="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="35" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="6" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="1" x2="1" y1="19" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="1" y1="28" y2="19"/>
    <ellipse cx="25" cy="22" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="25" y1="34" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="25" y1="36" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="28" x2="25" y1="36" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="37" y1="22" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="37" y1="24" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="37" y1="24" y2="22"/>
    <ellipse cx="25" cy="34" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="34" y1="37" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="38" x2="40" y1="37" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="34" y1="33" y2="33"/>
    <ellipse cx="36" cy="22" rx="8" ry="7.5" stroke-width="1"/>
    <ellipse cx="36" cy="34" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="8" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="7" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="9" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="25" y1="22" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="25" y1="24" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="28" x2="25" y1="24" y2="22"/>
   </symbol>
   <symbol id="voltageTransformer:shape80">
    <ellipse cx="8" cy="16" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <rect height="24" stroke-width="0.379884" width="14" x="1" y="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="67" y2="23"/>
    <ellipse cx="8" cy="8" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
   </symbol>
   <symbol id="voltageTransformer:shape14">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="3" x2="7" y1="3" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="6,19 26,19 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="25" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.37768" x1="2" x2="9" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="28" x2="30" y1="6" y2="4"/>
    <circle cx="30" cy="14" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="39" cy="11" fillStyle="0" r="6" stroke-width="0.431185"/>
    <ellipse cx="30" cy="6" fillStyle="0" rx="6.5" ry="6" stroke-width="0.431185"/>
    <circle cx="22" cy="11" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="37" x2="39" y1="13" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="40" x2="40" y1="11" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="42" x2="39" y1="13" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="28" x2="30" y1="17" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="31" x2="31" y1="15" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="33" x2="30" y1="17" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="21" x2="24" y1="14" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="21" x2="18" y1="14" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="18" x2="24" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="31" x2="31" y1="4" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="33" x2="30" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.37768" x1="0" x2="12" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251787" x1="6" x2="6" y1="19" y2="9"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1bc39b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1bc4af0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1bc54e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1bc6180" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1bc73b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1bc8050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1bc8bf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1bc95f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1271c90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1271c90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1bcc6e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1bcc6e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1bce6c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1bce6c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_1bcf6d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1bd1360" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1bd1fb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1bd2e90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1bd3890" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1bd4f60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1bd5c60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1bd6520" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1bd6ce0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1bd7dc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1bd8740" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1bd9230" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1bd9bf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1bdb070" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1bdbc10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1bdcc40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1bdd880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1bec050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1bdef00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1be00a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1be1680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1425" width="2352" x="-189" y="-1169"/>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -0.865385 815.000000 86.903846)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1286bb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -199.500000 1096.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1288430" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -174.000000 1078.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1288ca0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -188.000000 1115.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -0.865385 1375.000000 82.903846)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1289650" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -199.500000 1096.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12898f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -174.000000 1078.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1289b30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -188.000000 1115.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -0.865385 1831.000000 87.903846)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1289e60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -199.500000 1096.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_128a100" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -174.000000 1078.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_128a340" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -188.000000 1115.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -0.865385 1310.000000 601.903846)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_128a670" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -199.500000 1096.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_128a910" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -174.000000 1078.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_128ab50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -188.000000 1115.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -0.865385 480.000000 1170.903846)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_128ae80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -199.500000 1096.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_128b120" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -174.000000 1078.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_128b360" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -188.000000 1115.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -0.865385 1560.000000 285.903846)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_128b690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -199.500000 1096.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_128b930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -174.000000 1078.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_128bb70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -188.000000 1115.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_128bea0" transform="matrix(1.000000 -0.000000 0.000000 -0.825688 408.000000 889.311927) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_128c730" transform="matrix(1.000000 -0.000000 0.000000 -0.825688 400.000000 847.201835) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_128c9b0" transform="matrix(1.000000 -0.000000 0.000000 -0.825688 408.000000 875.275229) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_128ced0" transform="matrix(1.000000 -0.000000 0.000000 -0.825688 414.000000 861.238532) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_128d150" transform="matrix(1.000000 -0.000000 0.000000 -0.825688 408.000000 905.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_128d390" transform="matrix(1.000000 -0.000000 0.000000 -0.825688 400.000000 832.201835) translate(0,12)">Ubc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1217490" transform="matrix(1.000000 -0.000000 0.000000 -0.825688 400.000000 817.201835) translate(0,12)">Uca(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12176d0" transform="matrix(1.000000 -0.000000 0.000000 -0.825688 416.000000 800.201835) translate(0,12)">F(Hz):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1218310" transform="matrix(1.000000 -0.000000 0.000000 -0.825688 410.000000 416.311927) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12186b0" transform="matrix(1.000000 -0.000000 0.000000 -0.825688 402.000000 374.201835) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12188f0" transform="matrix(1.000000 -0.000000 0.000000 -0.825688 410.000000 402.275229) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1218b30" transform="matrix(1.000000 -0.000000 0.000000 -0.825688 416.000000 388.238532) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1218d70" transform="matrix(1.000000 -0.000000 0.000000 -0.825688 410.000000 432.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1218fb0" transform="matrix(1.000000 -0.000000 0.000000 -0.825688 402.000000 359.201835) translate(0,12)">Ubc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12191f0" transform="matrix(1.000000 -0.000000 0.000000 -0.825688 402.000000 344.201835) translate(0,12)">Uca(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1219430" transform="matrix(1.000000 -0.000000 0.000000 -0.825688 418.000000 327.201835) translate(0,12)">F(Hz):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1221200" transform="matrix(1.000000 -0.000000 0.000000 -0.825688 1985.000000 876.311927) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1221450" transform="matrix(1.000000 -0.000000 0.000000 -0.825688 1977.000000 834.201835) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1221690" transform="matrix(1.000000 -0.000000 0.000000 -0.825688 1985.000000 862.275229) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12218d0" transform="matrix(1.000000 -0.000000 0.000000 -0.825688 1991.000000 848.238532) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1221b10" transform="matrix(1.000000 -0.000000 0.000000 -0.825688 1985.000000 892.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1221d50" transform="matrix(1.000000 -0.000000 0.000000 -0.825688 1977.000000 819.201835) translate(0,12)">Ubc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1221f90" transform="matrix(1.000000 -0.000000 0.000000 -0.825688 1977.000000 804.201835) translate(0,12)">Uca(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12221d0" transform="matrix(1.000000 -0.000000 0.000000 -0.825688 1993.000000 787.201835) translate(0,12)">F(Hz):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12287f0" transform="matrix(1.000000 -0.000000 0.000000 -0.865385 1174.000000 606.000000) translate(0,12)">档位:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12290b0" transform="matrix(1.000000 -0.000000 0.000000 -0.865385 1174.500000 589.557692) translate(0,12)">油温:</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -0.865385 1314.000000 275.903846)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13bdfb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -199.500000 1096.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1962bd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -174.000000 1078.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1962d40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -188.000000 1115.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" fillStyle="1" height="42" stroke="rgb(255,255,255)" stroke-width="1" width="72" x="78" y="-1080"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="14" stroke="rgb(60,120,255)" stroke-width="1" width="6" x="386" y="28"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="14" stroke="rgb(60,120,255)" stroke-width="1" width="27" x="349" y="8"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-315292">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 587.732799 -883.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48692" ObjectName="SW-SB_DT.SB_DT_3616SW"/>
     <cge:Meas_Ref ObjectId="315292"/>
    <cge:TPSR_Ref TObjectID="48692"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-315291">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 587.732799 -784.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48691" ObjectName="SW-SB_DT.SB_DT_3611SW"/>
     <cge:Meas_Ref ObjectId="315291"/>
    <cge:TPSR_Ref TObjectID="48691"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-315494">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1047.330059 -705.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48713" ObjectName="SW-SB_DT.SB_DT_3011SW"/>
     <cge:Meas_Ref ObjectId="315494"/>
    <cge:TPSR_Ref TObjectID="48713"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-315463">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 622.865666 -649.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48708" ObjectName="SW-SB_DT.SB_DT_39017SW"/>
     <cge:Meas_Ref ObjectId="315463"/>
    <cge:TPSR_Ref TObjectID="48708"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-315462">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 622.865666 -719.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48707" ObjectName="SW-SB_DT.SB_DT_39010SW"/>
     <cge:Meas_Ref ObjectId="315462"/>
    <cge:TPSR_Ref TObjectID="48707"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-315458">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1817.597250 -783.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48705" ObjectName="SW-SB_DT.SB_DT_3642SW"/>
     <cge:Meas_Ref ObjectId="315458"/>
    <cge:TPSR_Ref TObjectID="48705"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-315435">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1302.097067 -701.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48703" ObjectName="SW-SB_DT.SB_DT_3121SW"/>
     <cge:Meas_Ref ObjectId="315435"/>
    <cge:TPSR_Ref TObjectID="48703"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-315436">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1461.097067 -700.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48704" ObjectName="SW-SB_DT.SB_DT_3122SW"/>
     <cge:Meas_Ref ObjectId="315436"/>
    <cge:TPSR_Ref TObjectID="48704"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-315629">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 652.732799 -10.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48724" ObjectName="SW-SB_DT.SB_DT_0826SW"/>
     <cge:Meas_Ref ObjectId="315629"/>
    <cge:TPSR_Ref TObjectID="48724"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-315340">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1139.732799 -888.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48696" ObjectName="SW-SB_DT.SB_DT_3626SW"/>
     <cge:Meas_Ref ObjectId="315340"/>
    <cge:TPSR_Ref TObjectID="48696"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-315339">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1139.732799 -789.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48695" ObjectName="SW-SB_DT.SB_DT_3621SW"/>
     <cge:Meas_Ref ObjectId="315339"/>
    <cge:TPSR_Ref TObjectID="48695"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-315388">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1601.732799 -884.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48700" ObjectName="SW-SB_DT.SB_DT_3636SW"/>
     <cge:Meas_Ref ObjectId="315388"/>
    <cge:TPSR_Ref TObjectID="48700"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-315387">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1601.732799 -785.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48699" ObjectName="SW-SB_DT.SB_DT_3632SW"/>
     <cge:Meas_Ref ObjectId="315387"/>
    <cge:TPSR_Ref TObjectID="48699"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-315503">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1046.000000 -381.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48716" ObjectName="SW-SB_DT.SB_DT_001XC1"/>
     <cge:Meas_Ref ObjectId="315503"/>
    <cge:TPSR_Ref TObjectID="48716"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-315503">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1046.000000 -301.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48715" ObjectName="SW-SB_DT.SB_DT_001XC"/>
     <cge:Meas_Ref ObjectId="315503"/>
    <cge:TPSR_Ref TObjectID="48715"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-315470">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 1865.865666 -640.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48711" ObjectName="SW-SB_DT.SB_DT_39027SW"/>
     <cge:Meas_Ref ObjectId="315470"/>
    <cge:TPSR_Ref TObjectID="48711"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-315469">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 1865.865666 -710.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48710" ObjectName="SW-SB_DT.SB_DT_39020SW"/>
     <cge:Meas_Ref ObjectId="315469"/>
    <cge:TPSR_Ref TObjectID="48710"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-315596">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 436.000000 -236.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48718" ObjectName="SW-SB_DT.SB_DT_081XC"/>
     <cge:Meas_Ref ObjectId="315596"/>
    <cge:TPSR_Ref TObjectID="48718"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-315596">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 436.000000 -156.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48719" ObjectName="SW-SB_DT.SB_DT_081XC1"/>
     <cge:Meas_Ref ObjectId="315596"/>
    <cge:TPSR_Ref TObjectID="48719"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-315598">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 477.000000 -62.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48720" ObjectName="SW-SB_DT.SB_DT_08160SW"/>
     <cge:Meas_Ref ObjectId="315598"/>
    <cge:TPSR_Ref TObjectID="48720"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-315627">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 652.000000 -236.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48722" ObjectName="SW-SB_DT.SB_DT_082XC"/>
     <cge:Meas_Ref ObjectId="315627"/>
    <cge:TPSR_Ref TObjectID="48722"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-315627">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 652.000000 -156.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48723" ObjectName="SW-SB_DT.SB_DT_082XC1"/>
     <cge:Meas_Ref ObjectId="315627"/>
    <cge:TPSR_Ref TObjectID="48723"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-315630">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 694.000000 -66.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48725" ObjectName="SW-SB_DT.SB_DT_08260SW"/>
     <cge:Meas_Ref ObjectId="315630"/>
    <cge:TPSR_Ref TObjectID="48725"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-315631">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 540.000000 11.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48726" ObjectName="SW-SB_DT.SB_DT_08267SW"/>
     <cge:Meas_Ref ObjectId="315631"/>
    <cge:TPSR_Ref TObjectID="48726"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-315673">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1066.000000 -235.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48734" ObjectName="SW-SB_DT.SB_DT_084XC"/>
     <cge:Meas_Ref ObjectId="315673"/>
    <cge:TPSR_Ref TObjectID="48734"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-315673">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1066.000000 -155.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48735" ObjectName="SW-SB_DT.SB_DT_084XC1"/>
     <cge:Meas_Ref ObjectId="315673"/>
    <cge:TPSR_Ref TObjectID="48735"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-315676">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1107.000000 -61.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48737" ObjectName="SW-SB_DT.SB_DT_08460SW"/>
     <cge:Meas_Ref ObjectId="315676"/>
    <cge:TPSR_Ref TObjectID="48737"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-315675">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1067.000000 -4.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48736" ObjectName="SW-SB_DT.SB_DT_0846SW"/>
     <cge:Meas_Ref ObjectId="315675"/>
    <cge:TPSR_Ref TObjectID="48736"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-315677">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1109.000000 80.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48738" ObjectName="SW-SB_DT.SB_DT_08467SW"/>
     <cge:Meas_Ref ObjectId="315677"/>
    <cge:TPSR_Ref TObjectID="48738"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-315727">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1282.000000 -236.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48740" ObjectName="SW-SB_DT.SB_DT_085XC"/>
     <cge:Meas_Ref ObjectId="315727"/>
    <cge:TPSR_Ref TObjectID="48740"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-315727">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1282.000000 -156.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48741" ObjectName="SW-SB_DT.SB_DT_085XC1"/>
     <cge:Meas_Ref ObjectId="315727"/>
    <cge:TPSR_Ref TObjectID="48741"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-315730">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1323.000000 -62.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48743" ObjectName="SW-SB_DT.SB_DT_08560SW"/>
     <cge:Meas_Ref ObjectId="315730"/>
    <cge:TPSR_Ref TObjectID="48743"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-315729">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1283.000000 -5.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48742" ObjectName="SW-SB_DT.SB_DT_0856SW"/>
     <cge:Meas_Ref ObjectId="315729"/>
    <cge:TPSR_Ref TObjectID="48742"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-315731">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1325.000000 79.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48744" ObjectName="SW-SB_DT.SB_DT_08567SW"/>
     <cge:Meas_Ref ObjectId="315731"/>
    <cge:TPSR_Ref TObjectID="48744"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-315781">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1492.000000 -237.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48746" ObjectName="SW-SB_DT.SB_DT_086XC"/>
     <cge:Meas_Ref ObjectId="315781"/>
    <cge:TPSR_Ref TObjectID="48746"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-315781">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1492.000000 -157.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48747" ObjectName="SW-SB_DT.SB_DT_086XC1"/>
     <cge:Meas_Ref ObjectId="315781"/>
    <cge:TPSR_Ref TObjectID="48747"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-315783">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1533.000000 -63.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48748" ObjectName="SW-SB_DT.SB_DT_08660SW"/>
     <cge:Meas_Ref ObjectId="315783"/>
    <cge:TPSR_Ref TObjectID="48748"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-315827">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1709.000000 -237.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48750" ObjectName="SW-SB_DT.SB_DT_087XC"/>
     <cge:Meas_Ref ObjectId="315827"/>
    <cge:TPSR_Ref TObjectID="48750"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-315827">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1709.000000 -157.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48751" ObjectName="SW-SB_DT.SB_DT_087XC1"/>
     <cge:Meas_Ref ObjectId="315827"/>
    <cge:TPSR_Ref TObjectID="48751"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-315829">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1750.000000 -63.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48752" ObjectName="SW-SB_DT.SB_DT_08760SW"/>
     <cge:Meas_Ref ObjectId="315829"/>
    <cge:TPSR_Ref TObjectID="48752"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-315873">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1916.000000 -238.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48754" ObjectName="SW-SB_DT.SB_DT_088XC"/>
     <cge:Meas_Ref ObjectId="315873"/>
    <cge:TPSR_Ref TObjectID="48754"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-315873">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1916.000000 -158.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48755" ObjectName="SW-SB_DT.SB_DT_088XC1"/>
     <cge:Meas_Ref ObjectId="315873"/>
    <cge:TPSR_Ref TObjectID="48755"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-315875">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1957.000000 -64.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48756" ObjectName="SW-SB_DT.SB_DT_08860SW"/>
     <cge:Meas_Ref ObjectId="315875"/>
    <cge:TPSR_Ref TObjectID="48756"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-315654">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2104.000000 -238.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48727" ObjectName="SW-SB_DT.SB_DT_0121XC"/>
     <cge:Meas_Ref ObjectId="315654"/>
    <cge:TPSR_Ref TObjectID="48727"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-315654">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2104.000000 -181.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48728" ObjectName="SW-SB_DT.SB_DT_0121XC1"/>
     <cge:Meas_Ref ObjectId="315654"/>
    <cge:TPSR_Ref TObjectID="48728"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-315662">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1651.000000 -350.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48732" ObjectName="SW-SB_DT.SB_DT_0901XC1"/>
     <cge:Meas_Ref ObjectId="315662"/>
    <cge:TPSR_Ref TObjectID="48732"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-315662">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1651.000000 -293.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48731" ObjectName="SW-SB_DT.SB_DT_0901XC"/>
     <cge:Meas_Ref ObjectId="315662"/>
    <cge:TPSR_Ref TObjectID="48731"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-315468">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1879.000000 -660.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48709" ObjectName="SW-SB_DT.SB_DT_3902SW"/>
     <cge:Meas_Ref ObjectId="315468"/>
    <cge:TPSR_Ref TObjectID="48709"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-315461">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 636.000000 -669.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48706" ObjectName="SW-SB_DT.SB_DT_3901SW"/>
     <cge:Meas_Ref ObjectId="315461"/>
    <cge:TPSR_Ref TObjectID="48706"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-315389">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1641.617415 -956.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48701" ObjectName="SW-SB_DT.SB_DT_36367SW"/>
     <cge:Meas_Ref ObjectId="315389"/>
    <cge:TPSR_Ref TObjectID="48701"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-315341">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1179.617415 -960.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48697" ObjectName="SW-SB_DT.SB_DT_36267SW"/>
     <cge:Meas_Ref ObjectId="315341"/>
    <cge:TPSR_Ref TObjectID="48697"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-315293">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 627.617415 -955.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48693" ObjectName="SW-SB_DT.SB_DT_36167SW"/>
     <cge:Meas_Ref ObjectId="315293"/>
    <cge:TPSR_Ref TObjectID="48693"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-315658">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 857.000000 -235.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48729" ObjectName="SW-SB_DT.SB_DT_0831XC"/>
     <cge:Meas_Ref ObjectId="315658"/>
    <cge:TPSR_Ref TObjectID="48729"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-315658">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 857.000000 -141.659574)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48730" ObjectName="SW-SB_DT.SB_DT_0831XC1"/>
     <cge:Meas_Ref ObjectId="315658"/>
    <cge:TPSR_Ref TObjectID="48730"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-SB_DT.SB_DT_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="414,-767 1327,-767 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="48759" ObjectName="BS-SB_DT.SB_DT_3IM"/>
    <cge:TPSR_Ref TObjectID="48759"/></metadata>
   <polyline fill="none" opacity="0" points="414,-767 1327,-767 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-SB_DT.SB_DT_3ⅡM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1447,-768 2111,-768 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="48760" ObjectName="BS-SB_DT.SB_DT_3ⅡM"/>
    <cge:TPSR_Ref TObjectID="48760"/></metadata>
   <polyline fill="none" opacity="0" points="1447,-768 2111,-768 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-SB_DT.SB_DT_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="348,-282 2149,-282 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="48761" ObjectName="BS-SB_DT.SB_DT_9IM"/>
    <cge:TPSR_Ref TObjectID="48761"/></metadata>
   <polyline fill="none" opacity="0" points="348,-282 2149,-282 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-SB_DT.SB_DT_Cb1">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 634.000000 137.000000)" xlink:href="#capacitor:shape15"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48762" ObjectName="CB-SB_DT.SB_DT_Cb1"/>
    <cge:TPSR_Ref TObjectID="48762"/></metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-SB_DT.SB_DT_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="48030"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1018.000000 -546.000000)" xlink:href="#transformer2:shape21_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1018.000000 -546.000000)" xlink:href="#transformer2:shape21_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="48757" ObjectName="TF-SB_DT.SB_DT_1T"/>
    <cge:TPSR_Ref TObjectID="48757"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 836.000000 89.340426)" xlink:href="#transformer2:shape38_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 836.000000 89.340426)" xlink:href="#transformer2:shape38_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1843.000000 -1075.000000)" xlink:href="#transformer2:shape97_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1843.000000 -1075.000000)" xlink:href="#transformer2:shape97_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_10795b0">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 704.078502 -564.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13f4f90">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 542.597250 -1068.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13104f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 1709.000000 -470.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1367640">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 1094.597250 -1073.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_139cc40">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 1556.597250 -1069.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1326ca0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1606.000000 -1018.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1328160">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1822.000000 -885.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_131ce60">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 963.000000 -541.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1352610">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1051.000000 -467.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1353150">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 961.000000 -429.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13a2630">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 1947.078502 -555.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12d8ce0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 392.000000 -62.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13339f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 441.000000 -60.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13133a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 608.000000 -62.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1370af0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 657.000000 -60.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1360030">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1022.000000 -61.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13057e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1071.000000 -59.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_136b260">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1023.000000 83.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1343c00">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1238.000000 -62.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1347c20">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1287.000000 -60.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_135a8a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1239.000000 82.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_125abf0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1448.000000 -63.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_125ec10">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1497.000000 -61.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12d4750">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1665.000000 -63.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12d8770">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1714.000000 -61.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_126a770">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1872.000000 -64.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_122bee0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1921.000000 -62.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_123a970">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1654.000000 -409.000000)" xlink:href="#lightningRod:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12aaf30">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1881.000000 -567.000000)" xlink:href="#lightningRod:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12ab620">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 638.000000 -576.000000)" xlink:href="#lightningRod:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12ac350">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1820.000000 -830.000000)" xlink:href="#lightningRod:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_127f350">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 862.000000 -43.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_127fee0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 858.000000 -183.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_121dc30">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 429.000000 -3.000000)" xlink:href="#lightningRod:shape10"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -83.000000 -1013.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-0" ratioFlag="0">
    <text fill="rgb(0,205,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -46.000000 -809.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-0" ratioFlag="0">
    <text fill="rgb(0,205,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -45.000000 -769.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-0" ratioFlag="0">
    <text fill="rgb(0,205,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -48.000000 -891.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-0" ratioFlag="0">
    <text fill="rgb(0,205,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -46.000000 -852.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-316091" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 411.000000 205.500000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="316091" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48717"/>
     <cge:Term_Ref ObjectID="47948"/>
    <cge:TPSR_Ref TObjectID="48717"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-316092" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 411.000000 205.500000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="316092" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48717"/>
     <cge:Term_Ref ObjectID="47948"/>
    <cge:TPSR_Ref TObjectID="48717"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-316082" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 411.000000 205.500000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="316082" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48717"/>
     <cge:Term_Ref ObjectID="47948"/>
    <cge:TPSR_Ref TObjectID="48717"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-316104" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1048.000000 205.500000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="316104" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48733"/>
     <cge:Term_Ref ObjectID="47980"/>
    <cge:TPSR_Ref TObjectID="48733"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-316105" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1048.000000 205.500000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="316105" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48733"/>
     <cge:Term_Ref ObjectID="47980"/>
    <cge:TPSR_Ref TObjectID="48733"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-316101" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1048.000000 205.500000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="316101" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48733"/>
     <cge:Term_Ref ObjectID="47980"/>
    <cge:TPSR_Ref TObjectID="48733"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-316111" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1264.000000 205.500000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="316111" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48739"/>
     <cge:Term_Ref ObjectID="47992"/>
    <cge:TPSR_Ref TObjectID="48739"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-316112" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1264.000000 205.500000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="316112" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48739"/>
     <cge:Term_Ref ObjectID="47992"/>
    <cge:TPSR_Ref TObjectID="48739"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-316108" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1264.000000 205.500000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="316108" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48739"/>
     <cge:Term_Ref ObjectID="47992"/>
    <cge:TPSR_Ref TObjectID="48739"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-316118" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1476.000000 205.500000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="316118" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48745"/>
     <cge:Term_Ref ObjectID="48004"/>
    <cge:TPSR_Ref TObjectID="48745"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-316119" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1476.000000 205.500000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="316119" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48745"/>
     <cge:Term_Ref ObjectID="48004"/>
    <cge:TPSR_Ref TObjectID="48745"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-316115" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1476.000000 205.500000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="316115" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48745"/>
     <cge:Term_Ref ObjectID="48004"/>
    <cge:TPSR_Ref TObjectID="48745"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-316125" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1707.000000 205.500000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="316125" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48749"/>
     <cge:Term_Ref ObjectID="48012"/>
    <cge:TPSR_Ref TObjectID="48749"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-316126" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1707.000000 205.500000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="316126" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48749"/>
     <cge:Term_Ref ObjectID="48012"/>
    <cge:TPSR_Ref TObjectID="48749"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-316122" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1707.000000 205.500000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="316122" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48749"/>
     <cge:Term_Ref ObjectID="48012"/>
    <cge:TPSR_Ref TObjectID="48749"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-316132" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1921.000000 205.500000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="316132" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48753"/>
     <cge:Term_Ref ObjectID="48020"/>
    <cge:TPSR_Ref TObjectID="48753"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-316133" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1921.000000 205.500000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="316133" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48753"/>
     <cge:Term_Ref ObjectID="48020"/>
    <cge:TPSR_Ref TObjectID="48753"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-316129" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1921.000000 205.500000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="316129" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48753"/>
     <cge:Term_Ref ObjectID="48020"/>
    <cge:TPSR_Ref TObjectID="48753"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-316079" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1175.000000 -361.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="316079" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48714"/>
     <cge:Term_Ref ObjectID="47942"/>
    <cge:TPSR_Ref TObjectID="48714"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-316080" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1175.000000 -361.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="316080" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48714"/>
     <cge:Term_Ref ObjectID="47942"/>
    <cge:TPSR_Ref TObjectID="48714"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-316069" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1175.000000 -361.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="316069" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48714"/>
     <cge:Term_Ref ObjectID="47942"/>
    <cge:TPSR_Ref TObjectID="48714"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-315999" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 684.000000 -877.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="315999" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48690"/>
     <cge:Term_Ref ObjectID="47894"/>
    <cge:TPSR_Ref TObjectID="48690"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-316000" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 684.000000 -877.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="316000" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48690"/>
     <cge:Term_Ref ObjectID="47894"/>
    <cge:TPSR_Ref TObjectID="48690"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-315989" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 684.000000 -877.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="315989" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48690"/>
     <cge:Term_Ref ObjectID="47894"/>
    <cge:TPSR_Ref TObjectID="48690"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-316012" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1245.000000 -879.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="316012" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48694"/>
     <cge:Term_Ref ObjectID="47902"/>
    <cge:TPSR_Ref TObjectID="48694"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-316013" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1245.000000 -879.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="316013" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48694"/>
     <cge:Term_Ref ObjectID="47902"/>
    <cge:TPSR_Ref TObjectID="48694"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-316002" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1245.000000 -879.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="316002" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48694"/>
     <cge:Term_Ref ObjectID="47902"/>
    <cge:TPSR_Ref TObjectID="48694"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-316026" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1697.000000 -875.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="316026" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48698"/>
     <cge:Term_Ref ObjectID="47910"/>
    <cge:TPSR_Ref TObjectID="48698"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-316027" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1697.000000 -875.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="316027" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48698"/>
     <cge:Term_Ref ObjectID="47910"/>
    <cge:TPSR_Ref TObjectID="48698"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-316016" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1697.000000 -875.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="316016" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48698"/>
     <cge:Term_Ref ObjectID="47910"/>
    <cge:TPSR_Ref TObjectID="48698"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-316034" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1426.000000 -677.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="316034" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48702"/>
     <cge:Term_Ref ObjectID="47918"/>
    <cge:TPSR_Ref TObjectID="48702"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-316035" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1426.000000 -677.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="316035" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48702"/>
     <cge:Term_Ref ObjectID="47918"/>
    <cge:TPSR_Ref TObjectID="48702"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-316030" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1426.000000 -677.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="316030" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48702"/>
     <cge:Term_Ref ObjectID="47918"/>
    <cge:TPSR_Ref TObjectID="48702"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-316038" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 466.000000 -906.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="316038" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48759"/>
     <cge:Term_Ref ObjectID="48032"/>
    <cge:TPSR_Ref TObjectID="48759"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-316039" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 466.000000 -906.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="316039" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48759"/>
     <cge:Term_Ref ObjectID="48032"/>
    <cge:TPSR_Ref TObjectID="48759"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-316040" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 466.000000 -906.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="316040" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48759"/>
     <cge:Term_Ref ObjectID="48032"/>
    <cge:TPSR_Ref TObjectID="48759"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-316044" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 466.000000 -906.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="316044" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48759"/>
     <cge:Term_Ref ObjectID="48032"/>
    <cge:TPSR_Ref TObjectID="48759"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-316041" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 466.000000 -906.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="316041" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48759"/>
     <cge:Term_Ref ObjectID="48032"/>
    <cge:TPSR_Ref TObjectID="48759"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ubc" PreSymbol="0" appendix="" decimal="2" id="ME-316042" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 466.000000 -906.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="316042" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48759"/>
     <cge:Term_Ref ObjectID="48032"/>
    <cge:TPSR_Ref TObjectID="48759"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uca" PreSymbol="0" appendix="" decimal="2" id="ME-316043" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 466.000000 -906.000000) translate(0,102)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="316043" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48759"/>
     <cge:Term_Ref ObjectID="48032"/>
    <cge:TPSR_Ref TObjectID="48759"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-316045" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 466.000000 -906.000000) translate(0,117)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="316045" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48759"/>
     <cge:Term_Ref ObjectID="48032"/>
    <cge:TPSR_Ref TObjectID="48759"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-316046" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2041.000000 -893.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="316046" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48760"/>
     <cge:Term_Ref ObjectID="48033"/>
    <cge:TPSR_Ref TObjectID="48760"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-316047" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2041.000000 -893.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="316047" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48760"/>
     <cge:Term_Ref ObjectID="48033"/>
    <cge:TPSR_Ref TObjectID="48760"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-316048" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2041.000000 -893.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="316048" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48760"/>
     <cge:Term_Ref ObjectID="48033"/>
    <cge:TPSR_Ref TObjectID="48760"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-316052" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2041.000000 -893.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="316052" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48760"/>
     <cge:Term_Ref ObjectID="48033"/>
    <cge:TPSR_Ref TObjectID="48760"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-316049" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2041.000000 -893.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="316049" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48760"/>
     <cge:Term_Ref ObjectID="48033"/>
    <cge:TPSR_Ref TObjectID="48760"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ubc" PreSymbol="0" appendix="" decimal="2" id="ME-316050" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2041.000000 -893.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="316050" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48760"/>
     <cge:Term_Ref ObjectID="48033"/>
    <cge:TPSR_Ref TObjectID="48760"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uca" PreSymbol="0" appendix="" decimal="2" id="ME-316051" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2041.000000 -893.000000) translate(0,102)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="316051" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48760"/>
     <cge:Term_Ref ObjectID="48033"/>
    <cge:TPSR_Ref TObjectID="48760"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-316053" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2041.000000 -893.000000) translate(0,117)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="316053" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48760"/>
     <cge:Term_Ref ObjectID="48033"/>
    <cge:TPSR_Ref TObjectID="48760"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-316136" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 467.000000 -433.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="316136" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48761"/>
     <cge:Term_Ref ObjectID="48034"/>
    <cge:TPSR_Ref TObjectID="48761"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-316137" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 467.000000 -433.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="316137" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48761"/>
     <cge:Term_Ref ObjectID="48034"/>
    <cge:TPSR_Ref TObjectID="48761"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-316138" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 467.000000 -433.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="316138" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48761"/>
     <cge:Term_Ref ObjectID="48034"/>
    <cge:TPSR_Ref TObjectID="48761"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-316142" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 467.000000 -433.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="316142" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48761"/>
     <cge:Term_Ref ObjectID="48034"/>
    <cge:TPSR_Ref TObjectID="48761"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-316139" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 467.000000 -433.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="316139" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48761"/>
     <cge:Term_Ref ObjectID="48034"/>
    <cge:TPSR_Ref TObjectID="48761"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ubc" PreSymbol="0" appendix="" decimal="2" id="ME-316140" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 467.000000 -433.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="316140" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48761"/>
     <cge:Term_Ref ObjectID="48034"/>
    <cge:TPSR_Ref TObjectID="48761"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uca" PreSymbol="0" appendix="" decimal="2" id="ME-316141" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 467.000000 -433.000000) translate(0,102)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="316141" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48761"/>
     <cge:Term_Ref ObjectID="48034"/>
    <cge:TPSR_Ref TObjectID="48761"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-316143" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 467.000000 -433.000000) translate(0,117)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="316143" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48761"/>
     <cge:Term_Ref ObjectID="48034"/>
    <cge:TPSR_Ref TObjectID="48761"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-316099" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 629.000000 213.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="316099" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48721"/>
     <cge:Term_Ref ObjectID="47956"/>
    <cge:TPSR_Ref TObjectID="48721"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-316095" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 629.000000 213.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="316095" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48721"/>
     <cge:Term_Ref ObjectID="47956"/>
    <cge:TPSR_Ref TObjectID="48721"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-316068" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1218.000000 -605.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="316068" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48757"/>
     <cge:Term_Ref ObjectID="48031"/>
    <cge:TPSR_Ref TObjectID="48757"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-316067" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1218.000000 -605.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="316067" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48757"/>
     <cge:Term_Ref ObjectID="48031"/>
    <cge:TPSR_Ref TObjectID="48757"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-316064" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1183.000000 -687.000000) translate(0,12)">316064.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="316064" ObjectName="SB_DT.SB_DT_301BK:F"/>
     <cge:PSR_Ref ObjectID="48712"/>
     <cge:Term_Ref ObjectID="47938"/>
    <cge:TPSR_Ref TObjectID="48712"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-316065" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1183.000000 -687.000000) translate(0,27)">316065.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="316065" ObjectName="SB_DT.SB_DT_301BK:F"/>
     <cge:PSR_Ref ObjectID="48712"/>
     <cge:Term_Ref ObjectID="47938"/>
    <cge:TPSR_Ref TObjectID="48712"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-316054" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1183.000000 -687.000000) translate(0,42)">316054.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="316054" ObjectName="SB_DT.SB_DT_301BK:F"/>
     <cge:PSR_Ref ObjectID="48712"/>
     <cge:Term_Ref ObjectID="47938"/>
    <cge:TPSR_Ref TObjectID="48712"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="139" x="-72" y="-1072"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="139" x="-72" y="-1072"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-120" y="-1089"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-120" y="-1089"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="175" y="-1083"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="175" y="-1083"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="177" y="-1041"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="177" y="-1041"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="44" qtmmishow="hidden" width="73" x="78" y="-1080"/>
    </a>
   <metadata/><rect fill="white" height="44" opacity="0" stroke="white" transform="" width="73" x="78" y="-1080"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="558" y="-861"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="558" y="-861"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1108" y="-866"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1108" y="-866"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="28" x="1572" y="-862"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="28" x="1572" y="-862"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="1378" y="-719"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="1378" y="-719"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="52" x="1086" y="-605"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="52" x="1086" y="-605"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="455" y="-218"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="455" y="-218"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="671" y="-218"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="671" y="-218"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1085" y="-217"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1085" y="-217"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1301" y="-218"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1301" y="-218"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1511" y="-219"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1511" y="-219"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1728" y="-219"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1728" y="-219"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1935" y="-220"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1935" y="-220"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="26" qtmmishow="hidden" width="65" x="-161" y="-689"/>
    </a>
   <metadata/><rect fill="white" height="26" opacity="0" stroke="white" transform="" width="65" x="-161" y="-689"/></g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="139" x="-72" y="-1072"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-120" y="-1089"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="175" y="-1083"/></g>
   <g href="cx_配调_配网接线图35_双柏.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="177" y="-1041"/></g>
   <g href="AVC独田变站.svg" style="fill-opacity:0"><rect height="44" qtmmishow="hidden" width="73" x="78" y="-1080"/></g>
   <g href="35kV独田变SB_DT_361间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="558" y="-861"/></g>
   <g href="35kV独田变35kV田鄂线362间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1108" y="-866"/></g>
   <g href="35kV独田变35kV双田线363间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="28" x="1572" y="-862"/></g>
   <g href="35kV独田变35kV分段312间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="1378" y="-719"/></g>
   <g href="35kV独田变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="52" x="1086" y="-605"/></g>
   <g href="35kV独田变10kV接地变081间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="455" y="-218"/></g>
   <g href="35kV独田变10kV1号电容器082间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="671" y="-218"/></g>
   <g href="35kV独田变10kV龙田线084间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1085" y="-217"/></g>
   <g href="35kV独田变10kV孔雀岭线085间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1301" y="-218"/></g>
   <g href="35kV独田变10kV备用线1086间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1511" y="-219"/></g>
   <g href="35kV独田变10kV备用线2087间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1728" y="-219"/></g>
   <g href="35kV独田变10kV备用3088间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1935" y="-220"/></g>
   <g href="35kV独田变GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="26" qtmmishow="hidden" width="65" x="-161" y="-689"/></g>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="662" x2="626" y1="147" y2="147"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="626" x2="626" y1="4" y2="147"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="662" x2="662" y1="135" y2="147"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="389" x2="444" y1="-20" y2="-20"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="388" x2="390" y1="-1" y2="-1"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="389" x2="389" y1="6" y2="28"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="389" x2="386" y1="6" y2="0"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.1875" x1="333" x2="333" y1="13" y2="16"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.344531" x1="336" x2="336" y1="11" y2="18"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.305149" x1="340" x2="349" y1="15" y2="15"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.560509" x1="340" x2="340" y1="20" y2="9"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="389" x2="354" y1="15" y2="15"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="389" x2="414" y1="15" y2="15"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="414" x2="414" y1="15" y2="15"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="414" x2="414" y1="15" y2="26"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="382" x2="395" y1="76" y2="76"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="386" x2="391" y1="78" y2="78"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="387" x2="390" y1="80" y2="80"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="389" x2="389" y1="42" y2="56"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="389" x2="389" y1="5" y2="5"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="389" x2="389" y1="-1" y2="-20"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="388" x2="388" y1="61" y2="75"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="396" x2="390" y1="53" y2="62"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="398" x2="392" y1="55" y2="64"/>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1310ec0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 1636.000000 -530.000000)" xlink:href="#voltageTransformer:shape106"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1229930">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1214.000000 -1086.000000)" xlink:href="#voltageTransformer:shape80"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_122a140">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 662.000000 -1081.000000)" xlink:href="#voltageTransformer:shape80"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_122abc0">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1676.000000 -1082.000000)" xlink:href="#voltageTransformer:shape80"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_128da40">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 615.000000 -543.000000)" xlink:href="#voltageTransformer:shape14"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1290200">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1858.000000 -528.000000)" xlink:href="#voltageTransformer:shape14"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-SB_DT.084Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1071.000000 158.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48763" ObjectName="EC-SB_DT.084Ld"/>
    <cge:TPSR_Ref TObjectID="48763"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-SB_DT.085Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1287.000000 157.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48764" ObjectName="EC-SB_DT.085Ld"/>
    <cge:TPSR_Ref TObjectID="48764"/></metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_12eeb10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="582,-654 567,-654 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="48708@1" ObjectIDZND0="g_12ee4e0@0" Pin0InfoVect0LinkObjId="g_12ee4e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315463_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="582,-654 567,-654 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12f09e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="582,-724 569,-724 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="48707@1" ObjectIDZND0="g_12f03b0@0" Pin0InfoVect0LinkObjId="g_12f03b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315462_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="582,-724 569,-724 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13840d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="550,-1014 550,-1002 597,-1002 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_13f4f90@0" ObjectIDZND0="g_122a140@0" ObjectIDZND1="48692@x" ObjectIDZND2="48693@x" Pin0InfoVect0LinkObjId="g_122a140_0" Pin0InfoVect1LinkObjId="SW-315292_0" Pin0InfoVect2LinkObjId="SW-315293_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13f4f90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="550,-1014 550,-1002 597,-1002 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13a54c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="597,-1002 597,-1080 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_13f4f90@0" ObjectIDND1="g_122a140@0" ObjectIDND2="48692@x" ObjectIDZND0="48350@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_13f4f90_0" Pin1InfoVect1LinkObjId="g_122a140_0" Pin1InfoVect2LinkObjId="SW-315292_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="597,-1002 597,-1080 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13a56b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="597,-1002 654,-1002 654,-1014 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="voltageTransformer" ObjectIDND0="g_13f4f90@0" ObjectIDND1="48692@x" ObjectIDND2="48693@x" ObjectIDZND0="g_122a140@0" Pin0InfoVect0LinkObjId="g_122a140_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_13f4f90_0" Pin1InfoVect1LinkObjId="SW-315292_0" Pin1InfoVect2LinkObjId="SW-315293_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="597,-1002 654,-1002 654,-1014 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_138cb40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1102,-1019 1102,-1007 1149,-1007 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1367640@0" ObjectIDZND0="g_1229930@0" ObjectIDZND1="48697@x" ObjectIDZND2="48696@x" Pin0InfoVect0LinkObjId="g_1229930_0" Pin0InfoVect1LinkObjId="SW-315341_0" Pin0InfoVect2LinkObjId="SW-315340_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1367640_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1102,-1019 1102,-1007 1149,-1007 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_138cd30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1149,-1007 1149,-1091 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_1367640@0" ObjectIDND1="g_1229930@0" ObjectIDND2="48697@x" ObjectIDZND0="49390@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1367640_0" Pin1InfoVect1LinkObjId="g_1229930_0" Pin1InfoVect2LinkObjId="SW-315341_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1149,-1007 1149,-1091 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_138cf20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1149,-1007 1206,-1007 1206,-1019 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="voltageTransformer" ObjectIDND0="g_1367640@0" ObjectIDND1="48697@x" ObjectIDND2="48696@x" ObjectIDZND0="g_1229930@0" Pin0InfoVect0LinkObjId="g_1229930_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1367640_0" Pin1InfoVect1LinkObjId="SW-315341_0" Pin1InfoVect2LinkObjId="SW-315340_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1149,-1007 1206,-1007 1206,-1019 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_134d170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1564,-1015 1564,-1003 1611,-1003 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_139cc40@0" ObjectIDZND0="g_1326ca0@0" ObjectIDZND1="48701@x" ObjectIDZND2="48700@x" Pin0InfoVect0LinkObjId="g_1326ca0_0" Pin0InfoVect1LinkObjId="SW-315389_0" Pin0InfoVect2LinkObjId="SW-315388_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_139cc40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1564,-1015 1564,-1003 1611,-1003 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_134d360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1611,-1003 1668,-1003 1668,-1015 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="voltageTransformer" ObjectIDND0="g_139cc40@0" ObjectIDND1="g_1326ca0@0" ObjectIDND2="48701@x" ObjectIDZND0="g_122abc0@0" Pin0InfoVect0LinkObjId="g_122abc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_139cc40_0" Pin1InfoVect1LinkObjId="g_1326ca0_0" Pin1InfoVect2LinkObjId="SW-315389_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1611,-1003 1668,-1003 1668,-1015 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1327550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1611,-1003 1611,-1023 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_139cc40@0" ObjectIDND1="48701@x" ObjectIDND2="48700@x" ObjectIDZND0="g_1326ca0@0" Pin0InfoVect0LinkObjId="g_1326ca0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_139cc40_0" Pin1InfoVect1LinkObjId="SW-315389_0" Pin1InfoVect2LinkObjId="SW-315388_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1611,-1003 1611,-1023 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1327770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1149,-794 1149,-767 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48695@0" ObjectIDZND0="48759@0" Pin0InfoVect0LinkObjId="g_1329250_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315339_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1149,-794 1149,-767 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1327990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1611,-790 1611,-768 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48699@0" ObjectIDZND0="48760@0" Pin0InfoVect0LinkObjId="g_1328a80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315387_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1611,-790 1611,-768 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1328a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1827,-788 1827,-768 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48705@0" ObjectIDZND0="48760@0" Pin0InfoVect0LinkObjId="g_1327990_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315458_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1827,-788 1827,-768 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1329250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="618,-724 645,-724 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" EndDevType1="switch" ObjectIDND0="48707@0" ObjectIDZND0="48759@0" ObjectIDZND1="48706@x" Pin0InfoVect0LinkObjId="g_1327770_0" Pin0InfoVect1LinkObjId="SW-315461_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315462_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="618,-724 645,-724 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1329470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="645,-710 645,-724 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="48706@1" ObjectIDZND0="48707@x" ObjectIDZND1="48759@0" Pin0InfoVect0LinkObjId="SW-315462_0" Pin0InfoVect1LinkObjId="g_1327770_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315461_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="645,-710 645,-724 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1329690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="645,-724 645,-767 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="48707@x" ObjectIDND1="48706@x" ObjectIDZND0="48759@0" Pin0InfoVect0LinkObjId="g_1327770_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-315462_0" Pin1InfoVect1LinkObjId="SW-315461_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="645,-724 645,-767 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_131af00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="618,-654 645,-654 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="48708@0" ObjectIDZND0="g_10795b0@0" ObjectIDZND1="g_12ab620@0" ObjectIDZND2="48706@x" Pin0InfoVect0LinkObjId="g_10795b0_0" Pin0InfoVect1LinkObjId="g_12ab620_0" Pin0InfoVect2LinkObjId="SW-315461_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315463_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="618,-654 645,-654 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_131b120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="645,-674 645,-654 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="48706@0" ObjectIDZND0="48708@x" ObjectIDZND1="g_10795b0@0" ObjectIDZND2="g_12ab620@0" Pin0InfoVect0LinkObjId="SW-315463_0" Pin0InfoVect1LinkObjId="g_10795b0_0" Pin0InfoVect2LinkObjId="g_12ab620_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315461_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="645,-674 645,-654 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_131b340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="697,-622 697,-636 645,-636 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_10795b0@0" ObjectIDZND0="48708@x" ObjectIDZND1="48706@x" ObjectIDZND2="g_12ab620@0" Pin0InfoVect0LinkObjId="SW-315463_0" Pin0InfoVect1LinkObjId="SW-315461_0" Pin0InfoVect2LinkObjId="g_12ab620_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_10795b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="697,-622 697,-636 645,-636 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_131b560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="645,-654 645,-636 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="48708@x" ObjectIDND1="48706@x" ObjectIDZND0="g_10795b0@0" ObjectIDZND1="g_12ab620@0" Pin0InfoVect0LinkObjId="g_10795b0_0" Pin0InfoVect1LinkObjId="g_12ab620_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-315463_0" Pin1InfoVect1LinkObjId="SW-315461_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="645,-654 645,-636 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_131b780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="645,-636 645,-625 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_10795b0@0" ObjectIDND1="48708@x" ObjectIDND2="48706@x" ObjectIDZND0="g_12ab620@1" Pin0InfoVect0LinkObjId="g_12ab620_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_10795b0_0" Pin1InfoVect1LinkObjId="SW-315463_0" Pin1InfoVect2LinkObjId="SW-315461_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="645,-636 645,-625 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_131d9d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1056,-659 1056,-638 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="48712@0" ObjectIDZND0="48757@0" Pin0InfoVect0LinkObjId="g_1352f30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315493_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1056,-659 1056,-638 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_131dbf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1056,-616 970,-616 970,-595 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="48757@x" ObjectIDZND0="g_131ce60@0" Pin0InfoVect0LinkObjId="g_131ce60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_131d9d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1056,-616 970,-616 970,-595 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1352f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1056,-525 1056,-551 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_1352610@1" ObjectIDZND0="48757@1" Pin0InfoVect0LinkObjId="g_131d9d0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1352610_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1056,-525 1056,-551 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12ba460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1018,-436 1056,-436 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_1353150@0" ObjectIDZND0="48716@x" ObjectIDZND1="g_1352610@0" Pin0InfoVect0LinkObjId="SW-315503_0" Pin0InfoVect1LinkObjId="g_1352610_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1353150_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1018,-436 1056,-436 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12ba6e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1056,-388 1056,-369 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="48716@1" ObjectIDZND0="48714@1" Pin0InfoVect0LinkObjId="SW-315502_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315503_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1056,-388 1056,-369 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12786d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1056,-746 1056,-767 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48713@1" ObjectIDZND0="48759@0" Pin0InfoVect0LinkObjId="g_1327770_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315494_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1056,-746 1056,-767 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13a1440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1403,-695 1470,-695 1470,-705 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="48702@0" ObjectIDZND0="48704@0" Pin0InfoVect0LinkObjId="SW-315436_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315434_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1403,-695 1470,-695 1470,-705 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13a16a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1311,-742 1311,-767 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48703@1" ObjectIDZND0="48759@0" Pin0InfoVect0LinkObjId="g_1327770_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315435_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1311,-742 1311,-767 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13a1eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1470,-741 1470,-768 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48704@1" ObjectIDZND0="48760@0" Pin0InfoVect0LinkObjId="g_1327990_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315436_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1470,-741 1470,-768 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_139d0b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1825,-645 1810,-645 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="48711@1" ObjectIDZND0="g_13a2f90@0" Pin0InfoVect0LinkObjId="g_13a2f90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315470_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1825,-645 1810,-645 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_139e160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1825,-715 1812,-715 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="48710@1" ObjectIDZND0="g_139d730@0" Pin0InfoVect0LinkObjId="g_139d730_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315469_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1825,-715 1812,-715 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_139e3c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1861,-645 1888,-645 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="48711@0" ObjectIDZND0="g_13a2630@0" ObjectIDZND1="g_12aaf30@0" ObjectIDZND2="48709@x" Pin0InfoVect0LinkObjId="g_13a2630_0" Pin0InfoVect1LinkObjId="g_12aaf30_0" Pin0InfoVect2LinkObjId="SW-315468_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315470_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1861,-645 1888,-645 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_139e620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1888,-665 1888,-645 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="48709@0" ObjectIDZND0="48711@x" ObjectIDZND1="g_13a2630@0" ObjectIDZND2="g_12aaf30@0" Pin0InfoVect0LinkObjId="SW-315470_0" Pin0InfoVect1LinkObjId="g_13a2630_0" Pin0InfoVect2LinkObjId="g_12aaf30_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315468_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1888,-665 1888,-645 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_139e880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1940,-613 1940,-627 1888,-627 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_13a2630@0" ObjectIDZND0="48711@x" ObjectIDZND1="48709@x" ObjectIDZND2="g_12aaf30@0" Pin0InfoVect0LinkObjId="SW-315470_0" Pin0InfoVect1LinkObjId="SW-315468_0" Pin0InfoVect2LinkObjId="g_12aaf30_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13a2630_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1940,-613 1940,-627 1888,-627 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_139eae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1888,-645 1888,-627 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="48711@x" ObjectIDND1="48709@x" ObjectIDZND0="g_13a2630@0" ObjectIDZND1="g_12aaf30@0" Pin0InfoVect0LinkObjId="g_13a2630_0" Pin0InfoVect1LinkObjId="g_12aaf30_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-315470_0" Pin1InfoVect1LinkObjId="SW-315468_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1888,-645 1888,-627 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_139ed40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1888,-627 1888,-616 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="48711@x" ObjectIDND1="48709@x" ObjectIDND2="g_13a2630@0" ObjectIDZND0="g_12aaf30@1" Pin0InfoVect0LinkObjId="g_12aaf30_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-315470_0" Pin1InfoVect1LinkObjId="SW-315468_0" Pin1InfoVect2LinkObjId="g_13a2630_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1888,-627 1888,-616 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_130b5d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1861,-715 1888,-715 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" EndDevType1="switch" ObjectIDND0="48710@0" ObjectIDZND0="48760@0" ObjectIDZND1="48709@x" Pin0InfoVect0LinkObjId="g_1327990_0" Pin0InfoVect1LinkObjId="SW-315468_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315469_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1861,-715 1888,-715 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_130c070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1888,-701 1888,-715 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="48709@1" ObjectIDZND0="48710@x" ObjectIDZND1="48760@0" Pin0InfoVect0LinkObjId="SW-315469_0" Pin0InfoVect1LinkObjId="g_1327990_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315468_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1888,-701 1888,-715 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_130c2d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1888,-715 1888,-768 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="48710@x" ObjectIDND1="48709@x" ObjectIDZND0="48760@0" Pin0InfoVect0LinkObjId="g_1327990_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-315469_0" Pin1InfoVect1LinkObjId="SW-315468_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1888,-715 1888,-768 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_138c110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="446,-243 446,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="48718@1" ObjectIDZND0="48717@1" Pin0InfoVect0LinkObjId="SW-315595_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315596_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="446,-243 446,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_138c370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="446,-197 446,-180 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="48717@0" ObjectIDZND0="48719@1" Pin0InfoVect0LinkObjId="SW-315596_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315595_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="446,-197 446,-180 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13343e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="399,-116 399,-138 446,-138 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_12d8ce0@0" ObjectIDZND0="g_13339f0@0" ObjectIDZND1="48719@x" ObjectIDZND2="48720@x" Pin0InfoVect0LinkObjId="g_13339f0_0" Pin0InfoVect1LinkObjId="SW-315596_0" Pin0InfoVect2LinkObjId="SW-315598_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_12d8ce0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="399,-116 399,-138 446,-138 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13350f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="446,-118 446,-138 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_13339f0@1" ObjectIDZND0="g_12d8ce0@0" ObjectIDZND1="48719@x" ObjectIDZND2="48720@x" Pin0InfoVect0LinkObjId="g_12d8ce0_0" Pin0InfoVect1LinkObjId="SW-315596_0" Pin0InfoVect2LinkObjId="SW-315598_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13339f0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="446,-118 446,-138 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1335350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="446,-138 446,-163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_12d8ce0@0" ObjectIDND1="g_13339f0@0" ObjectIDND2="48720@x" ObjectIDZND0="48719@0" Pin0InfoVect0LinkObjId="SW-315596_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_12d8ce0_0" Pin1InfoVect1LinkObjId="g_13339f0_0" Pin1InfoVect2LinkObjId="SW-315598_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="446,-138 446,-163 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13355b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="446,-138 486,-138 486,-113 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_12d8ce0@0" ObjectIDND1="g_13339f0@0" ObjectIDND2="48719@x" ObjectIDZND0="48720@0" Pin0InfoVect0LinkObjId="SW-315598_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_12d8ce0_0" Pin1InfoVect1LinkObjId="g_13339f0_0" Pin1InfoVect2LinkObjId="SW-315596_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="446,-138 486,-138 486,-113 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_135fb70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1076,-242 1076,-223 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="48734@1" ObjectIDZND0="48733@1" Pin0InfoVect0LinkObjId="SW-315672_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315673_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1076,-242 1076,-223 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_135fdd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1076,-196 1076,-179 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="48733@0" ObjectIDZND0="48735@1" Pin0InfoVect0LinkObjId="SW-315673_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315672_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1076,-196 1076,-179 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1306200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1029,-115 1029,-137 1076,-137 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1360030@0" ObjectIDZND0="g_13057e0@0" ObjectIDZND1="48737@x" ObjectIDZND2="48735@x" Pin0InfoVect0LinkObjId="g_13057e0_0" Pin0InfoVect1LinkObjId="SW-315676_0" Pin0InfoVect2LinkObjId="SW-315673_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1360030_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1029,-115 1029,-137 1076,-137 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1306460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1076,-117 1076,-137 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_13057e0@1" ObjectIDZND0="g_1360030@0" ObjectIDZND1="48737@x" ObjectIDZND2="48735@x" Pin0InfoVect0LinkObjId="g_1360030_0" Pin0InfoVect1LinkObjId="SW-315676_0" Pin0InfoVect2LinkObjId="SW-315673_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13057e0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1076,-117 1076,-137 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13066c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1076,-137 1076,-162 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_13057e0@0" ObjectIDND1="g_1360030@0" ObjectIDND2="48737@x" ObjectIDZND0="48735@0" Pin0InfoVect0LinkObjId="SW-315673_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_13057e0_0" Pin1InfoVect1LinkObjId="g_1360030_0" Pin1InfoVect2LinkObjId="SW-315676_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1076,-137 1076,-162 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1306920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1076,-137 1116,-137 1116,-112 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_13057e0@0" ObjectIDND1="g_1360030@0" ObjectIDND2="48735@x" ObjectIDZND0="48737@0" Pin0InfoVect0LinkObjId="SW-315676_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_13057e0_0" Pin1InfoVect1LinkObjId="g_1360030_0" Pin1InfoVect2LinkObjId="SW-315673_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1076,-137 1116,-137 1116,-112 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1309e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1076,-45 1076,-64 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="48736@1" ObjectIDZND0="g_13057e0@0" Pin0InfoVect0LinkObjId="g_13057e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315675_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1076,-45 1076,-64 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_136bf90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1030,29 1030,11 1076,11 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" EndDevType2="switch" ObjectIDND0="g_136b260@0" ObjectIDZND0="48736@x" ObjectIDZND1="48763@x" ObjectIDZND2="48738@x" Pin0InfoVect0LinkObjId="SW-315675_0" Pin0InfoVect1LinkObjId="EC-SB_DT.084Ld_0" Pin0InfoVect2LinkObjId="SW-315677_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_136b260_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1030,29 1030,11 1076,11 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_136cca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1076,-9 1076,11 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" EndDevType2="switch" ObjectIDND0="48736@0" ObjectIDZND0="g_136b260@0" ObjectIDZND1="48763@x" ObjectIDZND2="48738@x" Pin0InfoVect0LinkObjId="g_136b260_0" Pin0InfoVect1LinkObjId="EC-SB_DT.084Ld_0" Pin0InfoVect2LinkObjId="SW-315677_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315675_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1076,-9 1076,11 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_136cf00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1076,11 1076,137 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_136b260@0" ObjectIDND1="48736@x" ObjectIDND2="48738@x" ObjectIDZND0="48763@0" Pin0InfoVect0LinkObjId="EC-SB_DT.084Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_136b260_0" Pin1InfoVect1LinkObjId="SW-315675_0" Pin1InfoVect2LinkObjId="SW-315677_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1076,11 1076,137 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_136d160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1076,11 1118,11 1118,29 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="load" EndDevType0="switch" ObjectIDND0="g_136b260@0" ObjectIDND1="48736@x" ObjectIDND2="48763@x" ObjectIDZND0="48738@0" Pin0InfoVect0LinkObjId="SW-315677_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_136b260_0" Pin1InfoVect1LinkObjId="SW-315675_0" Pin1InfoVect2LinkObjId="EC-SB_DT.084Ld_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1076,11 1118,11 1118,29 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1343740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1292,-243 1292,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="48740@1" ObjectIDZND0="48739@1" Pin0InfoVect0LinkObjId="SW-315726_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315727_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1292,-243 1292,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13439a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1292,-197 1292,-180 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="48739@0" ObjectIDZND0="48741@1" Pin0InfoVect0LinkObjId="SW-315727_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315726_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1292,-197 1292,-180 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1348640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1245,-116 1245,-138 1292,-138 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1343c00@0" ObjectIDZND0="g_1347c20@0" ObjectIDZND1="48743@x" ObjectIDZND2="48741@x" Pin0InfoVect0LinkObjId="g_1347c20_0" Pin0InfoVect1LinkObjId="SW-315730_0" Pin0InfoVect2LinkObjId="SW-315727_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1343c00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1245,-116 1245,-138 1292,-138 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13488a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1292,-118 1292,-138 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1347c20@1" ObjectIDZND0="g_1343c00@0" ObjectIDZND1="48743@x" ObjectIDZND2="48741@x" Pin0InfoVect0LinkObjId="g_1343c00_0" Pin0InfoVect1LinkObjId="SW-315730_0" Pin0InfoVect2LinkObjId="SW-315727_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1347c20_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1292,-118 1292,-138 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1348b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1292,-138 1292,-163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_1347c20@0" ObjectIDND1="g_1343c00@0" ObjectIDND2="48743@x" ObjectIDZND0="48741@0" Pin0InfoVect0LinkObjId="SW-315727_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1347c20_0" Pin1InfoVect1LinkObjId="g_1343c00_0" Pin1InfoVect2LinkObjId="SW-315730_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1292,-138 1292,-163 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1348d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1292,-138 1332,-138 1332,-113 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_1347c20@0" ObjectIDND1="g_1343c00@0" ObjectIDND2="48741@x" ObjectIDZND0="48743@0" Pin0InfoVect0LinkObjId="SW-315730_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1347c20_0" Pin1InfoVect1LinkObjId="g_1343c00_0" Pin1InfoVect2LinkObjId="SW-315727_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1292,-138 1332,-138 1332,-113 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1357350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1292,-46 1292,-65 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="48742@1" ObjectIDZND0="g_1347c20@0" Pin0InfoVect0LinkObjId="g_1347c20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315729_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1292,-46 1292,-65 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_135b5d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1246,28 1246,10 1292,10 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="load" ObjectIDND0="g_135a8a0@0" ObjectIDZND0="48742@x" ObjectIDZND1="48744@x" ObjectIDZND2="48764@x" Pin0InfoVect0LinkObjId="SW-315729_0" Pin0InfoVect1LinkObjId="SW-315731_0" Pin0InfoVect2LinkObjId="EC-SB_DT.085Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_135a8a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1246,28 1246,10 1292,10 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_135b830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1292,-10 1292,10 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="load" ObjectIDND0="48742@0" ObjectIDZND0="g_135a8a0@0" ObjectIDZND1="48744@x" ObjectIDZND2="48764@x" Pin0InfoVect0LinkObjId="g_135a8a0_0" Pin0InfoVect1LinkObjId="SW-315731_0" Pin0InfoVect2LinkObjId="EC-SB_DT.085Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315729_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1292,-10 1292,10 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_135ba90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1292,10 1292,136 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="48742@x" ObjectIDND1="g_135a8a0@0" ObjectIDND2="48744@x" ObjectIDZND0="48764@0" Pin0InfoVect0LinkObjId="EC-SB_DT.085Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-315729_0" Pin1InfoVect1LinkObjId="g_135a8a0_0" Pin1InfoVect2LinkObjId="SW-315731_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1292,10 1292,136 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_135bcf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1292,10 1334,10 1334,28 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="load" EndDevType0="switch" ObjectIDND0="48742@x" ObjectIDND1="g_135a8a0@0" ObjectIDND2="48764@x" ObjectIDZND0="48744@0" Pin0InfoVect0LinkObjId="SW-315731_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-315729_0" Pin1InfoVect1LinkObjId="g_135a8a0_0" Pin1InfoVect2LinkObjId="EC-SB_DT.085Ld_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1292,10 1334,10 1334,28 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_125a730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1502,-244 1502,-225 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="48746@1" ObjectIDZND0="48745@1" Pin0InfoVect0LinkObjId="SW-315780_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315781_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1502,-244 1502,-225 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_125a990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1502,-198 1502,-181 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="48745@0" ObjectIDZND0="48747@1" Pin0InfoVect0LinkObjId="SW-315781_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315780_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1502,-198 1502,-181 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_125f630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1455,-117 1455,-139 1502,-139 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_125abf0@0" ObjectIDZND0="g_125ec10@0" ObjectIDZND1="48748@x" ObjectIDZND2="48747@x" Pin0InfoVect0LinkObjId="g_125ec10_0" Pin0InfoVect1LinkObjId="SW-315783_0" Pin0InfoVect2LinkObjId="SW-315781_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_125abf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1455,-117 1455,-139 1502,-139 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_125f890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1502,-119 1502,-139 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_125ec10@1" ObjectIDZND0="g_125abf0@0" ObjectIDZND1="48748@x" ObjectIDZND2="48747@x" Pin0InfoVect0LinkObjId="g_125abf0_0" Pin0InfoVect1LinkObjId="SW-315783_0" Pin0InfoVect2LinkObjId="SW-315781_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_125ec10_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1502,-119 1502,-139 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_125faf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1502,-139 1502,-164 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_125ec10@0" ObjectIDND1="g_125abf0@0" ObjectIDND2="48748@x" ObjectIDZND0="48747@0" Pin0InfoVect0LinkObjId="SW-315781_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_125ec10_0" Pin1InfoVect1LinkObjId="g_125abf0_0" Pin1InfoVect2LinkObjId="SW-315783_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1502,-139 1502,-164 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_125fd50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1502,-139 1542,-139 1542,-114 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_125ec10@0" ObjectIDND1="g_125abf0@0" ObjectIDND2="48747@x" ObjectIDZND0="48748@0" Pin0InfoVect0LinkObjId="SW-315783_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_125ec10_0" Pin1InfoVect1LinkObjId="g_125abf0_0" Pin1InfoVect2LinkObjId="SW-315781_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1502,-139 1542,-139 1542,-114 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_125ffb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1502,-47 1502,-66 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_125ec10@0" Pin0InfoVect0LinkObjId="g_125ec10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1502,-47 1502,-66 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12d4290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1719,-244 1719,-225 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="48750@1" ObjectIDZND0="48749@1" Pin0InfoVect0LinkObjId="SW-315826_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315827_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1719,-244 1719,-225 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12d44f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1719,-198 1719,-181 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="48749@0" ObjectIDZND0="48751@1" Pin0InfoVect0LinkObjId="SW-315827_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315826_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1719,-198 1719,-181 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1260c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1672,-117 1672,-139 1719,-139 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_12d4750@0" ObjectIDZND0="g_12d8770@0" ObjectIDZND1="48752@x" ObjectIDZND2="48751@x" Pin0InfoVect0LinkObjId="g_12d8770_0" Pin0InfoVect1LinkObjId="SW-315829_0" Pin0InfoVect2LinkObjId="SW-315827_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_12d4750_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1672,-117 1672,-139 1719,-139 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1260eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1719,-119 1719,-139 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_12d8770@1" ObjectIDZND0="g_12d4750@0" ObjectIDZND1="48752@x" ObjectIDZND2="48751@x" Pin0InfoVect0LinkObjId="g_12d4750_0" Pin0InfoVect1LinkObjId="SW-315829_0" Pin0InfoVect2LinkObjId="SW-315827_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_12d8770_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1719,-119 1719,-139 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1261110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1719,-139 1719,-164 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_12d8770@0" ObjectIDND1="g_12d4750@0" ObjectIDND2="48752@x" ObjectIDZND0="48751@0" Pin0InfoVect0LinkObjId="SW-315827_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_12d8770_0" Pin1InfoVect1LinkObjId="g_12d4750_0" Pin1InfoVect2LinkObjId="SW-315829_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1719,-139 1719,-164 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1261370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1719,-139 1759,-139 1759,-114 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_12d8770@0" ObjectIDND1="g_12d4750@0" ObjectIDND2="48751@x" ObjectIDZND0="48752@0" Pin0InfoVect0LinkObjId="SW-315829_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_12d8770_0" Pin1InfoVect1LinkObjId="g_12d4750_0" Pin1InfoVect2LinkObjId="SW-315827_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1719,-139 1759,-139 1759,-114 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12615d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1719,-47 1719,-66 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_12d8770@0" Pin0InfoVect0LinkObjId="g_12d8770_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1719,-47 1719,-66 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_126a2b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1926,-245 1926,-226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="48754@1" ObjectIDZND0="48753@1" Pin0InfoVect0LinkObjId="SW-315872_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315873_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1926,-245 1926,-226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_126a510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1926,-199 1926,-182 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="48753@0" ObjectIDZND0="48755@1" Pin0InfoVect0LinkObjId="SW-315873_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315872_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1926,-199 1926,-182 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_122c900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1879,-118 1879,-140 1926,-140 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_126a770@0" ObjectIDZND0="g_122bee0@0" ObjectIDZND1="48756@x" ObjectIDZND2="48755@x" Pin0InfoVect0LinkObjId="g_122bee0_0" Pin0InfoVect1LinkObjId="SW-315875_0" Pin0InfoVect2LinkObjId="SW-315873_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_126a770_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1879,-118 1879,-140 1926,-140 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_122cb60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1926,-120 1926,-140 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_122bee0@1" ObjectIDZND0="g_126a770@0" ObjectIDZND1="48756@x" ObjectIDZND2="48755@x" Pin0InfoVect0LinkObjId="g_126a770_0" Pin0InfoVect1LinkObjId="SW-315875_0" Pin0InfoVect2LinkObjId="SW-315873_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_122bee0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1926,-120 1926,-140 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_122cdc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1926,-140 1926,-165 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_122bee0@0" ObjectIDND1="g_126a770@0" ObjectIDND2="48756@x" ObjectIDZND0="48755@0" Pin0InfoVect0LinkObjId="SW-315873_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_122bee0_0" Pin1InfoVect1LinkObjId="g_126a770_0" Pin1InfoVect2LinkObjId="SW-315875_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1926,-140 1926,-165 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_122d020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1926,-140 1966,-140 1966,-115 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_122bee0@0" ObjectIDND1="g_126a770@0" ObjectIDND2="48755@x" ObjectIDZND0="48756@0" Pin0InfoVect0LinkObjId="SW-315875_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_122bee0_0" Pin1InfoVect1LinkObjId="g_126a770_0" Pin1InfoVect2LinkObjId="SW-315873_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1926,-140 1966,-140 1966,-115 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_122d280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1926,-48 1926,-67 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_122bee0@0" Pin0InfoVect0LinkObjId="g_122bee0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1926,-48 1926,-67 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_122df90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1056,-342 1056,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="48714@0" ObjectIDZND0="48715@1" Pin0InfoVect0LinkObjId="SW-315503_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315502_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1056,-342 1056,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12340f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2114,-262 2114,-282 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48727@0" ObjectIDZND0="48761@0" Pin0InfoVect0LinkObjId="g_12a7b10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315654_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2114,-262 2114,-282 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1234350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2114,-245 2114,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="48727@1" ObjectIDZND0="48728@1" Pin0InfoVect0LinkObjId="SW-315654_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315654_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2114,-245 2114,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12345b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2114,-188 2114,-164 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="48728@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315654_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="2114,-188 2114,-164 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_123a710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1661,-357 1661,-317 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="48732@1" ObjectIDZND0="48731@1" Pin0InfoVect0LinkObjId="SW-315662_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315662_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1661,-357 1661,-317 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_123b270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1661,-488 1661,-458 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_1310ec0@0" ObjectIDZND0="g_123a970@1" Pin0InfoVect0LinkObjId="g_123a970_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1310ec0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1661,-488 1661,-458 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12a6b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1661,-393 1716,-393 1716,-412 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="48732@x" ObjectIDND1="g_123a970@0" ObjectIDZND0="g_13104f0@0" Pin0InfoVect0LinkObjId="g_13104f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-315662_0" Pin1InfoVect1LinkObjId="g_123a970_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1661,-393 1716,-393 1716,-412 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12a7650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1661,-374 1661,-393 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="48732@0" ObjectIDZND0="g_13104f0@0" ObjectIDZND1="g_123a970@0" Pin0InfoVect0LinkObjId="g_13104f0_0" Pin0InfoVect1LinkObjId="g_123a970_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315662_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1661,-374 1661,-393 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12a78b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1661,-393 1661,-414 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_13104f0@0" ObjectIDND1="48732@x" ObjectIDZND0="g_123a970@0" Pin0InfoVect0LinkObjId="g_123a970_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_13104f0_0" Pin1InfoVect1LinkObjId="SW-315662_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1661,-393 1661,-414 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12a7b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1056,-308 1056,-282 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48715@0" ObjectIDZND0="48761@0" Pin0InfoVect0LinkObjId="g_12340f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315503_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1056,-308 1056,-282 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12a7d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="597,-789 597,-767 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48691@0" ObjectIDZND0="48759@0" Pin0InfoVect0LinkObjId="g_1327770_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315291_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="597,-789 597,-767 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12abe90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1888,-550 1888,-572 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_1290200@0" ObjectIDZND0="g_12aaf30@0" Pin0InfoVect0LinkObjId="g_12aaf30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1290200_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1888,-550 1888,-572 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12ac0f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="645,-581 645,-565 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_12ab620@0" ObjectIDZND0="g_128da40@0" Pin0InfoVect0LinkObjId="g_128da40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_12ab620_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="645,-581 645,-565 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12acc50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1827,-879 1827,-890 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_12ac350@1" ObjectIDZND0="g_1328160@0" Pin0InfoVect0LinkObjId="g_1328160_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_12ac350_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1827,-879 1827,-890 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12aceb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1827,-943 1827,-981 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_1328160@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="TF-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1328160_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1827,-943 1827,-981 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12ad110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="446,-260 446,-282 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48718@0" ObjectIDZND0="48761@0" Pin0InfoVect0LinkObjId="g_12340f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315596_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="446,-260 446,-282 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12ad370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="867,-259 867,-282 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48729@0" ObjectIDZND0="48761@0" Pin0InfoVect0LinkObjId="g_12340f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315658_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="867,-259 867,-282 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12ad5d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1076,-259 1076,-282 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48734@0" ObjectIDZND0="48761@0" Pin0InfoVect0LinkObjId="g_12340f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315673_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1076,-259 1076,-282 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12ad830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1292,-260 1292,-282 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48740@0" ObjectIDZND0="48761@0" Pin0InfoVect0LinkObjId="g_12340f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315727_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1292,-260 1292,-282 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12ada90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1502,-261 1502,-282 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48746@0" ObjectIDZND0="48761@0" Pin0InfoVect0LinkObjId="g_12340f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315781_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1502,-261 1502,-282 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12adcf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1719,-261 1719,-282 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48750@0" ObjectIDZND0="48761@0" Pin0InfoVect0LinkObjId="g_12340f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315827_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1719,-261 1719,-282 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12adf50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1926,-262 1926,-282 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48754@0" ObjectIDZND0="48761@0" Pin0InfoVect0LinkObjId="g_12340f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315873_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1926,-262 1926,-282 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12b6170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1683,-961 1696,-961 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="48701@1" ObjectIDZND0="g_12b56e0@0" Pin0InfoVect0LinkObjId="g_12b56e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315389_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1683,-961 1696,-961 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12b63d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1647,-961 1611,-961 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="48701@0" ObjectIDZND0="48700@x" ObjectIDZND1="g_139cc40@0" ObjectIDZND2="g_1326ca0@0" Pin0InfoVect0LinkObjId="SW-315388_0" Pin0InfoVect1LinkObjId="g_139cc40_0" Pin0InfoVect2LinkObjId="g_1326ca0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315389_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1647,-961 1611,-961 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12b6ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1611,-925 1611,-961 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="48700@1" ObjectIDZND0="48701@x" ObjectIDZND1="g_139cc40@0" ObjectIDZND2="g_1326ca0@0" Pin0InfoVect0LinkObjId="SW-315389_0" Pin0InfoVect1LinkObjId="g_139cc40_0" Pin0InfoVect2LinkObjId="g_1326ca0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315388_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1611,-925 1611,-961 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12b7120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1611,-961 1611,-1003 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="voltageTransformer" ObjectIDND0="48701@x" ObjectIDND1="48700@x" ObjectIDZND0="g_139cc40@0" ObjectIDZND1="g_1326ca0@0" ObjectIDZND2="g_122abc0@0" Pin0InfoVect0LinkObjId="g_139cc40_0" Pin0InfoVect1LinkObjId="g_1326ca0_0" Pin0InfoVect2LinkObjId="g_122abc0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-315389_0" Pin1InfoVect1LinkObjId="SW-315388_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1611,-961 1611,-1003 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1208210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1221,-965 1234,-965 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="48697@1" ObjectIDZND0="g_12077e0@0" Pin0InfoVect0LinkObjId="g_12077e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315341_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1221,-965 1234,-965 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1208470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1185,-965 1149,-965 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="voltageTransformer" ObjectIDND0="48697@0" ObjectIDZND0="48696@x" ObjectIDZND1="g_1367640@0" ObjectIDZND2="g_1229930@0" Pin0InfoVect0LinkObjId="SW-315340_0" Pin0InfoVect1LinkObjId="g_1367640_0" Pin0InfoVect2LinkObjId="g_1229930_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315341_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1185,-965 1149,-965 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1208f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1149,-929 1149,-965 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="voltageTransformer" ObjectIDND0="48696@1" ObjectIDZND0="48697@x" ObjectIDZND1="g_1367640@0" ObjectIDZND2="g_1229930@0" Pin0InfoVect0LinkObjId="SW-315341_0" Pin0InfoVect1LinkObjId="g_1367640_0" Pin0InfoVect2LinkObjId="g_1229930_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315340_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1149,-929 1149,-965 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12091a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1149,-965 1149,-1007 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" EndDevType2="powerLine" ObjectIDND0="48697@x" ObjectIDND1="48696@x" ObjectIDZND0="g_1367640@0" ObjectIDZND1="g_1229930@0" ObjectIDZND2="49390@1" Pin0InfoVect0LinkObjId="g_1367640_0" Pin0InfoVect1LinkObjId="g_1229930_0" Pin0InfoVect2LinkObjId="g_138cd30_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-315341_0" Pin1InfoVect1LinkObjId="SW-315340_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1149,-965 1149,-1007 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1209df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="633,-960 597,-960 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="voltageTransformer" ObjectIDND0="48693@0" ObjectIDZND0="48692@x" ObjectIDZND1="g_13f4f90@0" ObjectIDZND2="g_122a140@0" Pin0InfoVect0LinkObjId="SW-315292_0" Pin0InfoVect1LinkObjId="g_13f4f90_0" Pin0InfoVect2LinkObjId="g_122a140_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315293_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="633,-960 597,-960 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_120a8c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="597,-924 597,-960 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" EndDevType2="powerLine" ObjectIDND0="48692@1" ObjectIDZND0="g_13f4f90@0" ObjectIDZND1="g_122a140@0" ObjectIDZND2="48350@1" Pin0InfoVect0LinkObjId="g_13f4f90_0" Pin0InfoVect1LinkObjId="g_122a140_0" Pin0InfoVect2LinkObjId="g_13a54c0_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315292_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="597,-924 597,-960 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_120ab20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="597,-960 597,-1002 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" EndDevType2="powerLine" ObjectIDND0="48692@x" ObjectIDND1="48693@x" ObjectIDZND0="g_13f4f90@0" ObjectIDZND1="g_122a140@0" ObjectIDZND2="48350@1" Pin0InfoVect0LinkObjId="g_13f4f90_0" Pin0InfoVect1LinkObjId="g_122a140_0" Pin0InfoVect2LinkObjId="g_13a54c0_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-315292_0" Pin1InfoVect1LinkObjId="SW-315293_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="597,-960 597,-1002 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_120ad80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="669,-960 682,-960 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="48693@1" ObjectIDZND0="g_1209400@0" Pin0InfoVect0LinkObjId="g_1209400_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315293_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="669,-960 682,-960 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_120d2f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="597,-870 597,-888 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="48690@1" ObjectIDZND0="48692@0" Pin0InfoVect0LinkObjId="SW-315292_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315290_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="597,-870 597,-888 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_120d550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="597,-825 597,-843 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="48691@1" ObjectIDZND0="48690@0" Pin0InfoVect0LinkObjId="SW-315290_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315291_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="597,-825 597,-843 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_120d7b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1149,-875 1149,-893 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="48694@1" ObjectIDZND0="48696@0" Pin0InfoVect0LinkObjId="SW-315340_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315338_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1149,-875 1149,-893 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_120da10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1149,-830 1149,-848 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="48695@1" ObjectIDZND0="48694@0" Pin0InfoVect0LinkObjId="SW-315338_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315339_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1149,-830 1149,-848 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_120dc70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1611,-871 1611,-889 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="48698@1" ObjectIDZND0="48700@0" Pin0InfoVect0LinkObjId="SW-315388_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315386_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1611,-871 1611,-889 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_120ded0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1611,-826 1611,-844 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="48699@1" ObjectIDZND0="48698@0" Pin0InfoVect0LinkObjId="SW-315386_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315387_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1611,-826 1611,-844 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_120f800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1056,-710 1056,-686 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="48713@0" ObjectIDZND0="48712@1" Pin0InfoVect0LinkObjId="SW-315493_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315494_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1056,-710 1056,-686 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12134e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="867,-220 867,-242 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_127fee0@0" ObjectIDZND0="48729@1" Pin0InfoVect0LinkObjId="SW-315658_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_127fee0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="867,-220 867,-242 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_127d360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="867,-166 867,-188 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="48730@1" ObjectIDZND0="g_127fee0@1" Pin0InfoVect0LinkObjId="g_127fee0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315658_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="867,-166 867,-188 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_127fc80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="867,-48 867,-4 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_127f350@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="TF-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_127f350_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="867,-48 867,-4 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1280ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="867,-149 867,-101 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="48730@0" ObjectIDZND0="g_127f350@1" Pin0InfoVect0LinkObjId="g_127f350_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315658_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="867,-149 867,-101 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_121d130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1827,-835 1827,-824 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_12ac350@0" ObjectIDZND0="48705@1" Pin0InfoVect0LinkObjId="SW-315458_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_12ac350_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1827,-835 1827,-824 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_121d320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1661,-300 1661,-282 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48731@0" ObjectIDZND0="48761@0" Pin0InfoVect0LinkObjId="g_12340f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315662_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1661,-300 1661,-282 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_121da00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="446,-65 446,-36 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_13339f0@0" ObjectIDZND0="g_121dc30@0" Pin0InfoVect0LinkObjId="g_121dc30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13339f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="446,-65 446,-36 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1222af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="662,-260 662,-282 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48722@0" ObjectIDZND0="48761@0" Pin0InfoVect0LinkObjId="g_12340f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315627_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="662,-260 662,-282 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12231d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="662,-243 662,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="48722@1" ObjectIDZND0="48721@1" Pin0InfoVect0LinkObjId="SW-315626_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315627_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="662,-243 662,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12233e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="662,-197 662,-180 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="48721@0" ObjectIDZND0="48723@1" Pin0InfoVect0LinkObjId="SW-315627_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315626_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="662,-197 662,-180 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1223610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="615,-116 615,-144 662,-143 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_13133a0@0" ObjectIDZND0="48723@x" ObjectIDZND1="g_1370af0@0" ObjectIDZND2="48725@x" Pin0InfoVect0LinkObjId="SW-315627_0" Pin0InfoVect1LinkObjId="g_1370af0_0" Pin0InfoVect2LinkObjId="SW-315630_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13133a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="615,-116 615,-144 662,-143 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1223840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="703,-117 703,-144 663,-143 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="48725@0" ObjectIDZND0="g_13133a0@0" ObjectIDZND1="48723@x" ObjectIDZND2="g_1370af0@0" Pin0InfoVect0LinkObjId="g_13133a0_0" Pin0InfoVect1LinkObjId="SW-315627_0" Pin0InfoVect2LinkObjId="g_1370af0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315630_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="703,-117 703,-144 663,-143 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1223a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="662,-65 662,-51 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1370af0@0" ObjectIDZND0="48724@1" Pin0InfoVect0LinkObjId="SW-315629_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1370af0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="662,-65 662,-51 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1225500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="662,-163 662,-143 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="48723@0" ObjectIDZND0="g_13133a0@0" ObjectIDZND1="g_1370af0@0" ObjectIDZND2="48725@x" Pin0InfoVect0LinkObjId="g_13133a0_0" Pin0InfoVect1LinkObjId="g_1370af0_0" Pin0InfoVect2LinkObjId="SW-315630_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315627_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="662,-163 662,-143 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12256f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="662,-143 662,-118 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_13133a0@0" ObjectIDND1="48723@x" ObjectIDND2="48725@x" ObjectIDZND0="g_1370af0@1" Pin0InfoVect0LinkObjId="g_1370af0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_13133a0_0" Pin1InfoVect1LinkObjId="SW-315627_0" Pin1InfoVect2LinkObjId="SW-315630_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="662,-143 662,-118 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1227640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1611,-1076 1611,-1108 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="powerLine" ObjectIDND0="g_1326ca0@1" ObjectIDZND0="48919@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1326ca0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1611,-1076 1611,-1108 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1292a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1311,-706 1311,-695 1376,-695 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="48703@0" ObjectIDZND0="48702@1" Pin0InfoVect0LinkObjId="SW-315434_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315435_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1311,-706 1311,-695 1376,-695 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1293290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="591,4 663,4 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="capacitor" ObjectIDND0="48726@0" ObjectIDZND0="48724@x" ObjectIDZND1="48762@x" Pin0InfoVect0LinkObjId="SW-315629_0" Pin0InfoVect1LinkObjId="CB-SB_DT.SB_DT_Cb1_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315631_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="591,4 663,4 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1293c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="662,26 662,4 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="48762@0" ObjectIDZND0="48726@x" ObjectIDZND1="48724@x" Pin0InfoVect0LinkObjId="SW-315631_0" Pin0InfoVect1LinkObjId="SW-315629_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="CB-SB_DT.SB_DT_Cb1_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="662,26 662,4 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1293df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="662,4 662,-15 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" EndDevType0="switch" ObjectIDND0="48726@x" ObjectIDND1="48762@x" ObjectIDZND0="48724@0" Pin0InfoVect0LinkObjId="SW-315629_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-315631_0" Pin1InfoVect1LinkObjId="CB-SB_DT.SB_DT_Cb1_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="662,4 662,-15 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1299410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="417,30 412,30 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="417,30 412,30 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1299800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="417,41 412,41 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="417,41 412,41 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_129c250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="398,58 390,58 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="398,58 390,58 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_129ebd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1056,-405 1056,-436 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="48716@0" ObjectIDZND0="g_1353150@0" ObjectIDZND1="g_1352610@0" Pin0InfoVect0LinkObjId="g_1353150_0" Pin0InfoVect1LinkObjId="g_1352610_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315503_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1056,-405 1056,-436 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_129ee30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1056,-436 1056,-472 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_1353150@0" ObjectIDND1="48716@x" ObjectIDZND0="g_1352610@0" Pin0InfoVect0LinkObjId="g_1352610_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1353150_0" Pin1InfoVect1LinkObjId="SW-315503_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1056,-436 1056,-472 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-315159" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 147.000000 -969.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48657" ObjectName="DYN-SB_DT"/>
     <cge:Meas_Ref ObjectId="315159"/>
    </metadata>
   </g>
  </g><g id="CircleFilled_Layer">
   <ellipse DF8003:Layer="PUBLIC" cx="415" cy="39" fill="none" fillStyle="0" rx="6" ry="6.5" stroke="rgb(60,120,255)" stroke-width="1"/>
   <ellipse DF8003:Layer="PUBLIC" cx="415" cy="32" fill="none" fillStyle="0" rx="6" ry="6.5" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="388" cy="58" fill="none" fillStyle="0" r="2.5" stroke="rgb(60,120,255)" stroke-width="1"/>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="SB_DT" endPointId="0" endStationName="PAS_T1" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_aitianD_line" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="597,-1079 597,-1120 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="48350" ObjectName="AC-35kV.LN_aitianD_line"/>
    <cge:TPSR_Ref TObjectID="48350_SS-340"/></metadata>
   <polyline fill="none" opacity="0" points="597,-1079 597,-1120 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="SB_DT" endPointId="0" endStationName="SB_EJ" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_tiane" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1149,-1088 1149,-1128 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="49390" ObjectName="AC-35kV.LN_tiane"/>
    <cge:TPSR_Ref TObjectID="49390_SS-340"/></metadata>
   <polyline fill="none" opacity="0" points="1149,-1088 1149,-1128 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="PAS_T2" endPointId="0" endStationName="SB_DT" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_ShuangTian_dt1" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1611,-1104 1611,-1143 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="48919" ObjectName="AC-35kV.LN_ShuangTian_dt1"/>
    <cge:TPSR_Ref TObjectID="48919_SS-340"/></metadata>
   <polyline fill="none" opacity="0" points="1611,-1104 1611,-1143 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="48760" cx="1611" cy="-768" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48760" cx="1827" cy="-768" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48759" cx="1149" cy="-767" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48759" cx="645" cy="-767" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48759" cx="1056" cy="-767" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48759" cx="1311" cy="-767" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48760" cx="1470" cy="-768" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48760" cx="1888" cy="-768" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48759" cx="597" cy="-767" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48761" cx="2114" cy="-282" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48761" cx="446" cy="-282" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48761" cx="867" cy="-282" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48761" cx="1076" cy="-282" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48761" cx="1292" cy="-282" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48761" cx="1502" cy="-282" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48761" cx="1719" cy="-282" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48761" cx="1926" cy="-282" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48761" cx="1056" cy="-282" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48761" cx="1661" cy="-282" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48761" cx="662" cy="-282" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-315493">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1047.330059 -651.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48712" ObjectName="SW-SB_DT.SB_DT_301BK"/>
     <cge:Meas_Ref ObjectId="315493"/>
    <cge:TPSR_Ref TObjectID="48712"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-315290">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 587.732799 -835.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48690" ObjectName="SW-SB_DT.SB_DT_361BK"/>
     <cge:Meas_Ref ObjectId="315290"/>
    <cge:TPSR_Ref TObjectID="48690"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-315434">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1411.999817 -686.097250)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48702" ObjectName="SW-SB_DT.SB_DT_312BK"/>
     <cge:Meas_Ref ObjectId="315434"/>
    <cge:TPSR_Ref TObjectID="48702"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-315338">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1139.732799 -840.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48694" ObjectName="SW-SB_DT.SB_DT_362BK"/>
     <cge:Meas_Ref ObjectId="315338"/>
    <cge:TPSR_Ref TObjectID="48694"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-315386">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1601.732799 -836.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48698" ObjectName="SW-SB_DT.SB_DT_363BK"/>
     <cge:Meas_Ref ObjectId="315386"/>
    <cge:TPSR_Ref TObjectID="48698"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-315502">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1047.000000 -334.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48714" ObjectName="SW-SB_DT.SB_DT_001BK"/>
     <cge:Meas_Ref ObjectId="315502"/>
    <cge:TPSR_Ref TObjectID="48714"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-315595">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 437.000000 -189.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48717" ObjectName="SW-SB_DT.SB_DT_081BK"/>
     <cge:Meas_Ref ObjectId="315595"/>
    <cge:TPSR_Ref TObjectID="48717"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-315626">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 653.000000 -189.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48721" ObjectName="SW-SB_DT.SB_DT_082BK"/>
     <cge:Meas_Ref ObjectId="315626"/>
    <cge:TPSR_Ref TObjectID="48721"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-315672">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1067.000000 -188.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48733" ObjectName="SW-SB_DT.SB_DT_084BK"/>
     <cge:Meas_Ref ObjectId="315672"/>
    <cge:TPSR_Ref TObjectID="48733"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-315726">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1283.000000 -189.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48739" ObjectName="SW-SB_DT.SB_DT_085BK"/>
     <cge:Meas_Ref ObjectId="315726"/>
    <cge:TPSR_Ref TObjectID="48739"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-315780">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1493.000000 -190.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48745" ObjectName="SW-SB_DT.SB_DT_086BK"/>
     <cge:Meas_Ref ObjectId="315780"/>
    <cge:TPSR_Ref TObjectID="48745"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-315826">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1710.000000 -190.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48749" ObjectName="SW-SB_DT.SB_DT_087BK"/>
     <cge:Meas_Ref ObjectId="315826"/>
    <cge:TPSR_Ref TObjectID="48749"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-315872">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1917.000000 -191.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48753" ObjectName="SW-SB_DT.SB_DT_088BK"/>
     <cge:Meas_Ref ObjectId="315872"/>
    <cge:TPSR_Ref TObjectID="48753"/></metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_13bab40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 343.000000 -311.000000) translate(0,15)">10kVI段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_e30fe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 552.732799 -918.000000) translate(0,12)">3616</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1138220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 548.732799 -806.000000) translate(0,12)">3611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_108a170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1071.330059 -680.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13d7ef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1003.330059 -734.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_132f4a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 558.732799 -861.000000) translate(0,12)">361</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13ed910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1672.597250 -345.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13ef590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 388.000000 170.000000) translate(0,12)">10kV接地变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13ef780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1474.000000 170.000000) translate(0,12)">10kV备用1</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13efab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1692.000000 170.000000) translate(0,12)">10kV备用2</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13efc60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1910.000000 170.000000) translate(0,12)">10kV备用3</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13eff90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1618.597250 -562.000000) translate(0,12)">10kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13eff90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1618.597250 -562.000000) translate(0,27)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13f0140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 609.597250 -511.000000) translate(0,12)">35kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13f0140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 609.597250 -511.000000) translate(0,27)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13fbde0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 556.597250 -1141.000000) translate(0,12)">35kV爱田线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13fbf50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1111.597250 -1153.000000) translate(0,12)">35kV田鄂线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13fc140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1893.597250 -1072.000000) translate(0,12)">35kV1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13fc330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2027.597250 -752.000000) translate(0,12)">35kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_13fc520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -162.000000 -935.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_13fc520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -162.000000 -935.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_13fc520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -162.000000 -935.000000) translate(0,59)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_13fc520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -162.000000 -935.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_13fc520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -162.000000 -935.000000) translate(0,101)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_13fc520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -162.000000 -935.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_13fc520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -162.000000 -935.000000) translate(0,143)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_13fc520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -162.000000 -935.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_13fc520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -162.000000 -935.000000) translate(0,185)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_13fc520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -162.000000 -935.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_13fc520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -162.000000 -935.000000) translate(0,227)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1384330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -174.000000 -482.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1384330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -174.000000 -482.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1384330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -174.000000 -482.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1384330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -174.000000 -482.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1384330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -174.000000 -482.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1384330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -174.000000 -482.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1384330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -174.000000 -482.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1384330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -174.000000 -482.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1384330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -174.000000 -482.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1384330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -174.000000 -482.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1384330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -174.000000 -482.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1384330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -174.000000 -482.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1384330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -174.000000 -482.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1384330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -174.000000 -482.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1384330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -174.000000 -482.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1384330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -174.000000 -482.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1384330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -174.000000 -482.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1384330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -174.000000 -482.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_12f95e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -36.000000 -1061.500000) translate(0,16)">独田变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_12f97e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 186.000000 -1075.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_13fe2c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 185.000000 -1034.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_1383ee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 94.500000 -1069.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_13aa5f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -184.000000 -86.000000) translate(0,17)">楚雄巡维中</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_13aa5f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -184.000000 -86.000000) translate(0,38)">心变运三班：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_104a790" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -56.000000 -96.500000) translate(0,17)">18787878955</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_104a790" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -56.000000 -96.500000) translate(0,38)">18787878953</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_104a790" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -56.000000 -96.500000) translate(0,59)">18787878979</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1367090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1104.732799 -923.000000) translate(0,12)">3626</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1367460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1100.732799 -811.000000) translate(0,12)">3621</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_138c770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1108.732799 -866.000000) translate(0,12)">362</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_139c690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1566.732799 -919.000000) translate(0,12)">3636</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_139ca60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1562.732799 -807.000000) translate(0,12)">3632</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_134cda0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1572.732799 -862.000000) translate(0,12)">363</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_130c530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1852.597250 -499.000000) translate(0,12)">35kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_130c530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1852.597250 -499.000000) translate(0,27)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13cf690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 820.000000 119.000000) translate(0,12)">10kV2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12a8580" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1089.000000 -569.000000) translate(0,12)">SZ20-5000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12a8580" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1089.000000 -569.000000) translate(0,27)">35±3×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12a8580" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1089.000000 -569.000000) translate(0,42)">YN，d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12a8580" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1089.000000 -569.000000) translate(0,57)">7.7%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_120e130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 642.000000 -986.000000) translate(0,12)">36167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_120e760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1194.000000 -991.000000) translate(0,12)">36267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_120e9a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1656.000000 -987.000000) translate(0,12)">36367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_120ebe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1834.000000 -813.000000) translate(0,12)">3642</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_120f100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1895.000000 -690.000000) translate(0,12)">3902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_120f380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1822.000000 -741.000000) translate(0,12)">39020</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_120f5c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1822.000000 -671.000000) translate(0,12)">39027</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_120f9f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 580.000000 -750.000000) translate(0,12)">39010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_120fca0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 580.000000 -680.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_120fee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 495.000000 -97.000000) translate(0,12)">08160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1210120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 712.000000 -101.000000) translate(0,12)">08260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1210360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 669.000000 -40.000000) translate(0,12)">0826</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12105a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 559.000000 -22.000000) translate(0,12)">08267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12107e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2125.000000 -226.000000) translate(0,12)">0121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1210a20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1975.000000 -99.000000) translate(0,12)">08860</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1210c60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1768.000000 -98.000000) translate(0,12)">08760</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1210ea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1728.000000 -219.000000) translate(0,12)">087</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12110e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1511.000000 -219.000000) translate(0,12)">086</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1211320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1551.000000 -98.000000) translate(0,12)">08660</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1211560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1301.000000 -218.000000) translate(0,12)">085</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12117a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1341.000000 -97.000000) translate(0,12)">08560</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12119e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1299.000000 -35.000000) translate(0,12)">0856</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1211c20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1343.000000 43.000000) translate(0,12)">08567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1211e60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1085.000000 -217.000000) translate(0,12)">084</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12120a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1125.000000 -96.000000) translate(0,12)">08460</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12122e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1083.000000 -34.000000) translate(0,12)">0846</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1212520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1127.000000 44.000000) translate(0,12)">08467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1212760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 671.000000 -218.000000) translate(0,12)">082</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12129a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 455.000000 -218.000000) translate(0,12)">081</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1212be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1065.000000 -365.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1212e20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1378.000000 -719.000000) translate(0,12)">312</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1213060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1318.000000 -731.000000) translate(0,12)">3121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12132a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1477.000000 -730.000000) translate(0,12)">3122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12808b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 882.000000 -212.000000) translate(0,12)">0831</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1219670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 652.000000 -699.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12198b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1042.000000 177.000000) translate(0,12)">10kV龙田线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1219e20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1260.000000 170.000000) translate(0,12)">10kV孔雀岭线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_121acc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1086.000000 -604.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_121c170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1935.000000 -220.000000) translate(0,12)">088</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_121c770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -157.000000 -683.000000) translate(0,12)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1224400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 621.000000 170.000000) translate(0,12)">10kV1号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12278a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1578.000000 -1164.000000) translate(0,12)">35kV双田线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1292c60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 406.597250 -746.000000) translate(0,12)">35kVI段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_12973b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 441.000000 3.000000) translate(0,8)">接地变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_129a620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 345.000000 -6.000000) translate(0,8)">(接触器)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_129b6d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 335.000000 31.000000) translate(0,8)">(接地电阻)</text>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_12ee4e0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 571.865666 -648.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12f03b0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 573.865666 -718.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13a2f90" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 1814.865666 -639.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_139d730" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 1816.865666 -709.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12b56e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1691.617415 -955.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12077e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1229.617415 -959.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1209400" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 677.617415 -954.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="SB_DT"/>
</svg>