<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-145" aopId="786686" id="thSvg" product="E8000V2" version="1.0" viewBox="3118 -1198 2738 1314">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="capacitor:shape32">
    <polyline arcFlag="1" points="36,25 37,25 38,25 38,25 39,26 39,26 40,26 40,27 41,27 41,28 41,29 41,29 42,30 42,31 42,31 41,32 41,33 41,33 41,34 40,34 40,35 39,35 39,35 38,36 38,36 37,36 36,36 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="19" x2="19" y1="2" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="19" x2="19" y1="21" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="36" y1="14" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="36" y1="55" y2="47"/>
    <polyline arcFlag="1" points="36,14 37,14 38,14 38,14 39,14 39,15 40,15 40,16 41,16 41,17 41,17 41,18 42,19 42,19 42,20 41,21 41,21 41,22 41,22 40,23 40,23 39,24 39,24 38,24 38,25 37,25 36,25 " stroke-width="1"/>
    <polyline arcFlag="1" points="36,36 37,36 38,36 38,37 39,37 39,37 40,38 40,38 41,39 41,39 41,40 41,40 42,41 42,42 42,42 41,43 41,44 41,44 41,45 40,45 40,46 39,46 39,47 38,47 38,47 37,47 36,47 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.368819" x1="2" x2="35" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.44164" x1="19" x2="36" y1="8" y2="8"/>
    <rect height="23" stroke-width="0.369608" width="12" x="13" y="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.356919" x1="19" x2="36" y1="55" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="2" x2="2" y1="2" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="35" x2="35" y1="2" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.271035" x1="11" x2="26" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="11" x2="26" y1="21" y2="21"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="12" x2="0" y1="13" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="5" x2="7" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="3" x2="9" y1="17" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="14" y2="5"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="generator:shape3">
    <polyline arcFlag="1" points="26,-19 26,-18 26,-17 26,-16 25,-15 25,-14 24,-13 24,-13 23,-12 22,-12 21,-11 20,-11 19,-11 18,-10 17,-11 16,-11 15,-11 14,-12 14,-12 13,-13 12,-13 12,-14 11,-15 11,-16 11,-17 10,-18 11,-19 " stroke-width="0.06"/>
    <circle cx="25" cy="-20" fillStyle="0" r="24" stroke-width="0.5"/>
    <polyline points="41,-19 42,-20 41,-20 41,-21 41,-22 40,-23 40,-24 39,-25 38,-25 38,-26 37,-26 36,-27 35,-27 34,-27 33,-27 32,-27 31,-26 30,-26 29,-25 28,-25 28,-24 27,-23 27,-22 26,-21 26,-20 26,-20 26,-19 " stroke-width="0.06"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="8" x2="5" y1="1" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="10" x2="3" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="8" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="1" x2="12" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="13" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="9" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
   </symbol>
   <symbol id="reactance:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="13" x2="13" y1="39" y2="47"/>
    <polyline points="13,39 15,39 17,38 18,38 20,37 21,36 23,35 24,33 25,31 25,30 26,28 26,26 26,24 25,22 25,21 24,19 23,18 21,16 20,15 18,14 17,14 15,13 13,13 11,13 9,14 8,14 6,15 5,16 3,18 2,19 1,21 1,22 0,24 0,26 " stroke-width="0.0972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="0" x2="12" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.44" x1="13" x2="13" y1="5" y2="26"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape18_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="57" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="13" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="22" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="13" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="66" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="65" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="74" y2="65"/>
   </symbol>
   <symbol id="switch2:shape18_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="74" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="5" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="65" y2="74"/>
   </symbol>
   <symbol id="switch2:shape18-UnNor1">
    <circle cx="10" cy="69" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="14" y2="65"/>
    <circle cx="10" cy="11" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="13" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="22" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="66" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="65" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="74" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="57" y2="66"/>
   </symbol>
   <symbol id="switch2:shape18-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="57" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="66"/>
    <circle cx="10" cy="11" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="13" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="22" y2="13"/>
    <circle cx="10" cy="69" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="66" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="65" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="74" y2="65"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="transformer2:shape3_0">
    <ellipse cx="13" cy="34" fillStyle="0" rx="13" ry="12.5" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="40" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="36" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="32" y2="36"/>
   </symbol>
   <symbol id="transformer2:shape3_1">
    <circle cx="13" cy="18" fillStyle="0" r="13" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="20" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="16" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="12" y2="16"/>
   </symbol>
   <symbol id="transformer2:shape13_0">
    <ellipse cx="38" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="66" x2="71" y1="83" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="71" x2="69" y1="83" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="71" x2="71" y1="80" y2="80"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.911765" x1="1" x2="69" y1="44" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="30" x2="38" y1="74" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="46" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="38" y1="58" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape13_1">
    <circle cx="38" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="29" x2="46" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="46" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="29" y1="34" y2="18"/>
   </symbol>
   <symbol id="transformer2:shape48_0">
    <ellipse cx="25" cy="29" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="17" x2="25" y1="32" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="33" y1="24" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="25" y1="16" y2="24"/>
   </symbol>
   <symbol id="transformer2:shape48_1">
    <circle cx="25" cy="61" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="16" x2="33" y1="59" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="33" y1="75" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="16" y1="75" y2="59"/>
   </symbol>
   <symbol id="voltageTransformer:shape79">
    <circle cx="18" cy="24" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="34" y1="13" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="13" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="19" y1="11" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="19" y1="11" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="25" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="7" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="7" y1="23" y2="25"/>
    <circle cx="18" cy="12" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="8" y1="11" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="8" y1="13" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="15" y2="9"/>
    <polyline points="40,23 28,32 28,36 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="34" y1="37" y2="46"/>
    <rect height="14" stroke-width="1" width="8" x="30" y="23"/>
    <circle cx="7" cy="24" r="7.5" stroke-width="1"/>
    <circle cx="7" cy="12" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="37" y1="48" y2="48"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="36" y1="50" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="39" y1="46" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="25" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="19" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="19" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="34" y1="23" y2="13"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2bc1570" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2bc1f50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2bc2930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2bc3610" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2bc4810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2bc5420" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2bc5fd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2bc6a00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2bc7270" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="55" stroke="rgb(255,0,0)" stroke-width="9.28571" width="98" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2bc7c50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 52.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2bc7c50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 52.000000) translate(0,35)">二种工作</text>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2bc9b50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2bc9b50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_2bcaaf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2bcc6f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2bcd2a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2bcdb90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2bce470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">禁止操作</text>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2bcfb80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2bd0350" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2bd0a40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2bd1400" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2bd1f40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2bd28c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2bd33b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="78" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2bd4740" transform="matrix(1.246377 -0.000000 0.000000 -1.035714 2.739130 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2bd8e50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">测试</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2bd9820" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2bd6540" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2bd6db0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2bde780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2bdb750" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1324" width="2748" x="3113" y="-1203"/>
  </g><g id="ArcThreePoints_Layer">
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="3497,-40 3497,-41 3497,-42 3497,-43 3496,-44 3496,-45 3495,-46 3495,-46 3494,-47 3493,-47 3492,-48 3491,-48 3490,-48 3489,-49 3488,-48 3487,-48 3486,-48 3485,-47 3485,-47 3484,-46 3483,-46 3483,-45 3482,-44 3482,-43 3482,-42 3481,-41 3482,-40 " stroke="rgb(60,120,255)" stroke-width="1.14"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3512,-40 3513,-39 3512,-39 3512,-38 3512,-37 3511,-36 3511,-35 3510,-34 3509,-34 3509,-33 3508,-33 3507,-32 3506,-32 3505,-32 3504,-32 3503,-32 3502,-33 3501,-33 3500,-34 3499,-34 3499,-35 3498,-36 3498,-37 3497,-38 3497,-39 3497,-39 3497,-40 " stroke="rgb(60,120,255)" stroke-width="1.14"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="3599,-40 3599,-41 3599,-42 3599,-43 3598,-44 3598,-45 3597,-46 3597,-46 3596,-47 3595,-47 3594,-48 3593,-48 3592,-48 3591,-49 3590,-48 3589,-48 3588,-48 3587,-47 3587,-47 3586,-46 3585,-46 3585,-45 3584,-44 3584,-43 3584,-42 3583,-41 3584,-40 " stroke="rgb(60,120,255)" stroke-width="1.14"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3614,-40 3615,-39 3614,-39 3614,-38 3614,-37 3613,-36 3613,-35 3612,-34 3611,-34 3611,-33 3610,-33 3609,-32 3608,-32 3607,-32 3606,-32 3605,-32 3604,-33 3603,-33 3602,-34 3601,-34 3601,-35 3600,-36 3600,-37 3599,-38 3599,-39 3599,-39 3599,-40 " stroke="rgb(60,120,255)" stroke-width="1.14"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="3595" x2="3573" y1="-336" y2="-336"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3647,-378 3584,-378 3584,-374 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="3584" x2="3579" y1="-369" y2="-364"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="3589" x2="3584" y1="-369" y2="-374"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="3584" x2="3579" y1="-374" y2="-369"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="3589" x2="3584" y1="-364" y2="-369"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="3584" x2="3584" y1="-369" y2="-336"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.9" x1="3708" x2="3699" y1="-219" y2="-210"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.9" x1="3717" x2="3708" y1="-218" y2="-227"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.9" x1="3708" x2="3699" y1="-227" y2="-218"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.9" x1="3717" x2="3708" y1="-210" y2="-219"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="3708" x2="3708" y1="-262" y2="-227"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="3708" x2="3708" y1="-219" y2="-163"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.9" x1="3708" x2="3699" y1="-121" y2="-130"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.9" x1="3717" x2="3708" y1="-122" y2="-113"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.9" x1="3708" x2="3699" y1="-113" y2="-122"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.9" x1="3717" x2="3708" y1="-130" y2="-121"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="3708" x2="3708" y1="-143" y2="-121"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="3694" x2="3694" y1="-143" y2="-139"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="3722" x2="3722" y1="-143" y2="-139"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="3694" x2="3722" y1="-139" y2="-139"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="3714" x2="3702" y1="-104" y2="-104"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="3709" x2="3707" y1="-97" y2="-97"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="3711" x2="3705" y1="-100" y2="-100"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="3708" x2="3708" y1="-104" y2="-113"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3708,-242 3747,-242 3747,-190 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.25" x1="3748" x2="3745" y1="-168" y2="-168"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.39375" x1="3750" x2="3743" y1="-170" y2="-170"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.305149" x1="3747" x2="3747" y1="-174" y2="-184"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.611465" x1="3741" x2="3752" y1="-174" y2="-174"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.25" x1="3976" x2="3973" y1="71" y2="71"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.39375" x1="3978" x2="3971" y1="68" y2="68"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.305149" x1="3975" x2="3975" y1="64" y2="54"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.611465" x1="3969" x2="3980" y1="64" y2="64"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="3975" x2="3975" y1="-24" y2="48"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.9" x1="4047" x2="4038" y1="-124" y2="-115"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.9" x1="4056" x2="4047" y1="-123" y2="-132"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.9" x1="4047" x2="4038" y1="-132" y2="-123"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.9" x1="4056" x2="4047" y1="-115" y2="-124"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="4047" x2="4047" y1="-149" y2="-132"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="4047" x2="4047" y1="-124" y2="-68"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.9" x1="4047" x2="4038" y1="-26" y2="-35"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.9" x1="4056" x2="4047" y1="-27" y2="-18"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.9" x1="4047" x2="4038" y1="-18" y2="-27"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.9" x1="4056" x2="4047" y1="-35" y2="-26"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="4047" x2="4047" y1="-48" y2="-26"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="4033" x2="4033" y1="-48" y2="-44"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="4061" x2="4061" y1="-48" y2="-44"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="4033" x2="4061" y1="-44" y2="-44"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="4053" x2="4041" y1="-9" y2="-9"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="4048" x2="4046" y1="-2" y2="-2"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="4050" x2="4044" y1="-5" y2="-5"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="4047" x2="4047" y1="-9" y2="-18"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="3975" x2="4178" y1="-149" y2="-149"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="4123" x2="4101" y1="-73" y2="-73"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="4112" x2="4112" y1="-124" y2="-73"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.9" x1="4112" x2="4103" y1="-124" y2="-115"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.9" x1="4121" x2="4112" y1="-123" y2="-132"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.9" x1="4112" x2="4103" y1="-132" y2="-123"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.9" x1="4121" x2="4112" y1="-115" y2="-124"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="4112" x2="4112" y1="-149" y2="-132"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="4178" x2="4178" y1="-124" y2="-73"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.9" x1="4178" x2="4169" y1="-124" y2="-115"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.9" x1="4187" x2="4178" y1="-123" y2="-132"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.9" x1="4178" x2="4169" y1="-132" y2="-123"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.9" x1="4187" x2="4178" y1="-115" y2="-124"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="4178" x2="4178" y1="-149" y2="-132"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="4290" x2="4290" y1="-1003" y2="-976"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="5122" x2="5257" y1="-447" y2="-447"/>
  </g><g id="PolygonFilled_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4283,-1124 4297,-1124 4290,-1136 4283,-1124 " stroke="rgb(60,120,255)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3926,-1078 3940,-1078 3933,-1066 3926,-1078 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3926,-1042 3940,-1042 3933,-1054 3926,-1042 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3926,-812 3940,-812 3933,-800 3926,-812 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3926,-775 3940,-775 3933,-787 3926,-775 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4232,-405 4246,-405 4239,-393 4232,-405 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4234,-380 4244,-380 4239,-363 4234,-380 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4403,-406 4417,-406 4410,-394 4403,-406 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4403,-346 4417,-346 4410,-358 4403,-346 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4584,-406 4598,-406 4591,-394 4584,-406 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4584,-346 4598,-346 4591,-358 4584,-346 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4753,-406 4767,-406 4760,-394 4753,-406 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4753,-346 4767,-346 4760,-358 4753,-346 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4931,-406 4945,-406 4938,-394 4931,-406 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4931,-363 4945,-363 4938,-375 4931,-363 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3884,-405 3898,-405 3891,-393 3884,-405 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3886,-380 3896,-380 3891,-363 3886,-380 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3640,-405 3654,-405 3647,-393 3640,-405 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3492,-143 3506,-143 3499,-131 3492,-143 " stroke="rgb(60,120,255)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3492,-102 3506,-102 3499,-114 3492,-102 " stroke="rgb(60,120,255)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3594,-143 3608,-143 3601,-131 3594,-143 " stroke="rgb(60,120,255)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3594,-102 3608,-102 3601,-114 3594,-102 " stroke="rgb(60,120,255)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3837,-143 3851,-143 3844,-131 3837,-143 " stroke="rgb(60,120,255)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3837,-102 3851,-102 3844,-114 3837,-102 " stroke="rgb(60,120,255)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3968,-143 3982,-143 3975,-131 3968,-143 " stroke="rgb(60,120,255)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3968,-102 3982,-102 3975,-114 3968,-102 " stroke="rgb(60,120,255)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3968,-19 3982,-19 3975,-7 3968,-19 " stroke="rgb(60,120,255)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3968,14 3982,14 3975,2 3968,14 " stroke="rgb(60,120,255)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="5516,-818 5530,-818 5523,-806 5516,-818 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="5516,-781 5530,-781 5523,-793 5516,-781 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="5343,-330 5353,-330 5348,-313 5343,-330 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="5524,-327 5534,-327 5529,-310 5524,-327 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="5701,-406 5715,-406 5708,-394 5701,-406 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="5701,-363 5715,-363 5708,-375 5701,-363 " stroke="rgb(0,255,0)"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-89068">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3923.857143 -604.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19259" ObjectName="SW-CX_WLHG.CX_WLHG_001BK"/>
     <cge:Meas_Ref ObjectId="89068"/>
    <cge:TPSR_Ref TObjectID="19259"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-89062">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3923.857143 -967.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19255" ObjectName="SW-CX_WLHG.CX_WLHG_341BK"/>
     <cge:Meas_Ref ObjectId="89062"/>
    <cge:TPSR_Ref TObjectID="19255"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4280.878419 -967.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19319" ObjectName="SW-CX_WLHG.CX_WLHG_Zyb1BK"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="19319"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-89065">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3923.857143 -841.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19256" ObjectName="SW-CX_WLHG.CX_WLHG_301BK"/>
     <cge:Meas_Ref ObjectId="89065"/>
    <cge:TPSR_Ref TObjectID="19256"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-89087">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4230.000000 -486.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19274" ObjectName="SW-CX_WLHG.CX_WLHG_053BK"/>
     <cge:Meas_Ref ObjectId="89087"/>
    <cge:TPSR_Ref TObjectID="19274"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-89091">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4401.000000 -486.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19278" ObjectName="SW-CX_WLHG.CX_WLHG_054BK"/>
     <cge:Meas_Ref ObjectId="89091"/>
    <cge:TPSR_Ref TObjectID="19278"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-89095">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4582.000000 -486.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19282" ObjectName="SW-CX_WLHG.CX_WLHG_055BK"/>
     <cge:Meas_Ref ObjectId="89095"/>
    <cge:TPSR_Ref TObjectID="19282"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-89099">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4751.000000 -486.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19286" ObjectName="SW-CX_WLHG.CX_WLHG_056BK"/>
     <cge:Meas_Ref ObjectId="89099"/>
    <cge:TPSR_Ref TObjectID="19286"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-89103">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4929.000000 -486.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19290" ObjectName="SW-CX_WLHG.CX_WLHG_057BK"/>
     <cge:Meas_Ref ObjectId="89103"/>
    <cge:TPSR_Ref TObjectID="19290"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-89083">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3882.000000 -486.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19270" ObjectName="SW-CX_WLHG.CX_WLHG_052BK"/>
     <cge:Meas_Ref ObjectId="89083"/>
    <cge:TPSR_Ref TObjectID="19270"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-89079">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3638.000000 -486.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19266" ObjectName="SW-CX_WLHG.CX_WLHG_051BK"/>
     <cge:Meas_Ref ObjectId="89079"/>
    <cge:TPSR_Ref TObjectID="19266"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3638.000000 -294.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3490.000000 -178.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3592.000000 -178.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3835.000000 -178.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-114479">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3966.000000 -178.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21746" ObjectName="SW-CX_WLHG.CX_WLHG_034BK"/>
     <cge:Meas_Ref ObjectId="114479"/>
    <cge:TPSR_Ref TObjectID="21746"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-243958">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5513.857143 -610.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41002" ObjectName="SW-CX_WLHG.CX_WLHG_002BK"/>
     <cge:Meas_Ref ObjectId="243958"/>
    <cge:TPSR_Ref TObjectID="41002"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-243961">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5513.857143 -847.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40999" ObjectName="SW-CX_WLHG.CX_WLHG_302BK"/>
     <cge:Meas_Ref ObjectId="243961"/>
    <cge:TPSR_Ref TObjectID="40999"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-243974">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5114.000000 -484.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41007" ObjectName="SW-CX_WLHG.CX_WLHG_012BK"/>
     <cge:Meas_Ref ObjectId="243974"/>
    <cge:TPSR_Ref TObjectID="41007"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-246713">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5339.000000 -485.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41401" ObjectName="SW-CX_WLHG.CX_WLHG_061BK"/>
     <cge:Meas_Ref ObjectId="246713"/>
    <cge:TPSR_Ref TObjectID="41401"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-246700">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5521.000000 -484.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41404" ObjectName="SW-CX_WLHG.CX_WLHG_062BK"/>
     <cge:Meas_Ref ObjectId="246700"/>
    <cge:TPSR_Ref TObjectID="41404"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-246695">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5699.000000 -486.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41013" ObjectName="SW-CX_WLHG.CX_WLHG_063BK"/>
     <cge:Meas_Ref ObjectId="246695"/>
    <cge:TPSR_Ref TObjectID="41013"/></metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_21b1270">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4092.492401 -1087.000000)" xlink:href="#voltageTransformer:shape79"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21b3830">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4134.000000 -688.000000)" xlink:href="#voltageTransformer:shape79"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21d3b80">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5753.000000 -690.000000)" xlink:href="#voltageTransformer:shape79"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_WLHG.CX_WLHG_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3504,-568 5140,-568 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="19253" ObjectName="BS-CX_WLHG.CX_WLHG_9IM"/>
    <cge:TPSR_Ref TObjectID="19253"/></metadata>
   <polyline fill="none" opacity="0" points="3504,-568 5140,-568 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_WLHG.CX_WLHG_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3793,-919 5630,-919 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="19252" ObjectName="BS-CX_WLHG.CX_WLHG_3IM"/>
    <cge:TPSR_Ref TObjectID="19252"/></metadata>
   <polyline fill="none" opacity="0" points="3793,-919 5630,-919 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3451,-261 4041,-261 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="3451,-261 4041,-261 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_WLHG.CX_WLHG_9ⅡM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5221,-567 5856,-567 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="41005" ObjectName="BS-CX_WLHG.CX_WLHG_9ⅡM"/>
    <cge:TPSR_Ref TObjectID="41005"/></metadata>
   <polyline fill="none" opacity="0" points="5221,-567 5856,-567 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Reactance_Layer">
   <g DF8003:Layer="PUBLIC" id="RB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4925.000000 -222.000000)" xlink:href="#reactance:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="RB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="RB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5695.000000 -222.000000)" xlink:href="#reactance:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="RB-0"/>
    </metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2315c30" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4247.000000 -1101.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22f2c30" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4267.000000 -384.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2295cf0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4438.000000 -384.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2275830" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4355.000000 -245.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22ef3b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4619.000000 -384.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2280fa0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4536.000000 -245.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22fc310" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4788.000000 -384.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_222d010" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4705.000000 -245.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22168d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4966.000000 -384.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2232350" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3919.000000 -384.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_224e4b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3675.000000 -384.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2238f40" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3789.000000 16.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28dc410" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5376.000000 -383.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28eae20" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5558.000000 -382.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28f80b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5736.000000 -384.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-10KV" id="g_21a1a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3933,-669 3933,-685 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="19260@0" ObjectIDZND0="19294@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-89069_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3933,-669 3933,-685 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_224b8f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3933,-601 3933,-612 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="19261@0" ObjectIDZND0="19259@0" Pin0InfoVect0LinkObjId="SW-89068_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-89069_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3933,-601 3933,-612 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22e7520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3933,-639 3933,-652 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="19259@1" ObjectIDZND0="19260@1" Pin0InfoVect0LinkObjId="SW-89069_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-89068_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3933,-639 3933,-652 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_224a620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3933,-583 3933,-568 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="19261@1" ObjectIDZND0="19253@0" Pin0InfoVect0LinkObjId="g_222a420_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-89069_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3933,-583 3933,-568 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21fc220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3933,-975 3933,-965 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="19255@0" ObjectIDZND0="19318@1" Pin0InfoVect0LinkObjId="SW-89063_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-89062_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3933,-975 3933,-965 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21fc410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3933,-948 3933,-919 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="19318@0" ObjectIDZND0="19252@0" Pin0InfoVect0LinkObjId="g_2314930_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-89063_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3933,-948 3933,-919 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21fc600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3933,-1013 3933,-1002 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="19317@1" ObjectIDZND0="19255@1" Pin0InfoVect0LinkObjId="SW-89062_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-89063_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3933,-1013 3933,-1002 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2328a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4111,-919 4111,-947 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="19252@0" ObjectIDZND0="19262@0" Pin0InfoVect0LinkObjId="SW-89074_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21fc410_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4111,-919 4111,-947 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2328c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4111,-1092 4111,-1016 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" ObjectIDND0="g_21b1270@0" ObjectIDZND0="19262@1" Pin0InfoVect0LinkObjId="SW-89074_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21b1270_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4111,-1092 4111,-1016 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2314740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4290,-975 4290,-965 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="19319@0" ObjectIDZND0="19321@1" Pin0InfoVect0LinkObjId="SW-89075_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4290,-975 4290,-965 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2314930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4290,-948 4290,-919 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="19321@0" ObjectIDZND0="19252@0" Pin0InfoVect0LinkObjId="g_21fc410_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-89075_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4290,-948 4290,-919 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2314b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4290,-1013 4290,-1002 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="19320@1" ObjectIDZND0="19319@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-89075_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4290,-1013 4290,-1002 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2315a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4290,-1059 4290,-1030 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="19320@0" Pin0InfoVect0LinkObjId="SW-89075_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4290,-1059 4290,-1030 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_23163e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4290,-1090 4253,-1090 4253,-1106 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="earth" ObjectIDND0="0@x" ObjectIDZND0="g_2315c30@0" Pin0InfoVect0LinkObjId="g_2315c30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4290,-1090 4253,-1090 4253,-1106 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_22b84d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4290,-1147 4290,-1138 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" EndDevType1="transformer2" ObjectIDZND0="g_22b7b00@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_22b7b00_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4290,-1147 4290,-1138 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_22b8df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4334,-1138 4290,-1138 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_22b7b00@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22b7b00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4334,-1138 4290,-1138 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_22b8fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4290,-1138 4290,-1101 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_22b7b00@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22b7b00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4290,-1138 4290,-1101 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22d9810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3933,-904 3933,-919 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="19257@0" ObjectIDZND0="19252@0" Pin0InfoVect0LinkObjId="g_21fc410_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-89066_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3933,-904 3933,-919 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22d9a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3933,-765 3933,-822 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="19294@0" ObjectIDZND0="19258@0" Pin0InfoVect0LinkObjId="SW-89066_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21a1a20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3933,-765 3933,-822 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22d9c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3933,-839 3933,-849 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="19258@1" ObjectIDZND0="19256@0" Pin0InfoVect0LinkObjId="SW-89065_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-89066_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3933,-839 3933,-849 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22d9e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3933,-876 3933,-887 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="19256@1" ObjectIDZND0="19257@1" Pin0InfoVect0LinkObjId="SW-89066_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-89065_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3933,-876 3933,-887 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2219ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4153,-693 4153,-627 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" ObjectIDND0="g_21b3830@0" ObjectIDZND0="19264@0" Pin0InfoVect0LinkObjId="SW-89077_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21b3830_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4153,-693 4153,-627 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_222a420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4239,-551 4239,-568 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="19275@0" ObjectIDZND0="19253@0" Pin0InfoVect0LinkObjId="g_224a620_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-89088_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4239,-551 4239,-568 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23070e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4239,-483 4239,-494 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="19276@0" ObjectIDZND0="19274@0" Pin0InfoVect0LinkObjId="SW-89087_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-89088_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4239,-483 4239,-494 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2307300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4239,-521 4239,-534 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="19274@1" ObjectIDZND0="19275@1" Pin0InfoVect0LinkObjId="SW-89088_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-89087_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4239,-521 4239,-534 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2307690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4205,-447 4205,-456 4239,-456 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2277000@0" ObjectIDZND0="19277@x" ObjectIDZND1="19276@x" Pin0InfoVect0LinkObjId="SW-89089_0" Pin0InfoVect1LinkObjId="SW-89088_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2277000_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4205,-447 4205,-456 4239,-456 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2276bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4239,-465 4239,-456 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="19276@1" ObjectIDZND0="g_2277000@0" ObjectIDZND1="19277@x" Pin0InfoVect0LinkObjId="g_2277000_0" Pin0InfoVect1LinkObjId="SW-89089_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-89088_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4239,-465 4239,-456 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2276de0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4239,-456 4239,-380 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" ObjectIDND0="g_2277000@0" ObjectIDND1="19277@x" ObjectIDND2="19276@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2277000_0" Pin1InfoVect1LinkObjId="SW-89089_0" Pin1InfoVect2LinkObjId="SW-89088_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4239,-456 4239,-380 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22f2a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4273,-447 4273,-456 4239,-456 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="19277@1" ObjectIDZND0="g_2277000@0" ObjectIDZND1="19276@x" Pin0InfoVect0LinkObjId="g_2277000_0" Pin0InfoVect1LinkObjId="SW-89088_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-89089_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4273,-447 4273,-456 4239,-456 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22f3520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4273,-402 4273,-411 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_22f2c30@0" ObjectIDZND0="19277@0" Pin0InfoVect0LinkObjId="SW-89089_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22f2c30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4273,-402 4273,-411 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22bc750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4410,-551 4410,-568 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="19279@0" ObjectIDZND0="19253@0" Pin0InfoVect0LinkObjId="g_224a620_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-89092_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4410,-551 4410,-568 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22cd500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4410,-483 4410,-494 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="19280@0" ObjectIDZND0="19278@0" Pin0InfoVect0LinkObjId="SW-89091_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-89092_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4410,-483 4410,-494 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22cd760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4410,-521 4410,-534 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="19278@1" ObjectIDZND0="19279@1" Pin0InfoVect0LinkObjId="SW-89092_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-89091_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4410,-521 4410,-534 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22cdaf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4376,-447 4376,-456 4410,-456 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="transformer2" ObjectIDND0="g_22ce1f0@0" ObjectIDZND0="19280@x" ObjectIDZND1="19281@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-89092_0" Pin0InfoVect1LinkObjId="SW-89093_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22ce1f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4376,-447 4376,-456 4410,-456 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22cdd30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4410,-465 4410,-456 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="transformer2" ObjectIDND0="19280@1" ObjectIDZND0="g_22ce1f0@0" ObjectIDZND1="19281@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_22ce1f0_0" Pin0InfoVect1LinkObjId="SW-89093_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-89092_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4410,-465 4410,-456 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22cdf90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4410,-456 4410,-334 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="transformer2" ObjectIDND0="19280@x" ObjectIDND1="g_22ce1f0@0" ObjectIDND2="19281@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-89092_0" Pin1InfoVect1LinkObjId="g_22ce1f0_0" Pin1InfoVect2LinkObjId="SW-89093_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4410,-456 4410,-334 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2295a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4444,-447 4444,-456 4410,-456 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="transformer2" ObjectIDND0="19281@1" ObjectIDZND0="19280@x" ObjectIDZND1="g_22ce1f0@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-89092_0" Pin0InfoVect1LinkObjId="g_22ce1f0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-89093_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4444,-447 4444,-456 4410,-456 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22737a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4444,-402 4444,-411 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2295cf0@0" ObjectIDZND0="19281@0" Pin0InfoVect0LinkObjId="SW-89093_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2295cf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4444,-402 4444,-411 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2275610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4410,-273 4361,-273 4361,-263 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="earth" ObjectIDND0="0@x" ObjectIDZND0="g_2275830@0" Pin0InfoVect0LinkObjId="g_2275830_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4410,-273 4361,-273 4361,-263 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22c8680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4591,-551 4591,-568 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="19283@0" ObjectIDZND0="19253@0" Pin0InfoVect0LinkObjId="g_224a620_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-89096_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4591,-551 4591,-568 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22cfb50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4591,-483 4591,-494 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="19284@0" ObjectIDZND0="19282@0" Pin0InfoVect0LinkObjId="SW-89095_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-89096_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4591,-483 4591,-494 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22cfdb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4591,-521 4591,-534 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="19282@1" ObjectIDZND0="19283@1" Pin0InfoVect0LinkObjId="SW-89096_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-89095_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4591,-521 4591,-534 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22d0140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4557,-447 4557,-456 4591,-456 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="transformer2" ObjectIDND0="g_22d0840@0" ObjectIDZND0="19284@x" ObjectIDZND1="19285@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-89096_0" Pin0InfoVect1LinkObjId="SW-89097_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22d0840_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4557,-447 4557,-456 4591,-456 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22d0380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4591,-465 4591,-456 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="transformer2" ObjectIDND0="19284@1" ObjectIDZND0="g_22d0840@0" ObjectIDZND1="19285@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_22d0840_0" Pin0InfoVect1LinkObjId="SW-89097_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-89096_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4591,-465 4591,-456 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22d05e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4591,-456 4591,-334 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="transformer2" ObjectIDND0="19284@x" ObjectIDND1="g_22d0840@0" ObjectIDND2="19285@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-89096_0" Pin1InfoVect1LinkObjId="g_22d0840_0" Pin1InfoVect2LinkObjId="SW-89097_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4591,-456 4591,-334 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22ef150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4625,-447 4625,-456 4591,-456 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="transformer2" ObjectIDND0="19285@1" ObjectIDZND0="19284@x" ObjectIDZND1="g_22d0840@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-89096_0" Pin0InfoVect1LinkObjId="g_22d0840_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-89097_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4625,-447 4625,-456 4591,-456 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22efd80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4625,-402 4625,-411 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_22ef3b0@0" ObjectIDZND0="19285@0" Pin0InfoVect0LinkObjId="SW-89097_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22ef3b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4625,-402 4625,-411 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_22f11d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4591,-273 4542,-273 4542,-263 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="earth" ObjectIDND0="0@x" ObjectIDZND0="g_2280fa0@0" Pin0InfoVect0LinkObjId="g_2280fa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4591,-273 4542,-273 4542,-263 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2283dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4760,-551 4760,-568 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="19287@0" ObjectIDZND0="19253@0" Pin0InfoVect0LinkObjId="g_224a620_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-89100_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4760,-551 4760,-568 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22aaf00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4760,-483 4760,-494 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="19288@0" ObjectIDZND0="19286@0" Pin0InfoVect0LinkObjId="SW-89099_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-89100_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4760,-483 4760,-494 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22ab160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4760,-521 4760,-534 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="19286@1" ObjectIDZND0="19287@1" Pin0InfoVect0LinkObjId="SW-89100_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-89099_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4760,-521 4760,-534 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22ab4f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4726,-447 4726,-456 4760,-456 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="transformer2" ObjectIDND0="g_22abbf0@0" ObjectIDZND0="19288@x" ObjectIDZND1="19289@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-89100_0" Pin0InfoVect1LinkObjId="SW-89101_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22abbf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4726,-447 4726,-456 4760,-456 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22ab730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4760,-465 4760,-456 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="transformer2" ObjectIDND0="19288@1" ObjectIDZND0="g_22abbf0@0" ObjectIDZND1="19289@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_22abbf0_0" Pin0InfoVect1LinkObjId="SW-89101_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-89100_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4760,-465 4760,-456 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22ab990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4760,-456 4760,-334 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="transformer2" ObjectIDND0="19288@x" ObjectIDND1="g_22abbf0@0" ObjectIDND2="19289@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-89100_0" Pin1InfoVect1LinkObjId="g_22abbf0_0" Pin1InfoVect2LinkObjId="SW-89101_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4760,-456 4760,-334 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22fc0b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4794,-447 4794,-456 4760,-456 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="transformer2" ObjectIDND0="19289@1" ObjectIDZND0="19288@x" ObjectIDZND1="g_22abbf0@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-89100_0" Pin0InfoVect1LinkObjId="g_22abbf0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-89101_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4794,-447 4794,-456 4760,-456 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22fcce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4794,-402 4794,-411 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_22fc310@0" ObjectIDZND0="19289@0" Pin0InfoVect0LinkObjId="SW-89101_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22fc310_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4794,-402 4794,-411 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_222cdd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4760,-273 4711,-273 4711,-263 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="earth" ObjectIDND0="0@x" ObjectIDZND0="g_222d010@0" Pin0InfoVect0LinkObjId="g_222d010_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4760,-273 4711,-273 4711,-263 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_222ffd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4938,-551 4938,-568 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="19291@0" ObjectIDZND0="19253@0" Pin0InfoVect0LinkObjId="g_224a620_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-89104_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4938,-551 4938,-568 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2289960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4938,-483 4938,-494 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="19292@0" ObjectIDZND0="19290@0" Pin0InfoVect0LinkObjId="SW-89103_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-89104_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4938,-483 4938,-494 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2289bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4938,-521 4938,-534 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="19290@1" ObjectIDZND0="19291@1" Pin0InfoVect0LinkObjId="SW-89104_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-89103_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4938,-521 4938,-534 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2289f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4904,-447 4904,-456 4938,-456 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_228a3f0@0" ObjectIDZND0="19292@x" ObjectIDZND1="19293@x" ObjectIDZND2="19554@x" Pin0InfoVect0LinkObjId="SW-89104_0" Pin0InfoVect1LinkObjId="SW-89105_0" Pin0InfoVect2LinkObjId="SW-89076_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_228a3f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4904,-447 4904,-456 4938,-456 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_228a190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4938,-465 4938,-456 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="19292@1" ObjectIDZND0="g_228a3f0@0" ObjectIDZND1="19293@x" ObjectIDZND2="19554@x" Pin0InfoVect0LinkObjId="g_228a3f0_0" Pin0InfoVect1LinkObjId="SW-89105_0" Pin0InfoVect2LinkObjId="SW-89076_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-89104_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4938,-465 4938,-456 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2216670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4972,-447 4972,-456 4938,-456 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="19293@1" ObjectIDZND0="19292@x" ObjectIDZND1="g_228a3f0@0" ObjectIDZND2="19554@x" Pin0InfoVect0LinkObjId="SW-89104_0" Pin0InfoVect1LinkObjId="g_228a3f0_0" Pin0InfoVect2LinkObjId="SW-89076_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-89105_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4972,-447 4972,-456 4938,-456 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22172b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4972,-402 4972,-411 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_22168d0@0" ObjectIDZND0="19293@0" Pin0InfoVect0LinkObjId="SW-89105_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22168d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4972,-402 4972,-411 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21adf50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4938,-456 4938,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="19292@x" ObjectIDND1="g_228a3f0@0" ObjectIDND2="19293@x" ObjectIDZND0="19554@1" Pin0InfoVect0LinkObjId="SW-89076_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-89104_0" Pin1InfoVect1LinkObjId="g_228a3f0_0" Pin1InfoVect2LinkObjId="SW-89105_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4938,-456 4938,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_21ae1b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4938,-227 4938,-218 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="reactance" EndDevType0="capacitor" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4938,-227 4938,-218 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21b0a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4938,-279 4903,-279 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="reactance" EndDevType0="lightningRod" ObjectIDND0="19554@x" ObjectIDND1="0@x" ObjectIDZND0="g_2260ed0@0" Pin0InfoVect0LinkObjId="g_2260ed0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-89076_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4938,-279 4903,-279 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2260a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4938,-289 4938,-279 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="reactance" EndDevType1="lightningRod" ObjectIDND0="19554@0" ObjectIDZND0="0@x" ObjectIDZND1="g_2260ed0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_2260ed0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-89076_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4938,-289 4938,-279 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2260c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4938,-279 4938,-269 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="reactance" ObjectIDND0="19554@x" ObjectIDND1="g_2260ed0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-89076_0" Pin1InfoVect1LinkObjId="g_2260ed0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4938,-279 4938,-269 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2263700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3891,-551 3891,-568 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="19271@0" ObjectIDZND0="19253@0" Pin0InfoVect0LinkObjId="g_224a620_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-89084_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3891,-551 3891,-568 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22e3290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3891,-483 3891,-494 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="19272@0" ObjectIDZND0="19270@0" Pin0InfoVect0LinkObjId="SW-89083_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-89084_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3891,-483 3891,-494 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22e34f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3891,-521 3891,-534 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="19270@1" ObjectIDZND0="19271@1" Pin0InfoVect0LinkObjId="SW-89084_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-89083_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3891,-521 3891,-534 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22e38e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3857,-447 3857,-456 3891,-456 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_22e4000@0" ObjectIDZND0="19272@x" ObjectIDZND1="19273@x" Pin0InfoVect0LinkObjId="SW-89084_0" Pin0InfoVect1LinkObjId="SW-89085_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22e4000_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3857,-447 3857,-456 3891,-456 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22e3b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3891,-465 3891,-456 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="19272@1" ObjectIDZND0="g_22e4000@0" ObjectIDZND1="19273@x" Pin0InfoVect0LinkObjId="g_22e4000_0" Pin0InfoVect1LinkObjId="SW-89085_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-89084_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3891,-465 3891,-456 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22e3da0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3891,-456 3891,-380 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" ObjectIDND0="19272@x" ObjectIDND1="g_22e4000@0" ObjectIDND2="19273@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-89084_0" Pin1InfoVect1LinkObjId="g_22e4000_0" Pin1InfoVect2LinkObjId="SW-89085_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3891,-456 3891,-380 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22320f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3925,-447 3925,-456 3891,-456 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="19273@1" ObjectIDZND0="19272@x" ObjectIDZND1="g_22e4000@0" Pin0InfoVect0LinkObjId="SW-89084_0" Pin0InfoVect1LinkObjId="g_22e4000_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-89085_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3925,-447 3925,-456 3891,-456 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2232d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3925,-402 3925,-411 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2232350@0" ObjectIDZND0="19273@0" Pin0InfoVect0LinkObjId="SW-89085_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2232350_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3925,-402 3925,-411 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2235bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3647,-551 3647,-568 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="19267@0" ObjectIDZND0="19253@0" Pin0InfoVect0LinkObjId="g_224a620_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-89080_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3647,-551 3647,-568 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2200b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3647,-483 3647,-494 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="19268@0" ObjectIDZND0="19266@0" Pin0InfoVect0LinkObjId="SW-89079_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-89080_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3647,-483 3647,-494 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2200db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3647,-521 3647,-534 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="19266@1" ObjectIDZND0="19267@1" Pin0InfoVect0LinkObjId="SW-89080_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-89079_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3647,-521 3647,-534 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22011a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3613,-447 3613,-456 3647,-456 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2201660@0" ObjectIDZND0="19268@x" ObjectIDZND1="19269@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-89080_0" Pin0InfoVect1LinkObjId="SW-89081_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2201660_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3613,-447 3613,-456 3647,-456 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2201400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3647,-465 3647,-456 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="19268@1" ObjectIDZND0="g_2201660@0" ObjectIDZND1="19269@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_2201660_0" Pin0InfoVect1LinkObjId="SW-89081_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-89080_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3647,-465 3647,-456 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_224e230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3681,-447 3681,-456 3647,-456 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="19269@1" ObjectIDZND0="19268@x" ObjectIDZND1="g_2201660@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-89080_0" Pin0InfoVect1LinkObjId="g_2201660_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-89081_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3681,-447 3681,-456 3647,-456 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_224eed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3681,-402 3681,-411 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_224e4b0@0" ObjectIDZND0="19269@0" Pin0InfoVect0LinkObjId="SW-89081_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_224e4b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3681,-402 3681,-411 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2268e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3647,-302 3647,-291 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3647,-302 3647,-291 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_22690c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3647,-273 3647,-261 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3647,-273 3647,-261 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2269320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3647,-456 3647,-359 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="19268@x" ObjectIDND1="g_2201660@0" ObjectIDND2="19269@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-89080_0" Pin1InfoVect1LinkObjId="g_2201660_0" Pin1InfoVect2LinkObjId="SW-89081_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3647,-456 3647,-359 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2269580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3647,-342 3647,-329 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3647,-342 3647,-329 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_21f3610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3499,-243 3499,-261 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3499,-243 3499,-261 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_21f96f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3499,-175 3499,-186 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3499,-175 3499,-186 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_21f9950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3499,-213 3499,-226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3499,-213 3499,-226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_21f9bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3499,-157 3499,-73 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="0@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3499,-157 3499,-73 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2255c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3601,-243 3601,-261 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3601,-243 3601,-261 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_225bc30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3601,-175 3601,-186 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3601,-175 3601,-186 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_225be90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3601,-213 3601,-226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3601,-213 3601,-226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_225c0f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3601,-157 3601,-73 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="0@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3601,-157 3601,-73 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_21a7340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3844,-243 3844,-261 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3844,-243 3844,-261 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2236ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3844,-175 3844,-186 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3844,-175 3844,-186 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2236d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3844,-213 3844,-226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3844,-213 3844,-226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2237270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3844,-157 3844,-73 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3844,-157 3844,-73 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2238ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3844,-12 3795,-12 3795,-2 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="earth" ObjectIDND0="0@x" ObjectIDZND0="g_2238f40@0" Pin0InfoVect0LinkObjId="g_2238f40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3844,-12 3795,-12 3795,-2 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_223b9e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3975,-243 3975,-261 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="21747@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-114480_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3975,-243 3975,-261 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2241b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3975,-175 3975,-186 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="21754@0" ObjectIDZND0="21746@0" Pin0InfoVect0LinkObjId="SW-114479_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-114480_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3975,-175 3975,-186 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2241da0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3975,-213 3975,-226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="21746@1" ObjectIDZND0="21747@1" Pin0InfoVect0LinkObjId="SW-114480_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-114479_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3975,-213 3975,-226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2242000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3975,-157 3975,-73 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="generator" ObjectIDND0="21754@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-114480_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3975,-157 3975,-73 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24b2250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3960,-1066 3960,-1083 3933,-1083 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2277bb0@0" ObjectIDZND0="19317@x" Pin0InfoVect0LinkObjId="SW-89063_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2277bb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3960,-1066 3960,-1083 3933,-1083 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24b2c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3933,-1083 3933,-1030 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2277bb0@0" ObjectIDZND0="19317@0" Pin0InfoVect0LinkObjId="SW-89063_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2277bb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3933,-1083 3933,-1030 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24b2e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3933,-1168 3933,-1083 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" EndDevType1="switch" ObjectIDZND0="g_2277bb0@0" ObjectIDZND1="19317@x" Pin0InfoVect0LinkObjId="g_2277bb0_0" Pin0InfoVect1LinkObjId="SW-89063_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3933,-1168 3933,-1083 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21ddad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4153,-590 4153,-568 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="g_2219f00@0" ObjectIDND1="19264@x" ObjectIDZND0="19253@0" Pin0InfoVect0LinkObjId="g_224a620_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2219f00_0" Pin1InfoVect1LinkObjId="SW-89077_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4153,-590 4153,-568 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21de440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4196,-597 4196,-590 4153,-590 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="g_2219f00@0" ObjectIDZND0="19264@x" ObjectIDZND1="19253@0" Pin0InfoVect0LinkObjId="SW-89077_0" Pin0InfoVect1LinkObjId="g_224a620_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2219f00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4196,-597 4196,-590 4153,-590 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21de630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4153,-590 4153,-609 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="busSection" EndDevType0="switch" ObjectIDND0="g_2219f00@0" ObjectIDND1="19253@0" ObjectIDZND0="19264@1" Pin0InfoVect0LinkObjId="SW-89077_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2219f00_0" Pin1InfoVect1LinkObjId="g_224a620_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4153,-590 4153,-609 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21bd2b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5523,-675 5523,-691 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="41003@0" ObjectIDZND0="40998@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-243959_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5523,-675 5523,-691 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21c3410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5523,-607 5523,-618 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="41004@0" ObjectIDZND0="41002@0" Pin0InfoVect0LinkObjId="SW-243958_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-243959_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5523,-607 5523,-618 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21c3670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5523,-645 5523,-658 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="41002@1" ObjectIDZND0="41003@1" Pin0InfoVect0LinkObjId="SW-243959_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-243958_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5523,-645 5523,-658 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21cd3b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5523,-776 5523,-828 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="40998@0" ObjectIDZND0="41001@0" Pin0InfoVect0LinkObjId="SW-243962_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21bd2b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5523,-776 5523,-828 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21cd610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5523,-845 5523,-855 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="41001@1" ObjectIDZND0="40999@0" Pin0InfoVect0LinkObjId="SW-243961_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-243962_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5523,-845 5523,-855 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21cd870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5523,-882 5523,-893 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="40999@1" ObjectIDZND0="41000@1" Pin0InfoVect0LinkObjId="SW-243962_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-243961_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5523,-882 5523,-893 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21ce8a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5523,-910 5523,-923 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="41000@0" ObjectIDZND0="19252@0" Pin0InfoVect0LinkObjId="g_21fc410_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-243962_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5523,-910 5523,-923 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21cea90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5523,-567 5523,-585 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="41005@0" ObjectIDZND0="41004@1" Pin0InfoVect0LinkObjId="SW-243959_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21d3590_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5523,-567 5523,-585 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_21d1c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5772,-695 5772,-629 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" ObjectIDND0="g_21d3b80@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21d3b80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5772,-695 5772,-629 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21d3590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5772,-592 5772,-567 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="g_21d1ef0@0" ObjectIDND1="0@x" ObjectIDZND0="41005@0" Pin0InfoVect0LinkObjId="g_21d3780_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_21d1ef0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5772,-592 5772,-567 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21d3780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5815,-599 5815,-592 5772,-592 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="busSection" EndDevType1="switch" ObjectIDND0="g_21d1ef0@0" ObjectIDZND0="41005@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_21d3590_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21d1ef0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5815,-599 5815,-592 5772,-592 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21d3970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5772,-592 5772,-611 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="41005@0" ObjectIDND1="g_21d1ef0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_21d3590_0" Pin1InfoVect1LinkObjId="g_21d1ef0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5772,-592 5772,-611 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28c8ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5123,-549 5123,-568 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="41008@0" ObjectIDZND0="19253@0" Pin0InfoVect0LinkObjId="g_224a620_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-243975_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5123,-549 5123,-568 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28cf040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5123,-481 5123,-492 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="41009@0" ObjectIDZND0="41007@0" Pin0InfoVect0LinkObjId="SW-243974_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-243975_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5123,-481 5123,-492 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28cf2a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5123,-519 5123,-532 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="41007@1" ObjectIDZND0="41008@1" Pin0InfoVect0LinkObjId="SW-243975_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-243974_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5123,-519 5123,-532 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28d0020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5123,-446 5123,-463 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="41009@1" Pin0InfoVect0LinkObjId="SW-243975_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5123,-446 5123,-463 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_28d0210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5257,-445 5257,-481 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5257,-445 5257,-481 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28d0400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5257,-550 5257,-567 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="41005@0" Pin0InfoVect0LinkObjId="g_21d3590_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5257,-550 5257,-567 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28d2880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5348,-550 5348,-567 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="41402@0" ObjectIDZND0="41005@0" Pin0InfoVect0LinkObjId="g_21d3590_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-246696_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5348,-550 5348,-567 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28d89e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5348,-482 5348,-493 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="41403@0" ObjectIDZND0="41401@0" Pin0InfoVect0LinkObjId="SW-246713_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-246696_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5348,-482 5348,-493 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28d8c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5348,-520 5348,-533 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="41401@1" ObjectIDZND0="41402@1" Pin0InfoVect0LinkObjId="SW-246696_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-246713_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5348,-520 5348,-533 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28dce60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5382,-401 5382,-410 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_28dc410@0" ObjectIDZND0="41407@0" Pin0InfoVect0LinkObjId="SW-246699_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28dc410_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5382,-401 5382,-410 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28e1290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5530,-549 5530,-567 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="41405@0" ObjectIDZND0="41005@0" Pin0InfoVect0LinkObjId="g_21d3590_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-246701_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5530,-549 5530,-567 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28e73f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5530,-481 5530,-492 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="41406@0" ObjectIDZND0="41404@0" Pin0InfoVect0LinkObjId="SW-246700_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-246701_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5530,-481 5530,-492 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28e7650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5530,-519 5530,-532 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="41404@1" ObjectIDZND0="41405@1" Pin0InfoVect0LinkObjId="SW-246701_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-246700_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5530,-519 5530,-532 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28eb870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5564,-400 5564,-409 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_28eae20@0" ObjectIDZND0="41408@0" Pin0InfoVect0LinkObjId="SW-246704_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28eae20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5564,-400 5564,-409 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28ee390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5708,-551 5708,-567 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="41015@0" ObjectIDZND0="41005@0" Pin0InfoVect0LinkObjId="g_21d3590_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-243988_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5708,-551 5708,-567 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28f44f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5708,-483 5708,-494 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="41016@0" ObjectIDZND0="41013@0" Pin0InfoVect0LinkObjId="SW-246695_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-243988_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5708,-483 5708,-494 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28f4750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5708,-521 5708,-534 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="41013@1" ObjectIDZND0="41015@1" Pin0InfoVect0LinkObjId="SW-243988_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-246695_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5708,-521 5708,-534 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28f8b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5742,-402 5742,-411 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_28f80b0@0" ObjectIDZND0="41014@0" Pin0InfoVect0LinkObjId="SW-243989_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28f80b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5742,-402 5742,-411 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28fb690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5708,-227 5708,-218 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="reactance" EndDevType0="capacitor" ObjectIDND0="0@1" ObjectIDZND0="41012@0" Pin0InfoVect0LinkObjId="CB-CX_WLHG.CX_WLHG_Cb2_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5708,-227 5708,-218 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_28fe480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5708,-279 5673,-279 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="reactance" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="g_28feba0@0" Pin0InfoVect0LinkObjId="g_28feba0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5708,-279 5673,-279 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_28fe6e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5708,-289 5708,-279 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="reactance" ObjectIDND0="0@0" ObjectIDZND0="g_28feba0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_28feba0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5708,-289 5708,-279 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_28fe940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5708,-279 5708,-269 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="reactance" ObjectIDND0="g_28feba0@0" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_28feba0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5708,-279 5708,-269 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2902ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5674,-447 5674,-456 5708,-456 5708,-465 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_28f4b40@0" ObjectIDZND0="41016@1" Pin0InfoVect0LinkObjId="SW-243988_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28f4b40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5674,-447 5674,-456 5708,-456 5708,-465 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2903130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5742,-447 5742,-456 5708,-456 5708,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="41014@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-243989_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5742,-447 5742,-456 5708,-456 5708,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_290af00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5348,-455 5348,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" ObjectIDND0="g_28d8ea0@0" ObjectIDND1="41403@x" ObjectIDND2="41407@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_28d8ea0_0" Pin1InfoVect1LinkObjId="SW-246696_0" Pin1InfoVect2LinkObjId="SW-246699_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5348,-455 5348,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_290ba60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5314,-446 5314,-455 5348,-455 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_28d8ea0@0" ObjectIDZND0="41403@x" ObjectIDZND1="41407@x" Pin0InfoVect0LinkObjId="SW-246696_0" Pin0InfoVect1LinkObjId="SW-246699_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28d8ea0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5314,-446 5314,-455 5348,-455 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_290bc50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5348,-455 5348,-464 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_28d8ea0@0" ObjectIDND1="41407@x" ObjectIDZND0="41403@1" Pin0InfoVect0LinkObjId="SW-246696_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_28d8ea0_0" Pin1InfoVect1LinkObjId="SW-246699_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5348,-455 5348,-464 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_290be60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5382,-446 5382,-455 5348,-455 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="41407@1" ObjectIDZND0="g_28d8ea0@0" ObjectIDZND1="41403@x" Pin0InfoVect0LinkObjId="g_28d8ea0_0" Pin0InfoVect1LinkObjId="SW-246696_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-246699_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5382,-446 5382,-455 5348,-455 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_290c0c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5530,-454 5530,-327 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" ObjectIDND0="g_28e78b0@0" ObjectIDND1="41406@x" ObjectIDND2="41408@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_28e78b0_0" Pin1InfoVect1LinkObjId="SW-246701_0" Pin1InfoVect2LinkObjId="SW-246704_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5530,-454 5530,-327 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_290cd20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5496,-445 5496,-454 5530,-454 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_28e78b0@0" ObjectIDZND0="41406@x" ObjectIDZND1="41408@x" Pin0InfoVect0LinkObjId="SW-246701_0" Pin0InfoVect1LinkObjId="SW-246704_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28e78b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5496,-445 5496,-454 5530,-454 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_290cf60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5530,-454 5530,-463 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_28e78b0@0" ObjectIDND1="41408@x" ObjectIDZND0="41406@1" Pin0InfoVect0LinkObjId="SW-246701_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_28e78b0_0" Pin1InfoVect1LinkObjId="SW-246704_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5530,-454 5530,-463 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_290d1c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5530,-454 5564,-454 5564,-445 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_28e78b0@0" ObjectIDND1="41406@x" ObjectIDZND0="41408@1" Pin0InfoVect0LinkObjId="SW-246704_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_28e78b0_0" Pin1InfoVect1LinkObjId="SW-246701_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5530,-454 5564,-454 5564,-445 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="3499" cy="-261" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="3601" cy="-261" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="3647" cy="-261" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="3844" cy="-261" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="3975" cy="-261" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19253" cx="3933" cy="-568" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19253" cx="4239" cy="-568" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19253" cx="4410" cy="-568" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19253" cx="4591" cy="-568" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19253" cx="4760" cy="-568" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19253" cx="4938" cy="-568" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19253" cx="3891" cy="-568" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19253" cx="3647" cy="-568" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19253" cx="4153" cy="-568" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19253" cx="5123" cy="-568" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="41005" cx="5523" cy="-567" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="41005" cx="5772" cy="-567" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="41005" cx="5257" cy="-567" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="41005" cx="5348" cy="-567" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="41005" cx="5530" cy="-567" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="41005" cx="5708" cy="-567" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19252" cx="3933" cy="-919" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19252" cx="4111" cy="-919" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19252" cx="4290" cy="-919" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19252" cx="3933" cy="-919" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19252" cx="5522" cy="-919" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-88994" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3422.000000 -1082.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19243" ObjectName="DYN-CX_WLHG"/>
     <cge:Meas_Ref ObjectId="88994"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_232f250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3786.000000 -739.000000) translate(0,15)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_20e3d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3140.000000 -576.000000) translate(0,17)">危险点说明:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_20e3d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3140.000000 -576.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_20e3d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3140.000000 -576.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_20e3d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3140.000000 -576.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_20e3d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3140.000000 -576.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_20e3d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3140.000000 -576.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_20e3d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3140.000000 -576.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_20e3d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3140.000000 -576.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_20e3d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3140.000000 -576.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_20e3d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3140.000000 -576.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_20e3d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3140.000000 -576.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_20e3d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3140.000000 -576.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_20e3d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3140.000000 -576.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_20e3d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3140.000000 -576.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_20e3d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3140.000000 -576.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_20e3d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3140.000000 -576.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_20e3d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3140.000000 -576.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_20e3d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3140.000000 -576.000000) translate(0,374)">联系方式:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_21a1d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3132.000000 -1055.000000) translate(0,17)">频率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_21a1d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3132.000000 -1055.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_21a1d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3132.000000 -1055.000000) translate(0,59)">全站有功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_21a1d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3132.000000 -1055.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_21a1d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3132.000000 -1055.000000) translate(0,101)">全站无功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_21a1d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3132.000000 -1055.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_21a1d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3132.000000 -1055.000000) translate(0,143)">并网联络点的电压和交换功率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_2270090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3276.000000 -1162.500000) translate(0,16)">威龙化工变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_219bb30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4391.000000 -937.000000) translate(0,12)">IM段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_224a810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3786.000000 -720.000000) translate(0,12)">SZ11-6300/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22536e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3492.000000 -66.000000) translate(0,12)">M</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_225c960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3594.000000 -66.000000) translate(0,12)">M</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24b1520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3912.000000 -1164.000000) translate(0,12)">威</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24b1520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3912.000000 -1164.000000) translate(0,27)">龙</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24b1520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3912.000000 -1164.000000) translate(0,42)">化</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24b1520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3912.000000 -1164.000000) translate(0,57)">工</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24b1520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3912.000000 -1164.000000) translate(0,72)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24b3060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4073.000000 -1152.000000) translate(0,12)">35kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24b3e50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4241.000000 -1176.000000) translate(0,12)">35kV1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24b46d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4319.000000 -1074.000000) translate(0,12)">SCB11-80/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24b5350" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4313.000000 -1060.000000) translate(0,12)">35±2.5%/0.4kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24b5b70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4117.000000 -754.000000) translate(0,12)">10kVⅠ段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24b5f50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3786.000000 -706.000000) translate(0,12)">35±2×2.5%/10.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24b6460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3822.000000 -360.000000) translate(0,12)">至空气鼓风机2(备用)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24b8160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3862.000000 -345.000000) translate(0,12)">1250kW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24b8660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4182.000000 -360.000000) translate(0,12)">至升温风机(备用)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24b9060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4212.000000 -345.000000) translate(0,12)">2600kW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24b92d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3937.000000 -280.000000) translate(0,12)">发电厂10kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24b9e40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4986.000000 -589.000000) translate(0,12)">10kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24ba0b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4365.000000 -238.000000) translate(0,12)">电炉变压器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24baaf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4354.500000 -222.000000) translate(0,12)">S11-10/0.4kW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24baf60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4374.500000 -209.000000) translate(0,12)">2000kW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24bb190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4532.000000 -238.000000) translate(0,12)">1号厂用变压器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24bb3d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4532.500000 -222.000000) translate(0,12)">S11-10/0.4kW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24bb620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4552.500000 -209.000000) translate(0,12)">2000kW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21d5400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4705.000000 -238.000000) translate(0,12)">2号厂用变压器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21d5640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4705.500000 -222.000000) translate(0,12)">S11-10/0.4kW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21d5890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4725.500000 -209.000000) translate(0,12)">2000kW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21d5ac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4899.000000 -154.000000) translate(0,12)">1号电容器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21d5ce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4899.500000 -140.000000) translate(0,12)">4000kVar</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21d63e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3445.000000 -24.000000) translate(0,12)">至空气鼓风机1</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21d68a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3465.500000 -9.000000) translate(0,12)">1000kW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21d6ae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3556.000000 -24.000000) translate(0,12)">二氧化硫风机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21d7170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3573.000000 -9.000000) translate(0,12)">2500kW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21d74e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3686.000000 -94.000000) translate(0,12)">测量TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21d7a20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3786.000000 18.000000) translate(0,12)">污水处理变压器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21d8290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3790.500000 34.000000) translate(0,12)">S11-10/0.4kW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21d85b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3811.500000 47.000000) translate(0,12)">500kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21d8920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3892.000000 -81.000000) translate(0,12)">1号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21d8d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3897.000000 -66.000000) translate(0,12)">7500kW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21d8fa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4022.000000 1.000000) translate(0,12)">1号发电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21d8fa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4022.000000 1.000000) translate(0,27)">机保护及</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21d8fa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4022.000000 1.000000) translate(0,42)">测量TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21d95d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4086.000000 -61.000000) translate(0,12)">1号发电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21d95d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4086.000000 -61.000000) translate(0,27)">机自动调</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21d95d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4086.000000 -61.000000) translate(0,42)">节励磁TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21da760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4154.000000 -30.000000) translate(0,12)">1号发电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21da760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4154.000000 -30.000000) translate(0,27)">机励磁</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21da760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4154.000000 -30.000000) translate(0,42)">整流变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21dafd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3941.857143 -996.000000) translate(0,12)">341</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21db250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4123.492401 -989.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21db490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3941.857143 -870.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21db6d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3941.857143 -633.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21db910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3656.000000 -515.000000) translate(0,12)">051</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21dbb50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3688.000000 -436.000000) translate(0,12)">05167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21dbd90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3900.000000 -515.000000) translate(0,12)">052</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21dbfd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3932.000000 -436.000000) translate(0,12)">05267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21dc210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4248.000000 -515.000000) translate(0,12)">053</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21dc450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4280.000000 -436.000000) translate(0,12)">05367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21dc690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4419.000000 -515.000000) translate(0,12)">054</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21dc8d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4451.000000 -436.000000) translate(0,12)">05467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21dcb10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4600.000000 -515.000000) translate(0,12)">055</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21dcd50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4632.000000 -436.000000) translate(0,12)">05567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21dcf90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4769.000000 -515.000000) translate(0,12)">056</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21dd1d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4801.000000 -436.000000) translate(0,12)">05667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21dd410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4947.000000 -515.000000) translate(0,12)">057</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21dd650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4979.000000 -436.000000) translate(0,12)">05767</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21dd890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4110.000000 -627.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21de840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4946.000000 -314.000000) translate(0,12)">0576</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21decc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3657.000000 -323.000000) translate(0,12)">041</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21def00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3508.000000 -207.000000) translate(0,12)">031</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21df140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3610.000000 -207.000000) translate(0,12)">032</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21df380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3854.000000 -207.000000) translate(0,12)">033</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21df5c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3985.000000 -207.000000) translate(0,12)">034</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_21b73f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3252.000000 -216.000000) translate(0,13)">冯  卫：18087857928</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_21b73f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3252.000000 -216.000000) translate(0,29)">温自琼：18787838331</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21c38d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5363.000000 -728.000000) translate(0,12)">SZ11-8000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21cddf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5363.000000 -714.000000) translate(0,12)">35±3×2.5%/10.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21ce430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5531.857143 -876.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21ce660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5531.857143 -639.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21d2ca0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5736.000000 -756.000000) translate(0,12)">10kVⅡ段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28c2f70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5269.492401 -523.000000) translate(0,12)">0122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28cf500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5132.000000 -513.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28d0730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5213.000000 -593.000000) translate(0,12)">10kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28dd250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5295.000000 -299.000000) translate(0,12)">二期60万吨硫铁</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28dd250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5295.000000 -299.000000) translate(0,27)">矿制酸Ⅰ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28df210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5357.000000 -514.000000) translate(0,12)">061</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28df480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5389.000000 -435.000000) translate(0,12)">06167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28ebc60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5484.000000 -299.000000) translate(0,12)">二期60万吨硫铁</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28ebc60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5484.000000 -299.000000) translate(0,27)">矿制酸Ⅱ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28ec2b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5539.000000 -513.000000) translate(0,12)">062</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28ec4e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5571.000000 -434.000000) translate(0,12)">06267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28ff910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5678.500000 -121.000000) translate(0,12)">4000kVar</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28fff40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5717.000000 -515.000000) translate(0,12)">063</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2900180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5749.000000 -436.000000) translate(0,12)">06367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29003c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5716.000000 -314.000000) translate(0,12)">0636</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_29049a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5382.000000 -756.000000) translate(0,15)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2905330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5654.000000 -144.000000) translate(0,12)">10kV2号电容器组</text>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21dfc60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3990.000000 1012.000000) translate(0,12)">P(kW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21e01a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3979.000000 997.000000) translate(0,12)">Q(kVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21e0680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3999.000000 982.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21e0d20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3985.000000 883.000000) translate(0,12)">P(kW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21e0ef0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3974.000000 868.000000) translate(0,12)">Q(kVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21e1100" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3994.000000 853.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21e17a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3988.000000 646.000000) translate(0,12)">P(kW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21e1990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3977.000000 631.000000) translate(0,12)">Q(kVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21e1ba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3997.000000 616.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21e2350" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3707.000000 527.000000) translate(0,12)">P(kW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21e2580" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3696.000000 512.000000) translate(0,12)">Q(kVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21e2790" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3716.000000 497.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21e57a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4982.000000 523.000000) translate(0,12)">Q(kVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21e5a10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5002.000000 508.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21e5d10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4883.000000 653.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21e6340" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4883.000000 638.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21e68a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4883.000000 623.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21e6e60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4875.000000 592.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21e70e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4875.000000 608.000000) translate(0,12)">3U0(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21e7410" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4315.000000 914.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21e7680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4315.000000 899.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21e78c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4315.000000 884.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21e7b00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4307.000000 853.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21e7d40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4307.000000 869.000000) translate(0,12)">3U0(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21e8570" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3973.000000 736.000000) translate(0,12)">油温(℃):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21e8d80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3972.000000 753.000000) translate(0,12)">档位(档):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21baad0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4012.000000 -85.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21bad00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3991.000000 -101.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21baf40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4003.000000 -55.000000) translate(0,12)">P(kW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21bb180" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3992.000000 -70.000000) translate(0,12)">Q(kVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2906500" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5372.000000 647.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2906760" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5372.000000 632.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29069a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5372.000000 617.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2906be0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5364.000000 586.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2906e20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5364.000000 602.000000) translate(0,12)">3U0(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29076d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5570.000000 736.000000) translate(0,12)">油温(℃):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2907930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5569.000000 753.000000) translate(0,12)">档位(档):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2907c60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5580.000000 893.000000) translate(0,12)">P(kW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2907ec0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5569.000000 878.000000) translate(0,12)">Q(kVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2908100" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5589.000000 863.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2908a10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5575.000000 661.000000) translate(0,12)">P(kW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2908c30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5564.000000 646.000000) translate(0,12)">Q(kVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2908e70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5584.000000 631.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2909780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5753.000000 520.000000) translate(0,12)">Q(kVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29099a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5773.000000 505.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_290a2b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5126.000000 437.000000) translate(0,12)">P(kW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_290a4a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5115.000000 422.000000) translate(0,12)">Q(kVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_290a6e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5135.000000 407.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_290e330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5293.000000 260.000000) translate(0,12)">P(kW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_290e590" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5282.000000 245.000000) translate(0,12)">Q(kVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_290e7d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5302.000000 230.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3120" y="-1197"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3119" y="-597"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3119" y="-1077"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="27" stroke="rgb(255,255,0)" stroke-width="0.416609" width="14" x="4104" y="-1061"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="27" stroke="rgb(0,255,0)" stroke-width="0.416609" width="14" x="4146" y="-682"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="15" stroke="rgb(60,120,255)" stroke-width="0.230232" width="8" x="3580" y="-360"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="27" stroke="rgb(60,120,255)" stroke-width="0.416609" width="14" x="3701" y="-206"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="26" stroke="rgb(60,120,255)" stroke-width="1" width="12" x="3741" y="-210"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="26" stroke="rgb(60,120,255)" stroke-width="1" width="12" x="3969" y="28"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="27" stroke="rgb(60,120,255)" stroke-width="0.416609" width="14" x="4040" y="-111"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="27" stroke="rgb(60,120,255)" stroke-width="0.416609" width="14" x="4105" y="-111"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="27" stroke="rgb(60,120,255)" stroke-width="0.416609" width="14" x="4171" y="-111"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="27" stroke="rgb(0,255,0)" stroke-width="0.416609" width="14" x="5765" y="-684"/>
  </g><g id="Generator_Layer">
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3950.000000 -68.000000)" xlink:href="#generator:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4276.878419 -1054.000000)" xlink:href="#transformer2:shape3_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4276.878419 -1054.000000)" xlink:href="#transformer2:shape3_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_WLHG.CX_WLHG_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="26882"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3894.857143 -680.000000)" xlink:href="#transformer2:shape13_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3894.857143 -680.000000)" xlink:href="#transformer2:shape13_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="19294" ObjectName="TF-CX_WLHG.CX_WLHG_1T"/>
    <cge:TPSR_Ref TObjectID="19294"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4385.000000 -249.000000)" xlink:href="#transformer2:shape48_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4385.000000 -249.000000)" xlink:href="#transformer2:shape48_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4566.000000 -249.000000)" xlink:href="#transformer2:shape48_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4566.000000 -249.000000)" xlink:href="#transformer2:shape48_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4735.000000 -249.000000)" xlink:href="#transformer2:shape48_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4735.000000 -249.000000)" xlink:href="#transformer2:shape48_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3819.000000 12.000000)" xlink:href="#transformer2:shape48_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3819.000000 12.000000)" xlink:href="#transformer2:shape48_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_WLHG.CX_WLHG_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="62112"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5484.857143 -686.000000)" xlink:href="#transformer2:shape13_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5484.857143 -686.000000)" xlink:href="#transformer2:shape13_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="40998" ObjectName="TF-CX_WLHG.CX_WLHG_2T"/>
    <cge:TPSR_Ref TObjectID="40998"/></metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3236.000000 -1114.013514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3236.000000 -1114.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-89015" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3263.538462 -1013.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="89015" ObjectName="CX_WLHG:CX_WLHG_301BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-89027" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4321.000000 -1013.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="89027" ObjectName="CX_WLHG:CX_WLHG_ZybBK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-89028" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4321.000000 -1000.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="89028" ObjectName="CX_WLHG:CX_WLHG_ZybBK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-89026" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4321.000000 -987.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="89026" ObjectName="CX_WLHG:CX_WLHG_ZybBK_Ia"/>
    </metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="3248" y="-1173"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="3248" y="-1173"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3199" y="-1190"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3199" y="-1190"/></g>
  </g><g id="CircleFilled_Layer">
   <circle DF8003:Layer="PUBLIC" cx="3569" cy="-332" fill="none" fillStyle="0" r="6" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="3569" cy="-340" fill="none" fillStyle="0" r="6" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="3599" cy="-332" fill="none" fillStyle="0" r="6" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="3599" cy="-340" fill="none" fillStyle="0" r="6" stroke="rgb(60,120,255)" stroke-width="1"/>
   <ellipse DF8003:Layer="PUBLIC" cx="3499" cy="-48" fill="none" fillStyle="0" rx="24" ry="23.5" stroke="rgb(60,120,255)" stroke-width="0.5"/>
   <ellipse DF8003:Layer="PUBLIC" cx="3601" cy="-48" fill="none" fillStyle="0" rx="24" ry="23.5" stroke="rgb(60,120,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="PUBLIC" cx="3694" cy="-149" fill="none" fillStyle="0" r="6" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="3694" cy="-157" fill="none" fillStyle="0" r="6" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="3708" cy="-149" fill="none" fillStyle="0" r="6" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="3708" cy="-157" fill="none" fillStyle="0" r="6" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="3722" cy="-149" fill="none" fillStyle="0" r="6" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="3722" cy="-157" fill="none" fillStyle="0" r="6" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="4033" cy="-54" fill="none" fillStyle="0" r="6" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="4033" cy="-62" fill="none" fillStyle="0" r="6" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="4047" cy="-54" fill="none" fillStyle="0" r="6" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="4047" cy="-62" fill="none" fillStyle="0" r="6" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="4061" cy="-54" fill="none" fillStyle="0" r="6" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="4061" cy="-62" fill="none" fillStyle="0" r="6" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="4097" cy="-69" fill="none" fillStyle="0" r="6" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="4097" cy="-77" fill="none" fillStyle="0" r="6" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="4127" cy="-69" fill="none" fillStyle="0" r="6" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="4127" cy="-77" fill="none" fillStyle="0" r="6" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="4179" cy="-45" fill="none" fillStyle="0" r="12" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="4179" cy="-61" fill="none" fillStyle="0" r="12" stroke="rgb(60,120,255)" stroke-width="1"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-89069">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3922.857143 -645.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19260" ObjectName="SW-CX_WLHG.CX_WLHG_001XC"/>
     <cge:Meas_Ref ObjectId="89069"/>
    <cge:TPSR_Ref TObjectID="19260"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-89069">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3922.857143 -577.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19261" ObjectName="SW-CX_WLHG.CX_WLHG_001XC1"/>
     <cge:Meas_Ref ObjectId="89069"/>
    <cge:TPSR_Ref TObjectID="19261"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-89063">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3922.857143 -1006.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19317" ObjectName="SW-CX_WLHG.CX_WLHG_341XC"/>
     <cge:Meas_Ref ObjectId="89063"/>
    <cge:TPSR_Ref TObjectID="19317"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-89063">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3922.857143 -941.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19318" ObjectName="SW-CX_WLHG.CX_WLHG_341XC1"/>
     <cge:Meas_Ref ObjectId="89063"/>
    <cge:TPSR_Ref TObjectID="19318"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-89074">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4101.492401 -942.000000)" xlink:href="#switch2:shape18_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19262" ObjectName="SW-CX_WLHG.CX_WLHG_3901XC"/>
     <cge:Meas_Ref ObjectId="89074"/>
    <cge:TPSR_Ref TObjectID="19262"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-89075">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4279.878419 -1006.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19320" ObjectName="SW-CX_WLHG.CX_WLHG_Zyb1XC"/>
     <cge:Meas_Ref ObjectId="89075"/>
    <cge:TPSR_Ref TObjectID="19320"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-89075">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4279.878419 -941.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19321" ObjectName="SW-CX_WLHG.CX_WLHG_Zyb1XC1"/>
     <cge:Meas_Ref ObjectId="89075"/>
    <cge:TPSR_Ref TObjectID="19321"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-89066">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3922.857143 -880.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19257" ObjectName="SW-CX_WLHG.CX_WLHG_301XC"/>
     <cge:Meas_Ref ObjectId="89066"/>
    <cge:TPSR_Ref TObjectID="19257"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-89066">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3922.857143 -815.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19258" ObjectName="SW-CX_WLHG.CX_WLHG_301XC1"/>
     <cge:Meas_Ref ObjectId="89066"/>
    <cge:TPSR_Ref TObjectID="19258"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-89077">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4143.000000 -603.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19264" ObjectName="SW-CX_WLHG.CX_WLHG_0901XC"/>
     <cge:Meas_Ref ObjectId="89077"/>
    <cge:TPSR_Ref TObjectID="19264"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-89088">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4229.000000 -527.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19275" ObjectName="SW-CX_WLHG.CX_WLHG_053XC"/>
     <cge:Meas_Ref ObjectId="89088"/>
    <cge:TPSR_Ref TObjectID="19275"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-89088">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4229.000000 -459.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19276" ObjectName="SW-CX_WLHG.CX_WLHG_053XC1"/>
     <cge:Meas_Ref ObjectId="89088"/>
    <cge:TPSR_Ref TObjectID="19276"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-89089">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4264.000000 -406.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19277" ObjectName="SW-CX_WLHG.CX_WLHG_05367SW"/>
     <cge:Meas_Ref ObjectId="89089"/>
    <cge:TPSR_Ref TObjectID="19277"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-89092">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4400.000000 -527.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19279" ObjectName="SW-CX_WLHG.CX_WLHG_054XC"/>
     <cge:Meas_Ref ObjectId="89092"/>
    <cge:TPSR_Ref TObjectID="19279"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-89092">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4400.000000 -459.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19280" ObjectName="SW-CX_WLHG.CX_WLHG_054XC1"/>
     <cge:Meas_Ref ObjectId="89092"/>
    <cge:TPSR_Ref TObjectID="19280"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-89093">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4435.000000 -406.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19281" ObjectName="SW-CX_WLHG.CX_WLHG_05467SW"/>
     <cge:Meas_Ref ObjectId="89093"/>
    <cge:TPSR_Ref TObjectID="19281"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-89096">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4581.000000 -527.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19283" ObjectName="SW-CX_WLHG.CX_WLHG_055XC"/>
     <cge:Meas_Ref ObjectId="89096"/>
    <cge:TPSR_Ref TObjectID="19283"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-89096">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4581.000000 -459.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19284" ObjectName="SW-CX_WLHG.CX_WLHG_055XC1"/>
     <cge:Meas_Ref ObjectId="89096"/>
    <cge:TPSR_Ref TObjectID="19284"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-89097">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4616.000000 -406.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19285" ObjectName="SW-CX_WLHG.CX_WLHG_05567SW"/>
     <cge:Meas_Ref ObjectId="89097"/>
    <cge:TPSR_Ref TObjectID="19285"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-89100">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4750.000000 -527.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19287" ObjectName="SW-CX_WLHG.CX_WLHG_056XC"/>
     <cge:Meas_Ref ObjectId="89100"/>
    <cge:TPSR_Ref TObjectID="19287"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-89100">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4750.000000 -459.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19288" ObjectName="SW-CX_WLHG.CX_WLHG_056XC1"/>
     <cge:Meas_Ref ObjectId="89100"/>
    <cge:TPSR_Ref TObjectID="19288"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-89101">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4785.000000 -406.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19289" ObjectName="SW-CX_WLHG.CX_WLHG_05667SW"/>
     <cge:Meas_Ref ObjectId="89101"/>
    <cge:TPSR_Ref TObjectID="19289"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-89104">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4928.000000 -527.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19291" ObjectName="SW-CX_WLHG.CX_WLHG_057XC"/>
     <cge:Meas_Ref ObjectId="89104"/>
    <cge:TPSR_Ref TObjectID="19291"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-89104">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4928.000000 -459.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19292" ObjectName="SW-CX_WLHG.CX_WLHG_057XC1"/>
     <cge:Meas_Ref ObjectId="89104"/>
    <cge:TPSR_Ref TObjectID="19292"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-89105">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4963.000000 -406.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19293" ObjectName="SW-CX_WLHG.CX_WLHG_05767SW"/>
     <cge:Meas_Ref ObjectId="89105"/>
    <cge:TPSR_Ref TObjectID="19293"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-89076">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4929.000000 -284.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19554" ObjectName="SW-CX_WLHG.CX_WLHG_0576SW"/>
     <cge:Meas_Ref ObjectId="89076"/>
    <cge:TPSR_Ref TObjectID="19554"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-89084">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3881.000000 -527.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19271" ObjectName="SW-CX_WLHG.CX_WLHG_052XC"/>
     <cge:Meas_Ref ObjectId="89084"/>
    <cge:TPSR_Ref TObjectID="19271"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-89084">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3881.000000 -459.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19272" ObjectName="SW-CX_WLHG.CX_WLHG_052XC1"/>
     <cge:Meas_Ref ObjectId="89084"/>
    <cge:TPSR_Ref TObjectID="19272"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-89085">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3916.000000 -406.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19273" ObjectName="SW-CX_WLHG.CX_WLHG_05267SW"/>
     <cge:Meas_Ref ObjectId="89085"/>
    <cge:TPSR_Ref TObjectID="19273"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-89080">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3637.000000 -527.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19267" ObjectName="SW-CX_WLHG.CX_WLHG_051XC"/>
     <cge:Meas_Ref ObjectId="89080"/>
    <cge:TPSR_Ref TObjectID="19267"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-89080">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3637.000000 -459.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19268" ObjectName="SW-CX_WLHG.CX_WLHG_051XC1"/>
     <cge:Meas_Ref ObjectId="89080"/>
    <cge:TPSR_Ref TObjectID="19268"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-89081">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3672.000000 -406.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19269" ObjectName="SW-CX_WLHG.CX_WLHG_05167SW"/>
     <cge:Meas_Ref ObjectId="89081"/>
    <cge:TPSR_Ref TObjectID="19269"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3637.000000 -335.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3637.000000 -267.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3489.000000 -219.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3489.000000 -151.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3591.000000 -219.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3591.000000 -151.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3834.000000 -219.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3834.000000 -151.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-114480">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3965.000000 -219.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21747" ObjectName="SW-CX_WLHG.CX_WLHG_034XC"/>
     <cge:Meas_Ref ObjectId="114480"/>
    <cge:TPSR_Ref TObjectID="21747"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-114480">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3965.000000 -151.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21754" ObjectName="SW-CX_WLHG.CX_WLHG_034XC1"/>
     <cge:Meas_Ref ObjectId="114480"/>
    <cge:TPSR_Ref TObjectID="21754"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-243959">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5512.857143 -651.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41003" ObjectName="SW-CX_WLHG.CX_WLHG_002XC"/>
     <cge:Meas_Ref ObjectId="243959"/>
    <cge:TPSR_Ref TObjectID="41003"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-243959">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5512.857143 -583.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41004" ObjectName="SW-CX_WLHG.CX_WLHG_002XC1"/>
     <cge:Meas_Ref ObjectId="243959"/>
    <cge:TPSR_Ref TObjectID="41004"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-243962">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5512.857143 -886.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41000" ObjectName="SW-CX_WLHG.CX_WLHG_302XC"/>
     <cge:Meas_Ref ObjectId="243962"/>
    <cge:TPSR_Ref TObjectID="41000"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-243962">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5512.857143 -821.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41001" ObjectName="SW-CX_WLHG.CX_WLHG_302XC1"/>
     <cge:Meas_Ref ObjectId="243962"/>
    <cge:TPSR_Ref TObjectID="41001"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5762.000000 -605.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5247.492401 -476.000000)" xlink:href="#switch2:shape18_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-243975">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5113.000000 -525.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41008" ObjectName="SW-CX_WLHG.CX_WLHG_012XC"/>
     <cge:Meas_Ref ObjectId="243975"/>
    <cge:TPSR_Ref TObjectID="41008"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-243975">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5113.000000 -457.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41009" ObjectName="SW-CX_WLHG.CX_WLHG_012XC1"/>
     <cge:Meas_Ref ObjectId="243975"/>
    <cge:TPSR_Ref TObjectID="41009"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-246696">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5338.000000 -526.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41402" ObjectName="SW-CX_WLHG.CX_WLHG_061XC"/>
     <cge:Meas_Ref ObjectId="246696"/>
    <cge:TPSR_Ref TObjectID="41402"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-246696">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5338.000000 -458.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41403" ObjectName="SW-CX_WLHG.CX_WLHG_061XC1"/>
     <cge:Meas_Ref ObjectId="246696"/>
    <cge:TPSR_Ref TObjectID="41403"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-246699">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5373.000000 -405.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41407" ObjectName="SW-CX_WLHG.CX_WLHG_06167SW"/>
     <cge:Meas_Ref ObjectId="246699"/>
    <cge:TPSR_Ref TObjectID="41407"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-246701">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5520.000000 -525.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41405" ObjectName="SW-CX_WLHG.CX_WLHG_062XC"/>
     <cge:Meas_Ref ObjectId="246701"/>
    <cge:TPSR_Ref TObjectID="41405"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-246701">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5520.000000 -457.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41406" ObjectName="SW-CX_WLHG.CX_WLHG_062XC1"/>
     <cge:Meas_Ref ObjectId="246701"/>
    <cge:TPSR_Ref TObjectID="41406"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-246704">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5555.000000 -404.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41408" ObjectName="SW-CX_WLHG.CX_WLHG_06267SW"/>
     <cge:Meas_Ref ObjectId="246704"/>
    <cge:TPSR_Ref TObjectID="41408"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-243988">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5698.000000 -527.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41015" ObjectName="SW-CX_WLHG.CX_WLHG_063XC"/>
     <cge:Meas_Ref ObjectId="243988"/>
    <cge:TPSR_Ref TObjectID="41015"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-243988">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5698.000000 -459.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41016" ObjectName="SW-CX_WLHG.CX_WLHG_063XC1"/>
     <cge:Meas_Ref ObjectId="243988"/>
    <cge:TPSR_Ref TObjectID="41016"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-243989">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5733.000000 -406.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41014" ObjectName="SW-CX_WLHG.CX_WLHG_06367SW"/>
     <cge:Meas_Ref ObjectId="243989"/>
    <cge:TPSR_Ref TObjectID="41014"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5699.000000 -284.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_22b7b00">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4326.878419 -1080.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2219f00">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4189.000000 -593.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2277000">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4198.000000 -389.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2277bb0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3952.857143 -1008.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22ce1f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4369.000000 -389.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22d0840">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4550.000000 -389.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22abbf0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4719.000000 -389.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_228a3f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4897.000000 -389.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2260ed0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4896.000000 -221.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22e4000">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3850.000000 -389.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2201660">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3606.000000 -389.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21d1ef0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5808.000000 -595.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28d8ea0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5307.000000 -388.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28e78b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5489.000000 -387.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28f4b40">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5667.000000 -389.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28feba0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5666.000000 -221.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_客户变35.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="3248" y="-1173"/></g>
   <g href="cx_索引_接线图_客户变35.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3199" y="-1190"/></g>
  </g><g id="Polygon_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4286,-1147 4290,-1163 4295,-1147 4286,-1147 " stroke="rgb(60,120,255)" stroke-width="0.5"/>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-89009" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4038.857143 -1014.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="89009" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19255"/>
     <cge:Term_Ref ObjectID="26802"/>
    <cge:TPSR_Ref TObjectID="19255"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-89010" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4038.857143 -1014.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="89010" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19255"/>
     <cge:Term_Ref ObjectID="26802"/>
    <cge:TPSR_Ref TObjectID="19255"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-89006" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4038.857143 -1014.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="89006" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19255"/>
     <cge:Term_Ref ObjectID="26802"/>
    <cge:TPSR_Ref TObjectID="19255"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-89015" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4034.857143 -885.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="89015" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19256"/>
     <cge:Term_Ref ObjectID="26804"/>
    <cge:TPSR_Ref TObjectID="19256"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-89016" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4034.857143 -885.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="89016" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19256"/>
     <cge:Term_Ref ObjectID="26804"/>
    <cge:TPSR_Ref TObjectID="19256"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-89012" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4034.857143 -885.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="89012" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19256"/>
     <cge:Term_Ref ObjectID="26804"/>
    <cge:TPSR_Ref TObjectID="19256"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-89021" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4039.857143 -648.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="89021" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19259"/>
     <cge:Term_Ref ObjectID="26810"/>
    <cge:TPSR_Ref TObjectID="19259"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-89022" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4039.857143 -648.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="89022" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19259"/>
     <cge:Term_Ref ObjectID="26810"/>
    <cge:TPSR_Ref TObjectID="19259"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-89018" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4039.857143 -648.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="89018" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19259"/>
     <cge:Term_Ref ObjectID="26810"/>
    <cge:TPSR_Ref TObjectID="19259"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-89036" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3757.000000 -530.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="89036" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19266"/>
     <cge:Term_Ref ObjectID="26824"/>
    <cge:TPSR_Ref TObjectID="19266"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-89037" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3757.000000 -530.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="89037" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19266"/>
     <cge:Term_Ref ObjectID="26824"/>
    <cge:TPSR_Ref TObjectID="19266"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-89035" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3757.000000 -530.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="89035" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19266"/>
     <cge:Term_Ref ObjectID="26824"/>
    <cge:TPSR_Ref TObjectID="19266"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-89040" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3937.000000 -530.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="89040" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19270"/>
     <cge:Term_Ref ObjectID="26832"/>
    <cge:TPSR_Ref TObjectID="19270"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-89041" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3937.000000 -530.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="89041" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19270"/>
     <cge:Term_Ref ObjectID="26832"/>
    <cge:TPSR_Ref TObjectID="19270"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-89039" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3937.000000 -530.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="89039" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19270"/>
     <cge:Term_Ref ObjectID="26832"/>
    <cge:TPSR_Ref TObjectID="19270"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-89044" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4285.000000 -530.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="89044" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19274"/>
     <cge:Term_Ref ObjectID="26840"/>
    <cge:TPSR_Ref TObjectID="19274"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-89045" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4285.000000 -530.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="89045" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19274"/>
     <cge:Term_Ref ObjectID="26840"/>
    <cge:TPSR_Ref TObjectID="19274"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-89043" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4285.000000 -530.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="89043" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19274"/>
     <cge:Term_Ref ObjectID="26840"/>
    <cge:TPSR_Ref TObjectID="19274"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-89048" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4456.000000 -530.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="89048" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19278"/>
     <cge:Term_Ref ObjectID="26848"/>
    <cge:TPSR_Ref TObjectID="19278"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-89049" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4456.000000 -530.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="89049" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19278"/>
     <cge:Term_Ref ObjectID="26848"/>
    <cge:TPSR_Ref TObjectID="19278"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-89047" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4456.000000 -530.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="89047" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19278"/>
     <cge:Term_Ref ObjectID="26848"/>
    <cge:TPSR_Ref TObjectID="19278"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-89052" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4634.000000 -530.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="89052" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19282"/>
     <cge:Term_Ref ObjectID="26856"/>
    <cge:TPSR_Ref TObjectID="19282"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-89053" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4634.000000 -530.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="89053" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19282"/>
     <cge:Term_Ref ObjectID="26856"/>
    <cge:TPSR_Ref TObjectID="19282"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-89051" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4634.000000 -530.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="89051" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19282"/>
     <cge:Term_Ref ObjectID="26856"/>
    <cge:TPSR_Ref TObjectID="19282"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-89056" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4803.000000 -530.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="89056" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19286"/>
     <cge:Term_Ref ObjectID="26864"/>
    <cge:TPSR_Ref TObjectID="19286"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-89057" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4803.000000 -530.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="89057" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19286"/>
     <cge:Term_Ref ObjectID="26864"/>
    <cge:TPSR_Ref TObjectID="19286"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-89055" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4803.000000 -530.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="89055" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19286"/>
     <cge:Term_Ref ObjectID="26864"/>
    <cge:TPSR_Ref TObjectID="19286"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-89060" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5046.000000 -526.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="89060" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19290"/>
     <cge:Term_Ref ObjectID="26872"/>
    <cge:TPSR_Ref TObjectID="19290"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-89059" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5046.000000 -526.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="89059" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19290"/>
     <cge:Term_Ref ObjectID="26872"/>
    <cge:TPSR_Ref TObjectID="19290"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-89030" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4940.000000 -652.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="89030" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19253"/>
     <cge:Term_Ref ObjectID="26799"/>
    <cge:TPSR_Ref TObjectID="19253"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-89031" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4940.000000 -652.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="89031" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19253"/>
     <cge:Term_Ref ObjectID="26799"/>
    <cge:TPSR_Ref TObjectID="19253"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-89032" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4940.000000 -652.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="89032" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19253"/>
     <cge:Term_Ref ObjectID="26799"/>
    <cge:TPSR_Ref TObjectID="19253"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-89034" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4940.000000 -652.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="89034" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19253"/>
     <cge:Term_Ref ObjectID="26799"/>
    <cge:TPSR_Ref TObjectID="19253"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-89033" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4940.000000 -652.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="89033" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19253"/>
     <cge:Term_Ref ObjectID="26799"/>
    <cge:TPSR_Ref TObjectID="19253"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-89001" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4372.000000 -915.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="89001" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19252"/>
     <cge:Term_Ref ObjectID="26798"/>
    <cge:TPSR_Ref TObjectID="19252"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-89002" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4372.000000 -915.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="89002" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19252"/>
     <cge:Term_Ref ObjectID="26798"/>
    <cge:TPSR_Ref TObjectID="19252"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-89003" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4372.000000 -915.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="89003" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19252"/>
     <cge:Term_Ref ObjectID="26798"/>
    <cge:TPSR_Ref TObjectID="19252"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-89005" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4372.000000 -915.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="89005" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19252"/>
     <cge:Term_Ref ObjectID="26798"/>
    <cge:TPSR_Ref TObjectID="19252"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-89004" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4372.000000 -915.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="89004" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19252"/>
     <cge:Term_Ref ObjectID="26798"/>
    <cge:TPSR_Ref TObjectID="19252"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="2" id="ME-89025" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4044.857143 -753.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="89025" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19294"/>
     <cge:Term_Ref ObjectID="26880"/>
    <cge:TPSR_Ref TObjectID="19294"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-89024" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4044.857143 -753.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="89024" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19294"/>
     <cge:Term_Ref ObjectID="26880"/>
    <cge:TPSR_Ref TObjectID="19294"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-114489" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4060.000000 56.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="114489" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21746"/>
     <cge:Term_Ref ObjectID="30457"/>
    <cge:TPSR_Ref TObjectID="21746"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-114490" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4060.000000 56.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="114490" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21746"/>
     <cge:Term_Ref ObjectID="30457"/>
    <cge:TPSR_Ref TObjectID="21746"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-114486" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4060.000000 56.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="114486" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21746"/>
     <cge:Term_Ref ObjectID="30457"/>
    <cge:TPSR_Ref TObjectID="21746"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-114483" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4060.000000 56.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="114483" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21746"/>
     <cge:Term_Ref ObjectID="30457"/>
    <cge:TPSR_Ref TObjectID="21746"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-243968" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5426.000000 -645.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="243968" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41005"/>
     <cge:Term_Ref ObjectID="62126"/>
    <cge:TPSR_Ref TObjectID="41005"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="1" id="ME-243969" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5426.000000 -645.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="243969" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41005"/>
     <cge:Term_Ref ObjectID="62126"/>
    <cge:TPSR_Ref TObjectID="41005"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="1" id="ME-243970" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5426.000000 -645.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="243970" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41005"/>
     <cge:Term_Ref ObjectID="62126"/>
    <cge:TPSR_Ref TObjectID="41005"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="1" id="ME-0" prefix="" rightAlign="0">
    <text fill="rgb(190,190,190)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5426.000000 -645.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41005"/>
     <cge:Term_Ref ObjectID="62126"/>
    <cge:TPSR_Ref TObjectID="41005"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ubc" PreSymbol="0" appendix="" decimal="1" id="ME-0" prefix="" rightAlign="0">
    <text fill="rgb(190,190,190)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5426.000000 -645.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41005"/>
     <cge:Term_Ref ObjectID="62126"/>
    <cge:TPSR_Ref TObjectID="41005"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="2" id="ME-243954" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5639.000000 -752.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="243954" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40998"/>
     <cge:Term_Ref ObjectID="62110"/>
    <cge:TPSR_Ref TObjectID="40998"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-243941" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5639.000000 -752.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="243941" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40998"/>
     <cge:Term_Ref ObjectID="62110"/>
    <cge:TPSR_Ref TObjectID="40998"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-243942" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5629.000000 -894.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="243942" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40999"/>
     <cge:Term_Ref ObjectID="62114"/>
    <cge:TPSR_Ref TObjectID="40999"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-243943" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5629.000000 -894.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="243943" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40999"/>
     <cge:Term_Ref ObjectID="62114"/>
    <cge:TPSR_Ref TObjectID="40999"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-243947" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5629.000000 -894.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="243947" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40999"/>
     <cge:Term_Ref ObjectID="62114"/>
    <cge:TPSR_Ref TObjectID="40999"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-243953" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5624.000000 -661.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="243953" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41002"/>
     <cge:Term_Ref ObjectID="62120"/>
    <cge:TPSR_Ref TObjectID="41002"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-243948" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5624.000000 -661.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="243948" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41002"/>
     <cge:Term_Ref ObjectID="62120"/>
    <cge:TPSR_Ref TObjectID="41002"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-243952" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5624.000000 -661.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="243952" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41002"/>
     <cge:Term_Ref ObjectID="62120"/>
    <cge:TPSR_Ref TObjectID="41002"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-243987" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5812.000000 -520.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="243987" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41013"/>
     <cge:Term_Ref ObjectID="62137"/>
    <cge:TPSR_Ref TObjectID="41013"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-243986" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5812.000000 -520.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="243986" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41013"/>
     <cge:Term_Ref ObjectID="62137"/>
    <cge:TPSR_Ref TObjectID="41013"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-243978" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5174.000000 -437.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="243978" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41007"/>
     <cge:Term_Ref ObjectID="62127"/>
    <cge:TPSR_Ref TObjectID="41007"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-243979" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5174.000000 -437.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="243979" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41007"/>
     <cge:Term_Ref ObjectID="62127"/>
    <cge:TPSR_Ref TObjectID="41007"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-243977" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5174.000000 -437.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="243977" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41007"/>
     <cge:Term_Ref ObjectID="62127"/>
    <cge:TPSR_Ref TObjectID="41007"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-246706" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5347.000000 -260.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="246706" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41401"/>
     <cge:Term_Ref ObjectID="62834"/>
    <cge:TPSR_Ref TObjectID="41401"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-246707" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5347.000000 -260.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="246707" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41401"/>
     <cge:Term_Ref ObjectID="62834"/>
    <cge:TPSR_Ref TObjectID="41401"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-246705" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5347.000000 -260.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="246705" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41401"/>
     <cge:Term_Ref ObjectID="62834"/>
    <cge:TPSR_Ref TObjectID="41401"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-246710" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5519.000000 -260.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="246710" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41404"/>
     <cge:Term_Ref ObjectID="62840"/>
    <cge:TPSR_Ref TObjectID="41404"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-246711" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5519.000000 -260.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="246711" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41404"/>
     <cge:Term_Ref ObjectID="62840"/>
    <cge:TPSR_Ref TObjectID="41404"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-246709" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5519.000000 -260.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="246709" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41404"/>
     <cge:Term_Ref ObjectID="62840"/>
    <cge:TPSR_Ref TObjectID="41404"/></metadata>
   </g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4919.000000 -155.000000)" xlink:href="#capacitor:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-CX_WLHG.CX_WLHG_Cb2">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5689.000000 -155.000000)" xlink:href="#capacitor:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41012" ObjectName="CB-CX_WLHG.CX_WLHG_Cb2"/>
    <cge:TPSR_Ref TObjectID="41012"/></metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_WLHG"/>
</svg>