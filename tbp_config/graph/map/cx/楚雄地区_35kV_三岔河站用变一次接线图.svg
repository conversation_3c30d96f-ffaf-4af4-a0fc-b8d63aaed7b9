<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-226" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="2593 -1233 2881 1346">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape25_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="4" x2="10" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="49" y2="58"/>
    <rect height="29" stroke-width="0.416609" width="9" x="14" y="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="7" x2="36" y1="14" y2="44"/>
   </symbol>
   <symbol id="switch2:shape25_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="4" x2="10" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="49" y2="58"/>
    <rect height="26" stroke-width="0.416609" width="14" x="0" y="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="7" x2="7" y1="50" y2="14"/>
   </symbol>
   <symbol id="switch2:shape25-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="7" x2="36" y1="14" y2="44"/>
    <rect height="29" stroke-width="0.416609" width="9" x="14" y="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="4" x2="10" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="5" y2="14"/>
   </symbol>
   <symbol id="switch2:shape25-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="7" x2="7" y1="50" y2="14"/>
    <rect height="26" stroke-width="0.416609" width="14" x="0" y="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="4" x2="10" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="5" y2="14"/>
   </symbol>
   <symbol id="transformer2:shape40_0">
    <circle cx="30" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="32" y1="53" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="26" y2="26"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,56 6,56 6,28 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="32,14 26,27 39,27 32,14 32,15 32,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="42" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="35" y1="57" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="26" y1="57" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="57" y2="62"/>
   </symbol>
   <symbol id="transformer2:shape40_1">
    <circle cx="31" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="81" y2="81"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="87" y2="87"/>
    <polyline DF8003:Layer="PUBLIC" points="30,87 26,78 36,78 30,87 "/>
   </symbol>
   <symbol id="voltageTransformer:shape2">
    <circle cx="7" cy="7" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="7" cy="15" fillStyle="0" r="6" stroke-width="0.431185"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_229cf80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2085540" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2085d60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2027ba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2028310" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2028a50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2029050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_26895d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_20277a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_20277a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_200e6b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_200e6b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2010440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2010440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_1fe2750" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1fe43c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1fe5030" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_22a8b10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_22a9260" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2695560" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22aaa70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22ab1e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2692ca0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2693d40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_26946c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22a4e10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_22a5800" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_22a6400" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_22a6e50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_22a7ff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_22b1ba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_22a4150" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22b3320" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_21cd540" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_21ce9b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1356" width="2891" x="2588" y="-1238"/>
  </g><g id="Rectangle_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="1170" stroke="rgb(0,255,0)" stroke-width="2.13655" width="417" x="4113" y="-1087"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="1247" stroke="rgb(0,255,0)" stroke-width="2.6792" width="522" x="2959" y="-1136"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.142857 -0.000000 0.000000 -1.039855 3709.000000 -464.166667)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48123" ObjectName="SW-DY_SCH.DY_SCH_1QFSW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="48123"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.142857 -0.000000 0.000000 -1.039855 3709.000000 -337.166667)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48125" ObjectName="SW-DY_SCH.DY_SCH_ATS1SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="48125"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.142857 -0.000000 0.000000 -1.039855 3948.000000 -463.166667)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48124" ObjectName="SW-DY_SCH.DY_SCH_2QFSW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="48124"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.142857 -0.000000 0.000000 -1.039855 3948.000000 -340.166667)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48126" ObjectName="SW-DY_SCH.DY_SCH_ATS2SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="48126"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3711.000000 -701.000000)" xlink:href="#switch2:shape25_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3950.000000 -700.000000)" xlink:href="#switch2:shape25_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3750,-146 3932,-146 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="3750,-146 3932,-146 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Link_Layer">
   <g class="BV-0KV" id="g_2666310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3718,-706 3718,-672 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="0@1" ObjectIDZND0="19749@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3718,-706 3718,-672 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fb9ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3839,-271 3893,-271 3893,-230 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="voltageTransformer" ObjectIDND0="0@0" ObjectIDND1="48125@x" ObjectIDND2="48126@x" ObjectIDZND0="g_2a5bfd0@0" Pin0InfoVect0LinkObjId="g_2a5bfd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3839,-271 3893,-271 3893,-230 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2816420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3839,-146 3839,-271 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="0@0" ObjectIDZND0="48125@x" ObjectIDZND1="48126@x" ObjectIDZND2="g_2a5bfd0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_2a5bfd0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3839,-146 3839,-271 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22a9d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3839,-271 3839,-303 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="voltageTransformer" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDND1="g_2a5bfd0@0" ObjectIDZND0="48125@x" ObjectIDZND1="48126@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_2a5bfd0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3839,-271 3839,-303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27f5090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3719,-342 3719,-303 3839,-303 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" EndDevType1="voltageTransformer" EndDevType2="switch" ObjectIDND0="48125@0" ObjectIDZND0="0@0" ObjectIDZND1="g_2a5bfd0@0" ObjectIDZND2="48126@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_2a5bfd0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3719,-342 3719,-303 3839,-303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_294da60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3719,-469 3719,-380 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="48123@0" ObjectIDZND0="48125@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3719,-469 3719,-380 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_294d580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3958,-345 3958,-303 3839,-303 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" EndDevType1="voltageTransformer" EndDevType2="switch" ObjectIDND0="48126@0" ObjectIDZND0="0@0" ObjectIDZND1="g_2a5bfd0@0" ObjectIDZND2="48125@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_2a5bfd0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3958,-345 3958,-303 3839,-303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27b0020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3958,-470 3958,-383 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="48124@0" ObjectIDZND0="48126@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3958,-470 3958,-383 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27f4db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3719,-621 3719,-507 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="19749@0" ObjectIDZND0="48123@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2666310_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3719,-621 3719,-507 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2aa2e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3957,-705 3957,-671 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3957,-705 3957,-671 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_268f3a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3958,-620 3958,-506 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="48124@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3958,-620 3958,-506 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_273ff50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3718,-758 3718,-812 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3718,-758 3718,-812 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2740180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3957,-757 3957,-814 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3957,-757 3957,-814 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="3839" cy="-146" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="267" x="2704" y="-1211"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="267" x="2704" y="-1211"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="2667" y="-1228"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="2667" y="-1228"/></g>
  </g><g id="MotifButton_Layer">
   <g href="大姚_35kV_三岔河变.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="267" x="2704" y="-1211"/></g>
   <g href="大姚_35kV_三岔河变.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="2667" y="-1228"/></g>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="2594" y="-1232"/>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_XJH.CX_XJH_PDB_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="27552"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3687.000000 -578.899083)" xlink:href="#transformer2:shape40_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3687.000000 -578.899083)" xlink:href="#transformer2:shape40_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="19749" ObjectName="TF-CX_XJH.CX_XJH_PDB_1T"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3926.000000 -577.899083)" xlink:href="#transformer2:shape40_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3926.000000 -577.899083)" xlink:href="#transformer2:shape40_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-308721" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4130.000000 -1012.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="308721"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-308722" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4130.000000 -964.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="308722"/>
    </metadata>
   </g>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4115" x2="4525" y1="-1049" y2="-1049"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4115" x2="4525" y1="-1002" y2="-1002"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4115" x2="4525" y1="-954" y2="-954"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4115" x2="4525" y1="-906" y2="-906"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4115" x2="4525" y1="-858" y2="-858"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4115" x2="4525" y1="-809" y2="-809"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4115" x2="4525" y1="-762" y2="-762"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4115" x2="4525" y1="-714" y2="-714"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4115" x2="4525" y1="-666" y2="-666"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4115" x2="4525" y1="-618" y2="-618"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5472" x2="5472" y1="-438" y2="-438"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="2959" x2="3477" y1="-1095" y2="-1095"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="2959" x2="3477" y1="-1048" y2="-1048"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="2959" x2="3476" y1="-1000" y2="-1000"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="2960" x2="3476" y1="-952" y2="-952"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="2959" x2="3477" y1="-904" y2="-904"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="2960" x2="3477" y1="-855" y2="-855"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="2960" x2="3477" y1="-808" y2="-808"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="2960" x2="3476" y1="-760" y2="-760"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="2959" x2="3476" y1="-712" y2="-712"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="2960" x2="3477" y1="-664" y2="-664"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="2962" x2="3479" y1="-567" y2="-567"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="2962" x2="3479" y1="-519" y2="-519"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="2962" x2="3479" y1="-471" y2="-471"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="2962" x2="3479" y1="-423" y2="-423"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="2962" x2="3479" y1="-374" y2="-374"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="2962" x2="3479" y1="-327" y2="-327"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="2963" x2="3479" y1="-279" y2="-279"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="2962" x2="3479" y1="-231" y2="-231"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="2962" x2="3479" y1="-183" y2="-183"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="2962" x2="3480" y1="-614" y2="-614"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="2960" x2="3478" y1="-133" y2="-133"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="2961" x2="3477" y1="-85" y2="-85"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="2960" x2="3477" y1="-37" y2="-37"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="2961" x2="3478" y1="11" y2="11"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4115" x2="4525" y1="-530" y2="-530"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4115" x2="4525" y1="-578" y2="-578"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4528" x2="4528" y1="80" y2="-1087"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4115" x2="4525" y1="-484" y2="-484"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4115" x2="4525" y1="-437" y2="-437"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4115" x2="4525" y1="-389" y2="-389"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4115" x2="4525" y1="-341" y2="-341"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4115" x2="4525" y1="-293" y2="-293"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4115" x2="4525" y1="-244" y2="-244"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4115" x2="4525" y1="-197" y2="-197"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4115" x2="4525" y1="-149" y2="-149"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4115" x2="4525" y1="-101" y2="-101"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4115" x2="4525" y1="-53" y2="-53"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4115" x2="4525" y1="35" y2="35"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4115" x2="4525" y1="-13" y2="-13"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="2960" x2="3477" y1="58" y2="58"/>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 2743.500000 -1155.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-184536" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3290.000000 -1080.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="184536" ObjectName="DY_SCH:DY_SCH_GG_Uab1_101"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-184537" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3290.000000 -1030.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="184537" ObjectName="DY_SCH:DY_SCH_GG_Ubc1_102"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-184538" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3290.000000 -987.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="184538" ObjectName="DY_SCH:DY_SCH_GG_Uca1_103"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-184533" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3290.000000 -940.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="184533" ObjectName="DY_SCH:DY_SCH_GG_Ua1_104"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-184534" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3290.000000 -891.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="184534" ObjectName="DY_SCH:DY_SCH_GG_Ub1_105"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-184535" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3290.000000 -843.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="184535" ObjectName="DY_SCH:DY_SCH_GG_Uc1_106"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-184530" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3290.000000 -796.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="184530" ObjectName="DY_SCH:DY_SCH_GG_Ia1_107"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-184531" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3290.000000 -744.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="184531" ObjectName="DY_SCH:DY_SCH_GG_Ib1_108"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-184532" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3290.000000 -693.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="184532" ObjectName="DY_SCH:DY_SCH_GG_Ic1_109"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-308211" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3290.000000 -650.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="308211" ObjectName="DY_SCH:DY_SCH_GG_P_110"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-308212" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3290.000000 -599.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="308212" ObjectName="DY_SCH:DY_SCH_GG_Q_111"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-308213" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3290.000000 -548.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="308213" ObjectName="DY_SCH:DY_SCH_GG_Cos_112"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-184545" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3290.000000 -457.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="184545" ObjectName="DY_SCH:DY_SCH_GG_Uab2_113"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-184546" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3290.000000 -407.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="184546" ObjectName="DY_SCH:DY_SCH_GG_Ubc2_114"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-184547" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3290.000000 -364.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="184547" ObjectName="DY_SCH:DY_SCH_GG_Uca2_115"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-184542" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3290.000000 -317.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="184542" ObjectName="DY_SCH:DY_SCH_GG_Ua2_116"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-184543" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3290.000000 -268.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="184543" ObjectName="DY_SCH:DY_SCH_GG_Ub2_117"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-184544" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3290.000000 -220.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="184544" ObjectName="DY_SCH:DY_SCH_GG_Uc2_118"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-184539" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3290.000000 -173.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="184539" ObjectName="DY_SCH:DY_SCH_GG_Ia2_119"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-184540" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3290.000000 -121.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="184540" ObjectName="DY_SCH:DY_SCH_GG_Ib2_120"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-184541" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3290.000000 -70.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="184541" ObjectName="DY_SCH:DY_SCH_GG_Ic2_121"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-308214" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3290.000000 -27.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="308214" ObjectName="DY_SCH:DY_SCH_GG_P_122"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-308215" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3290.000000 24.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="308215" ObjectName="DY_SCH:DY_SCH_GG_Q_123"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-308216" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3290.000000 75.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="308216" ObjectName="DY_SCH:DY_SCH_GG_Cos_124"/>
    </metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2a5bfd0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3886.000000 -210.000000)" xlink:href="#voltageTransformer:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_29b5370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2741.500000 -1200.500000) translate(0,16)">三岔河站用变一次接线图</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1ffed50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3675.000000 -849.000000) translate(0,15)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_203ccf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3736.000000 -134.000000) translate(0,20)">0.4KVⅠ段馈线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_29f0670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3937.000000 -855.000000) translate(0,15)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(205,51,51)" font-family="SimSun" font-size="18" graphid="g_2aa3490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4249.000000 -1072.000000) translate(0,15)">站用变保护信号量</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_209b9f0" transform="matrix(0.828800 -0.000000 -0.000000 1.000000 3633.483200 -505.000000) translate(0,15)">进线开关1</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a5c990" transform="matrix(0.828800 -0.000000 -0.000000 1.000000 3639.284800 -378.000000) translate(0,15)">ATS-Ⅰ位</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a5cbd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3982.000000 -374.000000) translate(0,15)">ATS-Ⅱ位</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_23220e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3980.000000 -499.000000) translate(0,15)">进线开关2</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(205,51,51)" font-family="SimSun" font-size="18" graphid="g_2322320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3167.000000 -1117.000000) translate(0,15)">1号站用遥测量</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_2322560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2976.000000 -1079.000000) translate(0,15)">1号站用变AB线电压Uab</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_2018540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3400.000000 -642.000000) translate(0,15)">KW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_2818100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2976.000000 -1031.000000) translate(0,15)">1号站用变BC线电压Ubc</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_26ce0f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3394.500000 -601.000000) translate(0,15)">KVar</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_26ce330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2976.000000 -981.000000) translate(0,15)">1号站用变CA线电压Uca</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_205b830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2976.000000 -936.000000) translate(0,15)">1号站用变A相电压Ua</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_27312f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2976.000000 -889.000000) translate(0,15)">1号站用变B相电压Ub</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_29d2080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2976.000000 -840.000000) translate(0,15)">1号站用变C相电压Uc</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_2a5d660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2976.000000 -791.000000) translate(0,15)">1号站用变A相电流Ia</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_26e5ab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3408.500000 -796.000000) translate(0,15)">A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_26ce580" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2976.000000 -748.000000) translate(0,15)">1号站用变B相电流Ib</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_270ff00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3408.500000 -742.000000) translate(0,15)">A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_2023030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2976.000000 -694.000000) translate(0,15)">1号站用变C相电流Ic</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_2123c20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4175.000000 -1036.000000) translate(0,15)">#1站用变交流空开跳闸</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_2122a50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4175.000000 -988.000000) translate(0,15)">#1站用变直流空开跳闸</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_27bf570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3406.000000 -1077.000000) translate(0,15)"> V</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_27bfa60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3406.000000 -1032.000000) translate(0,15)"> V</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_26c76d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3408.500000 -983.000000) translate(0,15)">V</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_26c7920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3406.000000 -930.000000) translate(0,15)"> V</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_26c7b30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3406.000000 -885.000000) translate(0,15)"> V</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_26c7d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3408.500000 -836.000000) translate(0,15)">V</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_23280b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2976.000000 -646.000000) translate(0,15)">1号站用变有功功率P</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_2328700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2976.000000 -597.000000) translate(0,15)">1号站用变无功功率Q</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_2751f70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2976.000000 -549.000000) translate(0,15)">1号站用变功率因素Cos</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_27061e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3408.500000 -698.000000) translate(0,15)">A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_2706420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3408.500000 -551.000000) translate(0,15)">Cos</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_2752190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2970.000000 -456.000000) translate(0,15)">2号站用变AB线电压Uab</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_1fcb150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3400.000000 -19.000000) translate(0,15)">KW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_1fcb360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2970.000000 -408.000000) translate(0,15)">2号站用变BC线电压Ubc</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_2743f10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3394.500000 22.000000) translate(0,15)">KVar</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_2744150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2970.000000 -358.000000) translate(0,15)">2号站用变CA线电压Uca</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_26e61a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2970.000000 -313.000000) translate(0,15)">2号站用变A相电压Ua</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_29b3200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2970.000000 -266.000000) translate(0,15)">2号站用变B相电压Ub</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_29b3850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2970.000000 -217.000000) translate(0,15)">2号站用变C相电压Uc</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_26acf10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2970.000000 -168.000000) translate(0,15)">2号站用变A相电流Ia</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_233d9c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3408.500000 -173.000000) translate(0,15)">A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_233dc10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2970.000000 -120.000000) translate(0,15)">2号站用变B相电流Ib</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_29495b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3408.500000 -119.000000) translate(0,15)">A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_29497c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2970.000000 -71.000000) translate(0,15)">2号站用变C相电流Ic</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_2b17cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3406.000000 -454.000000) translate(0,15)"> V</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_2b17ec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3406.000000 -409.000000) translate(0,15)"> V</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_2b180d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3408.500000 -360.000000) translate(0,15)">V</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_2b182e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3406.000000 -307.000000) translate(0,15)"> V</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_1fca260" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3406.000000 -262.000000) translate(0,15)"> V</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_1fca4a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3408.500000 -213.000000) translate(0,15)">V</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_1fca6e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2970.000000 -23.000000) translate(0,15)">2号站用变有功功率P</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_2741390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2970.000000 26.000000) translate(0,15)">2号站用变无功功率Q</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_27419e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2970.000000 74.000000) translate(0,15)">2号站用变功率因素Cos</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_2328d80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3408.500000 -75.000000) translate(0,15)">A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_2328f90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3408.500000 72.000000) translate(0,15)">Cos</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(205,51,51)" font-family="SimSun" font-size="18" graphid="g_2a5bd40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3150.000000 -503.000000) translate(0,15)">2号站用遥测量</text>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1"/>
</svg>