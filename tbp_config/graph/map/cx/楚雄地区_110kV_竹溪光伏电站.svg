<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-323" aopId="786686" id="thSvg" product="E8000V2" version="1.0" viewBox="-330 -1184 2228 1589">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="0" x2="12" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.742424" x1="2" x2="2" y1="11" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="26" x2="9" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="9" x2="9" y1="18" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="5" x2="5" y1="13" y2="5"/>
   </symbol>
   <symbol id="generator:shape4">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="9" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="1" y2="1"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape50">
    <polyline DF8003:Layer="PUBLIC" points="5,39 0,50 11,50 5,39 5,40 5,39 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.222222" x1="5" x2="5" y1="29" y2="33"/>
    <polyline DF8003:Layer="PUBLIC" points="5,25 0,14 11,14 5,25 5,24 5,25 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="59" y2="51"/>
   </symbol>
   <symbol id="lightningRod:shape177">
    <rect height="19" stroke-width="1" width="35" x="0" y="0"/>
    <text font-family="SimSun" font-size="15" graphid="g_1caa5b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 17.000000) translate(0,12)">SVG</text>
    <polyline points="17,19 17,30 " stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape155">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="59" x2="50" y1="40" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="60" x2="60" y1="34" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="34" y1="40" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="16" y1="40" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="17" x2="17" y1="48" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="24" x2="24" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.676705" x1="25" x2="43" y1="40" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="43" x2="43" y1="48" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="50" x2="50" y1="48" y2="31"/>
    <circle cx="28" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="32" cy="15" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="38" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="65" x2="65" y1="43" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="62" x2="62" y1="44" y2="36"/>
   </symbol>
   <symbol id="lightningRod:shape205">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="16,27 38,5 50,5 " stroke-width="1"/>
    <rect height="19" stroke-width="1" width="4" x="25" y="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="13" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="13" y1="2" y2="8"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="7" x2="15" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="15" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="7" x2="15" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="15" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape5_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="38" x2="13" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="38" x2="47" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="5" x2="14" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="14" y2="0"/>
   </symbol>
   <symbol id="switch2:shape5_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
   </symbol>
   <symbol id="switch2:shape5-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape5-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape40_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="45" x2="19" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="45" x2="19" y1="37" y2="37"/>
    <circle cx="32" cy="20" fillStyle="0" r="4.5" stroke-width="0.680952"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="44" x2="20" y1="9" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="18" y1="13" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="13" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="22" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="19" y2="19"/>
   </symbol>
   <symbol id="switch2:shape40_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="13" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="14" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="21" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="23" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="10" x2="18" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="45" x2="18" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="45" x2="18" y1="36" y2="36"/>
    <ellipse cx="32" cy="19" fillStyle="0" rx="4" ry="4.5" stroke-width="0.680952"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="37" y2="4"/>
   </symbol>
   <symbol id="switch2:shape40-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="45" x2="19" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="45" x2="19" y1="37" y2="37"/>
    <circle cx="32" cy="20" fillStyle="0" r="4.5" stroke-width="0.680952"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="44" x2="20" y1="9" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="18" y1="13" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="13" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="22" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="19" y2="19"/>
   </symbol>
   <symbol id="switch2:shape40-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="13" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="14" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="21" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="23" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="10" x2="18" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="45" x2="18" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="45" x2="18" y1="36" y2="36"/>
    <ellipse cx="32" cy="19" fillStyle="0" rx="4" ry="4.5" stroke-width="0.680952"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="37" y2="4"/>
   </symbol>
   <symbol id="transformer2:shape20_0">
    <circle cx="20" cy="16" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="47" x2="72" y1="39" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="78" x2="78" y1="43" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="75" x2="75" y1="44" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="73" x2="73" y1="34" y2="46"/>
    <polyline DF8003:Layer="PUBLIC" points="84,14 71,20 71,7 84,14 83,14 84,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="56" x2="98" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="13" y1="19" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="11" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="19" y1="15" y2="11"/>
   </symbol>
   <symbol id="transformer2:shape20_1">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="41,15 41,40 70,40 " stroke-width="1"/>
    <circle cx="42" cy="16" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="47" y1="15" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="47" y1="15" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="36" y1="15" y2="15"/>
   </symbol>
   <symbol id="transformer2:shape6_0">
    <ellipse cx="35" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="51" x2="56" y1="88" y2="88"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="56" x2="54" y1="88" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="56" x2="56" y1="85" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.911765" x1="1" x2="56" y1="57" y2="88"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="34" y1="73" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="34" x2="42" y1="65" y2="73"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="34" x2="34" y1="57" y2="65"/>
   </symbol>
   <symbol id="transformer2:shape6_1">
    <circle cx="35" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="27" x2="35" y1="33" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="35" x2="43" y1="25" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="35" x2="35" y1="17" y2="25"/>
   </symbol>
   <symbol id="voltageTransformer:shape86">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="15" x2="27" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="22" y1="15" y2="8"/>
    <rect height="16" stroke-width="1" width="9" x="18" y="16"/>
    <polyline points="28,19 16,28 16,32 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="22" x2="20" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="24" x2="18" y1="5" y2="5"/>
    <circle cx="8" cy="61" r="7.5" stroke-width="0.804311"/>
    <circle cx="21" cy="61" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="10" x2="6" y1="63" y2="64"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="10" x2="6" y1="60" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="6" x2="6" y1="59" y2="64"/>
    <circle cx="8" cy="48" r="7.5" stroke-width="0.804311"/>
    <circle cx="21" cy="48" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="24" x2="22" y1="52" y2="48"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="22" x2="24" y1="48" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.605383" x1="22" x2="17" y1="48" y2="48"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="24" x2="22" y1="66" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="22" x2="24" y1="62" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.605383" x1="22" x2="17" y1="62" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="11" x2="9" y1="52" y2="48"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="9" x2="11" y1="48" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.605383" x1="9" x2="4" y1="48" y2="48"/>
    <circle cx="8" cy="35" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="11" x2="9" y1="39" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="9" x2="11" y1="35" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.605383" x1="9" x2="4" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.29526" x1="23" x2="23" y1="31" y2="41"/>
   </symbol>
   <symbol id="voltageTransformer:shape71">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="53" x2="53" y1="12" y2="12"/>
    <ellipse cx="39" cy="19" fillStyle="0" rx="15" ry="12" stroke-width="0.862369"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.36031" x1="30" x2="34" y1="55" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.39762" x1="25" x2="21" y1="55" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.77579" x1="21" x2="35" y1="62" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="29" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="33" x2="41" y1="16" y2="20"/>
    <ellipse cx="16" cy="20" fillStyle="0" rx="14.5" ry="12" stroke-width="0.862369"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45294" x1="40" x2="41" y1="20" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="47" x2="41" y1="16" y2="20"/>
    <ellipse cx="28" cy="56" fillStyle="0" rx="14.5" ry="12" stroke-width="0.862369"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="53" x2="53" y1="32" y2="32"/>
    <ellipse cx="40" cy="39" fillStyle="0" rx="15" ry="12" stroke-width="0.862369"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="29" y1="32" y2="32"/>
    <ellipse cx="15" cy="40" fillStyle="0" rx="14.5" ry="12" stroke-width="0.862369"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45294" x1="15" x2="16" y1="20" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="22" x2="16" y1="16" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="9" x2="16" y1="16" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="33" x2="41" y1="36" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45294" x1="40" x2="41" y1="40" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="47" x2="41" y1="36" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45294" x1="15" x2="16" y1="40" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="21" x2="15" y1="36" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="9" x2="16" y1="36" y2="40"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1a491c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a49e30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1a91b80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1a92710" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1a93880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1a94390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a94dd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1a95710" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_136d860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_136d860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a98470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a98470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a9a170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a9a170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_1a9b190" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a9ce00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1a9da70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1a9e820" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1a9ef70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1aa6250" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1aa6dc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1aa7680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1aa7e40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1aa8f20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1aa0b20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1aa1660" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1aa2020" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1aa2c20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1aa3670" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1aa4810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1aa5430" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1b67610" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1b68050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1b62fe0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1b645c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1599" width="2238" x="-335" y="-1189"/>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c657e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1675.000000 186.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c65a50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1675.000000 170.600000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c65c90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1675.000000 155.200000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c65ed0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1681.000000 139.800000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c66110" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1667.000000 124.400000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c66350" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1681.000000 109.000000) translate(0,12)">F(HZ):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c66680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1181.000000 821.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c66900" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1181.000000 805.600000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c66b40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1181.000000 790.200000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c66d80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1187.000000 774.800000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c66fc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1173.000000 759.400000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c67200" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1187.000000 744.000000) translate(0,12)">F(HZ):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c67fb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1015.000000 592.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c68220" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1004.000000 577.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c68460" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1029.000000 562.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c68790" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 214.000000 -290.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c689f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 203.000000 -305.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c68c30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 228.000000 -320.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d36770" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 794.000000 919.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d36990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 783.000000 904.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d36b90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 808.000000 889.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d36ec0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1029.000000 200.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d37120" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1018.000000 185.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d37360" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1043.000000 170.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
  </g><g id="MultiLine_Layer">
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="733,217 776,217 776,252 " stroke="rgb(0,255,0)" stroke-width="1"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-302411">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 935.000000 -546.018519)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46649" ObjectName="SW-CX_ZhuXGF.CX_ZhuXGF_101BK"/>
     <cge:Meas_Ref ObjectId="302411"/>
    <cge:TPSR_Ref TObjectID="46649"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-302420">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 934.000000 -156.018519)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46656" ObjectName="SW-CX_ZhuXGF.CX_ZhuXGF_301BK"/>
     <cge:Meas_Ref ObjectId="302420"/>
    <cge:TPSR_Ref TObjectID="46656"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-302444">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 937.000000 48.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46672" ObjectName="SW-CX_ZhuXGF.CX_ZhuXGF_364BK"/>
     <cge:Meas_Ref ObjectId="302444"/>
    <cge:TPSR_Ref TObjectID="46672"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1603.000000 -861.018519)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-302427">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 300.000000 69.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46661" ObjectName="SW-CX_ZhuXGF.CX_ZhuXGF_361BK"/>
     <cge:Meas_Ref ObjectId="302427"/>
    <cge:TPSR_Ref TObjectID="46661"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-302432">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 501.000000 67.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46664" ObjectName="SW-CX_ZhuXGF.CX_ZhuXGF_362BK"/>
     <cge:Meas_Ref ObjectId="302432"/>
    <cge:TPSR_Ref TObjectID="46664"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1213.000000 70.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1454.000000 73.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1700.000000 76.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-302437">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 719.000000 65.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46667" ObjectName="SW-CX_ZhuXGF.CX_ZhuXGF_363BK"/>
     <cge:Meas_Ref ObjectId="302437"/>
    <cge:TPSR_Ref TObjectID="46667"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-302396">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 722.000000 -853.018519)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46640" ObjectName="SW-CX_ZhuXGF.CX_ZhuXGF_171BK"/>
     <cge:Meas_Ref ObjectId="302396"/>
    <cge:TPSR_Ref TObjectID="46640"/></metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1f69bc0">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 671.000000 -286.000000)" xlink:href="#voltageTransformer:shape86"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f34cd0">
    <use class="BV-110KV" transform="matrix(0.785714 -0.000000 0.000000 -0.913043 1014.000000 -889.000000)" xlink:href="#voltageTransformer:shape71"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_ZhuXGF.CX_ZhuXGF_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="207,-80 1798,-80 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="46638" ObjectName="BS-CX_ZhuXGF.CX_ZhuXGF_3IM"/>
    <cge:TPSR_Ref TObjectID="46638"/></metadata>
   <polyline fill="none" opacity="0" points="207,-80 1798,-80 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_ZhuXGF.CX_ZhuXGF_1IM">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="408,-716 1269,-716 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="46637" ObjectName="BS-CX_ZhuXGF.CX_ZhuXGF_1IM"/>
    <cge:TPSR_Ref TObjectID="46637"/></metadata>
   <polyline fill="none" opacity="0" points="408,-716 1269,-716 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1598.000000 -663.000000)" xlink:href="#transformer2:shape20_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1598.000000 -663.000000)" xlink:href="#transformer2:shape20_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 931.000000 182.000000)" xlink:href="#transformer2:shape20_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 931.000000 182.000000)" xlink:href="#transformer2:shape20_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_ZhuXGF.CX_ZhuXGF_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="39813"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 909.000000 -283.000000)" xlink:href="#transformer2:shape6_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 909.000000 -283.000000)" xlink:href="#transformer2:shape6_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="46675" ObjectName="TF-CX_ZhuXGF.CX_ZhuXGF_1T"/>
    <cge:TPSR_Ref TObjectID="46675"/></metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1a28080">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 881.000000 -269.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a54190">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 982.000000 -190.018519)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d1d660">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 689.000000 -245.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d13250">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 641.000000 -209.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a68320">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 261.000000 164.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e0aad0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 304.000000 170.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ac07f0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 681.000000 166.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1aab110">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 723.000000 192.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c511f0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 899.000000 150.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_163ba50">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 941.000000 157.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1caa2b0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 711.000000 297.000000)" xlink:href="#lightningRod:shape177"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b58460">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 461.000000 165.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a6db70">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 504.000000 171.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_16518c0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1173.000000 168.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d89230">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1216.000000 174.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c5ac60">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1414.000000 171.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c5ba10">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1457.000000 177.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a2ae10">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1660.000000 174.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a598e0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1703.000000 180.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e59240">
    <use class="BV-110KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 653.018519 -1103.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e489a0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 756.000000 -1024.000000)" xlink:href="#lightningRod:shape155"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d64ff0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1535.018519 -934.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d66d50">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1608.000000 -780.000000)" xlink:href="#lightningRod:shape205"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d67980">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1657.000000 -624.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -239.000000 -881.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-0" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 -206.461538 -748.966362) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-0" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 -206.461538 -707.966362) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="1" id="ME-302335" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1176.000000 -378.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="302335" ObjectName="CX_ZhuXGF:CX_ZhuXGF_1T_Tp"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="1" id="ME-302338" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1176.000000 -355.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="302338" ObjectName="CX_ZhuXGF:CX_ZhuXGF_1T_Tmp3"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="1" id="ME-302336" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1176.000000 -335.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="302336" ObjectName="CX_ZhuXGF:CX_ZhuXGF_1T_Tmp1"/>
    </metadata>
   </g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="46638" cx="648" cy="-80" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="46638" cx="310" cy="-80" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="46638" cx="510" cy="-80" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="46638" cx="729" cy="-80" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="46638" cx="946" cy="-80" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="46638" cx="1221" cy="-80" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="46638" cx="1463" cy="-80" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="46638" cx="1708" cy="-80" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="46638" cx="944" cy="-80" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="46637" cx="731" cy="-716" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="46637" cx="1046" cy="-716" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="46637" cx="944" cy="-716" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-302027" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -2.000000 -860.000000)" xlink:href="#dynamicPoint:shape34"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46607" ObjectName="DYN-CX_ZhuXGF"/>
     <cge:Meas_Ref ObjectId="302027"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ad4d70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ad4ee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -788.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ad4ee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -788.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ad4ee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -788.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ad4ee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -788.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ad4ee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -788.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ad4ee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -788.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ad4ee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -788.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_1db8b20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -200.000000 -929.500000) translate(0,16)">竹溪光伏电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a0bb70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1581.000000 -539.000000) translate(0,15)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a0c060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1738.000000 -822.000000) translate(0,15)">S11-315/10.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a0c060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1738.000000 -822.000000) translate(0,33)">10.5±2X2.5%/0.4kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a0c060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1738.000000 -822.000000) translate(0,51)">D,yn11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_15dc270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 546.000000 -243.000000) translate(0,15)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_15dc270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 546.000000 -243.000000) translate(0,33)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_15dc4e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1049.000000 -523.000000) translate(0,15)">1号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_15dc4e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1049.000000 -523.000000) translate(0,33)">SZ18-40000/115</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_15dc4e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1049.000000 -523.000000) translate(0,51)">115±8х1.25%/37/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_15dc4e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1049.000000 -523.000000) translate(0,69)">YN,yn0+d,Ud%=10.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e7cf50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 902.000000 306.000000) translate(0,15)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e7d1c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 954.000000 -185.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1ab4ec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 663.000000 316.000000) translate(0,15)">1号动态无功补偿装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1ab4ec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 663.000000 316.000000) translate(0,33)">      38MVar</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1ab53e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 878.000000 333.000000) translate(0,15)">SCB13-400/35GY</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1ab53e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 878.000000 333.000000) translate(0,33)">37±2X2.5%/0.4kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1ab53e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 878.000000 333.000000) translate(0,51)">D,YN11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1ab53e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 878.000000 333.000000) translate(0,69)">Ud=4%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d696e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1544.000000 -899.000000) translate(0,15)">竹</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d696e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1544.000000 -899.000000) translate(0,33)">溪</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d696e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1544.000000 -899.000000) translate(0,51)">光</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d696e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1544.000000 -899.000000) translate(0,69)">伏</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d696e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1544.000000 -899.000000) translate(0,87)">支</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d696e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1544.000000 -899.000000) translate(0,105)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1c9b0a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1552.000000 -1067.000000) translate(0,15)">10kV安乐村支线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="15" graphid="g_1c9c9c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 408.000000 -736.000000) translate(0,12)">I段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="15" graphid="g_1c9ce50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 738.000000 -777.000000) translate(0,12)">1711</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="15" graphid="g_1c9d090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 676.000000 -846.000000) translate(0,12)">17117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="15" graphid="g_1c9d2d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 740.000000 -882.000000) translate(0,12)">171</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="15" graphid="g_1c9d510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 674.000000 -961.000000) translate(0,12)">17160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="15" graphid="g_1c9d750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 738.000000 -994.000000) translate(0,12)">1716</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="15" graphid="g_1c9d990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 673.000000 -1072.000000) translate(0,12)">17167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="15" graphid="g_1c9dbd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 754.000000 -1142.000000) translate(0,12)">苍竹线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="15" graphid="g_1c9e400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1053.000000 -813.000000) translate(0,12)">1901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="15" graphid="g_1c9e680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 988.000000 -778.000000) translate(0,12)">19010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="15" graphid="g_1c9e8c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 990.000000 -885.000000) translate(0,12)">19017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="15" graphid="g_1c9eb00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 953.000000 -575.000000) translate(0,12)">101</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="15" graphid="g_1c9ed40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 950.000000 -686.000000) translate(0,12)">1011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="15" graphid="g_1c9f660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 888.000000 -654.000000) translate(0,12)">10117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="15" graphid="g_1c9f9f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 885.000000 -538.000000) translate(0,12)">10160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="15" graphid="g_1c9fc30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 951.000000 -469.000000) translate(0,12)">1016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="15" graphid="g_1c9fe70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 882.000000 -433.000000) translate(0,12)">10167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="15" graphid="g_1ca00b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 786.000000 -308.000000) translate(0,12)">1010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="15" graphid="g_1ca02f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 968.000000 -136.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="15" graphid="g_1ca0530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 670.000000 -124.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="15" graphid="g_1ca0770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 318.000000 39.000000) translate(0,12)">361</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="15" graphid="g_1ca09b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 339.000000 -31.000000) translate(0,12)">3611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="15" graphid="g_1ca0bf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 519.000000 37.000000) translate(0,12)">362</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="15" graphid="g_1ca0e30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 528.000000 -34.000000) translate(0,12)">3621</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="15" graphid="g_1ca1070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 737.000000 35.000000) translate(0,12)">363</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="15" graphid="g_1ca12b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 751.000000 -34.000000) translate(0,12)">3631</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="15" graphid="g_1ca14f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 681.000000 209.000000) translate(0,12)">3636</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="15" graphid="g_1ca1730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 759.000000 267.000000) translate(0,12)">36367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="15" graphid="g_1c42ea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 956.000000 19.000000) translate(0,12)">364</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="15" graphid="g_1c43350" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 967.000000 -30.000000) translate(0,12)">3641</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1c43590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 267.000000 232.000000) translate(0,15)">   1号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1c43590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 267.000000 232.000000) translate(0,33)">  1~7号升压变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1c43590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 267.000000 232.000000) translate(0,51)">(2.1MW+6*3.3MW)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="15" graphid="g_1c44120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 207.000000 -100.000000) translate(0,12)">IM段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1c44370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 457.000000 235.000000) translate(0,15)">   2号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1c44370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 457.000000 235.000000) translate(0,33)">  8~14号升压变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1c44370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 457.000000 235.000000) translate(0,51)">(2*2.1MW+5*3.3MW)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1c44630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1167.000000 243.000000) translate(0,15)">预留储能回路</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1c44860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1431.000000 245.000000) translate(0,15)">备用一</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1c44aa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1679.000000 247.000000) translate(0,15)">备用二</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="15" graphid="g_1c44ce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1240.000000 43.000000) translate(0,12)">365</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="15" graphid="g_1c44f20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1475.000000 47.000000) translate(0,12)">366</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="15" graphid="g_1c45160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1724.000000 50.000000) translate(0,12)">367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="15" graphid="g_1c453a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1629.000000 -888.000000) translate(0,12)">A01</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="15" graphid="g_1c455e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1622.000000 -987.000000) translate(0,12)">A011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="15" graphid="g_1c45820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1625.000000 -835.000000) translate(0,12)">A012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1c45a60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1572.000000 -1029.000000) translate(0,15)">05杆</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d927b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 593.000000 -170.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d92ca0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 885.000000 -181.000000) translate(0,12)">30117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d92ee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 255.000000 19.000000) translate(0,12)">36117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d93120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 450.000000 22.000000) translate(0,12)">36217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d93360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 668.000000 21.000000) translate(0,12)">36317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d935a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 884.000000 13.000000) translate(0,12)">36417</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1c6c4c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1310.000000 -1050.000000) translate(0,15)"> 至35kV罗川变电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1c6c4c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1310.000000 -1050.000000) translate(0,33)">10kV竹溪线485断路器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="15" graphid="g_1d35790" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1249.000000 -28.000000) translate(0,12)">3651</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="15" graphid="g_1d35c40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1488.000000 -27.000000) translate(0,12)">3661</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="15" graphid="g_1d35e80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1730.000000 -23.000000) translate(0,12)">3671</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d37b40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1130.000000 -382.000000) translate(0,12)">档位：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d37d80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1085.000000 -358.000000) translate(0,12)">绕温（℃）：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d38f70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1078.000000 -336.000000) translate(0,12)">油温1（℃）：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d3a1a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -323.000000 -51.000000) translate(0,17)">联系方式：4801697</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1d3d0a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 993.000000 -376.000000) translate(0,16)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_1d3d6d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -802.000000) translate(0,16)">AGC/AVC</text>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(170,85,127)" stroke-width="1" x1="846" x2="863" y1="-262" y2="-262"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(170,85,127)" stroke-width="1" x1="863" x2="863" y1="-262" y2="-284"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(170,85,127)" stroke-width="0.222222" x1="863" x2="863" y1="-299" y2="-303"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(170,85,127)" stroke-width="1" x1="863" x2="863" y1="-341" y2="-320"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.520408" x1="978" x2="978" y1="-334" y2="-317"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.520408" x1="993" x2="978" y1="-325" y2="-317"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.520408" x1="993" x2="978" y1="-325" y2="-334"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-302412">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 929.000000 -642.018519)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46650" ObjectName="SW-CX_ZhuXGF.CX_ZhuXGF_1011SW"/>
     <cge:Meas_Ref ObjectId="302412"/>
    <cge:TPSR_Ref TObjectID="46650"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-302414">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 898.000000 -602.018519)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46652" ObjectName="SW-CX_ZhuXGF.CX_ZhuXGF_10117SW"/>
     <cge:Meas_Ref ObjectId="302414"/>
    <cge:TPSR_Ref TObjectID="46652"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-302442">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 713.000000 257.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46670" ObjectName="SW-CX_ZhuXGF.CX_ZhuXGF_3636SW"/>
     <cge:Meas_Ref ObjectId="302442"/>
    <cge:TPSR_Ref TObjectID="46670"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-302443">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 752.000000 264.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46671" ObjectName="SW-CX_ZhuXGF.CX_ZhuXGF_36367SW"/>
     <cge:Meas_Ref ObjectId="302443"/>
    <cge:TPSR_Ref TObjectID="46671"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-302419">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 815.000000 -256.018519)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46655" ObjectName="SW-CX_ZhuXGF.CX_ZhuXGF_1010SW"/>
     <cge:Meas_Ref ObjectId="302419"/>
    <cge:TPSR_Ref TObjectID="46655"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-302415">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 896.000000 -486.018519)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46653" ObjectName="SW-CX_ZhuXGF.CX_ZhuXGF_10160SW"/>
     <cge:Meas_Ref ObjectId="302415"/>
    <cge:TPSR_Ref TObjectID="46653"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-302413">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 929.000000 -422.018519)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46651" ObjectName="SW-CX_ZhuXGF.CX_ZhuXGF_1016SW"/>
     <cge:Meas_Ref ObjectId="302413"/>
    <cge:TPSR_Ref TObjectID="46651"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-302416">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 893.000000 -381.018519)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46654" ObjectName="SW-CX_ZhuXGF.CX_ZhuXGF_10167SW"/>
     <cge:Meas_Ref ObjectId="302416"/>
    <cge:TPSR_Ref TObjectID="46654"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1597.000000 -942.018519)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-302428">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 279.000000 -5.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46662" ObjectName="SW-CX_ZhuXGF.CX_ZhuXGF_3611SW"/>
     <cge:Meas_Ref ObjectId="302428"/>
    <cge:TPSR_Ref TObjectID="46662"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-302433">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 478.000000 -4.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46665" ObjectName="SW-CX_ZhuXGF.CX_ZhuXGF_3621SW"/>
     <cge:Meas_Ref ObjectId="302433"/>
    <cge:TPSR_Ref TObjectID="46665"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1190.000000 -1.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1431.000000 2.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1677.000000 5.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-302425">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 616.000000 -97.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46659" ObjectName="SW-CX_ZhuXGF.CX_ZhuXGF_3901SW"/>
     <cge:Meas_Ref ObjectId="302425"/>
    <cge:TPSR_Ref TObjectID="46659"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-302438">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 696.000000 -6.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46668" ObjectName="SW-CX_ZhuXGF.CX_ZhuXGF_3631SW"/>
     <cge:Meas_Ref ObjectId="302438"/>
    <cge:TPSR_Ref TObjectID="46668"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-302445">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 914.000000 -2.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46673" ObjectName="SW-CX_ZhuXGF.CX_ZhuXGF_3641SW"/>
     <cge:Meas_Ref ObjectId="302445"/>
    <cge:TPSR_Ref TObjectID="46673"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-302421">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 912.000000 -109.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46657" ObjectName="SW-CX_ZhuXGF.CX_ZhuXGF_3011SW"/>
     <cge:Meas_Ref ObjectId="302421"/>
    <cge:TPSR_Ref TObjectID="46657"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-302400">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 685.000000 -909.018519)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46644" ObjectName="SW-CX_ZhuXGF.CX_ZhuXGF_17160SW"/>
     <cge:Meas_Ref ObjectId="302400"/>
    <cge:TPSR_Ref TObjectID="46644"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-302399">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 686.000000 -794.018519)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46643" ObjectName="SW-CX_ZhuXGF.CX_ZhuXGF_17117SW"/>
     <cge:Meas_Ref ObjectId="302399"/>
    <cge:TPSR_Ref TObjectID="46643"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-302397">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 716.000000 -730.018519)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46641" ObjectName="SW-CX_ZhuXGF.CX_ZhuXGF_1711SW"/>
     <cge:Meas_Ref ObjectId="302397"/>
    <cge:TPSR_Ref TObjectID="46641"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-302398">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 716.000000 -947.018519)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46642" ObjectName="SW-CX_ZhuXGF.CX_ZhuXGF_1716SW"/>
     <cge:Meas_Ref ObjectId="302398"/>
    <cge:TPSR_Ref TObjectID="46642"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-302401">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 684.000000 -1020.018519)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46645" ObjectName="SW-CX_ZhuXGF.CX_ZhuXGF_17167SW"/>
     <cge:Meas_Ref ObjectId="302401"/>
    <cge:TPSR_Ref TObjectID="46645"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-302410">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1001.000000 -833.018519)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46648" ObjectName="SW-CX_ZhuXGF.CX_ZhuXGF_19017SW"/>
     <cge:Meas_Ref ObjectId="302410"/>
    <cge:TPSR_Ref TObjectID="46648"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-302408">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1031.000000 -766.018519)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46646" ObjectName="SW-CX_ZhuXGF.CX_ZhuXGF_1901SW"/>
     <cge:Meas_Ref ObjectId="302408"/>
    <cge:TPSR_Ref TObjectID="46646"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-302409">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 999.000000 -726.018519)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46647" ObjectName="SW-CX_ZhuXGF.CX_ZhuXGF_19010SW"/>
     <cge:Meas_Ref ObjectId="302409"/>
    <cge:TPSR_Ref TObjectID="46647"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-302429">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 268.000000 35.981481)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46663" ObjectName="SW-CX_ZhuXGF.CX_ZhuXGF_36117SW"/>
     <cge:Meas_Ref ObjectId="302429"/>
    <cge:TPSR_Ref TObjectID="46663"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-302434">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 463.000000 39.981481)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46666" ObjectName="SW-CX_ZhuXGF.CX_ZhuXGF_36217SW"/>
     <cge:Meas_Ref ObjectId="302434"/>
    <cge:TPSR_Ref TObjectID="46666"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-302439">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 685.000000 37.981481)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46669" ObjectName="SW-CX_ZhuXGF.CX_ZhuXGF_36317SW"/>
     <cge:Meas_Ref ObjectId="302439"/>
    <cge:TPSR_Ref TObjectID="46669"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-302446">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 899.000000 29.981481)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46674" ObjectName="SW-CX_ZhuXGF.CX_ZhuXGF_36417SW"/>
     <cge:Meas_Ref ObjectId="302446"/>
    <cge:TPSR_Ref TObjectID="46674"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-302422">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 894.000000 -129.018519)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46658" ObjectName="SW-CX_ZhuXGF.CX_ZhuXGF_30117SW"/>
     <cge:Meas_Ref ObjectId="302422"/>
    <cge:TPSR_Ref TObjectID="46658"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-302426">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 604.000000 -118.018519)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46660" ObjectName="SW-CX_ZhuXGF.CX_ZhuXGF_39017SW"/>
     <cge:Meas_Ref ObjectId="302426"/>
    <cge:TPSR_Ref TObjectID="46660"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1598.000000 -790.018519)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_CL" endPointId="0" endStationName="CX_ZhuXGF" flowDrawDirect="1" flowShape="0" id="AC-110kV.cangzhu_line" runFlow="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="731,-1128 731,-1182 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="30215" ObjectName="AC-110kV.cangzhu_line"/>
    <cge:TPSR_Ref TObjectID="30215_SS-323"/></metadata>
   <polyline fill="none" opacity="0" points="731,-1128 731,-1182 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Link_Layer">
   <g class="BV-110KV" id="g_1abb290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="888,-341 888,-327 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="46655@x" ObjectIDZND0="g_1a28080@0" Pin0InfoVect0LinkObjId="g_1a28080_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-302419_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="888,-341 888,-327 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1abb4f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="888,-341 943,-341 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" ObjectIDND0="g_1a28080@0" ObjectIDND1="46655@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1a28080_0" Pin1InfoVect1LinkObjId="SW-302419_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="888,-341 943,-341 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1c5c430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="889,-628 879,-628 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="46652@0" ObjectIDZND0="g_1ca8fc0@0" Pin0InfoVect0LinkObjId="g_1ca8fc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-302414_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="889,-628 879,-628 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a680c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="650,-204 650,-216 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_1d13250@0" ObjectIDZND0="g_1f69bc0@0" Pin0InfoVect0LinkObjId="g_1f69bc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d13250_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="650,-204 650,-216 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_161fcc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="309,165 309,199 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="generator" ObjectIDND0="g_1e0aad0@1" ObjectIDZND0="47287@0" Pin0InfoVect0LinkObjId="SM-CX_ZhuXGF.P1_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e0aad0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="309,165 309,199 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a0b910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="944,-114 944,-80 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="46657@0" ObjectIDZND0="46638@0" Pin0InfoVect0LinkObjId="g_1a56de0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-302421_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="944,-114 944,-80 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1e7d400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="944,-581 944,-628 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="46649@1" ObjectIDZND0="46652@x" ObjectIDZND1="46650@x" Pin0InfoVect0LinkObjId="SW-302414_0" Pin0InfoVect1LinkObjId="SW-302412_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-302411_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="944,-581 944,-628 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1e7d5f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="925,-628 944,-628 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="46652@1" ObjectIDZND0="46649@x" ObjectIDZND1="46650@x" Pin0InfoVect0LinkObjId="SW-302411_0" Pin0InfoVect1LinkObjId="SW-302412_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-302414_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="925,-628 944,-628 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1e7d7e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="944,-628 944,-664 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="46652@x" ObjectIDND1="46649@x" ObjectIDZND0="46650@0" Pin0InfoVect0LinkObjId="SW-302412_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-302414_0" Pin1InfoVect1LinkObjId="SW-302411_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="944,-628 944,-664 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b5bc00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="757,257 728,257 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="46671@0" ObjectIDZND0="46670@x" ObjectIDZND1="g_1caa2b0@0" Pin0InfoVect0LinkObjId="SW-302442_0" Pin0InfoVect1LinkObjId="g_1caa2b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-302443_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="757,257 728,257 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b5be60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="728,234 728,257 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="46670@0" ObjectIDZND0="46671@x" ObjectIDZND1="g_1caa2b0@0" Pin0InfoVect0LinkObjId="SW-302443_0" Pin0InfoVect1LinkObjId="g_1caa2b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-302442_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="728,234 728,257 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a90a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="728,257 728,267 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="46670@x" ObjectIDND1="46671@x" ObjectIDZND0="g_1caa2b0@0" Pin0InfoVect0LinkObjId="g_1caa2b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-302442_0" Pin1InfoVect1LinkObjId="SW-302443_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="728,257 728,267 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1df3760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="268,105 268,86 309,86 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="lightningRod" ObjectIDND0="g_1a68320@0" ObjectIDZND0="46661@x" ObjectIDZND1="g_1e0aad0@0" Pin0InfoVect0LinkObjId="SW-302427_0" Pin0InfoVect1LinkObjId="g_1e0aad0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a68320_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="268,105 268,86 309,86 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1df39c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="309,60 309,86 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="46661@0" ObjectIDZND0="g_1a68320@0" ObjectIDZND1="g_1e0aad0@0" Pin0InfoVect0LinkObjId="g_1a68320_0" Pin0InfoVect1LinkObjId="g_1e0aad0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-302427_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="309,60 309,86 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1df3c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="946,152 946,218 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_163ba50@1" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_163ba50_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="946,152 946,218 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1e54c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="846,-248 846,-262 830,-262 830,-279 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1db6920@0" ObjectIDZND0="46655@0" Pin0InfoVect0LinkObjId="SW-302419_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1db6920_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="846,-248 846,-262 830,-262 830,-279 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1e54ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="830,-314 830,-341 888,-341 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="46655@1" ObjectIDZND0="g_1a28080@0" Pin0InfoVect0LinkObjId="g_1a28080_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-302419_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="830,-314 830,-341 888,-341 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a56090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="650,-173 650,-151 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1d13250@1" ObjectIDZND0="g_1d1d660@0" ObjectIDZND1="46660@x" ObjectIDZND2="46659@x" Pin0InfoVect0LinkObjId="g_1d1d660_0" Pin0InfoVect1LinkObjId="SW-302426_0" Pin0InfoVect2LinkObjId="SW-302425_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d13250_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="650,-173 650,-151 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a562f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="650,-151 696,-151 696,-186 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_1d13250@0" ObjectIDND1="46660@x" ObjectIDND2="46659@x" ObjectIDZND0="g_1d1d660@0" Pin0InfoVect0LinkObjId="g_1d1d660_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1d13250_0" Pin1InfoVect1LinkObjId="SW-302426_0" Pin1InfoVect2LinkObjId="SW-302425_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="650,-151 696,-151 696,-186 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a56de0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="648,-102 648,-80 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="46659@0" ObjectIDZND0="46638@0" Pin0InfoVect0LinkObjId="g_1a0b910_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-302425_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="648,-102 648,-80 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1a60ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="877,-512 884,-510 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2094e20@0" ObjectIDZND0="46653@0" Pin0InfoVect0LinkObjId="SW-302415_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2094e20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="877,-512 884,-510 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1e7bad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="926,-513 945,-513 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="46653@1" ObjectIDZND0="46649@x" ObjectIDZND1="46651@x" Pin0InfoVect0LinkObjId="SW-302411_0" Pin0InfoVect1LinkObjId="SW-302413_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-302415_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="926,-513 945,-513 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1e7c5c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="944,-513 944,-554 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="46653@x" ObjectIDND1="46651@x" ObjectIDZND0="46649@0" Pin0InfoVect0LinkObjId="SW-302411_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-302415_0" Pin1InfoVect1LinkObjId="SW-302413_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="944,-513 944,-554 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1ceb370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="944,-513 944,-478 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="46653@x" ObjectIDND1="46649@x" ObjectIDZND0="46651@1" Pin0InfoVect0LinkObjId="SW-302413_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-302415_0" Pin1InfoVect1LinkObjId="SW-302411_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="944,-513 944,-478 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1ceb5d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="878,-407 884,-407 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1d59000@0" ObjectIDZND0="46654@0" Pin0InfoVect0LinkObjId="SW-302416_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d59000_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="878,-407 884,-407 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1c49b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="925,-409 944,-409 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="transformer2" ObjectIDND0="46654@1" ObjectIDZND0="46651@x" ObjectIDZND1="46675@x" Pin0InfoVect0LinkObjId="SW-302413_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-302416_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="925,-409 944,-409 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1c4a650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="944,-365 944,-409 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="46675@0" ObjectIDZND0="46651@x" ObjectIDZND1="46654@x" Pin0InfoVect0LinkObjId="SW-302413_0" Pin0InfoVect1LinkObjId="SW-302416_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="944,-365 944,-409 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1c4a8b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="944,-409 944,-445 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer2" EndDevType0="switch" ObjectIDND0="46654@x" ObjectIDND1="46675@x" ObjectIDZND0="46651@0" Pin0InfoVect0LinkObjId="SW-302413_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-302416_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="944,-409 944,-445 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1ab4c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1613,-1004 1613,-1038 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="generator" ObjectIDND0="0@1" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1613,-1004 1613,-1038 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b58200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="309,112 309,86 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="g_1e0aad0@0" ObjectIDZND0="g_1a68320@0" ObjectIDZND1="46661@x" Pin0InfoVect0LinkObjId="g_1a68320_0" Pin0InfoVect1LinkObjId="SW-302427_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e0aad0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="309,112 309,86 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a6e850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="509,166 509,200 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="generator" ObjectIDND0="g_1a6db70@1" ObjectIDZND0="47288@0" Pin0InfoVect0LinkObjId="SM-CX_ZhuXGF.P2_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a6db70_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="509,166 509,200 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a6eab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="468,106 468,87 509,87 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="lightningRod" ObjectIDND0="g_1b58460@0" ObjectIDZND0="46664@x" ObjectIDZND1="g_1a6db70@0" Pin0InfoVect0LinkObjId="SW-302432_0" Pin0InfoVect1LinkObjId="g_1a6db70_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b58460_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="468,106 468,87 509,87 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a6ed10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="509,61 509,87 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="46664@0" ObjectIDZND0="g_1b58460@0" ObjectIDZND1="g_1a6db70@0" Pin0InfoVect0LinkObjId="g_1b58460_0" Pin0InfoVect1LinkObjId="g_1a6db70_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-302432_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="509,61 509,87 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1650dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="509,113 509,87 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="lightningRod" ObjectIDND0="g_1a6db70@0" ObjectIDZND0="46664@x" ObjectIDZND1="g_1b58460@0" Pin0InfoVect0LinkObjId="SW-302432_0" Pin0InfoVect1LinkObjId="g_1b58460_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a6db70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="509,113 509,87 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1d89f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1221,169 1221,203 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="generator" ObjectIDND0="g_1d89230@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d89230_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1221,169 1221,203 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1d8a1e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1180,109 1180,90 1221,90 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="lightningRod" ObjectIDND0="g_16518c0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_1d89230@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_1d89230_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_16518c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1180,109 1180,90 1221,90 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1d8a440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1221,64 1221,90 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_16518c0@0" ObjectIDZND1="g_1d89230@0" Pin0InfoVect0LinkObjId="g_16518c0_0" Pin0InfoVect1LinkObjId="g_1d89230_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1221,64 1221,90 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1c59f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1222,-6 1222,34 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1222,-6 1222,34 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1c5a170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1221,116 1221,90 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="g_1d89230@0" ObjectIDZND0="g_16518c0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_16518c0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d89230_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1221,116 1221,90 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_19b81b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1462,172 1462,206 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="generator" ObjectIDND0="g_1c5ba10@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c5ba10_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1462,172 1462,206 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_19b8410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1421,112 1421,93 1462,93 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="lightningRod" ObjectIDND0="g_1c5ac60@0" ObjectIDZND0="0@x" ObjectIDZND1="g_1c5ba10@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_1c5ba10_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c5ac60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1421,112 1421,93 1462,93 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_19b8670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1462,67 1462,93 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_1c5ac60@0" ObjectIDZND1="g_1c5ba10@0" Pin0InfoVect0LinkObjId="g_1c5ac60_0" Pin0InfoVect1LinkObjId="g_1c5ba10_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1462,67 1462,93 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1a2a0c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1463,-3 1463,37 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1463,-3 1463,37 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1a2a320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1462,119 1462,93 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="g_1c5ba10@0" ObjectIDZND0="g_1c5ac60@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_1c5ac60_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c5ba10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1462,119 1462,93 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1a5a630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1708,175 1708,209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="generator" ObjectIDND0="g_1a598e0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a598e0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1708,175 1708,209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1a5a890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1667,115 1667,96 1708,96 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="lightningRod" ObjectIDND0="g_1a2ae10@0" ObjectIDZND0="0@x" ObjectIDZND1="g_1a598e0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_1a598e0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a2ae10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1667,115 1667,96 1708,96 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1a5aaf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1708,70 1708,96 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_1a2ae10@0" ObjectIDZND1="g_1a598e0@0" Pin0InfoVect0LinkObjId="g_1a2ae10_0" Pin0InfoVect1LinkObjId="g_1a598e0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1708,70 1708,96 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1ebea10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1709,0 1709,40 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1709,0 1709,40 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1ebec70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1708,122 1708,96 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="lightningRod" ObjectIDND0="g_1a598e0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_1a2ae10@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_1a2ae10_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a598e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1708,122 1708,96 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d6c070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="310,-80 310,-45 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="46638@0" ObjectIDZND0="46662@1" Pin0InfoVect0LinkObjId="SW-302428_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a0b910_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="310,-80 310,-45 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d6c8a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="510,-80 510,-46 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="46638@0" ObjectIDZND0="46665@1" Pin0InfoVect0LinkObjId="SW-302433_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a0b910_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="510,-80 510,-46 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d6d0d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="729,-80 729,-43 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="46638@0" ObjectIDZND0="46668@1" Pin0InfoVect0LinkObjId="SW-302438_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a0b910_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="729,-80 729,-43 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d6d900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="946,-80 946,-43 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="46638@0" ObjectIDZND0="46673@1" Pin0InfoVect0LinkObjId="SW-302445_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a0b910_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="946,-80 946,-43 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f68380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1221,-80 1221,-40 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="46638@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a0b910_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1221,-80 1221,-40 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f68b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1463,-81 1463,-35 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="46638@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a0b910_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1463,-81 1463,-35 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f69390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1708,-80 1709,-32 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="46638@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a0b910_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1708,-80 1709,-32 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1d61750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="676,-935 666,-935 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="46644@0" ObjectIDZND0="g_1d5b140@0" Pin0InfoVect0LinkObjId="g_1d5b140_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-302400_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="676,-935 666,-935 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1d5bbd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="670,-819 678,-821 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1f6cca0@0" ObjectIDZND0="46643@0" Pin0InfoVect0LinkObjId="SW-302399_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f6cca0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="670,-819 678,-821 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1f6d730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="713,-820 732,-820 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="46643@1" ObjectIDZND0="46640@x" ObjectIDZND1="46641@x" Pin0InfoVect0LinkObjId="SW-302396_0" Pin0InfoVect1LinkObjId="SW-302397_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-302399_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="713,-820 732,-820 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1f6d990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="731,-820 731,-861 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="46641@x" ObjectIDND1="46643@x" ObjectIDZND0="46640@0" Pin0InfoVect0LinkObjId="SW-302396_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-302397_0" Pin1InfoVect1LinkObjId="SW-302399_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="731,-820 731,-861 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1f704b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="731,-820 731,-785 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="46640@x" ObjectIDND1="46643@x" ObjectIDZND0="46641@1" Pin0InfoVect0LinkObjId="SW-302397_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-302396_0" Pin1InfoVect1LinkObjId="SW-302399_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="731,-820 731,-785 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1eeafa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="731,-716 731,-752 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="46637@0" ObjectIDZND0="46641@0" Pin0InfoVect0LinkObjId="SW-302397_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f32540_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="731,-716 731,-752 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1eed8e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="731,-935 731,-969 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="46644@x" ObjectIDND1="46640@x" ObjectIDZND0="46642@0" Pin0InfoVect0LinkObjId="SW-302398_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-302400_0" Pin1InfoVect1LinkObjId="SW-302396_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="731,-935 731,-969 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1eee3d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="712,-935 731,-935 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="46644@1" ObjectIDZND0="46642@x" ObjectIDZND1="46640@x" Pin0InfoVect0LinkObjId="SW-302398_0" Pin0InfoVect1LinkObjId="SW-302396_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-302400_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="712,-935 731,-935 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1eee630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="731,-935 731,-888 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="46642@x" ObjectIDND1="46644@x" ObjectIDZND0="46640@1" Pin0InfoVect0LinkObjId="SW-302396_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-302398_0" Pin1InfoVect1LinkObjId="SW-302400_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="731,-935 731,-888 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1eee890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="675,-1046 665,-1046 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="46645@0" ObjectIDZND0="g_1e57a60@0" Pin0InfoVect0LinkObjId="g_1e57a60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-302401_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="675,-1046 665,-1046 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1e58d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="711,-1046 731,-1046 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="46645@1" ObjectIDZND0="46642@x" ObjectIDZND1="g_1e59240@0" ObjectIDZND2="30215@1" Pin0InfoVect0LinkObjId="SW-302398_0" Pin0InfoVect1LinkObjId="g_1e59240_0" Pin0InfoVect2LinkObjId="g_1e5aae0_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-302401_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="711,-1046 731,-1046 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1e58fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="731,-1046 731,-1005 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" ObjectIDND0="46645@x" ObjectIDND1="g_1e59240@0" ObjectIDND2="30215@1" ObjectIDZND0="46642@1" Pin0InfoVect0LinkObjId="SW-302398_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-302401_0" Pin1InfoVect1LinkObjId="g_1e59240_0" Pin1InfoVect2LinkObjId="g_1e5aae0_1" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="731,-1046 731,-1005 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1e59ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="712,-1095 731,-1095 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_1e59240@0" ObjectIDZND0="46645@x" ObjectIDZND1="46642@x" ObjectIDZND2="g_1e489a0@0" Pin0InfoVect0LinkObjId="SW-302401_0" Pin0InfoVect1LinkObjId="SW-302398_0" Pin0InfoVect2LinkObjId="g_1e489a0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e59240_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="712,-1095 731,-1095 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1e5aae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="731,-1095 731,-1129 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_1e59240@0" ObjectIDND1="46645@x" ObjectIDND2="46642@x" ObjectIDZND0="30215@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1e59240_0" Pin1InfoVect1LinkObjId="SW-302401_0" Pin1InfoVect2LinkObjId="SW-302398_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="731,-1095 731,-1129 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1e5b310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="982,-857 989,-857 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1cdc560@0" ObjectIDZND0="46648@0" Pin0InfoVect0LinkObjId="SW-302410_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1cdc560_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="982,-857 989,-857 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1f2eed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="990,-752 980,-752 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="46647@0" ObjectIDZND0="g_1f31ab0@0" Pin0InfoVect0LinkObjId="g_1f31ab0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-302409_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="990,-752 980,-752 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1f32540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1026,-752 1046,-752 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" EndDevType1="switch" ObjectIDND0="46647@1" ObjectIDZND0="46637@0" ObjectIDZND1="46646@x" Pin0InfoVect0LinkObjId="g_1c9ef80_0" Pin0InfoVect1LinkObjId="SW-302408_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-302409_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1026,-752 1046,-752 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f327a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1046,-741 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1046,-741 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1f33290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1046,-716 1046,-752 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="46637@0" ObjectIDZND0="46647@x" ObjectIDZND1="46646@x" Pin0InfoVect0LinkObjId="SW-302409_0" Pin0InfoVect1LinkObjId="SW-302408_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f32540_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1046,-716 1046,-752 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1f334f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1046,-752 1046,-792 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="busSection" EndDevType0="switch" ObjectIDND0="46647@x" ObjectIDND1="46637@0" ObjectIDZND0="46646@0" Pin0InfoVect0LinkObjId="SW-302408_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-302409_0" Pin1InfoVect1LinkObjId="g_1f32540_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1046,-752 1046,-792 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1f33d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1046,-859 1046,-895 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" ObjectIDND0="46646@x" ObjectIDND1="46648@x" ObjectIDZND0="g_1f34cd0@0" Pin0InfoVect0LinkObjId="g_1f34cd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-302408_0" Pin1InfoVect1LinkObjId="SW-302410_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1046,-859 1046,-895 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1f34810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1028,-859 1046,-859 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" ObjectIDND0="46648@1" ObjectIDZND0="46646@x" ObjectIDZND1="g_1f34cd0@0" Pin0InfoVect0LinkObjId="SW-302408_0" Pin0InfoVect1LinkObjId="g_1f34cd0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-302410_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1028,-859 1046,-859 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1f34a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1046,-859 1046,-824 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_1f34cd0@0" ObjectIDND1="46648@x" ObjectIDZND0="46646@1" Pin0InfoVect0LinkObjId="SW-302408_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1f34cd0_0" Pin1InfoVect1LinkObjId="SW-302410_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1046,-859 1046,-824 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1e4a380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="731,-1064 761,-1064 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="46645@x" ObjectIDND1="46642@x" ObjectIDND2="g_1e59240@0" ObjectIDZND0="g_1e489a0@0" Pin0InfoVect0LinkObjId="g_1e489a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-302401_0" Pin1InfoVect1LinkObjId="SW-302398_0" Pin1InfoVect2LinkObjId="g_1e59240_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="731,-1064 761,-1064 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1e4ae70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="731,-1046 731,-1064 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="powerLine" EndDevType2="lightningRod" ObjectIDND0="46645@x" ObjectIDND1="46642@x" ObjectIDZND0="g_1e59240@0" ObjectIDZND1="30215@1" ObjectIDZND2="g_1e489a0@0" Pin0InfoVect0LinkObjId="g_1e59240_0" Pin0InfoVect1LinkObjId="g_1e5aae0_1" Pin0InfoVect2LinkObjId="g_1e489a0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-302401_0" Pin1InfoVect1LinkObjId="SW-302398_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="731,-1046 731,-1064 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1e4b0d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="731,-1064 731,-1095 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="46645@x" ObjectIDND1="46642@x" ObjectIDND2="g_1e489a0@0" ObjectIDZND0="g_1e59240@0" ObjectIDZND1="30215@1" Pin0InfoVect0LinkObjId="g_1e59240_0" Pin0InfoVect1LinkObjId="g_1e5aae0_1" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-302401_0" Pin1InfoVect1LinkObjId="SW-302398_0" Pin1InfoVect2LinkObjId="g_1e489a0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="731,-1064 731,-1095 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1e4bdc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="998,-326 1040,-326 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="earth" ObjectIDZND0="g_1e4b330@0" Pin0InfoVect0LinkObjId="g_1e4b330_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="998,-326 1040,-326 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1d64b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1504,-1035 1613,-1035 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1504,-1035 1613,-1035 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1d64d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1613,-1035 1715,-1035 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="generator" ObjectIDND0="0@x" ObjectIDND1="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1613,-1035 1715,-1035 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1d65da0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1594,-926 1613,-926 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="g_1d64ff0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d64ff0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1594,-926 1613,-926 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1d66890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1612,-896 1612,-926 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="g_1d64ff0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_1d64ff0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1612,-896 1612,-926 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1d66af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1612,-926 1612,-964 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="g_1d64ff0@0" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1d64ff0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1612,-926 1612,-964 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1d68730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1664,-682 1664,-701 1613,-701 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" EndDevType1="lightningRod" ObjectIDND0="g_1d67980@0" ObjectIDZND0="0@x" ObjectIDZND1="g_1d66d50@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_1d66d50_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d67980_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1664,-682 1664,-701 1613,-701 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1d69220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1613,-658 1613,-701 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_1d66d50@0" ObjectIDZND1="g_1d67980@0" Pin0InfoVect0LinkObjId="g_1d66d50_0" Pin0InfoVect1LinkObjId="g_1d67980_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1613,-658 1613,-701 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1d69480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1613,-701 1613,-731 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_1d67980@0" ObjectIDZND0="g_1d66d50@1" Pin0InfoVect0LinkObjId="g_1d66d50_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_1d67980_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1613,-701 1613,-731 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c9bc30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="989,-249 989,-260 943,-260 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="transformer2" ObjectIDND0="g_1a54190@0" ObjectIDZND0="46656@x" ObjectIDZND1="46675@x" Pin0InfoVect0LinkObjId="SW-302420_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a54190_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="989,-249 989,-260 943,-260 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c9c5a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="943,-288 943,-260 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="46675@1" ObjectIDZND0="g_1a54190@0" ObjectIDZND1="46656@x" Pin0InfoVect0LinkObjId="g_1a54190_0" Pin0InfoVect1LinkObjId="SW-302420_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="943,-288 943,-260 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c9c790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="943,-260 943,-191 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" EndDevType0="breaker" ObjectIDND0="g_1a54190@0" ObjectIDND1="46675@x" ObjectIDZND0="46656@1" Pin0InfoVect0LinkObjId="SW-302420_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1a54190_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="943,-260 943,-191 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1c9ef80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="944,-700 944,-716 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="46650@1" ObjectIDZND0="46637@0" Pin0InfoVect0LinkObjId="g_1f32540_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-302412_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="944,-700 944,-716 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c40f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="728,187 728,199 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1aab110@1" ObjectIDZND0="46670@1" Pin0InfoVect0LinkObjId="SW-302442_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1aab110_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="728,187 728,199 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c41160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="688,107 688,91 728,91 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="lightningRod" ObjectIDND0="g_1ac07f0@0" ObjectIDZND0="46667@x" ObjectIDZND1="g_1aab110@0" Pin0InfoVect0LinkObjId="SW-302437_0" Pin0InfoVect1LinkObjId="g_1aab110_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1ac07f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="688,107 688,91 728,91 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c41ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="728,56 728,91 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="46667@0" ObjectIDZND0="g_1ac07f0@0" ObjectIDZND1="g_1aab110@0" Pin0InfoVect0LinkObjId="g_1ac07f0_0" Pin0InfoVect1LinkObjId="g_1aab110_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-302437_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="728,56 728,91 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c41ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="728,91 728,134 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="g_1ac07f0@0" ObjectIDND1="46667@x" ObjectIDZND0="g_1aab110@0" Pin0InfoVect0LinkObjId="g_1aab110_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1ac07f0_0" Pin1InfoVect1LinkObjId="SW-302437_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="728,91 728,134 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c41f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="906,91 906,76 946,76 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="lightningRod" ObjectIDND0="g_1c511f0@0" ObjectIDZND0="46672@x" ObjectIDZND1="g_163ba50@0" Pin0InfoVect0LinkObjId="SW-302444_0" Pin0InfoVect1LinkObjId="g_163ba50_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c511f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="906,91 906,76 946,76 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c429e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="946,39 946,76 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="46672@0" ObjectIDZND0="g_1c511f0@0" ObjectIDZND1="g_163ba50@0" Pin0InfoVect0LinkObjId="g_1c511f0_0" Pin0InfoVect1LinkObjId="g_163ba50_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-302444_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="946,39 946,76 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c42c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="946,76 946,99 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="g_1c511f0@0" ObjectIDND1="46672@x" ObjectIDZND0="g_163ba50@0" Pin0InfoVect0LinkObjId="g_163ba50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1c511f0_0" Pin1InfoVect1LinkObjId="SW-302444_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="946,76 946,99 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c45ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="259,10 249,10 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="46663@0" ObjectIDZND0="g_1deb710@0" Pin0InfoVect0LinkObjId="g_1deb710_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-302429_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="259,10 249,10 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1def6f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="454,14 444,14 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="46666@0" ObjectIDZND0="g_1deec60@0" Pin0InfoVect0LinkObjId="g_1deec60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-302434_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="454,14 444,14 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1df2ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="676,12 666,12 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="46669@0" ObjectIDZND0="g_1df2410@0" Pin0InfoVect0LinkObjId="g_1df2410_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-302439_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="676,12 666,12 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ad9f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="890,4 880,4 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="46674@0" ObjectIDZND0="g_1ad94a0@0" Pin0InfoVect0LinkObjId="g_1ad94a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-302446_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="890,4 880,4 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1add6e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="885,-155 875,-155 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="46658@0" ObjectIDZND0="g_1adcc50@0" Pin0InfoVect0LinkObjId="g_1adcc50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-302422_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="885,-155 875,-155 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d8c730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="595,-144 585,-144 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="46660@0" ObjectIDZND0="g_1d8bca0@0" Pin0InfoVect0LinkObjId="g_1d8bca0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-302426_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="595,-144 585,-144 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d8c990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="631,-144 650,-144 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="46660@1" ObjectIDZND0="g_1d13250@0" ObjectIDZND1="g_1d1d660@0" ObjectIDZND2="46659@x" Pin0InfoVect0LinkObjId="g_1d13250_0" Pin0InfoVect1LinkObjId="g_1d1d660_0" Pin0InfoVect2LinkObjId="SW-302425_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-302426_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="631,-144 650,-144 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d8d480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="650,-153 650,-144 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1d13250@0" ObjectIDND1="g_1d1d660@0" ObjectIDZND0="46660@x" ObjectIDZND1="46659@x" Pin0InfoVect0LinkObjId="SW-302426_0" Pin0InfoVect1LinkObjId="SW-302425_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1d13250_0" Pin1InfoVect1LinkObjId="g_1d1d660_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="650,-153 650,-144 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d8d6e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="650,-144 650,-138 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="46660@x" ObjectIDND1="g_1d13250@0" ObjectIDND2="g_1d1d660@0" ObjectIDZND0="46659@1" Pin0InfoVect0LinkObjId="SW-302425_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-302426_0" Pin1InfoVect1LinkObjId="g_1d13250_0" Pin1InfoVect2LinkObjId="g_1d1d660_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="650,-144 650,-138 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d8d940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="921,-155 943,-155 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="46658@1" ObjectIDZND0="46657@1" ObjectIDZND1="46656@x" Pin0InfoVect0LinkObjId="SW-302421_1" Pin0InfoVect1LinkObjId="SW-302420_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-302422_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="921,-155 943,-155 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d8e430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="943,-144 943,-155 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="46657@1" ObjectIDZND0="46658@x" ObjectIDZND1="46656@x" Pin0InfoVect0LinkObjId="SW-302422_0" Pin0InfoVect1LinkObjId="SW-302420_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-302421_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="943,-144 943,-155 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d8e690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="943,-155 943,-164 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="46658@x" ObjectIDND1="46657@1" ObjectIDZND0="46656@0" Pin0InfoVect0LinkObjId="SW-302420_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-302422_0" Pin1InfoVect1LinkObjId="SW-302421_1" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="943,-155 943,-164 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d8e8f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="926,4 946,4 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="46674@1" ObjectIDZND0="46672@x" ObjectIDZND1="46673@x" Pin0InfoVect0LinkObjId="SW-302444_0" Pin0InfoVect1LinkObjId="SW-302445_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-302446_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="926,4 946,4 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d8f3e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="946,12 946,4 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="46672@1" ObjectIDZND0="46674@x" ObjectIDZND1="46673@x" Pin0InfoVect0LinkObjId="SW-302446_0" Pin0InfoVect1LinkObjId="SW-302445_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-302444_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="946,12 946,4 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d8f640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="946,4 946,-7 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="46674@x" ObjectIDND1="46672@x" ObjectIDZND0="46673@0" Pin0InfoVect0LinkObjId="SW-302445_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-302446_0" Pin1InfoVect1LinkObjId="SW-302444_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="946,4 946,-7 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d8f8a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="712,12 728,12 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="46669@1" ObjectIDZND0="46668@x" ObjectIDZND1="46667@x" Pin0InfoVect0LinkObjId="SW-302438_0" Pin0InfoVect1LinkObjId="SW-302437_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-302439_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="712,12 728,12 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d90390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="728,-11 728,12 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="46668@0" ObjectIDZND0="46669@x" ObjectIDZND1="46667@x" Pin0InfoVect0LinkObjId="SW-302439_0" Pin0InfoVect1LinkObjId="SW-302437_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-302438_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="728,-11 728,12 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d905f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="728,12 728,29 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="46669@x" ObjectIDND1="46668@x" ObjectIDZND0="46667@1" Pin0InfoVect0LinkObjId="SW-302437_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-302439_0" Pin1InfoVect1LinkObjId="SW-302438_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="728,12 728,29 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d90850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="490,14 510,14 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="46666@1" ObjectIDZND0="46665@x" ObjectIDZND1="46664@x" Pin0InfoVect0LinkObjId="SW-302433_0" Pin0InfoVect1LinkObjId="SW-302432_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-302434_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="490,14 510,14 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d91340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="510,-9 510,14 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="46665@0" ObjectIDZND0="46666@x" ObjectIDZND1="46664@x" Pin0InfoVect0LinkObjId="SW-302434_0" Pin0InfoVect1LinkObjId="SW-302432_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-302433_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="510,-9 510,14 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d915a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="510,14 510,31 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="46666@x" ObjectIDND1="46665@x" ObjectIDZND0="46664@1" Pin0InfoVect0LinkObjId="SW-302432_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-302434_0" Pin1InfoVect1LinkObjId="SW-302433_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="510,14 510,31 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d91800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="295,10 310,10 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="46663@1" ObjectIDZND0="46662@x" ObjectIDZND1="46661@x" Pin0InfoVect0LinkObjId="SW-302428_0" Pin0InfoVect1LinkObjId="SW-302427_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-302429_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="295,10 310,10 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d922f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="310,-10 310,10 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="46662@0" ObjectIDZND0="46663@x" ObjectIDZND1="46661@x" Pin0InfoVect0LinkObjId="SW-302429_0" Pin0InfoVect1LinkObjId="SW-302427_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-302428_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="310,-10 310,10 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d92550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="310,10 310,30 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="46663@x" ObjectIDND1="46662@x" ObjectIDZND0="46661@1" Pin0InfoVect0LinkObjId="SW-302427_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-302429_0" Pin1InfoVect1LinkObjId="SW-302428_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="310,10 310,30 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1d352d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1613,-870 1613,-848 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1613,-870 1613,-848 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1d35530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1613,-812 1613,-775 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_1d66d50@0" Pin0InfoVect0LinkObjId="g_1d66d50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1613,-812 1613,-775 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-302303" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1242.000000 -821.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="302303" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46637"/>
     <cge:Term_Ref ObjectID="39103"/>
    <cge:TPSR_Ref TObjectID="46637"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-302304" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1242.000000 -821.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="302304" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46637"/>
     <cge:Term_Ref ObjectID="39103"/>
    <cge:TPSR_Ref TObjectID="46637"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-302305" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1242.000000 -821.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="302305" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46637"/>
     <cge:Term_Ref ObjectID="39103"/>
    <cge:TPSR_Ref TObjectID="46637"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-302309" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1242.000000 -821.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="302309" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46637"/>
     <cge:Term_Ref ObjectID="39103"/>
    <cge:TPSR_Ref TObjectID="46637"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-302306" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1242.000000 -821.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="302306" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46637"/>
     <cge:Term_Ref ObjectID="39103"/>
    <cge:TPSR_Ref TObjectID="46637"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-302310" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1242.000000 -821.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="302310" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46637"/>
     <cge:Term_Ref ObjectID="39103"/>
    <cge:TPSR_Ref TObjectID="46637"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-302339" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1735.000000 -184.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="302339" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46638"/>
     <cge:Term_Ref ObjectID="39104"/>
    <cge:TPSR_Ref TObjectID="46638"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-302340" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1735.000000 -184.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="302340" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46638"/>
     <cge:Term_Ref ObjectID="39104"/>
    <cge:TPSR_Ref TObjectID="46638"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-302341" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1735.000000 -184.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="302341" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46638"/>
     <cge:Term_Ref ObjectID="39104"/>
    <cge:TPSR_Ref TObjectID="46638"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-302345" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1735.000000 -184.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="302345" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46638"/>
     <cge:Term_Ref ObjectID="39104"/>
    <cge:TPSR_Ref TObjectID="46638"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-302342" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1735.000000 -184.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="302342" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46638"/>
     <cge:Term_Ref ObjectID="39104"/>
    <cge:TPSR_Ref TObjectID="46638"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-302346" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1735.000000 -184.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="302346" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46638"/>
     <cge:Term_Ref ObjectID="39104"/>
    <cge:TPSR_Ref TObjectID="46638"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-302320" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1075.000000 -591.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="302320" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46649"/>
     <cge:Term_Ref ObjectID="39452"/>
    <cge:TPSR_Ref TObjectID="46649"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-302321" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1075.000000 -591.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="302321" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46649"/>
     <cge:Term_Ref ObjectID="39452"/>
    <cge:TPSR_Ref TObjectID="46649"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-302317" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1075.000000 -591.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="302317" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46649"/>
     <cge:Term_Ref ObjectID="39452"/>
    <cge:TPSR_Ref TObjectID="46649"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-302353" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 283.000000 291.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="302353" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46661"/>
     <cge:Term_Ref ObjectID="39480"/>
    <cge:TPSR_Ref TObjectID="46661"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-302354" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 283.000000 291.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="302354" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46661"/>
     <cge:Term_Ref ObjectID="39480"/>
    <cge:TPSR_Ref TObjectID="46661"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-302350" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 283.000000 291.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="302350" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46661"/>
     <cge:Term_Ref ObjectID="39480"/>
    <cge:TPSR_Ref TObjectID="46661"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-302362" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 475.000000 299.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="302362" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46664"/>
     <cge:Term_Ref ObjectID="39785"/>
    <cge:TPSR_Ref TObjectID="46664"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-302363" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 475.000000 299.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="302363" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46664"/>
     <cge:Term_Ref ObjectID="39785"/>
    <cge:TPSR_Ref TObjectID="46664"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-302359" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 475.000000 299.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="302359" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46664"/>
     <cge:Term_Ref ObjectID="39785"/>
    <cge:TPSR_Ref TObjectID="46664"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-302370" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 998.000000 264.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="302370" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46672"/>
     <cge:Term_Ref ObjectID="39801"/>
    <cge:TPSR_Ref TObjectID="46672"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-302371" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 998.000000 264.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="302371" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46672"/>
     <cge:Term_Ref ObjectID="39801"/>
    <cge:TPSR_Ref TObjectID="46672"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-302369" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 998.000000 264.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="302369" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46672"/>
     <cge:Term_Ref ObjectID="39801"/>
    <cge:TPSR_Ref TObjectID="46672"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-302368" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 808.000000 271.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="302368" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46667"/>
     <cge:Term_Ref ObjectID="39791"/>
    <cge:TPSR_Ref TObjectID="46667"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-302365" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 808.000000 271.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="302365" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46667"/>
     <cge:Term_Ref ObjectID="39791"/>
    <cge:TPSR_Ref TObjectID="46667"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-302299" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 852.000000 -920.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="302299" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46640"/>
     <cge:Term_Ref ObjectID="39107"/>
    <cge:TPSR_Ref TObjectID="46640"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-302300" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 852.000000 -920.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="302300" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46640"/>
     <cge:Term_Ref ObjectID="39107"/>
    <cge:TPSR_Ref TObjectID="46640"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-302296" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 852.000000 -920.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="302296" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46640"/>
     <cge:Term_Ref ObjectID="39107"/>
    <cge:TPSR_Ref TObjectID="46640"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-302332" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1088.000000 -200.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="302332" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46656"/>
     <cge:Term_Ref ObjectID="39470"/>
    <cge:TPSR_Ref TObjectID="46656"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-302333" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1088.000000 -200.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="302333" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46656"/>
     <cge:Term_Ref ObjectID="39470"/>
    <cge:TPSR_Ref TObjectID="46656"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-302329" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1088.000000 -200.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="302329" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46656"/>
     <cge:Term_Ref ObjectID="39470"/>
    <cge:TPSR_Ref TObjectID="46656"/></metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_地调直调_光伏.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="175" x="-227" y="-940"/></g>
   <g href="cx_索引_接线图_地调直调_光伏.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-276" y="-957"/></g>
   <g href="AVC竹溪光伏.svg" style="fill-opacity:0"><rect height="48" qtmmishow="hidden" stroke="rgb(255,0,0)" width="143" x="10" y="-817"/></g>
  </g><g id="CircleFilled_Layer">
   <circle DF8003:Layer="PUBLIC" cx="977" cy="-325" fill="none" fillStyle="0" r="25" stroke="rgb(60,120,255)" stroke-width="0.520408"/>
  </g><g id="Generator_Layer">
   <g DF8003:Layer="PUBLIC" id="SM-CX_ZhuXGF.P1">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 304.000000 220.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47287" ObjectName="SM-CX_ZhuXGF.P1"/>
    <cge:TPSR_Ref TObjectID="47287"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_ZhuXGF.P2">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 504.000000 221.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47288" ObjectName="SM-CX_ZhuXGF.P2"/>
    <cge:TPSR_Ref TObjectID="47288"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1216.000000 224.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1457.000000 227.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1703.000000 230.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.307692 0.000000 1477.000000 -1040.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
  </g><g id="ConnectPoint_Layer"/><g id="PolygonFilled_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="none" points="863,-309 858,-320 869,-320 863,-309 863,-310 863,-309 " stroke="rgb(170,85,127)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="863,-295 858,-284 869,-284 863,-295 863,-294 863,-295 " stroke="rgb(170,85,127)"/>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="175" x="-227" y="-940"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="175" x="-227" y="-940"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-276" y="-957"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-276" y="-957"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <polygon fill="rgb(255,255,255)" points="10,-817 7,-820 7,-766 10,-769 10,-817" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(255,255,255)" points="10,-817 7,-820 156,-820 153,-817 10,-817" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(112,119,119)" points="10,-769 7,-766 156,-766 153,-769 10,-769" stroke="rgb(112,119,119)"/>
     <polygon fill="rgb(112,119,119)" points="153,-817 156,-820 156,-766 153,-769 153,-817" stroke="rgb(112,119,119)"/>
     <rect fill="rgb(224,238,238)" height="48" stroke="rgb(224,238,238)" width="143" x="10" y="-817"/>
     <rect fill="none" height="48" qtmmishow="hidden" stroke="rgb(255,0,0)" width="143" x="10" y="-817"/>
    </a>
   <metadata/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1db6920" refnum="0">
    <use class="BV-0KV" transform="matrix(1.500000 -0.000000 0.000000 -1.347826 837.000000 -224.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ca8fc0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 854.000000 -619.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2094e20" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 852.000000 -503.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d59000" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 853.000000 -398.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d5b140" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 641.000000 -926.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f6cca0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 645.000000 -810.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e57a60" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 640.000000 -1037.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1cdc560" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 957.000000 -848.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f31ab0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 954.000000 -743.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e4b330" refnum="0">
    <use class="BV-0KV" transform="matrix(1.500000 -0.000000 0.000000 -1.347826 1031.000000 -302.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1deb710" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 224.000000 19.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1deec60" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 419.000000 23.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1df2410" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 641.000000 21.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ad94a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 855.000000 13.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1adcc50" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 850.000000 -146.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d8bca0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 560.000000 -135.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_ZhuXGF"/>
</svg>