<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-59" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="3114 -1198 2067 1201">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="29" x2="29" y1="7" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="4" x2="22" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="22" x2="22" y1="0" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="25" x2="25" y1="6" y2="13"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="7" x2="11" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="9" x2="9" y1="27" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="0" x2="18" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="6" x2="13" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape74">
    <circle cx="39" cy="14" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="13" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="16" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="19" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="19" y2="19"/>
    <circle cx="30" cy="9" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="30" cy="20" fillStyle="0" r="8.5" stroke-width="1"/>
    <rect height="27" stroke-width="0.416667" width="14" x="0" y="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="71" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="31" y1="71" y2="71"/>
    <rect height="27" stroke-width="0.416667" width="14" x="24" y="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="82" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0587025" x1="31" x2="34" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.173913" x1="30" x2="30" y1="22" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.108974" x1="30" x2="27" y1="23" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0587025" x1="31" x2="34" y1="9" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.173913" x1="30" x2="30" y1="7" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.108974" x1="30" x2="27" y1="8" y2="11"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="41,17 39,15 45,15 43,18 " stroke-width="1"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="7" x2="15" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="15" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="7" x2="15" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="15" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="transformer2:shape11_0">
    <ellipse cx="13" cy="34" fillStyle="0" rx="13" ry="12.5" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="40" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="36" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="32" y2="36"/>
   </symbol>
   <symbol id="transformer2:shape11_1">
    <circle cx="13" cy="16" fillStyle="0" r="13" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="20" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="16" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="12" y2="16"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1fe6740" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1fe7540" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1fe7d80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1fe8cc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1fe9fc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1feaae0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1feb340" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1febe00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1ab3630" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1ab3630" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1feed30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1feed30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1ff0a90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1ff0a90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_1ff19c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1ff35f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1ff4280" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1ff50f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1ff5840" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1ff70c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1ff7d20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1ff85e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1ff8da0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1ff9e80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1ffa800" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1ffb2f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1ffbcb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1ffd2d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1ffdcf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1ffee90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1fffb20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_200df30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_20062d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2000cb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2002030" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1211" width="2077" x="3109" y="-1203"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3115" y="-1077"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3116" y="-1197"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3115" y="-597"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-43052">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3746.013739 -660.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7202" ObjectName="SW-CX_XHK.CX_XHK_0901SW"/>
     <cge:Meas_Ref ObjectId="43052"/>
    <cge:TPSR_Ref TObjectID="7202"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43054">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3574.000000 -575.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7204" ObjectName="SW-CX_XHK.CX_XHK_0551SW"/>
     <cge:Meas_Ref ObjectId="43054"/>
    <cge:TPSR_Ref TObjectID="7204"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43055">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3574.000000 -457.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7205" ObjectName="SW-CX_XHK.CX_XHK_0556SW"/>
     <cge:Meas_Ref ObjectId="43055"/>
    <cge:TPSR_Ref TObjectID="7205"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43056">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 3620.500000 -558.500000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7206" ObjectName="SW-CX_XHK.CX_XHK_05517SW"/>
     <cge:Meas_Ref ObjectId="43056"/>
    <cge:TPSR_Ref TObjectID="7206"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43062">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4017.013739 -655.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7212" ObjectName="SW-CX_XHK.CX_XHK_0511SW"/>
     <cge:Meas_Ref ObjectId="43062"/>
    <cge:TPSR_Ref TObjectID="7212"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-58608">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4063.000000 -706.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11080" ObjectName="SW-CX_XHK.CX_XHK_05117SW"/>
     <cge:Meas_Ref ObjectId="58608"/>
    <cge:TPSR_Ref TObjectID="11080"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-58609">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4063.000000 -777.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11081" ObjectName="SW-CX_XHK.CX_XHK_05167SW"/>
     <cge:Meas_Ref ObjectId="58609"/>
    <cge:TPSR_Ref TObjectID="11081"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43050">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3751.000000 -574.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7200" ObjectName="SW-CX_XHK.CX_XHK_0541SW"/>
     <cge:Meas_Ref ObjectId="43050"/>
    <cge:TPSR_Ref TObjectID="7200"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43051">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3751.000000 -456.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7201" ObjectName="SW-CX_XHK.CX_XHK_0546SW"/>
     <cge:Meas_Ref ObjectId="43051"/>
    <cge:TPSR_Ref TObjectID="7201"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-58606">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 3797.500000 -557.500000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11064" ObjectName="SW-CX_XHK.CX_XHK_05417SW"/>
     <cge:Meas_Ref ObjectId="58606"/>
    <cge:TPSR_Ref TObjectID="11064"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-58625">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3941.000000 -575.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11057" ObjectName="SW-CX_XHK.CX_XHK_0531SW"/>
     <cge:Meas_Ref ObjectId="58625"/>
    <cge:TPSR_Ref TObjectID="11057"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-58626">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3941.000000 -457.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11058" ObjectName="SW-CX_XHK.CX_XHK_0536SW"/>
     <cge:Meas_Ref ObjectId="58626"/>
    <cge:TPSR_Ref TObjectID="11058"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-58610">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 3987.500000 -560.500000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11056" ObjectName="SW-CX_XHK.CX_XHK_05317SW"/>
     <cge:Meas_Ref ObjectId="58610"/>
    <cge:TPSR_Ref TObjectID="11056"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43084">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4971.013739 -661.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7230" ObjectName="SW-CX_XHK.CX_XHK_0902SW"/>
     <cge:Meas_Ref ObjectId="43084"/>
    <cge:TPSR_Ref TObjectID="7230"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-58627">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4502.000000 -577.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11061" ObjectName="SW-CX_XHK.CX_XHK_0621SW"/>
     <cge:Meas_Ref ObjectId="58627"/>
    <cge:TPSR_Ref TObjectID="11061"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-58628">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4502.000000 -459.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11062" ObjectName="SW-CX_XHK.CX_XHK_0626SW"/>
     <cge:Meas_Ref ObjectId="58628"/>
    <cge:TPSR_Ref TObjectID="11062"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-58623">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4548.500000 -558.500000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11060" ObjectName="SW-CX_XHK.CX_XHK_06217SW"/>
     <cge:Meas_Ref ObjectId="58623"/>
    <cge:TPSR_Ref TObjectID="11060"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43072">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4619.013739 -655.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7218" ObjectName="SW-CX_XHK.CX_XHK_0611SW"/>
     <cge:Meas_Ref ObjectId="43072"/>
    <cge:TPSR_Ref TObjectID="7218"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-58620">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4664.000000 -706.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11077" ObjectName="SW-CX_XHK.CX_XHK_06117SW"/>
     <cge:Meas_Ref ObjectId="58620"/>
    <cge:TPSR_Ref TObjectID="11077"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-58621">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4665.000000 -777.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11078" ObjectName="SW-CX_XHK.CX_XHK_06167SW"/>
     <cge:Meas_Ref ObjectId="58621"/>
    <cge:TPSR_Ref TObjectID="11078"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43082">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4677.000000 -574.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7228" ObjectName="SW-CX_XHK.CX_XHK_0631SW"/>
     <cge:Meas_Ref ObjectId="43082"/>
    <cge:TPSR_Ref TObjectID="7228"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43083">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4677.000000 -456.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7229" ObjectName="SW-CX_XHK.CX_XHK_0636SW"/>
     <cge:Meas_Ref ObjectId="43083"/>
    <cge:TPSR_Ref TObjectID="7229"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-58624">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4723.500000 -558.500000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11066" ObjectName="SW-CX_XHK.CX_XHK_06317SW"/>
     <cge:Meas_Ref ObjectId="58624"/>
    <cge:TPSR_Ref TObjectID="11066"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43078">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4857.000000 -571.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7224" ObjectName="SW-CX_XHK.CX_XHK_0641SW"/>
     <cge:Meas_Ref ObjectId="43078"/>
    <cge:TPSR_Ref TObjectID="7224"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43079">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4857.000000 -453.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7225" ObjectName="SW-CX_XHK.CX_XHK_0646SW"/>
     <cge:Meas_Ref ObjectId="43079"/>
    <cge:TPSR_Ref TObjectID="7225"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43080">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4903.500000 -558.500000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7226" ObjectName="SW-CX_XHK.CX_XHK_06417SW"/>
     <cge:Meas_Ref ObjectId="43080"/>
    <cge:TPSR_Ref TObjectID="7226"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43074">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5049.000000 -571.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7220" ObjectName="SW-CX_XHK.CX_XHK_0651SW"/>
     <cge:Meas_Ref ObjectId="43074"/>
    <cge:TPSR_Ref TObjectID="7220"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43075">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5049.000000 -453.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7221" ObjectName="SW-CX_XHK.CX_XHK_0656SW"/>
     <cge:Meas_Ref ObjectId="43075"/>
    <cge:TPSR_Ref TObjectID="7221"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43076">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 5095.500000 -558.500000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7222" ObjectName="SW-CX_XHK.CX_XHK_06517SW"/>
     <cge:Meas_Ref ObjectId="43076"/>
    <cge:TPSR_Ref TObjectID="7222"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43064">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4295.000000 -578.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7214" ObjectName="SW-CX_XHK.CX_XHK_0121SW"/>
     <cge:Meas_Ref ObjectId="43064"/>
    <cge:TPSR_Ref TObjectID="7214"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43065">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4437.000000 -578.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7215" ObjectName="SW-CX_XHK.CX_XHK_0122SW"/>
     <cge:Meas_Ref ObjectId="43065"/>
    <cge:TPSR_Ref TObjectID="7215"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43066">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4295.000000 -500.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7216" ObjectName="SW-CX_XHK.CX_XHK_01217SW"/>
     <cge:Meas_Ref ObjectId="43066"/>
    <cge:TPSR_Ref TObjectID="7216"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-58619">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4437.000000 -498.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11065" ObjectName="SW-CX_XHK.CX_XHK_01227SW"/>
     <cge:Meas_Ref ObjectId="58619"/>
    <cge:TPSR_Ref TObjectID="11065"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43059">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4118.000000 -457.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7209" ObjectName="SW-CX_XHK.CX_XHK_0526SW"/>
     <cge:Meas_Ref ObjectId="43059"/>
    <cge:TPSR_Ref TObjectID="7209"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43060">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4164.500000 -560.500000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7210" ObjectName="SW-CX_XHK.CX_XHK_05217SW"/>
     <cge:Meas_Ref ObjectId="43060"/>
    <cge:TPSR_Ref TObjectID="7210"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43058">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4118.000000 -575.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7208" ObjectName="SW-CX_XHK.CX_XHK_0521SW"/>
     <cge:Meas_Ref ObjectId="43058"/>
    <cge:TPSR_Ref TObjectID="7208"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-58607">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 3712.000000 -716.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11079" ObjectName="SW-CX_XHK.CX_XHK_09017SW"/>
     <cge:Meas_Ref ObjectId="58607"/>
    <cge:TPSR_Ref TObjectID="11079"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-58622">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4937.000000 -717.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11076" ObjectName="SW-CX_XHK.CX_XHK_09027SW"/>
     <cge:Meas_Ref ObjectId="58622"/>
    <cge:TPSR_Ref TObjectID="11076"/></metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" runFlow="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3589,-445 3589,-383 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="3589,-445 3589,-383 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" runFlow="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3766,-444 3766,-382 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="3766,-444 3766,-382 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" runFlow="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3956,-445 3956,-383 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="3956,-445 3956,-383 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" runFlow="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4517,-447 4517,-385 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4517,-447 4517,-385 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" runFlow="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4692,-444 4692,-382 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4692,-444 4692,-382 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" runFlow="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4872,-448 4872,-386 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4872,-448 4872,-386 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" runFlow="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="5064,-448 5064,-386 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="5064,-448 5064,-386 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" runFlow="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4133,-445 4133,-383 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4133,-445 4133,-383 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1728b70" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 3661.500000 -574.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a82ce0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3689.000000 -752.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_174b510" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4104.000000 -722.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1711620" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4104.000000 -793.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a277f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 3838.500000 -573.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a22b70" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4028.500000 -576.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19fcae0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4589.500000 -574.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19fdbf0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4914.000000 -753.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a5c960" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4705.000000 -722.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a2b690" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4706.000000 -793.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a69460" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4764.500000 -574.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a7c2a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4944.500000 -574.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a7f3a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 5136.500000 -574.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19ab3b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4300.000000 -477.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19abe00" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4442.000000 -477.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a3bdf0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4205.500000 -576.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-10KV" id="g_1a87ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3589,-585 3611,-585 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="7204@x" ObjectIDND1="7203@x" ObjectIDZND0="7206@0" Pin0InfoVect0LinkObjId="SW-43056_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-43054_0" Pin1InfoVect1LinkObjId="SW-43053_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3589,-585 3611,-585 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a80e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3647,-584 3666,-584 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="7206@1" ObjectIDZND0="g_1728b70@0" Pin0InfoVect0LinkObjId="g_1728b70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43056_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3647,-584 3666,-584 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a82af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3761,-682 3761,-652 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="7202@0" ObjectIDZND0="19356@0" Pin0InfoVect0LinkObjId="g_1904d80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43052_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3761,-682 3761,-652 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a65200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3703,-742 3684,-742 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="11079@0" ObjectIDZND0="g_1a82ce0@0" Pin0InfoVect0LinkObjId="g_1a82ce0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58607_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3703,-742 3684,-742 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1aff8d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4032,-652 4032,-677 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="19356@0" ObjectIDZND0="7212@0" Pin0InfoVect0LinkObjId="SW-43062_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a82af0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4032,-652 4032,-677 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a987e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4090,-732 4109,-732 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="11080@1" ObjectIDZND0="g_174b510@0" Pin0InfoVect0LinkObjId="g_174b510_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58608_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4090,-732 4109,-732 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a989d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4032,-732 4054,-732 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="7212@x" ObjectIDND1="7211@x" ObjectIDZND0="11080@0" Pin0InfoVect0LinkObjId="SW-58608_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-43062_0" Pin1InfoVect1LinkObjId="SW-43061_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4032,-732 4054,-732 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a99190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4032,-713 4032,-732 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="7212@1" ObjectIDZND0="11080@x" ObjectIDZND1="7211@x" Pin0InfoVect0LinkObjId="SW-58608_0" Pin0InfoVect1LinkObjId="SW-43061_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43062_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4032,-713 4032,-732 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1903e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4090,-803 4109,-803 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="11081@1" ObjectIDZND0="g_1711620@0" Pin0InfoVect0LinkObjId="g_1711620_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58609_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4090,-803 4109,-803 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1904040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4032,-732 4032,-754 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="11080@x" ObjectIDND1="7212@x" ObjectIDZND0="7211@0" Pin0InfoVect0LinkObjId="SW-43061_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-58608_0" Pin1InfoVect1LinkObjId="SW-43062_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4032,-732 4032,-754 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1904d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3589,-633 3589,-652 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="7204@1" ObjectIDZND0="19356@0" Pin0InfoVect0LinkObjId="g_1a82af0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43054_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3589,-633 3589,-652 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1904f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3589,-445 3589,-479 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="7205@0" Pin0InfoVect0LinkObjId="SW-43055_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1728b70_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3589,-445 3589,-479 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1905160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3589,-515 3589,-535 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="7205@1" ObjectIDZND0="7203@0" Pin0InfoVect0LinkObjId="SW-43053_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43055_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3589,-515 3589,-535 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a27600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3766,-584 3788,-584 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="7200@x" ObjectIDND1="7199@x" ObjectIDZND0="11064@0" Pin0InfoVect0LinkObjId="SW-58606_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-43050_0" Pin1InfoVect1LinkObjId="SW-43049_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3766,-584 3788,-584 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a27fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3824,-583 3843,-583 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="11064@1" ObjectIDZND0="g_1a277f0@0" Pin0InfoVect0LinkObjId="g_1a277f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58606_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3824,-583 3843,-583 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a83090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3766,-635 3766,-652 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="7200@1" ObjectIDZND0="19356@0" Pin0InfoVect0LinkObjId="g_1a82af0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43050_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3766,-635 3766,-652 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a83280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3766,-444 3766,-478 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="7201@0" Pin0InfoVect0LinkObjId="SW-43051_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1728b70_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3766,-444 3766,-478 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a83470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3766,-514 3766,-534 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="7201@1" ObjectIDZND0="7199@0" Pin0InfoVect0LinkObjId="SW-43049_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43051_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3766,-514 3766,-534 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a22980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3956,-586 3978,-586 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="11057@x" ObjectIDND1="11059@x" ObjectIDZND0="11056@0" Pin0InfoVect0LinkObjId="SW-58610_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-58625_0" Pin1InfoVect1LinkObjId="SW-58629_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3956,-586 3978,-586 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a23320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4014,-586 4033,-586 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="11056@1" ObjectIDZND0="g_1a22b70@0" Pin0InfoVect0LinkObjId="g_1a22b70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58610_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4014,-586 4033,-586 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a23700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3956,-633 3956,-652 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="11057@1" ObjectIDZND0="19356@0" Pin0InfoVect0LinkObjId="g_1a82af0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58625_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3956,-633 3956,-652 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a238f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3956,-445 3956,-479 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="11058@0" Pin0InfoVect0LinkObjId="SW-58626_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1728b70_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3956,-445 3956,-479 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a23ae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3956,-515 3956,-535 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="11058@1" ObjectIDZND0="11059@0" Pin0InfoVect0LinkObjId="SW-58629_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58626_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3956,-515 3956,-535 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19fc8f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4517,-584 4539,-584 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="11061@x" ObjectIDND1="11063@x" ObjectIDZND0="11060@0" Pin0InfoVect0LinkObjId="SW-58623_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-58627_0" Pin1InfoVect1LinkObjId="SW-58630_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4517,-584 4539,-584 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19fd410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4575,-584 4594,-584 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="11060@1" ObjectIDZND0="g_19fcae0@0" Pin0InfoVect0LinkObjId="g_19fcae0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58623_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4575,-584 4594,-584 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19fda00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4986,-683 4986,-651 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="7230@0" ObjectIDZND0="19357@0" Pin0InfoVect0LinkObjId="g_1a2d2f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43084_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4986,-683 4986,-651 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19fe3d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4928,-743 4909,-743 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="11076@0" ObjectIDZND0="g_19fdbf0@0" Pin0InfoVect0LinkObjId="g_19fdbf0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58622_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4928,-743 4909,-743 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19f9520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4634,-651 4634,-677 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="19357@0" ObjectIDZND0="7218@0" Pin0InfoVect0LinkObjId="SW-43072_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_19fda00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4634,-651 4634,-677 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a5d290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4691,-732 4710,-732 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="11077@1" ObjectIDZND0="g_1a5c960@0" Pin0InfoVect0LinkObjId="g_1a5c960_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58620_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4691,-732 4710,-732 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a5d4b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4633,-732 4655,-732 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="7218@x" ObjectIDND1="7217@x" ObjectIDZND0="11077@0" Pin0InfoVect0LinkObjId="SW-58620_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-43072_0" Pin1InfoVect1LinkObjId="SW-43071_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4633,-732 4655,-732 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a5d6d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4634,-713 4634,-732 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="7218@1" ObjectIDZND0="11077@x" ObjectIDZND1="7217@x" Pin0InfoVect0LinkObjId="SW-58620_0" Pin0InfoVect1LinkObjId="SW-43071_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43072_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4634,-713 4634,-732 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a2bfc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4692,-803 4711,-803 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="11078@1" ObjectIDZND0="g_1a2b690@0" Pin0InfoVect0LinkObjId="g_1a2b690_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58621_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4692,-803 4711,-803 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a2c1e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4634,-732 4634,-754 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="11077@x" ObjectIDND1="7218@x" ObjectIDZND0="7217@0" Pin0InfoVect0LinkObjId="SW-43071_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-58620_0" Pin1InfoVect1LinkObjId="SW-43072_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4634,-732 4634,-754 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a2d2f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4517,-635 4517,-651 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="11061@1" ObjectIDZND0="19357@0" Pin0InfoVect0LinkObjId="g_19fda00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58627_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4517,-635 4517,-651 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a2d510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4517,-447 4517,-481 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="11062@0" Pin0InfoVect0LinkObjId="SW-58628_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1728b70_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4517,-447 4517,-481 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a2d730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4517,-517 4517,-537 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="11062@1" ObjectIDZND0="11063@0" Pin0InfoVect0LinkObjId="SW-58630_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58628_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4517,-517 4517,-537 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a69240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4692,-584 4714,-584 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="7228@x" ObjectIDND1="7227@x" ObjectIDZND0="11066@0" Pin0InfoVect0LinkObjId="SW-58624_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-43082_0" Pin1InfoVect1LinkObjId="SW-43081_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4692,-584 4714,-584 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a69d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4750,-584 4769,-584 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="11066@1" ObjectIDZND0="g_1a69460@0" Pin0InfoVect0LinkObjId="g_1a69460_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58624_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4750,-584 4769,-584 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a6a3d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4692,-632 4692,-651 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="7228@1" ObjectIDZND0="19357@0" Pin0InfoVect0LinkObjId="g_19fda00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43082_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4692,-632 4692,-651 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a6a5c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4692,-444 4692,-478 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="7229@0" Pin0InfoVect0LinkObjId="SW-43083_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1728b70_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4692,-444 4692,-478 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a6a7b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4692,-514 4692,-534 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="7229@1" ObjectIDZND0="7227@0" Pin0InfoVect0LinkObjId="SW-43081_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43083_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4692,-514 4692,-534 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a7c080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4872,-584 4894,-584 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="7224@x" ObjectIDND1="7226@x" ObjectIDND2="7224@x" ObjectIDZND0="7226@0" Pin0InfoVect0LinkObjId="SW-43080_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-43078_0" Pin1InfoVect1LinkObjId="SW-43080_0" Pin1InfoVect2LinkObjId="SW-43078_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4872,-584 4894,-584 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a7cc50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4930,-584 4949,-584 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="7226@1" ObjectIDZND0="g_1a7c2a0@0" Pin0InfoVect0LinkObjId="g_1a7c2a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43080_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4930,-584 4949,-584 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a7d3a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4872,-629 4872,-651 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="7224@1" ObjectIDZND0="19357@0" Pin0InfoVect0LinkObjId="g_19fda00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43078_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4872,-629 4872,-651 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a7d590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4872,-511 4872,-531 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="7225@1" ObjectIDZND0="7223@0" Pin0InfoVect0LinkObjId="SW-43077_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43079_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4872,-511 4872,-531 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a7f170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5064,-584 5086,-584 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="7220@x" ObjectIDND1="7222@x" ObjectIDND2="7220@x" ObjectIDZND0="7222@0" Pin0InfoVect0LinkObjId="SW-43076_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-43074_0" Pin1InfoVect1LinkObjId="SW-43076_0" Pin1InfoVect2LinkObjId="SW-43074_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5064,-584 5086,-584 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a7f920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5122,-584 5141,-584 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="7222@1" ObjectIDZND0="g_1a7f3a0@0" Pin0InfoVect0LinkObjId="g_1a7f3a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43076_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5122,-584 5141,-584 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a800d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5064,-629 5064,-651 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="7220@1" ObjectIDZND0="19357@0" Pin0InfoVect0LinkObjId="g_19fda00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43074_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5064,-629 5064,-651 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a802c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5064,-511 5064,-531 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="7221@1" ObjectIDZND0="7219@0" Pin0InfoVect0LinkObjId="SW-43073_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43075_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5064,-511 5064,-531 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19df290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4310,-652 4310,-636 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="19356@0" ObjectIDZND0="7214@1" Pin0InfoVect0LinkObjId="SW-43064_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a82af0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4310,-652 4310,-636 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1abbe50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4452,-636 4452,-651 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="7215@1" ObjectIDZND0="19357@0" Pin0InfoVect0LinkObjId="g_19fda00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43065_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4452,-636 4452,-651 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1abe920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4364,-575 4310,-575 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="7213@1" ObjectIDZND0="7214@x" ObjectIDZND1="7216@x" Pin0InfoVect0LinkObjId="SW-43064_0" Pin0InfoVect1LinkObjId="SW-43066_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43063_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4364,-575 4310,-575 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1abeb80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4310,-575 4310,-600 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="7213@x" ObjectIDND1="7216@x" ObjectIDZND0="7214@0" Pin0InfoVect0LinkObjId="SW-43064_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-43063_0" Pin1InfoVect1LinkObjId="SW-43066_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4310,-575 4310,-600 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19f5320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4310,-575 4310,-558 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="7213@x" ObjectIDND1="7214@x" ObjectIDZND0="7216@1" Pin0InfoVect0LinkObjId="SW-43066_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-43063_0" Pin1InfoVect1LinkObjId="SW-43064_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4310,-575 4310,-558 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19f5580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4310,-522 4310,-503 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="7216@0" ObjectIDZND0="g_19ab3b0@0" Pin0InfoVect0LinkObjId="g_19ab3b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43066_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4310,-522 4310,-503 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19f6070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4452,-600 4452,-575 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="7215@0" ObjectIDZND0="7213@x" ObjectIDZND1="11065@x" Pin0InfoVect0LinkObjId="SW-43063_0" Pin0InfoVect1LinkObjId="SW-58619_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43065_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4452,-600 4452,-575 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19f62d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4452,-575 4391,-575 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="7215@x" ObjectIDND1="11065@x" ObjectIDZND0="7213@0" Pin0InfoVect0LinkObjId="SW-43063_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-43065_0" Pin1InfoVect1LinkObjId="SW-58619_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4452,-575 4391,-575 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19aaf00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4452,-575 4452,-556 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="7215@x" ObjectIDND1="7213@x" ObjectIDZND0="11065@1" Pin0InfoVect0LinkObjId="SW-58619_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-43065_0" Pin1InfoVect1LinkObjId="SW-43063_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4452,-575 4452,-556 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19ab180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4452,-520 4452,-503 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="11065@0" ObjectIDZND0="g_19abe00@0" Pin0InfoVect0LinkObjId="g_19abe00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58619_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4452,-520 4452,-503 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a3bb90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4133,-586 4155,-586 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="7208@x" ObjectIDND1="7207@x" ObjectIDZND0="7210@0" Pin0InfoVect0LinkObjId="SW-43060_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-43058_0" Pin1InfoVect1LinkObjId="SW-43057_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4133,-586 4155,-586 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a3c880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4191,-586 4210,-586 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="7210@1" ObjectIDZND0="g_1a3bdf0@0" Pin0InfoVect0LinkObjId="g_1a3bdf0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43060_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4191,-586 4210,-586 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a3d910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4133,-633 4133,-652 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="7208@1" ObjectIDZND0="19356@0" Pin0InfoVect0LinkObjId="g_1a82af0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43058_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4133,-633 4133,-652 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a3db00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4133,-445 4133,-479 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="7209@0" Pin0InfoVect0LinkObjId="SW-43059_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1728b70_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4133,-445 4133,-479 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a3dcf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4133,-515 4133,-535 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="7209@1" ObjectIDZND0="7207@0" Pin0InfoVect0LinkObjId="SW-43057_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43059_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4133,-515 4133,-535 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19c5e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4986,-743 4986,-773 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="11076@x" ObjectIDND1="7230@x" ObjectIDZND0="g_1a43df0@0" Pin0InfoVect0LinkObjId="g_1a43df0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-58622_0" Pin1InfoVect1LinkObjId="SW-43084_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4986,-743 4986,-773 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19c6930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4964,-743 4986,-743 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="11076@1" ObjectIDZND0="g_1a43df0@0" ObjectIDZND1="7230@x" Pin0InfoVect0LinkObjId="g_1a43df0_0" Pin0InfoVect1LinkObjId="SW-43084_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58622_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4964,-743 4986,-743 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19c6b90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4986,-743 4986,-719 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_1a43df0@0" ObjectIDND1="11076@x" ObjectIDZND0="7230@1" Pin0InfoVect0LinkObjId="SW-43084_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1a43df0_0" Pin1InfoVect1LinkObjId="SW-58622_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4986,-743 4986,-719 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19c9040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3761,-740 3761,-770 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="11079@x" ObjectIDND1="7202@x" ObjectIDZND0="g_19c6df0@0" Pin0InfoVect0LinkObjId="g_19c6df0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-58607_0" Pin1InfoVect1LinkObjId="SW-43052_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3761,-740 3761,-770 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19c9b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3739,-742 3761,-742 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="11079@1" ObjectIDZND0="g_19c6df0@0" ObjectIDZND1="7202@x" Pin0InfoVect0LinkObjId="g_19c6df0_0" Pin0InfoVect1LinkObjId="SW-43052_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58607_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3739,-742 3761,-742 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19c9d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3761,-742 3761,-718 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_19c6df0@0" ObjectIDND1="11079@x" ObjectIDZND0="7202@1" Pin0InfoVect0LinkObjId="SW-43052_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_19c6df0_0" Pin1InfoVect1LinkObjId="SW-58607_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3761,-742 3761,-718 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19dba10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4872,-448 4872,-475 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="7225@0" Pin0InfoVect0LinkObjId="SW-43079_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1728b70_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4872,-448 4872,-475 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19dbc70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5064,-448 5064,-475 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="7221@0" Pin0InfoVect0LinkObjId="SW-43075_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1728b70_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5064,-448 5064,-475 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_198ae70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3589,-597 3589,-585 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="7204@0" ObjectIDZND0="7206@x" ObjectIDZND1="7203@x" Pin0InfoVect0LinkObjId="SW-43056_0" Pin0InfoVect1LinkObjId="SW-43053_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43054_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3589,-597 3589,-585 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_198b060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3589,-585 3589,-562 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="7206@x" ObjectIDND1="7204@x" ObjectIDZND0="7203@1" Pin0InfoVect0LinkObjId="SW-43053_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-43056_0" Pin1InfoVect1LinkObjId="SW-43054_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3589,-585 3589,-562 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_198c070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3766,-596 3766,-584 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="7200@0" ObjectIDZND0="11064@x" ObjectIDZND1="7199@x" Pin0InfoVect0LinkObjId="SW-58606_0" Pin0InfoVect1LinkObjId="SW-43049_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43050_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3766,-596 3766,-584 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_198c2d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3766,-584 3766,-561 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="11064@x" ObjectIDND1="7200@x" ObjectIDZND0="7199@1" Pin0InfoVect0LinkObjId="SW-43049_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-58606_0" Pin1InfoVect1LinkObjId="SW-43050_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3766,-584 3766,-561 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_198d3d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3956,-597 3956,-586 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="11057@0" ObjectIDZND0="11056@x" ObjectIDZND1="11059@x" Pin0InfoVect0LinkObjId="SW-58610_0" Pin0InfoVect1LinkObjId="SW-58629_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58625_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3956,-597 3956,-586 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_198d630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3956,-586 3956,-562 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="11056@x" ObjectIDND1="11057@x" ObjectIDZND0="11059@1" Pin0InfoVect0LinkObjId="SW-58629_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-58610_0" Pin1InfoVect1LinkObjId="SW-58625_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3956,-586 3956,-562 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_198e730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4133,-597 4133,-586 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="7208@0" ObjectIDZND0="7210@x" ObjectIDZND1="7207@x" Pin0InfoVect0LinkObjId="SW-43060_0" Pin0InfoVect1LinkObjId="SW-43057_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43058_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4133,-597 4133,-586 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_198e990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4133,-586 4133,-562 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="7210@x" ObjectIDND1="7208@x" ObjectIDZND0="7207@1" Pin0InfoVect0LinkObjId="SW-43057_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-43060_0" Pin1InfoVect1LinkObjId="SW-43058_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4133,-586 4133,-562 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_198fcb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4872,-593 4872,-584 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="7224@0" ObjectIDZND0="7226@x" ObjectIDZND1="7226@x" ObjectIDZND2="7224@x" Pin0InfoVect0LinkObjId="SW-43080_0" Pin0InfoVect1LinkObjId="SW-43080_0" Pin0InfoVect2LinkObjId="SW-43078_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43078_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4872,-593 4872,-584 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_198ff10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4872,-584 4872,-558 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="breaker" ObjectIDND0="7226@x" ObjectIDND1="7224@x" ObjectIDND2="7226@x" ObjectIDZND0="7223@1" Pin0InfoVect0LinkObjId="SW-43077_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-43080_0" Pin1InfoVect1LinkObjId="SW-43078_0" Pin1InfoVect2LinkObjId="SW-43080_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4872,-584 4872,-558 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1990c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5064,-593 5064,-584 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="7220@0" ObjectIDZND0="7222@x" ObjectIDZND1="7222@x" ObjectIDZND2="7220@x" Pin0InfoVect0LinkObjId="SW-43076_0" Pin0InfoVect1LinkObjId="SW-43076_0" Pin0InfoVect2LinkObjId="SW-43074_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43074_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5064,-593 5064,-584 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1990e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5064,-584 5064,-558 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="breaker" ObjectIDND0="7222@x" ObjectIDND1="7220@x" ObjectIDND2="7222@x" ObjectIDZND0="7219@1" Pin0InfoVect0LinkObjId="SW-43073_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-43076_0" Pin1InfoVect1LinkObjId="SW-43074_0" Pin1InfoVect2LinkObjId="SW-43076_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5064,-584 5064,-558 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1991970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4517,-599 4517,-584 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="11061@0" ObjectIDZND0="11060@x" ObjectIDZND1="11063@x" Pin0InfoVect0LinkObjId="SW-58623_0" Pin0InfoVect1LinkObjId="SW-58630_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58627_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4517,-599 4517,-584 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1991bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4517,-584 4517,-564 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="11060@x" ObjectIDND1="11061@x" ObjectIDZND0="11063@1" Pin0InfoVect0LinkObjId="SW-58630_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-58623_0" Pin1InfoVect1LinkObjId="SW-58627_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4517,-584 4517,-564 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1992cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4692,-596 4692,-584 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="7228@0" ObjectIDZND0="11066@x" ObjectIDZND1="7227@x" Pin0InfoVect0LinkObjId="SW-58624_0" Pin0InfoVect1LinkObjId="SW-43081_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43082_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4692,-596 4692,-584 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1992f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4692,-584 4692,-561 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="11066@x" ObjectIDND1="7228@x" ObjectIDZND0="7227@1" Pin0InfoVect0LinkObjId="SW-43081_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-58624_0" Pin1InfoVect1LinkObjId="SW-43082_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4692,-584 4692,-561 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19b1700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5064,-584 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="breaker" EndDevType0="switch" EndDevType1="switch" EndDevType2="breaker" ObjectIDND0="7222@x" ObjectIDND1="7220@x" ObjectIDND2="7219@x" ObjectIDZND0="7222@x" ObjectIDZND1="7220@x" ObjectIDZND2="7219@x" Pin0InfoVect0LinkObjId="SW-43076_0" Pin0InfoVect1LinkObjId="SW-43074_0" Pin0InfoVect2LinkObjId="SW-43073_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-43076_0" Pin1InfoVect1LinkObjId="SW-43074_0" Pin1InfoVect2LinkObjId="SW-43073_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5064,-584 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19b1960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4872,-584 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="breaker" EndDevType0="switch" EndDevType1="switch" EndDevType2="breaker" ObjectIDND0="7226@x" ObjectIDND1="7224@x" ObjectIDND2="7223@x" ObjectIDZND0="7226@x" ObjectIDZND1="7224@x" ObjectIDZND2="7223@x" Pin0InfoVect0LinkObjId="SW-43080_0" Pin0InfoVect1LinkObjId="SW-43078_0" Pin0InfoVect2LinkObjId="SW-43077_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-43080_0" Pin1InfoVect1LinkObjId="SW-43078_0" Pin1InfoVect2LinkObjId="SW-43077_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4872,-584 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19b6f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4032,-838 4032,-803 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="0@1" ObjectIDZND0="11081@x" ObjectIDZND1="7211@x" Pin0InfoVect0LinkObjId="SW-58609_0" Pin0InfoVect1LinkObjId="SW-43061_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1728b70_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4032,-838 4032,-803 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19b79a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4054,-803 4032,-803 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" EndDevType1="breaker" ObjectIDND0="11081@0" ObjectIDZND0="0@x" ObjectIDZND1="7211@x" Pin0InfoVect0LinkObjId="g_1728b70_0" Pin0InfoVect1LinkObjId="SW-43061_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58609_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4054,-803 4032,-803 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19b7c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4032,-803 4032,-781 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="11081@x" ObjectIDZND0="7211@1" Pin0InfoVect0LinkObjId="SW-43061_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1728b70_0" Pin1InfoVect1LinkObjId="SW-58609_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4032,-803 4032,-781 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19b8060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4634,-838 4634,-803 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="0@1" ObjectIDZND0="11078@x" ObjectIDZND1="7217@x" Pin0InfoVect0LinkObjId="SW-58621_0" Pin0InfoVect1LinkObjId="SW-43071_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1728b70_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4634,-838 4634,-803 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19b8b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4656,-803 4634,-803 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" EndDevType1="breaker" ObjectIDND0="11078@0" ObjectIDZND0="0@x" ObjectIDZND1="7217@x" Pin0InfoVect0LinkObjId="g_1728b70_0" Pin0InfoVect1LinkObjId="SW-43071_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58621_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4656,-803 4634,-803 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19b8dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4634,-803 4634,-781 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="11078@x" ObjectIDZND0="7217@1" Pin0InfoVect0LinkObjId="SW-43071_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1728b70_0" Pin1InfoVect1LinkObjId="SW-58621_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4634,-803 4634,-781 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="19356" cx="3761" cy="-652" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19356" cx="4032" cy="-652" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19356" cx="4310" cy="-652" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19356" cx="3589" cy="-652" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19356" cx="3766" cy="-652" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19356" cx="3956" cy="-652" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19356" cx="4133" cy="-652" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19357" cx="4986" cy="-651" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19357" cx="4634" cy="-651" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19357" cx="4517" cy="-651" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19357" cx="4692" cy="-651" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19357" cx="4872" cy="-651" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19357" cx="5064" cy="-651" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19357" cx="4452" cy="-651" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-37328" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3397.000000 -1085.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5910" ObjectName="DYN-CX_XHK"/>
     <cge:Meas_Ref ObjectId="37328"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1ae6220" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3658.000000 -903.000000) translate(0,15)">10kV I段母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a81040" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3544.000000 -371.000000) translate(0,15)">10kV滨河线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a28190" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3718.000000 -371.000000) translate(0,15)">10kV西郊线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a23510" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3908.000000 -371.000000) translate(0,15)">10kV排涝站线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a23ea0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4862.000000 -903.000000) translate(0,15)">10kV II段母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19fd630" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4468.000000 -371.000000) translate(0,15)">10kV航空路线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a69fb0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4647.000000 -371.000000) translate(0,15)">10kV东郊线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a7ceb0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4820.000000 -371.000000) translate(0,15)">10kV城东线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a7fb50" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5010.000000 -368.500000) translate(0,12)">10kV航空路Ⅰ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19ac850" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3990.000000 -903.000000) translate(0,15)">10kV 1号变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19ad340" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4593.000000 -903.000000) translate(0,15)">10kV 2号变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a3cae0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4081.000000 -371.000000) translate(0,15)">10kV新基地线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19c9ff0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3771.000000 -707.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19ca7e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4044.000000 -775.000000) translate(0,12)">051</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19cad00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4042.000000 -702.000000) translate(0,12)">0511</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19caf80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4647.000000 -774.000000) translate(0,12)">061</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19cb4a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4645.000000 -701.000000) translate(0,12)">0611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19cb720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4997.000000 -707.000000) translate(0,12)">0902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19cba60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3598.000000 -553.000000) translate(0,12)">055</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a71dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3596.000000 -619.000000) translate(0,12)">0551</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a71fd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3596.000000 -501.000000) translate(0,12)">0556</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a72210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3607.500000 -577.000000) translate(0,12)">05517</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a72550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5073.000000 -552.000000) translate(0,12)">065</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a729b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5071.000000 -500.000000) translate(0,12)">0656</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a72bf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5071.000000 -618.000000) translate(0,12)">0651</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a72e30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5082.500000 -575.000000) translate(0,12)">06517</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a73070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4890.500000 -575.000000) translate(0,12)">06417</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a73590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4879.000000 -618.000000) translate(0,12)">0641</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a73810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4879.000000 -500.000000) translate(0,12)">0646</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a73a50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4881.000000 -552.000000) translate(0,12)">064</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a73c90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4701.000000 -555.000000) translate(0,12)">063</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a815d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4699.000000 -503.000000) translate(0,12)">0636</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a81b10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4699.000000 -621.000000) translate(0,12)">0631</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a81d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4366.000000 -599.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a81f90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4271.000000 -624.000000) translate(0,12)">0121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a821d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4459.000000 -625.000000) translate(0,12)">0122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a82410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4260.000000 -546.000000) translate(0,12)">01217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a82650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4142.000000 -556.000000) translate(0,12)">052</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a82890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4140.000000 -622.000000) translate(0,12)">0521</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a75650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4140.000000 -504.000000) translate(0,12)">0526</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a75870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4152.000000 -577.000000) translate(0,12)">05217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a75ab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3775.000000 -555.000000) translate(0,12)">054</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a75cf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3773.000000 -621.000000) translate(0,12)">0541</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a75f30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3773.000000 -503.000000) translate(0,12)">0546</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a76170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3561.000000 -677.000000) translate(0,15)">10kV I段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a763c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5063.000000 -677.000000) translate(0,15)">10kV II段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1939000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3135.000000 -587.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1939000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3135.000000 -587.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1939000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3135.000000 -587.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1939000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3135.000000 -587.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1939000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3135.000000 -587.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1939000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3135.000000 -587.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1939000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3135.000000 -587.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1939000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3135.000000 -587.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1939000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3135.000000 -587.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1939000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3135.000000 -587.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1939000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3135.000000 -587.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1939000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3135.000000 -587.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1939000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3135.000000 -587.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1939000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3135.000000 -587.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1939000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3135.000000 -587.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1939000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3135.000000 -587.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1939000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3135.000000 -587.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1939000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3135.000000 -587.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_193b8c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3134.000000 -1025.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_193b8c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3134.000000 -1025.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_193b8c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3134.000000 -1025.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_193b8c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3134.000000 -1025.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_193b8c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3134.000000 -1025.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_193b8c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3134.000000 -1025.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_193b8c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3134.000000 -1025.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_1a55d20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3257.500000 -1165.500000) translate(0,16)">小河口开闭所</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a57c60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3702.000000 -767.000000) translate(0,12)">09017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a57f90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4068.000000 -755.000000) translate(0,12)">05117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a58140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4063.000000 -827.000000) translate(0,12)">05167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19d91d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3784.500000 -577.000000) translate(0,12)">05417</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19d93e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4455.000000 -542.000000) translate(0,12)">01227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19d9620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4710.500000 -575.000000) translate(0,12)">06317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19d9860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4675.000000 -754.000000) translate(0,12)">06117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19d9aa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4666.000000 -825.000000) translate(0,12)">06167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19d9ce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4925.000000 -766.000000) translate(0,12)">09027</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19dbed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4052.000000 -876.000000) translate(0,12)">315kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_198a4b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4652.000000 -876.000000) translate(0,12)">315kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19b5c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4530.000000 -557.000000) translate(0,12)">062</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19b6490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3965.000000 -556.000000) translate(0,12)">053</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19b9030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3963.000000 -622.000000) translate(0,12)">0531</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19b94e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3980.000000 -579.000000) translate(0,12)">05317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19b9720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3963.000000 -504.000000) translate(0,12)">0536</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19b9960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4524.000000 -624.000000) translate(0,12)">0621</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19b9ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4536.000000 -578.000000) translate(0,12)">06217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19b9de0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4524.000000 -506.000000) translate(0,12)">0626</text>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-43053">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3580.000000 -527.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7203" ObjectName="SW-CX_XHK.CX_XHK_055BK"/>
     <cge:Meas_Ref ObjectId="43053"/>
    <cge:TPSR_Ref TObjectID="7203"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43061">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4023.000000 -746.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7211" ObjectName="SW-CX_XHK.CX_XHK_051BK"/>
     <cge:Meas_Ref ObjectId="43061"/>
    <cge:TPSR_Ref TObjectID="7211"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43049">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3757.000000 -526.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7199" ObjectName="SW-CX_XHK.CX_XHK_054BK"/>
     <cge:Meas_Ref ObjectId="43049"/>
    <cge:TPSR_Ref TObjectID="7199"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-58629">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3947.000000 -527.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11059" ObjectName="SW-CX_XHK.CX_XHK_053BK"/>
     <cge:Meas_Ref ObjectId="58629"/>
    <cge:TPSR_Ref TObjectID="11059"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-58630">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4508.000000 -529.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11063" ObjectName="SW-CX_XHK.CX_XHK_062BK"/>
     <cge:Meas_Ref ObjectId="58630"/>
    <cge:TPSR_Ref TObjectID="11063"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43071">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4625.000000 -746.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7217" ObjectName="SW-CX_XHK.CX_XHK_061BK"/>
     <cge:Meas_Ref ObjectId="43071"/>
    <cge:TPSR_Ref TObjectID="7217"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43081">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4683.000000 -526.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7227" ObjectName="SW-CX_XHK.CX_XHK_063BK"/>
     <cge:Meas_Ref ObjectId="43081"/>
    <cge:TPSR_Ref TObjectID="7227"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43077">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4863.000000 -523.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7223" ObjectName="SW-CX_XHK.CX_XHK_064BK"/>
     <cge:Meas_Ref ObjectId="43077"/>
    <cge:TPSR_Ref TObjectID="7223"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43073">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5055.000000 -523.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7219" ObjectName="SW-CX_XHK.CX_XHK_065BK"/>
     <cge:Meas_Ref ObjectId="43073"/>
    <cge:TPSR_Ref TObjectID="7219"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43063">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4400.000000 -566.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7213" ObjectName="SW-CX_XHK.CX_XHK_012BK"/>
     <cge:Meas_Ref ObjectId="43063"/>
    <cge:TPSR_Ref TObjectID="7213"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43057">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4124.000000 -527.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7207" ObjectName="SW-CX_XHK.CX_XHK_052BK"/>
     <cge:Meas_Ref ObjectId="43057"/>
    <cge:TPSR_Ref TObjectID="7207"/></metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4019.000000 -833.000000)" xlink:href="#transformer2:shape11_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4019.000000 -833.000000)" xlink:href="#transformer2:shape11_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4621.000000 -833.000000)" xlink:href="#transformer2:shape11_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4621.000000 -833.000000)" xlink:href="#transformer2:shape11_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3225.500000 -1117.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="139" x="3237" y="-1176"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="139" x="3237" y="-1176"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3189" y="-1193"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3189" y="-1193"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="5073" y="-552"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="5073" y="-552"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4881" y="-552"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4881" y="-552"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4701" y="-555"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4701" y="-555"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4530" y="-557"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4530" y="-557"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="4366" y="-599"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="4366" y="-599"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4142" y="-556"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4142" y="-556"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="3965" y="-556"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="3965" y="-556"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="3775" y="-555"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="3775" y="-555"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="3598" y="-553"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="3598" y="-553"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4044" y="-775"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4044" y="-775"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4647" y="-774"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4647" y="-774"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a76f90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3987.000000 954.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a782f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3976.000000 939.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1936890" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4001.000000 924.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19372f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4589.000000 954.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19375f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4578.000000 939.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1937830" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4603.000000 924.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1937c50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3537.000000 338.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1937f10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3526.000000 323.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1938150" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3551.000000 308.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19d6e70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3502.000000 761.000000) translate(0,12)">Ua（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19d78c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3508.500000 715.000000) translate(0,12)">U0（V）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19d7bf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3494.000000 701.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19d7fb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3502.000000 730.000000) translate(0,12)">Uc（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19d8440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3502.000000 745.000000) translate(0,12)">Ub（kV）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19d8780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5061.000000 761.000000) translate(0,12)">Ua（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19d8990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5067.500000 715.000000) translate(0,12)">U0（V）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19d8ba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5053.000000 701.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19d8db0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5061.000000 730.000000) translate(0,12)">Uc（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19d8fc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5061.000000 745.000000) translate(0,12)">Ub（kV）：</text>
   <metadata/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1a43df0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4955.000000 -854.000000)" xlink:href="#lightningRod:shape74"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19c6df0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3730.000000 -851.000000)" xlink:href="#lightningRod:shape74"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="cx_配调_配网接线图开闭所.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="139" x="3237" y="-1176"/></g>
   <g href="cx_配调_配网接线图开闭所.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3189" y="-1193"/></g>
   <g href="10kV小河口开闭所10kV航空路Ⅰ回线065断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="5073" y="-552"/></g>
   <g href="10kV小河口开闭所10kV城东线064断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4881" y="-552"/></g>
   <g href="10kV小河口开闭所10kV东郊线063断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4701" y="-555"/></g>
   <g href="10kV小河口开闭所10kV航空路线062断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4530" y="-557"/></g>
   <g href="10kV小河口开闭所10kV分段012断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="4366" y="-599"/></g>
   <g href="10kV小河口开闭所10kV新基地线052断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4142" y="-556"/></g>
   <g href="10kV小河口开闭所10kV排涝站线053断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="3965" y="-556"/></g>
   <g href="10kV小河口开闭所10kV西郊线054断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="3775" y="-555"/></g>
   <g href="10kV小河口开闭所10kV滨河线055断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="3598" y="-553"/></g>
   <g href="10kV小河口开闭所10kV1号变051断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4044" y="-775"/></g>
   <g href="10kV小河口开闭所10kV2号变061断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4647" y="-774"/></g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_XHK.CX_XHK_9ⅠM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3567,-652 4352,-652 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="19356" ObjectName="BS-CX_XHK.CX_XHK_9ⅠM"/>
    <cge:TPSR_Ref TObjectID="19356"/></metadata>
   <polyline fill="none" opacity="0" points="3567,-652 4352,-652 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_XHK.CX_XHK_9ⅡM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4416,-651 5181,-651 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="19357" ObjectName="BS-CX_XHK.CX_XHK_9ⅡM"/>
    <cge:TPSR_Ref TObjectID="19357"/></metadata>
   <polyline fill="none" opacity="0" points="4416,-651 5181,-651 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-89228" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3582.000000 -761.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="89228" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19356"/>
     <cge:Term_Ref ObjectID="10443"/>
    <cge:TPSR_Ref TObjectID="19356"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-89229" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3582.000000 -761.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="89229" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19356"/>
     <cge:Term_Ref ObjectID="10443"/>
    <cge:TPSR_Ref TObjectID="19356"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-89230" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3582.000000 -761.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="89230" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19356"/>
     <cge:Term_Ref ObjectID="10443"/>
    <cge:TPSR_Ref TObjectID="19356"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-89232" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3582.000000 -761.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="89232" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19356"/>
     <cge:Term_Ref ObjectID="10443"/>
    <cge:TPSR_Ref TObjectID="19356"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-89231" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3582.000000 -761.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="89231" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19356"/>
     <cge:Term_Ref ObjectID="10443"/>
    <cge:TPSR_Ref TObjectID="19356"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-89244" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5143.000000 -761.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="89244" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19357"/>
     <cge:Term_Ref ObjectID="10444"/>
    <cge:TPSR_Ref TObjectID="19357"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-89245" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5143.000000 -761.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="89245" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19357"/>
     <cge:Term_Ref ObjectID="10444"/>
    <cge:TPSR_Ref TObjectID="19357"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-89246" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5143.000000 -761.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="89246" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19357"/>
     <cge:Term_Ref ObjectID="10444"/>
    <cge:TPSR_Ref TObjectID="19357"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-89248" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5143.000000 -761.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="89248" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19357"/>
     <cge:Term_Ref ObjectID="10444"/>
    <cge:TPSR_Ref TObjectID="19357"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-89247" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5143.000000 -761.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="89247" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19357"/>
     <cge:Term_Ref ObjectID="10444"/>
    <cge:TPSR_Ref TObjectID="19357"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-42989" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3592.000000 -338.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42989" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7203"/>
     <cge:Term_Ref ObjectID="10469"/>
    <cge:TPSR_Ref TObjectID="7203"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-42990" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3592.000000 -338.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42990" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7203"/>
     <cge:Term_Ref ObjectID="10469"/>
    <cge:TPSR_Ref TObjectID="7203"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-42985" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3592.000000 -338.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42985" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7203"/>
     <cge:Term_Ref ObjectID="10469"/>
    <cge:TPSR_Ref TObjectID="7203"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-42978" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3747.000000 -338.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42978" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7199"/>
     <cge:Term_Ref ObjectID="10461"/>
    <cge:TPSR_Ref TObjectID="7199"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-42979" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3747.000000 -338.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42979" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7199"/>
     <cge:Term_Ref ObjectID="10461"/>
    <cge:TPSR_Ref TObjectID="7199"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-42974" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3747.000000 -338.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42974" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7199"/>
     <cge:Term_Ref ObjectID="10461"/>
    <cge:TPSR_Ref TObjectID="7199"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-89238" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3939.000000 -338.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="89238" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11059"/>
     <cge:Term_Ref ObjectID="10453"/>
    <cge:TPSR_Ref TObjectID="11059"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-89239" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3939.000000 -338.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="89239" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11059"/>
     <cge:Term_Ref ObjectID="10453"/>
    <cge:TPSR_Ref TObjectID="11059"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-89235" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3939.000000 -338.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="89235" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11059"/>
     <cge:Term_Ref ObjectID="10453"/>
    <cge:TPSR_Ref TObjectID="11059"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-42997" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4119.000000 -338.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42997" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7207"/>
     <cge:Term_Ref ObjectID="10445"/>
    <cge:TPSR_Ref TObjectID="7207"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-42998" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4119.000000 -338.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42998" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7207"/>
     <cge:Term_Ref ObjectID="10445"/>
    <cge:TPSR_Ref TObjectID="7207"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-42993" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4119.000000 -338.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42993" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7207"/>
     <cge:Term_Ref ObjectID="10445"/>
    <cge:TPSR_Ref TObjectID="7207"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-43013" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4362.000000 -558.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43013" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7213"/>
     <cge:Term_Ref ObjectID="15399"/>
    <cge:TPSR_Ref TObjectID="7213"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-43014" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4362.000000 -558.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43014" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7213"/>
     <cge:Term_Ref ObjectID="15399"/>
    <cge:TPSR_Ref TObjectID="7213"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-43009" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4362.000000 -558.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43009" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7213"/>
     <cge:Term_Ref ObjectID="15399"/>
    <cge:TPSR_Ref TObjectID="7213"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-58498" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4503.000000 -338.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="58498" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11063"/>
     <cge:Term_Ref ObjectID="10477"/>
    <cge:TPSR_Ref TObjectID="11063"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-58499" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4503.000000 -338.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="58499" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11063"/>
     <cge:Term_Ref ObjectID="10477"/>
    <cge:TPSR_Ref TObjectID="11063"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-58494" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4503.000000 -338.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="58494" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11063"/>
     <cge:Term_Ref ObjectID="10477"/>
    <cge:TPSR_Ref TObjectID="11063"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-43037" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4677.000000 -338.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43037" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7227"/>
     <cge:Term_Ref ObjectID="10485"/>
    <cge:TPSR_Ref TObjectID="7227"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-43038" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4677.000000 -338.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43038" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7227"/>
     <cge:Term_Ref ObjectID="10485"/>
    <cge:TPSR_Ref TObjectID="7227"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-43033" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4677.000000 -338.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43033" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7227"/>
     <cge:Term_Ref ObjectID="10485"/>
    <cge:TPSR_Ref TObjectID="7227"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-89256" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5048.000000 -338.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="89256" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7219"/>
     <cge:Term_Ref ObjectID="10501"/>
    <cge:TPSR_Ref TObjectID="7219"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-89257" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5048.000000 -338.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="89257" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7219"/>
     <cge:Term_Ref ObjectID="10501"/>
    <cge:TPSR_Ref TObjectID="7219"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-89253" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5048.000000 -338.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="89253" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7219"/>
     <cge:Term_Ref ObjectID="10501"/>
    <cge:TPSR_Ref TObjectID="7219"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-43029" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4853.000000 -338.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43029" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7223"/>
     <cge:Term_Ref ObjectID="10493"/>
    <cge:TPSR_Ref TObjectID="7223"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-43030" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4853.000000 -338.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43030" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7223"/>
     <cge:Term_Ref ObjectID="10493"/>
    <cge:TPSR_Ref TObjectID="7223"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-43025" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4853.000000 -338.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43025" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7223"/>
     <cge:Term_Ref ObjectID="10493"/>
    <cge:TPSR_Ref TObjectID="7223"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-43021" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4646.000000 -953.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43021" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7217"/>
     <cge:Term_Ref ObjectID="15391"/>
    <cge:TPSR_Ref TObjectID="7217"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-43022" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4646.000000 -953.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43022" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7217"/>
     <cge:Term_Ref ObjectID="15391"/>
    <cge:TPSR_Ref TObjectID="7217"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-43017" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4646.000000 -953.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43017" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7217"/>
     <cge:Term_Ref ObjectID="15391"/>
    <cge:TPSR_Ref TObjectID="7217"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-43005" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4044.000000 -953.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43005" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7211"/>
     <cge:Term_Ref ObjectID="15383"/>
    <cge:TPSR_Ref TObjectID="7211"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-43006" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4044.000000 -953.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43006" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7211"/>
     <cge:Term_Ref ObjectID="15383"/>
    <cge:TPSR_Ref TObjectID="7211"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-43001" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4044.000000 -953.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43001" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7211"/>
     <cge:Term_Ref ObjectID="15383"/>
    <cge:TPSR_Ref TObjectID="7211"/></metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_XHK"/>
</svg>