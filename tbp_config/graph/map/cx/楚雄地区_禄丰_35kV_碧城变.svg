<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-101" aopId="256" id="thSvg" viewBox="3116 -1254 2170 1257">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="0" x2="12" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="lightningRod:shape123">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="2" y2="2"/>
    <ellipse cx="8" cy="8" rx="8.5" ry="7.5" stroke-width="0.155709"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="25" x2="20" y1="8" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="25" x2="20" y1="8" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="20" x2="20" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="8" x2="5" y1="7" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="10" x2="8" y1="9" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="8" x2="8" y1="4" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="14" x2="11" y1="18" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="16" x2="14" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="14" x2="14" y1="15" y2="18"/>
    <ellipse cx="19" cy="8" rx="8.5" ry="7.5" stroke-width="0.155709"/>
    <ellipse cx="14" cy="16" rx="9" ry="7.5" stroke-width="0.155709"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="59" x2="24" y1="7" y2="7"/>
    <rect height="12" stroke-width="1" width="26" x="18" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="1" x2="1" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="5" x2="5" y1="3" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="17" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="8" x2="8" y1="12" y2="1"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="6" y2="41"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="63" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="55" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="56" y2="56"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="9" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="1" y2="1"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
   </symbol>
   <symbol id="lightningRod:shape50">
    <polyline DF8003:Layer="PUBLIC" points="5,39 0,50 11,50 5,39 5,40 5,39 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.222222" x1="5" x2="5" y1="29" y2="33"/>
    <polyline DF8003:Layer="PUBLIC" points="5,25 0,14 11,14 5,25 5,24 5,25 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="59" y2="51"/>
   </symbol>
   <symbol id="lightningRod:shape133">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="77" x2="117" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="117" x2="117" y1="85" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="83" x2="117" y1="85" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="77" x2="77" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="34" x2="54" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="34" x2="54" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="44" x2="44" y1="20" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="10" x2="10" y1="2" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="0" x2="20" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="0" x2="20" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="10" x2="10" y1="20" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="77" x2="77" y1="2" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="67" x2="87" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="67" x2="87" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="77" x2="77" y1="20" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="77" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="44" x2="44" y1="2" y2="12"/>
    <ellipse cx="83" cy="103" rx="13" ry="12.5" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="79" x2="83" y1="89" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="83" x2="87" y1="85" y2="89"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="83" x2="83" y1="81" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="79" x2="83" y1="109" y2="105"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="83" x2="87" y1="105" y2="109"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="83" x2="83" y1="101" y2="105"/>
    <circle cx="83" cy="85" r="13" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="81" x2="85" y1="48" y2="48"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="83" x2="83" y1="73" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="74" x2="92" y1="55" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="80" x2="87" y1="52" y2="52"/>
   </symbol>
   <symbol id="load:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape19_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="50" y2="42"/>
    <rect height="4" stroke-width="1" width="19" x="7" y="26"/>
    <polyline fill="none" points="27,39 5,17 5,5 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="9" y1="41" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="42"/>
    <rect height="4" stroke-width="1" width="19" x="-15" y="26"/>
    <polyline fill="none" points="-16,39 6,17 6,5 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="2" y1="41" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="50" y2="42"/>
    <rect height="4" stroke-width="1" width="19" x="7" y="26"/>
    <polyline fill="none" points="27,39 5,17 5,5 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="9" y1="41" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="42"/>
    <rect height="4" stroke-width="1" width="19" x="-15" y="26"/>
    <polyline fill="none" points="-16,39 6,17 6,5 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="2" y1="41" y2="41"/>
   </symbol>
   <symbol id="transformer2:shape0_0">
    <circle cx="25" cy="29" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="17" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="34" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="17" x2="34" y1="18" y2="18"/>
   </symbol>
   <symbol id="transformer2:shape0_1">
    <ellipse cx="25" cy="60" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="24" y1="58" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="32" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="16" x2="24" y1="74" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape11_0">
    <ellipse cx="13" cy="34" rx="13" ry="12.5" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="40" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="36" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="32" y2="36"/>
   </symbol>
   <symbol id="transformer2:shape11_1">
    <circle cx="13" cy="16" r="13" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="20" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="16" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="12" y2="16"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">开关检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注1</text>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注2</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">禁止操作</text>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="78" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.246377 -0.000000 0.000000 -1.035714 2.739130 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="32" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(0.512795 -0.000000 0.000000 -1.035714 2.846957 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape28">
    
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(0,0,0)" height="1267" width="2180" x="3111" y="-1259"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(0,0,0)" stroke-width="1" width="360" x="3117" y="-1197"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(0,0,0)" stroke-width="1" width="360" x="3117" y="-597"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(0,0,0)" stroke-width="1" width="360" x="3117" y="-1077"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3862.000000 -1003.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3862.000000 -1095.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3903.000000 -1094.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3937.000000 -1163.000000)" xlink:href="#switch2:shape19-UnNor1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4901.000000 -1002.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4901.000000 -1094.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4942.000000 -1093.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4976.000000 -1162.000000)" xlink:href="#switch2:shape19-UnNor1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-78069">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4316.000000 -538.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16986" ObjectName="SW-LF_BC.LF_BC_4041SW"/>
     <cge:Meas_Ref ObjectId="78069"/>
    <cge:TPSR_Ref TObjectID="16986"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-78073">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4434.000000 -534.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16990" ObjectName="SW-LF_BC.LF_BC_4042SW"/>
     <cge:Meas_Ref ObjectId="78073"/>
    <cge:TPSR_Ref TObjectID="16990"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-77768">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4817.000000 -935.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16857" ObjectName="SW-LF_BC.LF_BC_3111SW"/>
     <cge:Meas_Ref ObjectId="77768"/>
    <cge:TPSR_Ref TObjectID="16857"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4864.000000 -875.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-77888">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4817.000000 -602.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16875" ObjectName="SW-LF_BC.LF_BC_4032SW"/>
     <cge:Meas_Ref ObjectId="77888"/>
    <cge:TPSR_Ref TObjectID="16875"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-77892">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3933.000000 -935.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16879" ObjectName="SW-LF_BC.LF_BC_3131SW"/>
     <cge:Meas_Ref ObjectId="77892"/>
    <cge:TPSR_Ref TObjectID="16879"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3980.000000 -875.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4593.000000 -539.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4243.000000 -538.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-78180">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4139.000000 -211.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17097" ObjectName="SW-LF_BC.LF_BC_4241SW"/>
     <cge:Meas_Ref ObjectId="78180"/>
    <cge:TPSR_Ref TObjectID="17097"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-78158">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4712.000000 -211.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17075" ObjectName="SW-LF_BC.LF_BC_4141SW"/>
     <cge:Meas_Ref ObjectId="78158"/>
    <cge:TPSR_Ref TObjectID="17075"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-77992">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4029.000000 -538.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16909" ObjectName="SW-LF_BC.LF_BC_4121SW"/>
     <cge:Meas_Ref ObjectId="77992"/>
    <cge:TPSR_Ref TObjectID="16909"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-77996">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4029.000000 -352.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16913" ObjectName="SW-LF_BC.LF_BC_4123SW"/>
     <cge:Meas_Ref ObjectId="77996"/>
    <cge:TPSR_Ref TObjectID="16913"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-77997">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4078.000000 -283.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16914" ObjectName="SW-LF_BC.LF_BC_4124SW"/>
     <cge:Meas_Ref ObjectId="77997"/>
    <cge:TPSR_Ref TObjectID="16914"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3736.000000 -283.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3602.000000 -283.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-77969">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3956.000000 -283.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16886" ObjectName="SW-LF_BC.LF_BC_4114SW"/>
     <cge:Meas_Ref ObjectId="77969"/>
    <cge:TPSR_Ref TObjectID="16886"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-78135">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 4844.000000 -536.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17052" ObjectName="SW-LF_BC.LF_BC_4231SW"/>
     <cge:Meas_Ref ObjectId="78135"/>
    <cge:TPSR_Ref TObjectID="17052"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-78109">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4995.000000 -536.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17026" ObjectName="SW-LF_BC.LF_BC_4221SW"/>
     <cge:Meas_Ref ObjectId="78109"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-78084">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5133.000000 -530.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17001" ObjectName="SW-LF_BC.LF_BC_4211SW"/>
     <cge:Meas_Ref ObjectId="78084"/>
    <cge:TPSR_Ref TObjectID="17001"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-77829">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4280.000000 -935.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16866" ObjectName="SW-LF_BC.LF_BC_3121SW"/>
     <cge:Meas_Ref ObjectId="77829"/>
    <cge:TPSR_Ref TObjectID="16866"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4327.000000 -875.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4817.000000 -711.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-77825">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4280.000000 -601.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16862" ObjectName="SW-LF_BC.LF_BC_4022SW"/>
     <cge:Meas_Ref ObjectId="77825"/>
    <cge:TPSR_Ref TObjectID="16862"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-77824">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4280.000000 -710.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16861" ObjectName="SW-LF_BC.LF_BC_4021SW"/>
     <cge:Meas_Ref ObjectId="77824"/>
    <cge:TPSR_Ref TObjectID="16861"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-77762">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3933.000000 -603.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16852" ObjectName="SW-LF_BC.LF_BC_4012SW"/>
     <cge:Meas_Ref ObjectId="77762"/>
    <cge:TPSR_Ref TObjectID="16852"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-77761">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3933.000000 -712.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16851" ObjectName="SW-LF_BC.LF_BC_4011SW"/>
     <cge:Meas_Ref ObjectId="77761"/>
    <cge:TPSR_Ref TObjectID="16851"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3553.000000 -537.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-78017">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3687.000000 -538.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16934" ObjectName="SW-LF_BC.LF_BC_4131SW"/>
     <cge:Meas_Ref ObjectId="78017"/>
    <cge:TPSR_Ref TObjectID="16934"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3809.000000 -538.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-77964">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3907.000000 -538.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16881" ObjectName="SW-LF_BC.LF_BC_4111SW"/>
     <cge:Meas_Ref ObjectId="77964"/>
    <cge:TPSR_Ref TObjectID="16881"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-79379">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4891.000000 -281.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17424" ObjectName="SW-LF_BC.LF_BC_4234SW"/>
     <cge:Meas_Ref ObjectId="79379"/>
    <cge:TPSR_Ref TObjectID="17424"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-79355">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5046.000000 -283.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17422" ObjectName="SW-LF_BC.LF_BC_4224SW"/>
     <cge:Meas_Ref ObjectId="79355"/>
    <cge:TPSR_Ref TObjectID="17422"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-78088">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5133.000000 -347.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17005" ObjectName="SW-LF_BC.LF_BC_4213SW"/>
     <cge:Meas_Ref ObjectId="78088"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-79354">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4996.000000 -353.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17421" ObjectName="SW-LF_BC.LF_BC_4223SW"/>
     <cge:Meas_Ref ObjectId="79354"/>
    <cge:TPSR_Ref TObjectID="17421"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-79378">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4844.000000 -354.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17423" ObjectName="SW-LF_BC.LF_BC_4233SW"/>
     <cge:Meas_Ref ObjectId="79378"/>
    <cge:TPSR_Ref TObjectID="17423"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3553.000000 -352.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3687.000000 -352.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-77968">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3907.000000 -352.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16885" ObjectName="SW-LF_BC.LF_BC_4113SW"/>
     <cge:Meas_Ref ObjectId="77968"/>
    <cge:TPSR_Ref TObjectID="16885"/></metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" runFlow="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3871,-1152 3871,-1230 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="3871,-1152 3871,-1230 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" runFlow="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4910,-1167 4910,-1242 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4910,-1167 4910,-1242 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4801.515038 -762.000000)" xlink:href="#transformer2:shape0_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4801.515038 -762.000000)" xlink:href="#transformer2:shape0_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3917.515038 -762.000000)" xlink:href="#transformer2:shape0_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3917.515038 -762.000000)" xlink:href="#transformer2:shape0_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3805.000000 -418.000000)" xlink:href="#transformer2:shape11_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3805.000000 -418.000000)" xlink:href="#transformer2:shape11_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4264.515038 -762.000000)" xlink:href="#transformer2:shape0_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4264.515038 -762.000000)" xlink:href="#transformer2:shape0_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_28a2f90">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3972.500000 -1154.500000)" xlink:href="#lightningRod:shape123"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28a4110">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3798.000000 -1183.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28aed70">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5011.500000 -1153.500000)" xlink:href="#lightningRod:shape123"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28b0280">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4838.000000 -1182.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28b8ab0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4484.500000 -487.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28d3820">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4593.000000 -480.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28d4960">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4550.000000 -461.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28d5650">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4588.000000 -439.000000)" xlink:href="#lightningRod:shape123"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28d9890">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4243.000000 -478.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28da9f0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4200.000000 -460.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28db6c0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4239.000000 -437.000000)" xlink:href="#lightningRod:shape123"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28de1b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4143.000000 -434.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28e40b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4105.000000 -29.000000)" xlink:href="#lightningRod:shape133"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_289e9b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4676.000000 -32.000000)" xlink:href="#lightningRod:shape133"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28eabf0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4716.000000 -434.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28f2800">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3809.000000 -482.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28fa240">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4078.916667 -492.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28faff0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4032.916667 -404.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29015f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4079.166667 -178.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2905330">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3736.250000 -492.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29060e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3691.250000 -404.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2909f40">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3737.500000 -178.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_290e1f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3602.250000 -492.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_290efa0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3557.250000 -404.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2913130">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3603.500000 -178.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2916fb0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3956.250000 -492.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2917d60">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3911.250000 -404.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_291bbb0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3957.500000 -178.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29e90a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4892.500000 -490.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29e9e50">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4847.500000 -405.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29eb4e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4891.500000 -177.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29f1a90">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5044.583333 -490.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29f2840">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4999.583333 -404.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29f4130">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5047.833333 -179.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29fa070">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5181.000000 -484.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29fae20">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5137.000000 -399.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29fc750">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5182.000000 -264.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 0.000000 0.000000 2.335135 3236.000000 -1118.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="ConnectNode_Layer"/><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259" style="fill-opacity:0">
    <a>
     
     <rect height="41" qtmmishow="hidden" width="138" x="3248" y="-1177"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="3248" y="-1177"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124" style="fill-opacity:0">
    <a>
     
     <rect height="69" qtmmishow="hidden" width="77" x="3199" y="-1194"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3199" y="-1194"/></g>
  </g><g id="MotifButton_Layer">
   <g href="lf_索引_接线图.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="3248" y="-1177"/></g>
   <g href="cx_索引_接线图_客户变110.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3199" y="-1194"/></g>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3862.000000 -1050.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4901.000000 -1049.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-78070">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4433.500000 -490.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16987" ObjectName="SW-LF_BC.LF_BC_404BK"/>
     <cge:Meas_Ref ObjectId="78070"/>
    <cge:TPSR_Ref TObjectID="16987"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-77750">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4816.515038 -879.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16844" ObjectName="SW-LF_BC.LF_BC_311BK"/>
     <cge:Meas_Ref ObjectId="77750"/>
    <cge:TPSR_Ref TObjectID="16844"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-77878">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4816.515038 -654.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16869" ObjectName="SW-LF_BC.LF_BC_403BK"/>
     <cge:Meas_Ref ObjectId="77878"/>
    <cge:TPSR_Ref TObjectID="16869"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-77877">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3932.515038 -879.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16868" ObjectName="SW-LF_BC.LF_BC_313BK"/>
     <cge:Meas_Ref ObjectId="77877"/>
    <cge:TPSR_Ref TObjectID="16868"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-78181">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4139.000000 -165.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17098" ObjectName="SW-LF_BC.LF_BC_424BK"/>
     <cge:Meas_Ref ObjectId="78181"/>
    <cge:TPSR_Ref TObjectID="17098"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-78159">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4711.000000 -168.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17076" ObjectName="SW-LF_BC.LF_BC_414BK"/>
     <cge:Meas_Ref ObjectId="78159"/>
    <cge:TPSR_Ref TObjectID="17076"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-77975">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4028.916667 -492.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16892" ObjectName="SW-LF_BC.LF_BC_412BK"/>
     <cge:Meas_Ref ObjectId="77975"/>
    <cge:TPSR_Ref TObjectID="16892"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-78018">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3687.250000 -492.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16935" ObjectName="SW-LF_BC.LF_BC_413BK"/>
     <cge:Meas_Ref ObjectId="78018"/>
    <cge:TPSR_Ref TObjectID="16935"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-78001">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3553.250000 -492.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16918" ObjectName="SW-LF_BC.LF_BC_415BK"/>
     <cge:Meas_Ref ObjectId="78001"/>
    <cge:TPSR_Ref TObjectID="16918"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-77965">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3907.250000 -492.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16882" ObjectName="SW-LF_BC.LF_BC_411BK"/>
     <cge:Meas_Ref ObjectId="77965"/>
    <cge:TPSR_Ref TObjectID="16882"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-78136">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4842.500000 -490.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17053" ObjectName="SW-LF_BC.LF_BC_423BK"/>
     <cge:Meas_Ref ObjectId="78136"/>
    <cge:TPSR_Ref TObjectID="17053"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-78092">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4995.583333 -490.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17009" ObjectName="SW-LF_BC.LF_BC_422BK"/>
     <cge:Meas_Ref ObjectId="78092"/>
    <cge:TPSR_Ref TObjectID="17009"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-78026">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5133.000000 -484.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16943" ObjectName="SW-LF_BC.LF_BC_421BK"/>
     <cge:Meas_Ref ObjectId="78026"/>
    <cge:TPSR_Ref TObjectID="16943"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-77826">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4279.515038 -879.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16863" ObjectName="SW-LF_BC.LF_BC_312BK"/>
     <cge:Meas_Ref ObjectId="77826"/>
    <cge:TPSR_Ref TObjectID="16863"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-77821">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4279.515038 -653.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16858" ObjectName="SW-LF_BC.LF_BC_402BK"/>
     <cge:Meas_Ref ObjectId="77821"/>
    <cge:TPSR_Ref TObjectID="16858"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-77751">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3932.515038 -655.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16845" ObjectName="SW-LF_BC.LF_BC_401BK"/>
     <cge:Meas_Ref ObjectId="77751"/>
    <cge:TPSR_Ref TObjectID="16845"/></metadata>
   </g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BV-0KV" transform="matrix(0.300000 -0.000000 0.000000 -2.538462 3561.000000 -178.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BV-0KV" transform="matrix(0.300000 -0.000000 0.000000 -2.538462 3695.000000 -169.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BV-10KV" transform="matrix(0.300000 -0.000000 0.000000 -2.538462 3915.000000 -172.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BV-10KV" transform="matrix(0.300000 -0.000000 0.000000 -2.538462 4037.000000 -172.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BV-10KV" transform="matrix(0.300000 -0.000000 0.000000 -2.538462 4851.000000 -174.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BV-10KV" transform="matrix(0.300000 -0.000000 0.000000 -2.538462 5004.000000 -174.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_2843e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3871,-991 3871,-1008 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17417@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3871,-991 3871,-1008 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2896b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3871,-1044 3871,-1058 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3871,-1044 3871,-1058 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_289d270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3871,-1085 3871,-1100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3871,-1085 3871,-1100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_289db50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3871,-1136 3871,-1152 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3871,-1136 3871,-1152 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_28a1250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3871,-1152 3912,-1152 3912,-1135 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3871,-1152 3912,-1152 3912,-1135 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_28a1440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3912,-1099 3912,-1084 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_2a38030@0" Pin0InfoVect0LinkObjId="g_2a38030_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3912,-1099 3912,-1084 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_28a2da0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3932,-1168 3948,-1168 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_28a2f90@0" Pin0InfoVect0LinkObjId="g_28a2f90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3932,-1168 3948,-1168 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28a6f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4910,-991 4910,-1007 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17417@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4910,-991 4910,-1007 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_28a8760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4910,-1043 4910,-1057 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4910,-1043 4910,-1057 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_28aa420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4910,-1084 4910,-1099 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4910,-1084 4910,-1099 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_28aa610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4910,-1135 4910,-1151 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="g_28b0280@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_28b0280_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4910,-1135 4910,-1151 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_28ac2d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4910,-1151 4951,-1151 4951,-1134 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_28b0280@0" ObjectIDND2="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_28b0280_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4910,-1151 4951,-1151 4951,-1134 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_28ac4f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4951,-1098 4951,-1083 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_2a38ac0@0" Pin0InfoVect0LinkObjId="g_2a38ac0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4951,-1098 4951,-1083 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_28aeb50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4971,-1167 4987,-1167 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_28aed70@0" Pin0InfoVect0LinkObjId="g_28aed70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4971,-1167 4987,-1167 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28b42c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4325,-590 4325,-579 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17418@0" ObjectIDZND0="16986@1" Pin0InfoVect0LinkObjId="SW-78069_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a17eb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4325,-590 4325,-579 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28b6670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4443,-575 4443,-590 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="16990@1" ObjectIDZND0="17419@0" Pin0InfoVect0LinkObjId="g_28c72b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-78073_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4443,-575 4443,-590 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28b88c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4443,-479 4492,-479 4492,-492 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="16987@x" ObjectIDND1="16986@x" ObjectIDZND0="g_28b8ab0@0" Pin0InfoVect0LinkObjId="g_28b8ab0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-78070_0" Pin1InfoVect1LinkObjId="SW-78069_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4443,-479 4492,-479 4492,-492 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28b9d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4325,-543 4325,-462 4443,-462 4443,-479 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="16986@0" ObjectIDZND0="g_28b8ab0@0" ObjectIDZND1="16987@x" Pin0InfoVect0LinkObjId="g_28b8ab0_0" Pin0InfoVect1LinkObjId="SW-78070_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-78069_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4325,-543 4325,-462 4443,-462 4443,-479 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28b9f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4443,-539 4443,-525 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="16990@0" ObjectIDZND0="16987@1" Pin0InfoVect0LinkObjId="SW-78070_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-78073_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4443,-539 4443,-525 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28ba170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4443,-498 4443,-479 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="16987@0" ObjectIDZND0="g_28b8ab0@0" ObjectIDZND1="16986@x" Pin0InfoVect0LinkObjId="g_28b8ab0_0" Pin0InfoVect1LinkObjId="SW-78069_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-78070_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4443,-498 4443,-479 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28bc520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4826,-991 4826,-976 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17417@0" ObjectIDZND0="16857@1" Pin0InfoVect0LinkObjId="SW-77768_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4826,-991 4826,-976 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28be3a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4826,-940 4826,-929 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="16857@0" ObjectIDZND0="16844@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-77750_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-77768_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4826,-940 4826,-929 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28be5c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4826,-929 4826,-914 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="16857@x" ObjectIDND1="0@x" ObjectIDZND0="16844@1" Pin0InfoVect0LinkObjId="SW-77750_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-77768_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4826,-929 4826,-914 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28c0b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4826,-929 4873,-929 4873,-916 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="16844@x" ObjectIDND1="16857@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-77750_0" Pin1InfoVect1LinkObjId="SW-77768_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4826,-929 4873,-929 4873,-916 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_28c0db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4873,-880 4873,-867 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_2a39550@0" Pin0InfoVect0LinkObjId="g_2a39550_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4873,-880 4873,-867 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28c2110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4826,-887 4826,-847 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="16844@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-77750_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4826,-887 4826,-847 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28c63a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4826,-662 4826,-643 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="16869@0" ObjectIDZND0="16875@1" Pin0InfoVect0LinkObjId="SW-77888_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-77878_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4826,-662 4826,-643 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28c72b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4826,-607 4826,-590 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="16875@0" ObjectIDZND0="17419@0" Pin0InfoVect0LinkObjId="g_28b6670_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-77888_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4826,-607 4826,-590 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28c9790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3942,-991 3942,-976 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17417@0" ObjectIDZND0="16879@1" Pin0InfoVect0LinkObjId="SW-77892_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3942,-991 3942,-976 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28cb4f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3942,-940 3942,-929 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="16879@0" ObjectIDZND0="16868@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-77877_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-77892_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3942,-940 3942,-929 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28cb750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3942,-929 3942,-914 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="16879@x" ObjectIDND1="0@x" ObjectIDZND0="16868@1" Pin0InfoVect0LinkObjId="SW-77877_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-77892_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3942,-929 3942,-914 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28cdee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3942,-929 3989,-929 3989,-916 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="16868@x" ObjectIDND1="16879@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-77877_0" Pin1InfoVect1LinkObjId="SW-77892_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3942,-929 3989,-929 3989,-916 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_28ce140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3989,-880 3989,-867 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_2a3aa70@0" Pin0InfoVect0LinkObjId="g_2a3aa70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3989,-880 3989,-867 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28cf4a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3942,-887 3942,-847 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="16868@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-77877_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3942,-887 3942,-847 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28d35c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4602,-590 4602,-580 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17419@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28b6670_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4602,-590 4602,-580 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_28d3fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4602,-485 4602,-463 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_28d3820@0" ObjectIDZND0="g_28d5650@0" Pin0InfoVect0LinkObjId="g_28d5650_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28d3820_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4602,-485 4602,-463 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_28d4240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4602,-530 4557,-530 4557,-519 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_28d3820@0" ObjectIDND1="0@x" ObjectIDZND0="g_28d4960@0" Pin0InfoVect0LinkObjId="g_28d4960_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_28d3820_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4602,-530 4557,-530 4557,-519 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_28d44a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4602,-544 4602,-530 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_28d4960@0" ObjectIDZND1="g_28d3820@0" Pin0InfoVect0LinkObjId="g_28d4960_0" Pin0InfoVect1LinkObjId="g_28d3820_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4602,-544 4602,-530 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_28d4700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4602,-530 4602,-516 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_28d4960@0" ObjectIDND1="0@x" ObjectIDZND0="g_28d3820@1" Pin0InfoVect0LinkObjId="g_28d3820_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_28d4960_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4602,-530 4602,-516 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28d9630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4252,-590 4252,-579 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17418@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a17eb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4252,-590 4252,-579 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_28da070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4252,-483 4252,-461 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_28d9890@0" ObjectIDZND0="g_28db6c0@0" Pin0InfoVect0LinkObjId="g_28db6c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28d9890_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4252,-483 4252,-461 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_28da2d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4252,-529 4207,-529 4207,-518 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_28d9890@0" ObjectIDND1="0@x" ObjectIDZND0="g_28da9f0@0" Pin0InfoVect0LinkObjId="g_28da9f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_28d9890_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4252,-529 4207,-529 4207,-518 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_28da530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4252,-543 4252,-529 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_28da9f0@0" ObjectIDZND1="g_28d9890@0" Pin0InfoVect0LinkObjId="g_28da9f0_0" Pin0InfoVect1LinkObjId="g_28d9890_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4252,-543 4252,-529 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_28da790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4252,-529 4252,-514 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_28da9f0@0" ObjectIDND1="0@x" ObjectIDZND0="g_28d9890@1" Pin0InfoVect0LinkObjId="g_28d9890_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_28da9f0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4252,-529 4252,-514 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28deb80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4148,-590 4148,-496 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="lightningRod" ObjectIDND0="17418@0" ObjectIDZND0="g_28de1b0@0" Pin0InfoVect0LinkObjId="g_28de1b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a17eb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4148,-590 4148,-496 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28e12b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4148,-439 4148,-252 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_28de1b0@1" ObjectIDZND0="17097@1" Pin0InfoVect0LinkObjId="SW-78180_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28de1b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4148,-439 4148,-252 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28e3100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4148,-216 4148,-200 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="17097@0" ObjectIDZND0="17098@1" Pin0InfoVect0LinkObjId="SW-78181_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-78180_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4148,-216 4148,-200 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28e3360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4148,-161 4188,-161 4188,-145 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="17098@x" ObjectIDND1="g_28e40b0@0" ObjectIDZND0="g_28e40b0@0" Pin0InfoVect0LinkObjId="g_28e40b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-78181_0" Pin1InfoVect1LinkObjId="g_28e40b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4148,-161 4188,-161 4188,-145 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28e35c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4148,-173 4148,-161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="17098@0" ObjectIDZND0="g_28e40b0@0" ObjectIDZND1="g_28e40b0@0" Pin0InfoVect0LinkObjId="g_28e40b0_0" Pin0InfoVect1LinkObjId="g_28e40b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-78181_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4148,-173 4148,-161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28e3820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4149,-161 4149,-59 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="17098@x" ObjectIDND1="g_28e40b0@0" ObjectIDZND0="g_28e40b0@1" Pin0InfoVect0LinkObjId="g_28e40b0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-78181_0" Pin1InfoVect1LinkObjId="g_28e40b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4149,-161 4149,-59 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28eb840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4721,-590 4721,-494 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="lightningRod" ObjectIDND0="17419@0" ObjectIDZND0="g_28eabf0@0" Pin0InfoVect0LinkObjId="g_28eabf0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28b6670_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4721,-590 4721,-494 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28edfc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4721,-439 4721,-252 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_28eabf0@1" ObjectIDZND0="17075@1" Pin0InfoVect0LinkObjId="SW-78158_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28eabf0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4721,-439 4721,-252 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28efe00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4721,-216 4721,-200 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="17075@0" ObjectIDZND0="17076@1" Pin0InfoVect0LinkObjId="SW-78159_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-78158_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4721,-216 4721,-200 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28f0060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4720,-161 4760,-161 4760,-145 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="17076@x" ObjectIDND1="g_289e9b0@0" ObjectIDZND0="g_289e9b0@0" Pin0InfoVect0LinkObjId="g_289e9b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-78159_0" Pin1InfoVect1LinkObjId="g_289e9b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4720,-161 4760,-161 4760,-145 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28f02c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4720,-176 4720,-161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="17076@0" ObjectIDZND0="g_289e9b0@0" ObjectIDZND1="g_289e9b0@0" Pin0InfoVect0LinkObjId="g_289e9b0_0" Pin0InfoVect1LinkObjId="g_289e9b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-78159_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4720,-176 4720,-161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28f0520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4720,-161 4720,-62 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="17076@x" ObjectIDND1="g_289e9b0@0" ObjectIDZND0="g_289e9b0@1" Pin0InfoVect0LinkObjId="g_289e9b0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-78159_0" Pin1InfoVect1LinkObjId="g_289e9b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4720,-161 4720,-62 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28f2610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3818,-590 3818,-579 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17418@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a17eb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3818,-590 3818,-579 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_28f2e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3818,-543 3818,-518 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_28f2800@1" Pin0InfoVect0LinkObjId="g_28f2800_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3818,-543 3818,-518 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_28f3060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3818,-487 3818,-465 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_28f2800@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28f2800_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3818,-487 3818,-465 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28f7850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4038,-590 4038,-579 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17418@0" ObjectIDZND0="16909@1" Pin0InfoVect0LinkObjId="SW-77992_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a17eb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4038,-590 4038,-579 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28f9b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4038,-543 4038,-527 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="16909@0" ObjectIDZND0="16892@1" Pin0InfoVect0LinkObjId="SW-77975_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-77992_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4038,-543 4038,-527 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28f9d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4038,-484 4086,-484 4086,-497 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="16892@x" ObjectIDND1="g_28faff0@0" ObjectIDZND0="g_28fa240@0" Pin0InfoVect0LinkObjId="g_28fa240_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-77975_0" Pin1InfoVect1LinkObjId="g_28faff0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4038,-484 4086,-484 4086,-497 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28f9fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4038,-500 4038,-484 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="16892@0" ObjectIDZND0="g_28fa240@0" ObjectIDZND1="g_28faff0@0" Pin0InfoVect0LinkObjId="g_28fa240_0" Pin0InfoVect1LinkObjId="g_28faff0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-77975_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4038,-500 4038,-484 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28fbd40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4038,-484 4038,-462 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="16892@x" ObjectIDND1="g_28fa240@0" ObjectIDZND0="g_28faff0@0" Pin0InfoVect0LinkObjId="g_28faff0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-77975_0" Pin1InfoVect1LinkObjId="g_28fa240_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4038,-484 4038,-462 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28fe740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4038,-393 4038,-409 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="16913@1" ObjectIDZND0="g_28faff0@1" Pin0InfoVect0LinkObjId="g_28faff0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-77996_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4038,-393 4038,-409 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2901390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4087,-264 4087,-288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17426@0" ObjectIDZND0="16914@0" Pin0InfoVect0LinkObjId="SW-77997_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4087,-264 4087,-288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2902d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3696,-590 3696,-579 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17418@0" ObjectIDZND0="16934@1" Pin0InfoVect0LinkObjId="SW-78017_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a17eb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3696,-590 3696,-579 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2904c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3696,-543 3696,-527 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="16934@0" ObjectIDZND0="16935@1" Pin0InfoVect0LinkObjId="SW-78018_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-78017_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3696,-543 3696,-527 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2904e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3696,-484 3744,-484 3744,-497 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="16935@x" ObjectIDND1="g_29060e0@0" ObjectIDZND0="g_2905330@0" Pin0InfoVect0LinkObjId="g_2905330_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-78018_0" Pin1InfoVect1LinkObjId="g_29060e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3696,-484 3744,-484 3744,-497 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29050d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3696,-500 3696,-484 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="16935@0" ObjectIDZND0="g_2905330@0" ObjectIDZND1="g_29060e0@0" Pin0InfoVect0LinkObjId="g_2905330_0" Pin0InfoVect1LinkObjId="g_29060e0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-78018_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3696,-500 3696,-484 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2906e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3696,-484 3696,-462 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="16935@x" ObjectIDND1="g_2905330@0" ObjectIDZND0="g_29060e0@0" Pin0InfoVect0LinkObjId="g_29060e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-78018_0" Pin1InfoVect1LinkObjId="g_2905330_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3696,-484 3696,-462 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2907090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3696,-393 3696,-409 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_29060e0@1" Pin0InfoVect0LinkObjId="g_29060e0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3696,-393 3696,-409 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2909ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3745,-264 3745,-288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17426@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3745,-264 3745,-288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_290dd30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3562,-484 3609,-484 3609,-497 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="16918@x" ObjectIDND1="g_290efa0@0" ObjectIDZND0="g_290e1f0@0" Pin0InfoVect0LinkObjId="g_290e1f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-78001_0" Pin1InfoVect1LinkObjId="g_290efa0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3562,-484 3609,-484 3609,-497 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_290df90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3562,-500 3562,-484 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="16918@0" ObjectIDZND0="g_290efa0@0" ObjectIDZND1="g_290e1f0@0" Pin0InfoVect0LinkObjId="g_290efa0_0" Pin0InfoVect1LinkObjId="g_290e1f0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-78001_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3562,-500 3562,-484 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_290fcf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3562,-484 3562,-462 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="16918@x" ObjectIDND1="g_290e1f0@0" ObjectIDZND0="g_290efa0@0" Pin0InfoVect0LinkObjId="g_290efa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-78001_0" Pin1InfoVect1LinkObjId="g_290e1f0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3562,-484 3562,-462 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_290ff50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3562,-393 3562,-409 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_290efa0@1" Pin0InfoVect0LinkObjId="g_290efa0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3562,-393 3562,-409 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2912ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3611,-264 3611,-288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17426@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3611,-264 3611,-288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2914850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3916,-590 3916,-579 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17418@0" ObjectIDZND0="16881@1" Pin0InfoVect0LinkObjId="SW-77964_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a17eb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3916,-590 3916,-579 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2916890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3916,-543 3916,-527 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="16881@0" ObjectIDZND0="16882@1" Pin0InfoVect0LinkObjId="SW-77965_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-77964_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3916,-543 3916,-527 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2916af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3916,-484 3964,-484 3964,-497 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="16882@x" ObjectIDND1="g_2917d60@0" ObjectIDZND0="g_2916fb0@0" Pin0InfoVect0LinkObjId="g_2916fb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-77965_0" Pin1InfoVect1LinkObjId="g_2917d60_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3916,-484 3964,-484 3964,-497 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2916d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3916,-500 3916,-484 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="16882@0" ObjectIDZND0="g_2916fb0@0" ObjectIDZND1="g_2917d60@0" Pin0InfoVect0LinkObjId="g_2916fb0_0" Pin0InfoVect1LinkObjId="g_2917d60_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-77965_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3916,-500 3916,-484 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2918ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3916,-484 3916,-462 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="16882@x" ObjectIDND1="g_2916fb0@0" ObjectIDZND0="g_2917d60@0" Pin0InfoVect0LinkObjId="g_2917d60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-77965_0" Pin1InfoVect1LinkObjId="g_2916fb0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3916,-484 3916,-462 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2918d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3916,-393 3916,-409 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="16885@1" ObjectIDZND0="g_2917d60@1" Pin0InfoVect0LinkObjId="g_2917d60_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-77968_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3916,-393 3916,-409 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_291b950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3965,-264 3965,-288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17426@0" ObjectIDZND0="16886@0" Pin0InfoVect0LinkObjId="SW-77969_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3965,-264 3965,-288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29e66b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4852,-590 4852,-577 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17419@0" ObjectIDZND0="17052@1" Pin0InfoVect0LinkObjId="SW-78135_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28b6670_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4852,-590 4852,-577 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29e8980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4852,-541 4852,-525 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="17052@0" ObjectIDZND0="17053@1" Pin0InfoVect0LinkObjId="SW-78136_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-78135_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4852,-541 4852,-525 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29e8be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4852,-482 4900,-482 4900,-495 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="17053@x" ObjectIDND1="g_29e9e50@0" ObjectIDZND0="g_29e90a0@0" Pin0InfoVect0LinkObjId="g_29e90a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-78136_0" Pin1InfoVect1LinkObjId="g_29e9e50_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4852,-482 4900,-482 4900,-495 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29e8e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4852,-498 4852,-482 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="17053@0" ObjectIDZND0="g_29e90a0@0" ObjectIDZND1="g_29e9e50@0" Pin0InfoVect0LinkObjId="g_29e90a0_0" Pin0InfoVect1LinkObjId="g_29e9e50_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-78136_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4852,-498 4852,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29eaba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4852,-482 4852,-463 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="17053@x" ObjectIDND1="g_29e90a0@0" ObjectIDZND0="g_29e9e50@0" Pin0InfoVect0LinkObjId="g_29e9e50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-78136_0" Pin1InfoVect1LinkObjId="g_29e90a0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4852,-482 4852,-463 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29eb2f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4900,-264 4900,-286 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17426@0" ObjectIDZND0="17424@0" Pin0InfoVect0LinkObjId="SW-79379_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4900,-264 4900,-286 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29ef0a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5005,-590 5005,-577 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17419@0" ObjectIDZND0="17026@1" Pin0InfoVect0LinkObjId="SW-78109_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28b6670_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5005,-590 5005,-577 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29f1370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5005,-541 5005,-525 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="17026@0" ObjectIDZND0="17009@1" Pin0InfoVect0LinkObjId="SW-78092_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-78109_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5005,-541 5005,-525 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29f15d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5005,-482 5052,-482 5052,-495 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="17009@x" ObjectIDND1="g_29f2840@0" ObjectIDZND0="g_29f1a90@0" Pin0InfoVect0LinkObjId="g_29f1a90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-78092_0" Pin1InfoVect1LinkObjId="g_29f2840_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5005,-482 5052,-482 5052,-495 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29f1830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5005,-498 5005,-482 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="17009@0" ObjectIDZND0="g_29f1a90@0" ObjectIDZND1="g_29f2840@0" Pin0InfoVect0LinkObjId="g_29f1a90_0" Pin0InfoVect1LinkObjId="g_29f2840_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-78092_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5005,-498 5005,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29f3590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5005,-482 5005,-462 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="17009@x" ObjectIDND1="g_29f1a90@0" ObjectIDZND0="g_29f2840@0" Pin0InfoVect0LinkObjId="g_29f2840_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-78092_0" Pin1InfoVect1LinkObjId="g_29f1a90_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5005,-482 5005,-462 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29f37f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5005,-393 5005,-409 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="17421@1" ObjectIDZND0="g_29f2840@1" Pin0InfoVect0LinkObjId="g_29f2840_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-79354_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5005,-393 5005,-409 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29f3f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5055,-264 5055,-288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17426@0" ObjectIDZND0="17422@0" Pin0InfoVect0LinkObjId="SW-79355_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5055,-264 5055,-288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29f7680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5142,-590 5142,-571 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17419@0" ObjectIDZND0="17001@1" Pin0InfoVect0LinkObjId="SW-78084_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28b6670_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5142,-590 5142,-571 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29f9950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5142,-535 5142,-519 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="17001@0" ObjectIDZND0="16943@1" Pin0InfoVect0LinkObjId="SW-78026_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-78084_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5142,-535 5142,-519 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29f9bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5142,-476 5188,-476 5188,-489 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="16943@x" ObjectIDND1="g_29fae20@0" ObjectIDZND0="g_29fa070@0" Pin0InfoVect0LinkObjId="g_29fa070_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-78026_0" Pin1InfoVect1LinkObjId="g_29fae20_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5142,-476 5188,-476 5188,-489 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29f9e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5142,-492 5142,-476 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="16943@0" ObjectIDZND0="g_29fa070@0" ObjectIDZND1="g_29fae20@0" Pin0InfoVect0LinkObjId="g_29fa070_0" Pin0InfoVect1LinkObjId="g_29fae20_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-78026_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5142,-492 5142,-476 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29fbb70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5142,-476 5142,-457 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="16943@x" ObjectIDND1="g_29fa070@0" ObjectIDZND0="g_29fae20@0" Pin0InfoVect0LinkObjId="g_29fae20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-78026_0" Pin1InfoVect1LinkObjId="g_29fa070_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5142,-476 5142,-457 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29fbdd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5142,-338 5189,-338 5189,-322 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="17426@0" ObjectIDND1="17005@1" ObjectIDZND0="g_29fc750@0" Pin0InfoVect0LinkObjId="g_29fc750_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="SW-78088_1" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5142,-338 5189,-338 5189,-322 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29fc030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5142,-388 5142,-404 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="17005@1" ObjectIDZND0="g_29fae20@1" Pin0InfoVect0LinkObjId="g_29fae20_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-78088_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5142,-388 5142,-404 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29fc290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5142,-264 5142,-338 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="17426@0" ObjectIDZND0="g_29fc750@0" ObjectIDZND1="17005@1" Pin0InfoVect0LinkObjId="g_29fc750_0" Pin0InfoVect1LinkObjId="SW-78088_1" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5142,-264 5142,-338 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29fc4f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5142,-338 5142,-352 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="17426@0" ObjectIDND1="g_29fc750@0" ObjectIDZND0="17005@0" Pin0InfoVect0LinkObjId="SW-78088_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="g_29fc750_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5142,-338 5142,-352 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a048c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4289,-991 4289,-976 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17417@0" ObjectIDZND0="16866@1" Pin0InfoVect0LinkObjId="SW-77829_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4289,-991 4289,-976 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a06a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4289,-940 4289,-929 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="16866@0" ObjectIDZND0="16863@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-77826_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-77829_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4289,-940 4289,-929 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a06ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4289,-929 4289,-914 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="16866@x" ObjectIDND1="0@x" ObjectIDZND0="16863@1" Pin0InfoVect0LinkObjId="SW-77826_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-77829_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4289,-929 4289,-914 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a096e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4289,-929 4336,-929 4336,-916 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="16863@x" ObjectIDND1="16866@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-77826_0" Pin1InfoVect1LinkObjId="SW-77829_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4289,-929 4336,-929 4336,-916 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a09940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4336,-880 4336,-867 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_2a39fe0@0" Pin0InfoVect0LinkObjId="g_2a39fe0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4336,-880 4336,-867 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a0adc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4289,-887 4289,-847 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="16863@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-77826_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4289,-887 4289,-847 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a100c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4826,-716 4826,-689 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="16869@1" Pin0InfoVect0LinkObjId="SW-77878_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4826,-716 4826,-689 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a10320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4826,-767 4826,-752 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4826,-767 4826,-752 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a10580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3942,-767 3942,-752 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="16851@1" Pin0InfoVect0LinkObjId="SW-77761_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3942,-767 3942,-752 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a14ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4289,-661 4289,-642 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="16858@0" ObjectIDZND0="16862@1" Pin0InfoVect0LinkObjId="SW-77825_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-77821_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4289,-661 4289,-642 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a179f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4289,-715 4289,-688 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="16861@0" ObjectIDZND0="16858@1" Pin0InfoVect0LinkObjId="SW-77821_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-77824_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4289,-715 4289,-688 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a17c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4289,-767 4289,-749 4289,-751 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="16861@1" Pin0InfoVect0LinkObjId="SW-77824_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4289,-767 4289,-749 4289,-751 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a17eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4289,-606 4289,-590 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="16862@0" ObjectIDZND0="17418@0" Pin0InfoVect0LinkObjId="g_2a1fd90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-77825_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4289,-606 4289,-590 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a1cb60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3942,-663 3942,-644 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="16845@0" ObjectIDZND0="16852@1" Pin0InfoVect0LinkObjId="SW-77762_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-77751_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3942,-663 3942,-644 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a1f560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3942,-717 3942,-690 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="16851@0" ObjectIDZND0="16845@1" Pin0InfoVect0LinkObjId="SW-77751_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-77761_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3942,-717 3942,-690 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a1fd90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3942,-608 3942,-590 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="16852@0" ObjectIDZND0="17418@0" Pin0InfoVect0LinkObjId="g_2a17eb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-77762_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3942,-608 3942,-590 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a1fff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3871,-1152 3871,-1169 3887,-1169 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3871,-1152 3871,-1169 3887,-1169 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a20450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3857,-1190 3871,-1190 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="powerLine" ObjectIDND0="g_28a4110@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28a4110_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3857,-1190 3871,-1190 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a20f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4910,-1151 4910,-1168 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@x" ObjectIDZND1="g_28b0280@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_28b0280_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4910,-1151 4910,-1168 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a211a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4910,-1168 4926,-1168 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="g_28b0280@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_28b0280_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4910,-1168 4926,-1168 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a21400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4896,-1189 4910,-1189 4910,-1169 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_28b0280@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28b0280_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4896,-1189 4910,-1189 4910,-1169 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a22250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3562,-357 3562,-343 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_2913130@0" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_2913130_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3562,-357 3562,-343 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a224b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3562,-343 3611,-343 3611,-324 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2913130@0" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2913130_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3562,-343 3611,-343 3611,-324 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a23480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3562,-252 3562,-231 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_2913130@0" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2913130_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3562,-252 3562,-231 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a23f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3611,-236 3611,-252 3562,-252 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="load" ObjectIDND0="g_2913130@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2913130_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3611,-236 3611,-252 3562,-252 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a241d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3562,-252 3562,-343 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2913130@0" ObjectIDND1="0@x" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2913130_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3562,-252 3562,-343 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a24f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3745,-324 3745,-343 3696,-343 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" EndDevType2="switch" ObjectIDND0="0@1" ObjectIDZND0="g_2909f40@0" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_2909f40_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3745,-324 3745,-343 3696,-343 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a251e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3696,-343 3696,-357 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2909f40@0" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2909f40_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3696,-343 3696,-357 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a25440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3696,-237 3696,-222 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="load" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="g_2909f40@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_2909f40_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3696,-237 3696,-222 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a25f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3696,-343 3696,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="g_2909f40@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_2909f40_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3696,-343 3696,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a26120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3696,-237 3745,-238 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="load" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_2909f40@0" Pin0InfoVect0LinkObjId="g_2909f40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3696,-237 3745,-238 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a27860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3965,-324 3965,-343 3916,-343 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" EndDevType2="switch" ObjectIDND0="16886@1" ObjectIDZND0="g_291bbb0@0" ObjectIDZND1="0@x" ObjectIDZND2="16885@x" Pin0InfoVect0LinkObjId="g_291bbb0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-77968_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-77969_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3965,-324 3965,-343 3916,-343 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a27ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3916,-343 3916,-357 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_291bbb0@0" ObjectIDND1="0@x" ObjectIDND2="16886@x" ObjectIDZND0="16885@0" Pin0InfoVect0LinkObjId="SW-77968_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_291bbb0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-77969_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3916,-343 3916,-357 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a27d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3965,-236 3916,-236 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="load" ObjectIDND0="g_291bbb0@0" ObjectIDZND0="16886@x" ObjectIDZND1="16885@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-77969_0" Pin0InfoVect1LinkObjId="SW-77968_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_291bbb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3965,-236 3916,-236 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a28810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3916,-343 3916,-236 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="16886@x" ObjectIDND1="16885@x" ObjectIDZND0="g_291bbb0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_291bbb0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-77969_0" Pin1InfoVect1LinkObjId="SW-77968_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3916,-343 3916,-236 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a28a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3916,-236 3916,-225 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_291bbb0@0" ObjectIDND1="16886@x" ObjectIDND2="16885@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_291bbb0_0" Pin1InfoVect1LinkObjId="SW-77969_0" Pin1InfoVect2LinkObjId="SW-77968_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3916,-236 3916,-225 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a2d090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4900,-322 4852,-322 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" EndDevType2="switch" ObjectIDND0="17424@1" ObjectIDZND0="g_29eb4e0@0" ObjectIDZND1="0@x" ObjectIDZND2="17423@x" Pin0InfoVect0LinkObjId="g_29eb4e0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-79378_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-79379_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4900,-322 4852,-322 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a2dce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4852,-322 4852,-359 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_29eb4e0@0" ObjectIDND1="0@x" ObjectIDND2="17424@x" ObjectIDZND0="17423@0" Pin0InfoVect0LinkObjId="SW-79378_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_29eb4e0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-79379_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4852,-322 4852,-359 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a2df40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4899,-235 4852,-235 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_29eb4e0@0" ObjectIDZND0="0@x" ObjectIDZND1="17424@x" ObjectIDZND2="17423@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-79379_0" Pin0InfoVect2LinkObjId="SW-79378_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29eb4e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4899,-235 4852,-235 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a2ea30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4852,-227 4852,-235 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_29eb4e0@0" ObjectIDZND1="17424@x" ObjectIDZND2="17423@x" Pin0InfoVect0LinkObjId="g_29eb4e0_0" Pin0InfoVect1LinkObjId="SW-79379_0" Pin0InfoVect2LinkObjId="SW-79378_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4852,-227 4852,-235 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a2ec90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4852,-235 4852,-322 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_29eb4e0@0" ObjectIDND1="0@x" ObjectIDZND0="17424@x" ObjectIDZND1="17423@x" Pin0InfoVect0LinkObjId="SW-79379_0" Pin0InfoVect1LinkObjId="SW-79378_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_29eb4e0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4852,-235 4852,-322 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a2eef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5055,-324 5005,-324 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" EndDevType2="switch" ObjectIDND0="17422@1" ObjectIDZND0="g_29f4130@0" ObjectIDZND1="0@x" ObjectIDZND2="17421@x" Pin0InfoVect0LinkObjId="g_29f4130_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-79354_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-79355_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5055,-324 5005,-324 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a2f150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5055,-237 5005,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="load" ObjectIDND0="g_29f4130@0" ObjectIDZND0="17422@x" ObjectIDZND1="17421@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-79355_0" Pin0InfoVect1LinkObjId="SW-79354_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29f4130_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5055,-237 5005,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a2fc40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5005,-358 5005,-324 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" EndDevType2="switch" ObjectIDND0="17421@0" ObjectIDZND0="g_29f4130@0" ObjectIDZND1="0@x" ObjectIDZND2="17422@x" Pin0InfoVect0LinkObjId="g_29f4130_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-79355_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-79354_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5005,-358 5005,-324 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a30730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5005,-324 5005,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="17422@x" ObjectIDND1="17421@x" ObjectIDZND0="g_29f4130@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_29f4130_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-79355_0" Pin1InfoVect1LinkObjId="SW-79354_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5005,-324 5005,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a30990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5005,-237 5005,-227 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_29f4130@0" ObjectIDND1="17422@x" ObjectIDND2="17421@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_29f4130_0" Pin1InfoVect1LinkObjId="SW-79355_0" Pin1InfoVect2LinkObjId="SW-79354_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5005,-237 5005,-227 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a3bad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4853,-410 4853,-395 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_29e9e50@1" ObjectIDZND0="17423@1" Pin0InfoVect0LinkObjId="SW-79378_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29e9e50_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4853,-410 4853,-395 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a46d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3562,-578 3562,-590 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="17418@0" Pin0InfoVect0LinkObjId="g_2a17eb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3562,-578 3562,-590 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a47550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3562,-542 3562,-527 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="16918@1" Pin0InfoVect0LinkObjId="SW-78001_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3562,-542 3562,-527 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ca0c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4038,-343 4038,-236 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="16914@x" ObjectIDND1="16913@x" ObjectIDZND0="g_29015f0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_29015f0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-77997_0" Pin1InfoVect1LinkObjId="SW-77996_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4038,-343 4038,-236 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ca1010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4087,-324 4087,-343 4038,-343 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" EndDevType2="switch" ObjectIDND0="16914@1" ObjectIDZND0="g_29015f0@0" ObjectIDZND1="0@x" ObjectIDZND2="16913@x" Pin0InfoVect0LinkObjId="g_29015f0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-77996_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-77997_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4087,-324 4087,-343 4038,-343 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ca1200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4038,-343 4038,-357 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_29015f0@0" ObjectIDND1="0@x" ObjectIDND2="16914@x" ObjectIDZND0="16913@0" Pin0InfoVect0LinkObjId="SW-77996_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_29015f0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-77997_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4038,-343 4038,-357 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c9f5c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4087,-236 4038,-236 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="load" ObjectIDND0="g_29015f0@0" ObjectIDZND0="16914@x" ObjectIDZND1="16913@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-77997_0" Pin0InfoVect1LinkObjId="SW-77996_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29015f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4087,-236 4038,-236 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c9f7b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4038,-236 4038,-225 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="load" ObjectIDND0="16914@x" ObjectIDND1="16913@x" ObjectIDND2="g_29015f0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-77997_0" Pin1InfoVect1LinkObjId="SW-77996_0" Pin1InfoVect2LinkObjId="g_29015f0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4038,-236 4038,-225 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-79009" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3544.000000 -118.000000) translate(0,15)">79009.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79009" ObjectName="LF_BC.LF_BC_415BK:F"/>
     <cge:PSR_Ref ObjectID="16918"/>
     <cge:Term_Ref ObjectID="23937"/>
    <cge:TPSR_Ref TObjectID="16918"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-79010" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3544.000000 -118.000000) translate(0,33)">79010.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79010" ObjectName="LF_BC.LF_BC_415BK:F"/>
     <cge:PSR_Ref ObjectID="16918"/>
     <cge:Term_Ref ObjectID="23937"/>
    <cge:TPSR_Ref TObjectID="16918"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-79005" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3544.000000 -118.000000) translate(0,51)">79005.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79005" ObjectName="LF_BC.LF_BC_415BK:F"/>
     <cge:PSR_Ref ObjectID="16918"/>
     <cge:Term_Ref ObjectID="23937"/>
    <cge:TPSR_Ref TObjectID="16918"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-79003" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3724.000000 -118.000000) translate(0,15)">79003.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79003" ObjectName="LF_BC.LF_BC_413BK:F"/>
     <cge:PSR_Ref ObjectID="16935"/>
     <cge:Term_Ref ObjectID="23943"/>
    <cge:TPSR_Ref TObjectID="16935"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-79004" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3724.000000 -118.000000) translate(0,33)">79004.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79004" ObjectName="LF_BC.LF_BC_413BK:F"/>
     <cge:PSR_Ref ObjectID="16935"/>
     <cge:Term_Ref ObjectID="23943"/>
    <cge:TPSR_Ref TObjectID="16935"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-78999" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3724.000000 -118.000000) translate(0,51)">78999.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78999" ObjectName="LF_BC.LF_BC_413BK:F"/>
     <cge:PSR_Ref ObjectID="16935"/>
     <cge:Term_Ref ObjectID="23943"/>
    <cge:TPSR_Ref TObjectID="16935"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-78991" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3891.000000 -118.000000) translate(0,15)">78991.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78991" ObjectName="LF_BC.LF_BC_411BK:F"/>
     <cge:PSR_Ref ObjectID="16882"/>
     <cge:Term_Ref ObjectID="23923"/>
    <cge:TPSR_Ref TObjectID="16882"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-78992" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3891.000000 -118.000000) translate(0,33)">78992.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78992" ObjectName="LF_BC.LF_BC_411BK:F"/>
     <cge:PSR_Ref ObjectID="16882"/>
     <cge:Term_Ref ObjectID="23923"/>
    <cge:TPSR_Ref TObjectID="16882"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-78987" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3891.000000 -118.000000) translate(0,51)">78987.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78987" ObjectName="LF_BC.LF_BC_411BK:F"/>
     <cge:PSR_Ref ObjectID="16882"/>
     <cge:Term_Ref ObjectID="23923"/>
    <cge:TPSR_Ref TObjectID="16882"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-78997" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4069.000000 -118.000000) translate(0,15)">78997.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78997" ObjectName="LF_BC.LF_BC_412BK:F"/>
     <cge:PSR_Ref ObjectID="16892"/>
     <cge:Term_Ref ObjectID="23929"/>
    <cge:TPSR_Ref TObjectID="16892"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-78998" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4069.000000 -118.000000) translate(0,33)">78998.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78998" ObjectName="LF_BC.LF_BC_412BK:F"/>
     <cge:PSR_Ref ObjectID="16892"/>
     <cge:Term_Ref ObjectID="23929"/>
    <cge:TPSR_Ref TObjectID="16892"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-78993" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4069.000000 -118.000000) translate(0,51)">78993.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78993" ObjectName="LF_BC.LF_BC_412BK:F"/>
     <cge:PSR_Ref ObjectID="16892"/>
     <cge:Term_Ref ObjectID="23929"/>
    <cge:TPSR_Ref TObjectID="16892"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-79045" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4289.000000 -210.000000) translate(0,15)">79045.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79045" ObjectName="LF_BC.LF_BC_424BK:F"/>
     <cge:PSR_Ref ObjectID="17098"/>
     <cge:Term_Ref ObjectID="23991"/>
    <cge:TPSR_Ref TObjectID="17098"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-79046" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4289.000000 -210.000000) translate(0,33)">79046.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79046" ObjectName="LF_BC.LF_BC_424BK:F"/>
     <cge:PSR_Ref ObjectID="17098"/>
     <cge:Term_Ref ObjectID="23991"/>
    <cge:TPSR_Ref TObjectID="17098"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-79041" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4289.000000 -210.000000) translate(0,51)">79041.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79041" ObjectName="LF_BC.LF_BC_424BK:F"/>
     <cge:PSR_Ref ObjectID="17098"/>
     <cge:Term_Ref ObjectID="23991"/>
    <cge:TPSR_Ref TObjectID="17098"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-79015" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4483.000000 -400.000000) translate(0,12)">79015.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79015" ObjectName="LF_BC.LF_BC_404BK:F"/>
     <cge:PSR_Ref ObjectID="16987"/>
     <cge:Term_Ref ObjectID="23949"/>
    <cge:TPSR_Ref TObjectID="16987"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-79016" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4483.000000 -400.000000) translate(0,27)">79016.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79016" ObjectName="LF_BC.LF_BC_404BK:F"/>
     <cge:PSR_Ref ObjectID="16987"/>
     <cge:Term_Ref ObjectID="23949"/>
    <cge:TPSR_Ref TObjectID="16987"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-79011" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4483.000000 -400.000000) translate(0,42)">79011.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79011" ObjectName="LF_BC.LF_BC_404BK:F"/>
     <cge:PSR_Ref ObjectID="16987"/>
     <cge:Term_Ref ObjectID="23949"/>
    <cge:TPSR_Ref TObjectID="16987"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-79039" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4633.000000 -210.000000) translate(0,15)">79039.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79039" ObjectName="LF_BC.LF_BC_414BK:F"/>
     <cge:PSR_Ref ObjectID="17076"/>
     <cge:Term_Ref ObjectID="23987"/>
    <cge:TPSR_Ref TObjectID="17076"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-79040" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4633.000000 -210.000000) translate(0,33)">79040.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79040" ObjectName="LF_BC.LF_BC_414BK:F"/>
     <cge:PSR_Ref ObjectID="17076"/>
     <cge:Term_Ref ObjectID="23987"/>
    <cge:TPSR_Ref TObjectID="17076"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-79035" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4633.000000 -210.000000) translate(0,51)">79035.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79035" ObjectName="LF_BC.LF_BC_414BK:F"/>
     <cge:PSR_Ref ObjectID="17076"/>
     <cge:Term_Ref ObjectID="23987"/>
    <cge:TPSR_Ref TObjectID="17076"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-79033" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4880.000000 -118.000000) translate(0,15)">79033.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79033" ObjectName="LF_BC.LF_BC_423BK:F"/>
     <cge:PSR_Ref ObjectID="17053"/>
     <cge:Term_Ref ObjectID="23979"/>
    <cge:TPSR_Ref TObjectID="17053"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-79034" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4880.000000 -118.000000) translate(0,33)">79034.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79034" ObjectName="LF_BC.LF_BC_423BK:F"/>
     <cge:PSR_Ref ObjectID="17053"/>
     <cge:Term_Ref ObjectID="23979"/>
    <cge:TPSR_Ref TObjectID="17053"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-79029" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4880.000000 -118.000000) translate(0,51)">79029.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79029" ObjectName="LF_BC.LF_BC_423BK:F"/>
     <cge:PSR_Ref ObjectID="17053"/>
     <cge:Term_Ref ObjectID="23979"/>
    <cge:TPSR_Ref TObjectID="17053"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-79027" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5050.000000 -118.000000) translate(0,15)">79027.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79027" ObjectName="LF_BC.LF_BC_422BK:F"/>
     <cge:PSR_Ref ObjectID="17009"/>
     <cge:Term_Ref ObjectID="23957"/>
    <cge:TPSR_Ref TObjectID="17009"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-79028" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5050.000000 -118.000000) translate(0,33)">79028.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79028" ObjectName="LF_BC.LF_BC_422BK:F"/>
     <cge:PSR_Ref ObjectID="17009"/>
     <cge:Term_Ref ObjectID="23957"/>
    <cge:TPSR_Ref TObjectID="17009"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-79023" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5050.000000 -118.000000) translate(0,51)">79023.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79023" ObjectName="LF_BC.LF_BC_422BK:F"/>
     <cge:PSR_Ref ObjectID="17009"/>
     <cge:Term_Ref ObjectID="23957"/>
    <cge:TPSR_Ref TObjectID="17009"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-79021" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5221.000000 -118.000000) translate(0,15)">79021.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79021" ObjectName="LF_BC.LF_BC_421BK:F"/>
     <cge:PSR_Ref ObjectID="16943"/>
     <cge:Term_Ref ObjectID="23945"/>
    <cge:TPSR_Ref TObjectID="16943"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-79022" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5221.000000 -118.000000) translate(0,33)">79022.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79022" ObjectName="LF_BC.LF_BC_421BK:F"/>
     <cge:PSR_Ref ObjectID="16943"/>
     <cge:Term_Ref ObjectID="23945"/>
    <cge:TPSR_Ref TObjectID="16943"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-79017" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5221.000000 -118.000000) translate(0,51)">79017.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79017" ObjectName="LF_BC.LF_BC_421BK:F"/>
     <cge:PSR_Ref ObjectID="16943"/>
     <cge:Term_Ref ObjectID="23945"/>
    <cge:TPSR_Ref TObjectID="16943"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-78964" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4970.000000 -695.000000) translate(0,15)">78964.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78964" ObjectName="LF_BC.LF_BC_403BK:F"/>
     <cge:PSR_Ref ObjectID="16869"/>
     <cge:Term_Ref ObjectID="23915"/>
    <cge:TPSR_Ref TObjectID="16869"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-78965" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4970.000000 -695.000000) translate(0,33)">78965.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78965" ObjectName="LF_BC.LF_BC_403BK:F"/>
     <cge:PSR_Ref ObjectID="16869"/>
     <cge:Term_Ref ObjectID="23915"/>
    <cge:TPSR_Ref TObjectID="16869"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-78960" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4970.000000 -695.000000) translate(0,51)">78960.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78960" ObjectName="LF_BC.LF_BC_403BK:F"/>
     <cge:PSR_Ref ObjectID="16869"/>
     <cge:Term_Ref ObjectID="23915"/>
    <cge:TPSR_Ref TObjectID="16869"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-78934" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4718.000000 -928.000000) translate(0,15)">78934.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78934" ObjectName="LF_BC.LF_BC_311BK:F"/>
     <cge:PSR_Ref ObjectID="16844"/>
     <cge:Term_Ref ObjectID="16032"/>
    <cge:TPSR_Ref TObjectID="16844"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-78935" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4718.000000 -928.000000) translate(0,33)">78935.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78935" ObjectName="LF_BC.LF_BC_311BK:F"/>
     <cge:PSR_Ref ObjectID="16844"/>
     <cge:Term_Ref ObjectID="16032"/>
    <cge:TPSR_Ref TObjectID="16844"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-78930" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4718.000000 -928.000000) translate(0,51)">78930.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78930" ObjectName="LF_BC.LF_BC_311BK:F"/>
     <cge:PSR_Ref ObjectID="16844"/>
     <cge:Term_Ref ObjectID="16032"/>
    <cge:TPSR_Ref TObjectID="16844"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-78946" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4184.000000 -928.000000) translate(0,15)">78946.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78946" ObjectName="LF_BC.LF_BC_312BK:F"/>
     <cge:PSR_Ref ObjectID="16863"/>
     <cge:Term_Ref ObjectID="23909"/>
    <cge:TPSR_Ref TObjectID="16863"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-78947" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4184.000000 -928.000000) translate(0,33)">78947.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78947" ObjectName="LF_BC.LF_BC_312BK:F"/>
     <cge:PSR_Ref ObjectID="16863"/>
     <cge:Term_Ref ObjectID="23909"/>
    <cge:TPSR_Ref TObjectID="16863"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-78942" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4184.000000 -928.000000) translate(0,51)">78942.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78942" ObjectName="LF_BC.LF_BC_312BK:F"/>
     <cge:PSR_Ref ObjectID="16863"/>
     <cge:Term_Ref ObjectID="23909"/>
    <cge:TPSR_Ref TObjectID="16863"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-78952" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4404.000000 -695.000000) translate(0,15)">78952.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78952" ObjectName="LF_BC.LF_BC_402BK:F"/>
     <cge:PSR_Ref ObjectID="16858"/>
     <cge:Term_Ref ObjectID="16042"/>
    <cge:TPSR_Ref TObjectID="16858"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-78953" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4404.000000 -695.000000) translate(0,33)">78953.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78953" ObjectName="LF_BC.LF_BC_402BK:F"/>
     <cge:PSR_Ref ObjectID="16858"/>
     <cge:Term_Ref ObjectID="16042"/>
    <cge:TPSR_Ref TObjectID="16858"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-78948" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4404.000000 -695.000000) translate(0,51)">78948.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78948" ObjectName="LF_BC.LF_BC_402BK:F"/>
     <cge:PSR_Ref ObjectID="16858"/>
     <cge:Term_Ref ObjectID="16042"/>
    <cge:TPSR_Ref TObjectID="16858"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-78940" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4048.000000 -693.000000) translate(0,15)">78940.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78940" ObjectName="LF_BC.LF_BC_401BK:F"/>
     <cge:PSR_Ref ObjectID="16845"/>
     <cge:Term_Ref ObjectID="16034"/>
    <cge:TPSR_Ref TObjectID="16845"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-78941" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4048.000000 -693.000000) translate(0,33)">78941.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78941" ObjectName="LF_BC.LF_BC_401BK:F"/>
     <cge:PSR_Ref ObjectID="16845"/>
     <cge:Term_Ref ObjectID="16034"/>
    <cge:TPSR_Ref TObjectID="16845"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-78936" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4048.000000 -693.000000) translate(0,51)">78936.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78936" ObjectName="LF_BC.LF_BC_401BK:F"/>
     <cge:PSR_Ref ObjectID="16845"/>
     <cge:Term_Ref ObjectID="16034"/>
    <cge:TPSR_Ref TObjectID="16845"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-78958" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3833.000000 -929.000000) translate(0,15)">78958.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78958" ObjectName="LF_BC.LF_BC_313BK:F"/>
     <cge:PSR_Ref ObjectID="16868"/>
     <cge:Term_Ref ObjectID="23913"/>
    <cge:TPSR_Ref TObjectID="16868"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-78959" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3833.000000 -929.000000) translate(0,33)">78959.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78959" ObjectName="LF_BC.LF_BC_313BK:F"/>
     <cge:PSR_Ref ObjectID="16868"/>
     <cge:Term_Ref ObjectID="23913"/>
    <cge:TPSR_Ref TObjectID="16868"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-78954" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3833.000000 -929.000000) translate(0,51)">78954.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78954" ObjectName="LF_BC.LF_BC_313BK:F"/>
     <cge:PSR_Ref ObjectID="16868"/>
     <cge:Term_Ref ObjectID="23913"/>
    <cge:TPSR_Ref TObjectID="16868"/></metadata>
   </g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 0.000000 -17.000000)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3490.000000 71.000000) translate(0,16)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3459.000000 86.000000) translate(0,16)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3474.000000 101.000000) translate(0,16)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 0.000000 -16.000000)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3665.000000 72.000000) translate(0,16)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3634.000000 87.000000) translate(0,16)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3649.000000 102.000000) translate(0,16)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 0.000000 -21.000000)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3834.000000 67.000000) translate(0,16)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3803.000000 82.000000) translate(0,16)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3818.000000 97.000000) translate(0,16)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 0.000000 -0.000000)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4012.000000 88.000000) translate(0,16)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3981.000000 103.000000) translate(0,16)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3996.000000 118.000000) translate(0,16)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 0.000000 -25.000000)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4235.000000 155.000000) translate(0,16)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4204.000000 170.000000) translate(0,16)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4219.000000 185.000000) translate(0,16)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4427.000000 370.000000) translate(0,16)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4396.000000 385.000000) translate(0,16)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4411.000000 400.000000) translate(0,16)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 0.000000 -2.000000)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4568.000000 178.000000) translate(0,16)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4537.000000 193.000000) translate(0,16)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4552.000000 208.000000) translate(0,16)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 0.000000 -16.000000)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4827.000000 72.000000) translate(0,16)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4796.000000 87.000000) translate(0,16)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4811.000000 102.000000) translate(0,16)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 0.000000 -10.000000)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4996.000000 78.000000) translate(0,16)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4965.000000 93.000000) translate(0,16)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4980.000000 108.000000) translate(0,16)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 0.000000 6.000000)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5167.000000 94.000000) translate(0,16)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5136.000000 109.000000) translate(0,16)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5151.000000 124.000000) translate(0,16)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 0.000000 -0.000000)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4915.000000 665.000000) translate(0,16)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4884.000000 680.000000) translate(0,16)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4899.000000 695.000000) translate(0,16)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -36.000000 -3.000000)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4386.000000 662.000000) translate(0,16)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4355.000000 677.000000) translate(0,16)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4370.000000 692.000000) translate(0,16)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -12.000000 2.000000)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4007.000000 665.000000) translate(0,16)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3976.000000 680.000000) translate(0,16)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3991.000000 695.000000) translate(0,16)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -259.000000 -7.000000)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4038.000000 892.000000) translate(0,16)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4007.000000 907.000000) translate(0,16)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4022.000000 922.000000) translate(0,16)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -293.000000 12.000000)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4421.000000 910.000000) translate(0,16)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4390.000000 925.000000) translate(0,16)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4405.000000 940.000000) translate(0,16)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -291.000000 3.000000)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4952.000000 901.000000) translate(0,16)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4921.000000 916.000000) translate(0,16)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4936.000000 931.000000) translate(0,16)">P(MW):</text>
   <metadata/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2a38030">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3906.000000 -1066.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a38ac0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4945.000000 -1065.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a39550">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4867.000000 -849.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a39fe0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4330.000000 -849.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a3aa70">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3983.000000 -849.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -1025.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -1025.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -1025.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -1025.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -1025.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -1025.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -1025.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" transform="matrix(1.000000 0.000000 0.000000 1.000000 3283.000000 -1166.500000) translate(0,16)">碧城变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 3835.000000 -1253.000000) translate(0,18)">35kV碧城T接线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3832.000000 -1080.000000) translate(0,15)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 3975.000000 -1180.000000) translate(0,18)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4924.000000 -1252.000000) translate(0,18)">35kV碧城线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4871.000000 -1079.000000) translate(0,15)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 5009.000000 -1115.000000) translate(0,18)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4862.000000 -815.000000) translate(0,18)">3号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 3978.000000 -815.000000) translate(0,18)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4359.000000 -1029.000000) translate(0,18)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4534.000000 -436.000000) translate(0,18)">10kVII段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4184.000000 -434.000000) translate(0,18)">10kVI段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4676.000000 -24.000000) translate(0,18)">2号电容器720千乏</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4094.000000 -24.000000) translate(0,18)">1号电容器540千乏</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 5121.000000 -612.000000) translate(0,18)">10kVII段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3788.000000 -402.000000) translate(0,15)">站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4019.666667 -166.000000) translate(0,18)">碧城I回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 3678.000000 -166.000000) translate(0,18)">董家湾线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 3544.000000 -166.000000) translate(0,18)">城区线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 3898.000000 -166.000000) translate(0,18)">温泉线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4830.000000 -166.000000) translate(0,18)">硅铁线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4988.333333 -166.000000) translate(0,18)">碧城II回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 5122.000000 -241.000000) translate(0,18)">旁路线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4325.000000 -815.000000) translate(0,18)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 3519.000000 -612.000000) translate(0,18)">10kVI段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3923.000000 -1126.000000) translate(0,15)">30227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3825.000000 -1131.000000) translate(0,15)">3021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3825.000000 -1034.000000) translate(0,15)">3022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4959.000000 -1123.000000) translate(0,15)">30117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4861.000000 -1130.000000) translate(0,15)">3012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4859.000000 -1033.000000) translate(0,15)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3521.000000 -520.000000) translate(0,15)">415</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3652.000000 -520.000000) translate(0,15)">413</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3650.000000 -570.000000) translate(0,15)">4131</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3878.000000 -520.000000) translate(0,15)">411</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3865.000000 -565.000000) translate(0,15)">4111</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3868.000000 -382.000000) translate(0,15)">4113</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3972.000000 -313.000000) translate(0,15)">4114</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3999.000000 -520.000000) translate(0,15)">412</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3998.000000 -562.000000) translate(0,15)">4121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4045.000000 -382.000000) translate(0,15)">4123</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4094.000000 -313.000000) translate(0,15)">4124</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4157.000000 -194.000000) translate(0,15)">424</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4155.000000 -241.000000) translate(0,15)">4241</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4398.000000 -520.000000) translate(0,15)">404</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4332.000000 -568.000000) translate(0,15)">4041</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4398.000000 -520.000000) translate(0,15)">4042</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4729.000000 -197.000000) translate(0,15)">414</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4728.000000 -241.000000) translate(0,15)">4141</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4812.000000 -520.000000) translate(0,15)">423</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4806.000000 -572.000000) translate(0,15)">4231</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4810.000000 -384.000000) translate(0,15)">4233</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4907.000000 -311.000000) translate(0,15)">4234</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4968.000000 -520.000000) translate(0,15)">422</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4957.000000 -571.000000) translate(0,15)">4221</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4957.000000 -386.000000) translate(0,15)">4223</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5062.000000 -313.000000) translate(0,15)">4224</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5106.000000 -520.000000) translate(0,15)">421</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5095.000000 -560.000000) translate(0,15)">4211</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5149.000000 -377.000000) translate(0,15)">4213</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3882.000000 -688.000000) translate(0,15)">401</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3882.000000 -746.000000) translate(0,15)">4011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3882.000000 -637.000000) translate(0,15)">4012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3906.000000 -911.000000) translate(0,15)">313</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3889.000000 -964.000000) translate(0,15)">3131</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4256.000000 -908.000000) translate(0,15)">312</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4245.000000 -969.000000) translate(0,15)">3121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4234.000000 -692.000000) translate(0,15)">402</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4234.000000 -750.000000) translate(0,15)">4021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4234.000000 -641.000000) translate(0,15)">4022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4790.000000 -911.000000) translate(0,15)">311</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4788.000000 -967.000000) translate(0,15)">3111</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4835.000000 -683.000000) translate(0,15)">403</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4833.000000 -632.000000) translate(0,15)">4032</text>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="AC-LF_BC.LF_BC_35M">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3759,-991 4996,-991 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="17417" ObjectName="BS-LF_BC.LF_BC_35M"/>
    <cge:TPSR_Ref TObjectID="17417"/></metadata>
   <polyline fill="none" opacity="0" points="3759,-991 4996,-991 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="AC-LF_BC.LF_BC_10IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3525,-590 4348,-590 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="17418" ObjectName="BS-LF_BC.LF_BC_10IM"/>
    <cge:TPSR_Ref TObjectID="17418"/></metadata>
   <polyline fill="none" opacity="0" points="3525,-590 4348,-590 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="AC-LF_BC.LF_BC_10IIM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4396,-590 5216,-590 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="17419" ObjectName="BS-LF_BC.LF_BC_10IIM"/>
    <cge:TPSR_Ref TObjectID="17419"/></metadata>
   <polyline fill="none" opacity="0" points="4396,-590 5216,-590 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="AC-LF_BC.LF_BC_10M">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3492,-264 5246,-264 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="17426" ObjectName="BS-LF_BC.LF_BC_10M"/>
    <cge:TPSR_Ref TObjectID="17426"/></metadata>
   <polyline fill="none" opacity="0" points="3492,-264 5246,-264 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectPoint_Layer"/><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" stationName="LF_BC"/>
</svg>