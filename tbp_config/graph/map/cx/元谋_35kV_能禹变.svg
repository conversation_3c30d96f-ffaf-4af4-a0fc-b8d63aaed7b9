<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-238" aopId="3936770" id="thSvg" product="E8000V2" version="1.0" viewBox="-736 -1306 2178 1441">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="capacitor:shape20">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="37" x2="37" y1="13" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.504561" x1="37" x2="37" y1="102" y2="115"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.550926" x1="57" x2="57" y1="116" y2="110"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.431962" x1="2" x2="2" y1="63" y2="97"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.550926" x1="20" x2="20" y1="116" y2="110"/>
    <rect height="26" stroke-width="0.398039" width="12" x="31" y="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.603704" x1="13" x2="37" y1="105" y2="105"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.393258" x1="21" x2="56" y1="116" y2="116"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.603704" x1="13" x2="37" y1="53" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.715488" x1="13" x2="13" y1="53" y2="62"/>
    <polyline arcFlag="1" points="13,73 12,73 12,73 11,73 10,73 10,72 9,72 8,71 8,71 8,70 7,69 7,69 7,68 7,67 7,66 7,66 8,65 8,64 8,64 9,63 10,63 10,62 11,62 12,62 12,62 13,62 " stroke-width="0.0428972"/>
    <polyline arcFlag="1" points="13,84 12,84 12,84 11,84 10,84 10,83 9,83 8,82 8,82 8,81 7,80 7,80 7,79 7,78 7,77 7,77 8,76 8,75 8,75 9,74 10,74 10,73 11,73 12,73 12,73 13,73 " stroke-width="0.0428972"/>
    <polyline arcFlag="1" points="13,95 12,95 12,95 11,95 10,95 10,94 9,94 8,93 8,93 8,92 7,91 7,91 7,90 7,89 7,88 7,88 8,87 8,86 8,86 9,85 10,85 10,84 11,84 12,84 12,84 13,84 " stroke-width="0.0428972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.715488" x1="13" x2="13" y1="95" y2="104"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="27" x2="47" y1="101" y2="101"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="27" x2="47" y1="93" y2="93"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.774005" x1="37" x2="37" y1="26" y2="93"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="24" x2="36" y1="26" y2="26"/>
    <polyline arcFlag="1" points="37,13 39,13 41,14 42,14 44,15 45,16 47,17 48,19 49,21 49,22 50,24 50,26 50,28 49,30 49,31 48,33 47,34 45,36 44,37 42,38 41,38 39,39 37,39 35,39 33,38 32,38 30,37 29,36 27,34 26,33 25,31 25,30 24,28 24,26 " stroke-width="0.0972"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="8" x2="8" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape157">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,36 0,46 10,46 5,36 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,24 0,14 10,14 5,24 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="59" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape4">
    <rect height="31" stroke-width="2" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="9" x2="9" y1="35" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="63" x2="63" y1="6" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="61" x2="61" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="58" x2="58" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="58" x2="49" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <rect height="14" stroke-width="1" width="27" x="18" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="5" x2="5" y1="11" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="9" x2="18" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="8" x2="8" y1="2" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="58" x2="23" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape28">
    <circle cx="14" cy="13" fillStyle="0" r="13.5" stroke-width="1"/>
    <circle cx="14" cy="34" fillStyle="0" r="14" stroke-width="1"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="load:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="5" y2="29"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,19 9,31 17,19 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape30_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="31" x2="14" y1="13" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape30_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="12" x2="34" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape30-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="31" x2="14" y1="13" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape30-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="12" x2="34" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape19_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="14" y1="30" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="26" y1="38" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="14" y1="26" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="7" y1="38" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="27" y1="17" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="5" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="5" y1="46" y2="10"/>
    <rect height="26" stroke-width="0.416609" width="14" x="-2" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="34" y1="10" y2="40"/>
    <rect height="29" stroke-width="0.416609" width="9" x="12" y="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="5" y1="46" y2="10"/>
    <rect height="26" stroke-width="0.416609" width="14" x="-2" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape25_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="4" x2="10" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="49" y2="58"/>
    <rect height="29" stroke-width="0.416609" width="9" x="14" y="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="7" x2="36" y1="14" y2="44"/>
   </symbol>
   <symbol id="switch2:shape25_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="4" x2="10" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="49" y2="58"/>
    <rect height="26" stroke-width="0.416609" width="14" x="0" y="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="7" x2="7" y1="50" y2="14"/>
   </symbol>
   <symbol id="switch2:shape25-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="7" x2="36" y1="14" y2="44"/>
    <rect height="29" stroke-width="0.416609" width="9" x="14" y="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="4" x2="10" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="5" y2="14"/>
   </symbol>
   <symbol id="switch2:shape25-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="7" x2="7" y1="50" y2="14"/>
    <rect height="26" stroke-width="0.416609" width="14" x="0" y="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="4" x2="10" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="5" y2="14"/>
   </symbol>
   <symbol id="transformer2:shape88_0">
    <circle cx="63" cy="36" fillStyle="0" r="27" stroke-width="0.650262"/>
    <rect height="28" stroke-width="1" width="14" x="1" y="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="6" x2="9" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="4" x2="12" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.895105" x1="8" x2="8" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="14" x2="2" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="8" x2="8" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.62069" x1="44" x2="8" y1="55" y2="55"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="94,103 94,96 " stroke-width="3"/>
    <polyline DF8003:Layer="PUBLIC" points="63,40 56,25 71,25 63,40 63,40 63,40 "/>
   </symbol>
   <symbol id="transformer2:shape88_1">
    <circle cx="63" cy="74" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="88,103 94,103 31,40 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="63" x2="63" y1="79" y2="68"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="63" x2="63" y1="79" y2="68"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="74" x2="63" y1="87" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="74" x2="63" y1="87" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="63" x2="51" y1="78" y2="87"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="63" x2="51" y1="78" y2="87"/>
   </symbol>
   <symbol id="transformer2:shape12_0">
    <circle cx="16" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,57 40,57 40,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="42" y2="0"/>
    <polyline DF8003:Layer="PUBLIC" points="15,14 21,27 9,27 15,14 15,15 15,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="25" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="42" x2="39" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="15" y1="52" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="57" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="57" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="56" y2="52"/>
   </symbol>
   <symbol id="transformer2:shape12_1">
    <circle cx="16" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="82" y2="87"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="81" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="80" y2="76"/>
   </symbol>
   <symbol id="voltageTransformer:shape66">
    <polyline points="12,39 12,4 " stroke-width="1"/>
    <rect height="24" stroke-width="0.361111" width="11" x="6" y="10"/>
    <ellipse cx="12" cy="52" fillStyle="0" rx="11.5" ry="13" stroke-width="0.236902"/>
    <ellipse cx="29" cy="61" fillStyle="0" rx="11.5" ry="12.5" stroke-width="0.236902"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236902" x1="13" x2="13" y1="76" y2="81"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236902" x1="17" x2="13" y1="72" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236902" x1="13" x2="10" y1="76" y2="72"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236902" x1="13" x2="13" y1="62" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236902" x1="16" x2="13" y1="54" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236902" x1="13" x2="9" y1="58" y2="54"/>
    <ellipse cx="13" cy="74" fillStyle="0" rx="11" ry="12.5" stroke-width="0.236902"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="29,66 29,58 35,61 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="33" y1="66" y2="63"/>
   </symbol>
   <symbol id="voltageTransformer:shape23">
    <rect height="31" stroke-width="1" width="16" x="32" y="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="40" y1="57" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.32675" x1="42" x2="42" y1="85" y2="89"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.17171" x1="22" x2="31" y1="75" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.21568" x1="27" x2="22" y1="67" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.21447" x1="27" x2="31" y1="67" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.3267" x1="47" x2="42" y1="62" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.32675" x1="42" x2="42" y1="65" y2="69"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.3267" x1="37" x2="42" y1="62" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.3267" x1="61" x2="57" y1="69" y2="73"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.32675" x1="57" x2="57" y1="73" y2="77"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.3267" x1="52" x2="56" y1="69" y2="73"/>
    <ellipse cx="27" cy="73" fillStyle="0" rx="9.5" ry="11" stroke-width="0.695459"/>
    <ellipse cx="42" cy="82" fillStyle="0" rx="10" ry="11" stroke-width="0.695459"/>
    <ellipse cx="55" cy="73" fillStyle="0" rx="9.5" ry="11" stroke-width="0.695459"/>
    <ellipse cx="41" cy="67" fillStyle="0" rx="10" ry="11" stroke-width="0.695459"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.3267" x1="37" x2="42" y1="82" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.3267" x1="47" x2="42" y1="82" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="1" x2="12" y1="127" y2="127"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.327273" x1="7" x2="7" y1="127" y2="116"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="10" x2="3" y1="131" y2="131"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="8" x2="5" y1="134" y2="134"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="91"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="8" x2="8" y1="77" y2="112"/>
    <polyline points="41,65 8,65 8,75 " stroke-width="1"/>
   </symbol>
   <symbol id="voltageTransformer:shape50">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="12" y1="8" y2="11"/>
    <circle cx="9" cy="22" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="9" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="23" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="6" y1="23" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="12" y1="23" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="8" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="6" y1="7" y2="11"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_347e3f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_347f580" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_347ff30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3480c00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3481e60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3482a80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_34834e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_3483fa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2eeec20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2eeec20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3487370" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3487370" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3489100" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3489100" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_348a120" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_348bd20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_348c910" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_348d5d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_348de60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_348f620" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3490320" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3490be0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_34913a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3492160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3492ae0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_34935d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3493f90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3495430" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3495fa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3496fd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3497c10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_34a63e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3499290" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_349a430" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_349ba10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1451" width="2188" x="-741" y="-1311"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" lineStyle="2" stroke="rgb(0,255,0)" stroke-dasharray="10 5 " stroke-width="1" x1="-278" x2="-278" y1="-40" y2="124"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="-313" x2="-313" y1="-9" y2="18"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="-313" x2="-313" y1="47" y2="74"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="735" x2="591" y1="102" y2="102"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="791" x2="764" y1="102" y2="102"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="4.62979" x1="794" x2="794" y1="88" y2="115"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="27" stroke="rgb(0,255,0)" stroke-width="1" width="14" x="-320" y="19"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="14" stroke="rgb(0,255,0)" stroke-width="1" width="27" x="736" y="95"/>
   <rect DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" fillStyle="1" height="42" stroke="rgb(255,255,255)" stroke-width="1" width="72" x="-448" y="-1254"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-188044">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 104.333333 -990.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28640" ObjectName="SW-YM_NY.YM_NY_3311SW"/>
     <cge:Meas_Ref ObjectId="188044"/>
    <cge:TPSR_Ref TObjectID="28640"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188045">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 104.333333 -1117.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28641" ObjectName="SW-YM_NY.YM_NY_3316SW"/>
     <cge:Meas_Ref ObjectId="188045"/>
    <cge:TPSR_Ref TObjectID="28641"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188046">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 135.819337 -1172.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28642" ObjectName="SW-YM_NY.YM_NY_33167SW"/>
     <cge:Meas_Ref ObjectId="188046"/>
    <cge:TPSR_Ref TObjectID="28642"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188522">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 424.333333 -991.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28715" ObjectName="SW-YM_NY.YM_NY_3118SW"/>
     <cge:Meas_Ref ObjectId="188522"/>
    <cge:TPSR_Ref TObjectID="28715"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188523">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 462.752670 -1059.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28716" ObjectName="SW-YM_NY.YM_NY_3110SW"/>
     <cge:Meas_Ref ObjectId="188523"/>
    <cge:TPSR_Ref TObjectID="28716"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188058">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 781.333333 -989.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28644" ObjectName="SW-YM_NY.YM_NY_3321SW"/>
     <cge:Meas_Ref ObjectId="188058"/>
    <cge:TPSR_Ref TObjectID="28644"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188059">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 781.333333 -1116.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28645" ObjectName="SW-YM_NY.YM_NY_3326SW"/>
     <cge:Meas_Ref ObjectId="188059"/>
    <cge:TPSR_Ref TObjectID="28645"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188102">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 39.000000 -895.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28648" ObjectName="SW-YM_NY.YM_NY_3011SW"/>
     <cge:Meas_Ref ObjectId="188102"/>
    <cge:TPSR_Ref TObjectID="28648"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188242">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -236.000000 -237.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28660" ObjectName="SW-YM_NY.YM_NY_4113SW"/>
     <cge:Meas_Ref ObjectId="188242"/>
    <cge:TPSR_Ref TObjectID="28660"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-318922">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -202.180663 -447.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49389" ObjectName="SW-YM_NY.YM_NY_4110SW"/>
     <cge:Meas_Ref ObjectId="318922"/>
    <cge:TPSR_Ref TObjectID="49389"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188460">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -130.000000 -799.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28701" ObjectName="SW-YM_NY.YM_NY_0316SW"/>
     <cge:Meas_Ref ObjectId="188460"/>
    <cge:TPSR_Ref TObjectID="28701"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188462">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -98.180663 -845.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28703" ObjectName="SW-YM_NY.YM_NY_03167SW"/>
     <cge:Meas_Ref ObjectId="188462"/>
    <cge:TPSR_Ref TObjectID="28703"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188461">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -102.180663 -603.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28702" ObjectName="SW-YM_NY.YM_NY_03117SW"/>
     <cge:Meas_Ref ObjectId="188461"/>
    <cge:TPSR_Ref TObjectID="28702"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188060">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 810.819337 -1173.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28646" ObjectName="SW-YM_NY.YM_NY_33267SW"/>
     <cge:Meas_Ref ObjectId="188060"/>
    <cge:TPSR_Ref TObjectID="28646"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188105">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 39.000000 -677.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28651" ObjectName="SW-YM_NY.YM_NY_0016SW"/>
     <cge:Meas_Ref ObjectId="188105"/>
    <cge:TPSR_Ref TObjectID="28651"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188104">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 39.000000 -553.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28650" ObjectName="SW-YM_NY.YM_NY_0011SW"/>
     <cge:Meas_Ref ObjectId="188104"/>
    <cge:TPSR_Ref TObjectID="28650"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188459">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -130.000000 -664.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28700" ObjectName="SW-YM_NY.YM_NY_0312SW"/>
     <cge:Meas_Ref ObjectId="188459"/>
    <cge:TPSR_Ref TObjectID="28700"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188458">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -130.000000 -556.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28699" ObjectName="SW-YM_NY.YM_NY_0311SW"/>
     <cge:Meas_Ref ObjectId="188458"/>
    <cge:TPSR_Ref TObjectID="28699"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188482">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1130.000000 -795.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28708" ObjectName="SW-YM_NY.YM_NY_0426SW"/>
     <cge:Meas_Ref ObjectId="188482"/>
    <cge:TPSR_Ref TObjectID="28708"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188484">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1163.819337 -840.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28710" ObjectName="SW-YM_NY.YM_NY_04267SW"/>
     <cge:Meas_Ref ObjectId="188484"/>
    <cge:TPSR_Ref TObjectID="28710"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188483">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1157.819337 -601.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28709" ObjectName="SW-YM_NY.YM_NY_04227SW"/>
     <cge:Meas_Ref ObjectId="188483"/>
    <cge:TPSR_Ref TObjectID="28709"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188481">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1130.000000 -662.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28707" ObjectName="SW-YM_NY.YM_NY_0423SW"/>
     <cge:Meas_Ref ObjectId="188481"/>
    <cge:TPSR_Ref TObjectID="28707"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188480">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1130.000000 -554.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28706" ObjectName="SW-YM_NY.YM_NY_0422SW"/>
     <cge:Meas_Ref ObjectId="188480"/>
    <cge:TPSR_Ref TObjectID="28706"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188175">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 889.000000 -681.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28656" ObjectName="SW-YM_NY.YM_NY_0026SW"/>
     <cge:Meas_Ref ObjectId="188175"/>
    <cge:TPSR_Ref TObjectID="28656"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188526">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -247.000000 -541.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28719" ObjectName="SW-YM_NY.YM_NY_0410SW"/>
     <cge:Meas_Ref ObjectId="188526"/>
    <cge:TPSR_Ref TObjectID="28719"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188524">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 279.000000 -552.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28717" ObjectName="SW-YM_NY.YM_NY_4118SW"/>
     <cge:Meas_Ref ObjectId="188524"/>
    <cge:TPSR_Ref TObjectID="28717"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317181">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 318.819337 -616.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48913" ObjectName="SW-YM_NY.YM_NY_4100SW"/>
     <cge:Meas_Ref ObjectId="317181"/>
    <cge:TPSR_Ref TObjectID="48913"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188525">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 696.000000 -557.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28718" ObjectName="SW-YM_NY.YM_NY_4318SW"/>
     <cge:Meas_Ref ObjectId="188525"/>
    <cge:TPSR_Ref TObjectID="28718"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317182">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 735.819337 -621.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48914" ObjectName="SW-YM_NY.YM_NY_4300SW"/>
     <cge:Meas_Ref ObjectId="317182"/>
    <cge:TPSR_Ref TObjectID="48914"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188504">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 569.000000 -584.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28714" ObjectName="SW-YM_NY.YM_NY_0122SW"/>
     <cge:Meas_Ref ObjectId="188504"/>
    <cge:TPSR_Ref TObjectID="28714"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188241">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -236.000000 -357.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28659" ObjectName="SW-YM_NY.YM_NY_4112SW"/>
     <cge:Meas_Ref ObjectId="188241"/>
    <cge:TPSR_Ref TObjectID="28659"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188240">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -236.000000 -454.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28658" ObjectName="SW-YM_NY.YM_NY_4111SW"/>
     <cge:Meas_Ref ObjectId="188240"/>
    <cge:TPSR_Ref TObjectID="28658"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188286">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 64.000000 -238.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28668" ObjectName="SW-YM_NY.YM_NY_4133SW"/>
     <cge:Meas_Ref ObjectId="188286"/>
    <cge:TPSR_Ref TObjectID="28668"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-319320">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 97.819337 -448.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49496" ObjectName="SW-YM_NY.YM_NY_4130SW"/>
     <cge:Meas_Ref ObjectId="319320"/>
    <cge:TPSR_Ref TObjectID="49496"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188285">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 64.000000 -358.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28667" ObjectName="SW-YM_NY.YM_NY_4132SW"/>
     <cge:Meas_Ref ObjectId="188285"/>
    <cge:TPSR_Ref TObjectID="28667"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188284">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 64.000000 -466.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28666" ObjectName="SW-YM_NY.YM_NY_4131SW"/>
     <cge:Meas_Ref ObjectId="188284"/>
    <cge:TPSR_Ref TObjectID="28666"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188308">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 225.000000 -239.067797)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28672" ObjectName="SW-YM_NY.YM_NY_4143SW"/>
     <cge:Meas_Ref ObjectId="188308"/>
    <cge:TPSR_Ref TObjectID="28672"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-319357">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 258.819337 -449.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49500" ObjectName="SW-YM_NY.YM_NY_4140SW"/>
     <cge:Meas_Ref ObjectId="319357"/>
    <cge:TPSR_Ref TObjectID="49500"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188307">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 225.000000 -359.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28671" ObjectName="SW-YM_NY.YM_NY_4142SW"/>
     <cge:Meas_Ref ObjectId="188307"/>
    <cge:TPSR_Ref TObjectID="28671"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188306">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 225.000000 -470.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28670" ObjectName="SW-YM_NY.YM_NY_4141SW"/>
     <cge:Meas_Ref ObjectId="188306"/>
    <cge:TPSR_Ref TObjectID="28670"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188330">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 373.000000 -240.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28676" ObjectName="SW-YM_NY.YM_NY_4153SW"/>
     <cge:Meas_Ref ObjectId="188330"/>
    <cge:TPSR_Ref TObjectID="28676"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-319898">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 406.819337 -450.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49691" ObjectName="SW-YM_NY.YM_NY_4150SW"/>
     <cge:Meas_Ref ObjectId="319898"/>
    <cge:TPSR_Ref TObjectID="49691"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188329">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 373.000000 -360.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28675" ObjectName="SW-YM_NY.YM_NY_4152SW"/>
     <cge:Meas_Ref ObjectId="188329"/>
    <cge:TPSR_Ref TObjectID="28675"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188328">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 373.000000 -471.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28674" ObjectName="SW-YM_NY.YM_NY_4151SW"/>
     <cge:Meas_Ref ObjectId="188328"/>
    <cge:TPSR_Ref TObjectID="28674"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188374">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 746.000000 -239.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28684" ObjectName="SW-YM_NY.YM_NY_4323SW"/>
     <cge:Meas_Ref ObjectId="188374"/>
    <cge:TPSR_Ref TObjectID="28684"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-318672">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 779.819337 -449.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49328" ObjectName="SW-YM_NY.YM_NY_4320SW"/>
     <cge:Meas_Ref ObjectId="318672"/>
    <cge:TPSR_Ref TObjectID="49328"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188373">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 746.000000 -359.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28683" ObjectName="SW-YM_NY.YM_NY_4322SW"/>
     <cge:Meas_Ref ObjectId="188373"/>
    <cge:TPSR_Ref TObjectID="28683"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188372">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 746.000000 -465.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28682" ObjectName="SW-YM_NY.YM_NY_4321SW"/>
     <cge:Meas_Ref ObjectId="188372"/>
    <cge:TPSR_Ref TObjectID="28682"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188393">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 903.000000 -239.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28688" ObjectName="SW-YM_NY.YM_NY_4333SW"/>
     <cge:Meas_Ref ObjectId="188393"/>
    <cge:TPSR_Ref TObjectID="28688"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 936.819337 -449.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188392">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 903.000000 -359.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28687" ObjectName="SW-YM_NY.YM_NY_4332SW"/>
     <cge:Meas_Ref ObjectId="188392"/>
    <cge:TPSR_Ref TObjectID="28687"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188391">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 903.000000 -464.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28686" ObjectName="SW-YM_NY.YM_NY_4331SW"/>
     <cge:Meas_Ref ObjectId="188391"/>
    <cge:TPSR_Ref TObjectID="28686"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-318845">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1133.819337 -451.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49384" ObjectName="SW-YM_NY.YM_NY_4340SW"/>
     <cge:Meas_Ref ObjectId="318845"/>
    <cge:TPSR_Ref TObjectID="49384"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188414">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1105.000000 -349.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28691" ObjectName="SW-YM_NY.YM_NY_4342SW"/>
     <cge:Meas_Ref ObjectId="188414"/>
    <cge:TPSR_Ref TObjectID="28691"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188413">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1105.000000 -468.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28690" ObjectName="SW-YM_NY.YM_NY_4341SW"/>
     <cge:Meas_Ref ObjectId="188413"/>
    <cge:TPSR_Ref TObjectID="28690"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188415">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1008.000000 -334.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28692" ObjectName="SW-YM_NY.YM_NY_Z0116SW"/>
     <cge:Meas_Ref ObjectId="188415"/>
    <cge:TPSR_Ref TObjectID="28692"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188416">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1188.000000 -331.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28693" ObjectName="SW-YM_NY.YM_NY_Z0126SW"/>
     <cge:Meas_Ref ObjectId="188416"/>
    <cge:TPSR_Ref TObjectID="28693"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1031.819337 -257.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1206.819337 -255.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188438">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1304.000000 -237.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28697" ObjectName="SW-YM_NY.YM_NY_4353SW"/>
     <cge:Meas_Ref ObjectId="188438"/>
    <cge:TPSR_Ref TObjectID="28697"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1337.819337 -447.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188437">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1304.000000 -357.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28696" ObjectName="SW-YM_NY.YM_NY_4352SW"/>
     <cge:Meas_Ref ObjectId="188437"/>
    <cge:TPSR_Ref TObjectID="28696"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188436">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1304.000000 -466.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28695" ObjectName="SW-YM_NY.YM_NY_4351SW"/>
     <cge:Meas_Ref ObjectId="188436"/>
    <cge:TPSR_Ref TObjectID="28695"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188503">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 425.000000 -546.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28713" ObjectName="SW-YM_NY.YM_NY_0121SW"/>
     <cge:Meas_Ref ObjectId="188503"/>
    <cge:TPSR_Ref TObjectID="28713"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188463">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -99.180663 -782.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28704" ObjectName="SW-YM_NY.YM_NY_03160SW"/>
     <cge:Meas_Ref ObjectId="188463"/>
    <cge:TPSR_Ref TObjectID="28704"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188485">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1165.819337 -776.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28711" ObjectName="SW-YM_NY.YM_NY_04260SW"/>
     <cge:Meas_Ref ObjectId="188485"/>
    <cge:TPSR_Ref TObjectID="28711"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-220037">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 582.000000 -215.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34413" ObjectName="SW-YM_NY.YM_NY_0414SW"/>
     <cge:Meas_Ref ObjectId="220037"/>
    <cge:TPSR_Ref TObjectID="34413"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 615.819337 -425.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34414" ObjectName="SW-YM_NY.YM_NY_04127SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="34414"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188350">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 582.000000 -335.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28678" ObjectName="SW-YM_NY.YM_NY_0413SW"/>
     <cge:Meas_Ref ObjectId="188350"/>
    <cge:TPSR_Ref TObjectID="28678"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188351">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 582.000000 -456.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28679" ObjectName="SW-YM_NY.YM_NY_0412SW"/>
     <cge:Meas_Ref ObjectId="188351"/>
    <cge:TPSR_Ref TObjectID="28679"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -55.180663 -429.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34415" ObjectName="SW-YM_NY.YM_NY_03217SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="34415"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188263">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -89.000000 -339.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34406" ObjectName="SW-YM_NY.YM_NY_0323SW"/>
     <cge:Meas_Ref ObjectId="188263"/>
    <cge:TPSR_Ref TObjectID="34406"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188262">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -89.000000 -460.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34405" ObjectName="SW-YM_NY.YM_NY_0321SW"/>
     <cge:Meas_Ref ObjectId="188262"/>
    <cge:TPSR_Ref TObjectID="34405"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188264">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -226.000000 -5.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34407" ObjectName="SW-YM_NY.YM_NY_0326SW"/>
     <cge:Meas_Ref ObjectId="188264"/>
    <cge:TPSR_Ref TObjectID="34407"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188352">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -225.000000 79.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28680" ObjectName="SW-YM_NY.YM_NY_0416SW"/>
     <cge:Meas_Ref ObjectId="188352"/>
    <cge:TPSR_Ref TObjectID="28680"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 29.000000 -1157.000000)" xlink:href="#switch2:shape19_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 706.000000 -1157.000000)" xlink:href="#switch2:shape19_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1120.819337 -263.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.400000 -0.000000 0.000000 -0.564516 96.200000 -203.000000)" xlink:href="#switch2:shape25_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188172">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 889.000000 -899.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28653" ObjectName="SW-YM_NY.YM_NY_3021SW"/>
     <cge:Meas_Ref ObjectId="188172"/>
    <cge:TPSR_Ref TObjectID="28653"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188174">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 890.000000 -557.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28655" ObjectName="SW-YM_NY.YM_NY_0022SW"/>
     <cge:Meas_Ref ObjectId="188174"/>
    <cge:TPSR_Ref TObjectID="28655"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-YM_NY.YM_NY_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-28,-976 984,-976 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="28722" ObjectName="BS-YM_NY.YM_NY_3IM"/>
    <cge:TPSR_Ref TObjectID="28722"/></metadata>
   <polyline fill="none" opacity="0" points="-28,-976 984,-976 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-YM_NY.YM_NY_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-281,-530 455,-530 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="28723" ObjectName="BS-YM_NY.YM_NY_9IM"/>
    <cge:TPSR_Ref TObjectID="28723"/></metadata>
   <polyline fill="none" opacity="0" points="-281,-530 455,-530 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-YM_NY.YM_NY_9IIM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="545,-528 1370,-528 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="28724" ObjectName="BS-YM_NY.YM_NY_9IIM"/>
    <cge:TPSR_Ref TObjectID="28724"/></metadata>
   <polyline fill="none" opacity="0" points="545,-528 1370,-528 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-YM_NY.YM_NY_Cb2">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1102.000000 -855.000000)" xlink:href="#capacitor:shape20"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48925" ObjectName="CB-YM_NY.YM_NY_Cb2"/>
    <cge:TPSR_Ref TObjectID="48925"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-YM_NY.YM_NY_Cb1">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -158.000000 -866.000000)" xlink:href="#capacitor:shape20"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48924" ObjectName="CB-YM_NY.YM_NY_Cb1"/>
    <cge:TPSR_Ref TObjectID="48924"/></metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-YM_NY.YM_NY_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="40910"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -15.000000 -725.000000)" xlink:href="#transformer2:shape88_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -15.000000 -725.000000)" xlink:href="#transformer2:shape88_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="28720" ObjectName="TF-YM_NY.YM_NY_1T"/>
    <cge:TPSR_Ref TObjectID="28720"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-YM_NY.YM_NY_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="40914"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 835.000000 -741.000000)" xlink:href="#transformer2:shape88_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 835.000000 -741.000000)" xlink:href="#transformer2:shape88_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="28721" ObjectName="TF-YM_NY.YM_NY_2T"/>
    <cge:TPSR_Ref TObjectID="28721"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-0.804348 -0.000000 -0.000000 0.724490 -227.000000 -748.000000)" xlink:href="#transformer2:shape12_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(-0.804348 -0.000000 -0.000000 0.724490 -227.000000 -748.000000)" xlink:href="#transformer2:shape12_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2cec320">
    <use class="BV-35KV" transform="matrix(0.933333 0.000000 0.000000 -0.796610 388.129967 -1001.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bb7f50">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -232.000000 -285.125000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c7f4e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -126.000000 -715.125000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2cf5800">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1134.000000 -713.125000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d30ac0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -247.000000 -593.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c56400">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 68.000000 -286.125000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c37370">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 229.000000 -287.125000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b99f00">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 377.000000 -288.125000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bd9690">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 750.000000 -287.125000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b6f090">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 907.000000 -287.125000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bd7ba0">
    <use class="BV-10KV" transform="matrix(0.933333 0.000000 0.000000 -0.796610 978.063300 -331.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c169e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1109.000000 -275.125000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b097a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1012.000000 -271.125000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b0a1c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1192.000000 -267.125000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b12000">
    <use class="BV-10KV" transform="matrix(0.933333 0.000000 0.000000 -0.796610 1160.063300 -341.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bcd450">
    <use class="BV-10KV" transform="matrix(0.933333 0.000000 0.000000 -0.796610 1165.063300 -204.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bcf9b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1308.000000 -285.125000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c214f0">
    <use class="BV-10KV" transform="matrix(0.933333 0.000000 0.000000 -0.796610 1276.063300 -301.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c25df0">
    <use class="BV-10KV" transform="matrix(0.933333 0.000000 0.000000 -0.796610 1275.063300 -180.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d771a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 586.000000 -263.125000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b37ba0">
    <use class="BV-10KV" transform="matrix(0.933333 0.000000 0.000000 -0.796610 -261.936700 45.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b388d0">
    <use class="BV-10KV" transform="matrix(0.933333 0.000000 0.000000 -0.796610 -262.936700 135.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b43810">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 142.000000 -1218.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e31ef0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 820.000000 -1218.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e38f70">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 217.000000 -635.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e3b3e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 635.000000 -638.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e3cd20">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -318.000000 -345.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e3dad0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -171.000000 -327.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e3e880">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -319.000000 -224.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e3fc00">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -17.000000 -346.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e409b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -18.000000 -216.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e41d30">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 144.000000 -347.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e42ae0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 143.000000 -226.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e43e60">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 292.000000 -348.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e44c10">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 291.000000 -227.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e459c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 501.000000 -323.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e46770">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 500.000000 -202.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e47af0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 665.000000 -347.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e488a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 664.000000 -226.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e49c20">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 822.000000 -347.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e4a9d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 821.000000 -226.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e4b780">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1035.000000 -333.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e51460">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 950.000000 -255.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e569d0">
    <use class="BV-0KV" transform="matrix(0.535714 -0.000000 0.000000 -0.509434 92.000000 -176.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -622.000000 -1176.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-217877" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -592.000000 -1017.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217877" ObjectName="YM_NY:YM_NY_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-219737" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -592.000000 -978.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="219737" ObjectName="YM_NY:YM_NY_sumQ"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-187995" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 604.000000 -120.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="187995" ObjectName="YM_NY:YM_NY_041BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-187996" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 604.000000 -103.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="187996" ObjectName="YM_NY:YM_NY_041BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-187993" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 604.000000 -87.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="187993" ObjectName="YM_NY:YM_NY_041BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-187980" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -115.000000 -115.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="187980" ObjectName="YM_NY:YM_NY_032BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-187977" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -115.000000 -99.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="187977" ObjectName="YM_NY:YM_NY_032BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-187979" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -115.000000 -132.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="187979" ObjectName="YM_NY:YM_NY_032BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-217877" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -593.000000 -1099.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217877" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-217877" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -593.000000 -1059.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217877" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-187957" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 163.000000 -757.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="187957" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28720"/>
     <cge:Term_Ref ObjectID="40908"/>
    <cge:TPSR_Ref TObjectID="28720"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-187927" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 163.000000 -757.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="187927" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28720"/>
     <cge:Term_Ref ObjectID="40908"/>
    <cge:TPSR_Ref TObjectID="28720"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-187916" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 165.000000 -891.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="187916" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28647"/>
     <cge:Term_Ref ObjectID="40762"/>
    <cge:TPSR_Ref TObjectID="28647"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-187917" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 165.000000 -891.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="187917" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28647"/>
     <cge:Term_Ref ObjectID="40762"/>
    <cge:TPSR_Ref TObjectID="28647"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-187913" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 165.000000 -891.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="187913" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28647"/>
     <cge:Term_Ref ObjectID="40762"/>
    <cge:TPSR_Ref TObjectID="28647"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-187946" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 155.000000 -655.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="187946" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28649"/>
     <cge:Term_Ref ObjectID="40766"/>
    <cge:TPSR_Ref TObjectID="28649"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-187947" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 155.000000 -655.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="187947" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28649"/>
     <cge:Term_Ref ObjectID="40766"/>
    <cge:TPSR_Ref TObjectID="28649"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-187943" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 155.000000 -655.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="187943" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28649"/>
     <cge:Term_Ref ObjectID="40766"/>
    <cge:TPSR_Ref TObjectID="28649"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-187961" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1007.000000 -658.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="187961" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28654"/>
     <cge:Term_Ref ObjectID="40776"/>
    <cge:TPSR_Ref TObjectID="28654"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-187962" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1007.000000 -658.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="187962" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28654"/>
     <cge:Term_Ref ObjectID="40776"/>
    <cge:TPSR_Ref TObjectID="28654"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-187958" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1007.000000 -658.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="187958" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28654"/>
     <cge:Term_Ref ObjectID="40776"/>
    <cge:TPSR_Ref TObjectID="28654"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-187931" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1002.000000 -891.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="187931" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28652"/>
     <cge:Term_Ref ObjectID="40772"/>
    <cge:TPSR_Ref TObjectID="28652"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-187932" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1002.000000 -891.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="187932" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28652"/>
     <cge:Term_Ref ObjectID="40772"/>
    <cge:TPSR_Ref TObjectID="28652"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-187928" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1002.000000 -891.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="187928" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28652"/>
     <cge:Term_Ref ObjectID="40772"/>
    <cge:TPSR_Ref TObjectID="28652"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-187975" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -245.000000 -129.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="187975" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28657"/>
     <cge:Term_Ref ObjectID="40782"/>
    <cge:TPSR_Ref TObjectID="28657"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-187976" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -245.000000 -129.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="187976" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28657"/>
     <cge:Term_Ref ObjectID="40782"/>
    <cge:TPSR_Ref TObjectID="28657"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-187973" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -245.000000 -129.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="187973" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28657"/>
     <cge:Term_Ref ObjectID="40782"/>
    <cge:TPSR_Ref TObjectID="28657"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-187983" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 56.250000 -117.250000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="187983" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28665"/>
     <cge:Term_Ref ObjectID="40798"/>
    <cge:TPSR_Ref TObjectID="28665"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-187984" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 56.250000 -117.250000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="187984" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28665"/>
     <cge:Term_Ref ObjectID="40798"/>
    <cge:TPSR_Ref TObjectID="28665"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-187981" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 56.250000 -117.250000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="187981" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28665"/>
     <cge:Term_Ref ObjectID="40798"/>
    <cge:TPSR_Ref TObjectID="28665"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-187987" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 232.500000 -116.875000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="187987" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28669"/>
     <cge:Term_Ref ObjectID="40806"/>
    <cge:TPSR_Ref TObjectID="28669"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-187988" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 232.500000 -116.875000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="187988" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28669"/>
     <cge:Term_Ref ObjectID="40806"/>
    <cge:TPSR_Ref TObjectID="28669"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-187985" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 232.500000 -116.875000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="187985" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28669"/>
     <cge:Term_Ref ObjectID="40806"/>
    <cge:TPSR_Ref TObjectID="28669"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-187991" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 375.750000 -116.500000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="187991" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28673"/>
     <cge:Term_Ref ObjectID="40814"/>
    <cge:TPSR_Ref TObjectID="28673"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-187992" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 375.750000 -116.500000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="187992" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28673"/>
     <cge:Term_Ref ObjectID="40814"/>
    <cge:TPSR_Ref TObjectID="28673"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-187989" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 375.750000 -116.500000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="187989" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28673"/>
     <cge:Term_Ref ObjectID="40814"/>
    <cge:TPSR_Ref TObjectID="28673"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-187999" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 751.250000 -116.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="187999" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28681"/>
     <cge:Term_Ref ObjectID="40830"/>
    <cge:TPSR_Ref TObjectID="28681"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-188000" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 751.250000 -116.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188000" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28681"/>
     <cge:Term_Ref ObjectID="40830"/>
    <cge:TPSR_Ref TObjectID="28681"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-187997" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 751.250000 -116.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="187997" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28681"/>
     <cge:Term_Ref ObjectID="40830"/>
    <cge:TPSR_Ref TObjectID="28681"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-188003" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 908.500000 -118.625000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188003" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28685"/>
     <cge:Term_Ref ObjectID="40838"/>
    <cge:TPSR_Ref TObjectID="28685"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-188004" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 908.500000 -118.625000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188004" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28685"/>
     <cge:Term_Ref ObjectID="40838"/>
    <cge:TPSR_Ref TObjectID="28685"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-188001" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 908.500000 -118.625000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188001" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28685"/>
     <cge:Term_Ref ObjectID="40838"/>
    <cge:TPSR_Ref TObjectID="28685"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-188007" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1116.750000 -186.125000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188007" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28689"/>
     <cge:Term_Ref ObjectID="40846"/>
    <cge:TPSR_Ref TObjectID="28689"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-188008" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1116.750000 -186.125000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188008" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28689"/>
     <cge:Term_Ref ObjectID="40846"/>
    <cge:TPSR_Ref TObjectID="28689"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-188005" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1116.750000 -186.125000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188005" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28689"/>
     <cge:Term_Ref ObjectID="40846"/>
    <cge:TPSR_Ref TObjectID="28689"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-188011" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1302.000000 -116.750000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188011" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28694"/>
     <cge:Term_Ref ObjectID="40856"/>
    <cge:TPSR_Ref TObjectID="28694"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-188012" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1302.000000 -116.750000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188012" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28694"/>
     <cge:Term_Ref ObjectID="40856"/>
    <cge:TPSR_Ref TObjectID="28694"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-188009" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1302.000000 -116.750000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188009" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28694"/>
     <cge:Term_Ref ObjectID="40856"/>
    <cge:TPSR_Ref TObjectID="28694"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-187907" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 221.000000 -1099.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="187907" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28639"/>
     <cge:Term_Ref ObjectID="40746"/>
    <cge:TPSR_Ref TObjectID="28639"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-187908" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 221.000000 -1099.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="187908" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28639"/>
     <cge:Term_Ref ObjectID="40746"/>
    <cge:TPSR_Ref TObjectID="28639"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-187905" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 221.000000 -1099.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="187905" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28639"/>
     <cge:Term_Ref ObjectID="40746"/>
    <cge:TPSR_Ref TObjectID="28639"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-187911" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 903.000000 -1097.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="187911" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28643"/>
     <cge:Term_Ref ObjectID="40754"/>
    <cge:TPSR_Ref TObjectID="28643"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-187912" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 903.000000 -1097.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="187912" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28643"/>
     <cge:Term_Ref ObjectID="40754"/>
    <cge:TPSR_Ref TObjectID="28643"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-187909" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 903.000000 -1097.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="187909" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28643"/>
     <cge:Term_Ref ObjectID="40754"/>
    <cge:TPSR_Ref TObjectID="28643"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-187972" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1026.000000 -754.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="187972" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28721"/>
     <cge:Term_Ref ObjectID="40915"/>
    <cge:TPSR_Ref TObjectID="28721"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-187942" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1026.000000 -754.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="187942" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28721"/>
     <cge:Term_Ref ObjectID="40915"/>
    <cge:TPSR_Ref TObjectID="28721"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-188021" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 536.000000 -727.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188021" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28712"/>
     <cge:Term_Ref ObjectID="40892"/>
    <cge:TPSR_Ref TObjectID="28712"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-188022" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 536.000000 -727.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188022" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28712"/>
     <cge:Term_Ref ObjectID="40892"/>
    <cge:TPSR_Ref TObjectID="28712"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-188019" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 536.000000 -727.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188019" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28712"/>
     <cge:Term_Ref ObjectID="40892"/>
    <cge:TPSR_Ref TObjectID="28712"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-188015" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -20.000000 -668.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188015" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28698"/>
     <cge:Term_Ref ObjectID="40864"/>
    <cge:TPSR_Ref TObjectID="28698"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-188013" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -20.000000 -668.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188013" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28698"/>
     <cge:Term_Ref ObjectID="40864"/>
    <cge:TPSR_Ref TObjectID="28698"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-187964" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1347.000000 -618.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="187964" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28724"/>
     <cge:Term_Ref ObjectID="40918"/>
    <cge:TPSR_Ref TObjectID="28724"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-187965" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1347.000000 -618.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="187965" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28724"/>
     <cge:Term_Ref ObjectID="40918"/>
    <cge:TPSR_Ref TObjectID="28724"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-187966" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1347.000000 -618.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="187966" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28724"/>
     <cge:Term_Ref ObjectID="40918"/>
    <cge:TPSR_Ref TObjectID="28724"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-187967" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1347.000000 -618.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="187967" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28724"/>
     <cge:Term_Ref ObjectID="40918"/>
    <cge:TPSR_Ref TObjectID="28724"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-187971" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1347.000000 -618.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="187971" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28724"/>
     <cge:Term_Ref ObjectID="40918"/>
    <cge:TPSR_Ref TObjectID="28724"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-187949" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -319.000000 -613.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="187949" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28723"/>
     <cge:Term_Ref ObjectID="40917"/>
    <cge:TPSR_Ref TObjectID="28723"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-187950" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -319.000000 -613.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="187950" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28723"/>
     <cge:Term_Ref ObjectID="40917"/>
    <cge:TPSR_Ref TObjectID="28723"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-187951" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -319.000000 -613.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="187951" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28723"/>
     <cge:Term_Ref ObjectID="40917"/>
    <cge:TPSR_Ref TObjectID="28723"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-187952" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -319.000000 -613.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="187952" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28723"/>
     <cge:Term_Ref ObjectID="40917"/>
    <cge:TPSR_Ref TObjectID="28723"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-187956" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -319.000000 -613.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="187956" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28723"/>
     <cge:Term_Ref ObjectID="40917"/>
    <cge:TPSR_Ref TObjectID="28723"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-187919" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -7.000000 -1086.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="187919" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28722"/>
     <cge:Term_Ref ObjectID="40916"/>
    <cge:TPSR_Ref TObjectID="28722"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-187920" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -7.000000 -1086.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="187920" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28722"/>
     <cge:Term_Ref ObjectID="40916"/>
    <cge:TPSR_Ref TObjectID="28722"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-187921" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -7.000000 -1086.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="187921" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28722"/>
     <cge:Term_Ref ObjectID="40916"/>
    <cge:TPSR_Ref TObjectID="28722"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-187922" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -7.000000 -1086.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="187922" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28722"/>
     <cge:Term_Ref ObjectID="40916"/>
    <cge:TPSR_Ref TObjectID="28722"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-187926" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -7.000000 -1086.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="187926" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28722"/>
     <cge:Term_Ref ObjectID="40916"/>
    <cge:TPSR_Ref TObjectID="28722"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-188018" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1248.000000 -667.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188018" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28705"/>
     <cge:Term_Ref ObjectID="40878"/>
    <cge:TPSR_Ref TObjectID="28705"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-188016" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1248.000000 -667.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188016" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28705"/>
     <cge:Term_Ref ObjectID="40878"/>
    <cge:TPSR_Ref TObjectID="28705"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="-623" y="-1251"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="-623" y="-1251"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-655" y="-1269"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-655" y="-1269"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="123" y="-1085"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="123" y="-1085"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="803" y="-1085"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="803" y="-1085"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="52" x="96" y="-826"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="52" x="96" y="-826"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="53" x="938" y="-825"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="53" x="938" y="-825"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="-113" y="-647"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="-113" y="-647"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="1148" y="-645"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="1148" y="-645"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="-219" y="-436"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="-219" y="-436"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="82" y="-437"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="82" y="-437"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="243" y="-438"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="243" y="-438"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="391" y="-439"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="391" y="-439"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="443" y="-631"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="443" y="-631"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="764" y="-438"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="764" y="-438"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="921" y="-437"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="921" y="-437"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1123" y="-429"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1123" y="-429"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1322" y="-436"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1322" y="-436"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="-355" y="-1211"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="-355" y="-1211"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="-355" y="-1250"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="-355" y="-1250"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="603" y="-411"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="603" y="-411"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="-68" y="-415"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="-68" y="-415"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="21" qtmmishow="hidden" width="75" x="-709" y="-856"/>
    </a>
   <metadata/><rect fill="white" height="21" opacity="0" stroke="white" transform="" width="75" x="-709" y="-856"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="44" qtmmishow="hidden" width="73" x="-448" y="-1254"/>
    </a>
   <metadata/><rect fill="white" height="44" opacity="0" stroke="white" transform="" width="73" x="-448" y="-1254"/></g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="-623" y="-1251"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-655" y="-1269"/></g>
   <g href="35kV能禹变YM_NY_331间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="123" y="-1085"/></g>
   <g href="35kV能禹变YM_NY_332间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="803" y="-1085"/></g>
   <g href="35kV能禹变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="52" x="96" y="-826"/></g>
   <g href="35kV能禹变2号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="53" x="938" y="-825"/></g>
   <g href="35kV能禹变YM_NY_031间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="-113" y="-647"/></g>
   <g href="35kV能禹变YM_NY_042间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="1148" y="-645"/></g>
   <g href="35kV能禹变YM_NY_411间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="-219" y="-436"/></g>
   <g href="35kV能禹变YM_NY_413间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="82" y="-437"/></g>
   <g href="35kV能禹变YM_NY_414间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="243" y="-438"/></g>
   <g href="35kV能禹变YM_NY_415间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="391" y="-439"/></g>
   <g href="35kV能禹变YM_NY_012间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="443" y="-631"/></g>
   <g href="35kV能禹变YM_NY_432间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="764" y="-438"/></g>
   <g href="35kV能禹变YM_NY_433间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="921" y="-437"/></g>
   <g href="35kV能禹变YM_NY_434间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1123" y="-429"/></g>
   <g href="35kV能禹变YM_NY_435间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1322" y="-436"/></g>
   <g href="cx_配调_配网接线图35_元谋.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="-355" y="-1211"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="-355" y="-1250"/></g>
   <g href="35kV能禹变YM_NY_041间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="603" y="-411"/></g>
   <g href="35kV能禹变10KV能禹II回线032断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="-68" y="-415"/></g>
   <g href="35kV能禹变新远动GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="21" qtmmishow="hidden" width="75" x="-709" y="-856"/></g>
   <g href="AVC能禹变电站.svg" style="fill-opacity:0"><rect height="44" qtmmishow="hidden" width="73" x="-448" y="-1254"/></g>
  </g><g id="MultiLine_Layer">
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="-245,-647 -229,-647 -239,-656 -245,-647 " stroke="rgb(0,255,0)" stroke-width="1"/>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2c43f20">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 421.000000 -1085.000000)" xlink:href="#voltageTransformer:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c1c750">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 248.000000 -671.000000)" xlink:href="#voltageTransformer:shape23"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2cc0f90">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 665.000000 -676.000000)" xlink:href="#voltageTransformer:shape23"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b454a0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 25.000000 -1119.000000)" xlink:href="#voltageTransformer:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b4a8e0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 703.000000 -1120.000000)" xlink:href="#voltageTransformer:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-YM_NY.411Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -236.356070 -157.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34196" ObjectName="EC-YM_NY.411Ld"/>
    <cge:TPSR_Ref TObjectID="34196"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YM_NY.413Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 63.643930 -151.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34198" ObjectName="EC-YM_NY.413Ld"/>
    <cge:TPSR_Ref TObjectID="34198"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YM_NY.414Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 224.643930 -152.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34199" ObjectName="EC-YM_NY.414Ld"/>
    <cge:TPSR_Ref TObjectID="34199"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YM_NY.415Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 372.643930 -153.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34200" ObjectName="EC-YM_NY.415Ld"/>
    <cge:TPSR_Ref TObjectID="34200"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YM_NY.432Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 745.643930 -152.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34202" ObjectName="EC-YM_NY.432Ld"/>
    <cge:TPSR_Ref TObjectID="34202"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YM_NY.433Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 902.643930 -152.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34203" ObjectName="EC-YM_NY.433Ld"/>
    <cge:TPSR_Ref TObjectID="34203"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1008.000000 -392.000000)" xlink:href="#load:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1188.000000 -395.000000)" xlink:href="#load:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YM_NY.435Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1303.643930 -150.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34204" ObjectName="EC-YM_NY.435Ld"/>
    <cge:TPSR_Ref TObjectID="34204"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YM_NY.032Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -89.356070 -153.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43473" ObjectName="EC-YM_NY.032Ld"/>
    <cge:TPSR_Ref TObjectID="43473"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YM_NY.041Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 581.643930 -146.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34201" ObjectName="EC-YM_NY.041Ld"/>
    <cge:TPSR_Ref TObjectID="34201"/></metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_2cf2da0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="177,-1177 191,-1177 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="28642@0" ObjectIDZND0="g_2cf2770@0" Pin0InfoVect0LinkObjId="g_2cf2770_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188046_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="177,-1177 191,-1177 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cf2f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="34,-1207 34,-1226 113,-1226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="28642@x" ObjectIDZND1="28641@x" ObjectIDZND2="g_2b43810@0" Pin0InfoVect0LinkObjId="SW-188046_0" Pin0InfoVect1LinkObjId="SW-188045_0" Pin0InfoVect2LinkObjId="g_2b43810_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="34,-1207 34,-1226 113,-1226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cecaf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="394,-1044 394,-1063 434,-1063 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2cec320@0" ObjectIDZND0="g_2c43f20@0" ObjectIDZND1="28715@x" ObjectIDZND2="28716@x" Pin0InfoVect0LinkObjId="g_2c43f20_0" Pin0InfoVect1LinkObjId="SW-188522_0" Pin0InfoVect2LinkObjId="SW-188523_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2cec320_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="394,-1044 394,-1063 434,-1063 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cecce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="433,-1063 433,-1088 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="voltageTransformer" ObjectIDND0="g_2cec320@0" ObjectIDND1="28715@x" ObjectIDND2="28716@x" ObjectIDZND0="g_2c43f20@0" Pin0InfoVect0LinkObjId="g_2c43f20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2cec320_0" Pin1InfoVect1LinkObjId="SW-188522_0" Pin1InfoVect2LinkObjId="SW-188523_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="433,-1063 433,-1088 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c80bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="504,-1064 518,-1064 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="28716@0" ObjectIDZND0="g_2c80400@0" Pin0InfoVect0LinkObjId="g_2c80400_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188523_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="504,-1064 518,-1064 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c80da0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="468,-1064 434,-1064 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" EndDevType2="switch" ObjectIDND0="28716@1" ObjectIDZND0="g_2cec320@0" ObjectIDZND1="g_2c43f20@0" ObjectIDZND2="28715@x" Pin0InfoVect0LinkObjId="g_2cec320_0" Pin0InfoVect1LinkObjId="g_2c43f20_0" Pin0InfoVect2LinkObjId="SW-188522_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188523_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="468,-1064 434,-1064 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c80f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="141,-1177 113,-1177 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="28642@1" ObjectIDZND0="28641@x" ObjectIDZND1="g_2b43810@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-188045_0" Pin0InfoVect1LinkObjId="g_2b43810_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188046_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="141,-1177 113,-1177 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c817e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="113,-1177 113,-1158 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="28642@x" ObjectIDND1="g_2b43810@0" ObjectIDND2="0@x" ObjectIDZND0="28641@1" Pin0InfoVect0LinkObjId="SW-188045_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-188046_0" Pin1InfoVect1LinkObjId="g_2b43810_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="113,-1177 113,-1158 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2be5c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="48,-855 48,-826 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="28647@0" ObjectIDZND0="28720@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188101_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="48,-855 48,-826 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2caa100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-227,-290 -227,-278 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2bb7f50@0" ObjectIDZND0="28660@1" Pin0InfoVect0LinkObjId="SW-188242_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2bb7f50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-227,-290 -227,-278 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c7e000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-161,-452 -147,-452 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="49389@0" ObjectIDZND0="g_2cac170@0" Pin0InfoVect0LinkObjId="g_2cac170_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-318922_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-161,-452 -147,-452 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c7e3e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-261,-231 -227,-231 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_2e3e880@0" ObjectIDZND0="28660@x" ObjectIDZND1="34196@x" Pin0InfoVect0LinkObjId="SW-188242_0" Pin0InfoVect1LinkObjId="EC-YM_NY.411Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e3e880_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-261,-231 -227,-231 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c7eaa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-227,-242 -227,-231 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="28660@0" ObjectIDZND0="34196@x" ObjectIDZND1="g_2e3e880@0" Pin0InfoVect0LinkObjId="EC-YM_NY.411Ld_0" Pin0InfoVect1LinkObjId="g_2e3e880_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188242_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-227,-242 -227,-231 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c7ec90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-227,-231 -227,-184 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="28660@x" ObjectIDND1="g_2e3e880@0" ObjectIDZND0="34196@0" Pin0InfoVect0LinkObjId="EC-YM_NY.411Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-188242_0" Pin1InfoVect1LinkObjId="g_2e3e880_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-227,-231 -227,-184 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cdbbd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-57,-850 -43,-850 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="28703@0" ObjectIDZND0="g_2c204a0@0" Pin0InfoVect0LinkObjId="g_2c204a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188462_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-57,-850 -43,-850 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d0c880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-61,-608 -47,-608 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="28702@0" ObjectIDZND0="g_2d0bf50@0" Pin0InfoVect0LinkObjId="g_2d0bf50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188461_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-61,-608 -47,-608 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d0d220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="113,-1031 113,-1064 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28640@1" ObjectIDZND0="28639@0" Pin0InfoVect0LinkObjId="SW-188043_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188044_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="113,-1031 113,-1064 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d0d410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="113,-1091 113,-1122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="28639@1" ObjectIDZND0="28641@0" Pin0InfoVect0LinkObjId="SW-188045_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188043_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="113,-1091 113,-1122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d0d600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="790,-1030 790,-1063 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28644@1" ObjectIDZND0="28643@0" Pin0InfoVect0LinkObjId="SW-188057_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188058_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="790,-1030 790,-1063 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d0d7f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="790,-1090 790,-1121 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="28643@1" ObjectIDZND0="28645@0" Pin0InfoVect0LinkObjId="SW-188059_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188057_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="790,-1090 790,-1121 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c43d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="852,-1178 866,-1178 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="28646@0" ObjectIDZND0="g_2c433d0@0" Pin0InfoVect0LinkObjId="g_2c433d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188060_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="852,-1178 866,-1178 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c94720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="48,-900 48,-882 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28648@0" ObjectIDZND0="28647@1" Pin0InfoVect0LinkObjId="SW-188101_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188102_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="48,-900 48,-882 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c68780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="48,-734 48,-718 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="28720@0" ObjectIDZND0="28651@1" Pin0InfoVect0LinkObjId="SW-188105_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2be5c70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="48,-734 48,-718 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c6a690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="48,-682 48,-648 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28651@0" ObjectIDZND0="28649@1" Pin0InfoVect0LinkObjId="SW-188103_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188105_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="48,-682 48,-648 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cba3c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="48,-621 48,-594 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="28649@0" ObjectIDZND0="28650@1" Pin0InfoVect0LinkObjId="SW-188104_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188103_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="48,-621 48,-594 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ca7840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-121,-720 -121,-705 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2c7f4e0@0" ObjectIDZND0="28700@1" Pin0InfoVect0LinkObjId="SW-188459_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c7f4e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-121,-720 -121,-705 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ca7a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-121,-653 -121,-669 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="28698@1" ObjectIDZND0="28700@0" Pin0InfoVect0LinkObjId="SW-188459_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188457_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-121,-653 -121,-669 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ca9e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-97,-608 -121,-608 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="28702@1" ObjectIDZND0="28699@x" ObjectIDZND1="28698@x" Pin0InfoVect0LinkObjId="SW-188458_0" Pin0InfoVect1LinkObjId="SW-188457_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188461_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-97,-608 -121,-608 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cf53c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-121,-597 -121,-608 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="28699@1" ObjectIDZND0="28702@x" ObjectIDZND1="28698@x" Pin0InfoVect0LinkObjId="SW-188461_0" Pin0InfoVect1LinkObjId="SW-188457_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188458_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-121,-597 -121,-608 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cf55e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-121,-608 -121,-626 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="28702@x" ObjectIDND1="28699@x" ObjectIDZND0="28698@0" Pin0InfoVect0LinkObjId="SW-188457_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-188461_0" Pin1InfoVect1LinkObjId="SW-188458_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-121,-608 -121,-626 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ca1350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1205,-845 1219,-845 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="28710@0" ObjectIDZND0="g_2bfb930@0" Pin0InfoVect0LinkObjId="g_2bfb930_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188484_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1205,-845 1219,-845 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c97420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1199,-606 1213,-606 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="28709@0" ObjectIDZND0="g_2ca3a80@0" Pin0InfoVect0LinkObjId="g_2ca3a80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188483_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1199,-606 1213,-606 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c98540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1139,-718 1139,-703 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2cf5800@0" ObjectIDZND0="28707@1" Pin0InfoVect0LinkObjId="SW-188481_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2cf5800_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1139,-718 1139,-703 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c987a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1139,-651 1139,-667 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="28705@1" ObjectIDZND0="28707@0" Pin0InfoVect0LinkObjId="SW-188481_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188479_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1139,-651 1139,-667 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c98e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1163,-606 1139,-606 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="28709@1" ObjectIDZND0="28706@x" ObjectIDZND1="28705@x" Pin0InfoVect0LinkObjId="SW-188480_0" Pin0InfoVect1LinkObjId="SW-188479_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188483_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1163,-606 1139,-606 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c99060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1139,-595 1139,-606 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="28706@1" ObjectIDZND0="28709@x" ObjectIDZND1="28705@x" Pin0InfoVect0LinkObjId="SW-188483_0" Pin0InfoVect1LinkObjId="SW-188479_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188480_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1139,-595 1139,-606 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c992c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1139,-606 1139,-624 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="28709@x" ObjectIDND1="28706@x" ObjectIDZND0="28705@0" Pin0InfoVect0LinkObjId="SW-188479_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-188483_0" Pin1InfoVect1LinkObjId="SW-188480_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1139,-606 1139,-624 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d31490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-239,-681 -239,-629 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_2d30ac0@0" Pin0InfoVect0LinkObjId="g_2d30ac0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-239,-681 -239,-629 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c30d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-238,-598 -238,-582 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2d30ac0@1" ObjectIDZND0="28719@1" Pin0InfoVect0LinkObjId="SW-188526_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d30ac0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-238,-598 -238,-582 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d10dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="360,-621 374,-621 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="48913@0" ObjectIDZND0="g_2d10340@0" Pin0InfoVect0LinkObjId="g_2d10340_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317181_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="360,-621 374,-621 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d11520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="324,-621 289,-621 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="voltageTransformer" ObjectIDND0="48913@1" ObjectIDZND0="28717@x" ObjectIDZND1="g_2e38f70@0" ObjectIDZND2="g_2c1c750@0" Pin0InfoVect0LinkObjId="SW-188524_0" Pin0InfoVect1LinkObjId="g_2e38f70_0" Pin0InfoVect2LinkObjId="g_2c1c750_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317181_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="324,-621 289,-621 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d11e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="288,-593 288,-621 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="voltageTransformer" ObjectIDND0="28717@1" ObjectIDZND0="48913@x" ObjectIDZND1="g_2e38f70@0" ObjectIDZND2="g_2c1c750@0" Pin0InfoVect0LinkObjId="SW-317181_0" Pin0InfoVect1LinkObjId="g_2e38f70_0" Pin0InfoVect2LinkObjId="g_2c1c750_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188524_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="288,-593 288,-621 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c4ff00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="777,-626 791,-626 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="48914@0" ObjectIDZND0="g_2c4f470@0" Pin0InfoVect0LinkObjId="g_2c4f470_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317182_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="777,-626 791,-626 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c50650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="741,-626 706,-626 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="voltageTransformer" ObjectIDND0="48914@1" ObjectIDZND0="28718@x" ObjectIDZND1="g_2e3b3e0@0" ObjectIDZND2="g_2cc0f90@0" Pin0InfoVect0LinkObjId="SW-188525_0" Pin0InfoVect1LinkObjId="g_2e3b3e0_0" Pin0InfoVect2LinkObjId="g_2cc0f90_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317182_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="741,-626 706,-626 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c50840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="705,-598 705,-626 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="voltageTransformer" ObjectIDND0="28718@1" ObjectIDZND0="48914@x" ObjectIDZND1="g_2e3b3e0@0" ObjectIDZND2="g_2cc0f90@0" Pin0InfoVect0LinkObjId="SW-317182_0" Pin0InfoVect1LinkObjId="g_2e3b3e0_0" Pin0InfoVect2LinkObjId="g_2cc0f90_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188525_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="705,-598 705,-626 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c511b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="434,-610 434,-587 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="28712@0" ObjectIDZND0="28713@1" Pin0InfoVect0LinkObjId="SW-188503_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188502_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="434,-610 434,-587 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bdfc50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-227,-398 -227,-415 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28659@1" ObjectIDZND0="28657@0" Pin0InfoVect0LinkObjId="SW-188239_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188241_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-227,-398 -227,-415 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2be26b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-260,-352 -227,-352 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_2e3cd20@0" ObjectIDZND0="28659@x" ObjectIDZND1="g_2bb7f50@0" Pin0InfoVect0LinkObjId="SW-188241_0" Pin0InfoVect1LinkObjId="g_2bb7f50_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e3cd20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-260,-352 -227,-352 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2be31a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-227,-362 -227,-352 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="28659@0" ObjectIDZND0="g_2bb7f50@0" ObjectIDZND1="g_2e3cd20@0" Pin0InfoVect0LinkObjId="g_2bb7f50_0" Pin0InfoVect1LinkObjId="g_2e3cd20_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188241_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-227,-362 -227,-352 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2be3400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-227,-352 -227,-343 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="28659@x" ObjectIDND1="g_2e3cd20@0" ObjectIDZND0="g_2bb7f50@1" Pin0InfoVect0LinkObjId="g_2bb7f50_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-188241_0" Pin1InfoVect1LinkObjId="g_2e3cd20_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-227,-352 -227,-343 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c55450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-197,-452 -226,-452 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="49389@1" ObjectIDZND0="28657@x" ObjectIDZND1="28658@x" Pin0InfoVect0LinkObjId="SW-188239_0" Pin0InfoVect1LinkObjId="SW-188240_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-318922_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-197,-452 -226,-452 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c55f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-227,-442 -227,-452 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="28657@1" ObjectIDZND0="49389@x" ObjectIDZND1="28658@x" Pin0InfoVect0LinkObjId="SW-318922_0" Pin0InfoVect1LinkObjId="SW-188240_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188239_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-227,-442 -227,-452 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c561a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-227,-452 -227,-459 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="49389@x" ObjectIDND1="28657@x" ObjectIDZND0="28658@0" Pin0InfoVect0LinkObjId="SW-188240_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-318922_0" Pin1InfoVect1LinkObjId="SW-188239_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-227,-452 -227,-459 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b54530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="73,-291 73,-279 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2c56400@0" ObjectIDZND0="28668@1" Pin0InfoVect0LinkObjId="SW-188286_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c56400_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="73,-291 73,-279 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bf5580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="139,-453 153,-453 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="49496@0" ObjectIDZND0="g_2b571d0@0" Pin0InfoVect0LinkObjId="g_2b571d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319320_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="139,-453 153,-453 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bf57e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="39,-223 73,-223 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2e409b0@0" ObjectIDZND0="34198@x" ObjectIDZND1="0@x" ObjectIDZND2="28668@x" Pin0InfoVect0LinkObjId="EC-YM_NY.413Ld_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-188286_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e409b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="39,-223 73,-223 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c0b760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="73,-399 73,-416 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28667@1" ObjectIDZND0="28665@0" Pin0InfoVect0LinkObjId="SW-188283_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188285_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="73,-399 73,-416 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c0e1c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="40,-353 73,-353 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_2e3fc00@0" ObjectIDZND0="28667@x" ObjectIDZND1="g_2c56400@0" Pin0InfoVect0LinkObjId="SW-188285_0" Pin0InfoVect1LinkObjId="g_2c56400_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e3fc00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="40,-353 73,-353 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c356b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="73,-363 73,-353 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="28667@0" ObjectIDZND0="g_2c56400@0" ObjectIDZND1="g_2e3fc00@0" Pin0InfoVect0LinkObjId="g_2c56400_0" Pin0InfoVect1LinkObjId="g_2e3fc00_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188285_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="73,-363 73,-353 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c35910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="73,-353 73,-344 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="28667@x" ObjectIDND1="g_2e3fc00@0" ObjectIDZND0="g_2c56400@1" Pin0InfoVect0LinkObjId="g_2c56400_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-188285_0" Pin1InfoVect1LinkObjId="g_2e3fc00_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="73,-353 73,-344 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c35b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="103,-453 73,-453 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="49496@1" ObjectIDZND0="28665@x" ObjectIDZND1="28666@x" Pin0InfoVect0LinkObjId="SW-188283_0" Pin0InfoVect1LinkObjId="SW-188284_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319320_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="103,-453 73,-453 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c35dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="73,-443 73,-453 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="28665@1" ObjectIDZND0="28666@x" ObjectIDZND1="49496@x" Pin0InfoVect0LinkObjId="SW-188284_0" Pin0InfoVect1LinkObjId="SW-319320_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188283_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="73,-443 73,-453 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c36030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="73,-453 73,-471 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="28665@x" ObjectIDND1="49496@x" ObjectIDZND0="28666@0" Pin0InfoVect0LinkObjId="SW-188284_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-188283_0" Pin1InfoVect1LinkObjId="SW-319320_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="73,-453 73,-471 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bac750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="234,-292 234,-280 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2c37370@0" ObjectIDZND0="28672@1" Pin0InfoVect0LinkObjId="SW-188308_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c37370_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="234,-292 234,-280 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bafef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="300,-454 314,-454 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="49500@0" ObjectIDZND0="g_2baf460@0" Pin0InfoVect0LinkObjId="g_2baf460_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319357_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="300,-454 314,-454 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bb0150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="200,-233 234,-233 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_2e42ae0@0" ObjectIDZND0="28672@x" ObjectIDZND1="34199@x" Pin0InfoVect0LinkObjId="SW-188308_0" Pin0InfoVect1LinkObjId="EC-YM_NY.414Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e42ae0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="200,-233 234,-233 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c6ca40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="234,-244 234,-233 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="28672@0" ObjectIDZND0="34199@x" ObjectIDZND1="g_2e42ae0@0" Pin0InfoVect0LinkObjId="EC-YM_NY.414Ld_0" Pin0InfoVect1LinkObjId="g_2e42ae0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188308_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="234,-244 234,-233 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c6cca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="234,-233 234,-179 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="28672@x" ObjectIDND1="g_2e42ae0@0" ObjectIDZND0="34199@0" Pin0InfoVect0LinkObjId="EC-YM_NY.414Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-188308_0" Pin1InfoVect1LinkObjId="g_2e42ae0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="234,-233 234,-179 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2caee50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="234,-400 234,-417 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28671@1" ObjectIDZND0="28669@0" Pin0InfoVect0LinkObjId="SW-188305_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188307_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="234,-400 234,-417 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cb1840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="201,-354 234,-354 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_2e41d30@0" ObjectIDZND0="28671@x" ObjectIDZND1="g_2c37370@0" Pin0InfoVect0LinkObjId="SW-188307_0" Pin0InfoVect1LinkObjId="g_2c37370_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e41d30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="201,-354 234,-354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cb1aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="234,-364 234,-354 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="28671@0" ObjectIDZND0="g_2c37370@0" ObjectIDZND1="g_2e41d30@0" Pin0InfoVect0LinkObjId="g_2c37370_0" Pin0InfoVect1LinkObjId="g_2e41d30_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188307_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="234,-364 234,-354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cb1d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="234,-354 234,-345 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="28671@x" ObjectIDND1="g_2e41d30@0" ObjectIDZND0="g_2c37370@1" Pin0InfoVect0LinkObjId="g_2c37370_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-188307_0" Pin1InfoVect1LinkObjId="g_2e41d30_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="234,-354 234,-345 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cb1f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="264,-454 235,-454 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="49500@1" ObjectIDZND0="28669@x" ObjectIDZND1="28670@x" Pin0InfoVect0LinkObjId="SW-188305_0" Pin0InfoVect1LinkObjId="SW-188306_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319357_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="264,-454 235,-454 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cb21c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="234,-444 234,-454 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="28669@1" ObjectIDZND0="49500@x" ObjectIDZND1="28670@x" Pin0InfoVect0LinkObjId="SW-319357_0" Pin0InfoVect1LinkObjId="SW-188306_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188305_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="234,-444 234,-454 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cb2420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="234,-454 234,-475 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="49500@x" ObjectIDND1="28669@x" ObjectIDZND0="28670@0" Pin0InfoVect0LinkObjId="SW-188306_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-319357_0" Pin1InfoVect1LinkObjId="SW-188305_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="234,-454 234,-475 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b9d090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="382,-293 382,-281 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2b99f00@0" ObjectIDZND0="28676@1" Pin0InfoVect0LinkObjId="SW-188330_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b99f00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="382,-293 382,-281 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c60c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="448,-455 462,-455 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="49691@0" ObjectIDZND0="g_2c60170@0" Pin0InfoVect0LinkObjId="g_2c60170_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319898_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="448,-455 462,-455 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c61350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="348,-234 382,-234 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_2e44c10@0" ObjectIDZND0="28676@x" ObjectIDZND1="34200@x" Pin0InfoVect0LinkObjId="SW-188330_0" Pin0InfoVect1LinkObjId="EC-YM_NY.415Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e44c10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="348,-234 382,-234 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c61ae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="382,-245 382,-234 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="28676@0" ObjectIDZND0="34200@x" ObjectIDZND1="g_2e44c10@0" Pin0InfoVect0LinkObjId="EC-YM_NY.415Ld_0" Pin0InfoVect1LinkObjId="g_2e44c10_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188330_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="382,-245 382,-234 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c61d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="382,-234 382,-180 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="28676@x" ObjectIDND1="g_2e44c10@0" ObjectIDZND0="34200@0" Pin0InfoVect0LinkObjId="EC-YM_NY.415Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-188330_0" Pin1InfoVect1LinkObjId="g_2e44c10_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="382,-234 382,-180 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ba9680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="349,-355 382,-355 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_2e43e60@0" ObjectIDZND0="28675@x" ObjectIDZND1="g_2b99f00@0" Pin0InfoVect0LinkObjId="SW-188329_0" Pin0InfoVect1LinkObjId="g_2b99f00_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e43e60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="349,-355 382,-355 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ba98e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="382,-365 382,-355 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="28675@0" ObjectIDZND0="g_2b99f00@0" ObjectIDZND1="g_2e43e60@0" Pin0InfoVect0LinkObjId="g_2b99f00_0" Pin0InfoVect1LinkObjId="g_2e43e60_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188329_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="382,-365 382,-355 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ba9b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="382,-355 382,-346 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="28675@x" ObjectIDND1="g_2e43e60@0" ObjectIDZND0="g_2b99f00@1" Pin0InfoVect0LinkObjId="g_2b99f00_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-188329_0" Pin1InfoVect1LinkObjId="g_2e43e60_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="382,-355 382,-346 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ba9da0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="412,-455 383,-455 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="49691@1" ObjectIDZND0="28673@x" ObjectIDZND1="28674@x" Pin0InfoVect0LinkObjId="SW-188327_0" Pin0InfoVect1LinkObjId="SW-188328_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319898_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="412,-455 383,-455 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2baa000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="382,-445 382,-455 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="28673@1" ObjectIDZND0="49691@x" ObjectIDZND1="28674@x" Pin0InfoVect0LinkObjId="SW-319898_0" Pin0InfoVect1LinkObjId="SW-188328_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188327_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="382,-445 382,-455 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2baa260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="382,-455 382,-476 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="49691@x" ObjectIDND1="28673@x" ObjectIDZND0="28674@0" Pin0InfoVect0LinkObjId="SW-188328_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-319898_0" Pin1InfoVect1LinkObjId="SW-188327_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="382,-455 382,-476 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bdc890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="755,-292 755,-280 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2bd9690@0" ObjectIDZND0="28684@1" Pin0InfoVect0LinkObjId="SW-188374_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2bd9690_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="755,-292 755,-280 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bb1630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="821,-454 835,-454 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="49328@0" ObjectIDZND0="g_2bdf5a0@0" Pin0InfoVect0LinkObjId="g_2bdf5a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-318672_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="821,-454 835,-454 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bb1890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="721,-233 755,-233 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_2e488a0@0" ObjectIDZND0="28684@x" ObjectIDZND1="34202@x" Pin0InfoVect0LinkObjId="SW-188374_0" Pin0InfoVect1LinkObjId="EC-YM_NY.432Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e488a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="721,-233 755,-233 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bb21d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="755,-244 755,-233 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="28684@0" ObjectIDZND0="34202@x" ObjectIDZND1="g_2e488a0@0" Pin0InfoVect0LinkObjId="EC-YM_NY.432Ld_0" Pin0InfoVect1LinkObjId="g_2e488a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188374_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="755,-244 755,-233 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bb2430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="755,-233 755,-179 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="28684@x" ObjectIDND1="g_2e488a0@0" ObjectIDZND0="34202@0" Pin0InfoVect0LinkObjId="EC-YM_NY.432Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-188374_0" Pin1InfoVect1LinkObjId="g_2e488a0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="755,-233 755,-179 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bb6f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="755,-403 755,-417 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28683@1" ObjectIDZND0="28681@0" Pin0InfoVect0LinkObjId="SW-188371_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188373_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="755,-403 755,-417 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b6c8a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="722,-354 755,-354 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_2e47af0@0" ObjectIDZND0="28683@x" ObjectIDZND1="g_2bd9690@0" Pin0InfoVect0LinkObjId="SW-188373_0" Pin0InfoVect1LinkObjId="g_2bd9690_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e47af0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="722,-354 755,-354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b6cb00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="755,-364 755,-354 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="28683@0" ObjectIDZND0="g_2bd9690@0" ObjectIDZND1="g_2e47af0@0" Pin0InfoVect0LinkObjId="g_2bd9690_0" Pin0InfoVect1LinkObjId="g_2e47af0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188373_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="755,-364 755,-354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b6cd60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="755,-354 755,-345 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="28683@x" ObjectIDND1="g_2e47af0@0" ObjectIDZND0="g_2bd9690@1" Pin0InfoVect0LinkObjId="g_2bd9690_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-188373_0" Pin1InfoVect1LinkObjId="g_2e47af0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="755,-354 755,-345 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b6cfc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="785,-454 756,-454 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="49328@1" ObjectIDZND0="28681@x" ObjectIDZND1="28682@x" Pin0InfoVect0LinkObjId="SW-188371_0" Pin0InfoVect1LinkObjId="SW-188372_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-318672_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="785,-454 756,-454 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b6d220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="755,-444 755,-454 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="28681@1" ObjectIDZND0="49328@x" ObjectIDZND1="28682@x" Pin0InfoVect0LinkObjId="SW-318672_0" Pin0InfoVect1LinkObjId="SW-188372_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188371_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="755,-444 755,-454 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b6d480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="755,-454 755,-470 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="49328@x" ObjectIDND1="28681@x" ObjectIDZND0="28682@0" Pin0InfoVect0LinkObjId="SW-188372_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-318672_0" Pin1InfoVect1LinkObjId="SW-188371_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="755,-454 755,-470 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c785a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="912,-292 912,-280 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2b6f090@0" ObjectIDZND0="28688@1" Pin0InfoVect0LinkObjId="SW-188393_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b6f090_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="912,-292 912,-280 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2c7bce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="978,-454 992,-454 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_2c7b250@0" Pin0InfoVect0LinkObjId="g_2c7b250_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="978,-454 992,-454 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c7c430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="878,-233 912,-233 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_2e4a9d0@0" ObjectIDZND0="28688@x" ObjectIDZND1="34203@x" Pin0InfoVect0LinkObjId="SW-188393_0" Pin0InfoVect1LinkObjId="EC-YM_NY.433Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e4a9d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="878,-233 912,-233 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c7cbc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="912,-244 912,-233 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="28688@0" ObjectIDZND0="34203@x" ObjectIDZND1="g_2e4a9d0@0" Pin0InfoVect0LinkObjId="EC-YM_NY.433Ld_0" Pin0InfoVect1LinkObjId="g_2e4a9d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188393_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="912,-244 912,-233 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c7cdf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="912,-233 912,-179 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="28688@x" ObjectIDND1="g_2e4a9d0@0" ObjectIDZND0="34203@0" Pin0InfoVect0LinkObjId="EC-YM_NY.433Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-188393_0" Pin1InfoVect1LinkObjId="g_2e4a9d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="912,-233 912,-179 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cd2010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="912,-400 912,-417 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28687@1" ObjectIDZND0="28685@0" Pin0InfoVect0LinkObjId="SW-188390_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188392_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="912,-400 912,-417 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cd4a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="879,-354 912,-354 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_2e49c20@0" ObjectIDZND0="28687@x" ObjectIDZND1="g_2b6f090@0" Pin0InfoVect0LinkObjId="SW-188392_0" Pin0InfoVect1LinkObjId="g_2b6f090_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e49c20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="879,-354 912,-354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cd4cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="912,-364 912,-354 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="28687@0" ObjectIDZND0="g_2b6f090@0" ObjectIDZND1="g_2e49c20@0" Pin0InfoVect0LinkObjId="g_2b6f090_0" Pin0InfoVect1LinkObjId="g_2e49c20_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188392_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="912,-364 912,-354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cd4f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="912,-354 912,-345 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="28687@x" ObjectIDND1="g_2e49c20@0" ObjectIDZND0="g_2b6f090@1" Pin0InfoVect0LinkObjId="g_2b6f090_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-188392_0" Pin1InfoVect1LinkObjId="g_2e49c20_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="912,-354 912,-345 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bd2450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="942,-454 913,-454 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="28685@x" ObjectIDZND1="28686@x" Pin0InfoVect0LinkObjId="SW-188390_0" Pin0InfoVect1LinkObjId="SW-188391_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="942,-454 913,-454 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bd26b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="912,-443 912,-454 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="28685@1" ObjectIDZND0="0@x" ObjectIDZND1="28686@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-188391_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188390_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="912,-443 912,-454 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bd2910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="912,-454 912,-469 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="28685@x" ObjectIDZND0="28686@0" Pin0InfoVect0LinkObjId="SW-188391_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-188390_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="912,-454 912,-469 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bd7940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1175,-456 1189,-456 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="49384@0" ObjectIDZND0="g_2bd6eb0@0" Pin0InfoVect0LinkObjId="g_2bd6eb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-318845_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1175,-456 1189,-456 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c17400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1092,-340 1115,-340 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_2e4b780@0" ObjectIDZND0="28691@x" ObjectIDZND1="g_2c169e0@0" Pin0InfoVect0LinkObjId="SW-188414_0" Pin0InfoVect1LinkObjId="g_2c169e0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e4b780_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1092,-340 1115,-340 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c17ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1114,-354 1114,-362 1114,-340 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="28691@0" ObjectIDZND0="g_2c169e0@0" ObjectIDZND1="g_2e4b780@0" Pin0InfoVect0LinkObjId="g_2c169e0_0" Pin0InfoVect1LinkObjId="g_2e4b780_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188414_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1114,-354 1114,-362 1114,-340 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c18150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1114,-340 1114,-333 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="28691@x" ObjectIDND1="g_2e4b780@0" ObjectIDZND0="g_2c169e0@1" Pin0InfoVect0LinkObjId="g_2c169e0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-188414_0" Pin1InfoVect1LinkObjId="g_2e4b780_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1114,-340 1114,-333 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2c183b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1114,-222 1114,-199 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="breaker" EndDevType1="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1114,-222 1114,-199 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b0abe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1016,-214 1016,-199 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="breaker" EndDevType1="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1016,-214 1016,-199 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b0ae40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1198,-215 1198,-199 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="breaker" EndDevType1="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1198,-215 1198,-199 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b0e130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1114,-199 1198,-199 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="breaker" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1114,-199 1198,-199 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b0e390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1198,-199 1245,-199 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="breaker" BeginDevType2="breaker" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1198,-199 1245,-199 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b0ee80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1114,-199 1016,-199 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="breaker" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1114,-199 1016,-199 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b0f0e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1016,-199 982,-199 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="breaker" BeginDevType2="breaker" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1016,-199 982,-199 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b11b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1017,-339 1017,-327 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="28692@0" ObjectIDZND0="g_2b097a0@1" Pin0InfoVect0LinkObjId="g_2b097a0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188415_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1017,-339 1017,-327 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b11da0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1197,-336 1197,-321 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="28693@0" ObjectIDZND0="g_2b0a1c0@1" Pin0InfoVect0LinkObjId="g_2b0a1c0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188416_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1197,-336 1197,-321 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b135c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1017,-375 1017,-385 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="28692@1" ObjectIDZND0="0@x" ObjectIDZND1="g_2bd7ba0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_2bd7ba0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188415_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1017,-375 1017,-385 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b13820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1017,-385 1017,-397 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="28692@x" ObjectIDND1="g_2bd7ba0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-188415_0" Pin1InfoVect1LinkObjId="g_2bd7ba0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1017,-385 1017,-397 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b14900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1166,-384 1197,-384 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_2b12000@0" ObjectIDZND0="0@x" ObjectIDZND1="28693@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-188416_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b12000_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1166,-384 1197,-384 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2bc6df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1073,-262 1087,-262 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_2bc6360@0" Pin0InfoVect0LinkObjId="g_2bc6360_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1073,-262 1087,-262 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bc8010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1197,-400 1197,-384 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="g_2b12000@0" ObjectIDZND1="28693@x" Pin0InfoVect0LinkObjId="g_2b12000_0" Pin0InfoVect1LinkObjId="SW-188416_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1197,-400 1197,-384 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bc8200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1197,-384 1197,-372 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_2b12000@0" ObjectIDND1="0@x" ObjectIDZND0="28693@1" Pin0InfoVect0LinkObjId="SW-188416_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2b12000_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1197,-384 1197,-372 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bc83f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1037,-262 1017,-262 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="breaker" EndDevType2="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_2b097a0@0" ObjectIDZND1="0@x" ObjectIDZND2="g_2e51460@0" Pin0InfoVect0LinkObjId="g_2b097a0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_2e51460_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1037,-262 1017,-262 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bc8600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1008,-262 1017,-262 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="breaker" ObjectIDND0="g_2e51460@0" ObjectIDZND0="0@x" ObjectIDZND1="g_2b097a0@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_2b097a0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e51460_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1008,-262 1017,-262 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bc92e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1017,-276 1017,-262 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" EndDevType2="lightningRod" ObjectIDND0="g_2b097a0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="g_2e51460@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_2e51460_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b097a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1017,-276 1017,-262 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2bcca20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1248,-260 1262,-260 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_2bcbf90@0" Pin0InfoVect0LinkObjId="g_2bcbf90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1248,-260 1262,-260 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bce7a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1197,-249 1197,-239 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="g_2b0a1c0@0" ObjectIDND2="g_2bcd450@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_2b0a1c0_0" Pin1InfoVect2LinkObjId="g_2bcd450_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1197,-249 1197,-239 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bcea00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1212,-260 1197,-260 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="breaker" EndDevType2="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_2b0a1c0@0" ObjectIDZND1="0@x" ObjectIDZND2="g_2bcd450@0" Pin0InfoVect0LinkObjId="g_2b0a1c0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_2bcd450_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1212,-260 1197,-260 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bcf4f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1197,-272 1197,-260 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" EndDevType2="lightningRod" ObjectIDND0="g_2b0a1c0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="g_2bcd450@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_2bcd450_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b0a1c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1197,-272 1197,-260 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bcf750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1197,-260 1197,-249 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="breaker" EndDevType1="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_2b0a1c0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_2bcd450@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_2bcd450_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_2b0a1c0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1197,-260 1197,-249 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c21290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1313,-290 1313,-278 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2bcf9b0@0" ObjectIDZND0="28697@1" Pin0InfoVect0LinkObjId="SW-188438_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2bcf9b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1313,-290 1313,-278 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2c256a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1379,-452 1393,-452 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_2c24c10@0" Pin0InfoVect0LinkObjId="g_2c24c10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1379,-452 1393,-452 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c26f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1313,-242 1313,-231 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="28697@0" ObjectIDZND0="34204@x" ObjectIDZND1="g_2c25df0@0" Pin0InfoVect0LinkObjId="EC-YM_NY.435Ld_0" Pin0InfoVect1LinkObjId="g_2c25df0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188438_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1313,-242 1313,-231 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c271f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1313,-231 1313,-177 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="28697@x" ObjectIDND1="g_2c25df0@0" ObjectIDZND0="34204@0" Pin0InfoVect0LinkObjId="EC-YM_NY.435Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-188438_0" Pin1InfoVect1LinkObjId="g_2c25df0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1313,-231 1313,-177 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c2bd20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1313,-401 1313,-415 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28696@1" ObjectIDZND0="28694@0" Pin0InfoVect0LinkObjId="SW-188435_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188437_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1313,-401 1313,-415 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c2e780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1313,-362 1313,-352 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="28696@0" ObjectIDZND0="g_2bcf9b0@0" ObjectIDZND1="g_2c214f0@0" Pin0InfoVect0LinkObjId="g_2bcf9b0_0" Pin0InfoVect1LinkObjId="g_2c214f0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188437_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1313,-362 1313,-352 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c2e9e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1313,-352 1313,-343 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="28696@x" ObjectIDND1="g_2c214f0@0" ObjectIDZND0="g_2bcf9b0@1" Pin0InfoVect0LinkObjId="g_2bcf9b0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-188437_0" Pin1InfoVect1LinkObjId="g_2c214f0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1313,-352 1313,-343 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c2ec40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1343,-452 1314,-452 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="28694@x" ObjectIDZND1="28695@x" Pin0InfoVect0LinkObjId="SW-188435_0" Pin0InfoVect1LinkObjId="SW-188436_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1343,-452 1314,-452 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c2eea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1313,-442 1313,-452 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="28694@1" ObjectIDZND0="0@x" ObjectIDZND1="28695@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-188436_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188435_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1313,-442 1313,-452 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b57dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1313,-452 1313,-471 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="28694@x" ObjectIDZND0="28695@0" Pin0InfoVect0LinkObjId="SW-188436_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-188435_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1313,-452 1313,-471 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b59930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="113,-995 113,-976 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28640@0" ObjectIDZND0="28722@0" Pin0InfoVect0LinkObjId="g_2b59b70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188044_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="113,-995 113,-976 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b59b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="433,-996 433,-976 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28715@0" ObjectIDZND0="28722@0" Pin0InfoVect0LinkObjId="g_2b59930_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188522_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="433,-996 433,-976 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b59dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="790,-994 790,-976 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28644@0" ObjectIDZND0="28722@0" Pin0InfoVect0LinkObjId="g_2b59930_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188058_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="790,-994 790,-976 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b5a030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="48,-936 48,-976 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28648@1" ObjectIDZND0="28722@0" Pin0InfoVect0LinkObjId="g_2b59930_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188102_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="48,-936 48,-976 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b5a290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-238,-546 -238,-530 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28719@0" ObjectIDZND0="28723@0" Pin0InfoVect0LinkObjId="g_2b5a4f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188526_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-238,-546 -238,-530 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b5a4f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-121,-561 -121,-530 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28699@0" ObjectIDZND0="28723@0" Pin0InfoVect0LinkObjId="g_2b5a290_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188458_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-121,-561 -121,-530 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b5a750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="48,-558 48,-530 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28650@0" ObjectIDZND0="28723@0" Pin0InfoVect0LinkObjId="g_2b5a290_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188104_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="48,-558 48,-530 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b5a9b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="705,-562 705,-528 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28718@0" ObjectIDZND0="28724@0" Pin0InfoVect0LinkObjId="g_2b5b590_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188525_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="705,-562 705,-528 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b5ac10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-227,-495 -227,-530 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28658@1" ObjectIDZND0="28723@0" Pin0InfoVect0LinkObjId="g_2b5a290_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188240_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-227,-495 -227,-530 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b5ae70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="73,-507 73,-530 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28666@1" ObjectIDZND0="28723@0" Pin0InfoVect0LinkObjId="g_2b5a290_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188284_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="73,-507 73,-530 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b5b0d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="234,-511 234,-530 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28670@1" ObjectIDZND0="28723@0" Pin0InfoVect0LinkObjId="g_2b5a290_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188306_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="234,-511 234,-530 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b5b330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="382,-512 382,-530 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28674@1" ObjectIDZND0="28723@0" Pin0InfoVect0LinkObjId="g_2b5a290_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188328_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="382,-512 382,-530 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b5b590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="755,-506 755,-528 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28682@1" ObjectIDZND0="28724@0" Pin0InfoVect0LinkObjId="g_2b5a9b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188372_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="755,-506 755,-528 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b5b7f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="912,-505 912,-528 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28686@1" ObjectIDZND0="28724@0" Pin0InfoVect0LinkObjId="g_2b5a9b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188391_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="912,-505 912,-528 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b5e7d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1139,-559 1139,-528 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28706@0" ObjectIDZND0="28724@0" Pin0InfoVect0LinkObjId="g_2b5a9b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188480_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1139,-559 1139,-528 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b5ea10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1313,-507 1313,-528 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28695@1" ObjectIDZND0="28724@0" Pin0InfoVect0LinkObjId="g_2b5a9b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188436_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1313,-507 1313,-528 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b5f220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="578,-589 578,-528 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28714@0" ObjectIDZND0="28724@0" Pin0InfoVect0LinkObjId="g_2b5a9b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188504_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="578,-589 578,-528 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b66320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="382,-401 382,-419 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28675@1" ObjectIDZND0="28673@0" Pin0InfoVect0LinkObjId="SW-188327_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188329_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="382,-401 382,-419 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2af1d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="434,-637 434,-675 578,-675 578,-623 577,-622 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="28712@1" ObjectIDZND0="28714@1" Pin0InfoVect0LinkObjId="SW-188504_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188502_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="434,-637 434,-675 578,-675 578,-623 577,-622 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2af51a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-58,-787 -44,-787 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="28704@0" ObjectIDZND0="g_2af4710@0" Pin0InfoVect0LinkObjId="g_2af4710_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188463_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-58,-787 -44,-787 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2af5400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-93,-850 -121,-850 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="capacitor" ObjectIDND0="28703@1" ObjectIDZND0="28701@x" ObjectIDZND1="48924@x" Pin0InfoVect0LinkObjId="SW-188460_0" Pin0InfoVect1LinkObjId="CB-YM_NY.YM_NY_Cb1_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188462_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-93,-850 -121,-850 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2af5660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-94,-787 -120,-787 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="28704@1" ObjectIDZND0="g_2c7f4e0@0" ObjectIDZND1="28701@x" Pin0InfoVect0LinkObjId="g_2c7f4e0_0" Pin0InfoVect1LinkObjId="SW-188460_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188463_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-94,-787 -120,-787 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2af6150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-121,-839 -121,-850 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="capacitor" ObjectIDND0="28701@1" ObjectIDZND0="28703@x" ObjectIDZND1="48924@x" Pin0InfoVect0LinkObjId="SW-188462_0" Pin0InfoVect1LinkObjId="CB-YM_NY.YM_NY_Cb1_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188460_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-121,-839 -121,-850 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2af63b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-121,-850 -121,-871 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="capacitor" ObjectIDND0="28703@x" ObjectIDND1="28701@x" ObjectIDZND0="48924@1" Pin0InfoVect0LinkObjId="CB-YM_NY.YM_NY_Cb1_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-188462_0" Pin1InfoVect1LinkObjId="SW-188460_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-121,-850 -121,-871 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2af6ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-121,-773 -121,-787 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2c7f4e0@1" ObjectIDZND0="28704@x" ObjectIDZND1="28701@x" Pin0InfoVect0LinkObjId="SW-188463_0" Pin0InfoVect1LinkObjId="SW-188460_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c7f4e0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-121,-773 -121,-787 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2af7100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-121,-787 -121,-804 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="28704@x" ObjectIDND1="g_2c7f4e0@0" ObjectIDZND0="28701@0" Pin0InfoVect0LinkObjId="SW-188460_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-188463_0" Pin1InfoVect1LinkObjId="g_2c7f4e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-121,-787 -121,-804 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2afa8a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1207,-781 1221,-781 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="28711@0" ObjectIDZND0="g_2af9e10@0" Pin0InfoVect0LinkObjId="g_2af9e10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188485_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1207,-781 1221,-781 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2afab00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1169,-845 1140,-845 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="capacitor" ObjectIDND0="28710@1" ObjectIDZND0="28708@x" ObjectIDZND1="48925@x" Pin0InfoVect0LinkObjId="SW-188482_0" Pin0InfoVect1LinkObjId="CB-YM_NY.YM_NY_Cb2_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188484_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1169,-845 1140,-845 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2afad60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1171,-781 1139,-781 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="28711@1" ObjectIDZND0="g_2cf5800@0" ObjectIDZND1="28708@x" Pin0InfoVect0LinkObjId="g_2cf5800_0" Pin0InfoVect1LinkObjId="SW-188482_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188485_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1171,-781 1139,-781 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2afc430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1139,-860 1139,-845 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="48925@1" ObjectIDZND0="28710@x" ObjectIDZND1="28708@x" Pin0InfoVect0LinkObjId="SW-188484_0" Pin0InfoVect1LinkObjId="SW-188482_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="CB-YM_NY.YM_NY_Cb2_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1139,-860 1139,-845 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2afc620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1139,-845 1139,-835 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" EndDevType0="switch" ObjectIDND0="28710@x" ObjectIDND1="48925@x" ObjectIDZND0="28708@1" Pin0InfoVect0LinkObjId="SW-188482_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-188484_0" Pin1InfoVect1LinkObjId="CB-YM_NY.YM_NY_Cb2_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1139,-845 1139,-835 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2afd020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1139,-771 1139,-781 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2cf5800@1" ObjectIDZND0="28711@x" ObjectIDZND1="28708@x" Pin0InfoVect0LinkObjId="SW-188485_0" Pin0InfoVect1LinkObjId="SW-188482_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2cf5800_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1139,-771 1139,-781 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2afd280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1139,-781 1139,-800 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="28711@x" ObjectIDND1="g_2cf5800@0" ObjectIDZND0="28708@0" Pin0InfoVect0LinkObjId="SW-188482_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-188485_0" Pin1InfoVect1LinkObjId="g_2cf5800_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1139,-781 1139,-800 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d6d120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="288,-557 288,-530 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28717@0" ObjectIDZND0="28723@0" Pin0InfoVect0LinkObjId="g_2b5a290_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188524_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="288,-557 288,-530 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b17e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="591,-268 591,-256 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2d771a0@0" ObjectIDZND0="34413@1" Pin0InfoVect0LinkObjId="SW-220037_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d771a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="591,-268 591,-256 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b1b5e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="657,-430 671,-430 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="34414@0" ObjectIDZND0="g_2b1ab50@0" Pin0InfoVect0LinkObjId="g_2b1ab50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="657,-430 671,-430 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b20110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="591,-376 591,-393 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28678@1" ObjectIDZND0="28677@0" Pin0InfoVect0LinkObjId="SW-188349_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188350_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="591,-376 591,-393 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b22b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="558,-330 591,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_2e459c0@0" ObjectIDZND0="28678@x" ObjectIDZND1="g_2d771a0@0" Pin0InfoVect0LinkObjId="SW-188350_0" Pin0InfoVect1LinkObjId="g_2d771a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e459c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="558,-330 591,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b22dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="591,-340 591,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="28678@0" ObjectIDZND0="g_2d771a0@0" ObjectIDZND1="g_2e459c0@0" Pin0InfoVect0LinkObjId="g_2d771a0_0" Pin0InfoVect1LinkObjId="g_2e459c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188350_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="591,-340 591,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b23030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="591,-330 591,-321 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="28678@x" ObjectIDND1="g_2e459c0@0" ObjectIDZND0="g_2d771a0@1" Pin0InfoVect0LinkObjId="g_2d771a0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-188350_0" Pin1InfoVect1LinkObjId="g_2e459c0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="591,-330 591,-321 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b23290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="621,-430 592,-430 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="34414@1" ObjectIDZND0="28677@x" ObjectIDZND1="28679@x" Pin0InfoVect0LinkObjId="SW-188349_0" Pin0InfoVect1LinkObjId="SW-188351_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="621,-430 592,-430 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b234f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="591,-420 591,-430 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="28677@1" ObjectIDZND0="34414@x" ObjectIDZND1="28679@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-188351_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188349_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="591,-420 591,-430 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b23750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="591,-430 591,-463 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="34414@x" ObjectIDND1="28677@x" ObjectIDZND0="28679@0" Pin0InfoVect0LinkObjId="SW-188351_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-188349_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="591,-430 591,-463 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b239b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="591,-497 591,-528 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28679@1" ObjectIDZND0="28724@0" Pin0InfoVect0LinkObjId="g_2b5a9b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188351_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="591,-497 591,-528 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b290b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-14,-434 0,-434 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="34415@0" ObjectIDZND0="g_2b28620@0" Pin0InfoVect0LinkObjId="g_2b28620_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-14,-434 0,-434 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b2dc30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-80,-380 -80,-397 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="34406@1" ObjectIDZND0="34404@0" Pin0InfoVect0LinkObjId="SW-188261_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188263_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-80,-380 -80,-397 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b305b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-50,-434 -79,-434 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="34415@1" ObjectIDZND0="34404@x" ObjectIDZND1="34405@x" Pin0InfoVect0LinkObjId="SW-188261_0" Pin0InfoVect1LinkObjId="SW-188262_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-50,-434 -79,-434 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b30810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-80,-424 -80,-434 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="34404@1" ObjectIDZND0="34415@x" ObjectIDZND1="34405@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-188262_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188261_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-80,-424 -80,-434 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b30a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-80,-434 -80,-465 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="34415@x" ObjectIDND1="34404@x" ObjectIDZND0="34405@0" Pin0InfoVect0LinkObjId="SW-188262_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-188261_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-80,-434 -80,-465 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b30cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-80,-501 -80,-530 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="34405@1" ObjectIDZND0="28723@0" Pin0InfoVect0LinkObjId="g_2b5a290_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188262_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-80,-501 -80,-530 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b39600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-255,2 -255,-10 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2b37ba0@0" ObjectIDZND0="34407@x" Pin0InfoVect0LinkObjId="SW-188264_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b37ba0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-255,2 -255,-10 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b3a0f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-341,-10 -255,-10 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" EndDevType1="switch" ObjectIDZND0="g_2b37ba0@0" ObjectIDZND1="34407@x" Pin0InfoVect0LinkObjId="g_2b37ba0_0" Pin0InfoVect1LinkObjId="SW-188264_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-341,-10 -255,-10 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b3a350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-255,-10 -221,-10 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2b37ba0@0" ObjectIDZND0="34407@0" Pin0InfoVect0LinkObjId="SW-188264_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b37ba0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-255,-10 -221,-10 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b3a5b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-256,92 -256,74 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2b388d0@0" ObjectIDZND0="28680@x" Pin0InfoVect0LinkObjId="SW-188352_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b388d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-256,92 -256,74 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b3b0a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-337,74 -256,74 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" EndDevType1="switch" ObjectIDZND0="g_2b388d0@0" ObjectIDZND1="28680@x" Pin0InfoVect0LinkObjId="g_2b388d0_0" Pin0InfoVect1LinkObjId="SW-188352_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-337,74 -256,74 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b3d830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-256,74 -220,74 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2b388d0@0" ObjectIDZND0="28680@0" Pin0InfoVect0LinkObjId="SW-188352_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b388d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-256,74 -220,74 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b3da90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-184,74 591,74 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="28680@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188352_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="-184,74 591,74 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b41070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="591,-149 591,74 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="28680@x" Pin0InfoVect0LinkObjId="SW-188352_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="591,-149 591,74 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b412d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="591,74 591,101 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="28680@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188352_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="591,74 591,101 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b442d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="113,-1226 146,-1226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="28642@x" ObjectIDND1="28641@x" ObjectIDND2="0@x" ObjectIDZND0="g_2b43810@0" Pin0InfoVect0LinkObjId="g_2b43810_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-188046_0" Pin1InfoVect1LinkObjId="SW-188045_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="113,-1226 146,-1226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b44fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="113,-1226 113,-1246 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="powerLine" ObjectIDND0="28642@x" ObjectIDND1="28641@x" ObjectIDND2="g_2b43810@0" ObjectIDZND0="37851@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-188046_0" Pin1InfoVect1LinkObjId="SW-188045_0" Pin1InfoVect2LinkObjId="g_2b43810_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="113,-1226 113,-1246 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b45240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="113,-1177 113,-1226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="powerLine" ObjectIDND0="28642@x" ObjectIDND1="28641@x" ObjectIDZND0="g_2b43810@0" ObjectIDZND1="0@x" ObjectIDZND2="37851@1" Pin0InfoVect0LinkObjId="g_2b43810_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_2b44fe0_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-188046_0" Pin1InfoVect1LinkObjId="SW-188045_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="113,-1177 113,-1226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b496d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="34,-1150 34,-1162 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" ObjectIDND0="g_2b454a0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b454a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="34,-1150 34,-1162 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b49930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="433,-1032 433,-1063 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" EndDevType2="switch" ObjectIDND0="28715@1" ObjectIDZND0="g_2cec320@0" ObjectIDZND1="g_2c43f20@0" ObjectIDZND2="28716@x" Pin0InfoVect0LinkObjId="g_2cec320_0" Pin0InfoVect1LinkObjId="g_2c43f20_0" Pin0InfoVect2LinkObjId="SW-188523_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188522_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="433,-1032 433,-1063 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b4a420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="790,-1157 790,-1178 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="28645@1" ObjectIDZND0="28646@x" ObjectIDZND1="g_2e31ef0@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-188060_0" Pin0InfoVect1LinkObjId="g_2e31ef0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188059_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="790,-1157 790,-1178 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b4a680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="790,-1178 816,-1178 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="28645@x" ObjectIDND1="g_2e31ef0@0" ObjectIDND2="0@x" ObjectIDZND0="28646@1" Pin0InfoVect0LinkObjId="SW-188060_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-188059_0" Pin1InfoVect1LinkObjId="g_2e31ef0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="790,-1178 816,-1178 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2e31a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="711,-1151 711,-1162 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" ObjectIDND0="g_2b4a8e0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b4a8e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="711,-1151 711,-1162 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e31c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="711,-1207 711,-1226 790,-1226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="28645@x" ObjectIDZND1="28646@x" ObjectIDZND2="g_2e31ef0@0" Pin0InfoVect0LinkObjId="SW-188059_0" Pin0InfoVect1LinkObjId="SW-188060_0" Pin0InfoVect2LinkObjId="g_2e31ef0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="711,-1207 711,-1226 790,-1226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e32c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="790,-1226 824,-1226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="28645@x" ObjectIDND1="28646@x" ObjectIDND2="0@x" ObjectIDZND0="g_2e31ef0@0" Pin0InfoVect0LinkObjId="g_2e31ef0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-188059_0" Pin1InfoVect1LinkObjId="SW-188060_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="790,-1226 824,-1226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e33970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="790,-1178 790,-1226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="powerLine" ObjectIDND0="28645@x" ObjectIDND1="28646@x" ObjectIDZND0="g_2e31ef0@0" ObjectIDZND1="0@x" ObjectIDZND2="42321@1" Pin0InfoVect0LinkObjId="g_2e31ef0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_2e33bd0_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-188059_0" Pin1InfoVect1LinkObjId="SW-188060_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="790,-1178 790,-1226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e33bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="790,-1226 790,-1258 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="powerLine" ObjectIDND0="28645@x" ObjectIDND1="28646@x" ObjectIDND2="g_2e31ef0@0" ObjectIDZND0="42321@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-188059_0" Pin1InfoVect1LinkObjId="SW-188060_0" Pin1InfoVect2LinkObjId="g_2e31ef0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="790,-1226 790,-1258 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e39b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="274,-642 288,-642 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2e38f70@0" ObjectIDZND0="g_2c1c750@0" ObjectIDZND1="48913@x" ObjectIDZND2="28717@x" Pin0InfoVect0LinkObjId="g_2c1c750_0" Pin0InfoVect1LinkObjId="SW-317181_0" Pin0InfoVect2LinkObjId="SW-188524_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e38f70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="274,-642 288,-642 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e3a640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="288,-676 288,-642 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2c1c750@0" ObjectIDZND0="g_2e38f70@0" ObjectIDZND1="48913@x" ObjectIDZND2="28717@x" Pin0InfoVect0LinkObjId="g_2e38f70_0" Pin0InfoVect1LinkObjId="SW-317181_0" Pin0InfoVect2LinkObjId="SW-188524_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c1c750_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="288,-676 288,-642 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e3a8a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="288,-642 288,-621 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2e38f70@0" ObjectIDND1="g_2c1c750@0" ObjectIDZND0="48913@x" ObjectIDZND1="28717@x" Pin0InfoVect0LinkObjId="SW-317181_0" Pin0InfoVect1LinkObjId="SW-188524_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2e38f70_0" Pin1InfoVect1LinkObjId="g_2c1c750_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="288,-642 288,-621 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e3bda0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="692,-645 706,-645 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="g_2e3b3e0@0" ObjectIDZND0="48914@x" ObjectIDZND1="28718@x" ObjectIDZND2="g_2cc0f90@0" Pin0InfoVect0LinkObjId="SW-317182_0" Pin0InfoVect1LinkObjId="SW-188525_0" Pin0InfoVect2LinkObjId="g_2cc0f90_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e3b3e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="692,-645 706,-645 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e3c860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="705,-626 705,-645 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" ObjectIDND0="48914@x" ObjectIDND1="28718@x" ObjectIDZND0="g_2e3b3e0@0" ObjectIDZND1="g_2cc0f90@0" Pin0InfoVect0LinkObjId="g_2e3b3e0_0" Pin0InfoVect1LinkObjId="g_2cc0f90_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-317182_0" Pin1InfoVect1LinkObjId="SW-188525_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="705,-626 705,-645 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e3cac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="705,-645 705,-681 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="voltageTransformer" ObjectIDND0="g_2e3b3e0@0" ObjectIDND1="48914@x" ObjectIDND2="28718@x" ObjectIDZND0="g_2cc0f90@0" Pin0InfoVect0LinkObjId="g_2cc0f90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2e3b3e0_0" Pin1InfoVect1LinkObjId="SW-317182_0" Pin1InfoVect2LinkObjId="SW-188525_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="705,-645 705,-681 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e4c530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1126,-268 1114,-268 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="breaker" EndDevType2="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1126,-268 1114,-268 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2e4f1e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1162,-268 1176,-268 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_2e4f440@0" Pin0InfoVect0LinkObjId="g_2e4f440_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1162,-268 1176,-268 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e50e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1114,-272 1114,-268 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="breaker" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="g_2c169e0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="g_2c169e0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_2c169e0_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_2c169e0_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1114,-272 1114,-268 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e51060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1114,-268 1114,-249 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" BeginDevType2="switch" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1114,-268 1114,-249 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e51250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1017,-262 1017,-240 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="g_2b097a0@0" ObjectIDND2="g_2e51460@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_2b097a0_0" Pin1InfoVect2LinkObjId="g_2e51460_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1017,-262 1017,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e52110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="984,-374 984,-385 1017,-385 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_2bd7ba0@0" ObjectIDZND0="28692@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-188415_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2bd7ba0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="984,-374 984,-385 1017,-385 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e52370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1171,-247 1171,-254 1197,-254 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2bcd450@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="g_2b0a1c0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_2b0a1c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2bcd450_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1171,-247 1171,-254 1197,-254 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e52b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1282,-344 1282,-352 1313,-352 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_2c214f0@0" ObjectIDZND0="28696@x" ObjectIDZND1="g_2bcf9b0@0" Pin0InfoVect0LinkObjId="SW-188437_0" Pin0InfoVect1LinkObjId="g_2bcf9b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c214f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1282,-344 1282,-352 1313,-352 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e52de0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1281,-223 1281,-231 1313,-231 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_2c25df0@0" ObjectIDZND0="28697@x" ObjectIDZND1="34204@x" Pin0InfoVect0LinkObjId="SW-188438_0" Pin0InfoVect1LinkObjId="EC-YM_NY.435Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c25df0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1281,-223 1281,-231 1313,-231 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e53040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1114,-528 1114,-509 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="28724@0" ObjectIDZND0="28690@1" Pin0InfoVect0LinkObjId="SW-188413_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b5a9b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1114,-528 1114,-509 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e532a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1114,-408 1114,-390 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="28689@0" ObjectIDZND0="28691@1" Pin0InfoVect0LinkObjId="SW-188414_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188412_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1114,-408 1114,-390 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e53a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1139,-456 1114,-456 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="49384@1" ObjectIDZND0="28690@x" ObjectIDZND1="28689@x" Pin0InfoVect0LinkObjId="SW-188413_0" Pin0InfoVect1LinkObjId="SW-188412_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-318845_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1139,-456 1114,-456 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e54570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1114,-473 1114,-456 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="28690@0" ObjectIDZND0="49384@x" ObjectIDZND1="28689@x" Pin0InfoVect0LinkObjId="SW-318845_0" Pin0InfoVect1LinkObjId="SW-188412_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188413_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1114,-473 1114,-456 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e547d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1114,-456 1114,-435 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="49384@x" ObjectIDND1="28690@x" ObjectIDZND0="28689@1" Pin0InfoVect0LinkObjId="SW-188412_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-318845_0" Pin1InfoVect1LinkObjId="SW-188413_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1114,-456 1114,-435 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e55060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1114,-280 1114,-268 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="switch" EndDevType2="breaker" ObjectIDND0="g_2c169e0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c169e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1114,-280 1114,-268 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e59e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="99,-235 73,-235 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="0@0" ObjectIDZND0="28668@x" ObjectIDZND1="g_2e409b0@0" ObjectIDZND2="34198@x" Pin0InfoVect0LinkObjId="SW-188286_0" Pin0InfoVect1LinkObjId="g_2e409b0_0" Pin0InfoVect2LinkObjId="EC-YM_NY.413Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="99,-235 73,-235 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2e5a080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="99,-206 99,-200 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_2e569d0@0" Pin0InfoVect0LinkObjId="g_2e569d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="99,-206 99,-200 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e5ab70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="73,-223 73,-178 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_2e409b0@0" ObjectIDND1="0@x" ObjectIDND2="28668@x" ObjectIDZND0="34198@0" Pin0InfoVect0LinkObjId="EC-YM_NY.413Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2e409b0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-188286_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="73,-223 73,-178 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e5b660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="73,-243 73,-235 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="28668@0" ObjectIDZND0="0@x" ObjectIDZND1="g_2e409b0@0" ObjectIDZND2="34198@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_2e409b0_0" Pin0InfoVect2LinkObjId="EC-YM_NY.413Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188286_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="73,-243 73,-235 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e5b8c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="73,-235 73,-223 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="0@x" ObjectIDND1="28668@x" ObjectIDZND0="g_2e409b0@0" ObjectIDZND1="34198@x" Pin0InfoVect0LinkObjId="g_2e409b0_0" Pin0InfoVect1LinkObjId="EC-YM_NY.413Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-188286_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="73,-235 73,-223 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e5d010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-80,-154 -80,-10 -185,-10 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="34407@1" Pin0InfoVect0LinkObjId="SW-188264_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-80,-154 -80,-10 -185,-10 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e66330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="591,-173 591,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="34201@0" ObjectIDZND0="34413@x" ObjectIDZND1="g_2e46770@0" Pin0InfoVect0LinkObjId="SW-220037_0" Pin0InfoVect1LinkObjId="g_2e46770_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-YM_NY.041Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="591,-173 591,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e66e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="591,-220 591,-209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="34413@0" ObjectIDZND0="34201@x" ObjectIDZND1="g_2e46770@0" Pin0InfoVect0LinkObjId="EC-YM_NY.041Ld_0" Pin0InfoVect1LinkObjId="g_2e46770_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-220037_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="591,-220 591,-209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e67060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="591,-209 557,-209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="34201@x" ObjectIDND1="34413@x" ObjectIDZND0="g_2e46770@0" Pin0InfoVect0LinkObjId="g_2e46770_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-YM_NY.041Ld_0" Pin1InfoVect1LinkObjId="SW-220037_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="591,-209 557,-209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e672c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-80,-180 -80,-334 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="43473@0" ObjectIDZND0="34406@x" ObjectIDZND1="g_2e3dad0@0" Pin0InfoVect0LinkObjId="SW-188263_0" Pin0InfoVect1LinkObjId="g_2e3dad0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-YM_NY.032Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-80,-180 -80,-334 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e67d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-80,-344 -80,-334 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="34406@0" ObjectIDZND0="g_2e3dad0@0" ObjectIDZND1="43473@x" Pin0InfoVect0LinkObjId="g_2e3dad0_0" Pin0InfoVect1LinkObjId="EC-YM_NY.032Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188263_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-80,-344 -80,-334 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e67fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-80,-334 -113,-334 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="34406@x" ObjectIDND1="43473@x" ObjectIDZND0="g_2e3dad0@0" Pin0InfoVect0LinkObjId="g_2e3dad0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-188263_0" Pin1InfoVect1LinkObjId="EC-YM_NY.032Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-80,-334 -113,-334 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e6de00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="434,-551 434,-530 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28713@0" ObjectIDZND0="28723@0" Pin0InfoVect0LinkObjId="g_2b5a290_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188503_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="434,-551 434,-530 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e748d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="898,-884 898,-904 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="28652@1" ObjectIDZND0="28653@0" Pin0InfoVect0LinkObjId="SW-188172_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188171_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="898,-884 898,-904 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e74ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="898,-839 898,-859 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="28721@1" ObjectIDZND0="28652@0" Pin0InfoVect0LinkObjId="SW-188171_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e74cb0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="898,-839 898,-859 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e74cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="898,-721 898,-750 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="28656@1" ObjectIDZND0="28721@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188175_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="898,-721 898,-750 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e74ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="898,-653 898,-686 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="28654@1" ObjectIDZND0="28656@0" Pin0InfoVect0LinkObjId="SW-188175_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188173_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="898,-653 898,-686 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e750d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="899,-598 899,-626 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28655@1" ObjectIDZND0="28654@0" Pin0InfoVect0LinkObjId="SW-188173_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188174_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="899,-598 899,-626 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e777b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="898,-940 898,-976 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28653@1" ObjectIDZND0="28722@0" Pin0InfoVect0LinkObjId="g_2b59930_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188172_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="898,-940 898,-976 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e7aa30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="898,-528 898,-561 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="28724@0" ObjectIDZND0="28655@0" Pin0InfoVect0LinkObjId="SW-188174_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b5a9b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="898,-528 898,-561 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-154235" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -427.000000 -1162.000000)" xlink:href="#dynamicPoint:shape33"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26169" ObjectName="DYN-YM_NY"/>
     <cge:Meas_Ref ObjectId="154235"/>
    </metadata>
   </g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_299b530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -303.000000 129.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2993fd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -314.000000 114.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2996300" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -289.000000 99.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b05460" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 169.000000 1099.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b05720" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 158.000000 1084.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b05960" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 183.000000 1069.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b05d80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 850.000000 1098.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b06040" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 839.000000 1083.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b06280" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 864.000000 1068.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b066a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 99.000000 655.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b06960" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 88.000000 640.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b06ba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 113.000000 625.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b06fc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 111.000000 893.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b07280" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 100.000000 878.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b074c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 125.000000 863.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b078e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 951.000000 893.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b07ba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 940.000000 878.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d60750" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 965.000000 863.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d60b70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 951.000000 658.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d60e30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 940.000000 643.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d61070" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 965.000000 628.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d61490" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 482.000000 728.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d61750" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 471.000000 713.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d61990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 496.000000 698.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d61cc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -75.000000 1042.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d62390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -59.000000 1026.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d62a90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -67.000000 1057.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d62fd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -67.000000 1072.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d63730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -67.000000 1086.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d63a60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -384.000000 568.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d63cd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -368.000000 552.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d63f10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -376.000000 583.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d64150" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -376.000000 598.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d64390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -376.000000 612.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d646c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1282.000000 575.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d64930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1298.000000 559.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d64b70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1290.000000 590.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d64db0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1290.000000 605.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d64ff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1290.000000 619.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d65320" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 91.000000 741.000000) translate(0,12)">油温(℃):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d65f00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 91.000000 756.000000) translate(0,12)">档位(档):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d66820" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 951.000000 738.000000) translate(0,12)">油温(℃):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d66a90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 951.000000 753.000000) translate(0,12)">档位(档):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d75540" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -58.000000 653.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d757e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -83.000000 668.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d75b10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1211.000000 653.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d75d70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1186.000000 668.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -0.814815 -3.000000 82.370370)">
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="-122" x2="-102" y1="-2" y2="9"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="-122" x2="-122" y1="25" y2="-1"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="-121" x2="-99" y1="25" y2="9"/>
   <metadata/><line fill="none" opacity="0" stroke="white" stroke-width="10" transform="" x1="-122" x2="-102" y1="-2" y2="9"/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -0.814815 -7.000000 -0.629630)">
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="-122" x2="-102" y1="-2" y2="9"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="-122" x2="-122" y1="25" y2="-1"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="-121" x2="-99" y1="25" y2="9"/>
   <metadata/><line fill="none" opacity="0" stroke="white" stroke-width="10" transform="" x1="-122" x2="-102" y1="-2" y2="9"/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(0.000000 1.000000 0.814815 -0.000000 -88.370370 46.000000)">
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="-122" x2="-102" y1="-2" y2="9"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="-122" x2="-122" y1="25" y2="-1"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="-121" x2="-99" y1="25" y2="9"/>
   <metadata/><line fill="none" opacity="0" stroke="white" stroke-width="10" transform="" x1="-122" x2="-102" y1="-2" y2="9"/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -0.656250 74.000000 -93.500000)">
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="-415" x2="-399" y1="-128" y2="-144"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="-399" x2="-415" y1="-112" y2="-128"/>
   <metadata/><line fill="none" opacity="0" stroke="white" stroke-width="10" transform="" x1="-415" x2="-399" y1="-128" y2="-144"/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -0.656250 77.000000 -10.500000)">
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="-415" x2="-399" y1="-128" y2="-144"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="-399" x2="-415" y1="-112" y2="-128"/>
   <metadata/><line fill="none" opacity="0" stroke="white" stroke-width="10" transform="" x1="-415" x2="-399" y1="-128" y2="-144"/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(-1.000000 -0.000000 0.000000 -0.814815 75.000000 81.370370)">
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="-122" x2="-102" y1="-2" y2="9"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="-122" x2="-122" y1="25" y2="-1"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="-121" x2="-99" y1="25" y2="9"/>
   <metadata/><line fill="none" opacity="0" stroke="white" stroke-width="10" transform="" x1="-122" x2="-102" y1="-2" y2="9"/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e5d400" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -172.000000 131.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e5d930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -183.000000 116.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e5db70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -158.000000 101.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e5df90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 0.000000 115.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e5e250" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -11.000000 100.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e5e490" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 14.000000 85.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e5e8b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 174.000000 115.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e5eb70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 163.000000 100.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e5edb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 188.000000 85.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e5f1d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 316.000000 114.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e5f490" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 305.000000 99.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e5f6d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 330.000000 84.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e5faf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 691.000000 114.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e5fdb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 680.000000 99.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e5fff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 705.000000 84.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e60410" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 852.000000 118.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e606d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 841.000000 103.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e60910" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 866.000000 88.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e60d30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 515.000000 120.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e60ff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 504.000000 105.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e61230" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 529.000000 90.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e61650" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1059.000000 186.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e61910" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1048.000000 171.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e61b50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1073.000000 156.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e61f70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1239.000000 115.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e62230" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1228.000000 100.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e62470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1253.000000 85.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="PAS_T1" endPointId="0" endStationName="YM_NY" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_shaohuannengTny" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="113,-1245 113,-1273 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="37851" ObjectName="AC-35kV.LN_shaohuannengTny"/>
    <cge:TPSR_Ref TObjectID="37851_SS-238"/></metadata>
   <polyline fill="none" opacity="0" points="113,-1245 113,-1273 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="YM_NY" endPointId="0" endStationName="PAS_T1" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_shaonengNY" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="790,-1258 790,-1290 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="42321" ObjectName="AC-35kV.LN_shaonengNY"/>
    <cge:TPSR_Ref TObjectID="42321_SS-238"/></metadata>
   <polyline fill="none" opacity="0" points="790,-1258 790,-1290 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="28722" cx="113" cy="-976" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28722" cx="433" cy="-976" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28722" cx="790" cy="-976" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28722" cx="48" cy="-976" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28723" cx="-121" cy="-530" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28723" cx="48" cy="-530" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28724" cx="705" cy="-528" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28723" cx="-227" cy="-530" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28724" cx="1139" cy="-528" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28724" cx="578" cy="-528" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28723" cx="288" cy="-530" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28723" cx="-80" cy="-530" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28724" cx="591" cy="-528" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28723" cx="-238" cy="-530" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28723" cx="73" cy="-530" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28723" cx="234" cy="-530" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28723" cx="382" cy="-530" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28724" cx="755" cy="-528" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28724" cx="912" cy="-528" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28724" cx="1313" cy="-528" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28724" cx="1114" cy="-528" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28723" cx="434" cy="-530" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28722" cx="898" cy="-976" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28724" cx="898" cy="-528" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-188043">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 104.333333 -1056.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28639" ObjectName="SW-YM_NY.YM_NY_331BK"/>
     <cge:Meas_Ref ObjectId="188043"/>
    <cge:TPSR_Ref TObjectID="28639"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188057">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 781.333333 -1055.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28643" ObjectName="SW-YM_NY.YM_NY_332BK"/>
     <cge:Meas_Ref ObjectId="188057"/>
    <cge:TPSR_Ref TObjectID="28643"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188101">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 39.000000 -847.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28647" ObjectName="SW-YM_NY.YM_NY_301BK"/>
     <cge:Meas_Ref ObjectId="188101"/>
    <cge:TPSR_Ref TObjectID="28647"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188103">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 39.000000 -613.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28649" ObjectName="SW-YM_NY.YM_NY_001BK"/>
     <cge:Meas_Ref ObjectId="188103"/>
    <cge:TPSR_Ref TObjectID="28649"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188457">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -131.000000 -618.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28698" ObjectName="SW-YM_NY.YM_NY_031BK"/>
     <cge:Meas_Ref ObjectId="188457"/>
    <cge:TPSR_Ref TObjectID="28698"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188479">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1130.000000 -616.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28705" ObjectName="SW-YM_NY.YM_NY_042BK"/>
     <cge:Meas_Ref ObjectId="188479"/>
    <cge:TPSR_Ref TObjectID="28705"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188171">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 889.000000 -849.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28652" ObjectName="SW-YM_NY.YM_NY_302BK"/>
     <cge:Meas_Ref ObjectId="188171"/>
    <cge:TPSR_Ref TObjectID="28652"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188173">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 889.000000 -617.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28654" ObjectName="SW-YM_NY.YM_NY_002BK"/>
     <cge:Meas_Ref ObjectId="188173"/>
    <cge:TPSR_Ref TObjectID="28654"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188239">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -237.000000 -407.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28657" ObjectName="SW-YM_NY.YM_NY_411BK"/>
     <cge:Meas_Ref ObjectId="188239"/>
    <cge:TPSR_Ref TObjectID="28657"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188283">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 64.000000 -408.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28665" ObjectName="SW-YM_NY.YM_NY_413BK"/>
     <cge:Meas_Ref ObjectId="188283"/>
    <cge:TPSR_Ref TObjectID="28665"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188305">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 225.000000 -409.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28669" ObjectName="SW-YM_NY.YM_NY_414BK"/>
     <cge:Meas_Ref ObjectId="188305"/>
    <cge:TPSR_Ref TObjectID="28669"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188327">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 373.000000 -410.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28673" ObjectName="SW-YM_NY.YM_NY_415BK"/>
     <cge:Meas_Ref ObjectId="188327"/>
    <cge:TPSR_Ref TObjectID="28673"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188371">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 746.000000 -409.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28681" ObjectName="SW-YM_NY.YM_NY_432BK"/>
     <cge:Meas_Ref ObjectId="188371"/>
    <cge:TPSR_Ref TObjectID="28681"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188390">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 903.000000 -408.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28685" ObjectName="SW-YM_NY.YM_NY_433BK"/>
     <cge:Meas_Ref ObjectId="188390"/>
    <cge:TPSR_Ref TObjectID="28685"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188412">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1105.000000 -400.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28689" ObjectName="SW-YM_NY.YM_NY_434BK"/>
     <cge:Meas_Ref ObjectId="188412"/>
    <cge:TPSR_Ref TObjectID="28689"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1105.000000 -214.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1008.000000 -205.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1188.000000 -204.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188435">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1304.000000 -407.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28694" ObjectName="SW-YM_NY.YM_NY_435BK"/>
     <cge:Meas_Ref ObjectId="188435"/>
    <cge:TPSR_Ref TObjectID="28694"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188502">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 425.000000 -602.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28712" ObjectName="SW-YM_NY.YM_NY_012BK"/>
     <cge:Meas_Ref ObjectId="188502"/>
    <cge:TPSR_Ref TObjectID="28712"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188349">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 582.000000 -385.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28677" ObjectName="SW-YM_NY.YM_NY_041BK"/>
     <cge:Meas_Ref ObjectId="188349"/>
    <cge:TPSR_Ref TObjectID="28677"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188261">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -90.000000 -389.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34404" ObjectName="SW-YM_NY.YM_NY_032BK"/>
     <cge:Meas_Ref ObjectId="188261"/>
    <cge:TPSR_Ref TObjectID="34404"/></metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(64,64,64)" font-family="SimHei" font-size="20" graphid="g_2a4a1a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -567.000000 -1239.500000) translate(0,16)">能禹变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2843490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,17)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2843490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2843490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,59)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2843490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2843490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,101)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2843490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2843490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,143)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2843490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2843490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,185)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ba5990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ba5990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ba5990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ba5990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ba5990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ba5990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ba5990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ba5990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ba5990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ba5990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ba5990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ba5990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ba5990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ba5990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ba5990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ba5990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ba5990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ba5990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d456e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 723.000000 -141.000000) translate(0,12)">能禹市场线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cea6e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 895.000000 -141.000000) translate(0,12)">星火线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d097d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -250.000000 -149.000000) translate(0,12)">东山线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c3d4e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 997.000000 -441.000000) translate(0,12)">闽中线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ceced0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 396.000000 -1196.000000) translate(0,12)">35kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ced1b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 85.000000 -1302.000000) translate(0,12)">哨黄能线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2be4270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 769.000000 -1306.000000) translate(0,12)">哨能线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c7e1f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -182.000000 -477.000000) translate(0,12)">4110</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d0caa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 287.000000 -794.000000) translate(0,12)">10kVⅠ段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d0cea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 637.000000 -834.000000) translate(0,12)">10kVⅡ段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d0d080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -293.000000 -770.000000) translate(0,12)">10kV1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d11030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 330.000000 -648.000000) translate(0,12)">4100</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c50160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 747.000000 -653.000000) translate(0,12)">4300</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c60e60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 418.000000 -480.000000) translate(0,12)">4150</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c7bf40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 952.000000 -477.000000) translate(0,12)">4330</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bc7050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1043.000000 -287.000000) translate(0,12)">Z01160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bccc80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1218.000000 -285.000000) translate(0,12)">Z01260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c25900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1349.000000 -477.000000) translate(0,12)">4350</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b5fa30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 30.000000 -141.000000) translate(0,12)">城区Ⅴ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b60920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 200.000000 -141.000000) translate(0,12)">城区Ⅳ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b60e80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 347.000000 -141.000000) translate(0,12)">城区Ⅵ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b613e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1193.000000 -444.000000) translate(0,12)">保障房线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b61f80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1294.000000 -139.000000) translate(0,12)">牛街线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b64330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1204.000000 -361.000000) translate(0,12)">Z0126</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b64820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 81.000000 -502.000000) translate(0,12)">4131</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b64a60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 80.000000 -388.000000) translate(0,12)">4132</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b64ca0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 80.000000 -268.000000) translate(0,12)">4133</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b64ee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 82.000000 -437.000000) translate(0,12)">413</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b65120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 241.000000 -500.000000) translate(0,12)">4141</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b65360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 241.000000 -389.000000) translate(0,12)">4142</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b655a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 241.000000 -269.000000) translate(0,12)">4143</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b657e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 243.000000 -438.000000) translate(0,12)">414</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b65a20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 389.000000 -501.000000) translate(0,12)">4151</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b65c60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 389.000000 -390.000000) translate(0,12)">4152</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b65ea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 389.000000 -270.000000) translate(0,12)">4153</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b660e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -62.000000 -1000.000000) translate(0,12)">35kVIM段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2afafc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -220.000000 -484.000000) translate(0,12)">4111</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2afb5f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -220.000000 -387.000000) translate(0,12)">4112</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2afb830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -220.000000 -267.000000) translate(0,12)">4113</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2afba70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -219.000000 -436.000000) translate(0,12)">411</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2afd4e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 441.000000 -576.000000) translate(0,12)">0121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2afd990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 585.000000 -614.000000) translate(0,12)">0122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2afdbd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 443.000000 -631.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2afde10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1172.000000 -632.000000) translate(0,12)">04227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2afe250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1146.000000 -584.000000) translate(0,12)">0422</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2afe680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1146.000000 -692.000000) translate(0,12)">0423</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2afe8c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1180.000000 -807.000000) translate(0,12)">04260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2afeb00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1178.000000 -871.000000) translate(0,12)">04267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2afed40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1146.000000 -825.000000) translate(0,12)">0426</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2afef80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1148.000000 -645.000000) translate(0,12)">042</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2aff1c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 712.000000 -587.000000) translate(0,12)">4318</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2aff400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1121.000000 -379.000000) translate(0,12)">4342</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2aff640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1123.000000 -429.000000) translate(0,12)">434</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2aff880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1023.000000 -366.000000) translate(0,12)">Z0116</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2affac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 762.000000 -495.000000) translate(0,12)">4321</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2affd00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 762.000000 -389.000000) translate(0,12)">4322</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2afff40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 762.000000 -269.000000) translate(0,12)">4323</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b00180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 764.000000 -438.000000) translate(0,12)">432</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b003c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1320.000000 -496.000000) translate(0,12)">4351</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b00600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1320.000000 -387.000000) translate(0,12)">4352</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b00840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1320.000000 -267.000000) translate(0,12)">4353</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b00a80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1322.000000 -436.000000) translate(0,12)">435</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b00cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -87.000000 -634.000000) translate(0,12)">03117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b00f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -114.000000 -586.000000) translate(0,12)">0311</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b01140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -114.000000 -694.000000) translate(0,12)">0312</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b01380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -114.000000 -829.000000) translate(0,12)">0316</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b015c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -84.000000 -813.000000) translate(0,12)">03160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b01800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -83.000000 -876.000000) translate(0,12)">03167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b01a40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -113.000000 -647.000000) translate(0,12)">031</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b01c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 295.000000 -582.000000) translate(0,12)">4118</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b01ec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 919.000000 -494.000000) translate(0,12)">4331</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b02100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 919.000000 -389.000000) translate(0,12)">4332</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b02340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 918.000000 -269.000000) translate(0,12)">4333</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b02580" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 921.000000 -437.000000) translate(0,12)">433</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b027c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -231.000000 -571.000000) translate(0,12)">0410</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b02a00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 55.000000 -585.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b02c40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 55.000000 -709.000000) translate(0,12)">0016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b02e80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 57.000000 -644.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b030c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 55.000000 -925.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b03300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 57.000000 -876.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b03540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 905.000000 -711.000000) translate(0,12)">0026</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b03780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 907.000000 -646.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b039c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 907.000000 -878.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b03c00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 470.000000 -1090.000000) translate(0,12)">3110</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b03e40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 440.000000 -1021.000000) translate(0,12)">3118</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b04080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 120.000000 -1020.000000) translate(0,12)">3311</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b042c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 120.000000 -1147.000000) translate(0,12)">3316</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b04500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 150.000000 -1203.000000) translate(0,12)">33167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b04740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 123.000000 -1085.000000) translate(0,12)">331</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b04980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 797.000000 -1019.000000) translate(0,12)">3321</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b04bc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 825.000000 -1204.000000) translate(0,12)">33267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b04e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 797.000000 -1146.000000) translate(0,12)">3326</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b05040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 804.000000 -1083.000000) translate(0,12)">332</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d69c60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 391.000000 -439.000000) translate(0,12)">415</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d6df10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 89.000000 -809.000000) translate(0,12)">SZ11-10000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d6df10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 89.000000 -809.000000) translate(0,27)">35±3×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d6df10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 89.000000 -809.000000) translate(0,42)">YN,d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d700f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 930.000000 -807.000000) translate(0,12)">SZ11-10000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d700f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 930.000000 -807.000000) translate(0,27)">35±3×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d700f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 930.000000 -807.000000) translate(0,42)">YN,d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d703b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1027.000000 -233.000000) translate(0,12)">Z011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d705e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1125.000000 -238.000000) translate(0,12)">Z001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d707f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1208.000000 -231.000000) translate(0,12)">Z012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_2d727f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -344.000000 -1204.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_2d74da0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -344.000000 -1241.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b23c10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 599.000000 -487.000000) translate(0,12)">0412</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b24240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 600.000000 -365.000000) translate(0,12)">0413</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b24480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 598.000000 -245.000000) translate(0,12)">0414</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b246c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 603.000000 -411.000000) translate(0,12)">041</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b24900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 314.000000 52.000000) translate(0,12)">能禹Ⅰ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b30f30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -72.000000 -491.000000) translate(0,12)">0321</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b31560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -71.000000 -369.000000) translate(0,12)">0323</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b317a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -68.000000 -415.000000) translate(0,12)">032</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b319e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -247.000000 -40.000000) translate(0,12)">能禹Ⅱ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b3f8b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -294.000000 20.000000) translate(0,12)">站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b3f8b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -294.000000 20.000000) translate(0,27)">外</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b41ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 739.000000 76.000000) translate(0,12)">087</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b425e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 804.000000 74.000000) translate(0,12)">黄</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b425e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 804.000000 74.000000) translate(0,27)">瓜</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b425e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 804.000000 74.000000) translate(0,42)">园</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b425e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 804.000000 74.000000) translate(0,57)">变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b42ee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -216.000000 2.000000) translate(0,12)">0326</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b43150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -215.000000 84.000000) translate(0,12)">0416</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b43390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -40.000000 -460.000000) translate(0,12)">03217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b435d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 630.000000 -456.000000) translate(0,12)">04127</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e35f20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 96.000000 -826.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e38460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 939.000000 -824.000000) translate(0,12)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e3ab00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 451.000000 -660.000000) translate(0,12)">10kV分段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e4fed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1132.000000 -293.000000) translate(0,12)">Z0010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e54a30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1121.000000 -498.000000) translate(0,12)">4341</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_2e5bb20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 87.000000 -174.000000) translate(0,8)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2e626b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -736.000000 -261.500000) translate(0,17)">元谋巡维中心：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2e63ef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -590.000000 -271.000000) translate(0,16)">18787879021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2e63ef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -590.000000 -271.000000) translate(0,36)">13908784331</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2e65540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -590.000000 -306.000000) translate(0,16)">8320100</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e68230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 609.000000 -221.000000) translate(0,12)">能</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e68230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 609.000000 -221.000000) translate(0,27)">禹</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e68230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 609.000000 -221.000000) translate(0,42)">Ⅰ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e68230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 609.000000 -221.000000) translate(0,57)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e68230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 609.000000 -221.000000) translate(0,72)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e68870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -60.000000 -225.000000) translate(0,12)">能</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e68870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -60.000000 -225.000000) translate(0,27)">禹</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e68870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -60.000000 -225.000000) translate(0,42)">Ⅱ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e68870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -60.000000 -225.000000) translate(0,57)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e68870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -60.000000 -225.000000) translate(0,72)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e6a650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -699.000000 -850.000000) translate(0,12)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_2e6b870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -431.500000 -1243.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e6c5b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -321.000000 -521.000000) translate(0,12)">10kVI段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e6caf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1352.000000 -519.000000) translate(0,12)">10kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e730b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -214.000000 -1007.000000) translate(0,12)">10kV1号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e74120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 798.000000 -480.000000) translate(0,12)">4320</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e74690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1152.000000 -480.000000) translate(0,12)">4340</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e77fe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 905.000000 -929.000000) translate(0,12)">3021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e7b260" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 906.000000 -587.000000) translate(0,12)">0022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e7b890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 108.000000 -480.000000) translate(0,12)">4130</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e7bad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 273.000000 -479.000000) translate(0,12)">4140</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e7d7d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1074.000000 -993.000000) translate(0,12)">10kV2号电容器组</text>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2cf2770" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 186.819337 -1171.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c80400" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 513.819337 -1058.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2cac170" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -151.180663 -446.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c204a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -47.180663 -844.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d0bf50" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -51.180663 -602.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c433d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 861.819337 -1172.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bfb930" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1214.819337 -839.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ca3a80" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1208.819337 -600.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d10340" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 369.819337 -615.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c4f470" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 786.819337 -620.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b571d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 148.819337 -447.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2baf460" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 309.819337 -448.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c60170" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 457.819337 -449.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bdf5a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 830.819337 -448.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c7b250" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 987.819337 -448.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bd6eb0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1184.819337 -450.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bc6360" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1082.819337 -256.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bcbf90" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1257.819337 -254.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c24c10" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1388.819337 -446.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2af4710" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -48.180663 -781.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2af9e10" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1216.819337 -775.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b1ab50" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 666.819337 -424.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b28620" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -4.180663 -428.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e4f440" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1171.819337 -262.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="YM_NY"/>
</svg>