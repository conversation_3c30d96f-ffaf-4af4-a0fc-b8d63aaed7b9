<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-271" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="3136 -1491 3335 1732">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="capacitor:shape36">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.370253" x1="2" x2="2" y1="45" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="0.607143" x1="47" x2="47" y1="63" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="47" x2="47" y1="24" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="41" x2="53" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="51" x2="43" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="48" x2="45" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="36" x2="21" y1="25" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72286" x1="28" x2="28" y1="17" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="12" x2="12" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="45" x2="45" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.338133" x1="28" x2="11" y1="58" y2="58"/>
    <rect height="23" stroke-width="0.369608" width="12" x="22" y="30"/>
    <rect height="23" stroke-width="0.369608" width="12" x="41" y="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.368819" x1="45" x2="12" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.42985" x1="28" x2="28" y1="79" y2="25"/>
    <polyline arcFlag="1" points="11,39 10,39 9,39 9,40 8,40 8,40 7,41 7,41 6,42 6,42 6,43 6,43 5,44 5,45 5,45 6,46 6,47 6,47 6,48 7,48 7,49 8,49 8,50 9,50 9,50 10,50 11,50 " stroke-width="1"/>
    <polyline arcFlag="1" points="11,28 10,28 9,28 9,28 8,29 8,29 7,29 7,30 6,30 6,31 6,32 6,32 5,33 5,34 5,34 6,35 6,36 6,36 6,37 7,37 7,38 8,38 8,38 9,39 9,39 10,39 11,39 " stroke-width="1"/>
    <polyline arcFlag="1" points="11,18 10,18 9,18 9,18 8,18 8,19 7,19 7,20 6,20 6,21 6,21 6,22 5,23 5,23 5,24 6,25 6,25 6,26 6,26 7,27 7,27 8,28 8,28 9,28 9,29 10,29 11,29 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="11" y1="58" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="11" y1="18" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="29" x2="29" y1="92" y2="100"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="16" x2="28" y1="79" y2="79"/>
    <polyline points="29,92 31,92 33,91 34,91 36,90 37,89 39,88 40,86 41,84 41,83 42,81 42,79 42,77 41,75 41,74 40,72 39,71 37,69 36,68 34,67 33,67 31,66 29,66 27,66 25,67 24,67 22,68 21,69 19,71 18,72 17,74 17,75 16,77 16,79 " stroke-width="0.0972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.338133" x1="47" x2="28" y1="63" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="36" x2="21" y1="17" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.395152" x1="28" x2="11" y1="11" y2="11"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="lightningRod:shape132">
    <rect height="16" stroke-width="1" width="31" x="5" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="36" y1="9" y2="9"/>
   </symbol>
   <symbol id="lightningRod:shape100">
    <ellipse cx="12" cy="15" rx="11" ry="12.5" stroke-width="1.22172"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.3331" x1="16" x2="12" y1="12" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.33311" x1="12" x2="12" y1="16" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.3331" x1="8" x2="12" y1="12" y2="16"/>
    <ellipse cx="12" cy="35" rx="11" ry="12" stroke-width="1.22172"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.33311" x1="15" x2="9" y1="35" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.33311" x1="9" x2="9" y1="40" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.33311" x1="15" x2="9" y1="35" y2="41"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <rect height="14" stroke-width="1" width="27" x="18" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="5" x2="5" y1="11" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="9" x2="18" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="8" x2="8" y1="2" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="58" x2="23" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape175">
    <polyline DF8003:Layer="PUBLIC" points="6,4 0,16 12,16 6,4 6,5 6,4 "/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="14" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="5" x2="8" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="3" x2="10" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="12" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
   </symbol>
   <symbol id="lightningRod:shape4">
    <rect height="31" stroke-width="2" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="9" x2="9" y1="35" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="8" x2="8" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape21">
    <rect height="26" stroke-width="1.99997" width="11" x="2" y="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="7" x2="7" y1="50" y2="5"/>
   </symbol>
   <symbol id="lightningRod:shape157">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,36 0,46 10,46 5,36 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,24 0,14 10,14 5,24 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="59" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape10">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="17" y1="7" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="24" y1="19" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="15" y1="24" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="24" y1="17" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="10" y1="17" y2="24"/>
    <circle cx="17" cy="17" fillStyle="0" r="16" stroke-width="1.0625"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="17" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape174">
    <rect height="18" stroke-width="1.1697" width="11" x="1" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.1208" x1="7" x2="7" y1="14" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.1208" x1="7" x2="7" y1="39" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.447552" x1="7" x2="7" y1="7" y2="14"/>
   </symbol>
   <symbol id="lightningRod:shape182">
    <polyline arcFlag="1" points="12,29 10,29 9,29 8,29 7,28 6,28 5,27 4,26 3,25 2,24 1,22 1,21 1,19 0,18 0,16 1,15 1,13 1,12 2,10 3,9 4,8 5,7 6,6 7,5 8,5 9,5 10,5 12,5 " stroke-width="0.402481"/>
   </symbol>
   <symbol id="lightningRod:shape7">
    <polyline DF8003:Layer="PUBLIC" points="1,5 10,17 19,5 1,5 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="20" y2="23"/>
    <polyline DF8003:Layer="PUBLIC" points="19,44 10,32 1,44 19,44 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="29" y2="26"/>
   </symbol>
   <symbol id="load:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
   </symbol>
   <symbol id="load:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="5" y2="29"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,19 9,31 17,19 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape38_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="14" x2="5" y1="9" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="13" x2="22" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="22" x2="13" y1="9" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="5" x2="14" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="4" y1="10" y2="10"/>
   </symbol>
   <symbol id="switch2:shape38_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="13" x2="22" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="20" x2="4" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="22" x2="13" y1="9" y2="0"/>
   </symbol>
   <symbol id="switch2:shape38-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="2" y1="10" y2="10"/>
    <circle cx="15" cy="10" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="12" x2="3" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="11" x2="20" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="20" x2="11" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="3" x2="12" y1="19" y2="10"/>
   </symbol>
   <symbol id="switch2:shape38-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="3" x2="12" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="20" x2="11" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="11" x2="20" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="12" x2="3" y1="10" y2="1"/>
    <circle cx="15" cy="10" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="2" y1="10" y2="10"/>
   </symbol>
   <symbol id="switch2:shape37_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="13" x2="22" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="22" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="22" x2="13" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="5" x2="14" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="14" x2="5" y1="19" y2="10"/>
   </symbol>
   <symbol id="switch2:shape37_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="14" x2="5" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="21" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="5" x2="14" y1="10" y2="1"/>
   </symbol>
   <symbol id="switch2:shape37-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="22" x2="13" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="5" x2="14" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="14" x2="5" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="13" x2="22" y1="10" y2="1"/>
    <circle cx="10" cy="10" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="23" y1="10" y2="10"/>
   </symbol>
   <symbol id="switch2:shape37-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="23" y1="10" y2="10"/>
    <circle cx="10" cy="10" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="13" x2="22" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="14" x2="5" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="5" x2="14" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="22" x2="13" y1="19" y2="10"/>
   </symbol>
   <symbol id="switch2:shape7_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="18" x2="43" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="18" x2="9" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="5" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="9" x2="9" y1="14" y2="0"/>
   </symbol>
   <symbol id="switch2:shape7_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="3" x2="3" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="6" x2="6" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="10" x2="10" y1="14" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="43" x2="10" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape7-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="3" x2="3" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="6" x2="6" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="10" x2="10" y1="14" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="43" x2="10" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape7-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="3" x2="3" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="6" x2="6" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="10" x2="10" y1="14" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="43" x2="10" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape8_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="18" y2="43"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="18" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
   </symbol>
   <symbol id="switch2:shape8_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="44" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
   </symbol>
   <symbol id="switch2:shape8-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="18" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="18" y2="43"/>
   </symbol>
   <symbol id="switch2:shape8-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="44" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape30_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="31" x2="14" y1="13" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape30_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="12" x2="34" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape30-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="31" x2="14" y1="13" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape30-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="12" x2="34" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape27_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
   </symbol>
   <symbol id="switch2:shape27_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
   </symbol>
   <symbol id="switch2:shape27-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
   </symbol>
   <symbol id="switch2:shape27-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
   </symbol>
   <symbol id="transformer2:shape4_0">
    <circle cx="31" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.911765" x1="1" x2="56" y1="49" y2="80"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="56" x2="56" y1="77" y2="77"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="56" x2="54" y1="80" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="51" x2="56" y1="80" y2="80"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="42" x2="26" y1="24" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="42" x2="26" y1="24" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="26" y1="33" y2="16"/>
   </symbol>
   <symbol id="transformer2:shape4_1">
    <ellipse cx="31" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="30" x2="30" y1="58" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="30" x2="38" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="22" x2="30" y1="74" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape38_0">
    <circle cx="30" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="31,57 6,57 6,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="42" y2="0"/>
    <polyline DF8003:Layer="PUBLIC" points="32,14 26,27 39,27 32,14 32,15 32,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="32" y1="51" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="57" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="26" y1="57" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="35" y1="57" y2="53"/>
   </symbol>
   <symbol id="transformer2:shape38_1">
    <circle cx="31" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="30,87 26,78 36,78 30,87 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="87" y2="87"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="81" y2="81"/>
   </symbol>
   <symbol id="transformer2:shape14_0">
    <circle cx="37" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="66" x2="71" y1="84" y2="84"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="70" x2="68" y1="84" y2="79"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="71" x2="71" y1="81" y2="81"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.911765" x1="1" x2="69" y1="45" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="37" x2="28" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="37" x2="45" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="28" x2="45" y1="18" y2="18"/>
   </symbol>
   <symbol id="transformer2:shape14_1">
    <ellipse cx="37" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="29" x2="37" y1="75" y2="67"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="37" x2="45" y1="67" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="37" x2="37" y1="59" y2="67"/>
   </symbol>
   <symbol id="transformer2:shape24_0">
    <circle cx="16" cy="20" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="39" x2="14" y1="47" y2="72"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="42" x2="39" y1="77" y2="77"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="74" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="71" y2="71"/>
    <polyline DF8003:Layer="PUBLIC" points="14,84 20,71 7,71 14,84 14,83 14,84 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="56" y2="98"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="15" y1="19" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="19" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="13" y2="19"/>
   </symbol>
   <symbol id="transformer2:shape24_1">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,41 40,41 40,70 " stroke-width="1"/>
    <circle cx="16" cy="42" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="41" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="43" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="42" y2="47"/>
   </symbol>
   <symbol id="voltageTransformer:shape79">
    <circle cx="18" cy="24" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="34" y1="13" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="13" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="19" y1="11" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="19" y1="11" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="25" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="7" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="7" y1="23" y2="25"/>
    <circle cx="18" cy="12" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="8" y1="11" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="8" y1="13" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="15" y2="9"/>
    <polyline points="40,23 28,32 28,36 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="34" y1="37" y2="46"/>
    <rect height="14" stroke-width="1" width="8" x="30" y="23"/>
    <circle cx="7" cy="24" r="7.5" stroke-width="1"/>
    <circle cx="7" cy="12" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="37" y1="48" y2="48"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="36" y1="50" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="39" y1="46" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="25" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="19" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="19" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="34" y1="23" y2="13"/>
   </symbol>
   <symbol id="voltageTransformer:shape64">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="29" y1="17" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="5" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="6" y1="69" y2="69"/>
    <ellipse cx="8" cy="59" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <ellipse cx="8" cy="67" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="5" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="38" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="8" y1="16" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="17" y1="24" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="10" y1="37" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="10" y1="17" y2="30"/>
   </symbol>
   <symbol id="voltageTransformer:shape146">
    <circle cx="23" cy="32" r="7.5" stroke-width="0.804311"/>
    <rect height="13" stroke-width="1" width="5" x="3" y="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="18" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="17" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="34" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="25" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="3" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="4" y1="51" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="2" y1="47" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="47" y2="37"/>
    <circle cx="34" cy="27" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="23" x2="20" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="25" x2="23" y1="24" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="25" x2="23" y1="18" y2="21"/>
    <circle cx="33" cy="14" r="7.5" stroke-width="0.804311"/>
    <circle cx="44" cy="20" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="34" x2="32" y1="18" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="32" x2="29" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="34" x2="32" y1="12" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="35" x2="32" y1="27" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="37" x2="35" y1="30" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="37" x2="35" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="47" x2="43" y1="22" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="47" x2="43" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="43" x2="43" y1="18" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="23" x2="21" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="25" x2="23" y1="35" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="25" x2="23" y1="29" y2="32"/>
    <circle cx="23" cy="21" r="7.5" stroke-width="0.804311"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_3d48dc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3d49f70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3d4a960" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3d4b600" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3d4c830" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3d4d580" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3d4dfe0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_3d4eb00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_34dfb00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_34dfb00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3d51e00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3d51e00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3d53b90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3d53b90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_3d54bb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3d565e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3d57080" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3d57e40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3d58780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3d59e40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3d5aa40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3d5b2e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3d5baa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3d5cb80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3d5d500" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3d5dff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3d5e9b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3d5fe00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3d60920" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3d61900" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3d625c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3d70dc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3d63bc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_3d64df0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_3d663d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1742" width="3345" x="3131" y="-1496"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4205" x2="4166" y1="174" y2="174"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4166" x2="4166" y1="46" y2="174"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="6" x1="5874" x2="6400" y1="-976" y2="-976"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="5.42629" x1="6400" x2="6400" y1="-977" y2="-69"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="5.44422" x1="5874" x2="5874" y1="-977" y2="-66"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="6" x1="5874" x2="6400" y1="-67" y2="-67"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="6" x1="6161" x2="6454" y1="19" y2="19"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="6" x1="6164" x2="6164" y1="19" y2="220"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="6" x1="6451" x2="6451" y1="19" y2="217"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="6" x1="6162" x2="6455" y1="219" y2="219"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3820" x2="3820" y1="-1123" y2="-1178"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="4360" x2="4360" y1="-1140" y2="-1195"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="4816" x2="4816" y1="-1123" y2="-1178"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-218059">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6282.000000 -863.000000)" xlink:href="#switch2:shape38_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33817" ObjectName="SW-LF_DXYD.LF_DXYD_0904XC"/>
     <cge:Meas_Ref ObjectId="218059"/>
    <cge:TPSR_Ref TObjectID="33817"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-218059">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6194.000000 -863.000000)" xlink:href="#switch2:shape37_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33818" ObjectName="SW-LF_DXYD.LF_DXYD_0904XC1"/>
     <cge:Meas_Ref ObjectId="218059"/>
    <cge:TPSR_Ref TObjectID="33818"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-218138">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6283.000000 -734.000000)" xlink:href="#switch2:shape38_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33835" ObjectName="SW-LF_DXYD.LF_DXYD_035XC"/>
     <cge:Meas_Ref ObjectId="218138"/>
    <cge:TPSR_Ref TObjectID="33835"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-218138">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6175.000000 -734.000000)" xlink:href="#switch2:shape37_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33836" ObjectName="SW-LF_DXYD.LF_DXYD_035XC1"/>
     <cge:Meas_Ref ObjectId="218138"/>
    <cge:TPSR_Ref TObjectID="33836"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6067.000000 -767.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-300343">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5976.000000 -738.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46478" ObjectName="SW-LF_DXYD.LF_DXYD_0356SW"/>
     <cge:Meas_Ref ObjectId="300343"/>
    <cge:TPSR_Ref TObjectID="46478"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-218122">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6282.000000 -594.000000)" xlink:href="#switch2:shape38_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33831" ObjectName="SW-LF_DXYD.LF_DXYD_034XC"/>
     <cge:Meas_Ref ObjectId="218122"/>
    <cge:TPSR_Ref TObjectID="33831"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-218122">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6174.000000 -594.000000)" xlink:href="#switch2:shape37_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33832" ObjectName="SW-LF_DXYD.LF_DXYD_034XC1"/>
     <cge:Meas_Ref ObjectId="218122"/>
    <cge:TPSR_Ref TObjectID="33832"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6094.000000 -626.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-218106">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6280.000000 -465.000000)" xlink:href="#switch2:shape38_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33827" ObjectName="SW-LF_DXYD.LF_DXYD_033XC"/>
     <cge:Meas_Ref ObjectId="218106"/>
    <cge:TPSR_Ref TObjectID="33827"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-218106">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6172.000000 -465.000000)" xlink:href="#switch2:shape37_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33828" ObjectName="SW-LF_DXYD.LF_DXYD_033XC1"/>
     <cge:Meas_Ref ObjectId="218106"/>
    <cge:TPSR_Ref TObjectID="33828"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6109.000000 -499.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-300341">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6037.000000 -469.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46480" ObjectName="SW-LF_DXYD.LF_DXYD_0336SW"/>
     <cge:Meas_Ref ObjectId="300341"/>
    <cge:TPSR_Ref TObjectID="46480"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-218089">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6279.000000 -337.000000)" xlink:href="#switch2:shape38_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33822" ObjectName="SW-LF_DXYD.LF_DXYD_032XC"/>
     <cge:Meas_Ref ObjectId="218089"/>
    <cge:TPSR_Ref TObjectID="33822"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-218089">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6171.000000 -337.000000)" xlink:href="#switch2:shape37_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33823" ObjectName="SW-LF_DXYD.LF_DXYD_032XC1"/>
     <cge:Meas_Ref ObjectId="218089"/>
    <cge:TPSR_Ref TObjectID="33823"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6095.000000 -371.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-218018">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6283.000000 -226.000000)" xlink:href="#switch2:shape38_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33813" ObjectName="SW-LF_DXYD.LF_DXYD_003XC"/>
     <cge:Meas_Ref ObjectId="218018"/>
    <cge:TPSR_Ref TObjectID="33813"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-218018">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6175.000000 -226.000000)" xlink:href="#switch2:shape37_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33814" ObjectName="SW-LF_DXYD.LF_DXYD_003XC1"/>
     <cge:Meas_Ref ObjectId="218018"/>
    <cge:TPSR_Ref TObjectID="33814"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-218060">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6282.000000 -111.000000)" xlink:href="#switch2:shape38_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33819" ObjectName="SW-LF_DXYD.LF_DXYD_0903XC"/>
     <cge:Meas_Ref ObjectId="218060"/>
    <cge:TPSR_Ref TObjectID="33819"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-218004">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6353.000000 191.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43249" ObjectName="SW-LF_DXYD.LF_DXYD_30360SW"/>
     <cge:Meas_Ref ObjectId="218004"/>
    <cge:TPSR_Ref TObjectID="43249"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-218005">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6378.000000 114.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33812" ObjectName="SW-LF_DXYD.LF_DXYD_3036SW"/>
     <cge:Meas_Ref ObjectId="218005"/>
    <cge:TPSR_Ref TObjectID="33812"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-300342">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6018.000000 -598.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46479" ObjectName="SW-LF_DXYD.LF_DXYD_0346SW"/>
     <cge:Meas_Ref ObjectId="300342"/>
    <cge:TPSR_Ref TObjectID="46479"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-300340">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6026.000000 -341.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46481" ObjectName="SW-LF_DXYD.LF_DXYD_0326SW"/>
     <cge:Meas_Ref ObjectId="300340"/>
    <cge:TPSR_Ref TObjectID="46481"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-218139">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5896.000000 -760.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33837" ObjectName="SW-LF_DXYD.LF_DXYD_03567SW"/>
     <cge:Meas_Ref ObjectId="218139"/>
    <cge:TPSR_Ref TObjectID="33837"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-218123">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5950.000000 -622.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33833" ObjectName="SW-LF_DXYD.LF_DXYD_03467SW"/>
     <cge:Meas_Ref ObjectId="218123"/>
    <cge:TPSR_Ref TObjectID="33833"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-218107">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5938.000000 -486.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33829" ObjectName="SW-LF_DXYD.LF_DXYD_03367SW"/>
     <cge:Meas_Ref ObjectId="218107"/>
    <cge:TPSR_Ref TObjectID="33829"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-218090">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5947.000000 -361.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33824" ObjectName="SW-LF_DXYD.LF_DXYD_03267SW"/>
     <cge:Meas_Ref ObjectId="218090"/>
    <cge:TPSR_Ref TObjectID="33824"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-312963">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4415.241796 -696.340426)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48483" ObjectName="SW-LF_DX.LF_DX_3011SW"/>
     <cge:Meas_Ref ObjectId="312963"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-312587">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3836.544703 -845.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="45000" ObjectName="SW-LF_DX.LF_DX_35117SW"/>
     <cge:Meas_Ref ObjectId="312587"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-260490">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3810.881701 -776.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44998" ObjectName="SW-LF_DX.LF_DX_3511SW"/>
     <cge:Meas_Ref ObjectId="260490"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-312588">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3810.881701 -944.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44999" ObjectName="SW-LF_DX.LF_DX_3516SW"/>
     <cge:Meas_Ref ObjectId="312588"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-312967">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4412.000000 -337.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48487" ObjectName="SW-LF_DX.LF_DX_001XC1"/>
     <cge:Meas_Ref ObjectId="312967"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-312967">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4412.000000 -262.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48486" ObjectName="SW-LF_DX.LF_DX_001XC"/>
     <cge:Meas_Ref ObjectId="312967"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-313157">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3675.000000 -201.340426)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48495" ObjectName="SW-LF_DX.LF_DX_071XC"/>
     <cge:Meas_Ref ObjectId="313157"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-313157">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3675.000000 -115.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48496" ObjectName="SW-LF_DX.LF_DX_071XC1"/>
     <cge:Meas_Ref ObjectId="313157"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-313159">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3714.000000 -30.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48497" ObjectName="SW-LF_DX.LF_DX_07160SW"/>
     <cge:Meas_Ref ObjectId="313159"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-313705">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4910.000000 -201.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48549" ObjectName="SW-LF_DX.LF_DX_0816XC"/>
     <cge:Meas_Ref ObjectId="313705"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-313705">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4910.000000 -114.659574)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48550" ObjectName="SW-LF_DX.LF_DX_0816XC1"/>
     <cge:Meas_Ref ObjectId="313705"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-313588">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4196.000000 -203.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48535" ObjectName="SW-LF_DX.LF_DX_074XC"/>
     <cge:Meas_Ref ObjectId="313588"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-313588">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4196.000000 -117.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48536" ObjectName="SW-LF_DX.LF_DX_074XC1"/>
     <cge:Meas_Ref ObjectId="313588"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-313590">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4238.000000 -31.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48537" ObjectName="SW-LF_DX.LF_DX_07460SW"/>
     <cge:Meas_Ref ObjectId="313590"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-313591">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4197.000000 31.000000)" xlink:href="#switch2:shape27_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48538" ObjectName="SW-LF_DX.LF_DX_0746SW"/>
     <cge:Meas_Ref ObjectId="313591"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-312589">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3837.544703 -1011.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48441" ObjectName="SW-LF_DX.LF_DX_35167SW"/>
     <cge:Meas_Ref ObjectId="312589"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-312936">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4579.881701 -685.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48478" ObjectName="SW-LF_DX.LF_DX_3121SW"/>
     <cge:Meas_Ref ObjectId="312936"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-312938">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4735.881701 -687.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48480" ObjectName="SW-LF_DX.LF_DX_3122SW"/>
     <cge:Meas_Ref ObjectId="312938"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-313592">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4158.455297 51.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48539" ObjectName="SW-LF_DX.LF_DX_07467SW"/>
     <cge:Meas_Ref ObjectId="313592"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-312908">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 5406.455297 -693.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48474" ObjectName="SW-LF_DX.LF_DX_39020SW"/>
     <cge:Meas_Ref ObjectId="312908"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-312907">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5414.241796 -615.340426)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48473" ObjectName="SW-LF_DX.LF_DX_3902SW"/>
     <cge:Meas_Ref ObjectId="312907"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-312909">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 5408.455297 -586.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48475" ObjectName="SW-LF_DX.LF_DX_39027SW"/>
     <cge:Meas_Ref ObjectId="312909"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-312932">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4167.544703 -857.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48477" ObjectName="SW-LF_DX.LF_DX_35410SW"/>
     <cge:Meas_Ref ObjectId="312932"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-312931">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4138.881701 -788.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48476" ObjectName="SW-LF_DX.LF_DX_3541SW"/>
     <cge:Meas_Ref ObjectId="312931"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-312624">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4383.544703 -848.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48442" ObjectName="SW-LF_DX.LF_DX_35217SW"/>
     <cge:Meas_Ref ObjectId="312624"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-312623">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4350.881701 -779.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="45004" ObjectName="SW-LF_DX.LF_DX_3521SW"/>
     <cge:Meas_Ref ObjectId="312623"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-312625">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4350.881701 -947.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="45005" ObjectName="SW-LF_DX.LF_DX_3526SW"/>
     <cge:Meas_Ref ObjectId="312625"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-312626">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4384.544703 -1014.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="45006" ObjectName="SW-LF_DX.LF_DX_35267SW"/>
     <cge:Meas_Ref ObjectId="312626"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-312660">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4831.544703 -848.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="45013" ObjectName="SW-LF_DX.LF_DX_36127SW"/>
     <cge:Meas_Ref ObjectId="312660"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-312659">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4806.881701 -779.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="45011" ObjectName="SW-LF_DX.LF_DX_3612SW"/>
     <cge:Meas_Ref ObjectId="312659"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-312661">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4806.881701 -947.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="45012" ObjectName="SW-LF_DX.LF_DX_3616SW"/>
     <cge:Meas_Ref ObjectId="312661"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-312662">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4834.544703 -1014.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48443" ObjectName="SW-LF_DX.LF_DX_36167SW"/>
     <cge:Meas_Ref ObjectId="312662"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-312705">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5228.544703 -853.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="45003" ObjectName="SW-LF_DX.LF_DX_36227SW"/>
     <cge:Meas_Ref ObjectId="312705"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-256851">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5203.881701 -784.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="45001" ObjectName="SW-LF_DX.LF_DX_3622SW"/>
     <cge:Meas_Ref ObjectId="256851"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-312706">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5203.881701 -952.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="45002" ObjectName="SW-LF_DX.LF_DX_3626SW"/>
     <cge:Meas_Ref ObjectId="312706"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-312707">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5227.544703 -1019.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48444" ObjectName="SW-LF_DX.LF_DX_36267SW"/>
     <cge:Meas_Ref ObjectId="312707"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-312937">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4580.455297 -670.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48479" ObjectName="SW-LF_DX.LF_DX_31210SW"/>
     <cge:Meas_Ref ObjectId="312937"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-313060">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5042.241796 -696.340426)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48489" ObjectName="SW-LF_DX.LF_DX_3022SW"/>
     <cge:Meas_Ref ObjectId="313060"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-313064">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5039.000000 -336.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48493" ObjectName="SW-LF_DX.LF_DX_002XC1"/>
     <cge:Meas_Ref ObjectId="313064"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-313064">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5039.000000 -262.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48492" ObjectName="SW-LF_DX.LF_DX_002XC"/>
     <cge:Meas_Ref ObjectId="313064"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-312899">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 3914.455297 -694.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48471" ObjectName="SW-LF_DX.LF_DX_39010SW"/>
     <cge:Meas_Ref ObjectId="312899"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-312898">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3924.241796 -616.340426)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48470" ObjectName="SW-LF_DX.LF_DX_3901SW"/>
     <cge:Meas_Ref ObjectId="312898"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-312900">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 3916.455297 -587.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48472" ObjectName="SW-LF_DX.LF_DX_39017SW"/>
     <cge:Meas_Ref ObjectId="312900"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-313713">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4702.500000 -370.500000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48552" ObjectName="SW-LF_DX.LF_DX_012XC"/>
     <cge:Meas_Ref ObjectId="313713"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-313713">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4778.500000 -370.500000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48553" ObjectName="SW-LF_DX.LF_DX_012XC1"/>
     <cge:Meas_Ref ObjectId="313713"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-312939">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4758.544703 -671.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48481" ObjectName="SW-LF_DX.LF_DX_31220SW"/>
     <cge:Meas_Ref ObjectId="312939"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-313061">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5066.544703 -685.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48490" ObjectName="SW-LF_DX.LF_DX_30227SW"/>
     <cge:Meas_Ref ObjectId="313061"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-312964">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4433.544703 -686.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48484" ObjectName="SW-LF_DX.LF_DX_30117SW"/>
     <cge:Meas_Ref ObjectId="312964"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-313160">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3676.000000 29.000000)" xlink:href="#switch2:shape27_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48498" ObjectName="SW-LF_DX.LF_DX_0716SW"/>
     <cge:Meas_Ref ObjectId="313160"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-313161">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3716.000000 106.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48499" ObjectName="SW-LF_DX.LF_DX_07167SW"/>
     <cge:Meas_Ref ObjectId="313161"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-313219">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3846.000000 -199.340426)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48501" ObjectName="SW-LF_DX.LF_DX_072XC"/>
     <cge:Meas_Ref ObjectId="313219"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-313219">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3846.000000 -113.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48502" ObjectName="SW-LF_DX.LF_DX_072XC1"/>
     <cge:Meas_Ref ObjectId="313219"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-313221">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3885.000000 -28.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48503" ObjectName="SW-LF_DX.LF_DX_07260SW"/>
     <cge:Meas_Ref ObjectId="313221"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-313222">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3847.000000 31.000000)" xlink:href="#switch2:shape27_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48504" ObjectName="SW-LF_DX.LF_DX_0726SW"/>
     <cge:Meas_Ref ObjectId="313222"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-313223">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3887.000000 108.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48505" ObjectName="SW-LF_DX.LF_DX_07267SW"/>
     <cge:Meas_Ref ObjectId="313223"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-313627">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4004.000000 -116.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48542" ObjectName="SW-LF_DX.LF_DX_073XC1"/>
     <cge:Meas_Ref ObjectId="313627"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-313629">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4043.000000 -31.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48543" ObjectName="SW-LF_DX.LF_DX_07360SW"/>
     <cge:Meas_Ref ObjectId="313629"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-313627">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4004.000000 -202.340426)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48541" ObjectName="SW-LF_DX.LF_DX_073XC"/>
     <cge:Meas_Ref ObjectId="313627"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-313630">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3969.000000 114.000000)" xlink:href="#switch2:shape27_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48544" ObjectName="SW-LF_DX.LF_DX_0010SW"/>
     <cge:Meas_Ref ObjectId="313630"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-313281">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4372.000000 -200.340426)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48507" ObjectName="SW-LF_DX.LF_DX_075XC"/>
     <cge:Meas_Ref ObjectId="313281"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-313281">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4372.000000 -114.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48508" ObjectName="SW-LF_DX.LF_DX_075XC1"/>
     <cge:Meas_Ref ObjectId="313281"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-313283">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4411.000000 -29.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48509" ObjectName="SW-LF_DX.LF_DX_07560SW"/>
     <cge:Meas_Ref ObjectId="313283"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-313284">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4373.000000 30.000000)" xlink:href="#switch2:shape27_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48510" ObjectName="SW-LF_DX.LF_DX_0756SW"/>
     <cge:Meas_Ref ObjectId="313284"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-313285">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4413.000000 107.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48511" ObjectName="SW-LF_DX.LF_DX_07567SW"/>
     <cge:Meas_Ref ObjectId="313285"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-313343">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4554.000000 -201.340426)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48513" ObjectName="SW-LF_DX.LF_DX_076XC"/>
     <cge:Meas_Ref ObjectId="313343"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-313343">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4554.000000 -115.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48514" ObjectName="SW-LF_DX.LF_DX_076XC1"/>
     <cge:Meas_Ref ObjectId="313343"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-313345">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4593.000000 -30.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48515" ObjectName="SW-LF_DX.LF_DX_07660SW"/>
     <cge:Meas_Ref ObjectId="313345"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-313403">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5096.000000 -198.340426)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48517" ObjectName="SW-LF_DX.LF_DX_082XC"/>
     <cge:Meas_Ref ObjectId="313403"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-313403">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5096.000000 -112.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48518" ObjectName="SW-LF_DX.LF_DX_082XC1"/>
     <cge:Meas_Ref ObjectId="313403"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-313405">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5135.000000 -27.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48519" ObjectName="SW-LF_DX.LF_DX_08260SW"/>
     <cge:Meas_Ref ObjectId="313405"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-313406">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5097.000000 32.000000)" xlink:href="#switch2:shape27_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48520" ObjectName="SW-LF_DX.LF_DX_0826SW"/>
     <cge:Meas_Ref ObjectId="313406"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-313407">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5137.000000 109.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48521" ObjectName="SW-LF_DX.LF_DX_08267SW"/>
     <cge:Meas_Ref ObjectId="313407"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-313465">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5284.000000 -199.340426)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48523" ObjectName="SW-LF_DX.LF_DX_083XC"/>
     <cge:Meas_Ref ObjectId="313465"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-313465">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5284.000000 -113.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48524" ObjectName="SW-LF_DX.LF_DX_083XC1"/>
     <cge:Meas_Ref ObjectId="313465"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-313467">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5323.000000 -28.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48525" ObjectName="SW-LF_DX.LF_DX_08360SW"/>
     <cge:Meas_Ref ObjectId="313467"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-313468">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5285.000000 31.000000)" xlink:href="#switch2:shape27_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48526" ObjectName="SW-LF_DX.LF_DX_0836SW"/>
     <cge:Meas_Ref ObjectId="313468"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-313469">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5325.000000 108.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48527" ObjectName="SW-LF_DX.LF_DX_08367SW"/>
     <cge:Meas_Ref ObjectId="313469"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-313526">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5457.000000 -199.340426)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48529" ObjectName="SW-LF_DX.LF_DX_084XC"/>
     <cge:Meas_Ref ObjectId="313526"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-313526">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5457.000000 -113.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48530" ObjectName="SW-LF_DX.LF_DX_084XC1"/>
     <cge:Meas_Ref ObjectId="313526"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-313528">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5496.000000 -28.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48531" ObjectName="SW-LF_DX.LF_DX_08460SW"/>
     <cge:Meas_Ref ObjectId="313528"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-313529">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5458.000000 31.000000)" xlink:href="#switch2:shape27_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48532" ObjectName="SW-LF_DX.LF_DX_0846SW"/>
     <cge:Meas_Ref ObjectId="313529"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-313530">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5498.000000 108.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48533" ObjectName="SW-LF_DX.LF_DX_08467SW"/>
     <cge:Meas_Ref ObjectId="313530"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-313666">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3741.000000 -329.340426)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48546" ObjectName="SW-LF_DX.LF_DX_0901XC1"/>
     <cge:Meas_Ref ObjectId="313666"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-313666">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3741.000000 -277.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48545" ObjectName="SW-LF_DX.LF_DX_0901XC"/>
     <cge:Meas_Ref ObjectId="313666"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-313678">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5242.000000 -337.340426)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48547" ObjectName="SW-LF_DX.LF_DX_0902XC"/>
     <cge:Meas_Ref ObjectId="313678"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-313678">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5242.000000 -285.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48548" ObjectName="SW-LF_DX.LF_DX_0902XC1"/>
     <cge:Meas_Ref ObjectId="313678"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-313715">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4785.000000 -319.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48555" ObjectName="SW-LF_DX.LF_DX_0122XC1"/>
     <cge:Meas_Ref ObjectId="313715"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-313715">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4785.000000 -266.659574)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48554" ObjectName="SW-LF_DX.LF_DX_0122XC"/>
     <cge:Meas_Ref ObjectId="313715"/>
    </metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-LF_DXYD.LF_DXYD_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6353,-75 6353,-946 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="33809" ObjectName="BS-LF_DXYD.LF_DXYD_9IM"/>
    <cge:TPSR_Ref TObjectID="33809"/></metadata>
   <polyline fill="none" opacity="0" points="6353,-75 6353,-946 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-LF_DX.LF_DX_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3603,-250 4683,-250 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="48556" ObjectName="BS-LF_DX.LF_DX_9IM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="3603,-250 4683,-250 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-LF_DX.LF_DX_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3698,-757 4627,-757 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="42312" ObjectName="BS-LF_DX.LF_DX_3IM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="3698,-757 4627,-757 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-LF_DX.LF_DX_3ⅡM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4696,-757 5681,-757 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="48447" ObjectName="BS-LF_DX.LF_DX_3ⅡM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4696,-757 5681,-757 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-LF_DX.LF_DX_9IIM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4751,-249 5557,-249 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="48557" ObjectName="BS-LF_DX.LF_DX_9IIM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4751,-249 5557,-249 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3680.000000 151.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3851.000000 153.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4377.000000 152.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4559.000000 151.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5101.000000 154.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5289.000000 171.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5462.000000 171.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3811.000000 -1142.000000)" xlink:href="#load:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4351.000000 -1155.000000)" xlink:href="#load:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4807.000000 -1172.000000)" xlink:href="#load:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5204.000000 -1170.000000)" xlink:href="#load:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2e013a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3884.544703 -844.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2df2a30" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3887.544703 -1010.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2db9800" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4110.455297 52.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2dc2440" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 5357.455297 -692.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d28b50" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 5357.455297 -585.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e15f10" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4215.544703 -856.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d17c90" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4434.544703 -847.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d22ba0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4434.544703 -1013.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d30840" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4882.544703 -847.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d3b750" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4884.544703 -1013.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d3eed0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5279.544703 -852.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d99740" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5277.544703 -1018.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2da18b0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4532.455297 -669.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d7aab0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 3865.455297 -693.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d807b0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 3865.455297 -586.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_304a8e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4807.544703 -670.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3053250" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5115.544703 -684.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3053f40" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4482.544703 -685.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_30929b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3972.000000 183.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-10KV" id="g_2ded960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6353,-872 6303,-872 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="33809@0" ObjectIDZND0="33817@1" Pin0InfoVect0LinkObjId="SW-218059_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6353,-872 6303,-872 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ee0970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6286,-872 6268,-872 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="33817@0" ObjectIDZND0="g_2dedb50@1" Pin0InfoVect0LinkObjId="g_2dedb50_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218059_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6286,-872 6268,-872 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ee0b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6237,-872 6215,-872 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2dedb50@0" ObjectIDZND0="33818@1" Pin0InfoVect0LinkObjId="SW-218059_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2dedb50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6237,-872 6215,-872 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f1ecc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6150,-835 6150,-872 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_2edf170@0" ObjectIDZND0="33818@x" ObjectIDZND1="g_2ee0d50@0" Pin0InfoVect0LinkObjId="SW-218059_0" Pin0InfoVect1LinkObjId="g_2ee0d50_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2edf170_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="6150,-835 6150,-872 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f1f480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6198,-872 6150,-872 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="33818@0" ObjectIDZND0="g_2edf170@0" ObjectIDZND1="g_2ee0d50@0" Pin0InfoVect0LinkObjId="g_2edf170_0" Pin0InfoVect1LinkObjId="g_2ee0d50_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218059_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="6198,-872 6150,-872 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e5b500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6150,-872 6089,-872 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2edf170@0" ObjectIDND1="33818@x" ObjectIDZND0="g_2ee0d50@0" Pin0InfoVect0LinkObjId="g_2ee0d50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2edf170_0" Pin1InfoVect1LinkObjId="SW-218059_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6150,-872 6089,-872 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e66040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6353,-743 6304,-743 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="33809@0" ObjectIDZND0="33835@1" Pin0InfoVect0LinkObjId="SW-218138_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6353,-743 6304,-743 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e676b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6287,-743 6259,-743 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="33835@0" ObjectIDZND0="33834@0" Pin0InfoVect0LinkObjId="SW-218137_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218138_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6287,-743 6259,-743 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e678a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6232,-743 6196,-743 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="33834@1" ObjectIDZND0="33836@1" Pin0InfoVect0LinkObjId="SW-218138_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218137_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6232,-743 6196,-743 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ea8b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6103,-716 6103,-743 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2e67a90@0" ObjectIDZND0="0@x" ObjectIDZND1="33836@x" ObjectIDZND2="46478@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-218138_0" Pin0InfoVect2LinkObjId="SW-300343_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e67a90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="6103,-716 6103,-743 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e61370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6118,-774 6118,-743 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="33836@x" ObjectIDZND1="g_2e67a90@0" ObjectIDZND2="46478@x" Pin0InfoVect0LinkObjId="SW-218138_0" Pin0InfoVect1LinkObjId="g_2e67a90_0" Pin0InfoVect2LinkObjId="SW-300343_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="6118,-774 6118,-743 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e61bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6179,-743 6118,-743 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="33836@0" ObjectIDZND0="0@x" ObjectIDZND1="g_2e67a90@0" ObjectIDZND2="46478@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_2e67a90_0" Pin0InfoVect2LinkObjId="SW-300343_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218138_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="6179,-743 6118,-743 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e61db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6118,-743 6103,-743 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="33836@x" ObjectIDZND0="g_2e67a90@0" ObjectIDZND1="46478@x" Pin0InfoVect0LinkObjId="g_2e67a90_0" Pin0InfoVect1LinkObjId="SW-300343_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-218138_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="6118,-743 6103,-743 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e296b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6103,-743 6017,-743 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2e67a90@0" ObjectIDND1="0@x" ObjectIDND2="33836@x" ObjectIDZND0="46478@1" Pin0InfoVect0LinkObjId="SW-300343_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2e67a90_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-218138_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6103,-743 6017,-743 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f0aed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6231,-603 6195,-603 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="33830@1" ObjectIDZND0="33832@1" Pin0InfoVect0LinkObjId="SW-218122_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218121_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6231,-603 6195,-603 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e3f660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6353,-474 6301,-474 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="33809@0" ObjectIDZND0="33827@1" Pin0InfoVect0LinkObjId="SW-218106_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6353,-474 6301,-474 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e40c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6284,-474 6256,-474 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="33827@0" ObjectIDZND0="33826@0" Pin0InfoVect0LinkObjId="SW-218105_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218106_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6284,-474 6256,-474 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e40e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6229,-474 6193,-474 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="33826@1" ObjectIDZND0="33828@1" Pin0InfoVect0LinkObjId="SW-218106_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218105_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6229,-474 6193,-474 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f249d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6353,-346 6300,-346 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="33809@0" ObjectIDZND0="33822@1" Pin0InfoVect0LinkObjId="SW-218089_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6353,-346 6300,-346 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f266a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6283,-346 6255,-346 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="33822@0" ObjectIDZND0="33821@0" Pin0InfoVect0LinkObjId="SW-218088_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218089_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6283,-346 6255,-346 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f268c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6228,-346 6192,-346 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="33821@1" ObjectIDZND0="33823@1" Pin0InfoVect0LinkObjId="SW-218089_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218088_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6228,-346 6192,-346 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e844a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6353,-235 6304,-235 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="33809@0" ObjectIDZND0="33813@1" Pin0InfoVect0LinkObjId="SW-218018_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6353,-235 6304,-235 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e73130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6287,-235 6259,-235 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="33813@0" ObjectIDZND0="33815@0" Pin0InfoVect0LinkObjId="SW-218019_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218018_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6287,-235 6259,-235 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e73350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6232,-235 6196,-235 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="33815@1" ObjectIDZND0="33814@1" Pin0InfoVect0LinkObjId="SW-218018_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218019_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6232,-235 6196,-235 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2eb43c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6353,-120 6303,-120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="33809@0" ObjectIDZND0="33819@1" Pin0InfoVect0LinkObjId="SW-218060_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6353,-120 6303,-120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2eb4e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6168,-120 6107,-120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_2eb45e0@0" ObjectIDZND0="g_2eb6ae0@0" Pin0InfoVect0LinkObjId="g_2eb6ae0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2eb45e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6168,-120 6107,-120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2eb5c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6244,-86 6244,-120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_2eb5020@0" ObjectIDZND0="33819@x" ObjectIDZND1="g_2eb45e0@0" Pin0InfoVect0LinkObjId="SW-218060_0" Pin0InfoVect1LinkObjId="g_2eb45e0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2eb5020_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="6244,-86 6244,-120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2eb66a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6286,-120 6244,-120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="33819@0" ObjectIDZND0="g_2eb5020@0" ObjectIDZND1="g_2eb45e0@0" Pin0InfoVect0LinkObjId="g_2eb5020_0" Pin0InfoVect1LinkObjId="g_2eb45e0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218060_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="6286,-120 6244,-120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2eb68c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6244,-120 6199,-120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2eb5020@0" ObjectIDND1="33819@x" ObjectIDZND0="g_2eb45e0@1" Pin0InfoVect0LinkObjId="g_2eb45e0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2eb5020_0" Pin1InfoVect1LinkObjId="SW-218060_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6244,-120 6199,-120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ece000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6288,109 6315,109 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="33838@1" ObjectIDZND0="33811@1" Pin0InfoVect0LinkObjId="SW-218003_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e803e0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6288,109 6315,109 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e7ef60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6107,-207 6107,-235 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_2e73570@0" ObjectIDZND0="33814@x" ObjectIDZND1="g_2e7fc80@0" Pin0InfoVect0LinkObjId="SW-218018_0" Pin0InfoVect1LinkObjId="g_2e7fc80_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e73570_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="6107,-207 6107,-235 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e7fa20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6179,-235 6107,-235 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="33814@0" ObjectIDZND0="g_2e73570@0" ObjectIDZND1="g_2e7fc80@0" Pin0InfoVect0LinkObjId="g_2e73570_0" Pin0InfoVect1LinkObjId="g_2e7fc80_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218018_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="6179,-235 6107,-235 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e803e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6151,108 6208,108 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_2e80040@0" ObjectIDZND0="33838@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e80040_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6151,108 6208,108 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e80640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6107,-235 6073,-235 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2e73570@0" ObjectIDND1="33814@x" ObjectIDZND0="g_2e7fc80@0" Pin0InfoVect0LinkObjId="g_2e7fc80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2e73570_0" Pin1InfoVect1LinkObjId="SW-218018_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6107,-235 6073,-235 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e808a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6062,-235 5929,-235 5929,108 6140,108 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2e7fc80@1" ObjectIDZND0="g_2e80040@1" Pin0InfoVect0LinkObjId="g_2e80040_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e7fc80_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6062,-235 5929,-235 5929,108 6140,108 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2eba310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6362,140 6362,109 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="43249@0" ObjectIDZND0="33811@x" ObjectIDZND1="33812@x" Pin0InfoVect0LinkObjId="SW-218003_0" Pin0InfoVect1LinkObjId="SW-218005_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218004_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="6362,140 6362,109 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d8e2c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6342,109 6362,109 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="33811@0" ObjectIDZND0="43249@x" ObjectIDZND1="33812@x" Pin0InfoVect0LinkObjId="SW-218004_0" Pin0InfoVect1LinkObjId="SW-218005_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218003_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="6342,109 6362,109 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d8e4b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6362,109 6383,109 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="43249@x" ObjectIDND1="33811@x" ObjectIDZND0="33812@0" Pin0InfoVect0LinkObjId="SW-218005_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-218004_0" Pin1InfoVect1LinkObjId="SW-218003_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6362,109 6383,109 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ed26b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6353,-603 6303,-603 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="33809@0" ObjectIDZND0="33831@1" Pin0InfoVect0LinkObjId="SW-218122_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6353,-603 6303,-603 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ed2930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6286,-603 6258,-603 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="33831@0" ObjectIDZND0="33830@0" Pin0InfoVect0LinkObjId="SW-218121_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218122_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6286,-603 6258,-603 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e30690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5947,-767 5947,-743 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="33837@0" ObjectIDZND0="46478@x" ObjectIDZND1="g_2e2f8e0@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-300343_0" Pin0InfoVect1LinkObjId="g_2e2f8e0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218139_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5947,-767 5947,-743 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e31180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5981,-743 5947,-743 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="46478@0" ObjectIDZND0="33837@x" ObjectIDZND1="g_2e2f8e0@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-218139_0" Pin0InfoVect1LinkObjId="g_2e2f8e0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-300343_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5981,-743 5947,-743 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2dc6670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5989,-493 5989,-474 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="33829@0" ObjectIDZND0="46480@x" ObjectIDZND1="g_2dc58c0@0" Pin0InfoVect0LinkObjId="SW-300341_0" Pin0InfoVect1LinkObjId="g_2dc58c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218107_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5989,-493 5989,-474 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2dc7160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6042,-474 5989,-474 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="46480@0" ObjectIDZND0="33829@x" ObjectIDZND1="g_2dc58c0@0" Pin0InfoVect0LinkObjId="SW-218107_0" Pin0InfoVect1LinkObjId="g_2dc58c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-300341_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="6042,-474 5989,-474 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2dc73c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6146,-378 6146,-346 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="33823@x" ObjectIDZND1="g_2f26ae0@0" ObjectIDZND2="46481@x" Pin0InfoVect0LinkObjId="SW-218089_0" Pin0InfoVect1LinkObjId="g_2f26ae0_0" Pin0InfoVect2LinkObjId="SW-300340_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="6146,-378 6146,-346 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e0ed90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6146,-346 6175,-346 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_2f26ae0@0" ObjectIDND2="46481@x" ObjectIDZND0="33823@0" Pin0InfoVect0LinkObjId="SW-218089_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_2f26ae0_0" Pin1InfoVect2LinkObjId="SW-300340_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6146,-346 6175,-346 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e0eff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6129,-322 6129,-346 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2f26ae0@0" ObjectIDZND0="46481@x" ObjectIDZND1="0@x" ObjectIDZND2="33823@x" Pin0InfoVect0LinkObjId="SW-300340_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-218089_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f26ae0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="6129,-322 6129,-346 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e0fae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6067,-346 6129,-346 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="46481@1" ObjectIDZND0="g_2f26ae0@0" ObjectIDZND1="0@x" ObjectIDZND2="33823@x" Pin0InfoVect0LinkObjId="g_2f26ae0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-218089_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-300340_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="6067,-346 6129,-346 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e0fd40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6129,-346 6146,-346 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2f26ae0@0" ObjectIDND1="46481@x" ObjectIDZND0="0@x" ObjectIDZND1="33823@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-218089_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2f26ae0_0" Pin1InfoVect1LinkObjId="SW-300340_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="6129,-346 6146,-346 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d42b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5998,-368 5998,-346 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="33824@0" ObjectIDZND0="46481@x" ObjectIDZND1="g_2e0ffa0@0" Pin0InfoVect0LinkObjId="SW-300340_0" Pin0InfoVect1LinkObjId="g_2e0ffa0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218090_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5998,-368 5998,-346 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d43610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5998,-346 6031,-346 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="33824@x" ObjectIDND1="g_2e0ffa0@0" ObjectIDZND0="46481@0" Pin0InfoVect0LinkObjId="SW-300340_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-218090_0" Pin1InfoVect1LinkObjId="g_2e0ffa0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5998,-346 6031,-346 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d45b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6160,-506 6160,-474 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="33828@x" ObjectIDZND1="g_2e41070@0" ObjectIDZND2="46480@x" Pin0InfoVect0LinkObjId="SW-218106_0" Pin0InfoVect1LinkObjId="g_2e41070_0" Pin0InfoVect2LinkObjId="SW-300341_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="6160,-506 6160,-474 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d46470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6160,-474 6176,-474 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_2e41070@0" ObjectIDND2="46480@x" ObjectIDZND0="33828@0" Pin0InfoVect0LinkObjId="SW-218106_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_2e41070_0" Pin1InfoVect2LinkObjId="SW-300341_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6160,-474 6176,-474 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d46660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6145,-448 6145,-474 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2e41070@0" ObjectIDZND0="46480@x" ObjectIDZND1="0@x" ObjectIDZND2="33828@x" Pin0InfoVect0LinkObjId="SW-300341_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-218106_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e41070_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="6145,-448 6145,-474 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d470d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6078,-474 6145,-474 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="46480@1" ObjectIDZND0="g_2e41070@0" ObjectIDZND1="0@x" ObjectIDZND2="33828@x" Pin0InfoVect0LinkObjId="g_2e41070_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-218106_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-300341_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="6078,-474 6145,-474 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d47330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6145,-474 6160,-474 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2e41070@0" ObjectIDND1="46480@x" ObjectIDZND0="0@x" ObjectIDZND1="33828@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-218106_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2e41070_0" Pin1InfoVect1LinkObjId="SW-300341_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="6145,-474 6160,-474 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e380e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6001,-629 6001,-603 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="load" EndDevType2="lightningRod" ObjectIDND0="33833@0" ObjectIDZND0="46479@x" ObjectIDZND1="0@x" ObjectIDZND2="g_2e313e0@0" Pin0InfoVect0LinkObjId="SW-300342_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_2e313e0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218123_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="6001,-629 6001,-603 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e38bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6001,-603 6024,-603 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="33833@x" ObjectIDND1="0@x" ObjectIDND2="g_2e313e0@0" ObjectIDZND0="46479@0" Pin0InfoVect0LinkObjId="SW-300342_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-218123_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_2e313e0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6001,-603 6024,-603 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e38e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6130,-575 6130,-603 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2f0b0c0@0" ObjectIDZND0="46479@x" ObjectIDZND1="0@x" ObjectIDZND2="33832@x" Pin0InfoVect0LinkObjId="SW-300342_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-218122_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f0b0c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="6130,-575 6130,-603 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e398e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6130,-603 6059,-603 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2f0b0c0@0" ObjectIDND1="0@x" ObjectIDND2="33832@x" ObjectIDZND0="46479@1" Pin0InfoVect0LinkObjId="SW-300342_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2f0b0c0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-218122_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6130,-603 6059,-603 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e39b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6145,-633 6145,-603 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="33832@x" ObjectIDZND1="g_2f0b0c0@0" ObjectIDZND2="46479@x" Pin0InfoVect0LinkObjId="SW-218122_0" Pin0InfoVect1LinkObjId="g_2f0b0c0_0" Pin0InfoVect2LinkObjId="SW-300342_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="6145,-633 6145,-603 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e3a610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6178,-603 6145,-603 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="33832@0" ObjectIDZND0="0@x" ObjectIDZND1="g_2f0b0c0@0" ObjectIDZND2="46479@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_2f0b0c0_0" Pin0InfoVect2LinkObjId="SW-300342_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218122_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="6178,-603 6145,-603 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e3a870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6145,-603 6130,-603 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="33832@x" ObjectIDZND0="g_2f0b0c0@0" ObjectIDZND1="46479@x" Pin0InfoVect0LinkObjId="g_2f0b0c0_0" Pin0InfoVect1LinkObjId="SW-300342_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-218122_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="6145,-603 6130,-603 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e3aad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5969,-448 5969,-474 5989,-474 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2dc58c0@0" ObjectIDZND0="33829@x" ObjectIDZND1="46480@x" Pin0InfoVect0LinkObjId="SW-218107_0" Pin0InfoVect1LinkObjId="SW-300341_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2dc58c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5969,-448 5969,-474 5989,-474 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e3ad30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5978,-323 5978,-346 5998,-346 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2e0ffa0@0" ObjectIDZND0="33824@x" ObjectIDZND1="46481@x" Pin0InfoVect0LinkObjId="SW-218090_0" Pin0InfoVect1LinkObjId="SW-300340_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e0ffa0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5978,-323 5978,-346 5998,-346 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e01e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3878,-850 3889,-850 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="45000@0" ObjectIDZND0="g_2e013a0@0" Pin0InfoVect0LinkObjId="g_2e013a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-312587_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3878,-850 3889,-850 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e9c3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3797,-404 3797,-372 3751,-372 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_2e3af90@0" ObjectIDZND0="48546@x" ObjectIDZND1="g_2e9ba20@0" Pin0InfoVect0LinkObjId="SW-313666_0" Pin0InfoVect1LinkObjId="g_2e9ba20_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e3af90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3797,-404 3797,-372 3751,-372 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e9c650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3751,-375 3751,-353 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_2e3af90@0" ObjectIDND1="g_2e9ba20@0" ObjectIDZND0="48546@0" Pin0InfoVect0LinkObjId="SW-313666_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2e3af90_0" Pin1InfoVect1LinkObjId="g_2e9ba20_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3751,-375 3751,-353 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e898f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4422,-344 4422,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="48487@1" ObjectIDZND0="48485@1" Pin0InfoVect0LinkObjId="SW-312965_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-312967_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4422,-344 4422,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e89b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4422,-286 4422,-303 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="48486@1" ObjectIDZND0="48485@0" Pin0InfoVect0LinkObjId="SW-312965_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-312967_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4422,-286 4422,-303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e8a3e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3685,-184 3685,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="48494@1" ObjectIDZND0="48495@1" Pin0InfoVect0LinkObjId="SW-313157_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-313156_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3685,-184 3685,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e7b0e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3685,-137 3685,-157 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="48496@1" ObjectIDZND0="48494@0" Pin0InfoVect0LinkObjId="SW-313156_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-313157_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3685,-137 3685,-157 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2da8c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4920,-186 4920,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3436bb0@0" ObjectIDZND0="48549@1" Pin0InfoVect0LinkObjId="SW-313705_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3436bb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4920,-186 4920,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e08370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4920,-137 4920,-159 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="48550@1" ObjectIDZND0="g_3436bb0@1" Pin0InfoVect0LinkObjId="g_3436bb0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-313705_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4920,-137 4920,-159 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e085d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4206,-186 4206,-210 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="48534@1" ObjectIDZND0="48535@1" Pin0InfoVect0LinkObjId="SW-313588_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-313587_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4206,-186 4206,-210 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e54140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4206,-139 4206,-159 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="48536@1" ObjectIDZND0="48534@0" Pin0InfoVect0LinkObjId="SW-313587_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-313588_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4206,-139 4206,-159 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2dedff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3751,-390 3751,-372 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2e9ba20@1" ObjectIDZND0="g_2e3af90@0" ObjectIDZND1="48546@x" Pin0InfoVect0LinkObjId="g_2e3af90_0" Pin0InfoVect1LinkObjId="SW-313666_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e9ba20_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3751,-390 3751,-372 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2defb10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3842,-850 3820,-850 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="45000@1" ObjectIDZND0="42701@x" ObjectIDZND1="44998@x" Pin0InfoVect0LinkObjId="SW-260488_0" Pin0InfoVect1LinkObjId="SW-260490_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-312587_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3842,-850 3820,-850 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2defd00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3820,-871 3820,-850 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="42701@0" ObjectIDZND0="45000@x" ObjectIDZND1="44998@x" Pin0InfoVect0LinkObjId="SW-312587_0" Pin0InfoVect1LinkObjId="SW-260490_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-260488_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3820,-871 3820,-850 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2defef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3820,-850 3820,-817 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="45000@x" ObjectIDND1="42701@x" ObjectIDZND0="44998@1" Pin0InfoVect0LinkObjId="SW-260490_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-312587_0" Pin1InfoVect1LinkObjId="SW-260488_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3820,-850 3820,-817 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2df34c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3878,-1016 3892,-1016 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="48441@0" ObjectIDZND0="g_2df2a30@0" Pin0InfoVect0LinkObjId="g_2df2a30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-312589_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3878,-1016 3892,-1016 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2dba250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4117,46 4106,46 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="48539@0" ObjectIDZND0="g_2db9800@0" Pin0InfoVect0LinkObjId="g_2db9800_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-313592_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4117,46 4106,46 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d295e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5403,-591 5423,-591 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="48475@1" ObjectIDZND0="48473@x" ObjectIDZND1="g_2d29aa0@0" ObjectIDZND2="g_2dd6e20@0" Pin0InfoVect0LinkObjId="SW-312907_0" Pin0InfoVect1LinkObjId="g_2d29aa0_0" Pin0InfoVect2LinkObjId="g_2dd6e20_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-312909_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5403,-591 5423,-591 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d29840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5423,-620 5423,-591 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="48473@0" ObjectIDZND0="48475@x" ObjectIDZND1="g_2d29aa0@0" ObjectIDZND2="g_2dd6e20@0" Pin0InfoVect0LinkObjId="SW-312909_0" Pin0InfoVect1LinkObjId="g_2d29aa0_0" Pin0InfoVect2LinkObjId="g_2dd6e20_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-312907_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5423,-620 5423,-591 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d2a7d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5423,-591 5423,-559 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="48475@x" ObjectIDND1="48473@x" ObjectIDZND0="g_2d29aa0@0" ObjectIDZND1="g_2dd6e20@0" Pin0InfoVect0LinkObjId="g_2d29aa0_0" Pin0InfoVect1LinkObjId="g_2dd6e20_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-312909_0" Pin1InfoVect1LinkObjId="SW-312907_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5423,-591 5423,-559 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d2aa30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5463,-530 5463,-559 5464,-559 5423,-559 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2d29aa0@0" ObjectIDZND0="48475@x" ObjectIDZND1="48473@x" ObjectIDZND2="g_2dd6e20@0" Pin0InfoVect0LinkObjId="SW-312909_0" Pin0InfoVect1LinkObjId="SW-312907_0" Pin0InfoVect2LinkObjId="g_2dd6e20_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d29aa0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5463,-530 5463,-559 5464,-559 5423,-559 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d2aca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5353,-698 5365,-698 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2dc2440@0" ObjectIDZND0="48474@0" Pin0InfoVect0LinkObjId="SW-312908_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2dc2440_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5353,-698 5365,-698 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d2af00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5367,-591 5353,-591 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="48475@0" ObjectIDZND0="g_2d28b50@0" Pin0InfoVect0LinkObjId="g_2d28b50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-312909_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5367,-591 5353,-591 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d2b160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3685,-225 3685,-250 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48495@0" ObjectIDZND0="48556@0" Pin0InfoVect0LinkObjId="g_2f83540_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-313157_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3685,-225 3685,-250 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e169a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4209,-862 4220,-862 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="48477@0" ObjectIDZND0="g_2e15f10@0" Pin0InfoVect0LinkObjId="g_2e15f10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-312932_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4209,-862 4220,-862 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d18720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4425,-853 4439,-853 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="48442@0" ObjectIDZND0="g_2d17c90@0" Pin0InfoVect0LinkObjId="g_2d17c90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-312624_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4425,-853 4439,-853 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d1f9f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4389,-853 4360,-853 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="48442@1" ObjectIDZND0="42316@x" ObjectIDZND1="45004@x" Pin0InfoVect0LinkObjId="SW-256890_0" Pin0InfoVect1LinkObjId="SW-312623_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-312624_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4389,-853 4360,-853 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d1fc50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4360,-874 4360,-853 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="42316@0" ObjectIDZND0="48442@x" ObjectIDZND1="45004@x" Pin0InfoVect0LinkObjId="SW-312624_0" Pin0InfoVect1LinkObjId="SW-312623_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-256890_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4360,-874 4360,-853 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d1feb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4360,-853 4360,-820 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="48442@x" ObjectIDND1="42316@x" ObjectIDZND0="45004@1" Pin0InfoVect0LinkObjId="SW-312623_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-312624_0" Pin1InfoVect1LinkObjId="SW-256890_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4360,-853 4360,-820 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d23630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4425,-1019 4439,-1019 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="45006@0" ObjectIDZND0="g_2d22ba0@0" Pin0InfoVect0LinkObjId="g_2d22ba0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-312626_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4425,-1019 4439,-1019 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d312d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4873,-853 4887,-853 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="45013@0" ObjectIDZND0="g_2d30840@0" Pin0InfoVect0LinkObjId="g_2d30840_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-312660_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4873,-853 4887,-853 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d385a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4837,-853 4816,-853 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="45013@1" ObjectIDZND0="42309@x" ObjectIDZND1="45011@x" Pin0InfoVect0LinkObjId="SW-256774_0" Pin0InfoVect1LinkObjId="SW-312659_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-312660_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4837,-853 4816,-853 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d38800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4816,-874 4816,-853 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="42309@0" ObjectIDZND0="45013@x" ObjectIDZND1="45011@x" Pin0InfoVect0LinkObjId="SW-312660_0" Pin0InfoVect1LinkObjId="SW-312659_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-256774_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4816,-874 4816,-853 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d38a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4816,-853 4816,-820 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="45013@x" ObjectIDND1="42309@x" ObjectIDZND0="45011@1" Pin0InfoVect0LinkObjId="SW-312659_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-312660_0" Pin1InfoVect1LinkObjId="SW-256774_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4816,-853 4816,-820 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d3c1e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4875,-1019 4889,-1019 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="48443@0" ObjectIDZND0="g_2d3b750@0" Pin0InfoVect0LinkObjId="g_2d3b750_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-312662_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4875,-1019 4889,-1019 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d3f960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5270,-858 5284,-858 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="45003@0" ObjectIDZND0="g_2d3eed0@0" Pin0InfoVect0LinkObjId="g_2d3eed0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-312705_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5270,-858 5284,-858 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d96590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5234,-858 5213,-858 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="45003@1" ObjectIDZND0="42314@x" ObjectIDZND1="45001@x" Pin0InfoVect0LinkObjId="SW-256849_0" Pin0InfoVect1LinkObjId="SW-256851_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-312705_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5234,-858 5213,-858 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d967f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5213,-879 5213,-858 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="42314@0" ObjectIDZND0="45003@x" ObjectIDZND1="45001@x" Pin0InfoVect0LinkObjId="SW-312705_0" Pin0InfoVect1LinkObjId="SW-256851_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-256849_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5213,-879 5213,-858 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d96a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5213,-858 5213,-825 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="45003@x" ObjectIDND1="42314@x" ObjectIDZND0="45001@1" Pin0InfoVect0LinkObjId="SW-256851_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-312705_0" Pin1InfoVect1LinkObjId="SW-256849_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5213,-858 5213,-825 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d9a1d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5268,-1024 5282,-1024 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="48444@0" ObjectIDZND0="g_2d99740@0" Pin0InfoVect0LinkObjId="g_2d99740_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-312707_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5268,-1024 5282,-1024 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d9a430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3820,-898 3820,-949 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="42701@1" ObjectIDZND0="44999@0" Pin0InfoVect0LinkObjId="SW-312588_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-260488_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3820,-898 3820,-949 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d9a690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4173,-862 4148,-862 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="48477@1" ObjectIDZND0="48476@x" ObjectIDZND1="g_2e193e0@0" Pin0InfoVect0LinkObjId="SW-312931_0" Pin0InfoVect1LinkObjId="g_2e193e0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-312932_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4173,-862 4148,-862 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d9a8f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4148,-829 4148,-862 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="48476@1" ObjectIDZND0="48477@x" ObjectIDZND1="g_2e193e0@0" Pin0InfoVect0LinkObjId="SW-312932_0" Pin0InfoVect1LinkObjId="g_2e193e0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-312931_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4148,-829 4148,-862 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d9ab50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4148,-862 4148,-886 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="48477@x" ObjectIDND1="48476@x" ObjectIDZND0="g_2e193e0@0" Pin0InfoVect0LinkObjId="g_2e193e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-312932_0" Pin1InfoVect1LinkObjId="SW-312931_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4148,-862 4148,-886 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d9bb60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5213,-957 5213,-906 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="45002@0" ObjectIDZND0="42314@1" Pin0InfoVect0LinkObjId="SW-256849_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-312706_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5213,-957 5213,-906 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d9bdc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4360,-901 4360,-952 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="42316@1" ObjectIDZND0="45005@0" Pin0InfoVect0LinkObjId="SW-312625_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-256890_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4360,-901 4360,-952 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d9c020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4816,-901 4816,-952 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="42309@1" ObjectIDZND0="45012@0" Pin0InfoVect0LinkObjId="SW-312661_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-256774_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4816,-901 4816,-952 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2dd10d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5049,-344 5049,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="48493@1" ObjectIDZND0="48491@1" Pin0InfoVect0LinkObjId="SW-313062_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-313064_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5049,-344 5049,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2dd1330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5049,-286 5049,-303 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="48492@1" ObjectIDZND0="48491@0" Pin0InfoVect0LinkObjId="SW-313062_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-313064_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5049,-286 5049,-303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2dd5660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3820,-781 3820,-757 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="44998@0" ObjectIDZND0="42312@0" Pin0InfoVect0LinkObjId="g_2dd58c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-260490_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3820,-781 3820,-757 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2dd58c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4360,-784 4360,-757 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="45004@0" ObjectIDZND0="42312@0" Pin0InfoVect0LinkObjId="g_2dd5660_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-312623_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4360,-784 4360,-757 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2dd5b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4816,-784 4816,-757 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="45011@0" ObjectIDZND0="48447@0" Pin0InfoVect0LinkObjId="g_2dd5d80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-312659_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4816,-784 4816,-757 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2dd5d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5213,-789 5213,-757 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="45001@0" ObjectIDZND0="48447@0" Pin0InfoVect0LinkObjId="g_2dd5b20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-256851_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5213,-789 5213,-757 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2dd5fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4424,-737 4424,-757 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48483@1" ObjectIDZND0="42312@0" Pin0InfoVect0LinkObjId="g_2dd5660_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-312963_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4424,-737 4424,-757 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2dd6240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4589,-726 4589,-757 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48478@1" ObjectIDZND0="42312@0" Pin0InfoVect0LinkObjId="g_2dd5660_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-312936_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4589,-726 4589,-757 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2dd64a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4745,-728 4745,-757 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48480@1" ObjectIDZND0="48447@0" Pin0InfoVect0LinkObjId="g_2dd5b20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-312938_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4745,-728 4745,-757 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2dd6700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5401,-698 5423,-698 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="48474@1" ObjectIDZND0="48473@x" ObjectIDZND1="48447@0" Pin0InfoVect0LinkObjId="SW-312907_0" Pin0InfoVect1LinkObjId="g_2dd5b20_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-312908_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5401,-698 5423,-698 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2dd6960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5423,-656 5423,-698 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="48473@1" ObjectIDZND0="48474@x" ObjectIDZND1="48447@0" Pin0InfoVect0LinkObjId="SW-312908_0" Pin0InfoVect1LinkObjId="g_2dd5b20_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-312907_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5423,-656 5423,-698 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2dd6bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5423,-698 5423,-757 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="48474@x" ObjectIDND1="48473@x" ObjectIDZND0="48447@0" Pin0InfoVect0LinkObjId="g_2dd5b20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-312908_0" Pin1InfoVect1LinkObjId="SW-312907_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5423,-698 5423,-757 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2dd7720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5423,-559 5423,-533 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="48475@x" ObjectIDND1="48473@x" ObjectIDND2="g_2d29aa0@0" ObjectIDZND0="g_2dd6e20@1" Pin0InfoVect0LinkObjId="g_2dd6e20_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-312909_0" Pin1InfoVect1LinkObjId="SW-312907_0" Pin1InfoVect2LinkObjId="g_2d29aa0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5423,-559 5423,-533 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2dd7980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5423,-489 5423,-468 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_2dd6e20@0" ObjectIDZND0="g_305b720@0" Pin0InfoVect0LinkObjId="g_305b720_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2dd6e20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5423,-489 5423,-468 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d81240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3911,-592 3933,-592 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="48472@1" ObjectIDZND0="48470@x" ObjectIDZND1="g_2d81700@0" ObjectIDZND2="g_2d82b50@0" Pin0InfoVect0LinkObjId="SW-312898_0" Pin0InfoVect1LinkObjId="g_2d81700_0" Pin0InfoVect2LinkObjId="g_2d82b50_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-312900_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3911,-592 3933,-592 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d814a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3933,-621 3933,-592 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="48470@0" ObjectIDZND0="48472@x" ObjectIDZND1="g_2d81700@0" ObjectIDZND2="g_2d82b50@0" Pin0InfoVect0LinkObjId="SW-312900_0" Pin0InfoVect1LinkObjId="g_2d81700_0" Pin0InfoVect2LinkObjId="g_2d82b50_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-312898_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3933,-621 3933,-592 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d82430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3861,-699 3873,-699 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2d7aab0@0" ObjectIDZND0="48471@0" Pin0InfoVect0LinkObjId="SW-312899_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d7aab0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3861,-699 3873,-699 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d82690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3875,-592 3861,-592 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="48472@0" ObjectIDZND0="g_2d807b0@0" Pin0InfoVect0LinkObjId="g_2d807b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-312900_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3875,-592 3861,-592 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d828f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3909,-699 3933,-699 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="48471@1" ObjectIDZND0="48470@x" ObjectIDZND1="42312@0" Pin0InfoVect0LinkObjId="SW-312898_0" Pin0InfoVect1LinkObjId="g_2dd5660_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-312899_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3909,-699 3933,-699 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d83450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3933,-490 3933,-469 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_2d82b50@0" ObjectIDZND0="g_305e940@0" Pin0InfoVect0LinkObjId="g_305e940_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d82b50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3933,-490 3933,-469 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d836b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3933,-657 3933,-699 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="48470@1" ObjectIDZND0="48471@x" ObjectIDZND1="42312@0" Pin0InfoVect0LinkObjId="SW-312899_0" Pin0InfoVect1LinkObjId="g_2dd5660_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-312898_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3933,-657 3933,-699 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d83910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3933,-699 3933,-757 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="48471@x" ObjectIDND1="48470@x" ObjectIDZND0="42312@0" Pin0InfoVect0LinkObjId="g_2dd5660_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-312899_0" Pin1InfoVect1LinkObjId="SW-312898_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3933,-699 3933,-757 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f803d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4696,-380 4710,-380 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="48552@1" ObjectIDZND0="48551@1" Pin0InfoVect0LinkObjId="SW-313712_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-313713_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4696,-380 4710,-380 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f80630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4754,-380 4737,-380 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="48553@1" ObjectIDZND0="48551@0" Pin0InfoVect0LinkObjId="SW-313712_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-313713_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4754,-380 4737,-380 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f80890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4920,-225 4920,-249 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48549@0" ObjectIDZND0="48557@0" Pin0InfoVect0LinkObjId="g_2f80af0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-313705_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4920,-225 4920,-249 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f80af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5049,-269 5049,-249 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48492@0" ObjectIDZND0="48557@0" Pin0InfoVect0LinkObjId="g_2f80890_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-313064_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5049,-269 5049,-249 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f82bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5298,-412 5298,-380 5252,-380 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_2f80d50@0" ObjectIDZND0="48547@x" ObjectIDZND1="g_2f824c0@0" Pin0InfoVect0LinkObjId="SW-313678_0" Pin0InfoVect1LinkObjId="g_2f824c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f80d50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5298,-412 5298,-380 5252,-380 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f82e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5252,-383 5252,-361 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_2f80d50@0" ObjectIDND1="g_2f824c0@0" ObjectIDZND0="48547@0" Pin0InfoVect0LinkObjId="SW-313678_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2f80d50_0" Pin1InfoVect1LinkObjId="g_2f824c0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5252,-383 5252,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f83080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5252,-398 5252,-380 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2f824c0@1" ObjectIDZND0="g_2f80d50@0" ObjectIDZND1="48547@x" Pin0InfoVect0LinkObjId="g_2f80d50_0" Pin0InfoVect1LinkObjId="SW-313678_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f824c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5252,-398 5252,-380 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f832e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5252,-292 5252,-249 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48548@0" ObjectIDZND0="48557@0" Pin0InfoVect0LinkObjId="g_2f80890_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-313678_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5252,-292 5252,-249 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f83540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4422,-269 4422,-250 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48486@0" ObjectIDZND0="48556@0" Pin0InfoVect0LinkObjId="g_2d2b160_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-312967_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4422,-269 4422,-250 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f856d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5051,-737 5051,-757 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48489@1" ObjectIDZND0="48447@0" Pin0InfoVect0LinkObjId="g_2dd5b20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-313060_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5051,-737 5051,-757 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f889b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4148,-1045 4148,-1014 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_2f8d330@1" Pin0InfoVect0LinkObjId="g_2f8d330_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4148,-1045 4148,-1014 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f88ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4127,-947 4148,-947 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="g_2d9adb0@0" ObjectIDZND0="g_2e193e0@0" ObjectIDZND1="g_2f8d330@0" Pin0InfoVect0LinkObjId="g_2e193e0_0" Pin0InfoVect1LinkObjId="g_2f8d330_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d9adb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4127,-947 4148,-947 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f88d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4148,-930 4148,-947 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="g_2e193e0@1" ObjectIDZND0="g_2d9adb0@0" ObjectIDZND1="g_2f8d330@0" Pin0InfoVect0LinkObjId="g_2d9adb0_0" Pin0InfoVect1LinkObjId="g_2f8d330_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e193e0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4148,-930 4148,-947 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f88fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4148,-947 4148,-961 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2d9adb0@0" ObjectIDND1="g_2e193e0@0" ObjectIDZND0="g_2f8d330@0" Pin0InfoVect0LinkObjId="g_2f8d330_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2d9adb0_0" Pin1InfoVect1LinkObjId="g_2e193e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4148,-947 4148,-961 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f89e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3869,-1115 3869,-1099 3820,-1099 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_2dd7be0@0" ObjectIDZND0="g_2f891f0@0" Pin0InfoVect0LinkObjId="g_2f891f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2dd7be0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3869,-1115 3869,-1099 3820,-1099 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f8a0a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3820,-1099 3820,-1125 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="lightningRod" ObjectIDND0="g_2dd7be0@0" ObjectIDND1="g_2f891f0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2dd7be0_0" Pin1InfoVect1LinkObjId="g_2f891f0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3820,-1099 3820,-1125 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f8c320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4871,-1117 4871,-1100 4816,-1100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_2ddaa40@0" ObjectIDZND0="g_2f8b570@0" Pin0InfoVect0LinkObjId="g_2f8b570_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ddaa40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4871,-1117 4871,-1100 4816,-1100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f8dd50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3973,-531 3973,-553 3933,-553 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2d81700@0" ObjectIDZND0="48472@x" ObjectIDZND1="48470@x" ObjectIDZND2="g_2d82b50@0" Pin0InfoVect0LinkObjId="SW-312900_0" Pin0InfoVect1LinkObjId="SW-312898_0" Pin0InfoVect2LinkObjId="g_2d82b50_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d81700_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3973,-531 3973,-553 3933,-553 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f8dfb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3933,-592 3933,-553 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="48472@x" ObjectIDND1="48470@x" ObjectIDZND0="g_2d81700@0" ObjectIDZND1="g_2d82b50@0" Pin0InfoVect0LinkObjId="g_2d81700_0" Pin0InfoVect1LinkObjId="g_2d82b50_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-312900_0" Pin1InfoVect1LinkObjId="SW-312898_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3933,-592 3933,-553 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f8e210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3933,-553 3933,-534 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="48472@x" ObjectIDND1="48470@x" ObjectIDND2="g_2d81700@0" ObjectIDZND0="g_2d82b50@1" Pin0InfoVect0LinkObjId="g_2d82b50_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-312900_0" Pin1InfoVect1LinkObjId="SW-312898_0" Pin1InfoVect2LinkObjId="g_2d81700_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3933,-553 3933,-534 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f90660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4401,-637 4424,-637 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="g_2d9c280@0" ObjectIDZND0="g_2f8ee90@0" ObjectIDZND1="48482@x" Pin0InfoVect0LinkObjId="g_2f8ee90_0" Pin0InfoVect1LinkObjId="SW-312962_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d9c280_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4401,-637 4424,-637 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f908c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4424,-625 4424,-637 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="g_2f8ee90@1" ObjectIDZND0="g_2d9c280@0" ObjectIDZND1="48482@x" Pin0InfoVect0LinkObjId="g_2d9c280_0" Pin0InfoVect1LinkObjId="SW-312962_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f8ee90_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4424,-625 4424,-637 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f90b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4424,-637 4424,-653 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="breaker" ObjectIDND0="g_2d9c280@0" ObjectIDND1="g_2f8ee90@0" ObjectIDZND0="48482@0" Pin0InfoVect0LinkObjId="SW-312962_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2d9c280_0" Pin1InfoVect1LinkObjId="g_2f8ee90_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4424,-637 4424,-653 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f90d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4402,-557 4424,-557 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="transformer2" ObjectIDND0="g_2f8f8b0@0" ObjectIDZND0="g_2f8ee90@0" ObjectIDZND1="42325@x" Pin0InfoVect0LinkObjId="g_2f8ee90_0" Pin0InfoVect1LinkObjId="g_2f91240_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f8f8b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4402,-557 4424,-557 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f90fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4424,-572 4424,-557 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="transformer2" ObjectIDND0="g_2f8ee90@0" ObjectIDZND0="g_2f8f8b0@0" ObjectIDZND1="42325@x" Pin0InfoVect0LinkObjId="g_2f8f8b0_0" Pin0InfoVect1LinkObjId="g_2f91240_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f8ee90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4424,-572 4424,-557 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f91240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4424,-557 4424,-543 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_2f8f8b0@0" ObjectIDND1="g_2f8ee90@0" ObjectIDZND0="42325@1" Pin0InfoVect0LinkObjId="g_2f92c70_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2f8f8b0_0" Pin1InfoVect1LinkObjId="g_2f8ee90_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4424,-557 4424,-543 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f92c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4403,-449 4422,-449 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" EndDevType1="lightningRod" ObjectIDND0="g_2f91ec0@0" ObjectIDZND0="42325@x" ObjectIDZND1="g_2f914a0@0" Pin0InfoVect0LinkObjId="g_2f91240_0" Pin0InfoVect1LinkObjId="g_2f914a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f91ec0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4403,-449 4422,-449 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f92ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4422,-462 4422,-449 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="42325@0" ObjectIDZND0="g_2f91ec0@0" ObjectIDZND1="g_2f914a0@0" Pin0InfoVect0LinkObjId="g_2f91ec0_0" Pin0InfoVect1LinkObjId="g_2f914a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f91240_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4422,-462 4422,-449 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f93130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4422,-449 4422,-436 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" EndDevType0="lightningRod" ObjectIDND0="g_2f91ec0@0" ObjectIDND1="42325@x" ObjectIDZND0="g_2f914a0@1" Pin0InfoVect0LinkObjId="g_2f914a0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2f91ec0_0" Pin1InfoVect1LinkObjId="g_2f91240_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4422,-449 4422,-436 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f93390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4400,-372 4422,-372 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2d2db70@0" ObjectIDZND0="g_2f914a0@0" ObjectIDZND1="48487@x" Pin0InfoVect0LinkObjId="g_2f914a0_0" Pin0InfoVect1LinkObjId="SW-312967_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d2db70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4400,-372 4422,-372 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f935f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4422,-388 4422,-372 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2f914a0@0" ObjectIDZND0="g_2d2db70@0" ObjectIDZND1="48487@x" Pin0InfoVect0LinkObjId="g_2d2db70_0" Pin0InfoVect1LinkObjId="SW-312967_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f914a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4422,-388 4422,-372 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f93850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4422,-372 4422,-361 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_2d2db70@0" ObjectIDND1="g_2f914a0@0" ObjectIDZND0="48487@0" Pin0InfoVect0LinkObjId="SW-312967_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2d2db70_0" Pin1InfoVect1LinkObjId="g_2f914a0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4422,-372 4422,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f93ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4424,-524 4357,-524 4357,-516 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="42325@x" ObjectIDZND0="g_2d2d0f0@0" Pin0InfoVect0LinkObjId="g_2d2d0f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f91240_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4424,-524 4357,-524 4357,-516 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f93d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4539,-675 4528,-675 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="48479@0" ObjectIDZND0="g_2da18b0@0" Pin0InfoVect0LinkObjId="g_2da18b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-312937_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4539,-675 4528,-675 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_304b310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4764,-676 4745,-676 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="48481@1" ObjectIDZND0="48480@x" ObjectIDZND1="42318@x" Pin0InfoVect0LinkObjId="SW-312938_0" Pin0InfoVect1LinkObjId="SW-257114_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-312939_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4764,-676 4745,-676 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_304b570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4745,-692 4745,-676 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="48480@0" ObjectIDZND0="48481@x" ObjectIDZND1="42318@x" Pin0InfoVect0LinkObjId="SW-312939_0" Pin0InfoVect1LinkObjId="SW-257114_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-312938_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4745,-692 4745,-676 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_304b7d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4745,-676 4745,-665 4682,-665 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="48481@x" ObjectIDND1="48480@x" ObjectIDZND0="42318@0" Pin0InfoVect0LinkObjId="SW-257114_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-312939_0" Pin1InfoVect1LinkObjId="SW-312938_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4745,-676 4745,-665 4682,-665 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_304ba30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4575,-675 4589,-675 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="48479@1" ObjectIDZND0="48478@x" ObjectIDZND1="42318@x" Pin0InfoVect0LinkObjId="SW-312936_0" Pin0InfoVect1LinkObjId="SW-257114_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-312937_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4575,-675 4589,-675 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_304bc90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4589,-690 4589,-675 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="48478@0" ObjectIDZND0="48479@x" ObjectIDZND1="42318@x" Pin0InfoVect0LinkObjId="SW-312937_0" Pin0InfoVect1LinkObjId="SW-257114_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-312936_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4589,-690 4589,-675 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_304bef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4589,-675 4589,-665 4655,-665 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="48479@x" ObjectIDND1="48478@x" ObjectIDZND0="42318@1" Pin0InfoVect0LinkObjId="SW-257114_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-312937_0" Pin1InfoVect1LinkObjId="SW-312936_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4589,-675 4589,-665 4655,-665 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_304c150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4812,-676 4800,-676 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_304a8e0@0" ObjectIDZND0="48481@0" Pin0InfoVect0LinkObjId="SW-312939_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_304a8e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4812,-676 4800,-676 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_304db30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5032,-448 5049,-448 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" EndDevType1="lightningRod" ObjectIDND0="g_304cd80@0" ObjectIDZND0="42319@x" ObjectIDZND1="g_304c3b0@0" Pin0InfoVect0LinkObjId="g_3050560_0" Pin0InfoVect1LinkObjId="g_304c3b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_304cd80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5032,-448 5049,-448 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_304dd90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5049,-463 5049,-448 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="42319@0" ObjectIDZND0="g_304cd80@0" ObjectIDZND1="g_304c3b0@0" Pin0InfoVect0LinkObjId="g_304cd80_0" Pin0InfoVect1LinkObjId="g_304c3b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_304db30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5049,-463 5049,-448 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_304dff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5049,-448 5049,-436 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" EndDevType0="lightningRod" ObjectIDND0="g_304cd80@0" ObjectIDND1="42319@x" ObjectIDZND0="g_304c3b0@1" Pin0InfoVect0LinkObjId="g_304c3b0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_304cd80_0" Pin1InfoVect1LinkObjId="g_304db30_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5049,-448 5049,-436 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_304e250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5031,-371 5049,-371 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2dd3b00@0" ObjectIDZND0="g_304c3b0@0" ObjectIDZND1="48493@x" Pin0InfoVect0LinkObjId="g_304c3b0_0" Pin0InfoVect1LinkObjId="SW-313064_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2dd3b00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5031,-371 5049,-371 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_304e4b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5049,-383 5049,-371 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_304c3b0@0" ObjectIDZND0="g_2dd3b00@0" ObjectIDZND1="48493@x" Pin0InfoVect0LinkObjId="g_2dd3b00_0" Pin0InfoVect1LinkObjId="SW-313064_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_304c3b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5049,-383 5049,-371 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_304e710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5049,-371 5049,-360 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_2dd3b00@0" ObjectIDND1="g_304c3b0@0" ObjectIDZND0="48493@0" Pin0InfoVect0LinkObjId="SW-313064_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2dd3b00_0" Pin1InfoVect1LinkObjId="g_304c3b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5049,-371 5049,-360 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_304e970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5050,-525 4985,-525 4985,-518 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="42319@x" ObjectIDZND0="g_2dd2dd0@0" Pin0InfoVect0LinkObjId="g_2dd2dd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_304db30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5050,-525 4985,-525 4985,-518 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_304f980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5031,-637 5051,-637 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="lightningRod" ObjectIDND0="g_2dd48b0@0" ObjectIDZND0="48488@x" ObjectIDZND1="g_2f8e470@0" Pin0InfoVect0LinkObjId="SW-313059_0" Pin0InfoVect1LinkObjId="g_2f8e470_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2dd48b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5031,-637 5051,-637 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_304fbe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5051,-651 5051,-637 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="48488@0" ObjectIDZND0="g_2dd48b0@0" ObjectIDZND1="g_2f8e470@0" Pin0InfoVect0LinkObjId="g_2dd48b0_0" Pin0InfoVect1LinkObjId="g_2f8e470_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-313059_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5051,-651 5051,-637 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_304fe40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5051,-637 5051,-627 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="g_2dd48b0@0" ObjectIDND1="48488@x" ObjectIDZND0="g_2f8e470@1" Pin0InfoVect0LinkObjId="g_2f8e470_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2dd48b0_0" Pin1InfoVect1LinkObjId="SW-313059_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5051,-637 5051,-627 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30500a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5032,-560 5051,-560 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="transformer2" ObjectIDND0="g_304ebd0@0" ObjectIDZND0="g_2f8e470@0" ObjectIDZND1="42319@x" Pin0InfoVect0LinkObjId="g_2f8e470_0" Pin0InfoVect1LinkObjId="g_304db30_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_304ebd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5032,-560 5051,-560 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3050300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5051,-574 5051,-560 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="transformer2" ObjectIDND0="g_2f8e470@0" ObjectIDZND0="g_304ebd0@0" ObjectIDZND1="42319@x" Pin0InfoVect0LinkObjId="g_304ebd0_0" Pin0InfoVect1LinkObjId="g_304db30_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f8e470_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5051,-574 5051,-560 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3050560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5051,-560 5051,-544 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_304ebd0@0" ObjectIDND1="g_2f8e470@0" ObjectIDZND0="42319@1" Pin0InfoVect0LinkObjId="g_304db30_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_304ebd0_0" Pin1InfoVect1LinkObjId="g_2f8e470_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5051,-560 5051,-544 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3053ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5120,-690 5108,-690 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3053250@0" ObjectIDZND0="48490@0" Pin0InfoVect0LinkObjId="SW-313061_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3053250_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5120,-690 5108,-690 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30549d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4487,-691 4475,-691 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3053f40@0" ObjectIDZND0="48484@0" Pin0InfoVect0LinkObjId="SW-312964_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3053f40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4487,-691 4475,-691 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30576c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5072,-690 5051,-690 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="48490@1" ObjectIDZND0="48489@x" ObjectIDZND1="48488@x" Pin0InfoVect0LinkObjId="SW-313060_0" Pin0InfoVect1LinkObjId="SW-313059_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-313061_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5072,-690 5051,-690 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3057920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5051,-701 5051,-690 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="48489@0" ObjectIDZND0="48490@x" ObjectIDZND1="48488@x" Pin0InfoVect0LinkObjId="SW-313061_0" Pin0InfoVect1LinkObjId="SW-313059_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-313060_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5051,-701 5051,-690 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3057b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5051,-690 5051,-680 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="48490@x" ObjectIDND1="48489@x" ObjectIDZND0="48488@1" Pin0InfoVect0LinkObjId="SW-313059_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-313061_0" Pin1InfoVect1LinkObjId="SW-313060_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5051,-690 5051,-680 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3057de0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4439,-691 4424,-691 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="48484@1" ObjectIDZND0="48483@x" ObjectIDZND1="48482@x" Pin0InfoVect0LinkObjId="SW-312963_0" Pin0InfoVect1LinkObjId="SW-312962_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-312964_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4439,-691 4424,-691 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3058040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4424,-701 4424,-691 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="48483@0" ObjectIDZND0="48484@x" ObjectIDZND1="48482@x" Pin0InfoVect0LinkObjId="SW-312964_0" Pin0InfoVect1LinkObjId="SW-312962_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-312963_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4424,-701 4424,-691 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30582a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4424,-691 4424,-680 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="48484@x" ObjectIDND1="48483@x" ObjectIDZND0="48482@1" Pin0InfoVect0LinkObjId="SW-312962_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-312964_0" Pin1InfoVect1LinkObjId="SW-312963_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4424,-691 4424,-680 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3064d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4678,-380 4643,-380 4643,-250 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48552@0" ObjectIDZND0="48556@0" Pin0InfoVect0LinkObjId="g_2d2b160_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-313713_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4678,-380 4643,-380 4643,-250 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3064fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4771,-380 4795,-380 4795,-343 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="48553@0" ObjectIDZND0="48555@0" Pin0InfoVect0LinkObjId="SW-313715_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-313713_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4771,-380 4795,-380 4795,-343 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3065240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4795,-274 4795,-249 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48554@0" ObjectIDZND0="48557@0" Pin0InfoVect0LinkObjId="g_2f80890_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-313715_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4795,-274 4795,-249 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3069460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3685,-27 3685,-12 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_30654a0@0" ObjectIDZND0="48498@0" Pin0InfoVect0LinkObjId="SW-313160_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_30654a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3685,-27 3685,-12 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_306e310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3856,-182 3856,-206 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="48500@1" ObjectIDZND0="48501@1" Pin0InfoVect0LinkObjId="SW-313219_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-313218_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3856,-182 3856,-206 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_30764a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3856,-135 3856,-155 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="48502@1" ObjectIDZND0="48500@0" Pin0InfoVect0LinkObjId="SW-313218_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-313219_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3856,-135 3856,-155 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_307d7c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3856,-25 3856,-10 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_307a700@0" ObjectIDZND0="48504@0" Pin0InfoVect0LinkObjId="SW-313222_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_307a700_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3856,-25 3856,-10 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3082670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4014,-185 4014,-209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="48540@1" ObjectIDZND0="48541@1" Pin0InfoVect0LinkObjId="SW-313627_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-313626_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4014,-185 4014,-209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3087a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4014,-138 4014,-158 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="48542@1" ObjectIDZND0="48540@0" Pin0InfoVect0LinkObjId="SW-313626_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-313627_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4014,-138 4014,-158 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_308f480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4014,26 4014,-28 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3065ec0@0" ObjectIDZND0="g_308bc80@0" Pin0InfoVect0LinkObjId="g_308bc80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3065ec0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4014,26 4014,-28 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3093400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4014,42 3978,42 3978,73 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="48544@0" Pin0InfoVect0LinkObjId="SW-313630_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4014,42 3978,42 3978,73 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3093660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3978,109 3978,120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="48544@1" ObjectIDZND0="g_3091d80@0" Pin0InfoVect0LinkObjId="g_3091d80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-313630_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3978,109 3978,120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_30938c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3978,154 3978,165 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="earth" ObjectIDND0="g_3091d80@1" ObjectIDZND0="g_30929b0@0" Pin0InfoVect0LinkObjId="g_30929b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3091d80_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3978,154 3978,165 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3097750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4206,-26 4206,-10 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3096d30@0" ObjectIDZND0="48538@0" Pin0InfoVect0LinkObjId="SW-313591_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3096d30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4206,-26 4206,-10 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_30979b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4382,-183 4382,-207 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="48506@1" ObjectIDZND0="48507@1" Pin0InfoVect0LinkObjId="SW-313281_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-313280_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4382,-183 4382,-207 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_309fb40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4382,-136 4382,-156 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="48508@1" ObjectIDZND0="48506@0" Pin0InfoVect0LinkObjId="SW-313280_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-313281_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4382,-136 4382,-156 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_30a6e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4382,-26 4382,-11 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_30a3da0@0" ObjectIDZND0="48510@0" Pin0InfoVect0LinkObjId="SW-313284_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_30a3da0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4382,-26 4382,-11 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_30abd10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4564,-184 4564,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="48512@1" ObjectIDZND0="48513@1" Pin0InfoVect0LinkObjId="SW-313343_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-313342_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4564,-184 4564,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_30b3ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4564,-137 4564,-157 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="48514@1" ObjectIDZND0="48512@0" Pin0InfoVect0LinkObjId="SW-313342_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-313343_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4564,-137 4564,-157 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_30ba190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4920,-21 4920,23 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_30b9770@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_30b9770_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4920,-21 4920,23 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_30ba3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4884,-88 4884,-100 4920,-100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_2d85cd0@0" ObjectIDZND0="48550@x" ObjectIDZND1="g_30b9770@0" Pin0InfoVect0LinkObjId="SW-313705_0" Pin0InfoVect1LinkObjId="g_30b9770_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d85cd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4884,-88 4884,-100 4920,-100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_30ba650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4920,-122 4920,-100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="48550@0" ObjectIDZND0="g_2d85cd0@0" ObjectIDZND1="g_30b9770@0" Pin0InfoVect0LinkObjId="g_2d85cd0_0" Pin0InfoVect1LinkObjId="g_30b9770_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-313705_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4920,-122 4920,-100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_30ba8b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4920,-100 4920,-74 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2d85cd0@0" ObjectIDND1="48550@x" ObjectIDZND0="g_30b9770@1" Pin0InfoVect0LinkObjId="g_30b9770_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2d85cd0_0" Pin1InfoVect1LinkObjId="SW-313705_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4920,-100 4920,-74 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_30bab10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5106,-181 5106,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="48516@1" ObjectIDZND0="48517@1" Pin0InfoVect0LinkObjId="SW-313403_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-313402_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5106,-181 5106,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_30c2ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5106,-134 5106,-154 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="48518@1" ObjectIDZND0="48516@0" Pin0InfoVect0LinkObjId="SW-313402_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-313403_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5106,-134 5106,-154 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_30c9fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5106,-24 5106,-9 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_30c6f00@0" ObjectIDZND0="48520@0" Pin0InfoVect0LinkObjId="SW-313406_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_30c6f00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5106,-24 5106,-9 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_30cee70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5294,-182 5294,-206 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="48522@1" ObjectIDZND0="48523@1" Pin0InfoVect0LinkObjId="SW-313465_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-313464_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5294,-182 5294,-206 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_30d7000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5294,-135 5294,-155 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="48524@1" ObjectIDZND0="48522@0" Pin0InfoVect0LinkObjId="SW-313464_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-313465_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5294,-135 5294,-155 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_30de320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5294,-25 5294,-10 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_30db260@0" ObjectIDZND0="48526@0" Pin0InfoVect0LinkObjId="SW-313468_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_30db260_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5294,-25 5294,-10 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_30e31d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5467,-182 5467,-206 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="48528@1" ObjectIDZND0="48529@1" Pin0InfoVect0LinkObjId="SW-313526_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-313525_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5467,-182 5467,-206 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_30eb360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5467,-135 5467,-155 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="48530@1" ObjectIDZND0="48528@0" Pin0InfoVect0LinkObjId="SW-313525_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-313526_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5467,-135 5467,-155 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_30f2680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5467,-25 5467,-10 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_30ef5c0@0" ObjectIDZND0="48532@0" Pin0InfoVect0LinkObjId="SW-313529_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_30ef5c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5467,-25 5467,-10 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_30f7530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3856,-223 3856,-250 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48501@0" ObjectIDZND0="48556@0" Pin0InfoVect0LinkObjId="g_2d2b160_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-313219_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3856,-223 3856,-250 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_30f7790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4014,-226 4014,-250 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48541@0" ObjectIDZND0="48556@0" Pin0InfoVect0LinkObjId="g_2d2b160_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-313627_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4014,-226 4014,-250 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_30f79f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4206,-227 4206,-250 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48535@0" ObjectIDZND0="48556@0" Pin0InfoVect0LinkObjId="g_2d2b160_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-313588_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4206,-227 4206,-250 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_30f7c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4382,-224 4382,-250 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48507@0" ObjectIDZND0="48556@0" Pin0InfoVect0LinkObjId="g_2d2b160_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-313281_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4382,-224 4382,-250 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_30f7eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4564,-225 4564,-250 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48513@0" ObjectIDZND0="48556@0" Pin0InfoVect0LinkObjId="g_2d2b160_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-313343_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4564,-225 4564,-250 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_30f8110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5106,-222 5106,-249 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48517@0" ObjectIDZND0="48557@0" Pin0InfoVect0LinkObjId="g_2f80890_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-313403_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5106,-222 5106,-249 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_30f8370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5294,-223 5294,-249 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48523@0" ObjectIDZND0="48557@0" Pin0InfoVect0LinkObjId="g_2f80890_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-313465_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5294,-223 5294,-249 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_30f85d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5467,-223 5467,-249 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48529@0" ObjectIDZND0="48557@0" Pin0InfoVect0LinkObjId="g_2f80890_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-313526_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5467,-223 5467,-249 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30fc2f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4148,-793 4148,-757 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48476@0" ObjectIDZND0="42312@0" Pin0InfoVect0LinkObjId="g_2dd5660_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-312931_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4148,-793 4148,-757 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3105bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3751,-336 3751,-301 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="48546@1" ObjectIDZND0="48545@1" Pin0InfoVect0LinkObjId="SW-313666_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-313666_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3751,-336 3751,-301 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3106450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3751,-449 3751,-419 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_3061b60@0" ObjectIDZND0="g_2e9ba20@0" Pin0InfoVect0LinkObjId="g_2e9ba20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3061b60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3751,-449 3751,-419 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_342c710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5252,-344 5252,-309 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="48547@1" ObjectIDZND0="48548@1" Pin0InfoVect0LinkObjId="SW-313678_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-313678_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5252,-344 5252,-309 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_342cfa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5252,-457 5252,-422 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_3058500@0" ObjectIDZND0="g_2f824c0@0" Pin0InfoVect0LinkObjId="g_2f824c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3058500_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5252,-457 5252,-422 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_342f3c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4206,26 4206,46 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" EndDevType1="switch" ObjectIDND0="48538@1" ObjectIDZND0="48561@x" ObjectIDZND1="48539@x" Pin0InfoVect0LinkObjId="CB-LF_DX.LF_DX_Cb1_0" Pin0InfoVect1LinkObjId="SW-313592_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-313591_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4206,26 4206,46 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_342f5b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4206,46 4206,79 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="capacitor" ObjectIDND0="48538@x" ObjectIDND1="48539@x" ObjectIDZND0="48561@0" Pin0InfoVect0LinkObjId="CB-LF_DX.LF_DX_Cb1_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-313591_0" Pin1InfoVect1LinkObjId="SW-313592_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4206,46 4206,79 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3436320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4795,-326 4795,-291 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="48555@1" ObjectIDZND0="48554@1" Pin0InfoVect0LinkObjId="SW-313715_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-313715_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4795,-326 4795,-291 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3439510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3751,-284 3751,-250 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48545@0" ObjectIDZND0="48556@0" Pin0InfoVect0LinkObjId="g_2d2b160_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-313666_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3751,-284 3751,-250 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_343b5e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4206,46 4153,46 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" EndDevType0="switch" ObjectIDND0="48538@x" ObjectIDND1="48561@x" ObjectIDZND0="48539@1" Pin0InfoVect0LinkObjId="SW-313592_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-313591_0" Pin1InfoVect1LinkObjId="CB-LF_DX.LF_DX_Cb1_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4206,46 4153,46 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3442170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4564,-27 4564,130 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_30b8100@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_30b8100_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4564,-27 4564,130 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_345ca00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3685,-80 3685,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_30654a0@1" ObjectIDZND0="48496@0" Pin0InfoVect0LinkObjId="SW-313157_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_30654a0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3685,-80 3685,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_345cc60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3843,-1016 3820,-1016 3820,-985 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="48441@1" ObjectIDZND0="44999@1" Pin0InfoVect0LinkObjId="SW-312588_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-312589_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3843,-1016 3820,-1016 3820,-985 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_345cec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4390,-1019 4360,-1019 4360,-988 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="45006@1" ObjectIDZND0="45005@1" Pin0InfoVect0LinkObjId="SW-312625_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-312626_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4390,-1019 4360,-1019 4360,-988 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_345d120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4840,-1019 4816,-1019 4816,-988 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="48443@1" ObjectIDZND0="45012@1" Pin0InfoVect0LinkObjId="SW-312661_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-312662_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4840,-1019 4816,-1019 4816,-988 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_345d380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5233,-1024 5213,-1024 5213,-993 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="48444@1" ObjectIDZND0="45002@1" Pin0InfoVect0LinkObjId="SW-312706_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-312707_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5233,-1024 5213,-1024 5213,-993 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_345d5e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3649,-82 3649,-95 3723,-95 3723,-81 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2d844f0@0" ObjectIDZND0="48497@0" Pin0InfoVect0LinkObjId="SW-313159_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d844f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3649,-82 3649,-95 3723,-95 3723,-81 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_345d850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3800,-1016 3820,-1016 3820,-1099 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_2f891f0@0" ObjectIDZND0="g_2dd7be0@0" Pin0InfoVect0LinkObjId="g_2dd7be0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f891f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3800,-1016 3820,-1016 3820,-1099 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_345dd10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4788,-1019 4816,-1019 4816,-1100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_2f8b570@0" ObjectIDZND0="g_2ddaa40@0" Pin0InfoVect0LinkObjId="g_2ddaa40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f8b570_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4788,-1019 4816,-1019 4816,-1100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_345df70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3651,54 3651,44 3685,44 3685,24 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_30696c0@0" ObjectIDZND0="48498@1" Pin0InfoVect0LinkObjId="SW-313160_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_30696c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3651,54 3651,44 3685,44 3685,24 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_345e1e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3685,130 3685,44 3725,44 3725,55 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="48499@0" Pin0InfoVect0LinkObjId="SW-313161_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3685,130 3685,44 3725,44 3725,55 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_345e450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3856,-78 3856,-120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_307a700@1" ObjectIDZND0="48502@0" Pin0InfoVect0LinkObjId="SW-313219_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_307a700_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3856,-78 3856,-120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_345e6b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3820,-80 3820,-93 3894,-93 3894,-79 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_30799d0@0" ObjectIDZND0="48503@0" Pin0InfoVect0LinkObjId="SW-313221_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_30799d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3820,-80 3820,-93 3894,-93 3894,-79 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_345e920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3822,56 3822,46 3856,46 3856,26 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_307da20@0" ObjectIDZND0="48504@1" Pin0InfoVect0LinkObjId="SW-313222_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_307da20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3822,56 3822,46 3856,46 3856,26 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_345eb90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3856,132 3856,46 3896,46 3896,57 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="48505@0" Pin0InfoVect0LinkObjId="SW-313223_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3856,132 3856,46 3896,46 3896,57 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_345ee00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4014,-81 4014,-123 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_308bc80@1" ObjectIDZND0="48542@0" Pin0InfoVect0LinkObjId="SW-313627_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_308bc80_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4014,-81 4014,-123 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_345f060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3978,-83 3978,-96 4052,-96 4052,-82 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_308af50@0" ObjectIDZND0="48543@0" Pin0InfoVect0LinkObjId="SW-313629_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_308af50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3978,-83 3978,-96 4052,-96 4052,-82 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_345f2d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4174,-82 4174,-96 4206,-96 4206,-124 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2d84fd0@0" ObjectIDZND0="48536@0" Pin0InfoVect0LinkObjId="SW-313588_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d84fd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4174,-82 4174,-96 4206,-96 4206,-124 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_345f540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4247,-82 4247,-96 4206,-96 4206,-79 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="48537@0" ObjectIDZND0="g_3096d30@1" Pin0InfoVect0LinkObjId="g_3096d30_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-313590_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4247,-82 4247,-96 4206,-96 4206,-79 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_345f7b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4382,-79 4382,-121 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_30a3da0@1" ObjectIDZND0="48508@0" Pin0InfoVect0LinkObjId="SW-313281_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_30a3da0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4382,-79 4382,-121 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_345fa10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4346,-81 4346,-94 4420,-94 4420,-80 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_30a3070@0" ObjectIDZND0="48509@0" Pin0InfoVect0LinkObjId="SW-313283_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_30a3070_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4346,-81 4346,-94 4420,-94 4420,-80 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_345fc80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4348,55 4348,45 4382,45 4382,25 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_30a70c0@0" ObjectIDZND0="48510@1" Pin0InfoVect0LinkObjId="SW-313284_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_30a70c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4348,55 4348,45 4382,45 4382,25 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_345fef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4382,131 4382,45 4422,45 4422,56 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="48511@0" Pin0InfoVect0LinkObjId="SW-313285_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4382,131 4382,45 4422,45 4422,56 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3460160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4564,-80 4564,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_30b8100@1" ObjectIDZND0="48514@0" Pin0InfoVect0LinkObjId="SW-313343_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_30b8100_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4564,-80 4564,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34603c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4528,-82 4528,-95 4602,-95 4602,-81 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_30b73d0@0" ObjectIDZND0="48515@0" Pin0InfoVect0LinkObjId="SW-313345_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_30b73d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4528,-82 4528,-95 4602,-95 4602,-81 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3460630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5106,-77 5106,-119 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_30c6f00@1" ObjectIDZND0="48518@0" Pin0InfoVect0LinkObjId="SW-313403_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_30c6f00_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5106,-77 5106,-119 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3460890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5070,-79 5070,-92 5144,-92 5144,-78 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_30c61d0@0" ObjectIDZND0="48519@0" Pin0InfoVect0LinkObjId="SW-313405_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_30c61d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5070,-79 5070,-92 5144,-92 5144,-78 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3460b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5072,57 5072,47 5106,47 5106,27 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_30ca220@0" ObjectIDZND0="48520@1" Pin0InfoVect0LinkObjId="SW-313406_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_30ca220_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5072,57 5072,47 5106,47 5106,27 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3460d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5106,133 5106,47 5146,47 5146,58 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="48521@0" Pin0InfoVect0LinkObjId="SW-313407_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5106,133 5106,47 5146,47 5146,58 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3460fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5294,-78 5294,-120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_30db260@1" ObjectIDZND0="48524@0" Pin0InfoVect0LinkObjId="SW-313465_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_30db260_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5294,-78 5294,-120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3461240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5258,-80 5258,-93 5332,-93 5332,-79 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_30da530@0" ObjectIDZND0="48525@0" Pin0InfoVect0LinkObjId="SW-313467_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_30da530_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5258,-80 5258,-93 5332,-93 5332,-79 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34614b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5260,56 5260,46 5294,46 5294,26 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_30de580@0" ObjectIDZND0="48526@1" Pin0InfoVect0LinkObjId="SW-313468_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_30de580_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5260,56 5260,46 5294,46 5294,26 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3461720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5294,114 5294,46 5334,46 5334,57 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="48527@0" Pin0InfoVect0LinkObjId="SW-313469_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5294,114 5294,46 5334,46 5334,57 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3461990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5467,-78 5467,-120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_30ef5c0@1" ObjectIDZND0="48530@0" Pin0InfoVect0LinkObjId="SW-313526_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_30ef5c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5467,-78 5467,-120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3461bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5431,-80 5431,-93 5505,-93 5505,-79 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_30ee890@0" ObjectIDZND0="48531@0" Pin0InfoVect0LinkObjId="SW-313528_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_30ee890_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5431,-80 5431,-93 5505,-93 5505,-79 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3461e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5433,56 5433,46 5467,46 5467,26 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_30f28e0@0" ObjectIDZND0="48532@1" Pin0InfoVect0LinkObjId="SW-313529_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_30f28e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5433,56 5433,46 5467,46 5467,26 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34620d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5467,110 5467,49 5507,49 5507,57 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="48533@0" Pin0InfoVect0LinkObjId="SW-313530_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5467,110 5467,49 5507,49 5507,57 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3464bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5919,-743 5757,-743 5757,140 5467,140 5467,150 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_2e2f8e0@0" ObjectIDND1="33837@x" ObjectIDND2="46478@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2e2f8e0_0" Pin1InfoVect1LinkObjId="SW-218139_0" Pin1InfoVect2LinkObjId="SW-300343_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5919,-743 5757,-743 5757,140 5467,140 5467,150 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34656f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5919,-715 5919,-743 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="load" ObjectIDND0="g_2e2f8e0@0" ObjectIDZND0="33837@x" ObjectIDZND1="46478@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-218139_0" Pin0InfoVect1LinkObjId="SW-300343_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e2f8e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5919,-715 5919,-743 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3465950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5919,-743 5947,-743 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2e2f8e0@0" ObjectIDND1="0@x" ObjectIDZND0="33837@x" ObjectIDZND1="46478@x" Pin0InfoVect0LinkObjId="SW-218139_0" Pin0InfoVect1LinkObjId="SW-300343_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2e2f8e0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5919,-743 5947,-743 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3465bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5980,-603 5839,-603 5839,240 5356,240 5356,132 5294,132 5294,150 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_2e313e0@0" ObjectIDND1="33833@x" ObjectIDND2="46479@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2e313e0_0" Pin1InfoVect1LinkObjId="SW-218123_0" Pin1InfoVect2LinkObjId="SW-300342_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5980,-603 5839,-603 5839,240 5356,240 5356,132 5294,132 5294,150 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34666c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5980,-575 5980,-603 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2e313e0@0" ObjectIDZND0="0@x" ObjectIDZND1="33833@x" ObjectIDZND2="46479@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-218123_0" Pin0InfoVect2LinkObjId="SW-300342_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e313e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5980,-575 5980,-603 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3466920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5980,-603 6001,-603 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="g_2e313e0@0" ObjectIDZND0="33833@x" ObjectIDZND1="46479@x" Pin0InfoVect0LinkObjId="SW-218123_0" Pin0InfoVect1LinkObjId="SW-300342_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_2e313e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5980,-603 6001,-603 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3468390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4360,-1251 4815,-1251 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_346b600@0" ObjectIDZND0="g_346adf0@0" ObjectIDZND1="33812@x" Pin0InfoVect0LinkObjId="g_346adf0_0" Pin0InfoVect1LinkObjId="SW-218005_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_346b600_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4360,-1251 4815,-1251 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3468e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4360,-1402 4360,-1251 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_346b600@0" ObjectIDZND0="g_346adf0@0" ObjectIDZND1="33812@x" Pin0InfoVect0LinkObjId="g_346adf0_0" Pin0InfoVect1LinkObjId="SW-218005_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_346b600_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4360,-1402 4360,-1251 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34690e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4360,-1251 4360,-1170 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" ObjectIDND0="g_346adf0@0" ObjectIDND1="33812@x" ObjectIDND2="g_346b600@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_346adf0_0" Pin1InfoVect1LinkObjId="SW-218005_0" Pin1InfoVect2LinkObjId="g_346b600_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4360,-1251 4360,-1170 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3469bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4816,-1251 4816,-1169 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" ObjectIDND0="g_346b600@0" ObjectIDND1="g_346adf0@0" ObjectIDND2="33812@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_346b600_0" Pin1InfoVect1LinkObjId="g_346adf0_0" Pin1InfoVect2LinkObjId="SW-218005_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4816,-1251 4816,-1169 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3469e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6419,109 6470,109 6470,-1342 4816,-1342 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="33812@1" ObjectIDZND0="g_346b600@0" ObjectIDZND1="g_346adf0@0" Pin0InfoVect0LinkObjId="g_346b600_0" Pin0InfoVect1LinkObjId="g_346adf0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218005_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="6419,109 6470,109 6470,-1342 4816,-1342 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_346a930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4816,-1396 4816,-1342 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_346adf0@0" ObjectIDZND0="g_346b600@0" ObjectIDZND1="33812@x" Pin0InfoVect0LinkObjId="g_346b600_0" Pin0InfoVect1LinkObjId="SW-218005_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_346adf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4816,-1396 4816,-1342 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_346ab90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4816,-1342 4816,-1251 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_346adf0@0" ObjectIDND1="33812@x" ObjectIDZND0="g_346b600@0" Pin0InfoVect0LinkObjId="g_346b600_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_346adf0_0" Pin1InfoVect1LinkObjId="SW-218005_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4816,-1342 4816,-1251 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_346ceb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3820,-1490 3820,-1411 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_346be10@0" Pin0InfoVect0LinkObjId="g_346be10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3820,-1490 3820,-1411 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_346d110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3820,-1411 3820,-1178 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_346be10@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_346be10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3820,-1411 3820,-1178 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_346d370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4816,-1474 4816,-1420 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_346adf0@1" Pin0InfoVect0LinkObjId="g_346adf0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4816,-1474 4816,-1420 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_346d5d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4359,-1480 4359,-1426 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_346b600@1" Pin0InfoVect0LinkObjId="g_346b600_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4359,-1480 4359,-1426 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_346d830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5213,-1112 5213,-1334 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2ddc170@0" ObjectIDND1="g_2f8c580@0" ObjectIDZND0="g_346be10@0" Pin0InfoVect0LinkObjId="g_346be10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2ddc170_0" Pin1InfoVect1LinkObjId="g_2f8c580_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5213,-1112 5213,-1334 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_346e320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5270,-1124 5270,-1112 5213,-1112 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="g_2ddc170@0" ObjectIDZND0="g_346be10@0" ObjectIDZND1="g_2f8c580@0" Pin0InfoVect0LinkObjId="g_346be10_0" Pin0InfoVect1LinkObjId="g_2f8c580_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ddc170_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5270,-1124 5270,-1112 5213,-1112 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_346e580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5213,-1112 5213,-1024 5184,-1024 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_346be10@0" ObjectIDND1="g_2ddc170@0" ObjectIDZND0="g_2f8c580@0" Pin0InfoVect0LinkObjId="g_2f8c580_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_346be10_0" Pin1InfoVect1LinkObjId="g_2ddc170_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5213,-1112 5213,-1024 5184,-1024 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_346e7e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5214,-1465 5214,-1411 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_346be10@0" Pin0InfoVect0LinkObjId="g_346be10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5214,-1465 5214,-1411 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_346f2d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3823,-1411 5211,-1411 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_346be10@0" Pin0InfoVect0LinkObjId="g_346be10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3823,-1411 5211,-1411 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_346f530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5211,-1411 5214,-1411 5214,-1358 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_346be10@1" Pin0InfoVect0LinkObjId="g_346be10_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5211,-1411 5214,-1411 5214,-1358 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c3c050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4816,-1100 4816,-1125 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="lightningRod" ObjectIDND0="g_2ddaa40@0" ObjectIDND1="g_2f8b570@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2ddaa40_0" Pin1InfoVect1LinkObjId="g_2f8b570_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4816,-1100 4816,-1125 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c98580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4329,-1019 4360,-1019 4360,-1098 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="voltageTransformer" ObjectIDND0="g_2f8a7c0@0" ObjectIDZND0="g_3c8cec0@0" ObjectIDZND1="g_2dd9310@0" Pin0InfoVect0LinkObjId="g_3c8cec0_0" Pin0InfoVect1LinkObjId="g_2dd9310_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f8a7c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4329,-1019 4360,-1019 4360,-1098 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_450daf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4360,-1098 4360,-1105 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_2f8a7c0@0" ObjectIDND1="g_2dd9310@0" ObjectIDZND0="g_3c8cec0@0" Pin0InfoVect0LinkObjId="g_3c8cec0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2f8a7c0_0" Pin1InfoVect1LinkObjId="g_2dd9310_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4360,-1098 4360,-1105 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_450dce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4360,-1137 4360,-1146 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_3c8cec0@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3c8cec0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4360,-1137 4360,-1146 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cffc90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4412,-1114 4412,-1098 4360,-1098 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="g_2dd9310@0" ObjectIDZND0="g_2f8a7c0@0" ObjectIDZND1="g_3c8cec0@0" Pin0InfoVect0LinkObjId="g_2f8a7c0_0" Pin0InfoVect1LinkObjId="g_3c8cec0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2dd9310_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4412,-1114 4412,-1098 4360,-1098 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="48556" cx="3685" cy="-250" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="42312" cx="3820" cy="-757" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="42312" cx="4360" cy="-757" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48447" cx="4816" cy="-757" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48447" cx="5213" cy="-757" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="42312" cx="4424" cy="-757" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="42312" cx="4589" cy="-757" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48447" cx="4745" cy="-757" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48557" cx="4920" cy="-249" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48557" cx="5049" cy="-249" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48557" cx="5252" cy="-249" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48556" cx="4422" cy="-250" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48447" cx="5051" cy="-757" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48556" cx="4643" cy="-250" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48557" cx="4795" cy="-249" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48556" cx="3856" cy="-250" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48556" cx="4014" cy="-250" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48556" cx="4206" cy="-250" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48556" cx="4382" cy="-250" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48556" cx="4564" cy="-250" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48557" cx="5106" cy="-249" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48557" cx="5294" cy="-249" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48557" cx="5467" cy="-249" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="42312" cx="4148" cy="-757" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48556" cx="3751" cy="-250" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48447" cx="5423" cy="-757" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="42312" cx="3933" cy="-757" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="33809" cx="6353" cy="-872" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="33809" cx="6353" cy="-743" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="33809" cx="6353" cy="-474" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="33809" cx="6353" cy="-346" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="33809" cx="6353" cy="-235" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="33809" cx="6353" cy="-120" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="33809" cx="6353" cy="-603" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-217898" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3427.500000 -1092.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33797" ObjectName="DYN-WD_MJYD"/>
     <cge:Meas_Ref ObjectId="217898"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2f2fe90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2f2fe90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2f2fe90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2f2fe90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2f2fe90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2f2fe90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2f2fe90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c722f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c722f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c722f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c722f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c722f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c722f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c722f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c722f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c722f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c722f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c722f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c722f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c722f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c722f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c722f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c722f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c722f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c722f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_2eff510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3283.000000 -1166.500000) translate(0,16)">甸心移动变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2f08390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3263.000000 -232.000000) translate(0,16)">15758580348</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_2b7bc40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3438.000000 -1165.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_2e4d3f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3438.000000 -1200.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2cf1a90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3136.000000 -195.000000) translate(0,17)">楚雄巡维中</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2cf1a90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3136.000000 -195.000000) translate(0,38)">心变运二班：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2f2b3e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3263.000000 -205.500000) translate(0,16)">13508785260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2f2b3e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3263.000000 -205.500000) translate(0,36)">18787879001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2f2b3e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3263.000000 -205.500000) translate(0,56)">18787879002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f02aa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3574.000000 -1183.000000) translate(0,12)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e80af0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5945.000000 -122.000000) translate(0,15)">10kV转供电环网</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e80af0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5945.000000 -122.000000) translate(0,33)">柜母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_2ec0c20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6078.000000 -916.000000) translate(0,18)">10kV4号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ebbd00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6009.000000 -313.000000) translate(0,15)">转供电琅井线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ebc690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5980.000000 -446.000000) translate(0,15)">转供电安乐线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ebcf10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5818.000000 -625.000000) translate(0,15)">转供电羊毛岭线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ebdab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5779.000000 -765.000000) translate(0,15)">转供电妥安线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ebe010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6239.000000 -850.000000) translate(0,15)">0904</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2eb76b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5963.000000 -728.000000) translate(0,15)">0356</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2eb8310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6022.000000 -595.000000) translate(0,15)">0346</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2eb8770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6033.000000 -466.000000) translate(0,15)">0336</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2eb89b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6027.000000 -336.000000) translate(0,15)">0326</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2eb8e40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6233.000000 -769.000000) translate(0,15)">035</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2eb9110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6079.000000 -802.000000) translate(0,15)">03560</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2eb9350" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6100.000000 -665.000000) translate(0,15)">03460</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2eb9590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6233.000000 -627.000000) translate(0,15)">034</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2eb97d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6232.000000 -499.000000) translate(0,15)">033</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2eb9a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6119.000000 -536.000000) translate(0,15)">03360</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2eb9c50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6228.000000 -374.000000) translate(0,15)">032</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2eb9e90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6108.000000 -412.000000) translate(0,15)">03260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2eba0d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6268.000000 -101.000000) translate(0,15)">0903</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d903f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6316.000000 85.000000) translate(0,12)">303</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d90c40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6209.000000 56.000000) translate(0,12)">3号移动变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e4ef20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6233.000000 -258.000000) translate(0,12)">003</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e4f350" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6388.000000 83.000000) translate(0,12)">3036</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e4f640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6375.000000 156.000000) translate(0,12)">30360</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e4fa20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6358.000000 -946.000000) translate(0,12)">IM段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2d43870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5901.000000 -800.000000) translate(0,17)">03567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2d44ce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5956.000000 -658.000000) translate(0,17)">03467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2d45360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5938.000000 -528.000000) translate(0,17)">03367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2d455e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5951.000000 -397.000000) translate(0,17)">03267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e3bc50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3556.000000 -230.000000) translate(0,12)">10kVI段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e942c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3731.096525 -522.000000) translate(0,12)">10kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e942c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3731.096525 -522.000000) translate(0,27)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e89db0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4494.000000 -524.000000) translate(0,12)">档位:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dee220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3981.000000 202.000000) translate(0,12)">接地变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2deee50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4865.000000 202.000000) translate(0,12)">10kV2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2def3c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4155.000000 202.000000) translate(0,12)">1号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2dbefe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5370.000000 -417.000000) translate(0,15)">35kVⅡ段母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d2cc00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4472.500000 -487.000000) translate(0,12)">油温(℃):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2d83b70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3849.000000 -408.000000) translate(0,15)">35kⅠ段母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f81b00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5224.096525 -537.000000) translate(0,12)">10kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f81b00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5224.096525 -537.000000) translate(0,27)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_2f837a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4104.000000 -1173.000000) translate(0,13)">35kV1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f858c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4208.000000 -532.000000) translate(0,12)">SZ20-5000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f858c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4208.000000 -532.000000) translate(0,27)">35±3×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f858c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4208.000000 -532.000000) translate(0,42)">YN,d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f858c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4208.000000 -532.000000) translate(0,57)">7.60%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f88430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5075.000000 -456.000000) translate(0,12)">SZ20-5000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f88430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5075.000000 -456.000000) translate(0,27)">35±3×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f88430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5075.000000 -456.000000) translate(0,42)">YN,d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f88430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5075.000000 -456.000000) translate(0,57)">7.58%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30f8830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3684.000000 -749.000000) translate(0,12)">35kVI段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30f8e60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3830.000000 -892.000000) translate(0,12)">351</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30f90a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3827.000000 -806.000000) translate(0,12)">3511</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30f92e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3827.000000 -974.000000) translate(0,12)">3516</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30f9520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3854.000000 -876.000000) translate(0,12)">35117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30f9760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3843.000000 -1042.000000) translate(0,12)">35167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30f99a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4370.000000 -895.000000) translate(0,12)">352</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30f9be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4367.000000 -809.000000) translate(0,12)">3521</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30f9e20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4367.000000 -977.000000) translate(0,12)">3526</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30fa060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4406.000000 -880.000000) translate(0,12)">35217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30fa2a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4388.000000 -1045.000000) translate(0,12)">35267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30fa4e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4826.000000 -895.000000) translate(0,12)">361</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30fa720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4823.000000 -977.000000) translate(0,12)">3616</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30fa960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4823.000000 -809.000000) translate(0,12)">3612</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30faba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4850.000000 -882.000000) translate(0,12)">36127</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30fade0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4838.000000 -1045.000000) translate(0,12)">36167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30fb020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5223.000000 -900.000000) translate(0,12)">362</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30fb260" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5220.000000 -982.000000) translate(0,12)">3626</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30fb4a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5220.000000 -814.000000) translate(0,12)">3622</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30fb6e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5254.000000 -885.000000) translate(0,12)">36227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30fb920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5230.000000 -1050.000000) translate(0,12)">36267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30fbb60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5575.000000 -739.000000) translate(0,12)">35kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30fc4e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4155.000000 -818.000000) translate(0,12)">3541</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30fce40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4171.000000 -888.000000) translate(0,12)">35410</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30fd0c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3871.000000 -725.000000) translate(0,12)">39010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30fd300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3873.000000 -618.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30fd540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3940.000000 -646.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30fd780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4596.000000 -715.000000) translate(0,12)">3121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30fd9c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4537.000000 -701.000000) translate(0,12)">31210</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30fdc00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4752.000000 -717.000000) translate(0,12)">3122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30fde40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4776.000000 -700.000000) translate(0,12)">31220</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30fe080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5430.000000 -645.000000) translate(0,12)">3902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30fe2c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5362.000000 -724.000000) translate(0,12)">39020</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30fe500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5364.000000 -617.000000) translate(0,12)">39027</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30fe740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4380.000000 -726.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30fe980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4434.000000 -674.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30febc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4444.000000 -716.000000) translate(0,12)">30117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30fee00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4431.000000 -324.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30ff040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4220.000000 -557.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30ff570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5060.000000 -672.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30ff7f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5005.000000 -724.000000) translate(0,12)">3022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30ffa30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5070.000000 -716.000000) translate(0,12)">30227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30ffc70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5058.000000 -324.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30ffeb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5083.000000 -479.000000) translate(0,12)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3105e20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3761.000000 -323.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_342c970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5266.000000 -333.000000) translate(0,12)">0902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_342d190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3695.000000 -178.000000) translate(0,12)">071</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_342d440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3733.000000 -65.000000) translate(0,12)">07160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_342d680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3692.000000 -1.000000) translate(0,12)">0716</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_342d8c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3735.000000 71.000000) translate(0,12)">07167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_342db00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3866.000000 -176.000000) translate(0,12)">072</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_342dd40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3904.000000 -63.000000) translate(0,12)">07260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_342df80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3863.000000 1.000000) translate(0,12)">0726</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_342e1c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3906.000000 73.000000) translate(0,12)">07267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_342e400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4024.000000 -179.000000) translate(0,12)">073</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_342e640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4061.000000 -66.000000) translate(0,12)">07360</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_342e880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3985.000000 84.000000) translate(0,12)">0010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_342eac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4216.000000 -180.000000) translate(0,12)">074</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_342ed00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4256.000000 -66.000000) translate(0,12)">07460</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_342ef40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4213.000000 1.000000) translate(0,12)">0746</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_342f180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4115.000000 20.000000) translate(0,12)">07467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_342f7a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4392.000000 -177.000000) translate(0,12)">075</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_342fac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4429.000000 -64.000000) translate(0,12)">07560</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_342fd00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4431.000000 72.000000) translate(0,12)">07567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_342ff40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4389.000000 -0.000000) translate(0,12)">0756</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3430180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4574.000000 -178.000000) translate(0,12)">076</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34303c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4611.000000 -65.000000) translate(0,12)">07660</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3430600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4711.000000 -404.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3436580" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4809.000000 -314.000000) translate(0,12)">0122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34372d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4936.000000 -181.000000) translate(0,12)">0816</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34377c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5116.000000 -175.000000) translate(0,12)">082</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3437a00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5153.000000 -62.000000) translate(0,12)">08260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3437c40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5113.000000 2.000000) translate(0,12)">0826</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3437e80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5155.000000 74.000000) translate(0,12)">08267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34380c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5304.000000 -176.000000) translate(0,12)">083</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3438300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5301.000000 1.000000) translate(0,12)">0836</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3438540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5341.000000 -63.000000) translate(0,12)">08360</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3438780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5343.000000 73.000000) translate(0,12)">08367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34389c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5477.000000 -176.000000) translate(0,12)">084</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3438c00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5474.000000 1.000000) translate(0,12)">0846</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3438e40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5514.000000 -63.000000) translate(0,12)">08460</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3439080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5516.000000 73.000000) translate(0,12)">08467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34392c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5514.000000 -234.000000) translate(0,12)">10kVII段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3439700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4656.000000 -689.000000) translate(0,12)">312</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34399a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4509.000000 195.000000) translate(0,12)">10kV备用二</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_343a330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3848.000000 -1211.000000) translate(0,13)">35kV辛甸线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_343ae30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4839.000000 -1207.000000) translate(0,13)">35kV甸心线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_343ba10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5280.000000 -1110.000000) translate(0,13)">35kV甸黑阿线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_343c3f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4259.000000 -1171.000000) translate(0,13)">35kV高峰线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_343cc60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3637.000000 198.000000) translate(0,12)">10kV琅井线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_343d520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3812.000000 199.000000) translate(0,12)">10kV安乐线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_343dd90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4337.000000 201.000000) translate(0,12)">10kV备用一</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_343e1a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5059.000000 205.000000) translate(0,12)">10kV城区线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_343eba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5239.000000 206.000000) translate(0,12)">10kV羊毛岭线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_343f740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5434.000000 203.000000) translate(0,12)">10kV妥安线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_3470420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5510.000000 -1377.000000) translate(0,13)">35kV甸心线至35kV移动变临供电缆</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_3471fb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6073.000000 -1013.000000) translate(0,20)">预装式配电车</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3473640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4460.000000 -1279.000000) translate(0,15)">35kV甸心线至35kV高峰线临供电缆</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_3474e30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4691.000000 -1132.000000) translate(0,13)">拆除35kV甸心线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_3474e30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4691.000000 -1132.000000) translate(0,29)">N51号塔至站内</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_3474e30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4691.000000 -1132.000000) translate(0,45)">构架架空线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_3476a90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4225.000000 -1121.000000) translate(0,13)">拆除35kV高峰线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_3476a90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4225.000000 -1121.000000) translate(0,29)">N1号塔至站内</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_3476a90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4225.000000 -1121.000000) translate(0,45)">构架架空线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_3476d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3694.000000 -1140.000000) translate(0,13)">拆除35kV辛甸线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_3476d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3694.000000 -1140.000000) translate(0,29)">N23号塔至站内</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_3476d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3694.000000 -1140.000000) translate(0,45)">构架架空线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3477280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4458.000000 -1450.000000) translate(0,15)">35kV辛甸线至35kV甸黑阿线临供电缆</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_3478920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6198.000000 -30.000000) translate(0,20)">预装式移动变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35c7dc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5264.000000 -360.000000) translate(0,12)">0902</text>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-218137">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6223.000000 -733.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33834" ObjectName="SW-LF_DXYD.LF_DXYD_035BK"/>
     <cge:Meas_Ref ObjectId="218137"/>
    <cge:TPSR_Ref TObjectID="33834"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-218121">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6222.000000 -593.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33830" ObjectName="SW-LF_DXYD.LF_DXYD_034BK"/>
     <cge:Meas_Ref ObjectId="218121"/>
    <cge:TPSR_Ref TObjectID="33830"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-218105">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6220.000000 -464.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33826" ObjectName="SW-LF_DXYD.LF_DXYD_033BK"/>
     <cge:Meas_Ref ObjectId="218105"/>
    <cge:TPSR_Ref TObjectID="33826"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-218088">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6219.000000 -336.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33821" ObjectName="SW-LF_DXYD.LF_DXYD_032BK"/>
     <cge:Meas_Ref ObjectId="218088"/>
    <cge:TPSR_Ref TObjectID="33821"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-218019">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6223.000000 -225.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33815" ObjectName="SW-LF_DXYD.LF_DXYD_003BK"/>
     <cge:Meas_Ref ObjectId="218019"/>
    <cge:TPSR_Ref TObjectID="33815"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-218003">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6306.000000 119.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33811" ObjectName="SW-LF_DXYD.LF_DXYD_303BK"/>
     <cge:Meas_Ref ObjectId="218003"/>
    <cge:TPSR_Ref TObjectID="33811"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-312962">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4415.241796 -644.716049)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48482" ObjectName="SW-LF_DX.LF_DX_301BK"/>
     <cge:Meas_Ref ObjectId="312962"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-260488">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3811.241796 -862.716049)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42701" ObjectName="SW-LF_DX.LF_DX_351BK"/>
     <cge:Meas_Ref ObjectId="260488"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-312965">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4413.000000 -295.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48485" ObjectName="SW-LF_DX.LF_DX_001BK"/>
     <cge:Meas_Ref ObjectId="312965"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-313156">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3676.371429 -149.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48494" ObjectName="SW-LF_DX.LF_DX_071BK"/>
     <cge:Meas_Ref ObjectId="313156"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-313587">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4197.371429 -151.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48534" ObjectName="SW-LF_DX.LF_DX_074BK"/>
     <cge:Meas_Ref ObjectId="313587"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-256890">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4351.241796 -865.716049)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42316" ObjectName="SW-LF_DX.LF_DX_352BK"/>
     <cge:Meas_Ref ObjectId="256890"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-256774">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4807.241796 -865.716049)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42309" ObjectName="SW-LF_DX.LF_DX_361BK"/>
     <cge:Meas_Ref ObjectId="256774"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-256849">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5204.241796 -870.716049)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42314" ObjectName="SW-LF_DX.LF_DX_362BK"/>
     <cge:Meas_Ref ObjectId="256849"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-257114">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4646.000000 -655.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42318" ObjectName="SW-LF_DX.LF_DX_312BK"/>
     <cge:Meas_Ref ObjectId="257114"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-313059">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5041.541796 -644.716049)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48488" ObjectName="SW-LF_DX.LF_DX_302BK"/>
     <cge:Meas_Ref ObjectId="313059"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-313062">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5040.000000 -295.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48491" ObjectName="SW-LF_DX.LF_DX_002BK"/>
     <cge:Meas_Ref ObjectId="313062"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-313712">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4745.500000 -370.500000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48551" ObjectName="SW-LF_DX.LF_DX_012BK"/>
     <cge:Meas_Ref ObjectId="313712"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-313218">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3847.371429 -147.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48500" ObjectName="SW-LF_DX.LF_DX_072BK"/>
     <cge:Meas_Ref ObjectId="313218"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-313626">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4005.371429 -150.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48540" ObjectName="SW-LF_DX.LF_DX_073BK"/>
     <cge:Meas_Ref ObjectId="313626"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-313280">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4373.371429 -148.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48506" ObjectName="SW-LF_DX.LF_DX_075BK"/>
     <cge:Meas_Ref ObjectId="313280"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-313342">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4555.371429 -149.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48512" ObjectName="SW-LF_DX.LF_DX_076BK"/>
     <cge:Meas_Ref ObjectId="313342"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-313402">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5097.371429 -146.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48516" ObjectName="SW-LF_DX.LF_DX_082BK"/>
     <cge:Meas_Ref ObjectId="313402"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-313464">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5285.371429 -147.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48522" ObjectName="SW-LF_DX.LF_DX_083BK"/>
     <cge:Meas_Ref ObjectId="313464"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-313525">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5458.371429 -147.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48528" ObjectName="SW-LF_DX.LF_DX_084BK"/>
     <cge:Meas_Ref ObjectId="313525"/>
    </metadata>
   </g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-LF_DX.LF_DX_Cb1">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4177.000000 179.000000)" xlink:href="#capacitor:shape36"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48561" ObjectName="CB-LF_DX.LF_DX_Cb1"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2dedb50">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6232.000000 -863.000000)" xlink:href="#lightningRod:shape132"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ee0d50">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 6092.500000 -860.500000)" xlink:href="#lightningRod:shape100"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2edf170">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6093.000000 -828.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e67a90">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6046.000000 -709.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f0b0c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6073.000000 -568.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e41070">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6088.000000 -441.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f26ae0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6072.000000 -315.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e73570">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6050.000000 -200.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2eb45e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6163.000000 -111.000000)" xlink:href="#lightningRod:shape132"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2eb5020">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6187.000000 -79.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e7fc80">
    <use class="BV-10KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 6057.500000 -241.500000)" xlink:href="#lightningRod:shape175"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e80040">
    <use class="BV-10KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 6135.500000 102.500000)" xlink:href="#lightningRod:shape175"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e2f8e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5862.000000 -708.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e313e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5923.000000 -568.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2dc58c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5912.000000 -441.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e0ffa0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5921.000000 -316.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e3af90">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3790.000000 -399.659574)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e9ba20">
    <use class="BV-10KV" transform="matrix(0.777778 -0.000000 0.000000 -0.904762 3744.000000 -385.659574)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d29aa0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5456.371429 -476.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d2d0f0">
    <use class="BV-35KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4349.676442 -462.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d2db70">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4405.000000 -365.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e193e0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4141.000000 -881.000000)" xlink:href="#lightningRod:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d9adb0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4132.000000 -940.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d9c280">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4406.000000 -630.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2dd2dd0">
    <use class="BV-35KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4978.376442 -464.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2dd3b00">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5036.000000 -364.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2dd48b0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5036.000000 -630.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2dd6e20">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5416.000000 -484.000000)" xlink:href="#lightningRod:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d81700">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3966.371429 -477.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d82b50">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3926.000000 -485.000000)" xlink:href="#lightningRod:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d844f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3642.371429 -28.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d84fd0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4167.371429 -28.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d85cd0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4877.371429 -34.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f80d50">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5291.000000 -407.659574)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f824c0">
    <use class="BV-10KV" transform="matrix(0.777778 -0.000000 0.000000 -0.904762 5245.000000 -388.659574)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f891f0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3805.000000 -1009.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f8a7c0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4334.000000 -1012.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f8b570">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4793.000000 -1012.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f8c580">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5189.000000 -1017.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f8d330">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4143.000000 -956.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f8e470">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5046.000000 -569.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f8ee90">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4419.000000 -567.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f8f8b0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4407.000000 -550.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f914a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4417.000000 -383.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f91ec0">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4408.000000 -442.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_304c3b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5044.000000 -378.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_304cd80">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5037.000000 -441.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_304ebd0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5037.000000 -553.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_30654a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3680.000000 -22.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3065ec0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3997.000000 59.000000)" xlink:href="#lightningRod:shape10"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_30696c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3644.371429 108.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_30799d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3813.371429 -26.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_307a700">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3851.000000 -20.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_307da20">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3815.371429 110.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_308af50">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3971.371429 -29.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_308bc80">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4009.000000 -23.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3091d80">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3971.000000 159.000000)" xlink:href="#lightningRod:shape174"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3096d30">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4201.000000 -21.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_30a3070">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4339.371429 -27.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_30a3da0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4377.000000 -21.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_30a70c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4341.371429 109.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_30b73d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4521.371429 -28.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_30b8100">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4559.000000 -22.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_30b9770">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4915.000000 -16.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_30c61d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5063.371429 -25.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_30c6f00">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5101.000000 -19.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_30ca220">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5065.371429 111.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_30da530">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5251.371429 -26.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_30db260">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5289.000000 -20.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_30de580">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5253.371429 110.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_30ee890">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5424.371429 -26.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_30ef5c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5462.000000 -20.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_30f28e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5426.371429 110.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3436bb0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4911.000000 -154.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_346adf0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4804.000000 -1391.000000)" xlink:href="#lightningRod:shape182"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_346b600">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4347.000000 -1397.000000)" xlink:href="#lightningRod:shape182"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_346be10">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5201.000000 -1329.000000)" xlink:href="#lightningRod:shape182"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3c8cec0">
    <use class="BV-35KV" transform="matrix(0.700000 -0.000000 0.000000 -0.836735 4353.000000 -1101.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-217931" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6365.000000 -867.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217931" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="33809"/>
     <cge:Term_Ref ObjectID="49552"/>
    <cge:TPSR_Ref TObjectID="33809"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-217932" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6365.000000 -867.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217932" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="33809"/>
     <cge:Term_Ref ObjectID="49552"/>
    <cge:TPSR_Ref TObjectID="33809"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-217933" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6365.000000 -867.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217933" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="33809"/>
     <cge:Term_Ref ObjectID="49552"/>
    <cge:TPSR_Ref TObjectID="33809"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-217937" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6365.000000 -867.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217937" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="33809"/>
     <cge:Term_Ref ObjectID="49552"/>
    <cge:TPSR_Ref TObjectID="33809"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-217934" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6365.000000 -867.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217934" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="33809"/>
     <cge:Term_Ref ObjectID="49552"/>
    <cge:TPSR_Ref TObjectID="33809"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-217975" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6289.000000 -701.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217975" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="33834"/>
     <cge:Term_Ref ObjectID="49601"/>
    <cge:TPSR_Ref TObjectID="33834"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-217976" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6289.000000 -701.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217976" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="33834"/>
     <cge:Term_Ref ObjectID="49601"/>
    <cge:TPSR_Ref TObjectID="33834"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-217968" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6289.000000 -701.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217968" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="33834"/>
     <cge:Term_Ref ObjectID="49601"/>
    <cge:TPSR_Ref TObjectID="33834"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-217918" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6361.000000 30.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217918" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="33811"/>
     <cge:Term_Ref ObjectID="49555"/>
    <cge:TPSR_Ref TObjectID="33811"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-217919" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6361.000000 30.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217919" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="33811"/>
     <cge:Term_Ref ObjectID="49555"/>
    <cge:TPSR_Ref TObjectID="33811"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-217911" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6361.000000 30.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217911" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="33811"/>
     <cge:Term_Ref ObjectID="49555"/>
    <cge:TPSR_Ref TObjectID="33811"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-217928" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6306.000000 -205.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217928" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="33815"/>
     <cge:Term_Ref ObjectID="49563"/>
    <cge:TPSR_Ref TObjectID="33815"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-217929" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6306.000000 -205.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217929" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="33815"/>
     <cge:Term_Ref ObjectID="49563"/>
    <cge:TPSR_Ref TObjectID="33815"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-217921" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6306.000000 -205.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217921" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="33815"/>
     <cge:Term_Ref ObjectID="49563"/>
    <cge:TPSR_Ref TObjectID="33815"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-217945" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6302.000000 -315.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217945" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="33821"/>
     <cge:Term_Ref ObjectID="49575"/>
    <cge:TPSR_Ref TObjectID="33821"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-217946" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6302.000000 -315.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217946" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="33821"/>
     <cge:Term_Ref ObjectID="49575"/>
    <cge:TPSR_Ref TObjectID="33821"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-217938" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6302.000000 -315.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217938" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="33821"/>
     <cge:Term_Ref ObjectID="49575"/>
    <cge:TPSR_Ref TObjectID="33821"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-217965" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6298.000000 -562.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217965" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="33830"/>
     <cge:Term_Ref ObjectID="49593"/>
    <cge:TPSR_Ref TObjectID="33830"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-217966" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6298.000000 -562.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217966" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="33830"/>
     <cge:Term_Ref ObjectID="49593"/>
    <cge:TPSR_Ref TObjectID="33830"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-217958" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6298.000000 -562.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217958" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="33830"/>
     <cge:Term_Ref ObjectID="49593"/>
    <cge:TPSR_Ref TObjectID="33830"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-217955" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6304.000000 -435.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217955" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="33826"/>
     <cge:Term_Ref ObjectID="49585"/>
    <cge:TPSR_Ref TObjectID="33826"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-217956" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6304.000000 -435.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217956" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="33826"/>
     <cge:Term_Ref ObjectID="49585"/>
    <cge:TPSR_Ref TObjectID="33826"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-217948" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6304.000000 -435.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217948" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="33826"/>
     <cge:Term_Ref ObjectID="49585"/>
    <cge:TPSR_Ref TObjectID="33826"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="0" MeasureType="" PreSymbol="0" appendix="" decimal="1" id="ME-0" prefix="OT4  " rightAlign="0">
    <text fill="rgb(190,190,190)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4549.000000 -511.000000) translate(0,12)">OT4   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="0"/>
     <cge:Term_Ref ObjectID="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="0" MeasureType="" PreSymbol="0" appendix="" decimal="1" id="ME-0" prefix="OT4  " rightAlign="0">
    <text fill="rgb(190,190,190)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5092.000000 -527.000000) translate(0,12)">OT4   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="0"/>
     <cge:Term_Ref ObjectID="0"/>
    </metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="3248" y="-1177"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3199" y="-1194"/></g>
   <g href="cx_配调_配网接线图35_禄丰.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3427" y="-1173"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3427" y="-1208"/></g>
   <g href="35kV甸心移动变10kV转供Ⅰ回(琅井线)032间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="30" x="6228" y="-374"/></g>
   <g href="35kV甸心移动变10kV转供Ⅱ回(安乐线)033间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="30" x="6232" y="-499"/></g>
   <g href="35kV甸心移动变10kV转供Ⅲ回(羊毛岭线)034间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="31" x="6233" y="-627"/></g>
   <g href="35kV甸心移动变10kV转供Ⅳ回(妥安线)035间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="30" x="6233" y="-769"/></g>
   <g href="35kV甸心移动变3号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="68" x="6209" y="56"/></g>
   <g href="35kV甸心移动变GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="65" x="3570" y="-1185"/></g>
   <g href="35kV甸心变35kV辛甸线351间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="3830" y="-892"/></g>
   <g href="35kV甸心变35kV高峰线352间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4370" y="-895"/></g>
   <g href="35kV甸心变35kV甸心线361间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4826" y="-895"/></g>
   <g href="35kV甸心变35kV甸黑阿线362间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="5223" y="-900"/></g>
   <g href="35kV甸心变35kV分段312间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="4656" y="-689"/></g>
   <g href="35kV甸心变10kV琅井线071间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="3695" y="-178"/></g>
   <g href="35kV甸心变10kV安乐线072间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="3866" y="-176"/></g>
   <g href="35kV甸心变10kV接地变073间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4024" y="-179"/></g>
   <g href="35kV甸心变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="52" x="4220" y="-557"/></g>
   <g href="35kV甸心变10kV1号电容器组074间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4216" y="-180"/></g>
   <g href="35kV甸心变10kV备用一线075间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4392" y="-177"/></g>
   <g href="35kV甸心变10kV备用二线076间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4574" y="-178"/></g>
   <g href="35kV甸心变10kV城区线082间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="5116" y="-175"/></g>
   <g href="35kV甸心变10kV羊毛岭线083间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="5304" y="-176"/></g>
   <g href="35kV甸心变10kV妥安线084间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="5477" y="-176"/></g>
   <g href="35kV甸心变10kV分段012间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="4711" y="-404"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e50f30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6223.000000 702.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e51df0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6212.000000 687.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e7c0b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6237.000000 672.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e7c700" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6232.000000 564.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e7c9a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6221.000000 549.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e7cbe0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6246.000000 534.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e7cf10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6238.000000 436.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e7d170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6227.000000 421.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e7d3b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6252.000000 406.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e7d6e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6236.000000 319.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e7d940" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6225.000000 304.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e7db80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6250.000000 289.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e7deb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6240.000000 208.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e7e110" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6229.000000 193.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e7e350" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6254.000000 178.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e7e680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6305.000000 -29.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e8b570" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6294.000000 -44.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e8b7b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6319.000000 -59.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e8da80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6221.500000 -175.000000) translate(0,12)">绕温(℃):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e8e670" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6251.000000 -140.000000) translate(0,12)">档位:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e5c200" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6221.500000 -157.000000) translate(0,12)">油温(℃):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="5475" x2="5460" y1="-102" y2="-117"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="5460" x2="5472" y1="-102" y2="-118"/>
   <metadata/><line fill="none" opacity="0" stroke="white" stroke-width="10" transform="" x1="5475" x2="5460" y1="-102" y2="-117"/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="5302" x2="5287" y1="-105" y2="-120"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="5287" x2="5299" y1="-105" y2="-121"/>
   <metadata/><line fill="none" opacity="0" stroke="white" stroke-width="10" transform="" x1="5302" x2="5287" y1="-105" y2="-120"/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="4368" x2="4353" y1="1157" y2="1142"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="4353" x2="4365" y1="1157" y2="1141"/>
   <metadata/><line fill="none" opacity="0" stroke="white" stroke-width="10" transform="" x1="4368" x2="4353" y1="1157" y2="1142"/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="3827" x2="3812" y1="1133" y2="1118"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="3812" x2="3824" y1="1133" y2="1117"/>
   <metadata/><line fill="none" opacity="0" stroke="white" stroke-width="10" transform="" x1="3827" x2="3812" y1="1133" y2="1118"/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="4824" x2="4809" y1="1138" y2="1123"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="4809" x2="4821" y1="1138" y2="1122"/>
   <metadata/><line fill="none" opacity="0" stroke="white" stroke-width="10" transform="" x1="4824" x2="4809" y1="1138" y2="1123"/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-LF_DXYD.LF_DXYD_3T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="49611"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 6203.500000 77.500000)" xlink:href="#transformer2:shape4_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 6203.500000 77.500000)" xlink:href="#transformer2:shape4_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="33838" ObjectName="TF-LF_DXYD.LF_DXYD_3T"/>
    <cge:TPSR_Ref TObjectID="33838"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4889.000000 116.340426)" xlink:href="#transformer2:shape38_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4889.000000 116.340426)" xlink:href="#transformer2:shape38_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-LF_DX.LF_DX_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="17889"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4386.418267 -457.340426)" xlink:href="#transformer2:shape14_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4386.418267 -457.340426)" xlink:href="#transformer2:shape14_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="42325" ObjectName="TF-LF_DX.LF_DX_1T"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4133.000000 -1040.000000)" xlink:href="#transformer2:shape24_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4133.000000 -1040.000000)" xlink:href="#transformer2:shape24_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-LF_DX.LF_DX_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="17873"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5013.418267 -458.340426)" xlink:href="#transformer2:shape14_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5013.418267 -458.340426)" xlink:href="#transformer2:shape14_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="42319" ObjectName="TF-LF_DX.LF_DX_2T"/>
    </metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="3248" y="-1177"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="3248" y="-1177"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3199" y="-1194"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3199" y="-1194"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3427" y="-1173"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3427" y="-1173"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3427" y="-1208"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3427" y="-1208"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="30" x="6228" y="-374"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="30" x="6228" y="-374"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="30" x="6232" y="-499"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="30" x="6232" y="-499"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="31" x="6233" y="-627"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="31" x="6233" y="-627"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="30" x="6233" y="-769"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="30" x="6233" y="-769"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="68" x="6209" y="56"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="68" x="6209" y="56"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="65" x="3570" y="-1185"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="65" x="3570" y="-1185"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="3830" y="-892"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="3830" y="-892"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4370" y="-895"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4370" y="-895"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4826" y="-895"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4826" y="-895"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="5223" y="-900"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="5223" y="-900"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="4656" y="-689"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="4656" y="-689"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="3695" y="-178"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="3695" y="-178"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="3866" y="-176"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="3866" y="-176"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4024" y="-179"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4024" y="-179"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="52" x="4220" y="-557"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="52" x="4220" y="-557"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4216" y="-180"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4216" y="-180"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4392" y="-177"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4392" y="-177"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4574" y="-178"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4574" y="-178"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="5116" y="-175"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="5116" y="-175"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="5304" y="-176"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="5304" y="-176"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="5477" y="-176"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="5477" y="-176"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="4711" y="-404"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="4711" y="-404"/></g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2eb6ae0">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 6112.000000 -101.000000)" xlink:href="#voltageTransformer:shape79"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2dd7be0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3861.000000 -1110.000000)" xlink:href="#voltageTransformer:shape64"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2dd9310">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4404.000000 -1109.000000)" xlink:href="#voltageTransformer:shape64"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ddaa40">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4863.000000 -1112.000000)" xlink:href="#voltageTransformer:shape64"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ddc170">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5262.000000 -1119.000000)" xlink:href="#voltageTransformer:shape64"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3058500">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5219.000000 -452.000000)" xlink:href="#voltageTransformer:shape146"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_305b720">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5390.000000 -473.000000)" xlink:href="#voltageTransformer:shape146"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_305e940">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3900.000000 -474.000000)" xlink:href="#voltageTransformer:shape146"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3061b60">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3718.000000 -444.000000)" xlink:href="#voltageTransformer:shape146"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3220.000000 -1119.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-263419" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3256.000000 -987.000000) translate(0,21)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="263419" ObjectName="LF_DX:LF_DX_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-0" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3256.000000 -943.000000) translate(0,21)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="0" id="ME-217978" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6296.000000 141.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217978" ObjectName="LF_DXYD:LF_DXYD_3T_TP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-217979" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6296.000000 157.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217979" ObjectName="LF_DXYD:LF_DXYD_3T_Tmp"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-217980" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6296.000000 175.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217980" ObjectName="LF_DXYD:LF_DXYD_3T_Tmp1"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="LF_DXYD"/>
</svg>