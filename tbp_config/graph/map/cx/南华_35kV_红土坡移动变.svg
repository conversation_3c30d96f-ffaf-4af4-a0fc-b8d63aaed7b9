<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-271" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="-750 -1377 1644 1267">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="capacitor:shape4">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.484375" x1="31" x2="0" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.484375" x1="31" x2="0" y1="19" y2="19"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape21">
    <rect height="26" stroke-width="1.99997" width="11" x="2" y="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="7" x2="7" y1="50" y2="5"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="14" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="5" x2="8" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="3" x2="10" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="12" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="63" x2="63" y1="6" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="61" x2="61" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="58" x2="58" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="58" x2="49" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape157">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,36 0,46 10,46 5,36 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,24 0,14 10,14 5,24 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="59" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="8" x2="8" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape185">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="48" x2="45" y1="55" y2="53"/>
    <ellipse cx="34" cy="41" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="15" x2="15" y1="9" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="21" y1="9" y2="9"/>
    <g class="BV-0KV" stationId="-1">
     <rect height="13" stroke-width="1" width="6" x="12" y="18"/>
    </g>
    <polyline points="27,29 11,21 3,21 1,21 " stroke-width="1"/>
    <polyline points="38,47 37,49 15,49 15,31 " stroke-width="1"/>
    <ellipse cx="45" cy="41" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="45" x2="43" y1="44" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="46" x2="48" y1="44" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="48" x2="43" y1="40" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="34" x2="34" y1="41" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="31" x2="34" y1="43" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="37" x2="34" y1="43" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="18" x2="12" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="16" x2="14" y1="2" y2="2"/>
    <ellipse cx="34" cy="52" rx="7.5" ry="7" stroke-width="0.726474"/>
    <ellipse cx="45" cy="52" rx="7.5" ry="7" stroke-width="0.726474"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="32" x2="34" y1="55" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="35" x2="35" y1="53" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="37" x2="34" y1="55" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="46" x2="46" y1="53" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="43" x2="45" y1="55" y2="53"/>
   </symbol>
   <symbol id="lightningRod:shape132">
    <rect height="16" stroke-width="1" width="31" x="5" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="36" y1="9" y2="9"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <rect height="14" stroke-width="1" width="27" x="18" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="5" x2="5" y1="11" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="9" x2="18" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="8" x2="8" y1="2" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="58" x2="23" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape99">
    <polyline DF8003:Layer="PUBLIC" points="25,29 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="8" y1="46" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="41" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="41" y1="46" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="42" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="46" y2="46"/>
    <circle cx="25" cy="29" fillStyle="0" r="24" stroke-width="0.5"/>
   </symbol>
   <symbol id="load:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
   </symbol>
   <symbol id="load:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="6" y2="15"/>
    <polyline DF8003:Layer="PUBLIC" points="1,15 10,15 5,25 0,15 1,15 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="15" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="15" y2="25"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape8_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="18" y2="43"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="18" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
   </symbol>
   <symbol id="switch2:shape8_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="44" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
   </symbol>
   <symbol id="switch2:shape8-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="18" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="18" y2="43"/>
   </symbol>
   <symbol id="switch2:shape8-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="44" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape5_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="38" x2="13" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="38" x2="47" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="5" x2="14" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="14" y2="0"/>
   </symbol>
   <symbol id="switch2:shape5_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
   </symbol>
   <symbol id="switch2:shape5-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape5-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
   </symbol>
   <symbol id="transformer2:shape14_0">
    <circle cx="37" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="66" x2="71" y1="84" y2="84"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="70" x2="68" y1="84" y2="79"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="71" x2="71" y1="81" y2="81"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.911765" x1="1" x2="69" y1="45" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="37" x2="28" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="37" x2="45" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="28" x2="45" y1="18" y2="18"/>
   </symbol>
   <symbol id="transformer2:shape14_1">
    <ellipse cx="37" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="29" x2="37" y1="75" y2="67"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="37" x2="45" y1="67" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="37" x2="37" y1="59" y2="67"/>
   </symbol>
   <symbol id="transformer2:shape92_0">
    <ellipse cx="12" cy="16" rx="11" ry="12.5" stroke-width="1.22172"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.33311" x1="12" x2="7" y1="19" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.33311" x1="7" x2="17" y1="13" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.33311" x1="12" x2="17" y1="19" y2="13"/>
   </symbol>
   <symbol id="transformer2:shape92_1">
    <ellipse cx="12" cy="36" rx="11" ry="12" stroke-width="1.22172"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.3331" x1="16" x2="12" y1="40" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.33311" x1="12" x2="12" y1="36" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.3331" x1="8" x2="12" y1="40" y2="36"/>
   </symbol>
   <symbol id="voltageTransformer:shape142">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="29,66 29,1 56,1 56,18 " stroke-width="1.27273"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.893744" x1="7" x2="7" y1="68" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.893744" x1="11" x2="11" y1="73" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.637723" x1="2" x2="2" y1="67" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.50199" x1="46" x2="50" y1="31" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.20776" x1="59" x2="59" y1="48" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.26262" x1="50" x2="60" y1="43" y2="48"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.25643" x1="50" x2="59" y1="43" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.50193" x1="61" x2="65" y1="26" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.50199" x1="65" x2="69" y1="31" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.50193" x1="61" x2="65" y1="36" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.50193" x1="52" x2="56" y1="13" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.50199" x1="56" x2="61" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.50193" x1="52" x2="56" y1="23" y2="18"/>
    <ellipse cx="52" cy="44" fillStyle="0" rx="12.5" ry="10.5" stroke-width="0.716858"/>
    <ellipse cx="53" cy="18" fillStyle="0" rx="12.5" ry="10" stroke-width="0.716858"/>
    <ellipse cx="44" cy="32" fillStyle="0" rx="12.5" ry="10" stroke-width="0.716858"/>
    <ellipse cx="64" cy="31" fillStyle="0" rx="12.5" ry="10" stroke-width="0.716858"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.50193" x1="42" x2="46" y1="36" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.50193" x1="42" x2="46" y1="26" y2="31"/>
    <polyline points="65,32 65,66 12,66 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3924f00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_39258a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3926240" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3926ee0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3928140" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3928d60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_39297c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_392a240" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_392ab10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_392b3c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_392b3c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_392cdb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_392cdb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_392dda0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_392fa30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3930680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3931560" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3931e40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3933600" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3933e00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_39344f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3934f10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_39360f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3936a70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3937560" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3937f20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3939410" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3939f70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_393b1e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_393be70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_394a670" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_393d520" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_393e1e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_393f700" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1277" width="1654" x="-755" y="-1382"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,0)" stroke-width="1" x1="885" x2="894" y1="-666" y2="-666"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-218018">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 10.126582 -701.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33814" ObjectName="SW-NH_HTPYD.NH_HTPYD_001XC1"/>
     <cge:Meas_Ref ObjectId="218018"/>
    <cge:TPSR_Ref TObjectID="33814"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-218018">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 10.126582 -642.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33813" ObjectName="SW-NH_HTPYD.NH_HTPYD_001XC"/>
     <cge:Meas_Ref ObjectId="218018"/>
    <cge:TPSR_Ref TObjectID="33813"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-218089">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -233.873418 -453.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33823" ObjectName="SW-NH_HTPYD.NH_HTPYD_012XC1"/>
     <cge:Meas_Ref ObjectId="218089"/>
    <cge:TPSR_Ref TObjectID="33823"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-218089">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -233.873418 -512.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33822" ObjectName="SW-NH_HTPYD.NH_HTPYD_012XC"/>
     <cge:Meas_Ref ObjectId="218089"/>
    <cge:TPSR_Ref TObjectID="33822"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-218090">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -190.000000 -349.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33824" ObjectName="SW-NH_HTPYD.NH_HTPYD_01267SW"/>
     <cge:Meas_Ref ObjectId="218090"/>
    <cge:TPSR_Ref TObjectID="33824"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-218106">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -12.873418 -453.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33828" ObjectName="SW-NH_HTPYD.NH_HTPYD_013XC1"/>
     <cge:Meas_Ref ObjectId="218106"/>
    <cge:TPSR_Ref TObjectID="33828"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-218106">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -12.873418 -513.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33827" ObjectName="SW-NH_HTPYD.NH_HTPYD_013XC"/>
     <cge:Meas_Ref ObjectId="218106"/>
    <cge:TPSR_Ref TObjectID="33827"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-218107">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 31.000000 -349.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33829" ObjectName="SW-NH_HTPYD.NH_HTPYD_01367SW"/>
     <cge:Meas_Ref ObjectId="218107"/>
    <cge:TPSR_Ref TObjectID="33829"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-218122">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 227.126582 -454.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33832" ObjectName="SW-NH_HTPYD.NH_HTPYD_014XC1"/>
     <cge:Meas_Ref ObjectId="218122"/>
    <cge:TPSR_Ref TObjectID="33832"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-218122">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 227.126582 -514.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33831" ObjectName="SW-NH_HTPYD.NH_HTPYD_014XC"/>
     <cge:Meas_Ref ObjectId="218122"/>
    <cge:TPSR_Ref TObjectID="33831"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-218123">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 271.000000 -350.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33833" ObjectName="SW-NH_HTPYD.NH_HTPYD_01467SW"/>
     <cge:Meas_Ref ObjectId="218123"/>
    <cge:TPSR_Ref TObjectID="33833"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-218138">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 478.126582 -514.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33835" ObjectName="SW-NH_HTPYD.NH_HTPYD_015XC"/>
     <cge:Meas_Ref ObjectId="218138"/>
    <cge:TPSR_Ref TObjectID="33835"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-218139">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 522.000000 -350.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33837" ObjectName="SW-NH_HTPYD.NH_HTPYD_01567SW"/>
     <cge:Meas_Ref ObjectId="218139"/>
    <cge:TPSR_Ref TObjectID="33837"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-218138">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 478.126582 -454.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33836" ObjectName="SW-NH_HTPYD.NH_HTPYD_015XC1"/>
     <cge:Meas_Ref ObjectId="218138"/>
    <cge:TPSR_Ref TObjectID="33836"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-218059">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 700.126582 -514.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33817" ObjectName="SW-NH_HTPYD.NH_HTPYD_0901XC"/>
     <cge:Meas_Ref ObjectId="218059"/>
    <cge:TPSR_Ref TObjectID="33817"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-218059">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 699.126582 -427.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33818" ObjectName="SW-NH_HTPYD.NH_HTPYD_0901XC1"/>
     <cge:Meas_Ref ObjectId="218059"/>
    <cge:TPSR_Ref TObjectID="33818"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-218060">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 571.126582 -648.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33819" ObjectName="SW-NH_HTPYD.NH_HTPYD_0611XC"/>
     <cge:Meas_Ref ObjectId="218060"/>
    <cge:TPSR_Ref TObjectID="33819"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-218005">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 12.000000 -983.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33812" ObjectName="SW-NH_HTPYD.NH_HTPYD_3011SW"/>
     <cge:Meas_Ref ObjectId="218005"/>
    <cge:TPSR_Ref TObjectID="33812"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-218004">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 38.000000 -974.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43249" ObjectName="SW-NH_HTPYD.NH_HTPYD_30160SW"/>
     <cge:Meas_Ref ObjectId="218004"/>
    <cge:TPSR_Ref TObjectID="43249"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-WD_DPYD.DY_SYYD_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-291,-601 762,-601 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="33809" ObjectName="BS-WD_DPYD.DY_SYYD_9IM"/>
    <cge:TPSR_Ref TObjectID="33809"/></metadata>
   <polyline fill="none" opacity="0" points="-291,-601 762,-601 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -229.000000 -210.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -8.000000 -200.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 232.000000 -201.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 483.000000 -201.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 537.000000 -683.000000)" xlink:href="#load:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 748.000000 -343.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_36a3ff0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 574.000000 -736.000000)" xlink:href="#lightningRod:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36a4620">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 538.000000 -732.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36a8770">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 75.000000 -1097.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36a9450">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 -1258.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36abf50">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 15.000000 -751.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36bd300">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 -273.323558 -344.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36c15a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -229.000000 -322.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36ca6c0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 -52.323558 -344.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36ce960">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -8.000000 -322.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36d7ce0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 187.676442 -345.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_351f1e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 232.000000 -323.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3525490">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 438.676442 -345.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3529730">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 483.000000 -323.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3533ac0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 547.000000 -859.000000)" xlink:href="#lightningRod:shape185"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3538a20">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 718.500000 -467.500000)" xlink:href="#lightningRod:shape132"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_353c9e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -75.000000 -734.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3563240">
    <use class="BV-0KV" transform="matrix(-0.326531 -0.000000 -0.000000 0.310345 762.000000 -395.000000)" xlink:href="#lightningRod:shape99"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3564f40">
    <use class="BV-0KV" transform="matrix(0.326531 0.000000 0.000000 -0.310345 534.000000 -658.000000)" xlink:href="#lightningRod:shape99"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -618.000000 -1193.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="1" id="ME-217978" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 223.000000 -894.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217978" ObjectName="WD_DPYD:DY_SYYD_1T_TP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="1" id="ME-217979" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 223.000000 -876.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217979" ObjectName="WD_DPYD:DY_SYYD_1T_Tmp"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="1" id="ME-217980" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 223.000000 -856.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217980" ObjectName="WD_DPYD:DY_SYYD_1T_Tmp1"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-217918" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 175.000000 -1060.500000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217918" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="33811"/>
     <cge:Term_Ref ObjectID="49555"/>
    <cge:TPSR_Ref TObjectID="33811"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-217919" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 175.000000 -1060.500000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217919" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="33811"/>
     <cge:Term_Ref ObjectID="49555"/>
    <cge:TPSR_Ref TObjectID="33811"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-217911" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 175.000000 -1060.500000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217911" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="33811"/>
     <cge:Term_Ref ObjectID="49555"/>
    <cge:TPSR_Ref TObjectID="33811"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-217914" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 175.000000 -1060.500000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217914" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="33811"/>
     <cge:Term_Ref ObjectID="49555"/>
    <cge:TPSR_Ref TObjectID="33811"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-217915" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 175.000000 -1060.500000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217915" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="33811"/>
     <cge:Term_Ref ObjectID="49555"/>
    <cge:TPSR_Ref TObjectID="33811"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-217916" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 175.000000 -1060.500000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217916" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="33811"/>
     <cge:Term_Ref ObjectID="49555"/>
    <cge:TPSR_Ref TObjectID="33811"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-217917" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 175.000000 -1060.500000) translate(0,102)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217917" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="33811"/>
     <cge:Term_Ref ObjectID="49555"/>
    <cge:TPSR_Ref TObjectID="33811"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-217920" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 175.000000 -1060.500000) translate(0,117)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217920" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="33811"/>
     <cge:Term_Ref ObjectID="49555"/>
    <cge:TPSR_Ref TObjectID="33811"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-217928" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 145.000000 -758.500000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217928" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="33815"/>
     <cge:Term_Ref ObjectID="49563"/>
    <cge:TPSR_Ref TObjectID="33815"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-217929" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 145.000000 -758.500000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217929" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="33815"/>
     <cge:Term_Ref ObjectID="49563"/>
    <cge:TPSR_Ref TObjectID="33815"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-217921" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 145.000000 -758.500000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217921" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="33815"/>
     <cge:Term_Ref ObjectID="49563"/>
    <cge:TPSR_Ref TObjectID="33815"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-217924" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 145.000000 -758.500000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217924" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="33815"/>
     <cge:Term_Ref ObjectID="49563"/>
    <cge:TPSR_Ref TObjectID="33815"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-217925" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 145.000000 -758.500000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217925" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="33815"/>
     <cge:Term_Ref ObjectID="49563"/>
    <cge:TPSR_Ref TObjectID="33815"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-217926" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 145.000000 -758.500000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217926" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="33815"/>
     <cge:Term_Ref ObjectID="49563"/>
    <cge:TPSR_Ref TObjectID="33815"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-217927" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 145.000000 -758.500000) translate(0,102)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217927" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="33815"/>
     <cge:Term_Ref ObjectID="49563"/>
    <cge:TPSR_Ref TObjectID="33815"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-217930" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 145.000000 -758.500000) translate(0,117)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217930" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="33815"/>
     <cge:Term_Ref ObjectID="49563"/>
    <cge:TPSR_Ref TObjectID="33815"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-217945" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -208.000000 -171.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217945" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="33821"/>
     <cge:Term_Ref ObjectID="49575"/>
    <cge:TPSR_Ref TObjectID="33821"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-217946" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -208.000000 -171.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217946" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="33821"/>
     <cge:Term_Ref ObjectID="49575"/>
    <cge:TPSR_Ref TObjectID="33821"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-217938" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -208.000000 -171.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217938" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="33821"/>
     <cge:Term_Ref ObjectID="49575"/>
    <cge:TPSR_Ref TObjectID="33821"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-217947" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -208.000000 -171.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217947" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="33821"/>
     <cge:Term_Ref ObjectID="49575"/>
    <cge:TPSR_Ref TObjectID="33821"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-217955" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -1.000000 -171.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217955" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="33826"/>
     <cge:Term_Ref ObjectID="49585"/>
    <cge:TPSR_Ref TObjectID="33826"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-217956" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -1.000000 -171.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217956" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="33826"/>
     <cge:Term_Ref ObjectID="49585"/>
    <cge:TPSR_Ref TObjectID="33826"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-217948" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -1.000000 -171.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217948" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="33826"/>
     <cge:Term_Ref ObjectID="49585"/>
    <cge:TPSR_Ref TObjectID="33826"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-217957" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -1.000000 -171.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217957" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="33826"/>
     <cge:Term_Ref ObjectID="49585"/>
    <cge:TPSR_Ref TObjectID="33826"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-217965" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 232.000000 -171.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217965" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="33830"/>
     <cge:Term_Ref ObjectID="49593"/>
    <cge:TPSR_Ref TObjectID="33830"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-217966" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 232.000000 -171.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217966" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="33830"/>
     <cge:Term_Ref ObjectID="49593"/>
    <cge:TPSR_Ref TObjectID="33830"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-217958" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 232.000000 -171.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217958" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="33830"/>
     <cge:Term_Ref ObjectID="49593"/>
    <cge:TPSR_Ref TObjectID="33830"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-217967" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 232.000000 -171.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217967" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="33830"/>
     <cge:Term_Ref ObjectID="49593"/>
    <cge:TPSR_Ref TObjectID="33830"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-217975" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 497.000000 -171.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217975" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="33834"/>
     <cge:Term_Ref ObjectID="49601"/>
    <cge:TPSR_Ref TObjectID="33834"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-217976" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 497.000000 -171.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217976" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="33834"/>
     <cge:Term_Ref ObjectID="49601"/>
    <cge:TPSR_Ref TObjectID="33834"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-217968" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 497.000000 -171.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217968" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="33834"/>
     <cge:Term_Ref ObjectID="49601"/>
    <cge:TPSR_Ref TObjectID="33834"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-217977" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 497.000000 -171.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217977" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="33834"/>
     <cge:Term_Ref ObjectID="49601"/>
    <cge:TPSR_Ref TObjectID="33834"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-217931" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 834.000000 -653.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217931" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="33809"/>
     <cge:Term_Ref ObjectID="49552"/>
    <cge:TPSR_Ref TObjectID="33809"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-217932" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 834.000000 -653.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217932" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="33809"/>
     <cge:Term_Ref ObjectID="49552"/>
    <cge:TPSR_Ref TObjectID="33809"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-217933" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 834.000000 -653.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217933" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="33809"/>
     <cge:Term_Ref ObjectID="49552"/>
    <cge:TPSR_Ref TObjectID="33809"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-217937" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 834.000000 -653.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217937" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="33809"/>
     <cge:Term_Ref ObjectID="49552"/>
    <cge:TPSR_Ref TObjectID="33809"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-217934" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 834.000000 -653.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217934" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="33809"/>
     <cge:Term_Ref ObjectID="49552"/>
    <cge:TPSR_Ref TObjectID="33809"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ubc" PreSymbol="0" appendix="" decimal="2" id="ME-217935" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 834.000000 -653.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217935" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="33809"/>
     <cge:Term_Ref ObjectID="49552"/>
    <cge:TPSR_Ref TObjectID="33809"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uca" PreSymbol="0" appendix="" decimal="2" id="ME-217936" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 834.000000 -653.000000) translate(0,102)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217936" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="33809"/>
     <cge:Term_Ref ObjectID="49552"/>
    <cge:TPSR_Ref TObjectID="33809"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="240" x="-701" y="-1256"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="240" x="-701" y="-1256"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-750" y="-1273"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-750" y="-1273"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="-439" y="-1230"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="-439" y="-1230"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="-439" y="-1265"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="-439" y="-1265"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="52" x="59" y="-882"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="52" x="59" y="-882"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="-215" y="-504"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="-215" y="-504"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="6" y="-504"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="6" y="-504"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="247" y="-505"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="247" y="-505"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="498" y="-505"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="498" y="-505"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="80" x="-696" y="-923"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="80" x="-696" y="-923"/></g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="240" x="-701" y="-1256"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-750" y="-1273"/></g>
   <g href="cx_配调_配网接线图35_南华.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="-439" y="-1230"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="-439" y="-1265"/></g>
   <g href="35kV红土坡移动变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="52" x="59" y="-882"/></g>
   <g href="35kV红土坡移动变10kV簪花线012间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="-215" y="-504"/></g>
   <g href="35kV红土坡移动变10kV五顶山线013间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="6" y="-504"/></g>
   <g href="35kV红土坡移动变10kV红罗线014间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="247" y="-505"/></g>
   <g href="35kV红土坡移动变10kV机关线015间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="498" y="-505"/></g>
   <g href="35kV红土坡移动变GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="80" x="-696" y="-923"/></g>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-218003">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 12.241796 -934.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33811" ObjectName="SW-NH_HTPYD.NH_HTPYD_301BK"/>
     <cge:Meas_Ref ObjectId="218003"/>
    <cge:TPSR_Ref TObjectID="33811"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-218019">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.126582 -664.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33815" ObjectName="SW-NH_HTPYD.NH_HTPYD_001BK"/>
     <cge:Meas_Ref ObjectId="218019"/>
    <cge:TPSR_Ref TObjectID="33815"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-218088">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -233.873418 -475.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33821" ObjectName="SW-NH_HTPYD.NH_HTPYD_012BK"/>
     <cge:Meas_Ref ObjectId="218088"/>
    <cge:TPSR_Ref TObjectID="33821"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-218105">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -12.873418 -475.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33826" ObjectName="SW-NH_HTPYD.NH_HTPYD_013BK"/>
     <cge:Meas_Ref ObjectId="218105"/>
    <cge:TPSR_Ref TObjectID="33826"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-218121">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 228.126582 -476.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33830" ObjectName="SW-NH_HTPYD.NH_HTPYD_014BK"/>
     <cge:Meas_Ref ObjectId="218121"/>
    <cge:TPSR_Ref TObjectID="33830"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-218137">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 479.126582 -476.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33834" ObjectName="SW-NH_HTPYD.NH_HTPYD_015BK"/>
     <cge:Meas_Ref ObjectId="218137"/>
    <cge:TPSR_Ref TObjectID="33834"/></metadata>
   </g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(-0.548387 -0.000000 -0.000000 0.625000 761.000000 -418.000000)" xlink:href="#capacitor:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(0.548387 0.000000 0.000000 -0.625000 534.000000 -634.000000)" xlink:href="#capacitor:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-10KV" id="g_36a3b90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-224,-537 -224,-601 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="33822@0" ObjectIDZND0="33809@0" Pin0InfoVect0LinkObjId="g_36b4990_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218089_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-224,-537 -224,-601 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36a3dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="581,-800 581,-785 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3533ac0@0" ObjectIDZND0="g_36a3ff0@1" Pin0InfoVect0LinkObjId="g_36a3ff0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3533ac0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="581,-800 581,-785 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36a5310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="581,-717 545,-717 545,-736 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_36a3ff0@0" ObjectIDND1="33819@x" ObjectIDZND0="g_36a4620@0" Pin0InfoVect0LinkObjId="g_36a4620_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_36a3ff0_0" Pin1InfoVect1LinkObjId="SW-218060_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="581,-717 545,-717 545,-736 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36a5e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="581,-741 581,-717 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_36a3ff0@0" ObjectIDZND0="g_36a4620@0" ObjectIDZND1="33819@x" Pin0InfoVect0LinkObjId="g_36a4620_0" Pin0InfoVect1LinkObjId="SW-218060_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36a3ff0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="581,-741 581,-717 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36a8510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="21,-1088 -43,-1088 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="voltageTransformer" ObjectIDND0="g_36a8770@0" ObjectIDND1="g_36a9450@0" ObjectIDND2="33812@x" ObjectIDZND0="g_353f230@0" Pin0InfoVect0LinkObjId="g_353f230_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_36a8770_0" Pin1InfoVect1LinkObjId="g_36a9450_0" Pin1InfoVect2LinkObjId="SW-218005_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="21,-1088 -43,-1088 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36b4990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="20,-649 20,-601 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="33813@0" ObjectIDZND0="33809@0" Pin0InfoVect0LinkObjId="g_36a3b90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218018_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="20,-649 20,-601 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36b7cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-224,-519 -224,-510 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="33822@1" ObjectIDZND0="33821@1" Pin0InfoVect0LinkObjId="SW-218088_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218089_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-224,-519 -224,-510 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36b7f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-224,-483 -224,-477 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="33821@0" ObjectIDZND0="33823@1" Pin0InfoVect0LinkObjId="SW-218089_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218088_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-224,-483 -224,-477 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36bd0a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-224,-460 -224,-379 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="33823@0" ObjectIDZND0="g_36c15a0@1" Pin0InfoVect0LinkObjId="g_36c15a0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218089_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-224,-460 -224,-379 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36be030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-181,-400 -181,-426 -266,-426 -266,-398 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="33824@0" ObjectIDZND0="g_36bd300@0" Pin0InfoVect0LinkObjId="g_36bd300_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218090_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-181,-400 -181,-426 -266,-426 -266,-398 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36c5090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-3,-519 -3,-510 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="33827@1" ObjectIDZND0="33826@1" Pin0InfoVect0LinkObjId="SW-218105_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218106_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-3,-519 -3,-510 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36c52f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-3,-483 -3,-477 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="33826@0" ObjectIDZND0="33828@1" Pin0InfoVect0LinkObjId="SW-218106_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218105_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-3,-483 -3,-477 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36ca460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-3,-460 -3,-379 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="33828@0" ObjectIDZND0="g_36ce960@1" Pin0InfoVect0LinkObjId="g_36ce960_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218106_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-3,-460 -3,-379 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36cb3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="40,-400 40,-426 -45,-426 -45,-398 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="33829@0" ObjectIDZND0="g_36ca6c0@0" Pin0InfoVect0LinkObjId="g_36ca6c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218107_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="40,-400 40,-426 -45,-426 -45,-398 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36cf380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-3,-537 -3,-601 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="33827@0" ObjectIDZND0="33809@0" Pin0InfoVect0LinkObjId="g_36a3b90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218106_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-3,-537 -3,-601 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36d26b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="237,-520 237,-511 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="33831@1" ObjectIDZND0="33830@1" Pin0InfoVect0LinkObjId="SW-218121_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218122_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="237,-520 237,-511 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36d2910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="237,-484 237,-478 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="33830@0" ObjectIDZND0="33832@1" Pin0InfoVect0LinkObjId="SW-218122_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218121_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="237,-484 237,-478 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36d7a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="237,-461 237,-380 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="33832@0" ObjectIDZND0="g_351f1e0@1" Pin0InfoVect0LinkObjId="g_351f1e0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218122_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="237,-461 237,-380 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36d8a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="280,-401 280,-427 195,-427 195,-399 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="33833@0" ObjectIDZND0="g_36d7ce0@0" Pin0InfoVect0LinkObjId="g_36d7ce0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218123_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="280,-401 280,-427 195,-427 195,-399 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_351fc00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="237,-538 237,-601 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="33831@0" ObjectIDZND0="33809@0" Pin0InfoVect0LinkObjId="g_36a3b90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218122_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="237,-538 237,-601 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_351fe60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="488,-520 488,-511 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="33835@1" ObjectIDZND0="33834@1" Pin0InfoVect0LinkObjId="SW-218137_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218138_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="488,-520 488,-511 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35200c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="488,-484 488,-478 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="33834@0" ObjectIDZND0="33836@1" Pin0InfoVect0LinkObjId="SW-218138_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218137_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="488,-484 488,-478 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3525230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="488,-461 488,-380 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="33836@0" ObjectIDZND0="g_3529730@1" Pin0InfoVect0LinkObjId="g_3529730_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218138_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="488,-461 488,-380 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35261c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="531,-401 531,-427 446,-427 446,-399 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="33837@0" ObjectIDZND0="g_3525490@0" Pin0InfoVect0LinkObjId="g_3525490_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218139_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="531,-401 531,-427 446,-427 446,-399 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_352a150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="488,-538 488,-601 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="33835@0" ObjectIDZND0="33809@0" Pin0InfoVect0LinkObjId="g_36a3b90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218138_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="488,-538 488,-601 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35302d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="710,-538 710,-601 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="33817@0" ObjectIDZND0="33809@0" Pin0InfoVect0LinkObjId="g_36a3b90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218059_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="710,-538 710,-601 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3533600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="710,-521 710,-503 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="33817@1" ObjectIDZND0="g_3538a20@1" Pin0InfoVect0LinkObjId="g_3538a20_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218059_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="710,-521 710,-503 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3533860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="709,-451 709,-469 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="33818@1" ObjectIDZND0="g_3538a20@0" Pin0InfoVect0LinkObjId="g_3538a20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218059_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="709,-451 709,-469 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3539320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="21,-809 21,-834 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_36abf50@1" ObjectIDZND0="33838@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36abf50_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="21,-809 21,-834 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3539580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="21,-915 21,-942 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="33838@1" ObjectIDZND0="33811@0" Pin0InfoVect0LinkObjId="SW-218003_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3539320_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="21,-915 21,-942 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_353ba30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="20,-741 -17,-741 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_36abf50@0" ObjectIDND1="33814@x" ObjectIDZND0="g_353c9e0@0" Pin0InfoVect0LinkObjId="g_353c9e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_36abf50_0" Pin1InfoVect1LinkObjId="SW-218018_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="20,-741 -17,-741 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_353c520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="20,-756 20,-741 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_36abf50@0" ObjectIDZND0="33814@x" ObjectIDZND1="g_353c9e0@0" Pin0InfoVect0LinkObjId="SW-218018_0" Pin0InfoVect1LinkObjId="g_353c9e0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36abf50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="20,-756 20,-741 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_353c780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="20,-741 20,-725 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_36abf50@0" ObjectIDND1="g_353c9e0@0" ObjectIDZND0="33814@0" Pin0InfoVect0LinkObjId="SW-218018_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_36abf50_0" Pin1InfoVect1LinkObjId="g_353c9e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="20,-741 20,-725 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_353d790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-224,-327 -224,-230 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_36c15a0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="EC-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36c15a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-224,-327 -224,-230 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_353e280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="79,-1105 21,-1105 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="g_36a8770@0" ObjectIDZND0="33812@x" ObjectIDZND1="g_353f230@0" ObjectIDZND2="g_36a9450@0" Pin0InfoVect0LinkObjId="SW-218005_0" Pin0InfoVect1LinkObjId="g_353f230_0" Pin0InfoVect2LinkObjId="g_36a9450_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36a8770_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="79,-1105 21,-1105 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_353ed70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="21,-1107 21,-1088 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="voltageTransformer" ObjectIDND0="g_36a8770@0" ObjectIDND1="g_36a9450@0" ObjectIDZND0="33812@x" ObjectIDZND1="g_353f230@0" Pin0InfoVect0LinkObjId="SW-218005_0" Pin0InfoVect1LinkObjId="g_353f230_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_36a8770_0" Pin1InfoVect1LinkObjId="g_36a9450_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="21,-1107 21,-1088 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_353efd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="21,-1088 21,-1024 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="voltageTransformer" EndDevType0="switch" ObjectIDND0="g_36a8770@0" ObjectIDND1="g_36a9450@0" ObjectIDND2="g_353f230@0" ObjectIDZND0="33812@1" Pin0InfoVect0LinkObjId="SW-218005_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_36a8770_0" Pin1InfoVect1LinkObjId="g_36a9450_0" Pin1InfoVect2LinkObjId="g_353f230_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="21,-1088 21,-1024 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35418a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="20,-666 20,-672 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="33813@1" ObjectIDZND0="33815@0" Pin0InfoVect0LinkObjId="SW-218019_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218018_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="20,-666 20,-672 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3541b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="20,-699 20,-708 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="33815@1" ObjectIDZND0="33814@1" Pin0InfoVect0LinkObjId="SW-218018_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218019_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="20,-699 20,-708 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3542c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="21,-1376 21,-1316 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_36a9450@1" Pin0InfoVect0LinkObjId="g_36a9450_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="21,-1376 21,-1316 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35447e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-3,-327 -3,-221 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_36ce960@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="EC-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36ce960_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-3,-327 -3,-221 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35455f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="237,-328 237,-222 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_351f1e0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="EC-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_351f1e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="237,-328 237,-222 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3546460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="488,-328 488,-222 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_3529730@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="EC-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3529730_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="488,-328 488,-222 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35545c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="21,-1105 21,-1263 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_36a8770@0" ObjectIDND1="33812@x" ObjectIDND2="g_353f230@0" ObjectIDZND0="g_36a9450@0" Pin0InfoVect0LinkObjId="g_36a9450_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_36a8770_0" Pin1InfoVect1LinkObjId="SW-218005_0" Pin1InfoVect2LinkObjId="g_353f230_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="21,-1105 21,-1263 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3554bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="581,-717 581,-672 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_36a4620@0" ObjectIDND1="g_36a3ff0@0" ObjectIDZND0="33819@1" Pin0InfoVect0LinkObjId="SW-218060_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_36a4620_0" Pin1InfoVect1LinkObjId="g_36a3ff0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="581,-717 581,-672 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_355e300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="43,-981 21,-981 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="43249@0" ObjectIDZND0="33812@x" ObjectIDZND1="33811@x" Pin0InfoVect0LinkObjId="SW-218005_0" Pin0InfoVect1LinkObjId="SW-218003_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218004_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="43,-981 21,-981 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_355edf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="21,-988 21,-981 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="33812@0" ObjectIDZND0="43249@x" ObjectIDZND1="33811@x" Pin0InfoVect0LinkObjId="SW-218004_0" Pin0InfoVect1LinkObjId="SW-218003_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218005_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="21,-988 21,-981 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_355f050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="21,-981 21,-969 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="43249@x" ObjectIDND1="33812@x" ObjectIDZND0="33811@1" Pin0InfoVect0LinkObjId="SW-218003_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-218004_0" Pin1InfoVect1LinkObjId="SW-218005_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="21,-981 21,-969 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_355fb20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="542,-638 542,-631 581,-631 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="0@1" ObjectIDZND0="33819@x" ObjectIDZND1="33809@0" Pin0InfoVect0LinkObjId="SW-218060_0" Pin0InfoVect1LinkObjId="g_36a3b90_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="542,-638 542,-631 581,-631 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3560490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="581,-655 581,-631 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" EndDevType1="capacitor" ObjectIDND0="33819@0" ObjectIDZND0="33809@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_36a3b90_0" Pin0InfoVect1LinkObjId="EC-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218060_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="581,-655 581,-631 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3560680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="581,-631 581,-601 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" EndDevType0="busSection" ObjectIDND0="33819@x" ObjectIDND1="0@x" ObjectIDZND0="33809@0" Pin0InfoVect0LinkObjId="g_36a3b90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-218060_0" Pin1InfoVect1LinkObjId="EC-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="581,-631 581,-601 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3560890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="753,-418 753,-423 709,-423 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="transformer2" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="33818@x" Pin0InfoVect0LinkObjId="EC-0_0" Pin0InfoVect1LinkObjId="SW-218059_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="753,-418 753,-423 709,-423 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3561330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="709,-371 709,-423 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" EndDevType1="capacitor" ObjectIDND0="0@1" ObjectIDZND0="33818@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-218059_0" Pin0InfoVect1LinkObjId="EC-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="709,-371 709,-423 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3561570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="709,-423 709,-434 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="capacitor" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="33818@0" Pin0InfoVect0LinkObjId="SW-218059_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-0_0" Pin1InfoVect1LinkObjId="EC-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="709,-423 709,-434 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3564190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="753,-364 753,-378 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_3563240@0" Pin0InfoVect0LinkObjId="g_3563240_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="753,-364 753,-378 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_35643f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="753,-406 753,-393 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_3563240@1" Pin0InfoVect0LinkObjId="g_3563240_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="753,-406 753,-393 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3565e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="542,-688 542,-674 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_3564f40@0" Pin0InfoVect0LinkObjId="g_3564f40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="542,-688 542,-674 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_35660f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="542,-646 542,-659 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_3564f40@1" Pin0InfoVect0LinkObjId="g_3564f40_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="542,-646 542,-659 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-217898" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -415.500000 -1156.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33797" ObjectName="DYN-WD_MJYD"/>
     <cge:Meas_Ref ObjectId="217898"/>
    </metadata>
   </g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3543210" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 127.500000 859.000000) translate(0,12)">绕温(℃):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3543470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 157.000000 894.000000) translate(0,12)">档位:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35436b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 127.500000 877.000000) translate(0,12)">油温(℃):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3549020" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 121.000000 1001.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3549270" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 121.000000 986.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35494b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 112.000000 971.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35496f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 121.000000 1060.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3549930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 109.500000 1045.500000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3549b70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 135.000000 1031.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3549db0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 140.000000 956.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3549ff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 121.000000 1016.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_354a320" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 89.000000 699.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_354a5b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 89.000000 684.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_354a7f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 80.000000 669.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_354aa30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 89.000000 758.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_354ac70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 77.500000 743.500000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_354aeb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 103.000000 729.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_354b0f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 108.000000 654.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_354b330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 89.000000 714.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_354c2e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -244.000000 125.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_354c540" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -264.000000 172.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_354c780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -274.500000 156.500000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_354c9c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -250.000000 141.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_354ccf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -40.000000 125.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_354cf60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -60.000000 172.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_354d1a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -70.500000 156.500000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_354d3e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -46.000000 141.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_354e800" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 195.000000 125.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_354ea60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 175.000000 172.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_354eca0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 164.500000 156.500000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_354eee0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 189.000000 141.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_354f210" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 457.000000 125.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_354f480" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 437.000000 172.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_354f6c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 426.500000 156.500000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_354f900" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 451.000000 141.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3550290" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 771.000000 563.000000) translate(0,12)">Uca(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35504e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 778.000000 638.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3550720" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 770.000000 593.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3550960" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 779.000000 623.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3550ba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 786.000000 608.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3550de0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 778.000000 653.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3551020" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 771.000000 578.000000) translate(0,12)">Ubc(kV):</text>
   <metadata/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-NH_HTPYD.NH_HTPYD_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="49611"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -15.581733 -829.000000)" xlink:href="#transformer2:shape14_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -15.581733 -829.000000)" xlink:href="#transformer2:shape14_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="33838" ObjectName="TF-NH_HTPYD.NH_HTPYD_1T"/>
    <cge:TPSR_Ref TObjectID="33838"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 697.000000 -323.000000)" xlink:href="#transformer2:shape92_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 697.000000 -323.000000)" xlink:href="#transformer2:shape92_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(64,64,64)" font-family="SimHei" font-size="20" graphid="g_36a2880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -675.000000 -1245.500000) translate(0,16)">红土坡变移动式变电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_36a31c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_36a31c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_36a31c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_36a31c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_36a31c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_36a31c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_36a31c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_36a3470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_36a3470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_36a3470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_36a3470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_36a3470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_36a3470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_36a3470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_36a3470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_36a3470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_36a3470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_36a3470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_36a3470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_36a3470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_36a3470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_36a3470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_36a3470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_36a3470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_36a3470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36a6060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 540.000000 -880.000000) translate(0,12)">10kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_36a6850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -428.000000 -1222.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_36a6d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -428.000000 -1257.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35397e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 696.000000 -625.000000) translate(0,12)">10kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3539e10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 670.000000 -306.000000) translate(0,12)">10kV1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3541d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -260.000000 -198.000000) translate(0,12)">10kV簪花线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3542a20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 59.000000 -882.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3542e70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 31.000000 -963.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35438f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 30.000000 -693.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3543b30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -215.000000 -504.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3543d70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -171.000000 -384.000000) translate(0,12)">01267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35472d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6.000000 -504.000000) translate(0,12)">013</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3547900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 50.000000 -384.000000) translate(0,12)">01367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3547b40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 247.000000 -505.000000) translate(0,12)">014</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3547d80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 289.000000 -385.000000) translate(0,12)">01467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3547fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 498.000000 -505.000000) translate(0,12)">015</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3548200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 540.000000 -385.000000) translate(0,12)">01567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3548440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 729.000000 -496.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3552870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -37.000000 -198.000000) translate(0,12)">10kV五顶山线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3553480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 199.000000 -198.000000) translate(0,12)">10kV红罗线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3553d30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 446.000000 -198.000000) translate(0,12)">10kV机关线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_35547b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -696.000000 -923.000000) translate(0,16)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3558190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 593.000000 -673.000000) translate(0,16)">0611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_355f2b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -37.000000 -1015.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_355f8e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42.000000 -1005.000000) translate(0,12)">30160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3569700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -160.000000 -914.000000) translate(0,12)">1号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3569700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -160.000000 -914.000000) translate(0,27)">SZ-10000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3569700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -160.000000 -914.000000) translate(0,42)">35±3×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3569700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -160.000000 -914.000000) translate(0,57)">Ud=8.0%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3569700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -160.000000 -914.000000) translate(0,72)">YN,d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3569700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -160.000000 -914.000000) translate(0,87)">ONAN</text>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_353f230">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -123.000000 -1057.000000)" xlink:href="#voltageTransformer:shape142"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="33809" cx="-224" cy="-601" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="33809" cx="20" cy="-601" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="33809" cx="-3" cy="-601" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="33809" cx="237" cy="-601" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="33809" cx="488" cy="-601" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="33809" cx="710" cy="-601" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="33809" cx="581" cy="-601" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="NH_HTPYD"/>
</svg>