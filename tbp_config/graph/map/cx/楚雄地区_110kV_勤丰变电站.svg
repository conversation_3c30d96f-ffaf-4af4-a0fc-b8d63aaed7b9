<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-18" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="3117 -1268 2454 1269">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="36" y1="15" y2="4"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="36" y1="15" y2="4"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="capacitor:shape14">
    <polyline arcFlag="1" points="27,100 25,100 23,99 22,99 20,98 19,97 17,96 16,94 15,92 15,91 14,89 14,87 14,85 15,83 15,82 16,80 17,79 19,77 20,76 22,75 23,75 25,74 27,74 29,74 31,75 32,75 34,76 35,77 37,79 38,80 39,82 39,83 40,85 40,87 " stroke-width="0.0972"/>
    <rect height="23" stroke-width="0.945274" width="11" x="41" y="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.945274" x1="47" x2="45" y1="31" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.945274" x1="49" x2="47" y1="34" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.07143" x1="47" x2="47" y1="54" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.428414" x1="47" x2="47" y1="24" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.428414" x1="54" x2="40" y1="16" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.428414" x1="50" x2="44" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.309343" x1="49" x2="45" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="40" x2="28" y1="87" y2="87"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="27" x2="27" y1="99" y2="107"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="12" y1="15" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="11" y1="54" y2="47"/>
    <polyline arcFlag="1" points="11,15 10,15 9,15 9,15 8,15 8,16 7,16 7,17 6,17 6,18 6,18 6,19 5,20 5,20 5,21 6,22 6,22 6,23 6,23 7,24 7,24 8,25 8,25 9,25 9,26 10,26 11,26 " stroke-width="1"/>
    <polyline arcFlag="1" points="11,25 10,25 9,25 9,25 8,26 8,26 7,26 7,27 6,27 6,28 6,29 6,29 5,30 5,31 5,31 6,32 6,33 6,33 6,34 7,34 7,35 8,35 8,35 9,36 9,36 10,36 11,36 " stroke-width="1"/>
    <polyline arcFlag="1" points="11,36 10,36 9,36 9,37 8,37 8,37 7,38 7,38 6,39 6,39 6,40 6,40 5,41 5,42 5,42 6,43 6,44 6,44 6,45 7,45 7,46 8,46 8,47 9,47 9,47 10,47 11,47 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.42985" x1="28" x2="28" y1="88" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.368819" x1="45" x2="12" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.395152" x1="28" x2="13" y1="8" y2="8"/>
    <rect height="23" stroke-width="0.398039" width="12" x="22" y="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.338133" x1="46" x2="12" y1="55" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="46" x2="46" y1="2" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="11" x2="11" y1="2" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72286" x1="28" x2="28" y1="14" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="38" x2="18" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="38" x2="18" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.370253" x1="2" x2="2" y1="45" y2="16"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="13" x2="4" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="5" x2="7" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="3" x2="9" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="28" stroke-width="1" width="14" x="0" y="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="54" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="7" x2="7" y1="51" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="52" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="4" y2="39"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape29">
    <ellipse cx="11" cy="15" fillStyle="0" rx="10.5" ry="11.5" stroke-width="0.64567"/>
    <ellipse cx="11" cy="28" fillStyle="0" rx="10.5" ry="11.5" stroke-width="0.64567"/>
   </symbol>
   <symbol id="lightningRod:shape45">
    <circle cx="18" cy="15" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="18" cy="7" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="11" cy="11" fillStyle="0" r="6" stroke-width="0.431185"/>
   </symbol>
   <symbol id="lightningRod:shape54">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="6" x2="6" y1="58" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="6" x2="6" y1="8" y2="37"/>
   </symbol>
   <symbol id="lightningRod:shape50">
    <polyline DF8003:Layer="PUBLIC" points="5,39 0,50 11,50 5,39 5,40 5,39 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.222222" x1="5" x2="5" y1="29" y2="33"/>
    <polyline DF8003:Layer="PUBLIC" points="5,25 0,14 11,14 5,25 5,24 5,25 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="59" y2="51"/>
   </symbol>
   <symbol id="lightningRod:shape106">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="26" y1="40" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="54" x2="43" y1="40" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="42" x2="42" y1="48" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="35" x2="35" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.676705" x1="34" x2="16" y1="40" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="16" x2="16" y1="48" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="9" x2="9" y1="48" y2="31"/>
    <circle cx="30" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="26" cy="15" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="20" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="0" x2="9" y1="40" y2="40"/>
   </symbol>
   <symbol id="lightningRod:shape4">
    <rect height="31" stroke-width="2" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="9" x2="9" y1="35" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape132">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="7" x2="36" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="9" y2="9"/>
    <rect height="16" stroke-width="2" width="31" x="6" y="1"/>
   </symbol>
   <symbol id="lightningRod:shape41">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.282524" x1="6" x2="6" y1="35" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.337605" x1="7" x2="7" y1="5" y2="16"/>
    <polyline arcFlag="1" points="7,28 7,28 7,28 7,28 8,28 8,28 8,27 8,27 9,27 9,27 9,26 9,26 9,26 9,25 9,25 9,24 9,24 9,24 9,23 8,23 8,23 8,23 8,22 7,22 7,22 7,22 " stroke-width="0.0170053"/>
    <polyline arcFlag="1" points="6,34 7,34 7,34 7,34 7,34 8,34 8,34 8,33 8,33 8,33 8,32 9,32 9,32 9,31 9,31 9,31 8,30 8,30 8,30 8,29 8,29 8,29 7,29 7,28 7,28 7,28 " stroke-width="0.0170053"/>
    <polyline arcFlag="1" points="7,22 7,22 7,22 7,22 8,22 8,21 8,21 8,21 9,21 9,20 9,20 9,20 9,19 9,19 9,19 9,18 9,18 9,17 9,17 8,17 8,17 8,16 8,16 7,16 7,16 7,16 " stroke-width="0.0170053"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.266312" x1="5" x2="8" y1="50" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.282524" x1="4" x2="9" y1="48" y2="48"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.282524" x1="0" x2="13" y1="46" y2="46"/>
   </symbol>
   <symbol id="lightningRod:shape25">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0182374" x1="13" x2="13" y1="32" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0326126" x1="8" x2="13" y1="29" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0326126" x1="17" x2="13" y1="29" y2="32"/>
    <ellipse cx="12" cy="32" rx="12.5" ry="12" stroke-width="0.120929"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0433242" x1="16" x2="9" y1="12" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0433242" x1="9" x2="9" y1="17" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0433242" x1="16" x2="9" y1="12" y2="7"/>
    <circle cx="12" cy="12" r="12.5" stroke-width="0.120929"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="load:shape17">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,5 52,5 " stroke-width="1"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape31_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="27" x2="1" y1="37" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="27" x2="1" y1="5" y2="5"/>
    <circle cx="14" cy="21" fillStyle="0" r="4" stroke-width="0.619048"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="2" x2="26" y1="9" y2="33"/>
   </symbol>
   <symbol id="switch2:shape31_1">
    <circle cx="14" cy="21" fillStyle="0" r="4" stroke-width="0.619048"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="27" x2="1" y1="37" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="27" x2="1" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="14" y1="37" y2="5"/>
   </symbol>
   <symbol id="switch2:shape31-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="27" x2="1" y1="37" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="27" x2="1" y1="5" y2="5"/>
    <circle cx="14" cy="21" fillStyle="0" r="4" stroke-width="0.619048"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="2" x2="26" y1="9" y2="33"/>
   </symbol>
   <symbol id="switch2:shape31-UnNor2">
    <circle cx="14" cy="21" fillStyle="0" r="4" stroke-width="0.619048"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="27" x2="1" y1="37" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="27" x2="1" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="14" y1="37" y2="5"/>
   </symbol>
   <symbol id="switch2:shape8_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="18" y2="43"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="18" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
   </symbol>
   <symbol id="switch2:shape8_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="44" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
   </symbol>
   <symbol id="switch2:shape8-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="18" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="18" y2="43"/>
   </symbol>
   <symbol id="switch2:shape8-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="44" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
   </symbol>
   <symbol id="transformer:shape16_0">
    <ellipse cx="70" cy="46" fillStyle="0" rx="26.5" ry="26" stroke-width="0.540424"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="0" x2="71" y1="29" y2="100"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="80" x2="73" y1="47" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="87" x2="80" y1="54" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="80" x2="80" y1="38" y2="47"/>
   </symbol>
   <symbol id="transformer:shape16_1">
    <ellipse cx="41" cy="61" fillStyle="0" rx="26" ry="26.5" stroke-width="0.540424"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="41" x2="34" y1="71" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="49" x2="41" y1="79" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="41" x2="41" y1="62" y2="71"/>
   </symbol>
   <symbol id="transformer:shape16-2">
    <circle cx="41" cy="30" fillStyle="0" r="26.5" stroke-width="0.55102"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="40" x2="31" y1="32" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="40" x2="49" y1="32" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="31" x2="49" y1="16" y2="16"/>
   </symbol>
   <symbol id="transformer2:shape57_0">
    <circle cx="16" cy="80" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="16" y1="50" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="43" x2="40" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="45" x2="37" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="35" x2="47" y1="26" y2="26"/>
    <polyline DF8003:Layer="PUBLIC" points="16,12 22,25 10,25 16,12 16,13 16,12 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="43" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="21" y1="79" y2="84"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="11" y1="79" y2="84"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="79" y2="74"/>
   </symbol>
   <symbol id="transformer2:shape57_1">
    <circle cx="16" cy="58" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="16,55 41,55 41,26 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="55" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="21" y1="55" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="11" y1="55" y2="60"/>
   </symbol>
   <symbol id="voltageTransformer:shape34">
    <polyline points="41,18 8,18 8,43 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.3267" x1="47" x2="42" y1="39" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.3267" x1="37" x2="42" y1="39" y2="36"/>
    <ellipse cx="42" cy="19" fillStyle="0" rx="10" ry="11" stroke-width="0.695459"/>
    <ellipse cx="40" cy="37" fillStyle="0" rx="9.5" ry="11" stroke-width="0.695459"/>
    <ellipse cx="54" cy="29" fillStyle="0" rx="10" ry="11" stroke-width="0.695459"/>
    <ellipse cx="28" cy="30" fillStyle="0" rx="9.5" ry="11" stroke-width="0.695459"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.3267" x1="50" x2="54" y1="30" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.32675" x1="55" x2="55" y1="26" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.3267" x1="59" x2="55" y1="30" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.3267" x1="37" x2="42" y1="22" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.32675" x1="42" x2="42" y1="19" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.3267" x1="47" x2="42" y1="22" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.21447" x1="30" x2="34" y1="32" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.21568" x1="30" x2="25" y1="32" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.17171" x1="25" x2="34" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.32675" x1="42" x2="42" y1="36" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.618687" x1="6" x2="10" y1="51" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.789474" x1="0" x2="15" y1="43" y2="43"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.789474" x1="5" x2="11" y1="47" y2="47"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3548320" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3548cd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3549680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3549e40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_354aa70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_354b490" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_354bca0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_354c690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_354dcc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_354dcc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_354efb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_354efb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3550de0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3550de0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_3551eb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3553ab0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_35546a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3555460" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3555da0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3556bd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3557390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_35579a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_35583c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_35595a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3559f20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_355aa10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_355b3d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_355c8f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_355d3b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_355e3d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_355f060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_356d4d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_356dd00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_3560ec0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_35624a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1279" width="2464" x="3112" y="-1273"/>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dcab80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5412.000000 1172.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dcbff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5401.000000 1157.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dccd40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5426.000000 1142.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dcd7a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5412.000000 1018.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dcdaa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5401.000000 1003.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dcdce0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5426.000000 988.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dce100" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5412.000000 924.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dce3c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5401.000000 909.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dce600" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5426.000000 894.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dcea20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5412.000000 650.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dcece0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5401.000000 635.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dcef20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5426.000000 620.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dcf340" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5412.000000 565.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dcf600" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5401.000000 550.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dcf840" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5426.000000 535.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dcfc60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5412.000000 334.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dcff20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5401.000000 319.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dd0160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5426.000000 304.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dd0580" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4957.000000 738.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dd0840" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4946.000000 723.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dd0a80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4971.000000 708.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dd0ea0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4956.000000 852.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dd1160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4945.000000 837.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dd13a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4970.000000 822.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c3a580" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4390.000000 572.000000) translate(0,12)">Ia:</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c3a8e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4269.000000 939.000000) translate(0,12)">Ia:</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c3b1f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5243.000000 744.000000) translate(0,12)">Ia:</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ba8ab0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3882.000000 958.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ba95a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3888.000000 942.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ba9820" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3872.000000 927.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ba9d40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3882.000000 988.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ba9fc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3888.000000 911.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2baab20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3882.000000 973.000000) translate(0,12)">Ub(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2baae90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3621.000000 539.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bab110" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3626.000000 523.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bab350" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3611.000000 508.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bab590" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3621.000000 569.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bab7d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3627.000000 492.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2baba10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3621.000000 554.000000) translate(0,12)">Ub(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2babd40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4620.000000 929.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2babfc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4626.000000 913.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bac200" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4610.000000 898.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bac440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4620.000000 959.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bac680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4626.000000 882.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bac8c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4620.000000 944.000000) translate(0,12)">Ub(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bacbf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4954.000000 534.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bace70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4960.000000 518.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bad0b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4944.000000 503.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bad2f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4954.000000 564.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bad530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4960.000000 487.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bad770" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4954.000000 549.000000) translate(0,12)">Ub(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2be9d40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5051.000000 1238.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2be9fc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5057.000000 1222.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bea200" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5041.000000 1207.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bea440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5051.000000 1268.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bea680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5057.000000 1191.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bea8c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5051.000000 1253.000000) translate(0,12)">Ub(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2beabf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5074.000000 245.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2beae70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5080.000000 229.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2beb0b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5064.000000 214.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2beb2f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5074.000000 275.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2beb530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5080.000000 198.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2beb770" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5074.000000 260.000000) translate(0,12)">Ub(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_2f055d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3801.000000 75.000000) translate(0,11)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_2f06b90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3790.000000 60.000000) translate(0,11)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_2f07bf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3815.000000 45.000000) translate(0,11)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_2f085e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4070.000000 71.000000) translate(0,11)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_2f08880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4059.000000 56.000000) translate(0,11)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_2f08ac0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4084.000000 41.000000) translate(0,11)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_2f08df0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3642.000000 71.000000) translate(0,11)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_2f09050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3667.000000 56.000000) translate(0,11)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_2f09380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3925.000000 62.000000) translate(0,11)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_2f095e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3950.000000 47.000000) translate(0,11)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_2f09910" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4167.000000 72.000000) translate(0,11)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_2f09b70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4156.000000 57.000000) translate(0,11)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_2f09db0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4181.000000 42.000000) translate(0,11)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_2f0a0e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4285.000000 51.000000) translate(0,11)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_2f0a340" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4274.000000 36.000000) translate(0,11)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_2f0a580" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4299.000000 21.000000) translate(0,11)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_2f0a8b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4432.000000 72.000000) translate(0,11)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_2f0ab10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4457.000000 57.000000) translate(0,11)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_2f0ae40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4559.000000 72.000000) translate(0,11)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_2f0b0a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4584.000000 57.000000) translate(0,11)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_2f0b3d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4698.000000 77.000000) translate(0,11)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_2f0b630" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4687.000000 62.000000) translate(0,11)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_2f0b870" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4712.000000 47.000000) translate(0,11)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_2f0bba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4813.000000 75.000000) translate(0,11)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_2f0be00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4802.000000 60.000000) translate(0,11)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_2f0c040" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4827.000000 45.000000) translate(0,11)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d56a80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4404.000000 1035.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d56cd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4429.000000 1020.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d56ee0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4415.000000 1050.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d57210" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4120.000000 1033.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d57470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4145.000000 1018.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d576b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4131.000000 1048.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 82.000000 15.000000)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dd2910" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3740.000000 815.000000) translate(0,12)">温度1（℃）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dd35d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3739.000000 800.000000) translate(0,12)">温度2（℃）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dd17c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3747.000000 830.000000) translate(0,12)">档位（档）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 646.000000 36.000000)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_327b970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3740.000000 815.000000) translate(0,12)">温度1（℃）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32c9340" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3739.000000 800.000000) translate(0,12)">温度2（℃）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c4ce40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3747.000000 830.000000) translate(0,12)">档位（档）：</text>
   <metadata/></g>
  </g><g id="PolygonFilled_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="none" points="5371,-651 5393,-651 5382,-668 5371,-651 " stroke="rgb(255,255,255)"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-37000">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4559.585714 -991.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5853" ObjectName="SW-CX_QF.CX_QF_197BK"/>
     <cge:Meas_Ref ObjectId="37000"/>
    <cge:TPSR_Ref TObjectID="5853"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37124">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4015.000000 -994.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5882" ObjectName="SW-CX_QF.CX_QF_196BK"/>
     <cge:Meas_Ref ObjectId="37124"/>
    <cge:TPSR_Ref TObjectID="5882"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37014">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4268.300000 -887.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5859" ObjectName="SW-CX_QF.CX_QF_112BK"/>
     <cge:Meas_Ref ObjectId="37014"/>
    <cge:TPSR_Ref TObjectID="5859"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36995">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4559.000000 -572.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5849" ObjectName="SW-CX_QF.CX_QF_002BK"/>
     <cge:Meas_Ref ObjectId="36995"/>
    <cge:TPSR_Ref TObjectID="5849"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36841">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4394.000000 -521.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5841" ObjectName="SW-CX_QF.CX_QF_012BK"/>
     <cge:Meas_Ref ObjectId="36841"/>
    <cge:TPSR_Ref TObjectID="5841"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36816">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3989.000000 -345.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5838" ObjectName="SW-CX_QF.CX_QF_053BK"/>
     <cge:Meas_Ref ObjectId="36816"/>
    <cge:TPSR_Ref TObjectID="5838"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37039">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4471.000000 -350.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5864" ObjectName="SW-CX_QF.CX_QF_057BK"/>
     <cge:Meas_Ref ObjectId="37039"/>
    <cge:TPSR_Ref TObjectID="5864"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37065">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4588.000000 -350.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5869" ObjectName="SW-CX_QF.CX_QF_058BK"/>
     <cge:Meas_Ref ObjectId="37065"/>
    <cge:TPSR_Ref TObjectID="5869"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36736">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4109.000000 -349.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5827" ObjectName="SW-CX_QF.CX_QF_054BK"/>
     <cge:Meas_Ref ObjectId="36736"/>
    <cge:TPSR_Ref TObjectID="5827"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36443">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4981.000000 -854.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5797" ObjectName="SW-CX_QF.CX_QF_301BK"/>
     <cge:Meas_Ref ObjectId="36443"/>
    <cge:TPSR_Ref TObjectID="5797"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36660">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5182.000000 -714.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5818" ObjectName="SW-CX_QF.CX_QF_312BK"/>
     <cge:Meas_Ref ObjectId="36660"/>
    <cge:TPSR_Ref TObjectID="5818"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36989">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4984.000000 -653.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5845" ObjectName="SW-CX_QF.CX_QF_302BK"/>
     <cge:Meas_Ref ObjectId="36989"/>
    <cge:TPSR_Ref TObjectID="5845"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36577">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5185.000000 -918.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5809" ObjectName="SW-CX_QF.CX_QF_353BK"/>
     <cge:Meas_Ref ObjectId="36577"/>
    <cge:TPSR_Ref TObjectID="5809"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36523">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5186.000000 -1161.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5803" ObjectName="SW-CX_QF.CX_QF_351BK"/>
     <cge:Meas_Ref ObjectId="36523"/>
    <cge:TPSR_Ref TObjectID="5803"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36550">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5183.000000 -1009.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5806" ObjectName="SW-CX_QF.CX_QF_352BK"/>
     <cge:Meas_Ref ObjectId="36550"/>
    <cge:TPSR_Ref TObjectID="5806"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37092">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5182.000000 -327.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5874" ObjectName="SW-CX_QF.CX_QF_356BK"/>
     <cge:Meas_Ref ObjectId="37092"/>
    <cge:TPSR_Ref TObjectID="5874"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36604">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5181.000000 -639.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5812" ObjectName="SW-CX_QF.CX_QF_354BK"/>
     <cge:Meas_Ref ObjectId="36604"/>
    <cge:TPSR_Ref TObjectID="5812"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36631">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5180.000000 -553.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5815" ObjectName="SW-CX_QF.CX_QF_355BK"/>
     <cge:Meas_Ref ObjectId="36631"/>
    <cge:TPSR_Ref TObjectID="5815"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36792">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3689.000000 -345.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5835" ObjectName="SW-CX_QF.CX_QF_051BK"/>
     <cge:Meas_Ref ObjectId="36792"/>
    <cge:TPSR_Ref TObjectID="5835"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5425.000000 -973.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36680">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4201.000000 -350.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5819" ObjectName="SW-CX_QF.CX_QF_055BK"/>
     <cge:Meas_Ref ObjectId="36680"/>
    <cge:TPSR_Ref TObjectID="5819"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36446">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4014.000000 -543.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5800" ObjectName="SW-CX_QF.CX_QF_001BK"/>
     <cge:Meas_Ref ObjectId="36446"/>
    <cge:TPSR_Ref TObjectID="5800"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-195419">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3834.000000 -344.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29727" ObjectName="SW-CX_QF.CX_QF_052BK"/>
     <cge:Meas_Ref ObjectId="195419"/>
    <cge:TPSR_Ref TObjectID="29727"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-195441">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4316.000000 -340.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29729" ObjectName="SW-CX_QF.CX_QF_056BK"/>
     <cge:Meas_Ref ObjectId="195441"/>
    <cge:TPSR_Ref TObjectID="29729"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36764">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4725.000000 -349.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5831" ObjectName="SW-CX_QF.CX_QF_059BK"/>
     <cge:Meas_Ref ObjectId="36764"/>
    <cge:TPSR_Ref TObjectID="5831"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36708">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4845.000000 -350.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5823" ObjectName="SW-CX_QF.CX_QF_061BK"/>
     <cge:Meas_Ref ObjectId="36708"/>
    <cge:TPSR_Ref TObjectID="5823"/></metadata>
   </g>
  </g><g id="Transformer_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_QF.CX_QF_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="8581"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3983.000000 -690.000000)" xlink:href="#transformer:shape16_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="8583"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3983.000000 -690.000000)" xlink:href="#transformer:shape16_1"/>
    </g>
    <g id="WD-2">
     <metadata>
      <cge:PSR_Ref ObjectId="8585"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3983.000000 -690.000000)" xlink:href="#transformer:shape16-2"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="5885" ObjectName="TF-CX_QF.CX_QF_1T"/>
    <cge:TPSR_Ref TObjectID="5885"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_QF.CX_QF_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="8588"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4531.000000 -691.000000)" xlink:href="#transformer:shape16_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="8590"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4531.000000 -691.000000)" xlink:href="#transformer:shape16_1"/>
    </g>
    <g id="WD-2">
     <metadata>
      <cge:PSR_Ref ObjectId="8592"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4531.000000 -691.000000)" xlink:href="#transformer:shape16-2"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="5886" ObjectName="TF-CX_QF.CX_QF_2T"/>
    <cge:TPSR_Ref TObjectID="5886"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_QF.CX_QF_3IIM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5123,-281 5123,-712 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="5780" ObjectName="BS-CX_QF.CX_QF_3IIM"/>
    <cge:TPSR_Ref TObjectID="5780"/></metadata>
   <polyline fill="none" opacity="0" points="5123,-281 5123,-712 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_QF.CX_QF_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5123,-751 5123,-1171 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="5779" ObjectName="BS-CX_QF.CX_QF_3IM"/>
    <cge:TPSR_Ref TObjectID="5779"/></metadata>
   <polyline fill="none" opacity="0" points="5123,-751 5123,-1171 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_QF.CX_QF_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3633,-465 4389,-465 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="5775" ObjectName="BS-CX_QF.CX_QF_9IM"/>
    <cge:TPSR_Ref TObjectID="5775"/></metadata>
   <polyline fill="none" opacity="0" points="3633,-465 4389,-465 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_QF.CX_QF_9IIM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4449,-465 4974,-465 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="5776" ObjectName="BS-CX_QF.CX_QF_9IIM"/>
    <cge:TPSR_Ref TObjectID="5776"/></metadata>
   <polyline fill="none" opacity="0" points="4449,-465 4974,-465 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_QF.CX_QF_1IM">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4017,-897 4034,-897 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="5777" ObjectName="BS-CX_QF.CX_QF_1IM"/>
    <cge:TPSR_Ref TObjectID="5777"/></metadata>
   <polyline fill="none" opacity="0" points="4017,-897 4034,-897 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_QF.CX_QF_1IIM">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4555,-897 4574,-897 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="5778" ObjectName="BS-CX_QF.CX_QF_1IIM"/>
    <cge:TPSR_Ref TObjectID="5778"/></metadata>
   <polyline fill="none" opacity="0" points="4555,-897 4574,-897 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-CX_QF.CX_QF_Cb1">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3671.000000 -84.000000)" xlink:href="#capacitor:shape14"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15555" ObjectName="CB-CX_QF.CX_QF_Cb1"/>
    <cge:TPSR_Ref TObjectID="15555"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-CX_QF.CX_QF_Cb3">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3971.000000 -82.000000)" xlink:href="#capacitor:shape14"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15558" ObjectName="CB-CX_QF.CX_QF_Cb3"/>
    <cge:TPSR_Ref TObjectID="15558"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-CX_QF.CX_QF_Cb4">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4570.000000 -110.000000)" xlink:href="#capacitor:shape14"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15557" ObjectName="CB-CX_QF.CX_QF_Cb4"/>
    <cge:TPSR_Ref TObjectID="15557"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-CX_QF.CX_QF_Cb2">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4453.000000 -110.000000)" xlink:href="#capacitor:shape14"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15556" ObjectName="CB-CX_QF.CX_QF_Cb2"/>
    <cge:TPSR_Ref TObjectID="15556"/></metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3827.000000 -162.000000)" xlink:href="#transformer2:shape57_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3827.000000 -162.000000)" xlink:href="#transformer2:shape57_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2bcf9d0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4525.585714 -1141.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d4c740">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3981.000000 -1141.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d4cf10">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3914.000000 -741.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2cedc50">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4018.000000 -328.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2cefc10">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3960.000000 -207.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c281a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4145.000000 -217.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c5a5f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4237.000000 -218.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2cda6b0">
    <use class="BV-35KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4241.000000 -687.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c81ac0">
    <use class="BV-110KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 4748.000000 -769.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c86c20">
    <use class="BV-35KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 5243.500000 -834.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c878a0">
    <use class="BV-35KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 5259.000000 -813.000000)" xlink:href="#lightningRod:shape29"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d45ff0">
    <use class="BV-35KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 5309.500000 -930.500000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d46ff0">
    <use class="BV-35KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 5352.500000 -972.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2caf6a0">
    <use class="BV-35KV" transform="matrix(-0.000000 -1.000000 0.965517 -0.000000 5310.517241 -1173.500000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2cfe2c0">
    <use class="BV-35KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 5246.500000 -1099.500000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c62e10">
    <use class="BV-35KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 5307.500000 -1021.500000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ccbc20">
    <use class="BV-35KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 5304.500000 -565.500000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2cceee0">
    <use class="BV-35KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 5241.500000 -470.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ccfb80">
    <use class="BV-35KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 5257.000000 -449.000000)" xlink:href="#lightningRod:shape29"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d22530">
    <use class="BV-35KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 5244.500000 -406.500000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d234c0">
    <use class="BV-35KV" transform="matrix(1.840000 -0.000000 0.000000 1.818182 5312.000000 -403.000000)" xlink:href="#lightningRod:shape45"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d23c60">
    <use class="BV-35KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 5305.500000 -651.500000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bb6480">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3880.000000 -680.000000)" xlink:href="#lightningRod:shape54"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bb6ff0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4761.000000 -831.000000)" xlink:href="#lightningRod:shape54"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bb7ba0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3993.000000 -272.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bb8e90">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4205.000000 -288.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bb9f20">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4113.000000 -287.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bbb470">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4592.000000 -289.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bbc500">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4475.000000 -289.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c95e90">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3895.000000 -1109.000000)" xlink:href="#lightningRod:shape106"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c97200">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4439.585714 -1109.000000)" xlink:href="#lightningRod:shape106"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bcabf0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3718.000000 -328.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bcd010">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3660.000000 -207.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c140b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3693.000000 -272.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c3e3a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3780.000000 -549.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c3eac0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5256.000000 -1068.000000)" xlink:href="#lightningRod:shape132"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c3fa10">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5261.000000 -376.000000)" xlink:href="#lightningRod:shape132"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c40960">
    <use class="BV-35KV" transform="matrix(2.692308 -0.000000 0.000000 -1.405594 4272.000000 -680.000000)" xlink:href="#lightningRod:shape41"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c41e90">
    <use class="BV-35KV" transform="matrix(1.840000 -0.000000 0.000000 1.818182 5314.000000 -1095.000000)" xlink:href="#lightningRod:shape45"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ba6590">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5412.500000 -950.500000)" xlink:href="#lightningRod:shape25"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c01640">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3815.000000 -534.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ea57c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3863.000000 -327.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ea6570">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3838.000000 -271.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ec4020">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4320.000000 -143.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ec75e0">
    <use class="BV-10KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 4343.500000 -216.500000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ecab30">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4867.000000 -546.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ece080">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4902.000000 -531.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ed57f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4729.000000 -287.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2edd790">
    <use class="BV-10KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 4741.500000 -209.500000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ee21b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4849.000000 -288.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ee8d40">
    <use class="BV-10KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 4861.500000 -210.500000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-57366" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3925.000000 -799.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57366" ObjectName="CX_QF:CX_QF_1T_Tmp"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-57365" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3925.000000 -783.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57365" ObjectName="CX_QF:CX_QF_1T_Tmp1"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-36312" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4485.000000 -781.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36312" ObjectName="CX_QF:CX_QF_2T_Tmp"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-36313" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4487.000000 -764.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36313" ObjectName="CX_QF:CX_QF_2T_Tmp1"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3205.000000 -1118.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-62627" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3262.538462 -949.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="62627" ObjectName="CX_QF:CX_QF_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-79736" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3262.538462 -908.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79736" ObjectName="CX_QF:CX_QF_sumQ"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-62627" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3263.538462 -1032.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="62627" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-62627" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3262.538462 -990.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="62627" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="1" id="ME-56546" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3960.000000 -1202.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="56546" ObjectName="CX_QF:CX_QF_196BK_U"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="1" id="ME-36317" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4502.000000 -1201.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36317" ObjectName="CX_QF:CX_QF_197BK_U"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="1" id="ME-228855" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3961.000000 -1178.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="228855" ObjectName="CX_QF:CX_QF_196BK_Uab"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="1" id="ME-228856" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4503.000000 -1178.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="228856" ObjectName="CX_QF:CX_QF_197BK_Uab"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="1" id="ME-36215" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5363.000000 -1218.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36215" ObjectName="CX_QF:CX_QF_351BK_U"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="1" id="ME-36223" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5419.000000 -1047.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36223" ObjectName="CX_QF:CX_QF_352BK_U"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="1" id="ME-36231" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5549.000000 -954.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36231" ObjectName="CX_QF:CX_QF_353BK_U"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="1" id="ME-36239" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5441.000000 -689.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36239" ObjectName="CX_QF:CX_QF_354BK_U"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="1" id="ME-36247" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5420.000000 -589.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36247" ObjectName="CX_QF:CX_QF_355BK_U"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-56547" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4192.000000 -1044.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="56547" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5882"/>
     <cge:Term_Ref ObjectID="8575"/>
    <cge:TPSR_Ref TObjectID="5882"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-56548" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4192.000000 -1044.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="56548" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5882"/>
     <cge:Term_Ref ObjectID="8575"/>
    <cge:TPSR_Ref TObjectID="5882"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-36332" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4192.000000 -1044.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36332" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5882"/>
     <cge:Term_Ref ObjectID="8575"/>
    <cge:TPSR_Ref TObjectID="5882"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-36309" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4626.000000 -590.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36309" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5849"/>
     <cge:Term_Ref ObjectID="8509"/>
    <cge:TPSR_Ref TObjectID="5849"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-36310" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4626.000000 -590.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36310" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5849"/>
     <cge:Term_Ref ObjectID="8509"/>
    <cge:TPSR_Ref TObjectID="5849"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-36306" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4626.000000 -590.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36306" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5849"/>
     <cge:Term_Ref ObjectID="8509"/>
    <cge:TPSR_Ref TObjectID="5849"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-36275" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4122.000000 -69.500000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36275" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5827"/>
     <cge:Term_Ref ObjectID="8465"/>
    <cge:TPSR_Ref TObjectID="5827"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-36276" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4122.000000 -69.500000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36276" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5827"/>
     <cge:Term_Ref ObjectID="8465"/>
    <cge:TPSR_Ref TObjectID="5827"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-36271" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4122.000000 -69.500000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36271" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5827"/>
     <cge:Term_Ref ObjectID="8465"/>
    <cge:TPSR_Ref TObjectID="5827"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-36216" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5472.000000 -1172.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36216" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5803"/>
     <cge:Term_Ref ObjectID="8417"/>
    <cge:TPSR_Ref TObjectID="5803"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-36217" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5472.000000 -1172.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36217" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5803"/>
     <cge:Term_Ref ObjectID="8417"/>
    <cge:TPSR_Ref TObjectID="5803"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-36212" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5472.000000 -1172.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36212" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5803"/>
     <cge:Term_Ref ObjectID="8417"/>
    <cge:TPSR_Ref TObjectID="5803"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-36224" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5473.000000 -1018.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36224" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5806"/>
     <cge:Term_Ref ObjectID="8423"/>
    <cge:TPSR_Ref TObjectID="5806"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-36225" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5473.000000 -1018.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36225" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5806"/>
     <cge:Term_Ref ObjectID="8423"/>
    <cge:TPSR_Ref TObjectID="5806"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-36220" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5473.000000 -1018.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36220" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5806"/>
     <cge:Term_Ref ObjectID="8423"/>
    <cge:TPSR_Ref TObjectID="5806"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-36232" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5472.000000 -924.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36232" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5809"/>
     <cge:Term_Ref ObjectID="8429"/>
    <cge:TPSR_Ref TObjectID="5809"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-36233" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5472.000000 -924.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36233" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5809"/>
     <cge:Term_Ref ObjectID="8429"/>
    <cge:TPSR_Ref TObjectID="5809"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-36228" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5472.000000 -924.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36228" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5809"/>
     <cge:Term_Ref ObjectID="8429"/>
    <cge:TPSR_Ref TObjectID="5809"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-36252" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5261.000000 -743.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36252" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5818"/>
     <cge:Term_Ref ObjectID="8447"/>
    <cge:TPSR_Ref TObjectID="5818"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-36240" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5472.000000 -650.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36240" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5812"/>
     <cge:Term_Ref ObjectID="8435"/>
    <cge:TPSR_Ref TObjectID="5812"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-36241" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5472.000000 -650.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36241" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5812"/>
     <cge:Term_Ref ObjectID="8435"/>
    <cge:TPSR_Ref TObjectID="5812"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-36236" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5472.000000 -650.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36236" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5812"/>
     <cge:Term_Ref ObjectID="8435"/>
    <cge:TPSR_Ref TObjectID="5812"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-36248" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5472.000000 -565.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36248" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5815"/>
     <cge:Term_Ref ObjectID="8441"/>
    <cge:TPSR_Ref TObjectID="5815"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-36249" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5472.000000 -565.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36249" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5815"/>
     <cge:Term_Ref ObjectID="8441"/>
    <cge:TPSR_Ref TObjectID="5815"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-36244" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5472.000000 -565.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36244" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5815"/>
     <cge:Term_Ref ObjectID="8441"/>
    <cge:TPSR_Ref TObjectID="5815"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-36328" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5472.000000 -334.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36328" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5874"/>
     <cge:Term_Ref ObjectID="8559"/>
    <cge:TPSR_Ref TObjectID="5874"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-36329" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5472.000000 -334.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36329" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5874"/>
     <cge:Term_Ref ObjectID="8559"/>
    <cge:TPSR_Ref TObjectID="5874"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-36325" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5472.000000 -334.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36325" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5874"/>
     <cge:Term_Ref ObjectID="8559"/>
    <cge:TPSR_Ref TObjectID="5874"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-36201" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5016.000000 -852.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36201" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5797"/>
     <cge:Term_Ref ObjectID="8405"/>
    <cge:TPSR_Ref TObjectID="5797"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-36202" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5016.000000 -852.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36202" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5797"/>
     <cge:Term_Ref ObjectID="8405"/>
    <cge:TPSR_Ref TObjectID="5797"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-36194" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5016.000000 -852.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36194" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5797"/>
     <cge:Term_Ref ObjectID="8405"/>
    <cge:TPSR_Ref TObjectID="5797"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-36303" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5016.000000 -738.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36303" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5845"/>
     <cge:Term_Ref ObjectID="8501"/>
    <cge:TPSR_Ref TObjectID="5845"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-36304" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5016.000000 -738.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36304" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5845"/>
     <cge:Term_Ref ObjectID="8501"/>
    <cge:TPSR_Ref TObjectID="5845"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-56535" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5016.000000 -738.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="56535" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5845"/>
     <cge:Term_Ref ObjectID="8501"/>
    <cge:TPSR_Ref TObjectID="5845"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-36288" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3705.000000 -67.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36288" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5835"/>
     <cge:Term_Ref ObjectID="8481"/>
    <cge:TPSR_Ref TObjectID="5835"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-36287" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3705.000000 -67.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36287" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5835"/>
     <cge:Term_Ref ObjectID="8481"/>
    <cge:TPSR_Ref TObjectID="5835"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-36291" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3989.000000 -61.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36291" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5838"/>
     <cge:Term_Ref ObjectID="8487"/>
    <cge:TPSR_Ref TObjectID="5838"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-36290" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3989.000000 -61.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36290" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5838"/>
     <cge:Term_Ref ObjectID="8487"/>
    <cge:TPSR_Ref TObjectID="5838"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-36323" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4622.000000 -72.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36323" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5869"/>
     <cge:Term_Ref ObjectID="8549"/>
    <cge:TPSR_Ref TObjectID="5869"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-36322" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4622.000000 -72.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36322" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5869"/>
     <cge:Term_Ref ObjectID="8549"/>
    <cge:TPSR_Ref TObjectID="5869"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-36320" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4494.000000 -72.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36320" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5864"/>
     <cge:Term_Ref ObjectID="8539"/>
    <cge:TPSR_Ref TObjectID="5864"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-36319" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4494.000000 -72.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36319" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5864"/>
     <cge:Term_Ref ObjectID="8539"/>
    <cge:TPSR_Ref TObjectID="5864"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-36142" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3678.000000 -569.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36142" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5775"/>
     <cge:Term_Ref ObjectID="8367"/>
    <cge:TPSR_Ref TObjectID="5775"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-36143" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3678.000000 -569.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36143" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5775"/>
     <cge:Term_Ref ObjectID="8367"/>
    <cge:TPSR_Ref TObjectID="5775"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-36144" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3678.000000 -569.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36144" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5775"/>
     <cge:Term_Ref ObjectID="8367"/>
    <cge:TPSR_Ref TObjectID="5775"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-36148" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3678.000000 -569.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36148" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5775"/>
     <cge:Term_Ref ObjectID="8367"/>
    <cge:TPSR_Ref TObjectID="5775"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-36150" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3678.000000 -569.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36150" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5775"/>
     <cge:Term_Ref ObjectID="8367"/>
    <cge:TPSR_Ref TObjectID="5775"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-36156" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3678.000000 -569.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36156" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5775"/>
     <cge:Term_Ref ObjectID="8367"/>
    <cge:TPSR_Ref TObjectID="5775"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-36145" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5008.000000 -563.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36145" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5776"/>
     <cge:Term_Ref ObjectID="8368"/>
    <cge:TPSR_Ref TObjectID="5776"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-36146" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5008.000000 -563.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36146" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5776"/>
     <cge:Term_Ref ObjectID="8368"/>
    <cge:TPSR_Ref TObjectID="5776"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-36147" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5008.000000 -563.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36147" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5776"/>
     <cge:Term_Ref ObjectID="8368"/>
    <cge:TPSR_Ref TObjectID="5776"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-36149" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5008.000000 -563.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36149" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5776"/>
     <cge:Term_Ref ObjectID="8368"/>
    <cge:TPSR_Ref TObjectID="5776"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-36153" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5008.000000 -563.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36153" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5776"/>
     <cge:Term_Ref ObjectID="8368"/>
    <cge:TPSR_Ref TObjectID="5776"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-36157" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5008.000000 -563.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36157" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5776"/>
     <cge:Term_Ref ObjectID="8368"/>
    <cge:TPSR_Ref TObjectID="5776"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-36174" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5103.000000 -1268.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36174" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5779"/>
     <cge:Term_Ref ObjectID="8371"/>
    <cge:TPSR_Ref TObjectID="5779"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-36175" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5103.000000 -1268.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36175" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5779"/>
     <cge:Term_Ref ObjectID="8371"/>
    <cge:TPSR_Ref TObjectID="5779"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-36176" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5103.000000 -1268.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36176" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5779"/>
     <cge:Term_Ref ObjectID="8371"/>
    <cge:TPSR_Ref TObjectID="5779"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-36180" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5103.000000 -1268.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36180" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5779"/>
     <cge:Term_Ref ObjectID="8371"/>
    <cge:TPSR_Ref TObjectID="5779"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-36182" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5103.000000 -1268.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36182" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5779"/>
     <cge:Term_Ref ObjectID="8371"/>
    <cge:TPSR_Ref TObjectID="5779"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-36188" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5103.000000 -1268.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36188" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5779"/>
     <cge:Term_Ref ObjectID="8371"/>
    <cge:TPSR_Ref TObjectID="5779"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-36177" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5128.000000 -274.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36177" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5780"/>
     <cge:Term_Ref ObjectID="8372"/>
    <cge:TPSR_Ref TObjectID="5780"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-36178" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5128.000000 -274.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36178" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5780"/>
     <cge:Term_Ref ObjectID="8372"/>
    <cge:TPSR_Ref TObjectID="5780"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-36179" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5128.000000 -274.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36179" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5780"/>
     <cge:Term_Ref ObjectID="8372"/>
    <cge:TPSR_Ref TObjectID="5780"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-36181" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5128.000000 -274.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36181" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5780"/>
     <cge:Term_Ref ObjectID="8372"/>
    <cge:TPSR_Ref TObjectID="5780"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-36185" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5128.000000 -274.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36185" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5780"/>
     <cge:Term_Ref ObjectID="8372"/>
    <cge:TPSR_Ref TObjectID="5780"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-36189" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5128.000000 -274.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36189" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5780"/>
     <cge:Term_Ref ObjectID="8372"/>
    <cge:TPSR_Ref TObjectID="5780"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="44" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-36190" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3950.000000 -815.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36190" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5885"/>
     <cge:Term_Ref ObjectID="8584"/>
    <cge:TPSR_Ref TObjectID="5885"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="44" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-36296" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4509.000000 -797.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36296" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5886"/>
     <cge:Term_Ref ObjectID="8591"/>
    <cge:TPSR_Ref TObjectID="5886"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-36293" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4410.000000 -572.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36293" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5841"/>
     <cge:Term_Ref ObjectID="8493"/>
    <cge:TPSR_Ref TObjectID="5841"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-36314" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4287.300000 -938.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36314" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5859"/>
     <cge:Term_Ref ObjectID="8529"/>
    <cge:TPSR_Ref TObjectID="5859"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="43" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-70601" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3951.000000 -884.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="70601" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5794"/>
     <cge:Term_Ref ObjectID="8399"/>
    <cge:TPSR_Ref TObjectID="5794"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="43" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-70602" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3951.000000 -884.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="70602" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5794"/>
     <cge:Term_Ref ObjectID="8399"/>
    <cge:TPSR_Ref TObjectID="5794"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="43" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-70605" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3951.000000 -884.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="70605" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5794"/>
     <cge:Term_Ref ObjectID="8399"/>
    <cge:TPSR_Ref TObjectID="5794"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="43" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-70606" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4498.000000 -884.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="70606" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5842"/>
     <cge:Term_Ref ObjectID="8495"/>
    <cge:TPSR_Ref TObjectID="5842"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="43" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-70604" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4498.000000 -884.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="70604" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5842"/>
     <cge:Term_Ref ObjectID="8495"/>
    <cge:TPSR_Ref TObjectID="5842"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="43" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-70603" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4498.000000 -884.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="70603" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5842"/>
     <cge:Term_Ref ObjectID="8495"/>
    <cge:TPSR_Ref TObjectID="5842"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-36158" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3936.000000 -988.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36158" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5777"/>
     <cge:Term_Ref ObjectID="8369"/>
    <cge:TPSR_Ref TObjectID="5777"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-36159" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3936.000000 -988.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36159" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5777"/>
     <cge:Term_Ref ObjectID="8369"/>
    <cge:TPSR_Ref TObjectID="5777"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-36160" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3936.000000 -988.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36160" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5777"/>
     <cge:Term_Ref ObjectID="8369"/>
    <cge:TPSR_Ref TObjectID="5777"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-36164" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3936.000000 -988.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36164" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5777"/>
     <cge:Term_Ref ObjectID="8369"/>
    <cge:TPSR_Ref TObjectID="5777"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-36166" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3936.000000 -988.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36166" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5777"/>
     <cge:Term_Ref ObjectID="8369"/>
    <cge:TPSR_Ref TObjectID="5777"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-36172" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3936.000000 -988.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36172" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5777"/>
     <cge:Term_Ref ObjectID="8369"/>
    <cge:TPSR_Ref TObjectID="5777"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-36161" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4672.000000 -959.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36161" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5778"/>
     <cge:Term_Ref ObjectID="8370"/>
    <cge:TPSR_Ref TObjectID="5778"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-36162" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4672.000000 -959.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36162" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5778"/>
     <cge:Term_Ref ObjectID="8370"/>
    <cge:TPSR_Ref TObjectID="5778"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-36163" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4672.000000 -959.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36163" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5778"/>
     <cge:Term_Ref ObjectID="8370"/>
    <cge:TPSR_Ref TObjectID="5778"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-36165" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4672.000000 -959.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36165" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5778"/>
     <cge:Term_Ref ObjectID="8370"/>
    <cge:TPSR_Ref TObjectID="5778"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-36169" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4672.000000 -959.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36169" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5778"/>
     <cge:Term_Ref ObjectID="8370"/>
    <cge:TPSR_Ref TObjectID="5778"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-36173" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4672.000000 -959.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36173" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5778"/>
     <cge:Term_Ref ObjectID="8370"/>
    <cge:TPSR_Ref TObjectID="5778"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-36259" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4219.000000 -69.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36259" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5819"/>
     <cge:Term_Ref ObjectID="8449"/>
    <cge:TPSR_Ref TObjectID="5819"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-36260" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4219.000000 -69.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36260" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5819"/>
     <cge:Term_Ref ObjectID="8449"/>
    <cge:TPSR_Ref TObjectID="5819"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-36255" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4219.000000 -69.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36255" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5819"/>
     <cge:Term_Ref ObjectID="8449"/>
    <cge:TPSR_Ref TObjectID="5819"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-36208" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4066.000000 -590.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36208" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5800"/>
     <cge:Term_Ref ObjectID="8411"/>
    <cge:TPSR_Ref TObjectID="5800"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-36209" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4066.000000 -590.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36209" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5800"/>
     <cge:Term_Ref ObjectID="8411"/>
    <cge:TPSR_Ref TObjectID="5800"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-36205" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4066.000000 -590.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36205" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5800"/>
     <cge:Term_Ref ObjectID="8411"/>
    <cge:TPSR_Ref TObjectID="5800"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-195458" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3854.000000 -74.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195458" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29727"/>
     <cge:Term_Ref ObjectID="42398"/>
    <cge:TPSR_Ref TObjectID="29727"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-195459" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3854.000000 -74.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195459" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29727"/>
     <cge:Term_Ref ObjectID="42398"/>
    <cge:TPSR_Ref TObjectID="29727"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-195452" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3854.000000 -74.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195452" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29727"/>
     <cge:Term_Ref ObjectID="42398"/>
    <cge:TPSR_Ref TObjectID="29727"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-195468" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4337.000000 -49.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195468" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29729"/>
     <cge:Term_Ref ObjectID="42402"/>
    <cge:TPSR_Ref TObjectID="29729"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-195469" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4337.000000 -49.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195469" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29729"/>
     <cge:Term_Ref ObjectID="42402"/>
    <cge:TPSR_Ref TObjectID="29729"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-195462" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4337.000000 -49.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195462" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29729"/>
     <cge:Term_Ref ObjectID="42402"/>
    <cge:TPSR_Ref TObjectID="29729"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-36283" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4749.000000 -76.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36283" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5831"/>
     <cge:Term_Ref ObjectID="8473"/>
    <cge:TPSR_Ref TObjectID="5831"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-36284" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4749.000000 -76.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36284" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5831"/>
     <cge:Term_Ref ObjectID="8473"/>
    <cge:TPSR_Ref TObjectID="5831"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-36279" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4749.000000 -76.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36279" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5831"/>
     <cge:Term_Ref ObjectID="8473"/>
    <cge:TPSR_Ref TObjectID="5831"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-36267" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4864.000000 -74.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36267" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5823"/>
     <cge:Term_Ref ObjectID="8457"/>
    <cge:TPSR_Ref TObjectID="5823"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-36268" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4864.000000 -74.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36268" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5823"/>
     <cge:Term_Ref ObjectID="8457"/>
    <cge:TPSR_Ref TObjectID="5823"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-36263" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4864.000000 -74.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="36263" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5823"/>
     <cge:Term_Ref ObjectID="8457"/>
    <cge:TPSR_Ref TObjectID="5823"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-56541" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4477.000000 -1044.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="56541" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5853"/>
     <cge:Term_Ref ObjectID="8517"/>
    <cge:TPSR_Ref TObjectID="5853"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-56542" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4477.000000 -1044.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="56542" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5853"/>
     <cge:Term_Ref ObjectID="8517"/>
    <cge:TPSR_Ref TObjectID="5853"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-56538" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4477.000000 -1044.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="56538" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5853"/>
     <cge:Term_Ref ObjectID="8517"/>
    <cge:TPSR_Ref TObjectID="5853"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="3217" y="-1177"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="3217" y="-1177"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3168" y="-1194"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3168" y="-1194"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <polygon fill="rgb(255,255,255)" points="3402,-1187 3399,-1190 3399,-1122 3402,-1125 3402,-1187" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(255,255,255)" points="3402,-1187 3399,-1190 3470,-1190 3467,-1187 3402,-1187" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(255,123,239)" points="3402,-1125 3399,-1122 3470,-1122 3467,-1125 3402,-1125" stroke="rgb(255,123,239)"/>
     <polygon fill="rgb(255,123,239)" points="3467,-1187 3470,-1190 3470,-1122 3467,-1125 3467,-1187" stroke="rgb(255,123,239)"/>
     <rect fill="rgb(255,255,255)" height="62" stroke="rgb(255,255,255)" width="65" x="3402" y="-1187"/>
     <rect fill="none" height="62" qtmmishow="hidden" stroke="rgb(0,0,0)" width="65" x="3402" y="-1187"/>
    </a>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="22" qtmmishow="hidden" width="22" x="5372" y="-1195"/>
    </a>
   <metadata/><rect fill="white" height="22" opacity="0" stroke="white" transform="" width="22" x="5372" y="-1195"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="19" qtmmishow="hidden" width="24" x="5370" y="-669"/>
    </a>
   <metadata/><rect fill="white" height="19" opacity="0" stroke="white" transform="" width="24" x="5370" y="-669"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="63" x="4082" y="-759"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="63" x="4082" y="-759"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="62" x="4633" y="-756"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="62" x="4633" y="-756"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="3663" y="-374"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="3663" y="-374"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="3962" y="-376"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="3962" y="-376"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="4405" y="-555"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="4405" y="-555"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4080" y="-378"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4080" y="-378"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4606" y="-379"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4606" y="-379"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4491" y="-379"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4491" y="-379"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="5192" y="-342"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="5192" y="-342"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="5191" y="-568"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="5191" y="-568"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="5189" y="-656"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="5189" y="-656"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="5200" y="-743"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="5200" y="-743"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="5195" y="-933"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="5195" y="-933"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="5193" y="-1024"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="5193" y="-1024"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="5197" y="-1176"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="5197" y="-1176"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4577" y="-1020"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4577" y="-1020"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4034" y="-1023"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4034" y="-1023"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="4279" y="-921"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="4279" y="-921"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="25" qtmmishow="hidden" width="96" x="3185" y="-760"/>
    </a>
   <metadata/><rect fill="white" height="25" opacity="0" stroke="white" transform="" width="96" x="3185" y="-760"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4173" y="-379"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4173" y="-379"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4291" y="-369"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4291" y="-369"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4743" y="-378"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4743" y="-378"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4863" y="-379"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4863" y="-379"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="3852" y="-373"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="3852" y="-373"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3497" y="-1190"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3497" y="-1190"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3495" y="-1147"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3495" y="-1147"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="25" qtmmishow="hidden" width="96" x="3184" y="-702"/>
    </a>
   <metadata/><rect fill="white" height="25" opacity="0" stroke="white" transform="" width="96" x="3184" y="-702"/></g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="3217" y="-1177"/></g>
   <g href="cx_索引_接线图_局属变110.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3168" y="-1194"/></g>
   <g href="AVC勤丰站.svg" style="fill-opacity:0"><rect height="62" qtmmishow="hidden" stroke="rgb(0,0,0)" width="65" x="3402" y="-1187"/></g>
   <g href="cx_地调_重要用电用户表.svg" style="fill-opacity:0"><rect height="22" qtmmishow="hidden" width="22" x="5372" y="-1195"/></g>
   <g href="cx_地调_重要用电用户表.svg" style="fill-opacity:0"><rect height="19" qtmmishow="hidden" width="24" x="5370" y="-669"/></g>
   <g href="110kV勤丰变1号主变间隔接线图.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="63" x="4082" y="-759"/></g>
   <g href="110kV勤丰变2号主变间隔接线图.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="62" x="4633" y="-756"/></g>
   <g href="110kV勤丰变10kV1号电容器051间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="3663" y="-374"/></g>
   <g href="110kV勤丰变10kV3号电容器053间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="3962" y="-376"/></g>
   <g href="110kV勤丰变10kV分段012间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="4405" y="-555"/></g>
   <g href="110kV勤丰变10kV电瓶厂线054间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4080" y="-378"/></g>
   <g href="110kV勤丰变10kV4号电容器058间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4606" y="-379"/></g>
   <g href="110kV勤丰变10kV2号电容器057间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4491" y="-379"/></g>
   <g href="110kV勤丰变35KV备用出线356断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="5192" y="-342"/></g>
   <g href="110kV勤丰变35kV勤土线355断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="5191" y="-568"/></g>
   <g href="110kV勤丰变35kV羊街线354断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="5189" y="-656"/></g>
   <g href="110kV勤丰变35kV分段312断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="5200" y="-743"/></g>
   <g href="110kV勤丰变35kV碧城线353断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="5195" y="-933"/></g>
   <g href="110kV勤丰变35kV仁兴线352断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="5193" y="-1024"/></g>
   <g href="110kV勤丰变35kV果勤罗线351断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="5197" y="-1176"/></g>
   <g href="110kV勤丰变110KV禄勤老线197间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4577" y="-1020"/></g>
   <g href="110kV勤丰变110kV腰勤线196间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4034" y="-1023"/></g>
   <g href="110kV勤丰变110KV母联112断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="4279" y="-921"/></g>
   <g href="110kV勤丰变GG虚设备间隔接线图_0.svg" style="fill-opacity:0"><rect height="25" qtmmishow="hidden" width="96" x="3185" y="-760"/></g>
   <g href="110kV勤丰变10kV勤碧Ⅰ回线055间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4173" y="-379"/></g>
   <g href="110kV勤丰变10kV昆广高速专线056间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4291" y="-369"/></g>
   <g href="110kV勤丰变10kV勤丰Ⅰ回线059间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4743" y="-378"/></g>
   <g href="110kV勤丰变10kV勤碧Ⅱ回线061间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4863" y="-379"/></g>
   <g href="110kV勤丰变10kV1号站用变052间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="3852" y="-373"/></g>
   <g href="cx_索引_接线图_局属变110.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3497" y="-1190"/></g>
   <g href="cx_配调_配网接线图110.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3495" y="-1147"/></g>
   <g href="110kV勤丰变隔刀开关远方遥控清单.svg" style="fill-opacity:0"><rect height="25" qtmmishow="hidden" width="96" x="3184" y="-702"/></g>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.1875" x1="3753" x2="3756" y1="-533" y2="-533"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.344531" x1="3751" x2="3758" y1="-530" y2="-530"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.305149" x1="3755" x2="3755" y1="-527" y2="-502"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.560509" x1="3760" x2="3749" y1="-527" y2="-527"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.199521" x1="3755" x2="3772" y1="-502" y2="-502"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.187784" x1="3772" x2="3772" y1="-510" y2="-494"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.1875" x1="3731" x2="3734" y1="-398" y2="-398"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.344531" x1="3729" x2="3736" y1="-401" y2="-401"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.305149" x1="3733" x2="3733" y1="-429" y2="-404"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.560509" x1="3738" x2="3727" y1="-404" y2="-404"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.211257" x1="3715" x2="3733" y1="-429" y2="-429"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.187784" x1="3715" x2="3715" y1="-437" y2="-421"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.1875" x1="3876" x2="3879" y1="-397" y2="-397"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.344531" x1="3874" x2="3881" y1="-400" y2="-400"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.305149" x1="3878" x2="3878" y1="-428" y2="-403"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.560509" x1="3883" x2="3872" y1="-403" y2="-403"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.211257" x1="3860" x2="3878" y1="-428" y2="-428"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.187784" x1="3860" x2="3860" y1="-436" y2="-420"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.1875" x1="4031" x2="4034" y1="-397" y2="-397"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.344531" x1="4029" x2="4036" y1="-400" y2="-400"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.305149" x1="4033" x2="4033" y1="-428" y2="-403"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.560509" x1="4038" x2="4027" y1="-403" y2="-403"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.211257" x1="4015" x2="4033" y1="-428" y2="-428"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.187784" x1="4015" x2="4015" y1="-436" y2="-420"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.1875" x1="4151" x2="4154" y1="-397" y2="-397"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.344531" x1="4149" x2="4156" y1="-400" y2="-400"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.305149" x1="4153" x2="4153" y1="-428" y2="-403"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.560509" x1="4158" x2="4147" y1="-403" y2="-403"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.211257" x1="4135" x2="4153" y1="-428" y2="-428"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.187784" x1="4135" x2="4135" y1="-436" y2="-420"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.1875" x1="4243" x2="4246" y1="-396" y2="-396"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.344531" x1="4241" x2="4248" y1="-399" y2="-399"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.305149" x1="4245" x2="4245" y1="-427" y2="-402"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.560509" x1="4250" x2="4239" y1="-402" y2="-402"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.211257" x1="4227" x2="4245" y1="-427" y2="-427"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.187784" x1="4227" x2="4227" y1="-435" y2="-419"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.1875" x1="4840" x2="4843" y1="-530" y2="-530"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.344531" x1="4838" x2="4845" y1="-527" y2="-527"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.305149" x1="4842" x2="4842" y1="-524" y2="-499"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.560509" x1="4847" x2="4836" y1="-524" y2="-524"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.199521" x1="4842" x2="4859" y1="-499" y2="-499"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.187784" x1="4859" x2="4859" y1="-507" y2="-491"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.1875" x1="4767" x2="4770" y1="-395" y2="-395"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.344531" x1="4765" x2="4772" y1="-398" y2="-398"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.305149" x1="4769" x2="4769" y1="-426" y2="-401"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.560509" x1="4774" x2="4763" y1="-401" y2="-401"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.211257" x1="4751" x2="4769" y1="-426" y2="-426"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.187784" x1="4751" x2="4751" y1="-434" y2="-418"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.1875" x1="4887" x2="4890" y1="-396" y2="-396"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.344531" x1="4885" x2="4892" y1="-399" y2="-399"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.305149" x1="4889" x2="4889" y1="-427" y2="-402"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.560509" x1="4894" x2="4883" y1="-402" y2="-402"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.211257" x1="4871" x2="4889" y1="-427" y2="-427"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.187784" x1="4871" x2="4871" y1="-435" y2="-419"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,0)" stroke-width="1" x1="5534" x2="5543" y1="-902" y2="-902"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-36985">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4561.000000 -835.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5842" ObjectName="SW-CX_QF.CX_QF_1026SW"/>
     <cge:Meas_Ref ObjectId="36985"/>
    <cge:TPSR_Ref TObjectID="5842"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36986">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4583.000000 -803.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5843" ObjectName="SW-CX_QF.CX_QF_10267SW"/>
     <cge:Meas_Ref ObjectId="36986"/>
    <cge:TPSR_Ref TObjectID="5843"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37004">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4560.000000 -914.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5854" ObjectName="SW-CX_QF.CX_QF_1971SW"/>
     <cge:Meas_Ref ObjectId="37004"/>
    <cge:TPSR_Ref TObjectID="5854"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37005">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4582.000000 -973.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5855" ObjectName="SW-CX_QF.CX_QF_19717SW"/>
     <cge:Meas_Ref ObjectId="37005"/>
    <cge:TPSR_Ref TObjectID="5855"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37007">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4583.000000 -1044.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5857" ObjectName="SW-CX_QF.CX_QF_19760SW"/>
     <cge:Meas_Ref ObjectId="37007"/>
    <cge:TPSR_Ref TObjectID="5857"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37008">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4582.000000 -1117.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5858" ObjectName="SW-CX_QF.CX_QF_19767SW"/>
     <cge:Meas_Ref ObjectId="37008"/>
    <cge:TPSR_Ref TObjectID="5858"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36440">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4015.000000 -835.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5794" ObjectName="SW-CX_QF.CX_QF_1016SW"/>
     <cge:Meas_Ref ObjectId="36440"/>
    <cge:TPSR_Ref TObjectID="5794"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36441">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4037.000000 -803.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5795" ObjectName="SW-CX_QF.CX_QF_10167SW"/>
     <cge:Meas_Ref ObjectId="36441"/>
    <cge:TPSR_Ref TObjectID="5795"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37119">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4015.000000 -914.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5877" ObjectName="SW-CX_QF.CX_QF_1961SW"/>
     <cge:Meas_Ref ObjectId="37119"/>
    <cge:TPSR_Ref TObjectID="5877"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37120">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4037.000000 -973.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5878" ObjectName="SW-CX_QF.CX_QF_19617SW"/>
     <cge:Meas_Ref ObjectId="37120"/>
    <cge:TPSR_Ref TObjectID="5878"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37122">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4038.000000 -1044.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5880" ObjectName="SW-CX_QF.CX_QF_19660SW"/>
     <cge:Meas_Ref ObjectId="37122"/>
    <cge:TPSR_Ref TObjectID="5880"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37123">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4039.000000 -1117.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5881" ObjectName="SW-CX_QF.CX_QF_19667SW"/>
     <cge:Meas_Ref ObjectId="37123"/>
    <cge:TPSR_Ref TObjectID="5881"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36997">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4559.000000 -576.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5851" ObjectName="SW-CX_QF.CX_QF_0026SW"/>
     <cge:Meas_Ref ObjectId="36997"/>
    <cge:TPSR_Ref TObjectID="5851"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36996">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4559.000000 -478.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5850" ObjectName="SW-CX_QF.CX_QF_0022SW"/>
     <cge:Meas_Ref ObjectId="36996"/>
    <cge:TPSR_Ref TObjectID="5850"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36998">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4189.000000 -669.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5852" ObjectName="SW-CX_QF.CX_QF_3010SW"/>
     <cge:Meas_Ref ObjectId="36998"/>
    <cge:TPSR_Ref TObjectID="5852"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36993">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4344.000000 -669.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5848" ObjectName="SW-CX_QF.CX_QF_3020SW"/>
     <cge:Meas_Ref ObjectId="36993"/>
    <cge:TPSR_Ref TObjectID="5848"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-195473">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4356.000000 -479.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5781" ObjectName="SW-CX_QF.CX_QF_0121SW"/>
     <cge:Meas_Ref ObjectId="195473"/>
    <cge:TPSR_Ref TObjectID="5781"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36401">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4461.000000 -482.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5782" ObjectName="SW-CX_QF.CX_QF_0122SW"/>
     <cge:Meas_Ref ObjectId="36401"/>
    <cge:TPSR_Ref TObjectID="5782"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36818">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3989.000000 -226.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5840" ObjectName="SW-CX_QF.CX_QF_0536SW"/>
     <cge:Meas_Ref ObjectId="36818"/>
    <cge:TPSR_Ref TObjectID="5840"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57363">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4011.000000 -212.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10683" ObjectName="SW-CX_QF.CX_QF_05367SW"/>
     <cge:Meas_Ref ObjectId="57363"/>
    <cge:TPSR_Ref TObjectID="10683"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37030">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4200.000000 -892.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5860" ObjectName="SW-CX_QF.CX_QF_1121SW"/>
     <cge:Meas_Ref ObjectId="37030"/>
    <cge:TPSR_Ref TObjectID="5860"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37032">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4341.000000 -892.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5862" ObjectName="SW-CX_QF.CX_QF_1122SW"/>
     <cge:Meas_Ref ObjectId="37032"/>
    <cge:TPSR_Ref TObjectID="5862"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37031">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 4252.000000 -834.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5861" ObjectName="SW-CX_QF.CX_QF_11217SW"/>
     <cge:Meas_Ref ObjectId="37031"/>
    <cge:TPSR_Ref TObjectID="5861"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37033">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 4318.000000 -834.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5863" ObjectName="SW-CX_QF.CX_QF_11227SW"/>
     <cge:Meas_Ref ObjectId="37033"/>
    <cge:TPSR_Ref TObjectID="5863"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37041">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4471.000000 -409.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5865" ObjectName="SW-CX_QF.CX_QF_0571SW"/>
     <cge:Meas_Ref ObjectId="37041"/>
    <cge:TPSR_Ref TObjectID="5865"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37042">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4493.000000 -396.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5866" ObjectName="SW-CX_QF.CX_QF_05717SW"/>
     <cge:Meas_Ref ObjectId="37042"/>
    <cge:TPSR_Ref TObjectID="5866"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37043">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4471.000000 -241.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5867" ObjectName="SW-CX_QF.CX_QF_0576SW"/>
     <cge:Meas_Ref ObjectId="37043"/>
    <cge:TPSR_Ref TObjectID="5867"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37067">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4588.000000 -409.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5870" ObjectName="SW-CX_QF.CX_QF_0581SW"/>
     <cge:Meas_Ref ObjectId="37067"/>
    <cge:TPSR_Ref TObjectID="5870"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37068">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4610.000000 -396.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5871" ObjectName="SW-CX_QF.CX_QF_05817SW"/>
     <cge:Meas_Ref ObjectId="37068"/>
    <cge:TPSR_Ref TObjectID="5871"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37069">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4588.000000 -241.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5872" ObjectName="SW-CX_QF.CX_QF_0586SW"/>
     <cge:Meas_Ref ObjectId="37069"/>
    <cge:TPSR_Ref TObjectID="5872"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36738">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4109.000000 -238.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5829" ObjectName="SW-CX_QF.CX_QF_0546SW"/>
     <cge:Meas_Ref ObjectId="36738"/>
    <cge:TPSR_Ref TObjectID="5829"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36682">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4201.000000 -238.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5821" ObjectName="SW-CX_QF.CX_QF_0556SW"/>
     <cge:Meas_Ref ObjectId="36682"/>
    <cge:TPSR_Ref TObjectID="5821"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37070">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4610.000000 -229.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5873" ObjectName="SW-CX_QF.CX_QF_05867SW"/>
     <cge:Meas_Ref ObjectId="37070"/>
    <cge:TPSR_Ref TObjectID="5873"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37044">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4493.000000 -230.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5868" ObjectName="SW-CX_QF.CX_QF_05767SW"/>
     <cge:Meas_Ref ObjectId="37044"/>
    <cge:TPSR_Ref TObjectID="5868"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36445">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4932.000000 -859.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5799" ObjectName="SW-CX_QF.CX_QF_3016SW"/>
     <cge:Meas_Ref ObjectId="36445"/>
    <cge:TPSR_Ref TObjectID="5799"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36444">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5029.000000 -859.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5798" ObjectName="SW-CX_QF.CX_QF_3011SW"/>
     <cge:Meas_Ref ObjectId="36444"/>
    <cge:TPSR_Ref TObjectID="5798"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36402">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5133.000000 -774.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5783" ObjectName="SW-CX_QF.CX_QF_3121SW"/>
     <cge:Meas_Ref ObjectId="36402"/>
    <cge:TPSR_Ref TObjectID="5783"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36403">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5134.000000 -683.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5784" ObjectName="SW-CX_QF.CX_QF_3122SW"/>
     <cge:Meas_Ref ObjectId="36403"/>
    <cge:TPSR_Ref TObjectID="5784"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36991">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4935.000000 -658.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5847" ObjectName="SW-CX_QF.CX_QF_3026SW"/>
     <cge:Meas_Ref ObjectId="36991"/>
    <cge:TPSR_Ref TObjectID="5847"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36990">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5032.000000 -658.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5846" ObjectName="SW-CX_QF.CX_QF_3022SW"/>
     <cge:Meas_Ref ObjectId="36990"/>
    <cge:TPSR_Ref TObjectID="5846"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36421">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5138.000000 -1072.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5786" ObjectName="SW-CX_QF.CX_QF_3901SW"/>
     <cge:Meas_Ref ObjectId="36421"/>
    <cge:TPSR_Ref TObjectID="5786"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36551">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5134.000000 -994.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5807" ObjectName="SW-CX_QF.CX_QF_3521SW"/>
     <cge:Meas_Ref ObjectId="36551"/>
    <cge:TPSR_Ref TObjectID="5807"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36579">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5233.000000 -903.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5811" ObjectName="SW-CX_QF.CX_QF_3536SW"/>
     <cge:Meas_Ref ObjectId="36579"/>
    <cge:TPSR_Ref TObjectID="5811"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36524">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5137.000000 -1146.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5804" ObjectName="SW-CX_QF.CX_QF_3511SW"/>
     <cge:Meas_Ref ObjectId="36524"/>
    <cge:TPSR_Ref TObjectID="5804"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36525">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5234.000000 -1146.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5805" ObjectName="SW-CX_QF.CX_QF_3516SW"/>
     <cge:Meas_Ref ObjectId="36525"/>
    <cge:TPSR_Ref TObjectID="5805"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36426">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5147.000000 -820.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5788" ObjectName="SW-CX_QF.CX_QF_3903SW"/>
     <cge:Meas_Ref ObjectId="36426"/>
    <cge:TPSR_Ref TObjectID="5788"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36578">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5136.000000 -903.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5810" ObjectName="SW-CX_QF.CX_QF_3531SW"/>
     <cge:Meas_Ref ObjectId="36578"/>
    <cge:TPSR_Ref TObjectID="5810"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36552">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5231.000000 -994.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5808" ObjectName="SW-CX_QF.CX_QF_3526SW"/>
     <cge:Meas_Ref ObjectId="36552"/>
    <cge:TPSR_Ref TObjectID="5808"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37094">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5133.000000 -312.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5875" ObjectName="SW-CX_QF.CX_QF_3562SW"/>
     <cge:Meas_Ref ObjectId="37094"/>
    <cge:TPSR_Ref TObjectID="5875"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37095">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5230.000000 -312.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5876" ObjectName="SW-CX_QF.CX_QF_3566SW"/>
     <cge:Meas_Ref ObjectId="37095"/>
    <cge:TPSR_Ref TObjectID="5876"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36605">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5132.000000 -624.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5813" ObjectName="SW-CX_QF.CX_QF_3542SW"/>
     <cge:Meas_Ref ObjectId="36605"/>
    <cge:TPSR_Ref TObjectID="5813"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36606">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5229.000000 -624.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5814" ObjectName="SW-CX_QF.CX_QF_3546SW"/>
     <cge:Meas_Ref ObjectId="36606"/>
    <cge:TPSR_Ref TObjectID="5814"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36632">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5131.000000 -538.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5816" ObjectName="SW-CX_QF.CX_QF_3552SW"/>
     <cge:Meas_Ref ObjectId="36632"/>
    <cge:TPSR_Ref TObjectID="5816"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36633">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5228.000000 -538.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5817" ObjectName="SW-CX_QF.CX_QF_3556SW"/>
     <cge:Meas_Ref ObjectId="36633"/>
    <cge:TPSR_Ref TObjectID="5817"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36429">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5145.000000 -456.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5789" ObjectName="SW-CX_QF.CX_QF_3904SW"/>
     <cge:Meas_Ref ObjectId="36429"/>
    <cge:TPSR_Ref TObjectID="5789"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36424">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5136.000000 -380.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5787" ObjectName="SW-CX_QF.CX_QF_3902SW"/>
     <cge:Meas_Ref ObjectId="36424"/>
    <cge:TPSR_Ref TObjectID="5787"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36794">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3689.000000 -226.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5837" ObjectName="SW-CX_QF.CX_QF_0516SW"/>
     <cge:Meas_Ref ObjectId="36794"/>
    <cge:TPSR_Ref TObjectID="5837"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57361">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3711.000000 -212.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10681" ObjectName="SW-CX_QF.CX_QF_05167SW"/>
     <cge:Meas_Ref ObjectId="57361"/>
    <cge:TPSR_Ref TObjectID="10681"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37121">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4015.000000 -1061.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5879" ObjectName="SW-CX_QF.CX_QF_1966SW"/>
     <cge:Meas_Ref ObjectId="37121"/>
    <cge:TPSR_Ref TObjectID="5879"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37006">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4560.000000 -1061.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5856" ObjectName="SW-CX_QF.CX_QF_1976SW"/>
     <cge:Meas_Ref ObjectId="37006"/>
    <cge:TPSR_Ref TObjectID="5856"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36447">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4014.000000 -483.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5801" ObjectName="SW-CX_QF.CX_QF_0011SW"/>
     <cge:Meas_Ref ObjectId="36447"/>
    <cge:TPSR_Ref TObjectID="5801"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36448">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4014.000000 -598.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5802" ObjectName="SW-CX_QF.CX_QF_0016SW"/>
     <cge:Meas_Ref ObjectId="36448"/>
    <cge:TPSR_Ref TObjectID="5802"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36430">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3775.000000 -482.000000)" xlink:href="#switch2:shape31_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5790" ObjectName="SW-CX_QF.CX_QF_0901SW"/>
     <cge:Meas_Ref ObjectId="36430"/>
    <cge:TPSR_Ref TObjectID="5790"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36793">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3684.000000 -408.000000)" xlink:href="#switch2:shape31_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5836" ObjectName="SW-CX_QF.CX_QF_0511SW"/>
     <cge:Meas_Ref ObjectId="36793"/>
    <cge:TPSR_Ref TObjectID="5836"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36400">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3829.000000 -407.000000)" xlink:href="#switch2:shape31_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29728" ObjectName="SW-CX_QF.CX_QF_0521SW"/>
     <cge:Meas_Ref ObjectId="36400"/>
    <cge:TPSR_Ref TObjectID="29728"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36817">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3984.000000 -407.000000)" xlink:href="#switch2:shape31_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5839" ObjectName="SW-CX_QF.CX_QF_0531SW"/>
     <cge:Meas_Ref ObjectId="36817"/>
    <cge:TPSR_Ref TObjectID="5839"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36737">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4104.000000 -407.000000)" xlink:href="#switch2:shape31_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5828" ObjectName="SW-CX_QF.CX_QF_0541SW"/>
     <cge:Meas_Ref ObjectId="36737"/>
    <cge:TPSR_Ref TObjectID="5828"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36681">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4196.000000 -407.000000)" xlink:href="#switch2:shape31_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5820" ObjectName="SW-CX_QF.CX_QF_0551SW"/>
     <cge:Meas_Ref ObjectId="36681"/>
    <cge:TPSR_Ref TObjectID="5820"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-195421">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4316.000000 -408.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29730" ObjectName="SW-CX_QF.CX_QF_0561SW"/>
     <cge:Meas_Ref ObjectId="195421"/>
    <cge:TPSR_Ref TObjectID="29730"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-195423">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4338.000000 -395.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29732" ObjectName="SW-CX_QF.CX_QF_05617SW"/>
     <cge:Meas_Ref ObjectId="195423"/>
    <cge:TPSR_Ref TObjectID="29732"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-195422">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4316.000000 -240.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29731" ObjectName="SW-CX_QF.CX_QF_0566SW"/>
     <cge:Meas_Ref ObjectId="195422"/>
    <cge:TPSR_Ref TObjectID="29731"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36433">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4862.000000 -482.000000)" xlink:href="#switch2:shape31_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5791" ObjectName="SW-CX_QF.CX_QF_0902SW"/>
     <cge:Meas_Ref ObjectId="36433"/>
    <cge:TPSR_Ref TObjectID="5791"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36766">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4725.000000 -237.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5833" ObjectName="SW-CX_QF.CX_QF_0596SW"/>
     <cge:Meas_Ref ObjectId="36766"/>
    <cge:TPSR_Ref TObjectID="5833"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36765">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4720.000000 -406.000000)" xlink:href="#switch2:shape31_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5832" ObjectName="SW-CX_QF.CX_QF_0592SW"/>
     <cge:Meas_Ref ObjectId="36765"/>
    <cge:TPSR_Ref TObjectID="5832"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36710">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4845.000000 -238.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5825" ObjectName="SW-CX_QF.CX_QF_0616SW"/>
     <cge:Meas_Ref ObjectId="36710"/>
    <cge:TPSR_Ref TObjectID="5825"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36709">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4840.000000 -407.000000)" xlink:href="#switch2:shape31_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5824" ObjectName="SW-CX_QF.CX_QF_0612SW"/>
     <cge:Meas_Ref ObjectId="36709"/>
    <cge:TPSR_Ref TObjectID="5824"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36442">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3959.000000 -696.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5796" ObjectName="SW-CX_QF.CX_QF_1010SW"/>
     <cge:Meas_Ref ObjectId="36442"/>
    <cge:TPSR_Ref TObjectID="5796"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36987">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4694.000000 -826.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5844" ObjectName="SW-CX_QF.CX_QF_1020SW"/>
     <cge:Meas_Ref ObjectId="36987"/>
    <cge:TPSR_Ref TObjectID="5844"/></metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_YZ" endPointId="0" endStationName="CX_QF" flowDrawDirect="1" flowShape="0" id="AC-110kV.yaoqin_line" runFlow="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4024,-1181 4024,-1223 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11431" ObjectName="AC-110kV.yaoqin_line"/>
    <cge:TPSR_Ref TObjectID="11431_SS-18"/></metadata>
   <polyline fill="none" opacity="0" points="4024,-1181 4024,-1223 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="PAS_T1" endPointId="0" endStationName="CX_QF" flowDrawDirect="1" flowShape="0" id="AC-110kV.luqinfengTqf_line" runFlow="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4569,-1181 4569,-1230 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11498" ObjectName="AC-110kV.luqinfengTqf_line"/>
    <cge:TPSR_Ref TObjectID="11498_SS-18"/></metadata>
   <polyline fill="none" opacity="0" points="4569,-1181 4569,-1230 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_QF" endPointId="0" endStationName="LF_TG" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_qintu" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="5333,-543 5371,-543 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="34580" ObjectName="AC-35kV.LN_qintu"/>
    <cge:TPSR_Ref TObjectID="34580_SS-18"/></metadata>
   <polyline fill="none" opacity="0" points="5333,-543 5371,-543 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_QF" endPointId="0" endStationName="LF_BC" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_BiCheng" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="5335,-908 5373,-908 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="18074" ObjectName="AC-35kV.LN_BiCheng"/>
    <cge:TPSR_Ref TObjectID="18074_SS-18"/></metadata>
   <polyline fill="none" opacity="0" points="5335,-908 5373,-908 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_QF" endPointId="0" endStationName="LF_RX" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_RenXing" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="5333,-999 5371,-999 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="18073" ObjectName="AC-35kV.LN_RenXing"/>
    <cge:TPSR_Ref TObjectID="18073_SS-18"/></metadata>
   <polyline fill="none" opacity="0" points="5333,-999 5371,-999 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_QF" endPointId="0" endStationName="LF_YJ" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_YangJie" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="5336,-629 5374,-629 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="18071" ObjectName="AC-35kV.LN_YangJie"/>
    <cge:TPSR_Ref TObjectID="18071_SS-18"/></metadata>
   <polyline fill="none" opacity="0" points="5336,-629 5374,-629 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="PAS_T1" endPointId="0" endStationName="CX_QF" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_GuoQLTqf" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="5345,-1151 5373,-1151 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="39627" ObjectName="AC-35kV.LN_GuoQLTqf"/>
    <cge:TPSR_Ref TObjectID="39627_SS-18"/></metadata>
   <polyline fill="none" opacity="0" points="5345,-1151 5373,-1151 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2a24660" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4636.000000 -802.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a2cfe0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4634.585714 -972.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2cdf770" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4635.585714 -1043.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bcf3a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4634.585714 -1116.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d2af00" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4090.000000 -802.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c503e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4090.000000 -972.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d05da0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4091.000000 -1043.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d4c110" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4092.000000 -1116.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ceec80" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4064.000000 -223.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d5a310" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4254.300000 -807.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d5ad40" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4320.300000 -807.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ca2a10" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4546.000000 -407.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d43110" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4663.000000 -407.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c5b7a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4663.000000 -240.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2cd6fb0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4546.000000 -241.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bcbe60" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3764.000000 -223.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ec4fd0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4391.000000 -406.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="5779" cx="5123" cy="-864" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5779" cx="5123" cy="-779" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5780" cx="5123" cy="-688" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5780" cx="5123" cy="-663" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5779" cx="5123" cy="-1077" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5779" cx="5123" cy="-999" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5779" cx="5123" cy="-1151" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5779" cx="5123" cy="-825" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5779" cx="5123" cy="-908" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5780" cx="5123" cy="-317" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5780" cx="5123" cy="-629" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5780" cx="5123" cy="-543" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5780" cx="5123" cy="-461" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5780" cx="5123" cy="-385" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5778" cx="4570" cy="-897" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5778" cx="4569" cy="-897" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5777" cx="4024" cy="-897" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5777" cx="4024" cy="-897" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5777" cx="4034" cy="-897" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5778" cx="4555" cy="-897" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5775" cx="4365" cy="-465" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5775" cx="4023" cy="-465" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5775" cx="3789" cy="-465" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5775" cx="3698" cy="-465" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5775" cx="3843" cy="-465" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5775" cx="3998" cy="-465" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5775" cx="4210" cy="-465" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5775" cx="4118" cy="-465" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5775" cx="4325" cy="-465" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5776" cx="4568" cy="-465" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5776" cx="4470" cy="-465" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5776" cx="4480" cy="-465" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5776" cx="4597" cy="-465" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5776" cx="4734" cy="-465" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5776" cx="4854" cy="-465" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d8e8d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3143.000000 -1034.000000) translate(0,17)">下网有功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d8e8d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3143.000000 -1034.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d8e8d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3143.000000 -1034.000000) translate(0,59)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d8e8d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3143.000000 -1034.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d8e8d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3143.000000 -1034.000000) translate(0,101)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d8e8d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3143.000000 -1034.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d8e8d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3143.000000 -1034.000000) translate(0,143)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d8e8d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3143.000000 -1034.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d8e8d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3143.000000 -1034.000000) translate(0,185)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2cdd450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3143.000000 -589.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2cdd450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3143.000000 -589.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2cdd450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3143.000000 -589.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2cdd450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3143.000000 -589.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2cdd450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3143.000000 -589.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2cdd450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3143.000000 -589.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2cdd450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3143.000000 -589.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2cdd450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3143.000000 -589.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2cdd450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3143.000000 -589.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2cdd450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3143.000000 -589.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2cdd450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3143.000000 -589.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2cdd450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3143.000000 -589.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2cdd450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3143.000000 -589.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2cdd450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3143.000000 -589.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2cdd450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3143.000000 -589.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2cdd450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3143.000000 -589.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2cdd450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3143.000000 -589.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2cdd450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3143.000000 -589.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c98620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4034.000000 -1023.000000) translate(0,12)">196</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c9b550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4034.000000 -950.000000) translate(0,12)">1961</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c9b970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4040.000000 -1004.000000) translate(0,12)">19617</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c9bbb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4041.000000 -1075.000000) translate(0,12)">19660</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c99270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4030.000000 -1092.000000) translate(0,12)">1966</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c99690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4031.000000 -865.000000) translate(0,12)">1016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c998d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4040.000000 -834.000000) translate(0,12)">10167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bc2d70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4279.300000 -921.000000) translate(0,12)">112</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bc3220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4208.300000 -923.000000) translate(0,12)">1121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bc3640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4348.300000 -923.000000) translate(0,12)">1122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bc3880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4267.300000 -864.000000) translate(0,12)">11217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bc3ac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4333.300000 -864.000000) translate(0,12)">11227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bc3d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4577.585714 -1020.000000) translate(0,12)">197</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bc3f40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4575.585714 -944.000000) translate(0,12)">1971</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bc4180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4584.585714 -1004.000000) translate(0,12)">19717</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bc43c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4585.585714 -1075.000000) translate(0,12)">19760</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bc4600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4584.585714 -1148.000000) translate(0,12)">19767</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bc4840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4575.585714 -1093.000000) translate(0,12)">1976</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bc4a80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4577.000000 -865.000000) translate(0,12)">1026</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bc4cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4586.000000 -834.000000) translate(0,12)">10267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bc4f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4685.000000 -842.000000) translate(0,12)">1020</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bc5140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3952.000000 -691.000000) translate(0,12)">1010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2bc5380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4082.000000 -759.000000) translate(0,15)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2bc55c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4633.000000 -756.000000) translate(0,15)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bc59b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5063.000000 -1170.000000) translate(0,12)">35kVⅠ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bc6340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5056.000000 -296.000000) translate(0,12)">35kVⅡ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2bc69c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3596.000000 -461.000000) translate(0,15)">10kVⅠ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2bc7bd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4917.000000 -459.000000) translate(0,15)">10kVⅡ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c15400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4196.000000 -700.000000) translate(0,12)">3010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c15a30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4350.000000 -700.000000) translate(0,12)">3020</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c15c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4405.000000 -555.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c15eb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4372.000000 -509.000000) translate(0,12)">0121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c160f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4477.000000 -512.000000) translate(0,12)">0122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c16330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4577.000000 -557.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c16570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4575.000000 -508.000000) translate(0,12)">0022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c167b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4575.000000 -606.000000) translate(0,12)">0026</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c169f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4991.000000 -888.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c16c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5036.000000 -890.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c16e70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4939.000000 -890.000000) translate(0,12)">3016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c170b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4994.000000 -687.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c172f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5039.000000 -689.000000) translate(0,12)">3022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c17530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4941.000000 -689.000000) translate(0,12)">3026</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c17770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5200.000000 -743.000000) translate(0,12)">312</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c179b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5140.000000 -805.000000) translate(0,12)">3121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c17bf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5141.000000 -714.000000) translate(0,12)">3122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c17e30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5197.000000 -1176.000000) translate(0,12)">351</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c18070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5145.000000 -1177.000000) translate(0,12)">3511</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c182b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5241.000000 -1177.000000) translate(0,12)">3516</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c184f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5145.000000 -1103.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c18730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5193.000000 -1024.000000) translate(0,12)">352</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c18970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5141.000000 -1025.000000) translate(0,12)">3521</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c18bb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5238.000000 -1025.000000) translate(0,12)">3526</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c18df0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5195.000000 -933.000000) translate(0,12)">353</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c19030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5143.000000 -934.000000) translate(0,12)">3531</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c19270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5240.000000 -934.000000) translate(0,12)">3536</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c194b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5153.000000 -851.000000) translate(0,12)">3903</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c196f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5189.000000 -656.000000) translate(0,12)">354</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c19930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5140.000000 -655.000000) translate(0,12)">3542</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c19d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5236.000000 -655.000000) translate(0,12)">3546</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c19f90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5191.000000 -568.000000) translate(0,12)">355</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c1a1d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5139.000000 -569.000000) translate(0,12)">3552</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c1a410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5235.000000 -569.000000) translate(0,12)">3556</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c1a650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5152.000000 -487.000000) translate(0,12)">3904</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c1a890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5142.000000 -411.000000) translate(0,12)">3902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c1aad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5192.000000 -342.000000) translate(0,12)">356</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c1ad10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5140.000000 -343.000000) translate(0,12)">3562</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c1af50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5237.000000 -343.000000) translate(0,12)">3566</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c1b190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3663.000000 -374.000000) translate(0,12)">051</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c1b3d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3657.000000 -251.000000) translate(0,12)">0516</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c1b610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3963.000000 -376.000000) translate(0,12)">053</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c1b850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3956.000000 -254.000000) translate(0,12)">0536</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c1ba90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4217.000000 -268.000000) translate(0,12)">0556</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c1bcd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4080.000000 -378.000000) translate(0,12)">054</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c1bf10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4125.000000 -268.000000) translate(0,12)">0546</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c1c150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4606.000000 -379.000000) translate(0,12)">058</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c1c490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4604.000000 -271.000000) translate(0,12)">0586</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c1c8f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4604.000000 -439.000000) translate(0,12)">0582</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c1cb30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4617.000000 -422.000000) translate(0,12)">05827</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c1cd70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4616.000000 -255.000000) translate(0,12)">05867</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c1cfb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4491.000000 -379.000000) translate(0,12)">057</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c1d1f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4439.000000 -439.000000) translate(0,12)">0572</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c1d430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4506.000000 -423.000000) translate(0,12)">05727</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c1d670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4487.000000 -271.000000) translate(0,12)">0576</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c1d8b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4509.000000 -257.000000) translate(0,12)">05767</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c1daf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4001.000000 -1246.000000) translate(0,15)">腰勤线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c1e4b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4531.000000 -1254.000000) translate(0,15)">禄勤老线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c1ee30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4216.000000 -774.000000) translate(0,15)">35kV消弧线圈</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c1ffd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3728.000000 -651.000000) translate(0,15)">Ⅰ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c1ffd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3728.000000 -651.000000) translate(0,33)">段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c1ffd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3728.000000 -651.000000) translate(0,51)">母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c1ffd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3728.000000 -651.000000) translate(0,69)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c1ffd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3728.000000 -651.000000) translate(0,87)">TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c20950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4813.000000 -643.000000) translate(0,15)">Ⅱ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c20950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4813.000000 -643.000000) translate(0,33)">段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c20950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4813.000000 -643.000000) translate(0,51)">母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c20950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4813.000000 -643.000000) translate(0,69)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c20950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4813.000000 -643.000000) translate(0,87)">TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c20c20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3730.000000 -186.000000) translate(0,15)">1</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c20c20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3730.000000 -186.000000) translate(0,33)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c20c20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3730.000000 -186.000000) translate(0,51)">电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c20c20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3730.000000 -186.000000) translate(0,69)">容</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c20c20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3730.000000 -186.000000) translate(0,87)">器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c20c20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3730.000000 -186.000000) translate(0,105)">组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c219d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4036.000000 -186.000000) translate(0,15)">3</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c219d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4036.000000 -186.000000) translate(0,33)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c219d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4036.000000 -186.000000) translate(0,51)">电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c219d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4036.000000 -186.000000) translate(0,69)">容</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c219d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4036.000000 -186.000000) translate(0,87)">器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c219d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4036.000000 -186.000000) translate(0,105)">组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c21df0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4220.000000 -168.000000) translate(0,15)">勤</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c21df0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4220.000000 -168.000000) translate(0,33)">碧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c21df0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4220.000000 -168.000000) translate(0,51)">Ⅰ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c21df0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4220.000000 -168.000000) translate(0,69)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c21df0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4220.000000 -168.000000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c22580" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4128.000000 -149.000000) translate(0,15)">电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c22580" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4128.000000 -149.000000) translate(0,33)">瓶</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c22580" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4128.000000 -149.000000) translate(0,51)">厂</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c22580" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4128.000000 -149.000000) translate(0,69)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2dbf120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4628.000000 -186.000000) translate(0,15)">4</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2dbf120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4628.000000 -186.000000) translate(0,33)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2dbf120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4628.000000 -186.000000) translate(0,51)">电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2dbf120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4628.000000 -186.000000) translate(0,69)">容</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2dbf120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4628.000000 -186.000000) translate(0,87)">器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2dbf120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4628.000000 -186.000000) translate(0,105)">组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2dbf7a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4511.000000 -184.000000) translate(0,15)">2</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2dbf7a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4511.000000 -184.000000) translate(0,33)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2dbf7a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4511.000000 -184.000000) translate(0,51)">电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2dbf7a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4511.000000 -184.000000) translate(0,69)">容</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2dbf7a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4511.000000 -184.000000) translate(0,87)">器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2dbf7a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4511.000000 -184.000000) translate(0,105)">组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2dbf9b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5359.000000 -310.000000) translate(0,15)">备用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2dc0130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5297.000000 -469.000000) translate(0,15)">Ⅱ段计量TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2dc0ab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5356.000000 -395.000000) translate(0,15)">Ⅱ段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2dc0d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5356.000000 -1086.000000) translate(0,15)">Ⅰ段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2dc0f40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5297.000000 -833.000000) translate(0,15)">Ⅰ段计量TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2dc1180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5341.000000 -996.000000) translate(0,15)">仁兴线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2dc1910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5326.000000 -1145.000000) translate(0,15)">果勤罗线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2dc21b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5341.000000 -620.000000) translate(0,15)">羊街线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_2c39270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3257.000000 -1166.500000) translate(0,16)">勤丰变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c3b610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5367.000000 -952.000000) translate(0,15)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c3da10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5450.000000 -958.000000) translate(0,15)">K02</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_2b9a9a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3417.000000 -1163.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ba5830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5341.000000 -900.000000) translate(0,15)">碧城线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ba6030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5342.000000 -534.000000) translate(0,15)">勤土线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ba73a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4041.000000 -1148.000000) translate(0,12)">19667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_2bed9a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3189.000000 -757.000000) translate(0,18)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2bef3d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3263.000000 -230.000000) translate(0,16)">4908</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2bf0d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3695.000000 -712.000000) translate(0,16)">1号主变：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2bf0d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3695.000000 -712.000000) translate(0,36)">SFSZ8-31500/110GY</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2bf4280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4391.000000 -645.000000) translate(0,16)">2号主变：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2bf4280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4391.000000 -645.000000) translate(0,36)">SFSZ8-50000/110</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2bf49a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4868.000000 -170.000000) translate(0,15)">勤</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2bf49a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4868.000000 -170.000000) translate(0,33)">碧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2bf49a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4868.000000 -170.000000) translate(0,51)">Ⅱ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2bf49a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4868.000000 -170.000000) translate(0,69)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2bf49a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4868.000000 -170.000000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2bf4bf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4739.000000 -171.000000) translate(0,15)">勤</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2bf4bf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4739.000000 -171.000000) translate(0,33)">丰</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2bf4bf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4739.000000 -171.000000) translate(0,51)">Ⅰ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2bf4bf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4739.000000 -171.000000) translate(0,69)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2bf4bf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4739.000000 -171.000000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bf51a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3715.000000 -239.000000) translate(0,12)">05167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bf5390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4029.000000 -240.000000) translate(0,12)">05367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bf55d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4173.000000 -379.000000) translate(0,12)">055</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bfd840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4032.000000 -572.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bfde70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4030.000000 -628.000000) translate(0,12)">0016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bfe0b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4030.000000 -513.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ec5f20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4338.000000 -214.000000) translate(0,15)">昆</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ec5f20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4338.000000 -214.000000) translate(0,33)">广</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ec5f20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4338.000000 -214.000000) translate(0,51)">高</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ec5f20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4338.000000 -214.000000) translate(0,69)">速</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ec5f20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4338.000000 -214.000000) translate(0,87)">勤</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ec5f20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4338.000000 -214.000000) translate(0,105)">丰</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ec5f20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4338.000000 -214.000000) translate(0,123)">段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ec5f20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4338.000000 -214.000000) translate(0,141)">专</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ec5f20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4338.000000 -214.000000) translate(0,159)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eeae10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3649.000000 -438.000000) translate(0,12)">0511</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eeb440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3796.000000 -438.000000) translate(0,12)">0521</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2eeb680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3848.000000 -171.000000) translate(0,15)">1</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2eeb680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3848.000000 -171.000000) translate(0,33)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2eeb680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3848.000000 -171.000000) translate(0,51)">站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2eeb680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3848.000000 -171.000000) translate(0,69)">用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2eeb680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3848.000000 -171.000000) translate(0,87)">变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eeb8d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3950.000000 -435.000000) translate(0,12)">0531</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eebb00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4070.000000 -436.000000) translate(0,12)">0541</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eebd40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4164.000000 -435.000000) translate(0,12)">0551</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eebf80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4291.000000 -369.000000) translate(0,12)">056</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eec1c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4285.000000 -433.000000) translate(0,12)">0561</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eec400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4341.000000 -420.000000) translate(0,12)">05617</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eec640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4332.000000 -270.000000) translate(0,12)">0566</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eec880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3805.000000 -510.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eecac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4894.000000 -510.000000) translate(0,12)">0902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eecd00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4743.000000 -378.000000) translate(0,12)">059</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eecf40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4741.000000 -267.000000) translate(0,12)">0596</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eed180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4689.000000 -432.000000) translate(0,12)">0592</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eed3c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4863.000000 -379.000000) translate(0,12)">061</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eed600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4861.000000 -268.000000) translate(0,12)">0616</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eed840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4807.000000 -436.000000) translate(0,12)">0612</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eef2c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3852.000000 -373.000000) translate(0,12)">052</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2ef19a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3142.000000 -556.000000) translate(0,16)">1、全站停电检修前应挂“全站停电检修”牌，复电后</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2ef19a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3142.000000 -556.000000) translate(0,36)">方可摘除“全站停电检修”牌。</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2ef19a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3142.000000 -556.000000) translate(0,56)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2ef19a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3142.000000 -556.000000) translate(0,76)">2、各类间隔工作时，停电完成后应在相应间隔挂</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2ef19a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3142.000000 -556.000000) translate(0,96)">“禁止合闸，有人工作”牌，复电前方可摘除。</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2ef19a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3142.000000 -556.000000) translate(0,116)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2ef19a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3142.000000 -556.000000) translate(0,136)">3、线路工作时，停电完成后应在相应间隔</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2ef19a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3142.000000 -556.000000) translate(0,156)">挂“禁止合闸，线路有人工作”牌，复电前方可摘除。</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2ef19a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3142.000000 -556.000000) translate(0,176)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2ef19a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3142.000000 -556.000000) translate(0,196)">4、现场工作影响对应间隔四遥信息正确性的，应挂</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2ef19a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3142.000000 -556.000000) translate(0,216)">“禁止刷新”牌，工作结束后方可摘除。</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2ef19a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3142.000000 -556.000000) translate(0,236)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2ef19a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3142.000000 -556.000000) translate(0,256)">5、现场开展相应间隔四遥信息核对前，应挂“调试一”牌，</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2ef19a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3142.000000 -556.000000) translate(0,276)">核对工作结束后方可摘除。</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_2effab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3508.000000 -1182.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_2f007f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3508.000000 -1141.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="20" graphid="g_2f0c360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3125.000000 -196.000000) translate(0,16)">禄丰巡维中心</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="20" graphid="g_2f0c360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3125.000000 -196.000000) translate(0,36)">腰站变值班：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2cc2ce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3263.000000 -185.500000) translate(0,16)">13508785653</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f0d990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3905.000000 -1203.000000) translate(0,12)">Ux(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f0e320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4447.000000 -1202.000000) translate(0,12)">Ux(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f0e990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3897.000000 -1179.000000) translate(0,12)">Uab(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f150c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4439.000000 -1179.000000) translate(0,12)">Uab(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f15730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5308.000000 -1219.000000) translate(0,12)">Ux(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f15da0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5364.000000 -1048.000000) translate(0,12)">Ux(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f16410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5494.000000 -955.000000) translate(0,12)">Ux(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f16a80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5386.000000 -690.000000) translate(0,12)">Ux(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f170f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5365.000000 -590.000000) translate(0,12)">Ux(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_2f1eac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3189.000000 -700.000000) translate(0,18)">隔刀远控</text>
  </g><g id="CircleFilled_Layer">
   <circle DF8003:Layer="PUBLIC" cx="5383" cy="-1184" fill="rgb(0,0,0)" fillStyle="1" r="10" stroke="rgb(255,255,255)" stroke-width="1.75238"/>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2bff2b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3748.000000 -599.000000)" xlink:href="#voltageTransformer:shape34"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ecbbc0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4835.000000 -600.000000)" xlink:href="#voltageTransformer:shape34"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-110KV" id="g_2d73440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4570,-808 4588,-808 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5886@x" ObjectIDND1="5842@x" ObjectIDZND0="5843@0" Pin0InfoVect0LinkObjId="SW-36986_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2d5b730_0" Pin1InfoVect1LinkObjId="SW-36985_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4570,-808 4588,-808 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2d402b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4624,-808 4641,-808 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="5843@1" ObjectIDZND0="g_2a24660@0" Pin0InfoVect0LinkObjId="g_2a24660_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36986_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4624,-808 4641,-808 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2b92380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4569,-978 4587,-978 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5853@x" ObjectIDND1="5854@x" ObjectIDZND0="5855@0" Pin0InfoVect0LinkObjId="SW-37005_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-37000_0" Pin1InfoVect1LinkObjId="SW-37004_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4569,-978 4587,-978 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2d7edd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4623,-978 4640,-978 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="5855@1" ObjectIDZND0="g_2a2cfe0@0" Pin0InfoVect0LinkObjId="g_2a2cfe0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37005_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4623,-978 4640,-978 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2cdf390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4570,-1049 4588,-1049 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5853@x" ObjectIDND1="5856@x" ObjectIDZND0="5857@0" Pin0InfoVect0LinkObjId="SW-37007_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-37000_0" Pin1InfoVect1LinkObjId="SW-37006_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4570,-1049 4588,-1049 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2cdf580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4624,-1049 4641,-1049 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="5857@1" ObjectIDZND0="g_2cdf770@0" Pin0InfoVect0LinkObjId="g_2cdf770_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37007_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4624,-1049 4641,-1049 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2bcf1b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4623,-1122 4640,-1122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="5858@1" ObjectIDZND0="g_2bcf3a0@0" Pin0InfoVect0LinkObjId="g_2bcf3a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37008_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4623,-1122 4640,-1122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2d2ab20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4024,-808 4042,-808 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5885@x" ObjectIDND1="5794@x" ObjectIDZND0="5795@0" Pin0InfoVect0LinkObjId="SW-36441_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2bb6250_0" Pin1InfoVect1LinkObjId="SW-36440_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4024,-808 4042,-808 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2d2ad10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4078,-808 4095,-808 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="5795@1" ObjectIDZND0="g_2d2af00@0" Pin0InfoVect0LinkObjId="g_2d2af00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36441_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4078,-808 4095,-808 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2c50000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4024,-978 4042,-978 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5882@x" ObjectIDND1="5877@x" ObjectIDZND0="5878@0" Pin0InfoVect0LinkObjId="SW-37120_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-37124_0" Pin1InfoVect1LinkObjId="SW-37119_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4024,-978 4042,-978 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2c501f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4078,-978 4095,-978 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="5878@1" ObjectIDZND0="g_2c503e0@0" Pin0InfoVect0LinkObjId="g_2c503e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37120_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4078,-978 4095,-978 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2d059c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4025,-1049 4043,-1049 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5882@x" ObjectIDND1="5879@x" ObjectIDZND0="5880@0" Pin0InfoVect0LinkObjId="SW-37122_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-37124_0" Pin1InfoVect1LinkObjId="SW-37121_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4025,-1049 4043,-1049 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2d05bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4079,-1049 4096,-1049 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="5880@1" ObjectIDZND0="g_2d05da0@0" Pin0InfoVect0LinkObjId="g_2d05da0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37122_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4079,-1049 4096,-1049 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2d4bf20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4080,-1122 4097,-1122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="5881@1" ObjectIDZND0="g_2d4c110@0" Pin0InfoVect0LinkObjId="g_2d4c110_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37123_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4080,-1122 4097,-1122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d61ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4568,-465 4568,-483 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5776@0" ObjectIDZND0="5850@0" Pin0InfoVect0LinkObjId="SW-36996_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2edbec0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4568,-465 4568,-483 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d61cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4568,-519 4568,-536 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="5850@1" ObjectIDZND0="5849@1" Pin0InfoVect0LinkObjId="SW-36995_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36996_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4568,-519 4568,-536 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d61f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4568,-563 4568,-581 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="5849@0" ObjectIDZND0="5851@0" Pin0InfoVect0LinkObjId="SW-36997_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36995_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4568,-563 4568,-581 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ca9c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4365,-484 4365,-465 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="5781@0" ObjectIDZND0="5775@0" Pin0InfoVect0LinkObjId="g_2eb7da0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-195473_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4365,-484 4365,-465 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d1a9c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4470,-465 4470,-487 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5776@0" ObjectIDZND0="5782@0" Pin0InfoVect0LinkObjId="SW-36401_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2edbec0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4470,-465 4470,-487 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d1deb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4470,-523 4470,-531 4430,-531 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="5782@1" ObjectIDZND0="5841@0" Pin0InfoVect0LinkObjId="SW-36841_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36401_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4470,-523 4470,-531 4430,-531 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d1e0d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4403,-531 4365,-531 4365,-520 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="5841@1" ObjectIDZND0="5781@1" Pin0InfoVect0LinkObjId="SW-195473_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36841_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4403,-531 4365,-531 4365,-520 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2d1e2f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3921,-760 3921,-736 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2bb6480@0" ObjectIDND1="5885@x" ObjectIDND2="5796@x" ObjectIDZND0="g_2d4cf10@0" Pin0InfoVect0LinkObjId="g_2d4cf10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2bb6480_0" Pin1InfoVect1LinkObjId="g_2bb6250_0" Pin1InfoVect2LinkObjId="SW-36442_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3921,-760 3921,-736 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d200f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3998,-339 4025,-339 4025,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="5838@x" ObjectIDND1="g_2bb7ba0@0" ObjectIDZND0="g_2cedc50@0" Pin0InfoVect0LinkObjId="g_2cedc50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-36816_0" Pin1InfoVect1LinkObjId="g_2bb7ba0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3998,-339 4025,-339 4025,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d20310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3998,-339 3998,-353 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="breaker" ObjectIDND0="g_2cedc50@0" ObjectIDND1="g_2bb7ba0@0" ObjectIDZND0="5838@0" Pin0InfoVect0LinkObjId="SW-36816_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2cedc50_0" Pin1InfoVect1LinkObjId="g_2bb7ba0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3998,-339 3998,-353 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cee840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3998,-217 4016,-217 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="capacitor" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2cefc10@0" ObjectIDND1="15558@x" ObjectIDND2="5840@x" ObjectIDZND0="10683@0" Pin0InfoVect0LinkObjId="SW-57363_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2cefc10_0" Pin1InfoVect1LinkObjId="CB-CX_QF.CX_QF_Cb3_0" Pin1InfoVect2LinkObjId="SW-36818_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3998,-217 4016,-217 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ceea60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4052,-217 4069,-217 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="10683@1" ObjectIDZND0="g_2ceec80@0" Pin0InfoVect0LinkObjId="g_2ceec80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57363_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4052,-217 4069,-217 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cef5b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3998,-231 3998,-217 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="capacitor" EndDevType2="switch" ObjectIDND0="5840@0" ObjectIDZND0="g_2cefc10@0" ObjectIDZND1="15558@x" ObjectIDZND2="10683@x" Pin0InfoVect0LinkObjId="g_2cefc10_0" Pin0InfoVect1LinkObjId="CB-CX_QF.CX_QF_Cb3_0" Pin0InfoVect2LinkObjId="SW-57363_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36818_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3998,-231 3998,-217 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cef7d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3998,-217 3998,-190 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="capacitor" ObjectIDND0="g_2cefc10@0" ObjectIDND1="10683@x" ObjectIDND2="5840@x" ObjectIDZND0="15558@0" Pin0InfoVect0LinkObjId="CB-CX_QF.CX_QF_Cb3_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2cefc10_0" Pin1InfoVect1LinkObjId="SW-57363_0" Pin1InfoVect2LinkObjId="SW-36818_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3998,-217 3998,-190 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cef9f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3998,-217 3967,-217 3967,-202 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="15558@x" ObjectIDND1="10683@x" ObjectIDND2="5840@x" ObjectIDZND0="g_2cefc10@0" Pin0InfoVect0LinkObjId="g_2cefc10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="CB-CX_QF.CX_QF_Cb3_0" Pin1InfoVect1LinkObjId="SW-57363_0" Pin1InfoVect2LinkObjId="SW-36818_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3998,-217 3967,-217 3967,-202 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2cd1550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4034,-897 4205,-897 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5777@0" ObjectIDZND0="5860@0" Pin0InfoVect0LinkObjId="SW-37030_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c43a30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4034,-897 4205,-897 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2cd1770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4241,-897 4260,-897 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="5860@1" ObjectIDZND0="5859@x" ObjectIDZND1="5861@x" Pin0InfoVect0LinkObjId="SW-37014_0" Pin0InfoVect1LinkObjId="SW-37031_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37030_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4241,-897 4260,-897 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2cd1990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4260,-897 4277,-897 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="5860@x" ObjectIDND1="5861@x" ObjectIDZND0="5859@1" Pin0InfoVect0LinkObjId="SW-37014_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-37030_0" Pin1InfoVect1LinkObjId="SW-37031_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4260,-897 4277,-897 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2cd1bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4304,-897 4326,-897 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="5859@0" ObjectIDZND0="5862@x" ObjectIDZND1="5863@x" Pin0InfoVect0LinkObjId="SW-37032_0" Pin0InfoVect1LinkObjId="SW-37033_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37014_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4304,-897 4326,-897 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2cd1dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4326,-897 4346,-897 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5859@x" ObjectIDND1="5863@x" ObjectIDZND0="5862@0" Pin0InfoVect0LinkObjId="SW-37032_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-37014_0" Pin1InfoVect1LinkObjId="SW-37033_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4326,-897 4346,-897 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2c077d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4260,-897 4260,-875 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5859@x" ObjectIDND1="5860@x" ObjectIDZND0="5861@1" Pin0InfoVect0LinkObjId="SW-37031_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-37014_0" Pin1InfoVect1LinkObjId="SW-37030_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4260,-897 4260,-875 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2c07a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4260,-839 4260,-824 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="5861@0" ObjectIDZND0="g_2d5a310@0" Pin0InfoVect0LinkObjId="g_2d5a310_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37031_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4260,-839 4260,-824 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2d59e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4326,-897 4326,-875 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5859@x" ObjectIDND1="5862@x" ObjectIDZND0="5863@1" Pin0InfoVect0LinkObjId="SW-37033_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-37014_0" Pin1InfoVect1LinkObjId="SW-37032_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4326,-897 4326,-875 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2d5a0b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4326,-839 4326,-824 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="5863@0" ObjectIDZND0="g_2d5ad40@0" Pin0InfoVect0LinkObjId="g_2d5ad40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37033_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4326,-839 4326,-824 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d5b730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4568,-617 4568,-694 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer" ObjectIDND0="5851@1" ObjectIDZND0="5886@2" Pin0InfoVect0LinkObjId="g_2cda450_2" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36997_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4568,-617 4568,-694 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d5b990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3789,-465 3789,-486 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5775@0" ObjectIDZND0="5790@0" Pin0InfoVect0LinkObjId="SW-36430_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ca9c80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3789,-465 3789,-486 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d5bbf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3789,-522 3789,-538 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="5790@1" ObjectIDZND0="g_2c3e3a0@0" ObjectIDZND1="g_2c01640@0" Pin0InfoVect0LinkObjId="g_2c3e3a0_0" Pin0InfoVect1LinkObjId="g_2c01640_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36430_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3789,-522 3789,-538 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ca2550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4480,-401 4498,-401 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="5865@x" ObjectIDND1="5864@x" ObjectIDZND0="5866@0" Pin0InfoVect0LinkObjId="SW-37042_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-37041_0" Pin1InfoVect1LinkObjId="SW-37039_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4480,-401 4498,-401 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ca27b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4534,-401 4551,-401 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="5866@1" ObjectIDZND0="g_2ca2a10@0" Pin0InfoVect0LinkObjId="g_2ca2a10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37042_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4534,-401 4551,-401 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d655e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4480,-414 4480,-401 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="5865@0" ObjectIDZND0="5864@x" ObjectIDZND1="5866@x" Pin0InfoVect0LinkObjId="SW-37039_0" Pin0InfoVect1LinkObjId="SW-37042_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37041_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4480,-414 4480,-401 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ca47d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4480,-401 4480,-385 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="5865@x" ObjectIDND1="5866@x" ObjectIDZND0="5864@1" Pin0InfoVect0LinkObjId="SW-37039_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-37041_0" Pin1InfoVect1LinkObjId="SW-37042_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4480,-401 4480,-385 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cb1a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4480,-465 4480,-450 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5776@0" ObjectIDZND0="5865@1" Pin0InfoVect0LinkObjId="SW-37041_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2edbec0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4480,-465 4480,-450 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d42c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4597,-401 4615,-401 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="5870@x" ObjectIDND1="5869@x" ObjectIDZND0="5871@0" Pin0InfoVect0LinkObjId="SW-37068_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-37067_0" Pin1InfoVect1LinkObjId="SW-37065_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4597,-401 4615,-401 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d42eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4651,-401 4668,-401 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="5871@1" ObjectIDZND0="g_2d43110@0" Pin0InfoVect0LinkObjId="g_2d43110_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37068_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4651,-401 4668,-401 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c0a8b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4597,-414 4597,-401 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="5870@0" ObjectIDZND0="5871@x" ObjectIDZND1="5869@x" Pin0InfoVect0LinkObjId="SW-37068_0" Pin0InfoVect1LinkObjId="SW-37065_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37067_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4597,-414 4597,-401 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c0c570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4597,-401 4597,-385 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="5871@x" ObjectIDND1="5870@x" ObjectIDZND0="5869@1" Pin0InfoVect0LinkObjId="SW-37065_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-37068_0" Pin1InfoVect1LinkObjId="SW-37067_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4597,-401 4597,-385 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c0edf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4597,-465 4597,-450 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5776@0" ObjectIDZND0="5870@1" Pin0InfoVect0LinkObjId="SW-37067_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2edbec0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4597,-465 4597,-450 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c27cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4118,-230 4152,-230 4152,-212 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="34538@x" ObjectIDND1="5829@x" ObjectIDZND0="g_2c281a0@0" Pin0InfoVect0LinkObjId="g_2c281a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-CX_QF.054Ld_0" Pin1InfoVect1LinkObjId="SW-36738_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4118,-230 4152,-230 4152,-212 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c27f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4118,-230 4118,-105 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_2c281a0@0" ObjectIDND1="5829@x" ObjectIDZND0="34538@0" Pin0InfoVect0LinkObjId="EC-CX_QF.054Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2c281a0_0" Pin1InfoVect1LinkObjId="SW-36738_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4118,-230 4118,-105 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c5a130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4210,-231 4244,-231 4244,-213 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="5821@x" ObjectIDND1="34539@x" ObjectIDZND0="g_2c5a5f0@0" Pin0InfoVect0LinkObjId="g_2c5a5f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-36682_0" Pin1InfoVect1LinkObjId="EC-CX_QF.055Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4210,-231 4244,-231 4244,-213 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c5a390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4210,-231 4210,-106 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_2c5a5f0@0" ObjectIDND1="5821@x" ObjectIDZND0="34539@0" Pin0InfoVect0LinkObjId="EC-CX_QF.055Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2c5a5f0_0" Pin1InfoVect1LinkObjId="SW-36682_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4210,-231 4210,-106 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c5b2e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4597,-234 4615,-234 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="15557@x" ObjectIDND1="5872@x" ObjectIDZND0="5873@0" Pin0InfoVect0LinkObjId="SW-37070_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="CB-CX_QF.CX_QF_Cb4_0" Pin1InfoVect1LinkObjId="SW-37069_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4597,-234 4615,-234 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c5b540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4651,-234 4668,-234 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="5873@1" ObjectIDZND0="g_2c5b7a0@0" Pin0InfoVect0LinkObjId="g_2c5b7a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37070_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4651,-234 4668,-234 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cd6d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4534,-235 4551,-235 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="5868@1" ObjectIDZND0="g_2cd6fb0@0" Pin0InfoVect0LinkObjId="g_2cd6fb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37044_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4534,-235 4551,-235 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cd9d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4480,-235 4498,-235 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="15556@x" ObjectIDND1="5867@x" ObjectIDZND0="5868@0" Pin0InfoVect0LinkObjId="SW-37044_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="CB-CX_QF.CX_QF_Cb2_0" Pin1InfoVect1LinkObjId="SW-37043_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4480,-235 4498,-235 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cd9f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4597,-234 4597,-218 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="capacitor" ObjectIDND0="5873@x" ObjectIDND1="5872@x" ObjectIDZND0="15557@0" Pin0InfoVect0LinkObjId="CB-CX_QF.CX_QF_Cb4_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-37070_0" Pin1InfoVect1LinkObjId="SW-37069_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4597,-234 4597,-218 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cda1f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4480,-235 4480,-218 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="capacitor" ObjectIDND0="5868@x" ObjectIDND1="5867@x" ObjectIDZND0="15556@0" Pin0InfoVect0LinkObjId="CB-CX_QF.CX_QF_Cb2_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-37044_0" Pin1InfoVect1LinkObjId="SW-37043_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4480,-235 4480,-218 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cda450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4385,-674 4609,-674 4609,-737 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer" ObjectIDND0="5848@1" ObjectIDZND0="5886@x" Pin0InfoVect0LinkObjId="g_2d5b730_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36993_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4385,-674 4609,-674 4609,-737 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c80ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4248,-674 4248,-692 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="5848@x" ObjectIDND1="g_2c40960@0" ObjectIDND2="5852@x" ObjectIDZND0="g_2cda6b0@0" Pin0InfoVect0LinkObjId="g_2cda6b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-36993_0" Pin1InfoVect1LinkObjId="g_2c40960_0" Pin1InfoVect2LinkObjId="SW-36998_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4248,-674 4248,-692 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c81140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4230,-674 4248,-674 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="5852@1" ObjectIDZND0="g_2cda6b0@0" ObjectIDZND1="5848@x" ObjectIDZND2="g_2c40960@0" Pin0InfoVect0LinkObjId="g_2cda6b0_0" Pin0InfoVect1LinkObjId="SW-36993_0" Pin0InfoVect2LinkObjId="g_2c40960_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36998_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4230,-674 4248,-674 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c813a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4290,-674 4290,-692 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2cda6b0@0" ObjectIDND1="5852@x" ObjectIDND2="5848@x" ObjectIDZND0="g_2c40960@0" Pin0InfoVect0LinkObjId="g_2c40960_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2cda6b0_0" Pin1InfoVect1LinkObjId="SW-36998_0" Pin1InfoVect2LinkObjId="SW-36993_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4290,-674 4290,-692 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c81600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4285,-674 4349,-674 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_2cda6b0@0" ObjectIDND1="5852@x" ObjectIDND2="g_2c40960@0" ObjectIDZND0="5848@0" Pin0InfoVect0LinkObjId="SW-36993_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2cda6b0_0" Pin1InfoVect1LinkObjId="SW-36998_0" Pin1InfoVect2LinkObjId="g_2c40960_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4285,-674 4349,-674 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c81860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4248,-674 4285,-674 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_2cda6b0@0" ObjectIDND1="5852@x" ObjectIDZND0="5848@x" ObjectIDZND1="g_2c40960@0" Pin0InfoVect0LinkObjId="SW-36993_0" Pin0InfoVect1LinkObjId="g_2c40960_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2cda6b0_0" Pin1InfoVect1LinkObjId="SW-36998_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4248,-674 4285,-674 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cea330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4973,-864 4990,-864 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="5799@1" ObjectIDZND0="5797@1" Pin0InfoVect0LinkObjId="SW-36443_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36445_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4973,-864 4990,-864 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cea590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5017,-864 5034,-864 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="5797@0" ObjectIDZND0="5798@0" Pin0InfoVect0LinkObjId="SW-36444_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36443_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5017,-864 5034,-864 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d03160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5123,-779 5138,-779 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5779@0" ObjectIDZND0="5783@0" Pin0InfoVect0LinkObjId="SW-36402_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2bb6060_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5123,-779 5138,-779 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cb5a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5139,-688 5123,-688 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="5784@0" ObjectIDZND0="5780@0" Pin0InfoVect0LinkObjId="g_2d0c560_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36403_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5139,-688 5123,-688 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cb76a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5174,-779 5191,-779 5191,-749 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="5783@1" ObjectIDZND0="5818@1" Pin0InfoVect0LinkObjId="SW-36660_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36402_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5174,-779 5191,-779 5191,-749 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cb7900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5191,-722 5191,-688 5175,-688 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="5818@0" ObjectIDZND0="5784@1" Pin0InfoVect0LinkObjId="SW-36403_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36660_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5191,-722 5191,-688 5175,-688 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d0c560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5073,-663 5123,-663 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="5846@1" ObjectIDZND0="5780@0" Pin0InfoVect0LinkObjId="g_2cb5a40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36990_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5073,-663 5123,-663 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d0c7c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4976,-663 4993,-663 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="5847@1" ObjectIDZND0="5845@1" Pin0InfoVect0LinkObjId="SW-36989_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36991_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4976,-663 4993,-663 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d0ca20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5020,-663 5037,-663 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="5845@0" ObjectIDZND0="5846@0" Pin0InfoVect0LinkObjId="SW-36990_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36989_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5020,-663 5037,-663 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c869c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5123,-825 5152,-825 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5779@0" ObjectIDZND0="5788@0" Pin0InfoVect0LinkObjId="SW-36426_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2bb6060_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5123,-825 5152,-825 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c873e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5188,-825 5207,-825 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="5788@1" ObjectIDZND0="g_2c86c20@1" Pin0InfoVect0LinkObjId="g_2c86c20_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36426_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5188,-825 5207,-825 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c87640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5238,-825 5264,-825 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2c86c20@0" ObjectIDZND0="g_2c878a0@0" Pin0InfoVect0LinkObjId="g_2c878a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c86c20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5238,-825 5264,-825 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ce5bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5123,-908 5141,-908 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5779@0" ObjectIDZND0="5810@0" Pin0InfoVect0LinkObjId="SW-36578_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2bb6060_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5123,-908 5141,-908 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ce5e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5177,-908 5194,-908 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="5810@1" ObjectIDZND0="5809@1" Pin0InfoVect0LinkObjId="SW-36577_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36578_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5177,-908 5194,-908 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ce6090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5221,-908 5238,-908 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="5809@0" ObjectIDZND0="5811@0" Pin0InfoVect0LinkObjId="SW-36579_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36577_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5221,-908 5238,-908 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d45b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5274,-908 5297,-908 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="5811@1" ObjectIDZND0="g_2d45ff0@0" ObjectIDZND1="g_2d46ff0@0" ObjectIDZND2="18074@1" Pin0InfoVect0LinkObjId="g_2d45ff0_0" Pin0InfoVect1LinkObjId="g_2d46ff0_0" Pin0InfoVect2LinkObjId="g_2d45d90_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36579_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5274,-908 5297,-908 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d45d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5297,-908 5337,-908 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_2d45ff0@0" ObjectIDND1="g_2d46ff0@0" ObjectIDND2="5811@x" ObjectIDZND0="18074@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2d45ff0_0" Pin1InfoVect1LinkObjId="g_2d46ff0_0" Pin1InfoVect2LinkObjId="SW-36579_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5297,-908 5337,-908 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d46b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5297,-908 5297,-937 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="powerLine" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="5811@x" ObjectIDND1="18074@1" ObjectIDZND0="g_2d45ff0@0" ObjectIDZND1="g_2d46ff0@0" Pin0InfoVect0LinkObjId="g_2d45ff0_0" Pin0InfoVect1LinkObjId="g_2d46ff0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-36579_0" Pin1InfoVect1LinkObjId="g_2d45d90_1" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5297,-908 5297,-937 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d46d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5297,-937 5314,-937 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="powerLine" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="5811@x" ObjectIDND1="18074@1" ObjectIDND2="g_2d46ff0@0" ObjectIDZND0="g_2d45ff0@0" Pin0InfoVect0LinkObjId="g_2d45ff0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-36579_0" Pin1InfoVect1LinkObjId="g_2d45d90_1" Pin1InfoVect2LinkObjId="g_2d46ff0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5297,-937 5314,-937 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d477b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5297,-937 5297,-963 5316,-963 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="powerLine" EndDevType0="lightningRod" ObjectIDND0="g_2d45ff0@0" ObjectIDND1="5811@x" ObjectIDND2="18074@1" ObjectIDZND0="g_2d46ff0@1" Pin0InfoVect0LinkObjId="g_2d46ff0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2d45ff0_0" Pin1InfoVect1LinkObjId="SW-36579_0" Pin1InfoVect2LinkObjId="g_2d45d90_1" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5297,-937 5297,-963 5316,-963 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d47a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5347,-963 5368,-963 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2d46ff0@0" ObjectIDZND0="g_2ba6590@0" Pin0InfoVect0LinkObjId="g_2ba6590_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d46ff0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5347,-963 5368,-963 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cae860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5123,-1151 5142,-1151 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5779@0" ObjectIDZND0="5804@0" Pin0InfoVect0LinkObjId="SW-36524_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2bb6060_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5123,-1151 5142,-1151 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2caeac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5178,-1151 5195,-1151 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="5804@1" ObjectIDZND0="5803@1" Pin0InfoVect0LinkObjId="SW-36523_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36524_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5178,-1151 5195,-1151 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2caed20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5222,-1151 5239,-1151 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="5803@0" ObjectIDZND0="5805@0" Pin0InfoVect0LinkObjId="SW-36525_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36523_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5222,-1151 5239,-1151 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2caef80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5298,-1151 5298,-1180 5315,-1180 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="powerLine" EndDevType0="lightningRod" ObjectIDND0="5805@x" ObjectIDND1="39627@1" ObjectIDZND0="g_2caf6a0@0" Pin0InfoVect0LinkObjId="g_2caf6a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-36525_0" Pin1InfoVect1LinkObjId="g_2caf440_1" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5298,-1151 5298,-1180 5315,-1180 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2caf1e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5275,-1151 5298,-1151 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="5805@1" ObjectIDZND0="g_2caf6a0@0" ObjectIDZND1="39627@1" Pin0InfoVect0LinkObjId="g_2caf6a0_0" Pin0InfoVect1LinkObjId="g_2caf440_1" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36525_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5275,-1151 5298,-1151 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2caf440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5298,-1151 5348,-1151 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="powerLine" ObjectIDND0="g_2caf6a0@0" ObjectIDND1="5805@x" ObjectIDZND0="39627@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2caf6a0_0" Pin1InfoVect1LinkObjId="SW-36525_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5298,-1151 5348,-1151 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cfde00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5123,-1077 5143,-1077 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5779@0" ObjectIDZND0="5786@0" Pin0InfoVect0LinkObjId="SW-36421_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2bb6060_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5123,-1077 5143,-1077 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cfe060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5234,-1077 5234,-1106 5251,-1106 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2c3eac0@0" ObjectIDND1="5786@x" ObjectIDZND0="g_2cfe2c0@0" Pin0InfoVect0LinkObjId="g_2cfe2c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2c3eac0_0" Pin1InfoVect1LinkObjId="SW-36421_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5234,-1077 5234,-1106 5251,-1106 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cfef90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5234,-1077 5179,-1077 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_2cfe2c0@0" ObjectIDND1="g_2c3eac0@0" ObjectIDZND0="5786@1" Pin0InfoVect0LinkObjId="SW-36421_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2cfe2c0_0" Pin1InfoVect1LinkObjId="g_2c3eac0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5234,-1077 5179,-1077 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c62490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5123,-999 5139,-999 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5779@0" ObjectIDZND0="5807@0" Pin0InfoVect0LinkObjId="SW-36551_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2bb6060_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5123,-999 5139,-999 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c626f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5175,-999 5192,-999 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="5807@1" ObjectIDZND0="5806@1" Pin0InfoVect0LinkObjId="SW-36550_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36551_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5175,-999 5192,-999 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c62950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5295,-999 5295,-1028 5312,-1028 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="powerLine" EndDevType0="lightningRod" ObjectIDND0="5808@x" ObjectIDND1="18073@1" ObjectIDZND0="g_2c62e10@0" Pin0InfoVect0LinkObjId="g_2c62e10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-36552_0" Pin1InfoVect1LinkObjId="g_2c62bb0_1" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5295,-999 5295,-1028 5312,-1028 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c62bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5295,-999 5335,-999 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="powerLine" ObjectIDND0="g_2c62e10@0" ObjectIDND1="5808@x" ObjectIDZND0="18073@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2c62e10_0" Pin1InfoVect1LinkObjId="SW-36552_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5295,-999 5335,-999 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c63c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5123,-317 5138,-317 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5780@0" ObjectIDZND0="5875@0" Pin0InfoVect0LinkObjId="SW-37094_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2cb5a40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5123,-317 5138,-317 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c63ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5174,-317 5191,-317 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="5875@1" ObjectIDZND0="5874@1" Pin0InfoVect0LinkObjId="SW-37092_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37094_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5174,-317 5191,-317 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c64100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5218,-317 5235,-317 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="5874@0" ObjectIDZND0="5876@0" Pin0InfoVect0LinkObjId="SW-37095_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37092_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5218,-317 5235,-317 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c64360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5271,-317 5304,-317 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" ObjectIDND0="5876@1" ObjectIDZND0="22109@0" Pin0InfoVect0LinkObjId="EC-CX_QF.CX_QF_3566LD_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37095_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5271,-317 5304,-317 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c77f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5123,-629 5137,-629 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5780@0" ObjectIDZND0="5813@0" Pin0InfoVect0LinkObjId="SW-36605_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2cb5a40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5123,-629 5137,-629 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c78160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5173,-629 5190,-629 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="5813@1" ObjectIDZND0="5812@1" Pin0InfoVect0LinkObjId="SW-36604_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36605_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5173,-629 5190,-629 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c783c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5217,-629 5234,-629 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="5812@0" ObjectIDZND0="5814@0" Pin0InfoVect0LinkObjId="SW-36606_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36604_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5217,-629 5234,-629 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c78620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5293,-629 5293,-658 5310,-658 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="powerLine" EndDevType0="lightningRod" ObjectIDND0="5814@x" ObjectIDND1="18071@1" ObjectIDZND0="g_2d23c60@0" Pin0InfoVect0LinkObjId="g_2d23c60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-36606_0" Pin1InfoVect1LinkObjId="g_2c78ae0_1" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5293,-629 5293,-658 5310,-658 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c78880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5270,-629 5293,-629 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="5814@1" ObjectIDZND0="g_2d23c60@0" ObjectIDZND1="18071@1" Pin0InfoVect0LinkObjId="g_2d23c60_0" Pin0InfoVect1LinkObjId="g_2c78ae0_1" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36606_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5270,-629 5293,-629 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c78ae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5293,-629 5337,-629 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="powerLine" ObjectIDND0="g_2d23c60@0" ObjectIDND1="5814@x" ObjectIDZND0="18071@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2d23c60_0" Pin1InfoVect1LinkObjId="SW-36606_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5293,-629 5337,-629 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ccade0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5123,-543 5136,-543 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5780@0" ObjectIDZND0="5816@0" Pin0InfoVect0LinkObjId="SW-36632_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2cb5a40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5123,-543 5136,-543 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ccb040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5172,-543 5189,-543 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="5816@1" ObjectIDZND0="5815@1" Pin0InfoVect0LinkObjId="SW-36631_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36632_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5172,-543 5189,-543 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ccb2a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5216,-543 5233,-543 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="5815@0" ObjectIDZND0="5817@0" Pin0InfoVect0LinkObjId="SW-36633_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36631_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5216,-543 5233,-543 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ccb500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5292,-543 5292,-572 5309,-572 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="powerLine" EndDevType0="lightningRod" ObjectIDND0="5817@x" ObjectIDND1="34580@1" ObjectIDZND0="g_2ccbc20@0" Pin0InfoVect0LinkObjId="g_2ccbc20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-36633_0" Pin1InfoVect1LinkObjId="g_2ccb9c0_1" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5292,-543 5292,-572 5309,-572 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ccb760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5269,-543 5292,-543 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="5817@1" ObjectIDZND0="g_2ccbc20@0" ObjectIDZND1="34580@1" Pin0InfoVect0LinkObjId="g_2ccbc20_0" Pin0InfoVect1LinkObjId="g_2ccb9c0_1" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36633_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5269,-543 5292,-543 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ccb9c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5292,-543 5333,-543 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="powerLine" ObjectIDND0="g_2ccbc20@0" ObjectIDND1="5817@x" ObjectIDZND0="34580@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2ccbc20_0" Pin1InfoVect1LinkObjId="SW-36633_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5292,-543 5333,-543 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ccec80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5123,-461 5150,-461 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5780@0" ObjectIDZND0="5789@0" Pin0InfoVect0LinkObjId="SW-36429_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2cb5a40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5123,-461 5150,-461 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ccf6c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5186,-461 5205,-461 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="5789@1" ObjectIDZND0="g_2cceee0@1" Pin0InfoVect0LinkObjId="g_2cceee0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36429_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5186,-461 5205,-461 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ccf920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5236,-461 5262,-461 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2cceee0@0" ObjectIDZND0="g_2ccfb80@0" Pin0InfoVect0LinkObjId="g_2ccfb80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2cceee0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5236,-461 5262,-461 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d22070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5123,-385 5141,-385 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5780@0" ObjectIDZND0="5787@0" Pin0InfoVect0LinkObjId="SW-36424_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2cb5a40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5123,-385 5141,-385 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d222d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5232,-385 5232,-414 5249,-414 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2c3fa10@0" ObjectIDND1="5787@x" ObjectIDZND0="g_2d22530@0" Pin0InfoVect0LinkObjId="g_2d22530_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2c3fa10_0" Pin1InfoVect1LinkObjId="SW-36424_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5232,-385 5232,-414 5249,-414 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d23260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5232,-385 5177,-385 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_2d22530@0" ObjectIDND1="g_2c3fa10@0" ObjectIDZND0="5787@1" Pin0InfoVect0LinkObjId="SW-36424_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2d22530_0" Pin1InfoVect1LinkObjId="g_2c3fa10_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5232,-385 5177,-385 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bb6060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5070,-864 5123,-864 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="5798@1" ObjectIDZND0="5779@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36444_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5070,-864 5123,-864 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bb6250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4194,-674 4064,-674 4064,-736 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer" ObjectIDND0="5852@0" ObjectIDZND0="5885@x" Pin0InfoVect0LinkObjId="g_2c9a0e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36998_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4194,-674 4064,-674 4064,-736 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bb8770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3998,-267 3998,-277 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="5840@1" ObjectIDZND0="g_2bb7ba0@1" Pin0InfoVect0LinkObjId="g_2bb7ba0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36818_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3998,-267 3998,-277 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bb89d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3998,-330 3998,-339 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="lightningRod" ObjectIDND0="g_2bb7ba0@0" ObjectIDZND0="5838@x" ObjectIDZND1="g_2cedc50@0" Pin0InfoVect0LinkObjId="SW-36816_0" Pin0InfoVect1LinkObjId="g_2cedc50_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2bb7ba0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3998,-330 3998,-339 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bb8c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4210,-231 4210,-243 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_2c5a5f0@0" ObjectIDND1="34539@x" ObjectIDZND0="5821@0" Pin0InfoVect0LinkObjId="SW-36682_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2c5a5f0_0" Pin1InfoVect1LinkObjId="EC-CX_QF.055Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4210,-231 4210,-243 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bb9a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4210,-279 4210,-293 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="5821@1" ObjectIDZND0="g_2bb8e90@1" Pin0InfoVect0LinkObjId="g_2bb8e90_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36682_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4210,-279 4210,-293 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bb9cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4210,-346 4210,-358 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_2bb8e90@0" ObjectIDZND0="5819@0" Pin0InfoVect0LinkObjId="SW-36680_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2bb8e90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4210,-346 4210,-358 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bbaaf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4118,-230 4118,-243 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_2c281a0@0" ObjectIDND1="34538@x" ObjectIDZND0="5829@0" Pin0InfoVect0LinkObjId="SW-36738_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2c281a0_0" Pin1InfoVect1LinkObjId="EC-CX_QF.054Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4118,-230 4118,-243 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bbad50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4480,-235 4480,-246 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" EndDevType0="switch" ObjectIDND0="5868@x" ObjectIDND1="15556@x" ObjectIDZND0="5867@0" Pin0InfoVect0LinkObjId="SW-37043_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-37044_0" Pin1InfoVect1LinkObjId="CB-CX_QF.CX_QF_Cb2_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4480,-235 4480,-246 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bbafb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4597,-234 4597,-246 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" EndDevType0="switch" ObjectIDND0="5873@x" ObjectIDND1="15557@x" ObjectIDZND0="5872@0" Pin0InfoVect0LinkObjId="SW-37069_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-37070_0" Pin1InfoVect1LinkObjId="CB-CX_QF.CX_QF_Cb4_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4597,-234 4597,-246 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bbb210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4118,-279 4118,-292 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="5829@1" ObjectIDZND0="g_2bb9f20@1" Pin0InfoVect0LinkObjId="g_2bb9f20_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36738_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4118,-279 4118,-292 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bbc040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4597,-282 4597,-294 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="5872@1" ObjectIDZND0="g_2bbb470@1" Pin0InfoVect0LinkObjId="g_2bbb470_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37069_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4597,-282 4597,-294 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bbc2a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4597,-347 4597,-358 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_2bbb470@0" ObjectIDZND0="5869@0" Pin0InfoVect0LinkObjId="SW-37065_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2bbb470_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4597,-347 4597,-358 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c95510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4480,-282 4480,-294 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="5867@1" ObjectIDZND0="g_2bbc500@1" Pin0InfoVect0LinkObjId="g_2bbc500_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37043_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4480,-282 4480,-294 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c95770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4480,-347 4480,-358 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_2bbc500@0" ObjectIDZND0="5864@0" Pin0InfoVect0LinkObjId="SW-37039_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2bbc500_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4480,-347 4480,-358 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c959d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5219,-999 5236,-999 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="5806@0" ObjectIDZND0="5808@0" Pin0InfoVect0LinkObjId="SW-36552_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36550_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5219,-999 5236,-999 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c95c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5272,-999 5295,-999 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="5808@1" ObjectIDZND0="g_2c62e10@0" ObjectIDZND1="18073@1" Pin0InfoVect0LinkObjId="g_2c62e10_0" Pin0InfoVect1LinkObjId="g_2c62bb0_1" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36552_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5272,-999 5295,-999 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2c99b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4024,-778 4024,-808 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="5885@1" ObjectIDZND0="5795@x" ObjectIDZND1="5794@x" Pin0InfoVect0LinkObjId="SW-36441_0" Pin0InfoVect1LinkObjId="SW-36440_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2bb6250_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4024,-778 4024,-808 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2c99d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4570,-778 4570,-808 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="5886@1" ObjectIDZND0="5843@x" ObjectIDZND1="5842@x" Pin0InfoVect0LinkObjId="SW-36986_0" Pin0InfoVect1LinkObjId="SW-36985_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d5b730_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4570,-778 4570,-808 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2c99ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4570,-808 4570,-841 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5886@x" ObjectIDND1="5843@x" ObjectIDZND0="5842@0" Pin0InfoVect0LinkObjId="SW-36985_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2d5b730_0" Pin1InfoVect1LinkObjId="SW-36986_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4570,-808 4570,-841 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2c9a0e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4024,-840 4024,-808 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer" EndDevType1="switch" ObjectIDND0="5794@0" ObjectIDZND0="5885@x" ObjectIDZND1="5795@x" Pin0InfoVect0LinkObjId="g_2bb6250_0" Pin0InfoVect1LinkObjId="SW-36441_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36440_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4024,-840 4024,-808 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2bc2040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4569,-955 4569,-978 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="5854@1" ObjectIDZND0="5853@x" ObjectIDZND1="5855@x" Pin0InfoVect0LinkObjId="SW-37000_0" Pin0InfoVect1LinkObjId="SW-37005_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37004_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4569,-955 4569,-978 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2bc2250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4024,-955 4024,-978 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="5877@1" ObjectIDZND0="5882@x" ObjectIDZND1="5878@x" Pin0InfoVect0LinkObjId="SW-37124_0" Pin0InfoVect1LinkObjId="SW-37120_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37119_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4024,-955 4024,-978 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2bc2480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4569,-978 4569,-999 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="5855@x" ObjectIDND1="5854@x" ObjectIDZND0="5853@0" Pin0InfoVect0LinkObjId="SW-37000_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-37005_0" Pin1InfoVect1LinkObjId="SW-37004_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4569,-978 4569,-999 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2bc26b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4024,-978 4024,-1002 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="5878@x" ObjectIDND1="5877@x" ObjectIDZND0="5882@0" Pin0InfoVect0LinkObjId="SW-37124_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-37120_0" Pin1InfoVect1LinkObjId="SW-37119_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4024,-978 4024,-1002 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2bc28e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4024,-1029 4024,-1049 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="5882@1" ObjectIDZND0="5880@x" ObjectIDZND1="5879@x" Pin0InfoVect0LinkObjId="SW-37122_0" Pin0InfoVect1LinkObjId="SW-37121_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37124_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4024,-1029 4024,-1049 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2bc2b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4569,-1026 4569,-1049 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="5853@1" ObjectIDZND0="5857@x" ObjectIDZND1="5856@x" Pin0InfoVect0LinkObjId="SW-37007_0" Pin0InfoVect1LinkObjId="SW-37006_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37000_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4569,-1026 4569,-1049 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bca010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3698,-394 3717,-394 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" ObjectIDND0="5835@x" ObjectIDND1="5836@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-36792_0" Pin1InfoVect1LinkObjId="SW-36793_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3698,-394 3717,-394 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bca270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3698,-380 3698,-394 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="5835@1" ObjectIDZND0="5836@x" Pin0InfoVect0LinkObjId="SW-36793_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36792_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3698,-380 3698,-394 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bca4d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3698,-394 3698,-413 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="5835@x" ObjectIDZND0="5836@0" Pin0InfoVect0LinkObjId="SW-36793_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36792_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3698,-394 3698,-413 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bca730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3698,-339 3725,-339 3725,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="5835@x" ObjectIDND1="g_2c140b0@0" ObjectIDZND0="g_2bcabf0@0" Pin0InfoVect0LinkObjId="g_2bcabf0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-36792_0" Pin1InfoVect1LinkObjId="g_2c140b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3698,-339 3725,-339 3725,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bca990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3698,-339 3698,-353 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="breaker" ObjectIDND0="g_2bcabf0@0" ObjectIDND1="g_2c140b0@0" ObjectIDZND0="5835@0" Pin0InfoVect0LinkObjId="SW-36792_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2bcabf0_0" Pin1InfoVect1LinkObjId="g_2c140b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3698,-339 3698,-353 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bcb9a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3698,-217 3716,-217 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="capacitor" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2bcd010@0" ObjectIDND1="15555@x" ObjectIDND2="5837@x" ObjectIDZND0="10681@0" Pin0InfoVect0LinkObjId="SW-57361_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2bcd010_0" Pin1InfoVect1LinkObjId="CB-CX_QF.CX_QF_Cb1_0" Pin1InfoVect2LinkObjId="SW-36794_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3698,-217 3716,-217 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bcbc00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3752,-217 3769,-217 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="10681@1" ObjectIDZND0="g_2bcbe60@0" Pin0InfoVect0LinkObjId="g_2bcbe60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57361_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3752,-217 3769,-217 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bcc8f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3698,-231 3698,-217 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="capacitor" EndDevType2="switch" ObjectIDND0="5837@0" ObjectIDZND0="g_2bcd010@0" ObjectIDZND1="15555@x" ObjectIDZND2="10681@x" Pin0InfoVect0LinkObjId="g_2bcd010_0" Pin0InfoVect1LinkObjId="CB-CX_QF.CX_QF_Cb1_0" Pin0InfoVect2LinkObjId="SW-57361_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36794_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3698,-231 3698,-217 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bccb50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3698,-217 3698,-192 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="capacitor" ObjectIDND0="g_2bcd010@0" ObjectIDND1="10681@x" ObjectIDND2="5837@x" ObjectIDZND0="15555@0" Pin0InfoVect0LinkObjId="CB-CX_QF.CX_QF_Cb1_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2bcd010_0" Pin1InfoVect1LinkObjId="SW-57361_0" Pin1InfoVect2LinkObjId="SW-36794_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3698,-217 3698,-192 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bccdb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3698,-217 3667,-217 3667,-202 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="15555@x" ObjectIDND1="10681@x" ObjectIDND2="5837@x" ObjectIDZND0="g_2bcd010@0" Pin0InfoVect0LinkObjId="g_2bcd010_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="CB-CX_QF.CX_QF_Cb1_0" Pin1InfoVect1LinkObjId="SW-57361_0" Pin1InfoVect2LinkObjId="SW-36794_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3698,-217 3667,-217 3667,-202 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c14d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3698,-267 3698,-277 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="5837@1" ObjectIDZND0="g_2c140b0@1" Pin0InfoVect0LinkObjId="g_2c140b0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36794_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3698,-267 3698,-277 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c14f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3698,-330 3698,-339 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="lightningRod" ObjectIDND0="g_2c140b0@0" ObjectIDZND0="5835@x" ObjectIDZND1="g_2bcabf0@0" Pin0InfoVect0LinkObjId="SW-36792_0" Pin0InfoVect1LinkObjId="g_2bcabf0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c140b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3698,-330 3698,-339 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c151a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3698,-465 3698,-445 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5775@0" ObjectIDZND0="5836@1" Pin0InfoVect0LinkObjId="SW-36793_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ca9c80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3698,-465 3698,-445 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c3b420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4940,-663 4903,-663 4903,-739 4630,-739 4627,-738 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer" ObjectIDND0="5847@0" ObjectIDZND0="5886@0" Pin0InfoVect0LinkObjId="g_2d5b730_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36991_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4940,-663 4903,-663 4903,-739 4630,-739 4627,-738 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2c3d550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5408,-963 5434,-963 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="breaker" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5408,-963 5434,-963 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2c3d7b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5461,-963 5482,-963 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" ObjectIDND0="0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5461,-963 5482,-963 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c3f550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5323,-1077 5293,-1077 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2c41e90@0" ObjectIDZND0="g_2c3eac0@1" Pin0InfoVect0LinkObjId="g_2c3eac0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c41e90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5323,-1077 5293,-1077 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c3f7b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5261,-1077 5234,-1077 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2c3eac0@0" ObjectIDZND0="g_2cfe2c0@0" ObjectIDZND1="5786@x" Pin0InfoVect0LinkObjId="g_2cfe2c0_0" Pin0InfoVect1LinkObjId="SW-36421_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c3eac0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5261,-1077 5234,-1077 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c404a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5321,-385 5298,-385 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2d234c0@0" ObjectIDZND0="g_2c3fa10@1" Pin0InfoVect0LinkObjId="g_2c3fa10_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d234c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5321,-385 5298,-385 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c40700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5266,-385 5232,-385 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2c3fa10@0" ObjectIDZND0="g_2d22530@0" ObjectIDZND1="5787@x" Pin0InfoVect0LinkObjId="g_2d22530_0" Pin0InfoVect1LinkObjId="SW-36424_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c3fa10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5266,-385 5232,-385 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c42730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3789,-538 3822,-538 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2c3e3a0@0" ObjectIDND1="5790@x" ObjectIDZND0="g_2c01640@0" Pin0InfoVect0LinkObjId="g_2c01640_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2c3e3a0_0" Pin1InfoVect1LinkObjId="SW-36430_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3789,-538 3822,-538 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c42990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3789,-608 3789,-586 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_2bff2b0@0" ObjectIDZND0="g_2c3e3a0@0" Pin0InfoVect0LinkObjId="g_2c3e3a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2bff2b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3789,-608 3789,-586 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c42bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3789,-554 3789,-538 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2c3e3a0@1" ObjectIDZND0="g_2c01640@0" ObjectIDZND1="5790@x" Pin0InfoVect0LinkObjId="g_2c01640_0" Pin0InfoVect1LinkObjId="SW-36430_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c3e3a0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3789,-554 3789,-538 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2c43310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4382,-897 4555,-897 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="5862@1" ObjectIDZND0="5778@0" Pin0InfoVect0LinkObjId="g_2c43570_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37032_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4382,-897 4555,-897 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2c43570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4569,-919 4569,-897 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="5854@0" ObjectIDZND0="5778@0" Pin0InfoVect0LinkObjId="g_2c43310_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37004_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4569,-919 4569,-897 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2c437d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4570,-876 4570,-897 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="5842@1" ObjectIDZND0="5778@0" Pin0InfoVect0LinkObjId="g_2c43310_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36985_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4570,-876 4570,-897 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2c43a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4024,-919 4024,-897 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="5877@0" ObjectIDZND0="5777@0" Pin0InfoVect0LinkObjId="g_2c43c90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37119_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4024,-919 4024,-897 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2c43c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4024,-876 4024,-897 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="5794@1" ObjectIDZND0="5777@0" Pin0InfoVect0LinkObjId="g_2c43a30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36440_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4024,-876 4024,-897 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2c43ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4533,-1149 4569,-1149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="powerLine" ObjectIDND0="g_2c97200@0" ObjectIDND1="g_2bcf9d0@0" ObjectIDZND0="5858@x" ObjectIDZND1="5856@x" ObjectIDZND2="11498@1" Pin0InfoVect0LinkObjId="SW-37008_0" Pin0InfoVect1LinkObjId="SW-37006_0" Pin0InfoVect2LinkObjId="g_2c44610_1" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2c97200_0" Pin1InfoVect1LinkObjId="g_2bcf9d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4533,-1149 4569,-1149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2c44150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4494,-1149 4533,-1149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="powerLine" ObjectIDND0="g_2c97200@0" ObjectIDZND0="5858@x" ObjectIDZND1="5856@x" ObjectIDZND2="11498@1" Pin0InfoVect0LinkObjId="SW-37008_0" Pin0InfoVect1LinkObjId="SW-37006_0" Pin0InfoVect2LinkObjId="g_2c44610_1" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c97200_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4494,-1149 4533,-1149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2c443b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4533,-1149 4533,-1136 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2c97200@0" ObjectIDND1="5858@x" ObjectIDND2="5856@x" ObjectIDZND0="g_2bcf9d0@0" Pin0InfoVect0LinkObjId="g_2bcf9d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2c97200_0" Pin1InfoVect1LinkObjId="SW-37008_0" Pin1InfoVect2LinkObjId="SW-37006_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4533,-1149 4533,-1136 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2c44610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4569,-1149 4569,-1183 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_2c97200@0" ObjectIDND1="g_2bcf9d0@0" ObjectIDND2="5858@x" ObjectIDZND0="11498@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2c97200_0" Pin1InfoVect1LinkObjId="g_2bcf9d0_0" Pin1InfoVect2LinkObjId="SW-37008_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4569,-1149 4569,-1183 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2c44870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4587,-1122 4569,-1122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="5858@0" ObjectIDZND0="g_2c97200@0" ObjectIDZND1="g_2bcf9d0@0" ObjectIDZND2="11498@1" Pin0InfoVect0LinkObjId="g_2c97200_0" Pin0InfoVect1LinkObjId="g_2bcf9d0_0" Pin0InfoVect2LinkObjId="g_2c44610_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37008_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4587,-1122 4569,-1122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2c44ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4569,-1122 4569,-1149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="5858@x" ObjectIDND1="5856@x" ObjectIDZND0="g_2c97200@0" ObjectIDZND1="g_2bcf9d0@0" ObjectIDZND2="11498@1" Pin0InfoVect0LinkObjId="g_2c97200_0" Pin0InfoVect1LinkObjId="g_2bcf9d0_0" Pin0InfoVect2LinkObjId="g_2c44610_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-37008_0" Pin1InfoVect1LinkObjId="SW-37006_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4569,-1122 4569,-1149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2c44d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4044,-1122 4024,-1122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="5881@0" ObjectIDZND0="g_2c95e90@0" ObjectIDZND1="g_2d4c740@0" ObjectIDZND2="11431@1" Pin0InfoVect0LinkObjId="g_2c95e90_0" Pin0InfoVect1LinkObjId="g_2d4c740_0" Pin0InfoVect2LinkObjId="g_2c45910_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37123_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4044,-1122 4024,-1122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2c44f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3988,-1149 4024,-1149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="powerLine" ObjectIDND0="g_2c95e90@0" ObjectIDND1="g_2d4c740@0" ObjectIDZND0="5881@x" ObjectIDZND1="5879@x" ObjectIDZND2="11431@1" Pin0InfoVect0LinkObjId="SW-37123_0" Pin0InfoVect1LinkObjId="SW-37121_0" Pin0InfoVect2LinkObjId="g_2c45910_1" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2c95e90_0" Pin1InfoVect1LinkObjId="g_2d4c740_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3988,-1149 4024,-1149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2c451f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3949,-1149 3988,-1149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="powerLine" ObjectIDND0="g_2c95e90@0" ObjectIDZND0="5881@x" ObjectIDZND1="5879@x" ObjectIDZND2="11431@1" Pin0InfoVect0LinkObjId="SW-37123_0" Pin0InfoVect1LinkObjId="SW-37121_0" Pin0InfoVect2LinkObjId="g_2c45910_1" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c95e90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3949,-1149 3988,-1149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2c45450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3988,-1149 3988,-1136 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2c95e90@0" ObjectIDND1="5881@x" ObjectIDND2="5879@x" ObjectIDZND0="g_2d4c740@0" Pin0InfoVect0LinkObjId="g_2d4c740_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2c95e90_0" Pin1InfoVect1LinkObjId="SW-37123_0" Pin1InfoVect2LinkObjId="SW-37121_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3988,-1149 3988,-1136 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2c456b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4024,-1122 4024,-1149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="5881@x" ObjectIDND1="5879@x" ObjectIDZND0="g_2c95e90@0" ObjectIDZND1="g_2d4c740@0" ObjectIDZND2="11431@1" Pin0InfoVect0LinkObjId="g_2c95e90_0" Pin0InfoVect1LinkObjId="g_2d4c740_0" Pin0InfoVect2LinkObjId="g_2c45910_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-37123_0" Pin1InfoVect1LinkObjId="SW-37121_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4024,-1122 4024,-1149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2c45910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4024,-1149 4024,-1182 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="powerLine" ObjectIDND0="5881@x" ObjectIDND1="5879@x" ObjectIDND2="g_2c95e90@0" ObjectIDZND0="11431@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-37123_0" Pin1InfoVect1LinkObjId="SW-37121_0" Pin1InfoVect2LinkObjId="g_2c95e90_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4024,-1149 4024,-1182 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2c48330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4024,-1122 4024,-1102 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" ObjectIDND0="g_2c95e90@0" ObjectIDND1="g_2d4c740@0" ObjectIDND2="11431@1" ObjectIDZND0="5879@1" Pin0InfoVect0LinkObjId="SW-37121_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2c95e90_0" Pin1InfoVect1LinkObjId="g_2d4c740_0" Pin1InfoVect2LinkObjId="g_2c45910_1" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4024,-1122 4024,-1102 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2c48590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4024,-1066 4024,-1049 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="5879@0" ObjectIDZND0="5882@x" ObjectIDZND1="5880@x" Pin0InfoVect0LinkObjId="SW-37124_0" Pin0InfoVect1LinkObjId="SW-37122_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37121_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4024,-1066 4024,-1049 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2c4afb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4569,-1122 4569,-1102 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" ObjectIDND0="g_2c97200@0" ObjectIDND1="g_2bcf9d0@0" ObjectIDND2="11498@1" ObjectIDZND0="5856@1" Pin0InfoVect0LinkObjId="SW-37006_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2c97200_0" Pin1InfoVect1LinkObjId="g_2bcf9d0_0" Pin1InfoVect2LinkObjId="g_2c44610_1" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4569,-1122 4569,-1102 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2c4b210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4569,-1066 4569,-1049 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="5856@0" ObjectIDZND0="5853@x" ObjectIDZND1="5857@x" Pin0InfoVect0LinkObjId="SW-37000_0" Pin0InfoVect1LinkObjId="SW-37007_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37006_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4569,-1066 4569,-1049 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bf5eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4079,-737 4115,-737 4115,-663 4881,-663 4881,-864 4937,-864 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="switch" ObjectIDND0="5885@0" ObjectIDZND0="5799@0" Pin0InfoVect0LinkObjId="SW-36445_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2bb6250_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4079,-737 4115,-737 4115,-663 4881,-663 4881,-864 4937,-864 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bfcec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4023,-465 4023,-488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5775@0" ObjectIDZND0="5801@0" Pin0InfoVect0LinkObjId="SW-36447_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ca9c80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4023,-465 4023,-488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bfd120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4023,-524 4023,-551 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="5801@1" ObjectIDZND0="5800@0" Pin0InfoVect0LinkObjId="SW-36446_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36447_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4023,-524 4023,-551 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bfd380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4023,-578 4023,-603 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="5800@1" ObjectIDZND0="5802@0" Pin0InfoVect0LinkObjId="SW-36448_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36446_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4023,-578 4023,-603 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bfd5e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4023,-639 4023,-695 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer" ObjectIDND0="5802@1" ObjectIDZND0="5885@2" Pin0InfoVect0LinkObjId="g_2bb6250_2" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36448_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4023,-639 4023,-695 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ea5300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3843,-338 3870,-338 3870,-322 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="29727@x" ObjectIDND1="g_2ea6570@0" ObjectIDZND0="g_2ea57c0@0" Pin0InfoVect0LinkObjId="g_2ea57c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-195419_0" Pin1InfoVect1LinkObjId="g_2ea6570_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3843,-338 3870,-338 3870,-322 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ea5560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3843,-338 3843,-352 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="breaker" ObjectIDND0="g_2ea57c0@0" ObjectIDND1="g_2ea6570@0" ObjectIDZND0="29727@0" Pin0InfoVect0LinkObjId="SW-195419_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2ea57c0_0" Pin1InfoVect1LinkObjId="g_2ea6570_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3843,-338 3843,-352 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ea72c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3843,-257 3843,-276 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_2ea6570@1" Pin0InfoVect0LinkObjId="g_2ea6570_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3843,-257 3843,-276 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ea7520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3843,-329 3843,-338 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="lightningRod" ObjectIDND0="g_2ea6570@0" ObjectIDZND0="29727@x" ObjectIDZND1="g_2ea57c0@0" Pin0InfoVect0LinkObjId="SW-195419_0" Pin0InfoVect1LinkObjId="g_2ea57c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ea6570_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3843,-329 3843,-338 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ea7780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3843,-465 3843,-444 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5775@0" ObjectIDZND0="29728@1" Pin0InfoVect0LinkObjId="SW-36400_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ca9c80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3843,-465 3843,-444 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ead820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3998,-465 3998,-444 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5775@0" ObjectIDZND0="5839@1" Pin0InfoVect0LinkObjId="SW-36817_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ca9c80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3998,-465 3998,-444 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2eb1260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3998,-380 3998,-411 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="5838@1" ObjectIDZND0="5839@0" Pin0InfoVect0LinkObjId="SW-36817_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36816_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3998,-380 3998,-411 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2eb14c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3843,-379 3843,-412 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29727@1" ObjectIDZND0="29728@0" Pin0InfoVect0LinkObjId="SW-36400_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-195419_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3843,-379 3843,-412 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2eb7b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4210,-385 4210,-412 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="5819@1" ObjectIDZND0="5820@0" Pin0InfoVect0LinkObjId="SW-36681_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36680_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4210,-385 4210,-412 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2eb7da0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4210,-444 4210,-465 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="5820@1" ObjectIDZND0="5775@0" Pin0InfoVect0LinkObjId="g_2ca9c80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36681_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4210,-444 4210,-465 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2eb85d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4118,-345 4118,-357 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_2bb9f20@0" ObjectIDZND0="5827@0" Pin0InfoVect0LinkObjId="SW-36736_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2bb9f20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4118,-345 4118,-357 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2eb8830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4118,-384 4118,-412 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="5827@1" ObjectIDZND0="5828@0" Pin0InfoVect0LinkObjId="SW-36737_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36736_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4118,-384 4118,-412 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2eb8a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4118,-444 4118,-465 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="5828@1" ObjectIDZND0="5775@0" Pin0InfoVect0LinkObjId="g_2ca9c80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36737_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4118,-444 4118,-465 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ebbac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4325,-400 4343,-400 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="29730@x" ObjectIDND1="29729@x" ObjectIDZND0="29732@0" Pin0InfoVect0LinkObjId="SW-195423_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-195421_0" Pin1InfoVect1LinkObjId="SW-195441_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4325,-400 4343,-400 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ebbd20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4379,-400 4396,-400 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="29732@1" ObjectIDZND0="g_2ec4fd0@0" Pin0InfoVect0LinkObjId="g_2ec4fd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-195423_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4379,-400 4396,-400 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ebe4b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4325,-413 4325,-400 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="29730@0" ObjectIDZND0="29732@x" ObjectIDZND1="29729@x" Pin0InfoVect0LinkObjId="SW-195423_0" Pin0InfoVect1LinkObjId="SW-195441_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-195421_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4325,-413 4325,-400 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ec2f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4325,-464 4325,-449 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5775@0" ObjectIDZND0="29730@1" Pin0InfoVect0LinkObjId="SW-195421_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ca9c80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4325,-464 4325,-449 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ec4d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4325,-105 4325,-149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" ObjectIDND0="34540@0" ObjectIDZND0="g_2ec4020@1" Pin0InfoVect0LinkObjId="g_2ec4020_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-CX_QF.056Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4325,-105 4325,-149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ec5a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4325,-281 4325,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="29731@1" ObjectIDZND0="29729@0" Pin0InfoVect0LinkObjId="SW-195441_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-195422_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4325,-281 4325,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ec5cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4325,-375 4325,-401 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="29729@1" ObjectIDZND0="29730@x" ObjectIDZND1="29732@x" Pin0InfoVect0LinkObjId="SW-195421_0" Pin0InfoVect1LinkObjId="SW-195423_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-195441_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4325,-375 4325,-401 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ec8110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4348,-223 4325,-223 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2ec75e0@0" ObjectIDZND0="g_2ec4020@0" ObjectIDZND1="29731@x" Pin0InfoVect0LinkObjId="g_2ec4020_0" Pin0InfoVect1LinkObjId="SW-195422_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ec75e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4348,-223 4325,-223 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ec8bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4325,-201 4325,-223 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2ec4020@0" ObjectIDZND0="g_2ec75e0@0" ObjectIDZND1="29731@x" Pin0InfoVect0LinkObjId="g_2ec75e0_0" Pin0InfoVect1LinkObjId="SW-195422_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ec4020_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4325,-201 4325,-223 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ec8e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4325,-223 4325,-245 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_2ec75e0@0" ObjectIDND1="g_2ec4020@0" ObjectIDZND0="29731@0" Pin0InfoVect0LinkObjId="SW-195422_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2ec75e0_0" Pin1InfoVect1LinkObjId="g_2ec4020_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4325,-223 4325,-245 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2eca670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4876,-465 4876,-487 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5776@0" ObjectIDZND0="5791@0" Pin0InfoVect0LinkObjId="SW-36433_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2edbec0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4876,-465 4876,-487 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2eca8d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4876,-519 4876,-535 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="5791@1" ObjectIDZND0="g_2ece080@0" ObjectIDZND1="g_2ecab30@0" Pin0InfoVect0LinkObjId="g_2ece080_0" Pin0InfoVect1LinkObjId="g_2ecab30_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36433_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4876,-519 4876,-535 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ecb4a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4876,-535 4909,-535 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="5791@x" ObjectIDND1="g_2ecab30@0" ObjectIDZND0="g_2ece080@0" Pin0InfoVect0LinkObjId="g_2ece080_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-36433_0" Pin1InfoVect1LinkObjId="g_2ecab30_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4876,-535 4909,-535 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ecb700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4876,-605 4876,-584 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_2ecbbc0@0" ObjectIDZND0="g_2ecab30@0" Pin0InfoVect0LinkObjId="g_2ecab30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ecbbc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4876,-605 4876,-584 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ecb960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4876,-551 4876,-535 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_2ecab30@1" ObjectIDZND0="5791@x" ObjectIDZND1="g_2ece080@0" Pin0InfoVect0LinkObjId="SW-36433_0" Pin0InfoVect1LinkObjId="g_2ece080_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ecab30_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4876,-551 4876,-535 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ed6540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4734,-278 4734,-292 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="5833@1" ObjectIDZND0="g_2ed57f0@1" Pin0InfoVect0LinkObjId="g_2ed57f0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36766_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4734,-278 4734,-292 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ed67a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4734,-345 4734,-357 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_2ed57f0@0" ObjectIDZND0="5831@0" Pin0InfoVect0LinkObjId="SW-36764_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ed57f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4734,-345 4734,-357 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2edbc60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4734,-384 4734,-411 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="5831@1" ObjectIDZND0="5832@0" Pin0InfoVect0LinkObjId="SW-36765_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36764_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4734,-384 4734,-411 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2edbec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4734,-443 4734,-465 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="5832@1" ObjectIDZND0="5776@0" Pin0InfoVect0LinkObjId="g_2ee8880_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36765_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4734,-443 4734,-465 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2edd530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4746,-216 4734,-216 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_2edd790@0" ObjectIDZND0="5833@x" ObjectIDZND1="34541@x" Pin0InfoVect0LinkObjId="SW-36766_0" Pin0InfoVect1LinkObjId="EC-CX_QF.059Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2edd790_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4746,-216 4734,-216 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ededd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4734,-242 4734,-216 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="5833@0" ObjectIDZND0="g_2edd790@0" ObjectIDZND1="34541@x" Pin0InfoVect0LinkObjId="g_2edd790_0" Pin0InfoVect1LinkObjId="EC-CX_QF.059Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36766_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4734,-242 4734,-216 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2edf030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4734,-216 4734,-144 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_2edd790@0" ObjectIDND1="5833@x" ObjectIDZND0="34541@0" Pin0InfoVect0LinkObjId="EC-CX_QF.059Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2edd790_0" Pin1InfoVect1LinkObjId="SW-36766_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4734,-216 4734,-144 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ee2f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4854,-279 4854,-293 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="5825@1" ObjectIDZND0="g_2ee21b0@1" Pin0InfoVect0LinkObjId="g_2ee21b0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36710_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4854,-279 4854,-293 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ee3160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4854,-346 4854,-358 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_2ee21b0@0" ObjectIDZND0="5823@0" Pin0InfoVect0LinkObjId="SW-36708_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ee21b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4854,-346 4854,-358 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ee8620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4854,-385 4854,-412 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="5823@1" ObjectIDZND0="5824@0" Pin0InfoVect0LinkObjId="SW-36709_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36708_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4854,-385 4854,-412 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ee8880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4854,-444 4854,-465 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="5824@1" ObjectIDZND0="5776@0" Pin0InfoVect0LinkObjId="g_2edbec0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36709_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4854,-444 4854,-465 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ee8ae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4866,-217 4854,-217 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_2ee8d40@0" ObjectIDZND0="5825@x" ObjectIDZND1="34542@x" Pin0InfoVect0LinkObjId="SW-36710_0" Pin0InfoVect1LinkObjId="EC-CX_QF.061Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ee8d40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4866,-217 4854,-217 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ee9af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4854,-243 4854,-217 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="5825@0" ObjectIDZND0="g_2ee8d40@0" ObjectIDZND1="34542@x" Pin0InfoVect0LinkObjId="g_2ee8d40_0" Pin0InfoVect1LinkObjId="EC-CX_QF.061Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36710_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4854,-243 4854,-217 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ee9d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4854,-217 4854,-145 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="5825@x" ObjectIDND1="g_2ee8d40@0" ObjectIDZND0="34542@0" Pin0InfoVect0LinkObjId="EC-CX_QF.061Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-36710_0" Pin1InfoVect1LinkObjId="g_2ee8d40_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4854,-217 4854,-145 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2eef960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3968,-747 3968,-760 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="5796@0" ObjectIDZND0="5885@x" ObjectIDZND1="g_2d4cf10@0" ObjectIDZND2="g_2bb6480@0" Pin0InfoVect0LinkObjId="g_2bb6250_0" Pin0InfoVect1LinkObjId="g_2d4cf10_0" Pin0InfoVect2LinkObjId="g_2bb6480_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36442_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3968,-747 3968,-760 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2eefb50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4025,-760 3973,-760 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="5885@x" ObjectIDZND0="g_2d4cf10@0" ObjectIDZND1="g_2bb6480@0" ObjectIDZND2="5796@x" Pin0InfoVect0LinkObjId="g_2d4cf10_0" Pin0InfoVect1LinkObjId="g_2bb6480_0" Pin0InfoVect2LinkObjId="SW-36442_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2bb6250_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4025,-760 3973,-760 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2ef05a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3973,-760 3921,-760 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="5885@x" ObjectIDND1="5796@x" ObjectIDZND0="g_2d4cf10@0" ObjectIDZND1="g_2bb6480@0" Pin0InfoVect0LinkObjId="g_2d4cf10_0" Pin0InfoVect1LinkObjId="g_2bb6480_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2bb6250_0" Pin1InfoVect1LinkObjId="SW-36442_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3973,-760 3921,-760 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2ef07e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3921,-760 3886,-760 3886,-737 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2d4cf10@0" ObjectIDND1="5885@x" ObjectIDND2="5796@x" ObjectIDZND0="g_2bb6480@0" Pin0InfoVect0LinkObjId="g_2bb6480_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2d4cf10_0" Pin1InfoVect1LinkObjId="g_2bb6250_0" Pin1InfoVect2LinkObjId="SW-36442_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3921,-760 3886,-760 3886,-737 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2ef1280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4741,-774 4741,-762 4767,-762 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="transformer" ObjectIDND0="g_2c81ac0@0" ObjectIDZND0="g_2bb6ff0@0" ObjectIDZND1="5844@x" ObjectIDZND2="5886@x" Pin0InfoVect0LinkObjId="g_2bb6ff0_0" Pin0InfoVect1LinkObjId="SW-36987_0" Pin0InfoVect2LinkObjId="g_2d5b730_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c81ac0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4741,-774 4741,-762 4767,-762 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2ef14e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4767,-762 4767,-774 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="transformer" EndDevType0="lightningRod" ObjectIDND0="g_2c81ac0@0" ObjectIDND1="5844@x" ObjectIDND2="5886@x" ObjectIDZND0="g_2bb6ff0@0" Pin0InfoVect0LinkObjId="g_2bb6ff0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2c81ac0_0" Pin1InfoVect1LinkObjId="SW-36987_0" Pin1InfoVect2LinkObjId="g_2d5b730_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4767,-762 4767,-774 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2ef1740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4703,-775 4703,-762 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="transformer" ObjectIDND0="5844@0" ObjectIDZND0="g_2c81ac0@0" ObjectIDZND1="g_2bb6ff0@0" ObjectIDZND2="5886@x" Pin0InfoVect0LinkObjId="g_2c81ac0_0" Pin0InfoVect1LinkObjId="g_2bb6ff0_0" Pin0InfoVect2LinkObjId="g_2d5b730_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36987_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4703,-775 4703,-762 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2f1e600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4703,-762 4767,-762 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="5844@x" ObjectIDND1="5886@x" ObjectIDZND0="g_2c81ac0@0" ObjectIDZND1="g_2bb6ff0@0" Pin0InfoVect0LinkObjId="g_2c81ac0_0" Pin0InfoVect1LinkObjId="g_2bb6ff0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-36987_0" Pin1InfoVect1LinkObjId="g_2d5b730_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4703,-762 4767,-762 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2f1e860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4574,-762 4703,-762 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="5886@x" ObjectIDZND0="5844@x" ObjectIDZND1="g_2c81ac0@0" ObjectIDZND2="g_2bb6ff0@0" Pin0InfoVect0LinkObjId="SW-36987_0" Pin0InfoVect1LinkObjId="g_2c81ac0_0" Pin0InfoVect2LinkObjId="g_2bb6ff0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d5b730_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4574,-762 4703,-762 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-1199"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3144" y="-1108"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-599"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-18" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3420.000000 -1086.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18" ObjectName="DYN-CX_QF"/>
     <cge:Meas_Ref ObjectId="18"/>
    </metadata>
   </g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-CX_QF.054Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4109.000000 -78.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34538" ObjectName="EC-CX_QF.054Ld"/>
    <cge:TPSR_Ref TObjectID="34538"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_QF.CX_QF_3566LD">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5300.000000 -312.000000)" xlink:href="#load:shape17"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22109" ObjectName="EC-CX_QF.CX_QF_3566LD"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_QF.059Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4725.000000 -117.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34541" ObjectName="EC-CX_QF.059Ld"/>
    <cge:TPSR_Ref TObjectID="34541"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_QF.056Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4316.000000 -78.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34540" ObjectName="EC-CX_QF.056Ld"/>
    <cge:TPSR_Ref TObjectID="34540"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_QF.055Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4201.000000 -79.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34539" ObjectName="EC-CX_QF.055Ld"/>
    <cge:TPSR_Ref TObjectID="34539"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_QF.061Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4845.000000 -118.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34542" ObjectName="EC-CX_QF.061Ld"/>
    <cge:TPSR_Ref TObjectID="34542"/></metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_QF"/>
</svg>