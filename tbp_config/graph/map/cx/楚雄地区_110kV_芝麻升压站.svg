<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-257" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="-709 -1806 1954 1174">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape8_0">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="91" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="82" y2="91"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="91" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="15" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="breaker2:shape8_1">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="7" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="99" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
   </symbol>
   <symbol id="breaker2:shape8-UnNor1">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="15" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="91" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="82" y2="91"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="91" y2="82"/>
   </symbol>
   <symbol id="breaker2:shape8-UnNor2">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="7" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="98" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="earth:shape6">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="1" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="2" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="9" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="5" x2="5" y1="6" y2="26"/>
   </symbol>
   <symbol id="earth:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="14" y2="14"/>
   </symbol>
   <symbol id="generator:shape4">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <rect height="14" stroke-width="1" width="27" x="18" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="5" x2="5" y1="11" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="9" x2="18" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="8" x2="8" y1="2" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="58" x2="23" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="63" x2="63" y1="6" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="61" x2="61" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="58" x2="58" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="58" x2="49" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="14" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="5" x2="8" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="3" x2="10" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="12" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
   </symbol>
   <symbol id="lightningRod:shape188">
    <polyline DF8003:Layer="PUBLIC" points="19,18 10,0 1,19 19,18 " stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape177">
    <rect height="19" stroke-width="1" width="35" x="0" y="0"/>
    <text font-family="SimSun" font-size="15" graphid="g_2aba960" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 17.000000) translate(0,12)">SVG</text>
    <polyline points="17,19 17,30 " stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape174">
    <rect height="18" stroke-width="1.1697" width="11" x="1" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.1208" x1="7" x2="7" y1="14" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.1208" x1="7" x2="7" y1="39" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.447552" x1="7" x2="7" y1="7" y2="14"/>
   </symbol>
   <symbol id="lightningRod:shape192">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="26" y1="9" y2="9"/>
    <polyline DF8003:Layer="PUBLIC" points="5,19 17,9 5,0 5,19 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="38,1 26,10 38,19 38,1 " stroke-width="1"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape8_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="18" y2="43"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="18" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
   </symbol>
   <symbol id="switch2:shape8_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="44" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
   </symbol>
   <symbol id="switch2:shape8-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="18" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="18" y2="43"/>
   </symbol>
   <symbol id="switch2:shape8-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="44" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="transformer2:shape14_0">
    <circle cx="37" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="66" x2="71" y1="84" y2="84"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="70" x2="68" y1="84" y2="79"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="71" x2="71" y1="81" y2="81"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.911765" x1="1" x2="69" y1="45" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="37" x2="28" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="37" x2="45" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="28" x2="45" y1="18" y2="18"/>
   </symbol>
   <symbol id="transformer2:shape14_1">
    <ellipse cx="37" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="29" x2="37" y1="75" y2="67"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="37" x2="45" y1="67" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="37" x2="37" y1="59" y2="67"/>
   </symbol>
   <symbol id="voltageTransformer:shape71">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="53" x2="53" y1="12" y2="12"/>
    <ellipse cx="39" cy="19" fillStyle="0" rx="15" ry="12" stroke-width="0.862369"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.36031" x1="30" x2="34" y1="55" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.39762" x1="25" x2="21" y1="55" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.77579" x1="21" x2="35" y1="62" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="29" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="33" x2="41" y1="16" y2="20"/>
    <ellipse cx="16" cy="20" fillStyle="0" rx="14.5" ry="12" stroke-width="0.862369"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45294" x1="40" x2="41" y1="20" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="47" x2="41" y1="16" y2="20"/>
    <ellipse cx="28" cy="56" fillStyle="0" rx="14.5" ry="12" stroke-width="0.862369"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="53" x2="53" y1="32" y2="32"/>
    <ellipse cx="40" cy="39" fillStyle="0" rx="15" ry="12" stroke-width="0.862369"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="29" y1="32" y2="32"/>
    <ellipse cx="15" cy="40" fillStyle="0" rx="14.5" ry="12" stroke-width="0.862369"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45294" x1="15" x2="16" y1="20" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="22" x2="16" y1="16" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="9" x2="16" y1="16" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="33" x2="41" y1="36" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45294" x1="40" x2="41" y1="40" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="47" x2="41" y1="36" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45294" x1="15" x2="16" y1="40" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="21" x2="15" y1="36" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="9" x2="16" y1="36" y2="40"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_23be810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23eb170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_23ebb10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_236fe60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2370eb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2371990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23723b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2439880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_236e7b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_236e7b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_243ca20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_243ca20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_24360c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_24360c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_24370e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2438d50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_250ff40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2510cf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2511440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_24171b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_25132e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2513ba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_24125d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2413390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2413d10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2414800" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_24151c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_24783b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2415c00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2440690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_24412b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2689790" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2442080" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_23904c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2391aa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(0,0,0)" height="1184" width="1964" x="-714" y="-1811"/>
  </g><g id="CircleFilled_Layer">
   <circle DF8003:Layer="PUBLIC" cx="782" cy="-1691" fill="none" fillStyle="0" r="15" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="783" cy="-1717" fill="none" fillStyle="0" r="15" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="999" cy="-1695" fill="none" fillStyle="0" r="15" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="999" cy="-1717" fill="none" fillStyle="0" r="15" stroke="rgb(60,120,255)" stroke-width="1"/>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3551a90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 379.000000 1234.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a78500" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 368.000000 1219.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33215c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 393.000000 1204.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_346d2f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 379.000000 1564.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a12b30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 368.000000 1549.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34d2d70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 393.000000 1534.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a6ccc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -358.000000 1106.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a6ced0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -357.000000 1078.666667) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4078180" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -358.000000 1092.333333) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4078370" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -366.000000 1065.000000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4078d70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 43.000000 1076.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35e36f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 32.000000 1061.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35e3900" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 57.000000 1046.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33ee4f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1158.000000 1075.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35e3eb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1147.000000 1060.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29b3fa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1172.000000 1045.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29b4550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 845.000000 1056.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29b4780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 820.000000 1071.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
  </g><g id="MultiLine_Layer">
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="353,-1677 358,-1670 355,-1671 " stroke="rgb(255,0,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="358,-1670 358,-1673 " stroke="rgb(255,0,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1074,-753 1107,-753 1107,-725 1074,-711 " stroke="rgb(255,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="784,-1709 781,-1709 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="788,-1718 790,-1716 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="783,-1693 806,-1693 806,-1672 784,-1654 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="784,-1395 763,-1371 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="811,-1395 831,-1369 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="784,-1576 731,-1530 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="973,-1395 952,-1371 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1000,-1395 1020,-1369 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1000,-1576 1051,-1531 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1115,-1473 1166,-1428 " stroke="rgb(60,120,255)" stroke-width="1"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="13" stroke="rgb(60,120,255)" stroke-width="1" width="24" x="806" y="-1454"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="13" stroke="rgb(60,120,255)" stroke-width="1" width="24" x="845" y="-1453"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="26" stroke="rgb(170,85,127)" stroke-width="1" width="12" x="269" y="-1355"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="11" stroke="rgb(60,120,255)" stroke-width="1" width="6" x="-92" y="-1332"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="20" stroke="rgb(60,120,255)" stroke-width="1" width="13" x="777" y="-1372"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="20" stroke="rgb(60,120,255)" stroke-width="1" width="13" x="804" y="-1372"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="20" stroke="rgb(60,120,255)" stroke-width="1" width="13" x="966" y="-1372"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="20" stroke="rgb(60,120,255)" stroke-width="1" width="13" x="993" y="-1372"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="13" stroke="rgb(60,120,255)" stroke-width="1" width="24" x="954" y="-1439"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="13" stroke="rgb(60,120,255)" stroke-width="1" width="24" x="915" y="-1438"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-196303">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 209.000000 -1338.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29895" ObjectName="SW-CX_ZM.CX_ZM_1010SW"/>
     <cge:Meas_Ref ObjectId="196303"/>
    <cge:TPSR_Ref TObjectID="29895"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196298">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 275.000000 -1433.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29890" ObjectName="SW-CX_ZM.CX_ZM_13117SW"/>
     <cge:Meas_Ref ObjectId="196298"/>
    <cge:TPSR_Ref TObjectID="29890"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196299">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 275.000000 -1576.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29891" ObjectName="SW-CX_ZM.CX_ZM_13160SW"/>
     <cge:Meas_Ref ObjectId="196299"/>
    <cge:TPSR_Ref TObjectID="29891"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196297">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 320.000000 -1604.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29889" ObjectName="SW-CX_ZM.CX_ZM_1316SW"/>
     <cge:Meas_Ref ObjectId="196297"/>
    <cge:TPSR_Ref TObjectID="29889"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196300">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 338.000000 -1660.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29892" ObjectName="SW-CX_ZM.CX_ZM_K1317SW"/>
     <cge:Meas_Ref ObjectId="196300"/>
    <cge:TPSR_Ref TObjectID="29892"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196296">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 320.000000 -1453.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29888" ObjectName="SW-CX_ZM.CX_ZM_1311SW"/>
     <cge:Meas_Ref ObjectId="196296"/>
    <cge:TPSR_Ref TObjectID="29888"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196301">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(0.750000 -0.000000 0.000000 -0.928571 279.000000 -1693.714286)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29893" ObjectName="SW-CX_ZM.CX_ZM_1901SW"/>
     <cge:Meas_Ref ObjectId="196301"/>
    <cge:TPSR_Ref TObjectID="29893"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196302">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(0.750000 -0.000000 0.000000 -0.928571 252.000000 -1651.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29894" ObjectName="SW-CX_ZM.CX_ZM_19017SW"/>
     <cge:Meas_Ref ObjectId="196302"/>
    <cge:TPSR_Ref TObjectID="29894"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196314">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.500000 -0.000000 0.000000 -1.103988 -68.000000 -904.955479)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29910" ObjectName="SW-CX_ZM.CX_ZM_33367SW"/>
     <cge:Meas_Ref ObjectId="196314"/>
    <cge:TPSR_Ref TObjectID="29910"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196311">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.500000 -0.000000 0.000000 -1.103988 196.000000 -904.955479)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29906" ObjectName="SW-CX_ZM.CX_ZM_33267SW"/>
     <cge:Meas_Ref ObjectId="196311"/>
    <cge:TPSR_Ref TObjectID="29906"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196305">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 317.000000 -1230.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29897" ObjectName="SW-CX_ZM.CX_ZM_301XC"/>
     <cge:Meas_Ref ObjectId="196305"/>
    <cge:TPSR_Ref TObjectID="29897"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196305">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 317.000000 -1146.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29898" ObjectName="SW-CX_ZM.CX_ZM_301XC1"/>
     <cge:Meas_Ref ObjectId="196305"/>
    <cge:TPSR_Ref TObjectID="29898"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196321">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -68.000000 -1181.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29919" ObjectName="SW-CX_ZM.CX_ZM_3901XC"/>
     <cge:Meas_Ref ObjectId="196321"/>
    <cge:TPSR_Ref TObjectID="29919"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196321">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -68.000000 -1139.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29920" ObjectName="SW-CX_ZM.CX_ZM_3901XC1"/>
     <cge:Meas_Ref ObjectId="196321"/>
    <cge:TPSR_Ref TObjectID="29920"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196313">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -25.000000 -1076.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29908" ObjectName="SW-CX_ZM.CX_ZM_333XC"/>
     <cge:Meas_Ref ObjectId="196313"/>
    <cge:TPSR_Ref TObjectID="29908"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196313">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -25.000000 -991.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29909" ObjectName="SW-CX_ZM.CX_ZM_333XC1"/>
     <cge:Meas_Ref ObjectId="196313"/>
    <cge:TPSR_Ref TObjectID="29909"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196310">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 238.000000 -1081.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29904" ObjectName="SW-CX_ZM.CX_ZM_332XC"/>
     <cge:Meas_Ref ObjectId="196310"/>
    <cge:TPSR_Ref TObjectID="29904"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196307">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 508.000000 -1079.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29900" ObjectName="SW-CX_ZM.CX_ZM_331XC"/>
     <cge:Meas_Ref ObjectId="196307"/>
    <cge:TPSR_Ref TObjectID="29900"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196316">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 757.000000 -1081.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29912" ObjectName="SW-CX_ZM.CX_ZM_334XC"/>
     <cge:Meas_Ref ObjectId="196316"/>
    <cge:TPSR_Ref TObjectID="29912"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196319">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1064.000000 -1081.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29916" ObjectName="SW-CX_ZM.CX_ZM_335XC"/>
     <cge:Meas_Ref ObjectId="196319"/>
    <cge:TPSR_Ref TObjectID="29916"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196310">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 238.000000 -996.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29905" ObjectName="SW-CX_ZM.CX_ZM_332XC1"/>
     <cge:Meas_Ref ObjectId="196310"/>
    <cge:TPSR_Ref TObjectID="29905"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196307">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 508.000000 -992.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29901" ObjectName="SW-CX_ZM.CX_ZM_331XC1"/>
     <cge:Meas_Ref ObjectId="196307"/>
    <cge:TPSR_Ref TObjectID="29901"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196316">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 757.000000 -996.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29913" ObjectName="SW-CX_ZM.CX_ZM_334XC1"/>
     <cge:Meas_Ref ObjectId="196316"/>
    <cge:TPSR_Ref TObjectID="29913"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196319">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1064.000000 -996.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29917" ObjectName="SW-CX_ZM.CX_ZM_335XC1"/>
     <cge:Meas_Ref ObjectId="196319"/>
    <cge:TPSR_Ref TObjectID="29917"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196308">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.500000 -0.000000 0.000000 -1.103988 463.000000 -904.955479)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29902" ObjectName="SW-CX_ZM.CX_ZM_33167SW"/>
     <cge:Meas_Ref ObjectId="196308"/>
    <cge:TPSR_Ref TObjectID="29902"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196317">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.500000 -0.000000 0.000000 -1.103988 715.000000 -902.955479)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29914" ObjectName="SW-CX_ZM.CX_ZM_33467SW"/>
     <cge:Meas_Ref ObjectId="196317"/>
    <cge:TPSR_Ref TObjectID="29914"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196320">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.500000 -0.000000 0.000000 -1.103988 1022.037037 -903.955479)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29918" ObjectName="SW-CX_ZM.CX_ZM_33567SW"/>
     <cge:Meas_Ref ObjectId="196320"/>
    <cge:TPSR_Ref TObjectID="29918"/></metadata>
   </g>
  </g><g id="Generator_Layer">
   <g DF8003:Layer="PUBLIC" id="SM-CX_ZM.P3">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -20.000000 -784.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43349" ObjectName="SM-CX_ZM.P3"/>
    <cge:TPSR_Ref TObjectID="43349"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_ZM.P2">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 243.000000 -786.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43348" ObjectName="SM-CX_ZM.P2"/>
    <cge:TPSR_Ref TObjectID="43348"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_ZM.P1">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 513.000000 -788.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43347" ObjectName="SM-CX_ZM.P1"/>
    <cge:TPSR_Ref TObjectID="43347"/></metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_FS" endPointId="0" endStationName="CX_ZM" flowDrawDirect="1" flowShape="0" id="AC-110kV.fangzhi_line" runFlow="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="329,-1726 329,-1763 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="31885" ObjectName="AC-110kV.fangzhi_line"/>
    <cge:TPSR_Ref TObjectID="31885_SS-257"/></metadata>
   <polyline fill="none" opacity="0" points="329,-1726 329,-1763 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_3654e00" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 392.000000 -1659.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3623230" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 241.000000 -1575.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3e72430" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 241.000000 -1432.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3455410" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1031.000000 -682.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3ceb850" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 817.000000 -1644.000000)" xlink:href="#earth:shape6"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4134ea0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1027.000000 -1334.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ab1aa0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1143.000000 -1332.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-0KV" id="g_22e7030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="784,-1473 784,-1395 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" ObjectIDND0="0@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3654e00_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="784,-1473 784,-1395 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2ab4540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="784,-1630 784,-1676 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_3ef2ee0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3ef2ee0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="784,-1630 784,-1676 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26825d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="327,-1291 348,-1291 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="38856@x" ObjectIDND1="29897@x" ObjectIDZND0="g_2a050d0@0" Pin0InfoVect0LinkObjId="g_2a050d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_4466250_0" Pin1InfoVect1LinkObjId="SW-196305_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="327,-1291 348,-1291 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_4347930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="275,-1403 218,-1403 218,-1379 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="38856@x" ObjectIDZND0="29895@1" Pin0InfoVect0LinkObjId="SW-196303_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4466250_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="275,-1403 218,-1403 218,-1379 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2acf2a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="233,-1324 218,-1324 218,-1343 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="29895@0" Pin0InfoVect0LinkObjId="SW-196303_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="233,-1324 218,-1324 218,-1343 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2af8c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="275,-1369 275,-1403 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" EndDevType1="transformer2" ObjectIDZND0="29895@x" ObjectIDZND1="38856@x" Pin0InfoVect0LinkObjId="SW-196303_0" Pin0InfoVect1LinkObjId="g_4466250_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="275,-1369 275,-1403 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_4466250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="275,-1403 330,-1403 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="29895@x" ObjectIDZND0="38856@x" Pin0InfoVect0LinkObjId="g_34f36b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196303_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="275,-1403 330,-1403 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_41238a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="280,-1438 259,-1438 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="29890@0" ObjectIDZND0="g_3e72430@0" Pin0InfoVect0LinkObjId="g_3e72430_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196298_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="280,-1438 259,-1438 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3fd0c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="329,-1438 316,-1438 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="38856@x" ObjectIDND1="29888@x" ObjectIDZND0="29890@1" Pin0InfoVect0LinkObjId="SW-196298_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_4466250_0" Pin1InfoVect1LinkObjId="SW-196296_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="329,-1438 316,-1438 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3cc59b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="329,-1422 329,-1438 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="38856@1" ObjectIDZND0="29890@x" ObjectIDZND1="29888@x" Pin0InfoVect0LinkObjId="SW-196298_0" Pin0InfoVect1LinkObjId="SW-196296_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4466250_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="329,-1422 329,-1438 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2a806d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="280,-1581 259,-1581 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="29891@0" ObjectIDZND0="g_3623230@0" Pin0InfoVect0LinkObjId="g_3623230_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196299_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="280,-1581 259,-1581 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3551440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="329,-1581 316,-1581 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="29889@x" ObjectIDND1="29887@x" ObjectIDZND0="29891@1" Pin0InfoVect0LinkObjId="SW-196299_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-196297_0" Pin1InfoVect1LinkObjId="SW-196295_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="329,-1581 316,-1581 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_4463740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="329,-1438 329,-1458 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="38856@x" ObjectIDND1="29890@x" ObjectIDZND0="29888@0" Pin0InfoVect0LinkObjId="SW-196296_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_4466250_0" Pin1InfoVect1LinkObjId="SW-196298_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="329,-1438 329,-1458 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_32595a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="329,-1581 329,-1609 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="29891@x" ObjectIDND1="29887@x" ObjectIDZND0="29889@0" Pin0InfoVect0LinkObjId="SW-196297_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-196299_0" Pin1InfoVect1LinkObjId="SW-196295_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="329,-1581 329,-1609 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_33c8890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="329,-1555 329,-1581 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="29887@1" ObjectIDZND0="29889@x" ObjectIDZND1="29891@x" Pin0InfoVect0LinkObjId="SW-196297_0" Pin0InfoVect1LinkObjId="SW-196299_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196295_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="329,-1555 329,-1581 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3cabda0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="329,-1645 329,-1665 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="29889@1" ObjectIDZND0="29892@x" ObjectIDZND1="29893@x" ObjectIDZND2="g_35796a0@0" Pin0InfoVect0LinkObjId="SW-196300_0" Pin0InfoVect1LinkObjId="SW-196301_0" Pin0InfoVect2LinkObjId="g_35796a0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196297_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="329,-1645 329,-1665 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_352bdf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="329,-1665 343,-1665 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="29889@x" ObjectIDND1="29893@x" ObjectIDND2="g_35796a0@0" ObjectIDZND0="29892@0" Pin0InfoVect0LinkObjId="SW-196300_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-196297_0" Pin1InfoVect1LinkObjId="SW-196301_0" Pin1InfoVect2LinkObjId="g_35796a0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="329,-1665 343,-1665 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_40c5730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="379,-1665 396,-1665 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="29892@1" ObjectIDZND0="g_3654e00@0" Pin0InfoVect0LinkObjId="g_3654e00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196300_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="379,-1665 396,-1665 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2b3c0e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="329,-1698 310,-1698 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="29892@x" ObjectIDND1="29889@x" ObjectIDND2="g_35796a0@0" ObjectIDZND0="29893@1" Pin0InfoVect0LinkObjId="SW-196301_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-196300_0" Pin1InfoVect1LinkObjId="SW-196297_0" Pin1InfoVect2LinkObjId="g_35796a0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="329,-1698 310,-1698 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_412d630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="329,-1665 329,-1698 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="29892@x" ObjectIDND1="29889@x" ObjectIDZND0="29893@x" ObjectIDZND1="g_35796a0@0" ObjectIDZND2="31885@1" Pin0InfoVect0LinkObjId="SW-196301_0" Pin0InfoVect1LinkObjId="g_35796a0_0" Pin0InfoVect2LinkObjId="g_3ebce90_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-196300_0" Pin1InfoVect1LinkObjId="SW-196297_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="329,-1665 329,-1698 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_365df10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="283,-1698 240,-1698 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="29893@0" ObjectIDZND0="g_3defed0@0" Pin0InfoVect0LinkObjId="g_3defed0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196301_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="283,-1698 240,-1698 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_238c330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="329,-1510 329,-1528 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="48133@0" ObjectIDZND0="29887@0" Pin0InfoVect0LinkObjId="SW-196295_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4252890_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="329,-1510 329,-1528 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_358c7d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="250,-1324 250,-1317 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="250,-1324 250,-1317 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34f36b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="327,-1291 327,-1341 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="transformer2" ObjectIDND0="g_2a050d0@0" ObjectIDND1="29897@x" ObjectIDZND0="38856@0" Pin0InfoVect0LinkObjId="g_4466250_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2a050d0_0" Pin1InfoVect1LinkObjId="SW-196305_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="327,-1291 327,-1341 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3611170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-58,-1280 -58,-1303 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_22e6c90@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22e6c90_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="-58,-1280 -58,-1303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_353b1c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-97,-1229 -59,-1229 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_3610940@0" ObjectIDZND0="g_22e6c90@0" ObjectIDZND1="29919@x" Pin0InfoVect0LinkObjId="g_22e6c90_0" Pin0InfoVect1LinkObjId="SW-196321_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3610940_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-97,-1229 -59,-1229 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22edb60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-58,-1249 -58,-1229 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_22e6c90@0" ObjectIDZND0="g_3610940@0" ObjectIDZND1="29919@x" Pin0InfoVect0LinkObjId="g_3610940_0" Pin0InfoVect1LinkObjId="SW-196321_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22e6c90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-58,-1249 -58,-1229 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_34fc250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-58,-1310 -89,-1310 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="-58,-1310 -89,-1310 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3db8b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1074,-739 1074,-671 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1074,-739 1074,-671 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3e72680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1037,-700 1037,-721 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="lightningRod" ObjectIDND0="g_3455410@0" ObjectIDZND0="g_2a786a0@1" Pin0InfoVect0LinkObjId="g_2a786a0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3455410_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1037,-700 1037,-721 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3fe9eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1000,-1473 1000,-1395 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" ObjectIDND0="0@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3654e00_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1000,-1473 1000,-1395 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_22f8f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1000,-1630 1000,-1680 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_3dba800@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3dba800_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1000,-1630 1000,-1680 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3e71d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="798,-1312 798,-1258 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="busSection" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_3654e00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="798,-1312 798,-1258 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_43aba00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="987,-1312 987,-1259 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="busSection" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_3654e00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="987,-1312 987,-1259 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_41bdc40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1033,-1339 1033,-1323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="busSection" ObjectIDND0="g_4134ea0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_3654e00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4134ea0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1033,-1339 1033,-1323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_40f6d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1115,-1370 1115,-1332 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" ObjectIDND0="0@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3654e00_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1115,-1370 1115,-1332 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2622c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1115,-1527 1115,-1577 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_4105cb0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4105cb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1115,-1527 1115,-1577 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_411c510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1115,-1313 1115,-1259 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="busSection" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_3654e00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1115,-1313 1115,-1259 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a664a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1149,-1337 1149,-1323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="busSection" ObjectIDND0="g_2ab1aa0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_3654e00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ab1aa0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1149,-1337 1149,-1323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3dd7560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="327,-1218 327,-1237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29896@1" ObjectIDZND0="29897@1" Pin0InfoVect0LinkObjId="SW-196305_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196304_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="327,-1218 327,-1237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3dd7790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="327,-1254 327,-1291 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="transformer2" ObjectIDND0="29897@0" ObjectIDZND0="g_2a050d0@0" ObjectIDZND1="38856@x" Pin0InfoVect0LinkObjId="g_2a050d0_0" Pin0InfoVect1LinkObjId="g_4466250_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196305_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="327,-1254 327,-1291 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3df57f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="327,-1116 327,-1153 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="29885@0" ObjectIDZND0="29898@0" Pin0InfoVect0LinkObjId="SW-196305_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="327,-1116 327,-1153 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3df5a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="327,-1170 327,-1191 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="29898@1" ObjectIDZND0="29896@0" Pin0InfoVect0LinkObjId="SW-196304_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196305_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="327,-1170 327,-1191 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_365d950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-58,-1205 -58,-1229 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="29919@0" ObjectIDZND0="g_3610940@0" ObjectIDZND1="g_22e6c90@0" Pin0InfoVect0LinkObjId="g_3610940_0" Pin0InfoVect1LinkObjId="g_22e6c90_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196321_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-58,-1205 -58,-1229 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3631940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-58,-1118 -58,-1146 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="29885@0" ObjectIDZND0="29920@0" Pin0InfoVect0LinkObjId="SW-196321_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-58,-1118 -58,-1146 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3631b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-58,-1163 -58,-1188 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="29920@1" ObjectIDZND0="29919@1" Pin0InfoVect0LinkObjId="SW-196321_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196321_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-58,-1163 -58,-1188 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cf2800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-15,-1116 -15,-1100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="29885@0" ObjectIDZND0="29908@0" Pin0InfoVect0LinkObjId="SW-196313_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-15,-1116 -15,-1100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25fff90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-15,-998 -15,-805 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="generator" ObjectIDND0="29909@0" ObjectIDZND0="43349@0" Pin0InfoVect0LinkObjId="SM-CX_ZM.P3_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196313_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-15,-998 -15,-805 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3578400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-15,-1083 -15,-1067 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="29908@1" ObjectIDZND0="29907@1" Pin0InfoVect0LinkObjId="SW-196312_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196313_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-15,-1083 -15,-1067 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3442f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-15,-1040 -15,-1015 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29907@0" ObjectIDZND0="29909@1" Pin0InfoVect0LinkObjId="SW-196313_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196312_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-15,-1040 -15,-1015 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3504470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="248,-1117 248,-1105 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="29885@0" ObjectIDZND0="29904@0" Pin0InfoVect0LinkObjId="SW-196310_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="248,-1117 248,-1105 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35046f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="248,-1088 248,-1069 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="29904@1" ObjectIDZND0="29903@1" Pin0InfoVect0LinkObjId="SW-196309_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196310_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="248,-1088 248,-1069 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ec1310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="518,-1117 518,-1103 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="29885@0" ObjectIDZND0="29900@0" Pin0InfoVect0LinkObjId="SW-196307_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="518,-1117 518,-1103 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ec1570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="518,-1086 518,-1069 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="29900@1" ObjectIDZND0="29899@1" Pin0InfoVect0LinkObjId="SW-196306_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196307_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="518,-1086 518,-1069 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4079da0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1074,-1116 1074,-1104 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="29885@0" ObjectIDZND0="29916@0" Pin0InfoVect0LinkObjId="SW-196319_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1074,-1116 1074,-1104 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e862c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="248,-1042 248,-1020 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29903@0" ObjectIDZND0="29905@1" Pin0InfoVect0LinkObjId="SW-196310_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196309_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="248,-1042 248,-1020 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e86520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="248,-1003 248,-807 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="generator" ObjectIDND0="29905@0" ObjectIDZND0="43348@0" Pin0InfoVect0LinkObjId="SM-CX_ZM.P2_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196310_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="248,-1003 248,-807 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3632c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="518,-1043 518,-1016 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29899@0" ObjectIDZND0="29901@1" Pin0InfoVect0LinkObjId="SW-196307_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196306_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="518,-1043 518,-1016 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e208e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="518,-999 518,-809 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="generator" ObjectIDND0="29901@0" ObjectIDZND0="43347@0" Pin0InfoVect0LinkObjId="SM-CX_ZM.P1_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196307_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="518,-999 518,-809 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_43626c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1074,-1069 1074,-1089 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29915@1" ObjectIDZND0="29916@1" Pin0InfoVect0LinkObjId="SW-196319_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196318_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1074,-1069 1074,-1089 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4362920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="767,-1116 767,-1105 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="29885@0" ObjectIDZND0="29912@0" Pin0InfoVect0LinkObjId="SW-196316_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="767,-1116 767,-1105 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4362b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="767,-1088 767,-1069 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="29912@1" ObjectIDZND0="29911@1" Pin0InfoVect0LinkObjId="SW-196315_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196316_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="767,-1088 767,-1069 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3dc6680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1074,-788 1074,-1003 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="29917@0" Pin0InfoVect0LinkObjId="SW-196319_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1074,-788 1074,-1003 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3dc68e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1074,-1020 1074,-1042 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="29917@1" ObjectIDZND0="29915@0" Pin0InfoVect0LinkObjId="SW-196318_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196319_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1074,-1020 1074,-1042 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3dc6b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="767,-1020 767,-1042 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="29913@1" ObjectIDZND0="29911@0" Pin0InfoVect0LinkObjId="SW-196315_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196316_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="767,-1020 767,-1042 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c975c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="26,-947 26,-968 -54,-968 -54,-961 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3fab920@0" ObjectIDZND0="29910@0" Pin0InfoVect0LinkObjId="SW-196314_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3fab920_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="26,-947 26,-968 -54,-968 -54,-961 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c977b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="290,-949 290,-970 209,-970 209,-961 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3ce9280@0" ObjectIDZND0="29906@0" Pin0InfoVect0LinkObjId="SW-196311_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3ce9280_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="290,-949 290,-970 209,-970 209,-961 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ea1400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="557,-949 557,-970 476,-970 476,-961 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3c979a0@0" ObjectIDZND0="29902@0" Pin0InfoVect0LinkObjId="SW-196308_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3c979a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="557,-949 557,-970 476,-970 476,-961 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e524a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="809,-947 809,-968 728,-968 728,-959 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_35f5f70@0" ObjectIDZND0="29914@0" Pin0InfoVect0LinkObjId="SW-196317_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_35f5f70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="809,-947 809,-968 728,-968 728,-959 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_41faa00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="767,-767 767,-1003 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_43a8eb0@0" ObjectIDZND0="29913@0" Pin0InfoVect0LinkObjId="SW-196316_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_43a8eb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="767,-767 767,-1003 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4380c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1117,-948 1117,-969 1036,-969 1036,-960 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3dda440@0" ObjectIDZND0="29918@0" Pin0InfoVect0LinkObjId="SW-196320_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3dda440_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1117,-948 1117,-969 1036,-969 1036,-960 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ebce90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="329,-1719 329,-1727 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="29892@x" ObjectIDND1="29889@x" ObjectIDND2="29893@x" ObjectIDZND0="31885@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-196300_0" Pin1InfoVect1LinkObjId="SW-196297_0" Pin1InfoVect2LinkObjId="SW-196301_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="329,-1719 329,-1727 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ebd770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="329,-1698 329,-1719 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="29892@x" ObjectIDND1="29889@x" ObjectIDND2="29893@x" ObjectIDZND0="g_35796a0@0" ObjectIDZND1="31885@1" Pin0InfoVect0LinkObjId="g_35796a0_0" Pin0InfoVect1LinkObjId="g_3ebce90_1" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-196300_0" Pin1InfoVect1LinkObjId="SW-196297_0" Pin1InfoVect2LinkObjId="SW-196301_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="329,-1698 329,-1719 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ebd960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="329,-1719 348,-1719 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="29892@x" ObjectIDND1="29889@x" ObjectIDND2="29893@x" ObjectIDZND0="g_35796a0@0" Pin0InfoVect0LinkObjId="g_35796a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-196300_0" Pin1InfoVect1LinkObjId="SW-196297_0" Pin1InfoVect2LinkObjId="SW-196301_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="329,-1719 348,-1719 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_4252890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="329,-1494 329,-1510 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29888@1" ObjectIDZND0="48133@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196296_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="329,-1494 329,-1510 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectPoint_Layer"/><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-196244" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -301.000000 -1108.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196244" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29885"/>
     <cge:Term_Ref ObjectID="41545"/>
    <cge:TPSR_Ref TObjectID="29885"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-196245" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -301.000000 -1108.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196245" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29885"/>
     <cge:Term_Ref ObjectID="41545"/>
    <cge:TPSR_Ref TObjectID="29885"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-196246" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -301.000000 -1108.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196246" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29885"/>
     <cge:Term_Ref ObjectID="41545"/>
    <cge:TPSR_Ref TObjectID="29885"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-196247" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -301.000000 -1108.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196247" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29885"/>
     <cge:Term_Ref ObjectID="41545"/>
    <cge:TPSR_Ref TObjectID="29885"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-196235" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 434.000000 -1563.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196235" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29887"/>
     <cge:Term_Ref ObjectID="41548"/>
    <cge:TPSR_Ref TObjectID="29887"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-196236" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 434.000000 -1563.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196236" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29887"/>
     <cge:Term_Ref ObjectID="41548"/>
    <cge:TPSR_Ref TObjectID="29887"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-196234" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 434.000000 -1563.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196234" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29887"/>
     <cge:Term_Ref ObjectID="41548"/>
    <cge:TPSR_Ref TObjectID="29887"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-196240" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 435.000000 -1232.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196240" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29896"/>
     <cge:Term_Ref ObjectID="41566"/>
    <cge:TPSR_Ref TObjectID="29896"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-196241" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 435.000000 -1232.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196241" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29896"/>
     <cge:Term_Ref ObjectID="41566"/>
    <cge:TPSR_Ref TObjectID="29896"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-196239" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 435.000000 -1232.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196239" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29896"/>
     <cge:Term_Ref ObjectID="41566"/>
    <cge:TPSR_Ref TObjectID="29896"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-196259" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 98.000000 -1076.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196259" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29907"/>
     <cge:Term_Ref ObjectID="42254"/>
    <cge:TPSR_Ref TObjectID="29907"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-196260" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 98.000000 -1076.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196260" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29907"/>
     <cge:Term_Ref ObjectID="42254"/>
    <cge:TPSR_Ref TObjectID="29907"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-196261" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 98.000000 -1076.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196261" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29907"/>
     <cge:Term_Ref ObjectID="42254"/>
    <cge:TPSR_Ref TObjectID="29907"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-196255" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 300.000000 -1076.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196255" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29903"/>
     <cge:Term_Ref ObjectID="42246"/>
    <cge:TPSR_Ref TObjectID="29903"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-196256" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 300.000000 -1076.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196256" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29903"/>
     <cge:Term_Ref ObjectID="42246"/>
    <cge:TPSR_Ref TObjectID="29903"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-196257" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 300.000000 -1076.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196257" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29903"/>
     <cge:Term_Ref ObjectID="42246"/>
    <cge:TPSR_Ref TObjectID="29903"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-196251" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 558.000000 -1076.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196251" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29899"/>
     <cge:Term_Ref ObjectID="42238"/>
    <cge:TPSR_Ref TObjectID="29899"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-196252" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 558.000000 -1076.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196252" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29899"/>
     <cge:Term_Ref ObjectID="42238"/>
    <cge:TPSR_Ref TObjectID="29899"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-196253" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 558.000000 -1076.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196253" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29899"/>
     <cge:Term_Ref ObjectID="42238"/>
    <cge:TPSR_Ref TObjectID="29899"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-196267" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 884.000000 -1071.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196267" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29911"/>
     <cge:Term_Ref ObjectID="42262"/>
    <cge:TPSR_Ref TObjectID="29911"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-196268" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 884.000000 -1071.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196268" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29911"/>
     <cge:Term_Ref ObjectID="42262"/>
    <cge:TPSR_Ref TObjectID="29911"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-196263" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1214.000000 -1075.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196263" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29915"/>
     <cge:Term_Ref ObjectID="42270"/>
    <cge:TPSR_Ref TObjectID="29915"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-196264" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1214.000000 -1075.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196264" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29915"/>
     <cge:Term_Ref ObjectID="42270"/>
    <cge:TPSR_Ref TObjectID="29915"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-196265" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1214.000000 -1075.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196265" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29915"/>
     <cge:Term_Ref ObjectID="42270"/>
    <cge:TPSR_Ref TObjectID="29915"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="155" x="-606" y="-1702"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="155" x="-606" y="-1702"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-655" y="-1719"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-655" y="-1719"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <polygon fill="rgb(255,255,255)" points="-452,-1562 -455,-1565 -455,-1511 -452,-1514 -452,-1562" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(255,255,255)" points="-452,-1562 -455,-1565 -306,-1565 -309,-1562 -452,-1562" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(112,119,119)" points="-452,-1514 -455,-1511 -306,-1511 -309,-1514 -452,-1514" stroke="rgb(112,119,119)"/>
     <polygon fill="rgb(112,119,119)" points="-309,-1562 -306,-1565 -306,-1511 -309,-1514 -309,-1562" stroke="rgb(112,119,119)"/>
     <rect fill="rgb(224,238,238)" height="48" stroke="rgb(224,238,238)" width="143" x="-452" y="-1562"/>
     <rect fill="none" height="48" qtmmishow="hidden" stroke="rgb(255,0,0)" width="143" x="-452" y="-1562"/>
    </a>
   <metadata/></g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_地调直调_光伏.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="155" x="-606" y="-1702"/></g>
   <g href="cx_索引_接线图_地调直调_光伏.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-655" y="-1719"/></g>
   <g href="AVC芝麻升压站.svg" style="fill-opacity:0"><rect height="48" qtmmishow="hidden" stroke="rgb(255,0,0)" width="143" x="-452" y="-1562"/></g>
  </g><g id="ArcThreePoints_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="none" points="798,-1331 799,-1331 801,-1330 802,-1330 804,-1330 805,-1329 806,-1328 807,-1327 808,-1326 808,-1325 809,-1324 809,-1323 810,-1322 810,-1321 809,-1320 809,-1319 808,-1318 808,-1317 807,-1316 806,-1315 805,-1314 804,-1313 802,-1313 801,-1313 799,-1312 798,-1312 " stroke="rgb(60,120,255)" stroke-width="0.404269"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="968,-1409 968,-1410 968,-1411 968,-1411 969,-1412 969,-1412 969,-1413 970,-1413 970,-1414 971,-1414 971,-1414 972,-1414 972,-1414 973,-1414 973,-1414 974,-1414 974,-1414 975,-1414 975,-1413 976,-1413 976,-1412 976,-1412 977,-1411 977,-1411 977,-1410 977,-1409 " stroke="rgb(60,120,255)" stroke-width="0.191496"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="987,-1331 988,-1331 990,-1330 991,-1330 993,-1330 994,-1329 995,-1328 996,-1327 997,-1326 997,-1325 998,-1324 998,-1323 999,-1322 999,-1321 998,-1320 998,-1319 997,-1318 997,-1317 996,-1316 995,-1315 994,-1314 993,-1313 991,-1313 990,-1313 988,-1312 987,-1312 " stroke="rgb(60,120,255)" stroke-width="0.404269"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1115,-1332 1116,-1332 1118,-1331 1119,-1331 1121,-1331 1122,-1330 1123,-1329 1124,-1328 1125,-1327 1125,-1326 1126,-1325 1126,-1324 1127,-1323 1127,-1322 1126,-1321 1126,-1320 1125,-1319 1125,-1318 1124,-1317 1123,-1316 1122,-1315 1121,-1314 1119,-1314 1118,-1314 1116,-1313 1115,-1313 " stroke="rgb(60,120,255)" stroke-width="0.404269"/>
  </g><g id="PolygonFilled_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="rgb(139,102,139)" points="250,-1357 245,-1346 256,-1346 250,-1357 250,-1356 250,-1357 " stroke="rgb(170,85,127)"/>
   <polyline DF8003:Layer="PUBLIC" fill="rgb(139,102,139)" points="250,-1371 245,-1382 256,-1382 250,-1371 250,-1372 250,-1371 " stroke="rgb(170,85,127)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="784,-1747 778,-1760 791,-1760 784,-1747 784,-1748 784,-1747 " stroke="rgb(60,120,255)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1000,-1747 994,-1760 1007,-1760 1000,-1747 1000,-1748 1000,-1747 " stroke="rgb(60,120,255)"/>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_3defed0">
    <use class="BV-110KV" transform="matrix(0.000000 -0.554113 -0.666667 -0.000000 244.450980 -1675.484848)" xlink:href="#voltageTransformer:shape71"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_ZM.CX_ZM_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="58289"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 291.000000 -1336.000000)" xlink:href="#transformer2:shape14_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 291.000000 -1336.000000)" xlink:href="#transformer2:shape14_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="38856" ObjectName="TF-CX_ZM.CX_ZM_1T"/>
    <cge:TPSR_Ref TObjectID="38856"/></metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -618.000000 -1643.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-196235" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 -592.461538 -1510.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196235" ObjectName="CX_ZM:CX_ZM_131BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-196236" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 -593.461538 -1470.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196236" ObjectName="CX_ZM:CX_ZM_131BK_Q"/>
    </metadata>
   </g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-196186" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -426.000000 -1611.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29871" ObjectName="DYN-CX_ZM"/>
     <cge:Meas_Ref ObjectId="196186"/>
    </metadata>
   </g>
  </g><g id="Circle_Layer">
   <circle DF8003:Layer="PUBLIC" cx="-57" cy="-1310" fill="none" r="7.5" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="-57" cy="-1322" fill="none" r="7.5" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="-45" cy="-1316" fill="none" r="7.5" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="-68" cy="-1315" fill="none" r="7.5" stroke="rgb(60,120,255)" stroke-width="1"/>
   <ellipse DF8003:Layer="PUBLIC" cx="1074" cy="-774" fill="none" rx="14" ry="13.5" stroke="rgb(255,255,0)" stroke-width="1.42505"/>
   <ellipse DF8003:Layer="PUBLIC" cx="1074" cy="-752" fill="none" rx="14" ry="13.5" stroke="rgb(255,255,0)" stroke-width="1.5039"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-196295">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 320.000000 -1520.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29887" ObjectName="SW-CX_ZM.CX_ZM_131BK"/>
     <cge:Meas_Ref ObjectId="196295"/>
    <cge:TPSR_Ref TObjectID="29887"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 813.000000 -1661.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.900000 -0.000000 0.000000 -0.882353 775.000000 -1467.235294)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.900000 -0.000000 0.000000 -0.882353 991.000000 -1467.235294)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.900000 -0.000000 0.000000 -0.882353 1106.000000 -1364.235294)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196304">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 318.000000 -1183.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29896" ObjectName="SW-CX_ZM.CX_ZM_301BK"/>
     <cge:Meas_Ref ObjectId="196304"/>
    <cge:TPSR_Ref TObjectID="29896"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196312">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -25.000000 -1032.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29907" ObjectName="SW-CX_ZM.CX_ZM_333BK"/>
     <cge:Meas_Ref ObjectId="196312"/>
    <cge:TPSR_Ref TObjectID="29907"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196309">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 239.000000 -1034.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29903" ObjectName="SW-CX_ZM.CX_ZM_332BK"/>
     <cge:Meas_Ref ObjectId="196309"/>
    <cge:TPSR_Ref TObjectID="29903"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196306">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 509.000000 -1034.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29899" ObjectName="SW-CX_ZM.CX_ZM_331BK"/>
     <cge:Meas_Ref ObjectId="196306"/>
    <cge:TPSR_Ref TObjectID="29899"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196315">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 758.000000 -1034.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29911" ObjectName="SW-CX_ZM.CX_ZM_334BK"/>
     <cge:Meas_Ref ObjectId="196315"/>
    <cge:TPSR_Ref TObjectID="29911"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196318">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1065.000000 -1034.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29915" ObjectName="SW-CX_ZM.CX_ZM_335BK"/>
     <cge:Meas_Ref ObjectId="196318"/>
    <cge:TPSR_Ref TObjectID="29915"/></metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2a050d0">
    <use class="BV-35KV" transform="matrix(-0.935484 -0.000000 -0.000000 0.928571 402.000000 -1298.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35796a0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 344.000000 -1711.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22e6c90">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -67.000000 -1244.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3610940">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -104.000000 -1225.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3fab920">
    <use class="BV-35KV" transform="matrix(0.000000 -0.935484 -0.928571 -0.000000 33.000000 -893.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a45b00">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -25.000000 -850.000000)" xlink:href="#lightningRod:shape188"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3ce9280">
    <use class="BV-35KV" transform="matrix(0.000000 -0.935484 -0.928571 -0.000000 297.000000 -895.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35a2c40">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 238.000000 -852.000000)" xlink:href="#lightningRod:shape188"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_41ef010">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 508.000000 -854.000000)" xlink:href="#lightningRod:shape188"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_411ac30">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 757.000000 -856.000000)" xlink:href="#lightningRod:shape188"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_41c22e0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 777.000000 -834.000000)" xlink:href="#lightningRod:shape188"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_43a8eb0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 750.000000 -737.000000)" xlink:href="#lightningRod:shape177"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_334f420">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1064.000000 -872.000000)" xlink:href="#lightningRod:shape188"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35f07b0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1084.000000 -850.000000)" xlink:href="#lightningRod:shape188"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3cea960">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1064.000000 -684.000000)" xlink:href="#lightningRod:shape188"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a786a0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1030.000000 -716.000000)" xlink:href="#lightningRod:shape174"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3ef2ee0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 793.500000 -1592.500000)" xlink:href="#lightningRod:shape192"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3dba800">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1009.500000 -1592.500000)" xlink:href="#lightningRod:shape192"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4105cb0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1124.500000 -1489.500000)" xlink:href="#lightningRod:shape192"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3c979a0">
    <use class="BV-35KV" transform="matrix(0.000000 -0.935484 -0.928571 -0.000000 564.000000 -895.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35f5f70">
    <use class="BV-35KV" transform="matrix(0.000000 -0.935484 -0.928571 -0.000000 816.000000 -893.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3dda440">
    <use class="BV-35KV" transform="matrix(0.000000 -0.935484 -0.928571 -0.000000 1124.037037 -894.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3432f90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 736.500000 -689.000000) translate(0,15)">±10MVar</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_42542d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 727.000000 -730.000000) translate(0,15)">1号动态无</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_42542d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 727.000000 -730.000000) translate(0,33)">功补偿装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3d765b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -261.000000 -1141.000000) translate(0,15)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_43808f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1112.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_43808f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1112.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_43808f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1112.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_43808f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1112.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_43808f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1112.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_43808f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1112.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_43808f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1112.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_43808f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1112.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_43808f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1112.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_43808f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1112.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_43808f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1112.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_43808f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1112.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_43808f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1112.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_43808f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1112.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_43808f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1112.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_43808f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1112.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_43808f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1112.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_43808f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1112.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3dd41a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1550.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3dd41a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1550.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3dd41a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1550.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3dd41a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1550.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3dd41a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1550.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3dd41a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1550.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3dd41a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1550.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_35e4240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -579.000000 -1691.500000) translate(0,16)">芝麻光伏电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3424a60" transform="matrix(1.000000 0.000000 -0.000000 1.000000 749.000000 -1796.000000) translate(0,15)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a9f370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 272.000000 -1464.000000) translate(0,12)">13117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e47720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 271.000000 -1611.000000) translate(0,12)">13160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4373b10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 352.000000 -1691.000000) translate(0,12)">K1317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_425e730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 336.000000 -1634.000000) translate(0,12)">1316</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2aa0180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 338.000000 -1549.000000) translate(0,12)">131</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b02450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 175.000000 -1368.000000) translate(0,12)">1010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3f5fe10" transform="matrix(1.000000 0.000000 -0.000000 1.000000 338.000000 -1806.000000) translate(0,16)">方</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3f5fe10" transform="matrix(1.000000 0.000000 -0.000000 1.000000 338.000000 -1806.000000) translate(0,36)">芝</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3f5fe10" transform="matrix(1.000000 0.000000 -0.000000 1.000000 338.000000 -1806.000000) translate(0,56)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40e6170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 379.000000 -1415.000000) translate(0,12)">1号主变参数:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40e6170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 379.000000 -1415.000000) translate(0,27)">SZ11-50000/110GYW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40e6170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 379.000000 -1415.000000) translate(0,42)">115±8x1.25%/35kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40e6170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 379.000000 -1415.000000) translate(0,57)">50MVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40e6170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 379.000000 -1415.000000) translate(0,72)">YN,d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40e6170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 379.000000 -1415.000000) translate(0,87)">Ud%=10.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="28" graphid="g_263b900" transform="matrix(1.000000 0.000000 -0.000000 1.000000 185.000000 -1603.000000) translate(0,23)">110</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="28" graphid="g_263b900" transform="matrix(1.000000 0.000000 -0.000000 1.000000 185.000000 -1603.000000) translate(0,51)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="28" graphid="g_263b900" transform="matrix(1.000000 0.000000 -0.000000 1.000000 185.000000 -1603.000000) translate(0,79)">方</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="28" graphid="g_263b900" transform="matrix(1.000000 0.000000 -0.000000 1.000000 185.000000 -1603.000000) translate(0,107)">芝</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="28" graphid="g_263b900" transform="matrix(1.000000 0.000000 -0.000000 1.000000 185.000000 -1603.000000) translate(0,135)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f20560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 187.000000 -1724.000000) translate(0,12)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29d06a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -97.000000 -1378.000000) translate(0,12)"> 35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29d06a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -97.000000 -1378.000000) translate(0,27)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_421b630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -55.000000 -768.000000) translate(0,15)">3号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3392a00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -65.000000 -745.000000) translate(0,15)">26-40号方阵</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2352060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 208.000000 -770.000000) translate(0,15)">2号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22eecc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 202.000000 -749.000000) translate(0,15)">13-25号方阵</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_45772e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 478.000000 -772.000000) translate(0,15)">1号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2aa8400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 475.000000 -749.000000) translate(0,15)">1-12号方阵</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_410f830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1020.500000 -668.000000) translate(0,15)">1号站用变及小</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_410f830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1020.500000 -668.000000) translate(0,33)">电阻接地装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e82c50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1092.500000 -774.000000) translate(0,12)">1250kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3402460" transform="matrix(1.000000 0.000000 -0.000000 1.000000 937.000000 -1800.000000) translate(0,15)">10kV腊居村支线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3ec2d00" transform="matrix(1.000000 0.000000 -0.000000 1.000000 714.000000 -1245.000000) translate(0,15)">0.4kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25ef4f0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 994.000000 -1249.000000) translate(0,15)">0.4kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25ef6a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 651.500000 -1723.000000) translate(0,15)">1号站用变及小</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25ef6a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 651.500000 -1723.000000) translate(0,33)">电阻接地装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25ef6a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 651.500000 -1723.000000) translate(0,51)">   1250kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22e7870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1022.500000 -1729.000000) translate(0,15)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22e7870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1022.500000 -1729.000000) translate(0,33)"> 250kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3dce000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1083.500000 -1619.000000) translate(0,15)">100kW柴</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3dce000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1083.500000 -1619.000000) translate(0,33)">油发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e735f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 336.000000 -1483.000000) translate(0,12)">1311</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b6d020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 280.000000 -1722.000000) translate(0,12)">1901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26019f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 265.000000 -1685.000000) translate(0,12)">19017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_412d8e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 336.000000 -1212.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b3baf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -44.000000 -1182.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3443160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -7.000000 -1061.000000) translate(0,12)">333</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40e5bd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -108.000000 -941.000000) translate(0,12)">33367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c96fe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 257.000000 -1063.000000) translate(0,12)">332</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_41fac60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 146.000000 -944.000000) translate(0,12)">33267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3dd9fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 527.000000 -1063.000000) translate(0,12)">331</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3dda200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 776.000000 -1063.000000) translate(0,12)">334</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cafe60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 424.000000 -943.000000) translate(0,12)">33167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a6c220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 669.000000 -942.000000) translate(0,12)">33467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a6c410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1083.000000 -1063.000000) translate(0,12)">335</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a6c620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 979.000000 -941.000000) translate(0,12)">33567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_40785b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 140.000000 -1412.000000) translate(0,16)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_363fa60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -601.000000 -764.000000) translate(0,15)">8355520</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_4251e40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -425.000000 -1547.000000) translate(0,16)">AGC/AVC</text>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="784" x2="806" y1="-1448" y2="-1448"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="830" x2="862" y1="-1446" y2="-1446"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="869" x2="889" y1="-1446" y2="-1446"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="889" x2="889" y1="-1451" y2="-1442"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="891" x2="891" y1="-1450" y2="-1444"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="893" x2="893" y1="-1449" y2="-1444"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(170,85,127)" stroke-width="1" x1="250" x2="250" y1="-1403" y2="-1382"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(170,85,127)" stroke-width="1" x1="250" x2="275" y1="-1324" y2="-1324"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="233,-1324 250,-1324 250,-1346 " stroke="rgb(170,85,127)" stroke-width="1"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(170,85,127)" stroke-width="0.151049" x1="275" x2="275" y1="-1324" y2="-1329"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke="rgb(170,85,127)" stroke-width="1" x1="275" x2="275" y1="-1370" y2="-1335"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="256" x2="244" y1="-1304" y2="-1304"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.228882" x1="251" x2="249" y1="-1297" y2="-1297"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.322998" x1="253" x2="247" y1="-1300" y2="-1300"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.251748" x1="250" x2="250" y1="-1304" y2="-1313"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="-58" x2="-58" y1="-1322" y2="-1319"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="-61" x2="-58" y1="-1324" y2="-1322"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="-55" x2="-58" y1="-1324" y2="-1322"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="-45" x2="-45" y1="-1316" y2="-1313"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="-48" x2="-45" y1="-1318" y2="-1316"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="-42" x2="-45" y1="-1318" y2="-1316"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="-65" x2="-69" y1="-1317" y2="-1319"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="-65" x2="-69" y1="-1315" y2="-1313"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="-69" x2="-69" y1="-1313" y2="-1319"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="-84,-1321 -94,-1328 -94,-1331 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="-89" x2="-89" y1="-1332" y2="-1339"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="-91" x2="-87" y1="-1340" y2="-1340"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="-90" x2="-88" y1="-1342" y2="-1342"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="-92" x2="-85" y1="-1339" y2="-1339"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="-58" x2="-58" y1="-1310" y2="-1307"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="-61" x2="-58" y1="-1312" y2="-1310"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="-55" x2="-58" y1="-1312" y2="-1310"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="-89" x2="-89" y1="-1321" y2="-1310"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="1074" x2="1037" y1="-775" y2="-775"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="1038" x2="1038" y1="-775" y2="-775"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="1037" x2="1037" y1="-775" y2="-751"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="783" x2="788" y1="-1693" y2="-1698"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="783" x2="783" y1="-1693" y2="-1688"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="783" x2="778" y1="-1693" y2="-1698"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="784" x2="784" y1="-1714" y2="-1709"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="784" x2="788" y1="-1714" y2="-1718"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="784" x2="780" y1="-1714" y2="-1718"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="784" x2="784" y1="-1709" y2="-1709"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="780" x2="782" y1="-1718" y2="-1720"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="784" x2="784" y1="-1714" y2="-1714"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="784" x2="784" y1="-1774" y2="-1732"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="784" x2="822" y1="-1714" y2="-1714"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="822" x2="822" y1="-1714" y2="-1701"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="784" x2="784" y1="-1597" y2="-1555"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="785" x2="973" y1="-1418" y2="-1418"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="784" x2="811" y1="-1395" y2="-1395"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="811" x2="811" y1="-1352" y2="-1339"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="811" x2="811" y1="-1384" y2="-1372"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="784" x2="784" y1="-1352" y2="-1339"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="784" x2="784" y1="-1372" y2="-1384"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="784" x2="811" y1="-1339" y2="-1339"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="731" x2="731" y1="-1530" y2="-1324"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1000" x2="1000" y1="-1774" y2="-1732"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1000" x2="1000" y1="-1597" y2="-1555"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="973" x2="1000" y1="-1395" y2="-1395"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1000" x2="1000" y1="-1352" y2="-1339"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1000" x2="1000" y1="-1384" y2="-1372"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="973" x2="973" y1="-1352" y2="-1339"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="973" x2="973" y1="-1372" y2="-1384"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="973" x2="1000" y1="-1339" y2="-1339"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="811" x2="811" y1="-1395" y2="-1409"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="811" x2="968" y1="-1409" y2="-1409"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="973" x2="973" y1="-1395" y2="-1418"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="977" x2="1000" y1="-1409" y2="-1409"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="798" x2="798" y1="-1331" y2="-1339"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.335714" x1="1000" x2="994" y1="-1726" y2="-1716"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1000" x2="995" y1="-1692" y2="-1697"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1000" x2="1000" y1="-1692" y2="-1687"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1000" x2="1005" y1="-1692" y2="-1697"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.335714" x1="994" x2="1005" y1="-1716" y2="-1716"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.335714" x1="1000" x2="1005" y1="-1726" y2="-1716"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1000" x2="1027" y1="-1692" y2="-1692"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1027" x2="1027" y1="-1692" y2="-1672"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1027" x2="1000" y1="-1672" y2="-1656"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1000" x2="978" y1="-1433" y2="-1433"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="954" x2="922" y1="-1431" y2="-1431"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="915" x2="895" y1="-1431" y2="-1431"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="895" x2="895" y1="-1436" y2="-1427"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="893" x2="893" y1="-1435" y2="-1429"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="891" x2="891" y1="-1434" y2="-1429"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="987" x2="987" y1="-1331" y2="-1339"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1051" x2="1051" y1="-1531" y2="-1324"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1115" x2="1115" y1="-1494" y2="-1452"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1166" x2="1166" y1="-1428" y2="-1323"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1.42501" x1="1079" x2="1074" y1="-779" y2="-775"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1.425" x1="1079" x2="1081" y1="-778" y2="-784"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1.425" x1="1063" x2="1069" y1="-777" y2="-779"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1.42501" x1="1068" x2="1074" y1="-779" y2="-775"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1.425" x1="1074" x2="1074" y1="-769" y2="-776"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1.425" x1="1079" x2="1074" y1="-765" y2="-769"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1.5039" x1="1069" x2="1074" y1="-749" y2="-753"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1.50391" x1="1074" x2="1074" y1="-753" y2="-757"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1.5039" x1="1080" x2="1074" y1="-749" y2="-753"/>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1033" cy="-1323" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1149" cy="-1323" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29885" cx="327" cy="-1116" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29885" cx="-15" cy="-1116" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29885" cx="248" cy="-1116" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29885" cx="518" cy="-1116" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29885" cx="767" cy="-1116" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29885" cx="1074" cy="-1116" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48133" cx="329" cy="-1510" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48133" cx="329" cy="-1510" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="798" cy="-1258" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="987" cy="-1259" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1115" cy="-1259" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_ZM.CX_ZM_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-268,-1116 1237,-1116 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="29885" ObjectName="BS-CX_ZM.CX_ZM_3IM"/>
    <cge:TPSR_Ref TObjectID="29885"/></metadata>
   <polyline fill="none" opacity="0" points="-268,-1116 1237,-1116 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="685,-1323 1202,-1323 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="685,-1323 1202,-1323 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="685,-1258 875,-1258 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="685,-1258 875,-1258 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="922,-1259 1203,-1259 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="922,-1259 1203,-1259 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_ZM.XM">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="314,-1510 348,-1510 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="48133" ObjectName="BS-CX_ZM.XM"/>
    <cge:TPSR_Ref TObjectID="48133"/></metadata>
   <polyline fill="none" opacity="0" points="314,-1510 348,-1510 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_ZM"/>
</svg>